<template>
  <div class="form-process-box">
    <ul class="anchor-point">
      <li
        class="item"
        v-for="(item, index) in formFieldList"
        :key="index"
        @click="handleGoAnchor(index)"
      >
        {{ item.groupName }}
      </li>
    </ul>

    <div class="form-content">
      <div class="mask"></div>
      <h1 class="definition-name">
        {{ typeContractData.definitionName }}
      </h1>
      <ts-form ref="form" :model="form" labelWidth="120px">
        <div
          v-for="(item, index) in formFieldList"
          :key="index"
          :id="`groupItemBox${index}`"
        >
          <top-title :title="item.groupName" />
          <template v-if="item.groupName === '合同基本信息'">
            <div class="render-form-box">
              <render-component
                v-for="field in item.fieldList"
                :key="field.id"
                :field="field"
                :formData="form"
                :typeContractData="typeContractData"
                :renderStatus="renderStatus"
                numberType="1"
              />
            </div>
          </template>
          <template v-if="item.groupName === '相对方信息'">
            <process-form-opposite-party-info
              :renderStatus="renderStatus"
              :formData="form"
            />
          </template>
          <template v-if="item.groupName === '合同标的物信息'">
            <process-form-table
              class="form-table"
              :formData="form"
              :tableList="form.goodsList"
              :columns="subjectMatterColumns"
              :showComputedTotal="true"
              operateDataKey="goodsList"
              computedKey="grossPrice"
            >
              <template #goodsName="{ row, column, index }">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`goodsList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #specifications="{ row, column }">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #goodsUnit="{ row, column }">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #goodsPrice="{ row, column, index }">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`goodsList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input
                    style="min-width:80px"
                    oninput="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
                    onafterpaste="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                    @blur="addToDecimal($event, row, column.property)"
                    @input="handleComputedSyntagma(row)"
                  />
                </ts-form-item>
              </template>
              <template #goodsNumbers="{ row, column, index }">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`goodsList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input
                    style="min-width:80px"
                    oninput="this.value=this.value?.replace(/\D/g,'')"
                    onafterpaste="this.value=this.value?.replace(/\D/g,'')"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                    @input="handleComputedSyntagma(row)"
                  />
                </ts-form-item>
              </template>
              <template #goodsRemark="{ row, column }">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
            </process-form-table>
          </template>
          <template v-if="item.groupName === '合同付款信息'">
            <process-form-table
              class="form-table"
              :formData="form"
              :tableList="form.paymentList"
              operateDataKey="paymentList"
              :columns="paymentInfoColumns"
            >
              <template #paymentContent="{ row, column, index}">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`paymentList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-select
                    style="width:100%"
                    clearable
                    v-model="row[column.property]"
                    :placeholder="`请选择${column.label}`"
                  >
                    <ts-option
                      v-for="item of paymentContentOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></ts-option>
                  </ts-select>
                </ts-form-item>
              </template>
              <template #paymentDate="{ row, column, index}">
                <ts-form-item
                  label=""
                  labelWidth="0"
                  :prop="`paymentList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <span style="color: red;">*</span>
                  <el-date-picker
                    class="payment-date-picker"
                    v-model="row[column.property]"
                    @change="
                      e => {
                        datePickerChange(row, column, e);
                      }
                    "
                    type="date"
                    placeholder="选择日期"
                  />
                </ts-form-item>
              </template>
              <template #paymentCompany="{ row, column}">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #paymentPayee="{ row, column, index}">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`paymentList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #paymentTerm="{ row, column}">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #paymentPrice="{ row, column, index}">
                <ts-form-item
                  :label="column.label"
                  labelWidth="0"
                  :prop="`paymentList.${index}.${column.property}`"
                  :rules="rules.required"
                >
                  <ts-input
                    style="min-width:80px"
                    oninput="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
                    onafterpaste="this.value=this.value?.match(/\d+\.?\d{0,2}/)"
                    @blur="addToDecimal($event, row, column.property)"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
              <template #paymentBreach="{ row, column}">
                <ts-form-item label="" labelWidth="0">
                  <ts-input
                    style="min-width:80px"
                    v-model="row[column.property]"
                    :maxlength="50"
                    :placeholder="`请输入${column.label}`"
                  />
                </ts-form-item>
              </template>
            </process-form-table>
          </template>
          <template v-if="item.groupName === '签署文件'">
            <process-sign-paper ref="ProcessSignPaper" :formData="form" />
          </template>
        </div>

        <ts-form-item
          v-if="isShowApprovalIdea"
          label="审批意见"
          prop="remark"
          :rules="{ required: isShowApprovalIdea, message: '必填' }"
        >
          <ts-input
            v-model="form.remark"
            :maxlength="50"
            class="textarea"
            type="textarea"
            placeholder="请输入审批意见"
          />
        </ts-form-item>
      </ts-form>
    </div>

    <!-- 流程发起或者同意 -->
    <dialog-associated-process
      v-model="dialogAssociatedProcess"
      :processNeedIdObject="typeContractData"
      :submitCallBack="processSubmitCallBack"
    />

    <!-- 流程退回 -->
    <dialog-process-back
      v-model="dialogProcessBack"
      :typeContractData="typeContractData"
      @closeCreationDialog="closeCreationDialog"
    />
  </div>
</template>

<script>
import TopTitle from '@/components/TopTitle.vue';
import uuid from 'uuid';

import RenderComponent from '@/components/render-component/index.vue';
import ProcessFormOppositePartyInfo from './process-form-opposite-party-info.vue';
import ProcessFormTable from './process-form-table.vue';
import ProcessSignPaper from './process-sign-paper.vue';
import DialogAssociatedProcess from '@/components/process-components/dialog-associated-process.vue';
import DialogProcessBack from '../dialog/dialog-process-back.vue';
import { deepClone, computeNumber } from '@/unit/commonHandle.js';
import { toDecimal } from '@/util/index.js';
import api from '@/api/ajax/api-creation.js';

export default {
  components: {
    RenderComponent,
    ProcessFormOppositePartyInfo,
    ProcessFormTable,
    ProcessSignPaper,
    TopTitle,
    DialogAssociatedProcess,
    DialogProcessBack
  },
  props: {
    formFieldList: {
      type: Array,
      default: () => []
    },
    typeContractData: {
      type: Object,
      default: () => {}
    },
    renderStatus: {
      type: String,
      default: 'add'
    },
    processTabsStatus: {
      type: String
    }
  },
  data() {
    return {
      form: {
        recordPartnersList: [
          {
            partnersId: '',
            name: '',
            contactsPhone: '',
            address: '',
            bank: '',
            account: '',
            contractPrice: '',
            paymentPrice: '',
            remainingPrice: ''
          }
        ],
        goodsList: [],
        paymentList: [],
        filesId: '',
        templateId: ''
      },
      rules: {
        required: { required: true, message: '必填' }
      },
      subjectMatterColumns: [
        {
          prop: 'goodsName',
          label: '产品名称',
          align: 'center',
          columnSlots: 'goodsName'
        },
        {
          prop: 'specifications',
          label: '规格型号',
          align: 'center',
          columnSlots: 'specifications'
        },
        {
          prop: 'goodsUnit',
          align: 'center',
          label: '单位',
          columnSlots: 'goodsUnit'
        },
        {
          prop: 'goodsPrice',
          align: 'center',
          label: '含税价格',
          columnSlots: 'goodsPrice'
        },
        {
          prop: 'goodsNumbers',
          align: 'center',
          label: '数量',
          columnSlots: 'goodsNumbers'
        },
        {
          prop: 'grossPrice',
          align: 'center',
          label: '小计'
        },
        {
          prop: 'goodsRemark',
          align: 'center',
          label: '备注',
          columnSlots: 'goodsRemark'
        }
      ],
      paymentContentOption: [
        { value: '首付款', label: '首付款' },
        { value: '执行款', label: '执行款' },
        { value: '尾款', label: '尾款' },
        { value: '保证金', label: '保证金' }
      ],
      paymentInfoColumns: [
        {
          prop: 'paymentContent',
          label: '款项内容',
          align: 'center',
          columnSlots: 'paymentContent'
        },
        {
          prop: 'paymentDate',
          label: '付款日期',
          align: 'center',
          width: '160',
          columnSlots: 'paymentDate'
        },
        {
          prop: 'paymentCompany',
          label: '付款单位',
          align: 'center',
          width: '140',
          columnSlots: 'paymentCompany'
        },
        {
          prop: 'paymentPayee',
          label: '收款合同方',
          align: 'center',
          width: '140',
          columnSlots: 'paymentPayee'
        },
        {
          prop: 'paymentTerm',
          label: '付款条件',
          align: 'center',
          width: '140',
          columnSlots: 'paymentTerm'
        },
        {
          prop: 'paymentPrice',
          label: '付款金额',
          align: 'center',
          width: '140',
          columnSlots: 'paymentPrice'
        },
        {
          prop: 'paymentBreach',
          label: '违约责任',
          align: 'center',
          columnSlots: 'paymentBreach'
        }
      ],

      dialogAssociatedProcess: false,
      dialogProcessBack: false,
      submitFormData: null
    };
  },
  computed: {
    isShowApprovalIdea() {
      return (
        this.typeContractData.taskId &&
        this.processTabsStatus === '1' &&
        this.typeContractData.currentStepName &&
        this.typeContractData.currentStepName !== '重新提交'
      );
    }
  },
  created() {
    if (this.renderStatus !== 'add') {
      this.form = this.typeContractData.formData;
    } else {
      this.form.filesId = uuid();
    }
  },
  mounted() {
    // 节点不为重新提交 或者 查看时添加遮罩 禁止修改
    let stepName = this.typeContractData.currentStepName;
    let stepNameStatus =
      stepName && stepName !== '重新提交' && stepName !== '草稿';
    if (stepNameStatus || this.renderStatus === 'show') {
      this.handleAddMask();
    }
  },
  methods: {
    datePickerChange(row, column, e) {
      row[column.property] = this.$moment(e).format('YYYY-MM-DD');
    },
    async submit(type, otherData) {
      try {
        await this.$refs.form.validate();
        const data = {};

        let fieldJson = {};
        const basicsField = this.formFieldList.filter(
          item => item.groupName === '合同基本信息'
        )[0].fieldList;
        basicsField.forEach(item => {
          if (typeof this.form[item.fieldName] === 'number') {
            this.form[item.fieldName] = String(this.form[item.fieldName]);
          }
          fieldJson[item.fieldName] = this.form[item.fieldName];
        });

        data.fieldJson = JSON.stringify(fieldJson);

        this.form.recordPartnersList.forEach(item => {
          item.signNumbers = this.form.signNumbers;
          item.partya = this.form.partya;
        });

        data.goodsList = deepClone(this.form.goodsList); // 合同标的物信息
        data.paymentList = deepClone(this.form.paymentList); // 合同付款信息
        data.recordPartnersList = deepClone(this.form.recordPartnersList); // 相对方

        let definitionId;
        if (this.renderStatus === 'add') {
          definitionId = this.typeContractData.id;
          data.contractNumberValue = deepClone(this.form.contractNumberValue); // 编号
        } else {
          definitionId = this.typeContractData.definition_id;
          data.taskId = this.typeContractData.taskId;
          data.remark = this.form.remark;
        }

        data.definitionId = definitionId;
        data.workflowId = this.typeContractData.workflow_id;

        // 签署文件
        data.templateId = this.form.templateId;
        data.filesId = this.form.filesId;

        if (type === 'draft') {
          return deepClone(data);
        }

        if (type === 'preserve') {
          data.id = this.typeContractData.recordId;
          return deepClone(data);
        }

        if (type === 'submit') {
          data.id = otherData.recordId;
          this.typeContractData.recordId = otherData.recordId;

          this.submitFormData = deepClone(data);
          this.dialogAssociatedProcess = true;
        }
      } catch (error) {
        if (typeof error === 'boolean' && !error) {
          this.$message.warning('请将表单信息填写完整!');
          return 'validate fail';
        }
        throw error;
      }
    },
    async processSubmitCallBack(processForm, endStepName) {
      let submitData = {
        ...processForm,
        ...this.submitFormData,
        status: '1'
      };

      if (endStepName) submitData.status = '2';

      const submitRes = await api.contractRecordSave(submitData);
      if (submitRes.success && submitRes.statusCode === 200) {
        this.$message.success('流程提交成功');
        this.dialogAssociatedProcess = false;
        this.closeCreationDialog();
      } else {
        this.$message.error(submitRes.message || '操作失败!');
      }
    },
    addToDecimal(e, row, key) {
      this.$set(row, key, toDecimal(e.target.value));
    },
    handleComputedSyntagma(row) {
      const { goodsPrice, goodsNumbers } = row;
      const pengdingValue = computeNumber(goodsPrice, '*', goodsNumbers).result;
      // 列表行的小计
      row.grossPrice = toDecimal(pengdingValue);
    },
    // 锚点定位
    handleGoAnchor(index) {
      const formContent = document.querySelector('.form-content');
      const groupItem = this.$el.querySelector('#groupItemBox' + index);
      formContent.scrollTop = groupItem.offsetTop;
    },
    handleAddMask() {
      // 设置遮罩 减去文件上传的盒子高度
      const formContent = document.querySelector('.form-content');
      const mask = document.querySelector('.mask');
      setTimeout(() => {
        let ProcessSignPaperHeight = null;
        this.$nextTick(() => {
          ProcessSignPaperHeight =
            this.$refs.ProcessSignPaper[0].$el.querySelector(
              '.ts-upload-file-list'
            ).scrollHeight + 16;
          mask.style.height =
            computeNumber(formContent.scrollHeight, '-', ProcessSignPaperHeight)
              .result + 'px';
        });
      }, 500);
    },
    closeCreationDialog() {
      this.$emit('closeCreationDialog');
    }
  }
};
</script>

<style lang="scss" scoped>
.form-process-box {
  width: 100%;
  height: 100%;
  display: flex;
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
  .definition-name {
    text-align: center;
    font-size: 28px;
    margin-bottom: 0px;
  }
  > * {
    box-sizing: border-box;
    &::-webkit-scrollbar {
      width: 6px;
    }
    // 滚动条的滑块
    &::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background: rgba(153, 153, 153, 0.4);
      &:hover {
        background: rgba(153, 153, 153, 0.8);
      }
    }
    &::-webkit-scrollbar-track {
      box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
      border-radius: 8px;
      background: #fff;
    }
  }
  .anchor-point {
    width: 130px;
    height: calc(100%);
    overflow-y: auto;
    border: 1px solid #eee;
    margin-right: 8px;
    .item {
      height: 40px;
      line-height: 40px;
      border: 1px solid #eee;
      border-top: 0;
      border-left: 0;
      border-right: 0;
      padding: 0 8px;
      cursor: pointer;
    }
  }
  .form-content {
    flex: 1;
    height: calc(100%);
    overflow-y: auto;
    scroll-behavior: smooth;
    position: relative;
    .mask {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      cursor: not-allowed;
      z-index: 9999;
    }
    .render-form-box {
      display: flex;
      flex-wrap: wrap;
    }
    .form-table {
      margin: 8px 0;
    }
    .payment-date-picker {
      width: 140px;
      min-width: 140px !important;
      ::v-deep {
        .el-date-editor {
          width: 140px !important;
        }
      }
    }
  }
}
</style>
