<template>
  <div class="work-flow-view">
    <iframe ref="iframe" :src="src" class="iframe"></iframe>
  </div>
</template>

<script>
export default {
  props: {
    searchData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      src: ''
    };
  },
  async created() {
    const {
        definitionId,
        wfInstanceId,
        showWorkflowInfo = false,
        currentStepNo = '',
        wfDefinitionId = '',
        workflowName = '',
        workflowNo = ''
      } = this.searchData,
      paramsKeys = {
        showWorkflowInfo,
        currentStepNo,
        wfInstanceId,
        wfDefinitionId,
        workflowName,
        workflowNo
      },
      origin = window.location.origin,
      url = '/view-new/processView/workflowView/index.html';
    if (!wfDefinitionId || !workflowName || !workflowNo) {
      const res = await this.ajax.getWorkflowParams({
        definitionId
      });
      if (res.success && res.statusCode === 200) {
        const { wfDefinitionId, workflowName, workflowNo } = res.object;
        Object.assign(paramsKeys, {
          wfDefinitionId,
          workflowName,
          workflowNo
        });
      } else {
        this.$message.error(res.message || '获取流程图信息失败!');
        return;
      }
    }
    let params =
      '?' +
      Object.keys(paramsKeys)
        .map(key => `${key}=${paramsKeys[key]}`)
        .join('&');

    this.src = origin + url + params;

    this.$nextTick(() => {
      setTimeout(() => {
        const iframe = this.$refs.iframe;
        const iframeDocument =
          iframe.contentDocument || iframe.contentWindow.document;

        // 确保 iframe 内容已加载
        if (iframeDocument) {
          const element = iframeDocument.querySelector('#taskBox');
          if (element) {
            // 修改样式
            element.style.width = '360px';
          }
        }
      }, 1000);
    });
  }
};
</script>

<style lang="scss" scoped>
.work-flow-view {
  width: 100%;
  height: 100%;
  .iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
}
</style>
