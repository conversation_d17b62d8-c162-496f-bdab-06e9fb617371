<template>
  <div class="deptDocument">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      :elementCol="14"
      :showLength="4"
      @search="search"
    >
    </ts-search-bar>
    <div class="operation-area">
      <div class="area-right">
        <vxe-switch
          v-model="isListOverivew"
          open-label="列表视图"
          close-label="列表视图"
          @change="cutOverivew"
        ></vxe-switch>
        <ts-button
          v-for="(item, index) in btnList"
          :key="index"
          type="primary"
          :class="item.btnClass || ''"
          @click="() => item.events()"
        >
          {{ item.btnName }}
        </ts-button>
      </div>
    </div>
    <TsVxeTemplateTable
      id="table_dept_document"
      class="form-table"
      ref="table"
      :stripe="false"
      :defaultSort="{
        sidx: 'a.CREATE_DATE',
        sord: 'desc'
      }"
      :columns="columns"
      pageAlign="center"
      :cell-style="cellStyle"
      @refresh="handleRefreshTable"
      @selection-change="handleSelectChange"
    />
    <drawer-add-document
      ref="drawerAddDocument"
      @refresh="handleRefreshTable"
      @refreshTree="refreshTree"
    />
    <dialog-migration
      ref="dialogMigration"
      @refresh="search"
      @refreshTree="refreshTree"
    />
    <dialog-record-table ref="dialogRecordTable" />
    <dialog-upload-table ref="dialogUploadTable" />
    <ts-homs-select-person ref="TsHomsSelectPerson" @ok="handleSelectUserOk1" />
  </div>
</template>

<script>
import deptDocument from '../mixins/deptDocument';
import events from '../mixins/events';
import drawerAddDocument from '../component/drawer-add-document.vue';
import dialogRecordTable from '../component/dialog-record-table.vue';
import dialogMigration from '../component/dialog-migration.vue';
import dialogUploadTable from '../component/dialog-upload-table.vue';
export default {
  components: {
    drawerAddDocument,
    dialogRecordTable,
    dialogMigration,
    dialogUploadTable
  },
  mixins: [deptDocument, events],
  data() {
    return {
      selectList: [],
      treeNode: null,
      isListOverivew: true
    };
  },
  methods: {
    async refresh() {
      await this.search();
    },
    cellStyle({ row, column, rowIndex, columnIndex }) {
      if (column.field == 'actions') {
        return {
          backgroundColor: '#eceef3'
        };
      }
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    cutOverivew() {
      this.$emit('cutOverivew', false);
    },
    search(treeNode = null) {
      this.isListOverivew = true;
      if (treeNode) {
        this.treeNode = treeNode;
      }
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleGetSeacrhQueryParam() {
      this.pageSize = this.$refs.table.pageSize;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { completeDate = [] } = this.searchForm,
        [fromDate = '', toDate = ''] = completeDate,
        searchForm = {
          ...this.searchForm,
          fromDate,
          toDate,
          pageNo,
          pageSize,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      delete searchForm.completeDate;
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      if (this.treeNode) {
        searchForm.nodeId = this.treeNode.id;
        searchForm.parentid = this.treeNode.id;
        searchForm.id = this.treeNode.id;
        searchForm.name = this.treeNode.name;
      }
      return { searchForm, pageNo, pageSize };
    },
    async handleRefreshTable() {
      let { searchForm, pageNo, pageSize } = this.handleGetSeacrhQueryParam();
      let num = 1;
      if (searchForm.fromDate && num++) searchForm.fromDate += ' 00:00:00';
      if (searchForm.toDate && num++) searchForm.toDate += ' 23:59:59';
      if (num == 2) {
        this.$newMessage('warning', '请选择【发布日期】开始日期和结束日期');
        return false;
      }
      let res = await this.ajax.getDocumentListColumn(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.deptDocument {
  flex: 1;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  ::v-deep {
    .operation-area {
      display: flex;
      justify-content: flex-end;
      padding: 3px 0;
    }
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      .primary-span {
        color: #295cf9;
        cursor: pointer;
        text-align: left;
      }
    }
  }
}
</style>
