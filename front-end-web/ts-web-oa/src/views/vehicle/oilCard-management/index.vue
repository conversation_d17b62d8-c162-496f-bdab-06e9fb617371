<template>
  <div class="oilCard-management-box">
    <div class="vehicle-statistics-box">
      <div class="vehicle-total">
        <div
          :class="{ 'total-item': true, blue: true, red: item.red }"
          v-for="(item, index) in oilCardList"
          :key="index"
        >
          <span class="item-name">{{ item.title }}</span>
          <p>
            <span v-if="item.format" class="item-num">{{
              item.val | formatMoney
            }}</span>
            <span v-else class="item-num">{{ item.val }}</span>
            <span class="item-unit">{{ item.unit }}</span>
          </p>
        </div>
      </div>
    </div>
    <ts-search-bar
      v-model="searchForm"
      :actions="actions"
      :formList="searchList"
      :elementCol="14"
      @search="search"
      :resetData="{}"
    >
    </ts-search-bar>
    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @refresh="search"
    />

    <dialog-add ref="DialogAdd" @refreshTable="search" />
    <dialog-recharge ref="DialogRecharge" @refreshTable="search" />
    <dialog-oil-card-details ref="dialogOilCardDetails" />
  </div>
</template>

<script>
import table from './mixins/table';
import {
  vehicleOilList,
  getVehicleOilListStatistics
} from '@/api/ajax/vehicle/vehicle-oil.js';
import unitMixins from '@/views/vehicle/mixins/unit.js';
import DialogAdd from './components/dialog-add.vue';
import DialogRecharge from './components/dialog-recharge.vue';
import DialogOilCardDetails from './components/dialog-oil-card-details.vue';
export default {
  mixins: [table, unitMixins],
  components: {
    DialogAdd,
    DialogOilCardDetails,
    DialogRecharge
  },
  data() {
    return {
      loading: false,
      oilCardList: []
    };
  },
  async created() {
    this.$nextTick(() => {
      this.search();
    });
  },
  methods: {
    search() {
      this.handleRefreshTable();
      this.handleGetVehicleOilListStatistics();
    },
    refresh() {
      this.search();
    },
    handleDetails(row) {
      const { id } = row;
      this.$refs.dialogOilCardDetails.open({
        id
      });
    },
    handleEdit(row) {
      this.$refs.DialogAdd.open({
        type: 'edit',
        title: '编辑',
        eachData: row
      });
    },
    handleAdd() {
      this.$refs.DialogAdd.open({
        type: 'add',
        title: '新增'
      });
    },
    handleRecharge(row) {
      const { id, oilNo, oilPrice } = row;

      this.$refs.DialogRecharge.open({
        type: 'add',
        title: '充值',
        eachData: {
          oilId: id,
          oilNo,
          oilBalance: oilPrice
        }
      });
    },
    async handleGetVehicleOilListStatistics() {
      let res = await getVehicleOilListStatistics();
      if (res.success == false) {
        this.$message.error(res.message || '列表统计数据获取失败');
        return;
      }
      const {
        residueOilPrice,
        totalNumbers,
        totalOilPrice,
        totalRechargeNumbers
      } = res.object;

      this.oilCardList = [
        {
          title: '油卡总数',
          val: totalNumbers,
          unit: '张'
        },
        {
          title: '充值次数',
          val: totalRechargeNumbers,
          unit: '次'
        },
        {
          title: '油卡总额',
          val: totalOilPrice,
          unit: '元',
          format: true
        },
        {
          title: '剩余总额',
          val: residueOilPrice,
          red: true,
          unit: '元',
          format: true
        }
      ];
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { createDate = [] } = this.searchForm,
        [startDate = '', endDate = ''] = createDate,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          startDate,
          endDate,
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.createDate;
      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });
      let res = await vehicleOilList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.oilCard-management-box {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;

  .vehicle-statistics-box {
    display: flex;
    flex-direction: column;
    .vehicle-total,
    .vehicle-info {
      display: flex;
      margin-bottom: 8px;
      .total-item {
        flex: 1;
        padding: 4px 10px;
        padding-right: 0px;
        max-width: 150px;
        border-radius: 4px;
        margin-right: 12px;
        color: #333;
        > p {
          margin: 0;
        }
        .item-name {
          font-weight: 500;
        }
        .item-num {
          color: #5260ff;
          font-size: 18px;
          font-weight: 700;
        }
        .item-unit {
          color: #666666;
          font-size: 14px;
        }
        &.blue {
          background: rgba(239, 246, 255, 1);
        }
        &.red {
          background: rgba(255, 239, 239, 1);
          .item-num {
            color: red !important;
          }
        }
      }
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .label {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .success {
      background: #d4ffd4;
      color: #000;
    }

    .pengding {
      background: #eaeaea;
      color: #000;
    }

    .error {
      background: #ffd4d4;
      color: #000;
    }

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .operation-span {
      color: $primary-blue;
      margin-right: 8px;
      cursor: pointer;
    }
  }
}
</style>
