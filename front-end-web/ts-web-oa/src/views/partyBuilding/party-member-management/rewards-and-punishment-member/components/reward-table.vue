<template>
  <div>
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      @search="refresh"
    >
      <template slot="right">
        <ts-button type="primary" @click="openAddOrEditModal({})">
          新增
        </ts-button>
      </template>
    </ts-search-bar>
    <base-table
      ref="table"
      class="form-table"
      :columns="columns"
      @refresh="handleTableRefresh"
    />

    <ts-dialog
      :visible.sync="visible"
      :title="isEdit ? '编辑' : '新增'"
      type="large"
    >
      <ts-form ref="form" :model="editData">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              label="奖励党员姓名"
              prop="rewardPunishCode"
              :rules="requiredRow"
            >
              <base-select
                style="width: 100%"
                v-model="editData.rewardPunishCode"
                :inputText.sync="editData.rewardPunishName"
                :loadMethod="partyBuildingManageList"
                label="showManageUserName"
                value="manageUserCode"
                searchInputName="manageUserName"
                :clearable="false"
                @select="handlePersonSelect"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item label="党员类别" prop="type" :rules="requiredRow">
              <ts-select
                style="width: 100%"
                v-model="editData.type"
                clearable
                filterable
                disabled
                placeholder="请选择"
              >
                <ts-option
                  v-for="item of Dictionary.addPartyMemberType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              label="奖励名称"
              prop="rewardPunishContent"
              :rules="requiredRow"
            >
              <ts-input
                v-model="editData.rewardPunishContent"
                clearable
                placeholder="请输入奖励名称"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              label="奖励时间"
              prop="rewardPunishDate"
              :rules="requiredRow"
            >
              <ts-date-picker
                v-model="editData.rewardPunishDate"
                valueFormat="YYYY-MM-DD"
                style="width: 100%"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="原因">
          <ts-input
            v-model="editData.reason"
            type="textarea"
            class="textarea"
            maxlength="100"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>

        <ts-form-item label="附件">
          <base-upload ref="files" v-model="editData.files" />
        </ts-form-item>
      </ts-form>
      <template slot="footer">
        <ts-button type="primary" @click="handleSubmit">确定</ts-button>
        <ts-button @click="handleClose">取消</ts-button>
      </template>
    </ts-dialog>
  </div>
</template>

<script>
import Dictionary from '@/views/partyBuilding/dictionary.js';
import { deepClone } from '@/unit/commonHandle.js';

export default {
  components: {},
  data() {
    return {
      Dictionary,
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'rewardPunishName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名',
            clearable: true
          }
        },
        {
          label: '党员类别',
          value: 'type',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: Dictionary.addPartyMemberType
        },
        {
          label: '奖励日期',
          value: 'date',
          element: 'ts-range-picker',
          elementProp: {
            valueFormat: 'YYYY-MM-DD'
          }
        }
      ],

      columns: [
        {
          label: '',
          prop: 'pageIndex',
          width: 70,
          align: 'center'
        },
        {
          label: '姓名',
          width: 120,
          prop: 'rewardPunishName',
          align: 'center',
          formatter: row => {
            return (
              <span
                class="details-span"
                onClick={() => {
                  this.openDetailModal(row);
                }}>
                {row.rewardPunishName}
              </span>
            );
          }
        },
        {
          label: '党员类别',
          prop: 'type',
          width: 120,
          align: 'center',
          formatter: row => {
            let find =
              this.Dictionary.addPartyMemberType.find(
                item => item.value == row.type
              ) || {};
            return <span>{find.label || '-'}</span>;
          }
        },
        {
          label: '奖励名称',
          prop: 'rewardPunishContent',
          minWidth: 120,
          align: 'center'
        },
        {
          label: '奖励日期',
          prop: 'rewardPunishDate',
          width: 120,
          align: 'center'
        },
        {
          label: '创建人',
          prop: 'createUserName',
          width: 120,
          align: 'center'
        },
        {
          label: '创建时间',
          prop: 'createDate',
          width: 160,
          align: 'center'
        },
        {
          label: '操作',
          width: '100',
          fixed: 'right',
          formatter: row => {
            let actionList = [
              {
                label: '编辑',
                event: this.openAddOrEditModal,
                keyCode: 'edit'
              },
              {
                label: '删除',
                event: this.handleDelete,
                keyCode: 'edit',
                className: 'delete-span'
              }
            ];
            return (
              <BaseActionCell
                actions={actionList}
                on={{ 'action-select': e => e(row) }}
              />
            );
          }
        }
      ],

      visible: false,
      previewColumn: [
        {
          label: '奖励党员姓名',
          prop: 'rewardPunishName'
        },
        {
          label: '党员类别',
          prop: 'typeLabel'
        },
        {
          label: '奖励名称',
          prop: 'rewardPunishContent'
        },
        {
          label: '奖励日期',
          prop: 'rewardPunishDate'
        },
        {
          label: '原因',
          prop: 'reason',
          span: 2
        },
        {
          label: '附件',
          prop: 'file',
          span: 2
        }
      ],
      isEdit: false,
      requiredRow: { required: true, message: '必填' },
      editData: {}
    };
  },
  created() {
    this.$nextTick(() => {
      this.handleTableRefresh();
    });
  },
  methods: {
    forceUpdate() {
      this.$forceUpdate();
    },
    refresh() {
      this.$refs.table.pageNo = 1;

      this.searchForm.rewardPunishStartDate =
        this.searchForm.date && (this.searchForm.date[0] || '');
      this.searchForm.rewardPunishEndDate =
        this.searchForm.date && (this.searchForm.date[1] || '');
      this.handleTableRefresh();
    },

    async handleTableRefresh() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          rewardPunishStatus: 1,
          sidx: 'create_date',
          sord: 'desc'
        };
      delete searchForm.date;
      let res = await this.ajax.partyBuildingRewardPunishList(searchForm);
      this.$parent.tabList[0].totalNum = res.totalCount;

      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let pageIndex = (pageNo - 1) * pageSize + i + 1;
        return {
          pageIndex,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    /**@desc 打开新增编辑弹窗 */
    openAddOrEditModal(row = {}) {
      this.isEdit = !!Object.keys(row).length;
      this.editData = deepClone(row);
      this.visible = true;

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    async handleSubmit() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      const data = Object.assign({}, this.editData);
      data.rewardPunishStatus = 1;
      let API = null;
      if (!this.isEdit) {
        API = this.ajax.partyBuildingRewardPunishSave;
      } else {
        API = this.ajax.partyBuildingRewardPunishUpdate;
      }
      const res = await API(data);

      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }
      this.handleClose();
      this.$message.success('操作成功!');
      this.handleTableRefresh();
    },

    async handleDelete(row) {
      const { id } = row;
      try {
        await this.$confirm(`<span>您确认删除这条数据吗？</span>`, '提示', {
          type: 'warning',
          dangerouslyUseHTMLString: true
        });
        const res = await this.ajax.partyBuildingRewardPunishDelete(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleTableRefresh();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    handleClose() {
      this.visible = false;
    },

    async partyBuildingManageList(data) {
      let res = await this.ajax.partyBuildingManageList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '党员数据获取失败');
        return false;
      }
      res.rows.forEach(item => {
        item.showManageUserName = `${item.manageUserName} - ${item.manageDeptName}`;
      });
      return res.rows;
    },
    handlePersonSelect(res) {
      const { type, manageUserCode, manageUserName } = res;
      this.$set(this.editData, 'type', type);
      this.$set(this.editData, 'rewardPunishCode', manageUserCode);
      this.$set(this.editData, 'rewardPunishName', manageUserName);
    },
    openDetailModal(row = {}) {
      let detailData = deepClone(row);
      let find =
        this.Dictionary.addPartyMemberType.find(
          item => item.value == row.type
        ) || {};
      detailData.typeLabel = find.label || '-';

      this.$emit('preview', this.previewColumn, detailData);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .textarea {
    .el-textarea__inner {
      min-height: 110px !important;
      max-height: 200px !important;
    }
  }

  .form-table {
    flex: 1;
    overflow: hidden;
    transform: scale(1);

    .details-span {
      color: $primary-blue;
      cursor: pointer;
    }

    .delete-span {
      color: red;
      cursor: pointer;
    }
  }
}
</style>
