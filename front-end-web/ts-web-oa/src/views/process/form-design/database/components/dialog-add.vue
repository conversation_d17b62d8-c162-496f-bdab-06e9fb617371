<template>
  <ts-dialog
    class="dialog-edit-form"
    :title="title"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="content">
      <div class="title">数据库基本信息</div>
      <ts-form ref="form" :model="form" labelWidth="110px">
        <ts-row>
          <ts-col :span="12">
            <ts-form-item
              prop="tableName"
              :rules="rules.tableName"
              label="表名"
            >
              <ts-input
                v-model="form.tableName"
                :disabled="api == 'update'"
                placeholder="表名必须以字母开头，数字字母下划线结尾。长度限制100"
                maxlength="100"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              prop="tableComment"
              :rules="rules.required"
              label="表描述"
            >
              <ts-input v-model="form.tableComment" maxlength="100" />
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-row>
          <ts-col :span="8">
            <ts-form-item
              prop="editLine"
              :rules="rules.required"
              labelWidth="210px"
              label=" 是否允许审批时新增/删除行"
            >
              <ts-switch
                v-model="form.editLine"
                inactive-value="0"
                active-value="1"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="8">
            <ts-form-item
              prop="showType"
              :rules="rules.required"
              label=" 展现形式"
            >
              <ts-radio-group v-model="form.showType">
                <ts-radio :label="1">表格</ts-radio>
                <ts-radio :label="2">卡片</ts-radio>
              </ts-radio-group>
            </ts-form-item>
          </ts-col>
          <ts-col :span="8">
            <ts-form-item
              prop="workflowAlone"
              :rules="rules.required"
              labelWidth="210px"
              label=" 提交时按单项生成流程记录"
            >
              <ts-switch
                v-model="form.workflowAlone"
                :inactive-value="0"
                :active-value="1"
              />
            </ts-form-item>
          </ts-col>
        </ts-row>
      </ts-form>
    </div>
    <div class="content">
      <div class="title">
        数据库属性
        <ts-button @click="addRow">新增字段</ts-button>
      </div>
      <ts-form ref="formTable" :model="form" labelWidth="110px">
        <form-table
          ref="FormTable"
          :formData="form"
          operateDataKey="fields"
          :disabled="true"
          :loaclColumns="[
            {
              type: 'index',
              label: '',
              index: 1,
              align: 'center',
              width: 40
            }
          ]"
          :columns="columns"
        >
          <!-- 字段名称 -->
          <template v-slot:fieldName="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 字段描述 -->
          <template v-slot:remark="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 字段类型 -->
          <template v-slot:fieldType="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-select
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @change="fieldTypeChange(row)"
                @blur="row[`${column.property}Edit`] = false"
              >
                <ts-option
                  v-for="(item, index) in fieldTypeList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></ts-option>
              </ts-select>
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ getFieldTypeName(row[column.property]) }}
              </div>
            </ts-form-item>
          </template>
          <!-- 字段长度 -->
          <template v-slot:fieldLength="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                v-model="row[column.property]"
                :ref="`${column.property}Edit${index}`"
                @input="propertyInput(row, column.property)"
                @blur="fieldLengthBlur(row, index, column.property)"
                :disabled="
                  ['DATEPICKER', 'FILE', 'EXPRESSION'].includes(row.fieldType)
                "
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEditfieldLength(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 小数点长度 -->
          <template v-slot:pointLength="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                v-model="row[column.property]"
                :ref="`${column.property}Edit${index}`"
                @input="propertyInput(row, column.property)"
                @blur="pointLengthBlur(row, index, column.property)"
                :disabled="!['NUMBER', 'EXPRESSION'].includes(row.fieldType)"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEditpointLength(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 字段选项 -->
          <template v-slot:optionValue="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-model="row[column.property]"
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                @blur="row[`${column.property}Edit`] = false"
                :disabled="
                  !['SELECT', 'MULTIPLESELECT'].includes(row.fieldType)
                "
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEditoptionValue(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 提示文字 -->
          <template v-slot:promptText="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-model="row[column.property]"
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 默认值 -->
          <template v-slot:defaultValue="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @input="defaultValueInput(row)"
                @blur="defaultValueBlur(row, index, column.property)"
                @focus="defaultValueFocus(row, index, column.property)"
                :disabled="row.fieldType == 'FILE'"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEditdefaultValue(row, index, column.property)"
                v-else
              >
                {{
                  row.fieldType == 'EXPRESSION'
                    ? '编辑公式'
                    : row[column.property]
                }}
              </div>
            </ts-form-item>
          </template>
          <!-- 是否启用 -->
          <template v-slot:pcShow="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                :inactive-value="0"
                :active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 发起必填 -->
          <template v-slot:isNull="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                :inactive-value="0"
                :active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 发起只读 -->
          <template v-slot:isReadOnly="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                :inactive-value="0"
                :active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 发起隐藏 -->
          <template v-slot:isHide="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                inactive-value="0"
                active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 查询项 -->
          <template v-slot:isQuery="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                inactive-value="0"
                active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 提醒项 -->
          <template v-slot:isRemind="{ row, column, index }">
            <ts-form-item
              class="flex-item swtichCenter"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-switch
                v-model="row[column.property]"
                inactive-value="0"
                active-value="1"
              />
            </ts-form-item>
          </template>
          <!-- 字段排序 -->
          <template v-slot:seq="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @input="propertyInput(row, column.property)"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 列表宽度 -->
          <template v-slot:fieldWidth="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @input="propertyInput(row, column.property)"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 打印样式 -->
          <template v-slot:styleStr="{ row, column, index }">
            <ts-form-item
              class="flex-item"
              label=""
              label-width="0"
              :prop="`fields.${index}.${column.property}`"
            >
              <ts-input
                v-if="row[`${column.property}Edit`]"
                :ref="`${column.property}Edit${index}`"
                v-model="row[column.property]"
                @blur="row[`${column.property}Edit`] = false"
              />
              <div
                class="showText"
                :title="row[column.property]"
                @click="canEdit(row, index, column.property)"
                v-else
              >
                {{ row[column.property] }}
              </div>
            </ts-form-item>
          </template>
          <!-- 操作 -->
          <template v-slot:actions="{ row, column, index }">
            <ts-form-item class="flex-item" label="" label-width="0">
              <div @click="delRow(index)" class="action" v-if="row.isSyn != 1">
                删除
              </div>
            </ts-form-item>
          </template>
        </form-table>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="submitLoading" @click="submit"
        >提 交</ts-button
      >
      <ts-button @click="close">关 闭</ts-button>
    </span>
    <dialog-expression ref="dialogExpression" @ok="defaultValueOk" />
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import FormTable from '@/components/form-table/form-table.vue';
import dialogExpression from './dialog-expression.vue';
export default {
  components: { FormTable, dialogExpression },
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {
        showType: 1,
        workflowAlone: 0,
        editLine: '0',
        fields: []
      },
      expressionIndex: null,
      fieldTypeList: [
        {
          label: '单行文本',
          value: 'VARCHAR'
        },
        {
          label: '多行文本',
          value: 'TEXTAREA'
        },
        {
          label: '下拉单选',
          value: 'SELECT'
        },
        {
          label: '下拉多选',
          value: 'MULTIPLESELECT'
        },
        {
          label: '日期',
          value: 'DATEPICKER'
        },
        {
          label: '数值',
          value: 'NUMBER'
        },
        {
          label: '附件',
          value: 'FILE'
        },
        {
          label: '计算公式',
          value: 'EXPRESSION'
        }
      ],
      rules: {
        required: { required: true, message: '必填' },
        tableName: [
          { required: true, message: '必填' },
          {
            validator: async (prop, value, callback) => {
              let reg = /^[A-z][A-z0-9_]{0,99}$/;
              if (value && !reg.test(value)) {
                callback('表名必须以字母开头，数字字母下划线结尾。长度限制100');
                return;
              } else {
                let param = {
                  tableName: 'zt_' + value,
                  id: this.form.id || ''
                };
                let res = await this.ajax.checktable(param);
                if (!res.success) {
                  callback(new Error(res.message));
                  return;
                } else {
                  callback();
                }
              }
            },
            trigger: ['blur']
          }
        ]
      },
      columns: [
        {
          prop: 'fieldName',
          label: '字段名称'
        },
        {
          prop: 'fieldNameEdit',
          defaultValue: false,
          hide: true,
          label: '字段名称编辑状态'
        },
        {
          prop: 'remark',
          label: '字段描述'
        },
        {
          prop: 'remarkEdit',
          defaultValue: false,
          hide: true,
          label: '字段描述'
        },
        {
          prop: 'fieldType',
          label: '字段类型',
          defaultValue: 'VARCHAR'
        },
        {
          prop: 'fieldTypeEdit',
          defaultValue: false,
          hide: true,
          label: '字段类型'
        },
        {
          prop: 'fieldLength',
          defaultValue: 255,
          label: '字段长度'
        },
        {
          prop: 'fieldLengthEdit',
          defaultValue: false,
          hide: true,
          label: '字段长度'
        },
        {
          prop: 'pointLength',
          label: '小数点长度'
        },
        {
          prop: 'pointLengthEdit',
          defaultValue: false,
          hide: true,
          label: '小数点长度'
        },
        {
          prop: 'optionValue',
          label: '字段选项'
        },
        {
          prop: 'optionValueEdit',
          defaultValue: false,
          hide: true,
          label: '字段选项'
        },
        // {
        //   prop: 'promptText',
        //   label: '提示文字'
        // },
        // {
        //   prop: 'promptTextEdit',
        //   defaultValue: false,
        //   hide: true,
        //   label: '提示文字状态'
        // },
        {
          prop: 'defaultValue',
          label: '默认值'
        },
        {
          prop: 'defaultValueEdit',
          defaultValue: false,
          hide: true,
          label: '默认值'
        },
        {
          prop: 'pcShow',
          defaultValue: 1,
          label: '是否启用'
        },
        {
          prop: 'isNull',
          defaultValue: 1,
          label: '发起必填'
        },
        {
          prop: 'isReadOnly',
          label: '发起只读'
        },
        {
          prop: 'isHide',
          defaultValue: '0',
          label: '发起隐藏'
        },
        {
          prop: 'isQuery',
          defaultValue: '0',
          label: '查询项'
        },
        {
          prop: 'isRemind',
          defaultValue: '0',
          label: '提醒项'
        },
        {
          prop: 'seq',
          label: '字段排序'
        },
        {
          prop: 'seqEdit',
          defaultValue: false,
          hide: true,
          label: '字段排序'
        },
        {
          prop: 'fieldWidth',
          label: '列表宽度'
        },
        {
          prop: 'fieldWidthEdit',
          defaultValue: false,
          hide: true,
          label: '列表宽度'
        },
        {
          prop: 'styleStr',
          label: '打印样式'
        },
        {
          prop: 'styleStrEdit',
          defaultValue: false,
          hide: true,
          label: '打印样式'
        },
        {
          prop: 'isSyn',
          label: '同步',
          hide: true,
          defaultValue: 0
        },
        {
          prop: 'actions',
          label: '操作'
        }
      ],
      title: '',
      api: ''
    };
  },
  methods: {
    async open(data = null) {
      if (data) {
        this.form = await this.getDpTablefindById(data);
        this.title = '编辑数据库表';
        this.api = 'update';
      } else {
        this.title = '新增数据库表';
        this.api = 'insert';
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },
    async getDpTablefindById(id) {
      let res = await this.ajax.getDpTablefindById(id);
      if (res.success) {
        if (res.object.fields) {
          res.object.fields.forEach(e => {
            e.isSyn = res.object.isSyn;
            for (let key in e) {
              e[`${key}Edit`] = false;
            }
          });
        }
        res.object.tableName =
          res.object.tableName.split('zt_')[1] || res.object.tableName;
        return res.object || {};
      } else {
        this.$message.error(res.message || '获取表数据失败');
        return {};
      }
    },
    canEdit(row, index, property) {
      row[`${property}Edit`] = true;
      this.$nextTick(() => {
        this.$refs[`${property}Edit${index}`] &&
          this.$refs[`${property}Edit${index}`].focus();
      });
    },
    canEditfieldLength(row, index, property) {
      if (['DATEPICKER', 'FILE', 'EXPRESSION'].includes(row.fieldType)) {
        return;
      }
      row[`${property}Edit`] = true;
      this.$nextTick(() => {
        this.$refs[`${property}Edit${index}`].focus();
      });
    },
    canEditdefaultValue(row, index, property) {
      if (row.fieldType == 'FILE') {
        return;
      }
      row[`${property}Edit`] = true;
      this.$nextTick(() => {
        this.$refs[`${property}Edit${index}`].focus();
      });
    },
    canEditpointLength(row, index, property) {
      if (!['NUMBER', 'EXPRESSION'].includes(row.fieldType)) {
        return;
      }
      row[`${property}Edit`] = true;
      this.$nextTick(() => {
        this.$refs[`${property}Edit${index}`].focus();
      });
    },
    canEditoptionValue(row, index, property) {
      if (!['SELECT', 'MULTIPLESELECT'].includes(row.fieldType)) {
        return;
      }
      row[`${property}Edit`] = true;
      this.$nextTick(() => {
        this.$refs[`${property}Edit${index}`].focus();
      });
    },
    addRow() {
      this.$refs.FormTable.handleFormTableAddRow();
    },
    delRow(index) {
      this.form.fields.splice(index, 1);
    },
    fieldTypeChange(row) {
      if (!['SELECT', 'MULTIPLESELECT'].includes(row.fieldType)) {
        row.optionValue = '';
      }
      if (row.fieldType == 'DATEPICKER') {
        row.fieldLength = '';
      } else {
        row.fieldLength.trim && row.fieldLength.trim();
        !row.fieldLength && (row.fieldLength = 255);
        if (row.fieldType == 'FILE') {
          row.defaultValue = '';
        }
        if (row.fieldType != 'NUMBER') {
          row.pointLength = '';
        }
      }
      row.fieldTypeEdit = false;
    },
    propertyInput(row, property) {
      let val = row[property] || '',
        matchList = val.match(/\d+/g) || [],
        newVal = matchList[0] ? Math.abs(matchList[0]) : '';
      row[property] = newVal;
    },
    fieldLengthBlur(row, index, property) {
      if (
        row.fieldLength &&
        row.defaultValue.length > Number(row.fieldLength)
      ) {
        row.defaultValue = row.defaultValue.slice(0, row.fieldLength);
      }
      row[`${property}Edit`] = false;
    },
    pointLengthBlur(row, index, property) {
      let val = row.pointLength,
        fieldType = row.fieldType,
        defaultValue = row.defaultValue || '';
      if (val > 4) {
        row.pointLength = 4;
      }
      if (fieldType == 'NUMBER' && Number(defaultValue) && val) {
        let newDefaultValue,
          matchList = defaultValue.match(/\d+/g) || [];
        if (matchList.length > 1 && matchList[1].length > newVal) {
          if (newVal == 0) {
            matchList.splice(1, 1);
          } else {
            matchList[1] = matchList[1].slice(0, newVal);
          }
          newDefaultValue = matchList.join('.');
          if (defaultValue.indexOf('-') == 0 && newDefaultValue != 0) {
            newDefaultValue = '-' + newDefaultValue;
          }
        } else {
          newDefaultValue = parseFloat(defaultValue).toFixed(Number(newVal));
        }
        row.defaultValue = newDefaultValue;
      }
      row[`${property}Edit`] = false;
    },
    defaultValueInput(row) {
      let { fieldType, pointLength = '', defaultValue } = row;
      if (fieldType == 'NUMBER') {
        let isNegative = defaultValue.indexOf('-') == 0;
        let newVal =
          (isNegative ? '-' : '') +
          defaultValue
            .replace(/[^\d\.]/g, '')
            .replace('.', '$#$')
            .replace(/\./g, '')
            .replace('$#$', '.');
        if (pointLength) {
          let matchList = newVal.match(/\d+/g) || [];
          if (matchList.length > 1 && matchList[1].length >= pointLength) {
            matchList[1] = matchList[1].slice(0, pointLength);
            newVal = matchList.join('.');
            if (isNegative) {
              newVal = '-' + newVal;
            }
          }
        } else if (pointLength == 0) {
          let matchList = newVal.match(/\d+/g) || [];
          newVal = matchList[0] || '';
          if (isNegative) {
            newVal = '-' + newVal;
          }
        }
        row.defaultValue = newVal;
      }
    },
    defaultValueBlur(row, index, property) {
      let {
        fieldLength = '0',
        pointLength = '',
        fieldType,
        defaultValue
      } = row;
      fieldLength = parseFloat(fieldLength);
      if (isNaN(fieldLength) || fieldType == 'EXPRESSION') {
        return;
      }
      if (fieldType == 'NUMBER') {
        let newVal = parseFloat(defaultValue);
        if (isNaN(newVal)) {
          row.defaultValue = '';
          return;
        }
        if (defaultValue) {
          if (newVal == 0 || !pointLength) {
            row.defaultValue = parseFloat(defaultValue);
            return;
          } else {
            row.defaultValue = newVal.toFixed(Number(pointLength));
            return;
          }
        }
      }
      if (defaultValue.length > fieldLength) {
        row.defaultValue = row.defaultValue.slice(0, fieldLength);
      }
      row[`${property}Edit`] = false;
    },
    defaultValueFocus(row, expressionIndex, property) {
      if (row.fieldType != 'EXPRESSION') {
        return;
      }
      let list = [];
      this.form.fields.forEach(item => {
        if (
          ['NUMBER', 'DATEPICKER', 'VARCHAR', 'TEXTAREA'].includes(
            item.fieldType
          )
        ) {
          let fieldType =
            item.fieldType == 'NUMBER'
              ? 'number'
              : item.fieldType == 'DATEPICKER'
              ? 'date'
              : 'input';
          list.push({
            fieldName: item.fieldName,
            fieldType: fieldType,
            showName: item.remark
          });
        }
      });
      row[`${property}Edit`] = false;
      this.$refs.dialogExpression.open(row, list, expressionIndex);
    },
    defaultValueOk(content, index) {
      this.form.fields[index].defaultValue = content;
    },
    getFieldTypeName(fieldType) {
      let obj = this.fieldTypeList.find(e => e.value == fieldType);
      return obj.label;
    },
    dealTableData(data) {
      let reg = /^[A-z][A-z0-9_]{1,49}$/,
        dateReg = /^((\d{3}[1-9]|\d{2}[1-9]\d|\d[1-9]\d{2}|[1-9]\d{3})\-(((0[13578]|1[02])\-(0[1-9]|[12]\d|3[01]))|((0[469]|11)\-(0[1-9]|[12]\d|30))|(02\-(0[1-9]|[1]\d|2[0-8])))$)|(((\d{2})(0[48]|[2468][048]|[13579][26])|((0[48]|[2468][048]|[3579][26])00))\-02\-29)$/;
      let res = {};
      let rules = [];
      for (let i = 0; i < data.length; i++) {
        if (!res[data[i].fieldName]) {
          res[data[i].fieldName] = data[i];
        }
        if (data[i].fieldName && !reg.test(data[i].fieldName)) {
          rules.push(data[i].fieldName);
        }
        //  校验 日期 数值 默认值格式
        if (data[i].defaultValue) {
          let { fieldType, defaultValue, fieldLength } = data[i];
          if (fieldType == 'DATEPICKER' && !dateReg.test(defaultValue)) {
            rules.push(defaultValue);
          }
          if (fieldLength && defaultValue.length > fieldLength) {
            rules.push(defaultValue);
          }
        }
      }
      if (rules.length) {
        this.$message.error(`以下字段不符合要求${rules.join(',')}`);
        return false;
      }
      return true;
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        let index = [];
        let index1 = [];
        data.fields.forEach((e, i) => {
          for (let key in e) {
            if (key.indexOf('Edit') > -1) {
              delete e[key];
            }
          }
          if (e.fieldName.trim() == '') {
            index.push(i + 1);
          }
          if (e.fieldType == 'EXPRESSION' && e.defaultValue == '') {
            index1.push(i + 1);
          }
        });
        if (index.length || index1.length) {
          let msg = index.length
            ? `序号未${index.join(',')}的字段名不能为空`
            : '';
          let msg1 = index1.length
            ? ` 序号未${index.join(',')}的计算公式不能为空`
            : '';
          this.$alert(`${msg}${msg1}`, '提示', {
            confirmButtonText: '确定'
          });
          return false;
        }
        if (!this.dealTableData(data.fields)) {
          return false;
        }
        data.fields.sort((prev, next) => {
          return prev.seq - next.seq;
        });
        this.submitLoading = true;
        const res = await this.ajax.dpTableDo(data, this.api);
        if (res.success) {
          this.submitLoading = false;
          this.$message.success('操作成功!');
          this.$emit('ok');
          this.close();
        } else {
          this.submitLoading = false;
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {
        showType: 1,
        workflowAlone: 0,
        editLine: '0',
        fields: []
      };
      this.api = '';
      this.$refs.form.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-edit-form {
  ::v-deep {
    .el-dialog {
      overflow: hidden;
    }
    .el-dialog__body {
      width: 100% !important;
      overflow: auto;
    }
    .el-dialog__footer {
      width: 100% !important;
      text-align: center;
      background-color: #ffffff;
      padding-bottom: 20px !important;
    }
    .content {
      .form-table-box {
        .el-table {
          height: 100% !important;
        }
      }
      .title {
        margin-bottom: 10px;
        height: 30px;
        position: relative;
        .ts-button {
          position: absolute;
          right: 0;
        }
        &::before {
          content: '1';
          background-color: rgb(82, 96, 255);
          margin-right: 4px;
          height: 16px;
          width: 4px;
          border-radius: 4px;
          color: rgb(82, 96, 255);
        }
      }
      .flex-item {
        .el-form-item__content {
          display: flex;
          align-items: center;
        }
        .showText {
          width: 100%;
          height: 30px;
          line-height: 30px;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          cursor: pointer;
        }
        &.swtichCenter {
          display: flex;
          justify-content: center;
        }
        .el-input {
          min-width: 100% !important;
        }
        .action {
          width: 100%;
          text-align: center;
          color: rgb(82, 96, 255);
          cursor: pointer;
        }
      }
    }
  }
}
</style>
