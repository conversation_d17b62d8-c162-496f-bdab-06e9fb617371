<template>
  <ts-dialog
    class="dialog-children-table"
    :title="title"
    width="80%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <div class="body">
      <ts-search-bar
        v-model="searchForm"
        :actions="actions"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="{}"
      >
        <template v-for="(item, index) in searchList" :slot="item.value">
          <ts-input
            :key="index"
            v-if="item.type == 'input'"
            v-model="searchForm[item.value]"
            @keydown.enter.native="search"
            :placeholder="`请输入${item.label}`"
          />
          <div v-if="item.type == 'date'" :key="index">
            <ts-range-picker
              v-model="searchForm[item.value]"
              style="width: 100%"
              type="daterange"
              valueFormat="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            />
          </div>
          <input-ztree
            v-if="item.type == 'tree'"
            v-model="searchForm[item.value]"
            ref="tree"
            placeholder="请选择表单分类"
            :treeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
            style="width: 100%"
            @ok="handleOk"
            :key="item.code"
          />
          <ts-select
            :key="index"
            v-model="searchForm[item.value]"
            :clearable="item.elementProp.clearable"
            v-if="item.type == 'select'"
          >
            <ts-option
              v-for="(option, oIndex) in item.childNodeList"
              :key="oIndex"
              :label="option.label"
              :value="option.value"
            ></ts-option>
          </ts-select>
        </template>
        <template slot="right">
          <ts-button type="primary" @click="handleExport">
            导出
          </ts-button>
        </template>
      </ts-search-bar>
      <TsVxeTemplateTable
        class="form-table"
        ref="table"
        auto-resize
        :showOverflow="'title'"
        :columns="columns"
        :tooltipConfig="{
          showAll: false
        }"
        :pageSizes="[20, 50, 100, 200]"
        :defaultPageSize="20"
        :defaultSort="{
          sord: 'desc',
          sidx: 'create_date'
        }"
        @refresh="handleGetTableData"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import dialogChildrenTable from '../minix/dialog-children-table';
export default {
  mixins: [dialogChildrenTable],
  data() {
    return {
      visible: false,
      title: '',
      tableInfo: {}
    };
  },
  methods: {
    open(table) {
      this.tableInfo = table;
      this.title = this.tableInfo.tableName;
      this.getChildHeadList();
      this.visible = true;
      this.$nextTick(() => {
        this.search();
      });
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.handleGetTableData();
    },
    async handleExport() {
      let submitSearchFormData = this.handleGetSearchFormData();
      let queryMap = {};
      if (Object.keys(submitSearchFormData.queryMap).length != 0) {
        queryMap = { ...submitSearchFormData.queryMap };
      }
      let querParam = {
        ...submitSearchFormData,
        ...queryMap
      };
      delete querParam.queryMap;
      await this.ajax.downloadProcess(
        '/ts-form/dpTable/exportAllChildData',
        this.tableInfo.tableName + '.xlsx',
        querParam
      );
    },
    async handleGetTableData() {
      let pageNo = this.$refs.table.pageNo;
      let pageSize = this.$refs.table.pageSize;
      let submitSearchFormData = this.handleGetSearchFormData();
      let res = await this.ajax.getAllChildDataList(submitSearchFormData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-children-table {
  .body {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      .vxe-cell {
        p {
          margin: 0;
        }
      }
    }
  }
}
</style>
