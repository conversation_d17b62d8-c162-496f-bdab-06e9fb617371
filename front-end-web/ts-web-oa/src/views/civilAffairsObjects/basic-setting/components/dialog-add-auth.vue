<template>
  <ts-dialog
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="800"
    @close="close"
  >
    <div class="content">
      <ts-form class="add-form" ref="form" :model="form" labelWidth="130px">
        <ts-row>
          <ts-form-item
            label="管理人员"
            prop="personName"
            :rules="rules.required"
          >
            <ts-input v-model="form.personName" readonly>
              <template v-slot:suffix>
                <img
                  class="person-icon"
                  src="@/assets/img/defUserPhoto.png"
                  @click="handleOpenSelectPerson('personId')"
                />
              </template>
            </ts-input>
          </ts-form-item>
        </ts-row>

        <ts-row>
          <ts-form-item label="管理内容" prop="groupId" :rules="rules.required">
            <ts-select
              style="width: 100%"
              multiple
              v-model="form.groupId"
              clearable
              placeholder="请选择"
            >
              <div class="item-all-select" @click="handleClickAll">
                全部
              </div>
              <ts-option
                v-for="item of typeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              ></ts-option>
            </ts-select>
          </ts-form-item>
        </ts-row>
      </ts-form>
    </div>

    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="loading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <ts-user-dept-select ref="SelectPerson" @ok="handleOk" />
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import TsUserDeptSelect from '@/components/ts-user-dept-select/index.vue';
import tabsMixins from '@/views/civilAffairsObjects/configMixins/tabsMixins.js';

export default {
  mixins: [tabsMixins],
  components: {
    TsUserDeptSelect
  },
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      type: '',

      typeOptions: [],

      form: {
        groupId: [],
        personName: '',
        personId: ''
      },
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open({ row = {}, title, type }) {
      this.title = title;
      this.type = type;
      this.typeOptions = [];

      Object.entries(this.objectTabRefs).forEach(([value, { label }]) => {
        this.typeOptions.push({
          label,
          value
        });
      });

      if (type == 'add') {
        this.$set(this, 'form', {
          groupId: [],
          personName: '',
          personId: ''
        });
      } else {
        row.groupId = row.groupId.split(',');
        this.$set(this, 'form', deepClone(row));
      }

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    handleClickAll() {
      if (this.form.groupId.length === this.typeOptions.length) {
        this.$set(this.form, 'groupId', []);
      } else {
        this.$set(
          this.form,
          'groupId',
          this.typeOptions.map(f => f.value)
        );
      }
    },

    handleOpenSelectPerson(key) {
      const keyObject = {
        personId: 'personName'
      };
      const valStr = this.form[key];
      const nameStr = this.form[keyObject[key]];
      let empList = [];
      if (valStr && nameStr) {
        let valArr = valStr.split(',');
        let nameArr = nameStr.split(',');

        empList = valArr.map((item, index) => {
          return {
            empCode: item,
            empName: nameArr[index]
          };
        });
      }
      this.$refs.SelectPerson.open(key, {
        appendToBody: true,
        showCheckbox: false,
        title: '选择',
        deptList: [],
        empList,
        isRadio: false
      });
    },

    handleOk(result, key) {
      const keyObject = {
        personId: 'personName'
      };
      const { empList = [] } = result[key];

      const selectCodeArr = empList.map(item => item.empCode);
      const selectNameArr = empList.map(item => item.empName);

      this.$set(this.form, [key], selectCodeArr.join(','));
      this.$set(this.form, [keyObject[key]], selectNameArr.join(','));

      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });

      this.$forceUpdate();
    },

    async submit() {
      try {
        await this.$refs.form.validate();

        const data = deepClone(this.form);
        let API = null;
        if (this.type === 'add') {
          API = this.ajax.CivilAffairsAuthoritySave;
        } else {
          API = this.ajax.CivilAffairsAuthoritypdate;
        }

        let groupName = [];
        data.groupId.forEach(fv => {
          let find = this.typeOptions.find(f => f.value == fv);
          if (find) {
            groupName.push(find.label);
          }
        });

        data.groupId = data.groupId.join(',');
        data.groupName = groupName.join(',');

        this.loading = true;
        const res = await API(data);
        this.loading = false;
        if (!res.success) {
          this.$message.error(res.message || '操作失败');
          return;
        }
        this.close();
        this.$message.success('操作成功!');
        this.$emit('refresh');
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .suffix-input {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-left: 1px solid #eee;
    color: rgb(121, 121, 121);
    padding-left: 4px;
  }
  .person-icon {
    margin-top: 3px;
    width: 24px;
    height: 24px;
    cursor: pointer;
  }
}
.item-all-select {
  line-height: 30px;
  padding: 0 16px 0 40px;
  cursor: pointer;
  &:hover {
    // background: rgb(241, 242, 255);
  }
}
</style>
