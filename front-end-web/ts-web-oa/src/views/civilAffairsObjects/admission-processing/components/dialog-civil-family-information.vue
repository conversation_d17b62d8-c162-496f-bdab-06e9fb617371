<template>
  <ts-dialog
    :title="title"
    :visible.sync="visible"
    :append-to-body="true"
    width="800"
    @close="close"
  >
    <div class="content">
      <ts-form class="add-form" ref="form" :model="form" labelWidth="130px">
        <ts-row>
          <ts-col v-if="type == 'add'" :span="12">
            <ts-form-item
              prop="personnelId"
              :rules="rules.required"
              label="姓名"
            >
              <base-select
                style="width: 100%"
                v-model="form.personnelId"
                :inputText.sync="form.name"
                :loadMethod="handleGetPerson"
                label="name"
                value="personnelId"
                searchInputName="name"
                :clearable="false"
              />
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item
              label="家属姓名"
              prop="familyName"
              :rules="rules.required"
            >
              <ts-input
                v-model="form.familyName"
                :maxlength="10"
                placeholder="请输入"
              >
              </ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-row>
          <ts-col :span="12">
            <ts-form-item label="关系">
              <ts-input
                v-model="form.relationship"
                :maxlength="20"
                placeholder="请输入"
              >
              </ts-input>
            </ts-form-item>
          </ts-col>

          <ts-col :span="12">
            <ts-form-item label="电话">
              <ts-input
                v-model="form.telephone"
                :maxlength="11"
                placeholder="请输入"
                @input="
                  value => (form.telephone = (value.match(/\d+/g) || [''])[0])
                "
              >
              </ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>

        <ts-form-item label="备注">
          <ts-input
            v-model="form.remarks"
            type="textarea"
            class="textarea"
            maxlength="500"
            placeholder="请输入"
            show-word-limit
          />
        </ts-form-item>
      </ts-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <ts-button type="primary" :loading="loading" @click="submit">
        提 交
      </ts-button>
      <ts-button @click="close">关 闭</ts-button>
    </span>
  </ts-dialog>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';

export default {
  data() {
    return {
      visible: false,
      loading: false,
      title: '',
      type: '',

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  methods: {
    open({ data, title, type }) {
      this.title = title;
      this.type = type;
      this.$set(this, 'form', deepClone(data));

      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
      this.visible = true;
    },

    async handleGetPerson(data) {
      let res = await this.ajax.CivilAffairsPersonnelInfoList({
        pageSize: 15,
        ...data
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },

    async submit() {
      try {
        await this.$refs.form.validate();

        const data = deepClone(this.form);

        let API = null;
        if (this.type === 'add') {
          API = this.ajax.CivilFamilyInformationSave;
        } else {
          API = this.ajax.CivilFamilyInformationUpdate;
        }

        this.loading = true;
        const res = await API(data);
        this.loading = false;

        if (!res.success) {
          this.$message.error(res.message || '操作失败');
          return;
        }
        this.close();
        this.$message.success('操作成功!');
        this.$emit('refresh');
      } catch (e) {
      } finally {
        this.loading = false;
      }
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
