<template>
  <div class="supplier-item-anagement">
    <ts-search-bar
      v-model="searchForm"
      :formList="searchList"
      @search="search"
      :resetData="{}"
    >
      <template slot="right">
        <ts-button type="primary" @click="handleAdd">
          新增
        </ts-button>
        <ts-button type="primary" @click="handleExport" :loading="exportLoaing">
          导出
        </ts-button>
      </template>
    </ts-search-bar>

    <base-table
      class="form-table"
      ref="table"
      border
      stripe
      v-loading="loading"
      :columns="columns"
      @row-dblclick="handleEdit"
      @refresh="handleRefreshTable"
    />

    <dialog-add ref="DialogAdd" @refresh="handleRefreshTable" />
  </div>
</template>

<script>
import table from './mixins/table';
import DialogAdd from './components/dialog-add.vue';

export default {
  mixins: [table],
  components: {
    DialogAdd
  },
  data() {
    return {
      exportLoaing: false
    };
  },
  async created() {},
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    refresh() {
      this.$nextTick(async () => {
        this.handleRefreshTable();
      });
    },

    handleAdd() {
      this.$refs.DialogAdd.open({
        type: 'add',
        row: {}
      });
    },

    handleEdit(row) {
      this.$refs.DialogAdd.open({
        type: 'edit',
        row
      });
    },

    handleExport() {
      try {
        this.exportLoaing = true;

        let pageNo = this.$refs.table.pageNo;
        let pageSize = this.$refs.table.pageSize;
        let data = {
            ...this.searchForm,
            pageNo,
            pageSize,
            sidx: 'create_date',
            sord: 'desc'
          },
          queryList = [],
          aDom = document.createElement('a');

        Object.keys(data).map(key => {
          queryList.push(key + '=' + data[key]);
        });
        aDom.href =
          '/ts-oa/api/CivilAffairsGoodsManagement/exportPageList?' +
          queryList.join('&');
        aDom.click();
      } catch (error) {
      } finally {
        setTimeout(() => {
          this.exportLoaing = false;
        }, 1000);
      }
    },

    async handleDelete(row) {
      try {
        await this.$confirm(`是否确定删除【${row.goodsName}】`, '提示', {
          type: 'warning'
        });
        const res = await this.ajax.CivilAffairsGoodsManagementDelete(row.id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.handleRefreshTable();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleRefreshTable() {
      this.loading = true;
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          sidx: 'create_date',
          sord: 'desc'
        };

      Object.keys(searchForm).map(key => {
        if (searchForm[key] == null || searchForm[key] == undefined) {
          delete searchForm[key];
        }
      });

      let res = await this.ajax.CivilAffairsGoodsManagementPageList(searchForm);
      this.loading = false;
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.supplier-item-anagement {
  width: 100%;
  height: 100%;
  background: #fff;
  padding: 8px 8px 0 8px;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
      .operation-span {
        color: $primary-blue;
        margin-right: 8px;
        cursor: pointer;
      }
      .red {
        color: red !important;
      }
      .primary {
        color: $primary-blue;
      }
    }
  }
}
</style>
