export const urgencyLevel = {
  1: '普通',
  2: '紧急',
  3: '非常紧急'
};

export const overdue = {
  0: '未超期',
  1: '即将超期',
  2: '已超期'
};

export const progress = {
  0: '待登记',
  1: '待指派',
  2: '办理中',
  3: '待验收',
  4: '待批示',
  5: '已完结',
  6: '已撤销',
  7: '已终止'
};

export const urgent = {
  0: '否',
  1: '是'
};

export const registerStatus = [
  '待登记',
  '待指派',
  '办理中',
  '待验收',
  '待批示',
  '已完结',
  '已撤销',
  '已终止'
];

export const logStatus = [
  '登记',
  '指派',
  '办理',
  '验收',
  '批示',
  '催办',
  '延期',
  '撤销',
  '终止',
  '转办',
  '抄送',
  '进度反馈',
  '验收驳回',
  '批示驳回'
];

export const logStatusRemarkName = [
  { type: '2', name: '指派说明' },
  { type: '3', name: '办理说明' },
  { type: '4', name: '验收意见' },
  { type: '5', name: '批示意见' },
  { type: '6', name: '催办说明' },
  { type: '7', name: '延期说明' },
  { type: '8', name: '撤销原因' },
  { type: '9', name: '终止原因' },
  { type: '10', name: '转办原因' },
  { type: '12', name: '反馈说明' },
  { type: '13', name: '验收意见' },
  { type: '14', name: '批示意见' }
];

/**@desc 紧急程度 */
export const urgencyLevelList = Object.keys(urgencyLevel).map(key => {
  return {
    label: urgencyLevel[key],
    value: key,
    element: 'ts-option'
  };
});

/**@desc 是否超期 */
export const overdueList = Object.keys(overdue).map(key => {
  return {
    label: overdue[key],
    value: key,
    element: 'ts-option'
  };
});

/**@desc 当前进度 */
export const progressList = Object.keys(progress).map(key => {
  return {
    label: progress[key],
    value: key,
    element: 'ts-option'
  };
});

/**@desc 是否催办 */
export const urgentList = Object.keys(urgent).map(key => {
  return {
    label: urgent[key],
    value: key,
    element: 'ts-option'
  };
});

/**@desc 还原驼峰命名 */
export const camelToKebab = function(string) {
  return string.replace(/([a-z])([A-Z])/g, '$1_$2').toLowerCase();
};
