import { primaryBlue } from '@/assets/css/var.scss';
export default {
  data() {
    return {
      loading: false,
      searchForm: {},
      searchList: [
        {
          label: '合同类别',
          value: 'modelType',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择'
          },
          childNodeList: []
        },
        {
          label: '合同编号',
          value: 'no',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '合同名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '合同签订日期',
          value: 'completeDate',
          element: 'base-date-range-picker'
        },
        {
          label: '采购订单号',
          value: 'purchaseOrderFlowNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '采购项目名称',
          value: 'purchaseProjectName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '采购方式',
          value: 'purchaseType',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择'
          },
          childNodeList: []
        },
        {
          label: '资金来源',
          value: 'fundSource',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择'
          },
          childNodeList: []
        },
        {
          label: '医院名称',
          value: 'orgName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          }
        },
        {
          label: '归属科室',
          value: 'dept'
        },
        {
          label: '中标单位',
          value: 'bidWinner',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        },
        {
          label: '统一社会信用代码',
          value: 'bidWinnerNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入'
          },
          event: {
            change: e => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70
        },
        {
          label: '合同状态',
          prop: 'statusShow',
          align: 'center',
          width: 90,
          render: (h, { row }) => {
            let statusDic = {
              '3': '#DE813C',
              '4': 'red',
              '6': '#EA686A'
            };
            return h(
              'span',
              {
                style: {
                  color: statusDic[row?.status] || ''
                }
              },
              row?.statusShow
            );
          }
        },
        {
          label: '合同编号',
          prop: 'no',
          align: 'center',
          width: 150,
          render: (h, { row }) => {
            return h(
              'span',
              {
                class: row?.status !== '0' ? 'color-primary' : '',
                on: {
                  click: () => this.handleView(row)
                }
              },
              row?.no
            );
          }
        },
        {
          label: '合同名称',
          prop: 'name',
          align: 'center',
          minWidth: 140
        },
        {
          label: '合同类别',
          prop: 'modelTypeShow',
          align: 'center',
          width: 120
        },
        {
          label: '采购订单号',
          prop: 'purchaseOrderFlowNo',
          align: 'center',
          width: 150
          // render: (h, { row }) => {
          //   return h(
          //     'span',
          //     {
          //       class: 'color-primary',
          //       on: {
          //         click: () => {}
          //       }
          //     },
          //     row?.purchaseOrderFlowNo
          //   );
          // }
        },
        {
          label: '中标单位',
          prop: 'bidWinner',
          align: 'center',
          minWidth: 140
        },
        {
          label: '合同签订日期',
          prop: 'signDate',
          align: 'center',
          width: 110
        },
        {
          label: '合同总金额(元)',
          prop: 'amount',
          align: 'right',
          width: 120,
          render: (h, { row }) => {
            return h('span', Number(row.amount).toLocaleString('en-US'));
          }
        },
        {
          label: '采购项目名称',
          prop: 'purchaseProjectName',
          align: 'center',
          minWidth: 140
        },
        {
          label: '采购方式',
          prop: 'purchaseTypeShow',
          align: 'center',
          width: 100
        },
        {
          label: '资金来源',
          prop: 'fundSourceShow',
          align: 'center',
          width: 100
        },
        {
          label: '是否含税',
          prop: 'isTaxShow',
          align: 'center',
          width: 70
        },
        {
          label: '货币类型',
          prop: 'currencyTypeShow',
          align: 'center',
          width: 80
        },
        {
          label: '医院名称',
          prop: 'orgName',
          align: 'center',
          width: 140
        },
        {
          label: '归属科室',
          prop: 'deptName',
          align: 'center',
          minWidth: 120
        },
        {
          label: '科室负责人',
          prop: 'deptLeader',
          align: 'center',
          width: 110
        },
        {
          label: '统一社会信用代码',
          prop: 'bidWinnerNo',
          align: 'center',
          width: 140
        },
        {
          label: '中标单位联系人',
          prop: 'bidWinnerContactor',
          align: 'center',
          width: 130
        },
        {
          label: '中标单位联系电话',
          prop: 'bidWinnerMobile',
          align: 'center',
          width: 140
        },
        {
          label: '登记人',
          prop: 'createUserName',
          align: 'center',
          width: 90
        },
        {
          label: '操作',
          align: 'center',
          width: 180,
          prop: 'actions',
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            const actionMap = {
              '0': [
                {
                  label: '继续登记',
                  event: () => this.handleUpdateInfo(row.id, 'edit', '编辑合同')
                },
                {
                  label: '删除',
                  className: 'actionDel',
                  event: this.handleDelete
                }
              ],
              '1': [
                {
                  label: '开始履约',
                  event: () => this.handleChangeStatus(row.id, 'start')
                },
                {
                  label: '编辑',
                  event: () => this.handleUpdateInfo(row.id, 'edit', '编辑合同')
                },
                {
                  label: '删除',
                  className: 'actionDel',
                  event: this.handleDelete
                }
              ],
              '2': [
                {
                  label: '信息更新',
                  event: () =>
                    this.handleUpdateInfo(row.id, 'update', '合同信息更新')
                },
                {
                  label: '完成',
                  event: () => this.handleChangeStatus(row.id, 'end')
                },
                {
                  label: '中止',
                  className: 'actionDel',
                  event: () => this.handleStop(row.id, 'stop1')
                },
                {
                  label: '终止',
                  className: 'actionDel',
                  event: () => this.handleStop(row.id, 'stop2')
                }
              ],
              '3': [
                {
                  label: '继续履约',
                  event: () => this.handleChangeStatus(row.id, 'start')
                }
              ],
              '6': [
                {
                  label: '完成',
                  event: () => this.handleChangeStatus(row.id, 'end')
                }
              ]
            };

            const actions = actionMap[row?.status] || [];

            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions
                }
              },
              this.$slots.default
            );
          }
        }
      ]
    };
  }
};
