<template>
  <vxe-modal
    className="dialog-group-class"
    v-model="visible"
    title="群组分类管理"
    width="800"
    height="600"
    showFooter
    :before-hide-method="close"
  >
    <template #default>
      <div class="dialog-content">
        <ts-search-bar
          v-model="searchForm"
          :actions="[]"
          :formList="searchList"
          :elementCol="14"
          @search="search"
          :resetData="{}"
        >
          <template slot="right">
            <ts-button
              class="shallowButton"
              type="primary"
              @click="handleAddClass"
            >
              新增分类
            </ts-button>
          </template>
        </ts-search-bar>

        <TsVxeTemplateTable
          class="table_group_class"
          id="table_group_class"
          ref="table"
          :pageSizes="[100, 200, 500, 1000, 2000]"
          :defaultPageSize="100"
          :columns="columns"
          @refresh="handleRefreshTable"
          :default-sort="{ sidx: 'create_date', sord: 'desc' }"
        />
      </div>

      <dialog-add-group-class ref="DialogAddGroupClass" @ok="search" />
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import DialogAddGroupClass from './dialog-add-group-class.vue';
import Sortable from 'sortablejs';

export default {
  name: 'DialogGroupClass',
  components: {
    DialogAddGroupClass
  },
  data() {
    return {
      visible: false,
      searchForm: {},
      searchList: [
        {
          label: '分类名称',
          value: 'className',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入分类名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],

      sortableInstance: null,
      columns: [
        {
          type: 'seq',
          width: 60,
          align: 'center'
        },
        {
          label: '分类名称',
          prop: 'className',
          width: 150,
          align: 'center'
        },
        {
          label: '分类说明',
          prop: 'classDescription',
          align: 'center',
          minWidth: 200
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 100,
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '编辑',
                event: this.handleEditClass
              },
              {
                label: '删除',
                event: this.handleDeleteClass,
                className: 'actionDel'
              }
            ];

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  methods: {
    async open() {
      this.visible = true;
      this.$nextTick(() => {
        this.search();
        this.handleSetSort();
      });
    },

    handleSetSort() {
      const _this = this;

      if (this.sortableInstance) {
        this.sortableInstance.destroy();
        this.sortableInstance = null;
      }

      const tbody = document.querySelector('.table_group_class tbody');
      if (!tbody) return;
      this.sortableInstance = Sortable.create(tbody, {
        async onEnd({ newIndex, oldIndex }) {
          if (newIndex == oldIndex) return;

          let sortTable = deepClone(_this.tableData);
          const currRow = sortTable.splice(oldIndex, 1)[0];
          sortTable.splice(newIndex, 0, currRow);

          sortTable = sortTable.map((m, i) => {
            return {
              id: m.id,
              sortNo: String(i + 1)
            };
          });

          const res = await _this.ajax.updateGroupClassSort(sortTable);
          if (res.success) {
            _this.$refs.table.refresh({
              rows: []
            });

            await _this.search();
          } else {
            this.$message.error(res.message || '获取数据失败!');
          }
        }
      });
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    async handleRefreshTable() {
      try {
        this.tableData = [];
        let pageNo = this.$refs.table.pageNo,
          pageSize = this.$refs.table.pageSize,
          searchForm = {
            ...this.searchForm,
            pageNo,
            pageSize,
            classType: 0,
            sidx: this.$refs.table.sidx,
            sord: this.$refs.table.sord
          };

        Object.keys(searchForm).map(key => {
          if (
            searchForm[key] === null ||
            searchForm[key] === undefined ||
            searchForm[key] === ''
          ) {
            delete searchForm[key];
          }
        });

        const res = await this.ajax.getGroupClassList(searchForm);
        if (res.success == false) {
          this.$newMessage('error', res.message || '列表数据获取失败');
          return;
        }

        let rows = res.rows.map((item, i) => {
          let index = (pageNo - 1) * pageSize + i + 1;
          return {
            index,
            ...item
          };
        });

        this.tableData = deepClone(rows);
        this.$refs.table.refresh({
          ...res,
          rows
        });
      } catch (error) {
        this.$newMessage('error', error.message || '获取数据失败');
      }
    },

    handleAddClass() {
      this.$refs.DialogAddGroupClass.open({
        type: 'add',
        data: {}
      });
    },

    handleEditClass(data) {
      this.$refs.DialogAddGroupClass.open({
        type: 'edit',
        data
      });
    },

    async handleDeleteClass(row) {
      try {
        await this.$newConfirm(
          `确定【<span style="color: red">删除</span>】群组分类【${row.className}】吗？`
        );

        const res = await this.ajax.deleteGroupClass({
          id: row.id
        });

        if (res.success) {
          this.$newMessage('success', '【删除】成功');
          this.search();
        } else {
          this.$newMessage('error', res.message || '【删除】失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$newMessage('error', error.message || '【删除】失败');
        }
      }
    },

    close() {
      if (this.sortableInstance) {
        this.sortableInstance.destroy();
        this.sortableInstance = null;
      }
      this.$emit('ok');
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-group-class {
  .dialog-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .search-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e7ebf0;
    }

    .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);
    }
  }
}
</style>
