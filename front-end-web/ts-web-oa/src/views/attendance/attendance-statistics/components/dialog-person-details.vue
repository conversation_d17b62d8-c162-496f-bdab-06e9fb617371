<template>
  <ts-dialog
    title="明细"
    :visible.sync="visible"
    fullscreen
    :appendToBody="true"
  >
    <div class="content">
      <ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        :resetData="resetData"
        @search="search"
      >
        <template slot="right">
          <ts-button @click="handleExport">
            导出
          </ts-button>
        </template>
      </ts-search-bar>
      <base-table
        class="form-table"
        ref="table"
        border
        stripe
        :hasPage="false"
        v-loading="loading"
        :columns="columns"
        @refresh="handleRefreshTable"
      />
    </div>
    <template slot="footer">
      <ts-button @click="close">关闭</ts-button>
    </template>
  </ts-dialog>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      loading: false,
      columns: [],

      searchForm: {},
      resetData: {},
      searchList: [
        {
          label: '开始月份',
          value: 'searchStartMonth',
          element: 'ts-month-picker',
          elementProp: {
            valueFormat: 'YYYY-MM',
            allowClear: false
          }
        },
        {
          label: '结束月份',
          value: 'searchEndMonth',
          element: 'ts-month-picker',
          elementProp: {
            valueFormat: 'YYYY-MM',
            allowClear: false
          }
        }
      ],
      startMonth: '',
      searchOrgId: '',
      endMonth: ''
    };
  },
  methods: {
    open(data = {}) {
      this.searchOrgId = data.orgId;
      this.searchOrgName = data.orgName;
      let nowMonth = this.$dayjs(data.attendanceMonth).format('YYYY-MM');
      this.$set(this.resetData, 'searchStartMonth', nowMonth);
      this.$set(this.resetData, 'searchEndMonth', nowMonth);
      this.$set(this.searchForm, 'searchStartMonth', nowMonth);
      this.$set(this.searchForm, 'searchEndMonth', nowMonth);

      this.handleGetHeaderTitle();
      this.handleRefreshTable();
      this.visible = true;
    },
    search() {
      if (this.handleCheckTime()) {
        return;
      }
      this.$refs.table.pageNo = 1;
      this.handleGetHeaderTitle();
      this.handleRefreshTable();
    },
    // 获取表头
    async handleGetHeaderTitle() {
      this.columns = [
        {
          type: 'selection',
          align: 'center',
          width: 50
        }
      ];
      let res = await this.ajax.getHeaderTitle(this.searchForm);

      if (res.success && res.statusCode === 200) {
        Object.values(res.object).forEach((item, index) => {
          const [label, prop] = item.split('@');
          let minWidth = 50;
          if (isNaN(Number(label))) {
            minWidth = 80;
          }
          let columnsItem = {
            label,
            prop,
            minWidth,
            fixed: index < 3,
            align: 'center'
          };
          if (label === '姓名') {
            columnsItem.formatter = row => {
              return (
                <span
                  class="details-span"
                  onClick={() => {
                    this.handleDetails(row);
                  }}>
                  {row[prop]}
                </span>
              );
            };
          }
          this.columns.push(columnsItem);
        });
      } else {
        this.$message.error(res.message || '获取列表表头失败!');
        return;
      }
    },
    async handleRefreshTable() {
      this.loading = true;
      let pageNo = 1;
      let pageSize = 99999;
      let searchForm = {
        ...this.searchForm,
        pageNo,
        pageSize,
        searchOrgId: this.searchOrgId
      };
      let res = await this.ajax.attendanceDetailsAttendanceReport(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
      this.loading = false;
    },
    handleCheckTime() {
      if (
        this.$dayjs(this.searchForm.searchStartMonth).isAfter(
          this.$dayjs(this.searchForm.searchEndMonth)
        )
      ) {
        this.$message.warning('开始时间不能小于结束时间');
        return true;
      }
    },
    handleExport() {
      if (this.handleCheckTime()) {
        return;
      }
      let pageNo = this.$refs.table.pageNo;
      let pageSize = this.$refs.table.pageSize;
      let data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          searchOrgId: this.searchOrgId,
          searchOrgName: this.searchOrgName,
          sidx: 'create_date',
          sord: 'desc'
        },
        queryList = [],
        aDom = document.createElement('a');

      Object.keys(data).map(key => {
        queryList.push(key + '=' + data[key]);
      });
      aDom.href =
        '/ts-oa/attendance/attendanceDetails/exportReport?' +
        queryList.join('&');
      aDom.click();
    },
    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ .el-dialog__footer {
  width: calc(100vw - 80px) !important;
}

/deep/ .el-dialog__body {
  width: calc(100vw - 80px) !important;
  height: calc(100vh - 111px);
  display: flex;
  flex-direction: column;

  .content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    ::v-deep .form-table {
      flex: 1;
      overflow: hidden;
      transform: scale(1);

      .color-primary {
        color: $primary-blue;
      }

      .details-span {
        color: $primary-blue;
        cursor: pointer;
      }
    }
  }
}
</style>
