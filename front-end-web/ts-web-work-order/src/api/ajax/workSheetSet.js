import { $get, $postJson } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  //获取院区列表
  getHospitalYardList() {
    return $get(`${service.tsWorksheet()}/hospitalDistrictList`);
  },
  //获取工单系统设置信息
  getSystemConfig() {
    return $get(`${service.tsWorksheet()}/sysConfig`);
  },
  //编辑保存系统设置
  saveSystemConfig(data) {
    return $postJson(
      `${service.tsWorksheet()}/sysConfig`,
      JSON.stringify(data)
    );
  },
  //设置院区启用禁用
  changeHospitalYardStatus(id, status) {
    return $postJson(
      `${service.tsWorksheet()}/hospitalDistrict/${id}/${status}`
    );
  },
  //新增、保存院区
  saveHospitalYardItem(data) {
    return $postJson(
      `${service.tsWorksheet()}/hospitalDistrict`,
      JSON.stringify(data)
    );
  }
};
