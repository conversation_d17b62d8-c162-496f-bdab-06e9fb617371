import { $api } from '@/api/ajax';
import { service } from '@/api/config';

// 获取登陆用户信息
function loginPersonInfo() {
  return $api({
    url: `${service.tsWorksheet()}/workSheetPeopple/loginPersonInfo`,
    method: 'get'
  });
}

// 服务台人员页面-统计指标、
function getServiceDeskStaffStatisticalIndicators(fkUserId) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getServiceDeskStaffStatisticalIndicators?fkDeptId=${
      fkUserId ? fkUserId : ''
    }`,
    method: 'get'
  });
}

//理科室列表
function meauList(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/meauList`,
    method: 'get',
    params: data
  });
}

//工单服务台人员首页处理科室接口
function meauPermissionsList() {
  return $api({
    url: `${service.tsWorksheet()}/omMeau/meauPermissionsList`,
    method: 'get'
  });
}

// 异常工单各种状态统计
function getAbnormalWorkSheetStatisCounts(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getAbnormalWorkSheetStatisCounts`,
    method: 'get',
    params
  });
}

// 超期工单数据
function getExceedTimeWorkSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getExceedTimeWorkSheets`,
    method: 'get',
    params
  });
}

// 催办工单
function getHastenWorkSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getHastenWorkSheets`,
    method: 'get',
    params
  });
}

// 今日终止/暂停工单
function getSuspendTerminateSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getSuspendTerminateSheets`,
    method: 'get',
    params
  });
}

// 差评工单数据
function getBadReviewSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getBadReviewSheets`,
    method: 'get',
    params
  });
}

// 打回工单数据
function getBackSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getBackSheets`,
    method: 'get',
    params
  });
}

// 等待协助工单数据
function getAssistWorkOrder(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getAssistWorkOrder`,
    method: 'get',
    params
  });
}

// 工单详情
function workSheetInfo(workNumber) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/workSheetInfo/${workNumber}`,
    method: 'get'
  });
}

// 根据Id获取科室人员
function getEmployeePageList(data) {
  return $api({
    url: `${service.tsBasics()}/employee/getEmployeePageList`,
    method: 'post',
    data
  });
}

// 工单综合评分
function getComprehensiveScoreOfWorkOrder(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getComprehensiveScoreOfWorkOrder`,
    method: 'get',
    params
  });
}

// 处理人、报修人各状态工单数量
function getCountGroupByWorkStatus(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getCountGroupByWorkStatus`,
    method: 'get',
    params
  });
}

// 申请人首页-我的知识点
function getMyKnowledgeBaseCount(id) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getMyKnowledgeBaseCount/${id}`,
    method: 'get'
  });
}

// 申请人首页-我的知识点
function workSheetList(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/workSheetList`,
    method: 'post',
    params
  });
}

// 工单操作 暂停
function workSheetHasStopped(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/workSheetHasStopped`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;'
    },
    data
  });
}

// 工单操作 终止
function workSheetTerminated(data) {
  return $api({
    url: `${service.tsWorksheet()}/workTask/workSheetTerminated`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;'
    },
    data
  });
}

// 获取催办信息
function getHastenInfo(workNumber) {
  return $api({
    url: `${service.tsWorksheet()}/workSheetHasten/getHastenInfo/${workNumber}`,
    method: 'get'
  });
}

// 工单催办
function workSheetHasten(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheetHasten/save`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json;'
    },
    data
  });
}

//工单评价
function workOrderEvaluate(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/workSheetToEvaluate`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    data
  });
}

//工单验收
function workOrderAccept(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/workSheetAcceptance`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    data
  });
}

//获取开启工单信息
function openNodeInfo(data) {
  return $api({
    url: `${service.tsWorksheet()}/workTask/openNodeInfo`,
    method: 'GET',
    data: { pkWsTaskId: data }
  });
}

//开启工单
function openWorkOrder(data) {
  return $api({
    url: `${service.tsWorksheet()}/workSheet/workSheetHadRecovered`,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    data
  });
}

//今日暂停工单
function getTodaySuspendedSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getTodaySuspendedSheets`,
    method: 'get',
    params
  });
}

//今日终止工单
function getTodayTerminationSheets(params) {
  return $api({
    url: `${service.tsWorksheet()}/statisticsData/getTodayTerminationSheets`,
    method: 'get',
    params
  });
}
export default {
  loginPersonInfo,
  getServiceDeskStaffStatisticalIndicators,
  meauList,
  meauPermissionsList,
  getAbnormalWorkSheetStatisCounts,

  getExceedTimeWorkSheets,
  getHastenWorkSheets,
  getSuspendTerminateSheets,
  getBadReviewSheets,
  getBackSheets,
  getAssistWorkOrder,
  getTodaySuspendedSheets,
  getTodayTerminationSheets,

  workSheetInfo,
  getEmployeePageList,
  getComprehensiveScoreOfWorkOrder,
  getCountGroupByWorkStatus,
  getMyKnowledgeBaseCount,
  workSheetList,

  workSheetHasStopped,
  workSheetTerminated,
  getHastenInfo,
  workSheetHasten,

  workOrderEvaluate,
  workOrderAccept,
  openNodeInfo,
  openWorkOrder
};
