import { $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  //处理人搜索
  getOrderHandlePersonList(data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheetPeopple/getPeopleInfoList`,
      data,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      }
    });
  },

  //获取报修人数据
  getOrderReportPersonList(data) {
    return $api({
      url: `${service.tsBasics()}/employee/getEmployeePageList`,
      data,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      }
    });
  },

  //获取每个 tab 有多少数据正在处理
  getOrderStatusTabCount(data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheet/workSheetListBusCounts/` + data,
      method: 'get'
    });
  },

  //获取table数据
  getOrderTableDataList(workStatus, data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheet/workSheetList?workStatus=${workStatus}`,
      data,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      }
    });
  },

  /**@desc 派单 */
  handleDistribute(data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheet/workSheetDispatch`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 认领 */
  handleOrderClaim(data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheet/toClaim`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  },

  /**@desc 终止 */
  handleEndOrder(data) {
    return $api({
      url: `${service.tsWorksheet()}/workTask/workSheetTerminated`,
      method: 'post',
      data,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
};
