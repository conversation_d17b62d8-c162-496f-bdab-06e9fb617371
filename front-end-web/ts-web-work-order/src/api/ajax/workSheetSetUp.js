import { $api, $postJson } from '@/api/ajax';
import { service } from '@/api/config';

// 语音菜单分页列表
function selectOmMeauList(data) {
  return $api({
    url: `${service.tsWorksheet()}/omMeau/selectOmMeauList`,
    method: 'get',
    params: data
  });
}

// 保存修改语音菜单
function saveOrUpdate(data) {
  return $api({
    url: `${service.tsWorksheet()}/omMeau/saveOrUpdate`,
    method: 'post',
    headers: {
      'Content-Type': 'application/json; charset=utf-8'
    },
    data: JSON.stringify(data)
  });
}

// 删除语音菜单
function deleteOmMeau(ids) {
  return $api({
    url: `${service.tsWorksheet()}/omMeau/deleteOmMeau/${ids}`,
    method: 'post'
  });
}

//修改语音菜单排序
function changeVoiceSort(data) {
  return $postJson(
    `${service.tsWorksheet()}/omMeau/updateSort`,
    JSON.stringify(data)
  );
}

export default {
  selectOmMeauList,
  saveOrUpdate,
  deleteOmMeau,
  changeVoiceSort
};
