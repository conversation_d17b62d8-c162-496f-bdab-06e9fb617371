import { $get, $api } from '@/api/ajax';
import { service } from '@/api/config';

export default {
  /**@desc 基础服务 附件上传 */
  upload(businessId, data) {
    return $api({
      url: `${service.tsBasics()}/fileAttachment/upload?moduleName=workDesk&businessId=${businessId}`,
      data,
      method: 'post',
      headers: {
        'Content-Type': 'false',
        cache: 'false',
        processData: 'false'
      }
    });
  },
  /**@desc 通过businessId获取文件列表 */
  getFileAttachmentByBusinessId(businessId) {
    return $get(
      `${service.tsBasics()}/fileAttachment/getFileAttachmentByBusinessId?businessId=${businessId}`
    );
  },
  /**@desc 通过id删除文件 */
  deleteFileId(id) {
    return $get(
      `${service.tsBasics()}/fileAttachment/deleteFileId?fileid=${id}`
    );
  },
  // 获取科室树
  getTree() {
    return $api({
      url: `${service.tsBasics()}/organization/getTree`,
      method: 'post'
    });
  }
};
