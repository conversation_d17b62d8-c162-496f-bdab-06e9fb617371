import { $api } from '@/api/ajax';
import { service } from '@/api/config';
export default {
  /**@desc 获取故障类型数据  --编辑 */
  getFaultTypeTreeDataByStatus(deptId) {
    return $api({
      url: `${service.tsWorksheet()}/faultType/getFaultTypeAllList/1/${deptId}`
    });
  },

  /**@desc 处理人数据获取  --派单 */
  getHandlePersonDataByStatus(data) {
    return $api({
      url: `${service.tsWorksheet()}/workSheetPeopple/getNoPagePeopleInfoList`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  }
};
