import { Table } from '@trasen/trasen-element-ui/lib/index';

const newTable = {
  extends: Table,
  mounted() {
    this.layout.gutterWidth = this.tsGutterWidth; //设置默认table滚动条宽度
  },
  watch: {},
  props: {
    tsGutterWidth: {
      //table滚动条宽度
      type: Number,
      default: 10
    }
  },
  methods: {},
  beforeDestroy() {},
  activated() {},
  deactivated() {}
};

export default {
  install(Vue) {
    Vue.component('el-table', newTable);
  }
};
