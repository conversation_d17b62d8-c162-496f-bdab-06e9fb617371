import dayjs from 'dayjs';

/** @desc 事件类型  */
const affectScope = {
  1: '个人事件',
  2: '科室事件',
  3: '多科室事件',
  4: '全院事件'
};

/** @desc 计算 故障描述 表格展示 方法 */
function computedFaultDeion(h, row, opt, cell) {
  let nodeList = [];
  const isEmergency = row.faultEmergency < 3 ? true : false,
    isReback =
      (row.backUserName ? true : false) && row.workStatusName == '待派单',
    isNotHandled =
      (row.noPassCounts > 0 ? true : false) && row.workStatusName == '处理中';

  row.workSheetType == 5
    ? nodeList.push(
        h(
          'a',
          {
            class: 'come-form-tech',
            domProps: {
              target: '_blank',
              href: encodeURI(
                `/view-new/processView/see/index.html?workflowNo=${row.workflowNo}&businessId=${row.lbusinessId}&wfInstanceId=${row.workflowInstId}&currentStepNo=end&role=deal&type=see`
              )
            }
          },
          [
            h('img', {
              domProps: {
                src: require('@/assets/img/workSheet/reception/from_process.svg')
              },
              style: 'height: 22px; vertical-align: middle;'
            })
          ]
        )
      )
    : null;

  isEmergency
    ? nodeList.push(
        h(
          'span',
          {
            class:
              'text-label' +
              (row.faultEmergency == 2 ? ' urgent' : ' very-urgent'),
            domProps: {
              title: '影响范围：' + affectScope[row.faultAffectScope]
            }
          },
          row.faultEmergency == 2 ? '比较急' : '非常紧急'
        )
      )
    : null;

  isReback
    ? nodeList.push(
        h(
          'span',
          {
            class: 'text-label reback-label',
            domProps: {
              title: `退回${row.backRemark ? '，原因：' + row.backRemark : ''}`
            }
          },
          '退回'
        )
      )
    : null;
  isNotHandled
    ? nodeList.push(
        h('span', {
          class: 'text-label reback-label',
          domProps: {
            title: `提单人退回${row.noPassCounts}次`
          }
        })
      )
    : null;
  return h(
    'div',
    {
      class: 'work-sheet-deion-content'
    },
    [
      ...nodeList,
      h(
        'span',
        {
          class: 'work-sheet-deion',
          on: {
            click: () => this.handleShowWorkSheetDetail(row)
          }
        },
        cell || ''
      )
    ]
  );
}

/** @desc 计算 要求日期 表格展示 方法 */
function computedRequestDate(h, row, opt, cell) {
  if (!cell) {
    return '';
  }

  let days = dayjs().diff(dayjs(cell), 'day');
  return h(
    'span',
    {
      style:
        days >= -3 &&
        ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
          ? 'color: red;'
          : ''
    },
    cell
  );
}
export default {
  computedFaultDeion,
  computedRequestDate
};
