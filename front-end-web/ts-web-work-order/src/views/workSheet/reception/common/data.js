const statusBtn = {
  待派单: [
    { label: '派单', event: 'distribute', icon: 'oaicon oa-icon-tiaogang' },
    { label: '认领', event: 'claim', icon: 'fa fa-bookmark' },
    { label: '编辑', event: 'edit', icon: 'fa fa-pencil-square-o' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' },
    { label: '终止', event: 'end', icon: 'fa fa-ban' }
  ],
  待接单: [
    { label: '重派', event: 'resend', icon: 'oaicon oa-icon-lungang' },
    { label: '编辑', event: 'edit', icon: 'fa fa-pencil-square-o' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' },
    {
      label: '催办',
      event: 'urge',
      icon: 'oaicon oa-icon-send-message'
    },
    { label: '终止', event: 'end', icon: 'fa fa-ban' }
  ],
  处理中: [
    { label: '重派', event: 'resend', icon: 'oaicon oa-icon-lungang' },
    {
      label: '催办',
      event: 'urge',
      icon: 'oaicon oa-icon-send-message'
    },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' },
    { label: '暂停', event: 'pause', icon: 'fa fa-clock-o' },
    { label: '终止', event: 'end', icon: 'fa fa-ban' }
  ],
  待验收: [
    { label: '提交知识库', event: 'sendKnowledgeBase', icon: 'fa fa-leanpub' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' }
  ],
  待评价: [
    { label: '提交知识库', event: 'sendKnowledgeBase', icon: 'fa fa-leanpub' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' }
  ],
  已完成: [
    { label: '提交知识库', event: 'sendKnowledgeBase', icon: 'fa fa-leanpub' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' }
  ],
  已暂停: [
    { label: '开启', event: 'start', icon: 'fa fa-play-circle-o' },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' }
  ],
  已终止: [
    {
      label: '查看原因',
      event: 'watchReason',
      icon: 'oaicon oa-icon-lizhixinxichaxun'
    },
    { label: '复制', event: 'copy', icon: 'fa fa-copy' }
  ],
  未接听: [
    { label: '回拨', event: 'callBack', icon: 'fa fa-whatsapp' },
    {
      label: '标记无效来电',
      event: 'markUseless',
      icon: 'oaicon oa-icon-mark'
    }
  ],
  未建单: [
    { label: '建单', event: 'creatNew', icon: 'oaicon oa-icon-liuchengfaqi' },
    {
      label: '标记无效来电',
      event: 'markUseless',
      icon: 'oaicon oa-icon-mark'
    }
  ]
};

export default {
  statusBtn
};
export { statusBtn };
