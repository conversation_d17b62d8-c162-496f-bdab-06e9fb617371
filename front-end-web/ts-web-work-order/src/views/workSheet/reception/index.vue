<template>
  <div class="container flex-column">
    <div v-if="isWorkSheetAdmin" class="top-content flex">
      {{ today }}
      <div class="today-service-data flex">
        今日服务台:
        <div
          class="today-item flex"
          v-for="(item, index) of dayDataList"
          :key="index"
          :class="item.class"
          @click="
            () => {
              item.handleClick && item.handleClick();
            }
          "
        >
          {{ item.label }}
          <div class="today-count">{{ item.value }}</div>
        </div>
      </div>
    </div>
    <!-- 表格数据 -->
    <div class="bottom-content">
      <ts-tabs
        v-model="active"
        type="card"
        @tab-click="refresh"
        class="flex-column"
      >
        <ts-tab-pane label="全部工单" name="allOrderTableTree">
          <AllOrderTable
            ref="allOrderTableTree"
            v-on="tableEvent"
          ></AllOrderTable>
        </ts-tab-pane>
        <ts-tab-pane :label="`待派单(${eachStatusCount[15] || 0})`" name="1">
          待派单
        </ts-tab-pane>
        <ts-tab-pane :label="`处理中(${eachStatusCount[10] || 0})`" name="2">
          处理中
        </ts-tab-pane>
        <ts-tab-pane :label="`未建单(${eachStatusCount[12] || 0})`" name="3">
          未建单
        </ts-tab-pane>
        <ts-tab-pane label="已完成" name="4">已完成</ts-tab-pane>
        <ts-tab-pane name="3">
          <span slot="label" class="tab-dot">
            通话记录
            <div class="dot" :class="eachStatusCount[13] ? '' : 'none'">
              <div>
                {{ eachStatusCount[13] || 0 }}
              </div>
            </div>
          </span>
        </ts-tab-pane>
      </ts-tabs>
    </div>

    <!-- 详情信息 -->
    <el-dialog :visible.sync="showDetail" :title="title" width="900px">
      <DetailBorde ref="detailBorde" :messageInfo="orderDetail" />
      <DistributeAction
        ref="distributeAction"
        v-if="eventType == 'distribute' || eventType == 'resend'"
        :data="orderDetail.wsWsSheetInfoOutVo"
      />
      <PauseOrEndAction
        ref="pauseOrEndAction"
        v-if="eventType == 'end' || eventType == 'pause'"
      />
      <template slot="footer">
        <template v-if="title == '工单详情'">
          <ts-button @click="handleCloseDetail">关闭</ts-button>
        </template>

        <template v-else>
          <div style="text-align: center;">
            <ts-button type="primary" @click="handleSubmit">确认</ts-button>
            <ts-button @click="handleCloseDetail">取消</ts-button>
          </div>
        </template>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import AllOrderTable from './components/all-order-tab.vue';
import DetailBorde from '@/views/workSheet/comment/components/work-sheet-status-list/detail-borde.vue';
import DistributeAction from './components/distribute-action.vue'; //派单/重派
import PauseOrEndAction from './components/pause-or-end-action.vue'; //暂停/终止

export default {
  components: {
    AllOrderTable,
    DetailBorde,
    DistributeAction,
    PauseOrEndAction
  },
  data() {
    return {
      active: 'allOrderTableTree',
      //今日服务台数据
      dayDataList: [
        {
          label: '已接',
          value: 0
        },
        {
          label: '未接',
          value: 0,
          class: 'active-item',
          handleClick: () => {}
        },
        {
          label: '呼出',
          value: 0
        },
        {
          label: '电话解决',
          value: 0
        },
        {
          label: '提单',
          value: 0
        },
        {
          label: '无效来电',
          value: 0
        }
      ],
      //工单各状态数量数据
      eachStatusCount: {},

      eventType: '',
      actionRow: {},
      tableEvent: {
        'show-detail': this.handleShowDetail, //显示详情
        action: this.handleTableAction
      },

      showDetail: false,
      title: '工单详情',
      orderDetail: {}
    };
  },
  computed: {
    isWorkSheetAdmin: function() {
      return this.$store.state.common.userMessage.webSocket;
    },
    today: function() {
      return this.$dayjs().format('YYYY年MM月DD日');
    }
  },
  methods: {
    refresh() {
      this.ajax.getOrderStatusTabCount(5).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '工单状态数据获取失败');
          return;
        }
        this.eachStatusCount = res.object || {};
      });

      this.$refs[this.active] &&
        this.$refs[this.active].refresh &&
        this.$refs[this.active].refresh();
    },
    getData() {},
    handleCloseDetail() {
      this.orderDetail = {};
      this.actionRow = {};
      this.showDetail = false;
      this.$refs.detailBorde.messageTabs = '1';
    },
    /**@desc 获取详情数据 */
    async getDetailData(row) {
      const detailRes = await this.ajax.workSheetInfo(row.workNumber);
      if (detailRes.success == false) {
        this.$message.error(
          detailRes.message || '详细数据加载失败，请稍后再试'
        );
        return;
      }
      this.orderDetail = detailRes.object;
    },
    /**@desc 处理 数据操作 */
    async handleTableAction(event, title, row) {
      this.eventType = event;
      this.title = title;
      this.actionRow = row;
      let defaultShow = true;

      switch (event) {
        case 'distribute':
        case 'resend':
        case 'end':
          await this.getDetailData(row);
          break;
        case 'claim':
          defaultShow = false;
          this.handleClaim(row);
          break;
      }
      this.showDetail = defaultShow;
    },
    handleSubmit() {
      switch (this.eventType) {
        // 派单/重派
        case 'resend':
        case 'distribute':
          this.handleDistribute();
          break;
        // 终止
        case 'end':
          this.handleEndOrder();
          break;
      }
    },
    /**@desc 统一发送请求 */
    async sendRequest(requestName, data, successMessage, errorMessage) {
      let res = await this.ajax[requestName](data);
      if (res.success == false) {
        this.$message.error(res.message || errorMessage);
        return;
      }
      this.handleCloseDetail();
      this.refresh();
      this.$message.success(successMessage);
    },
    /**@desc 派单/重派 */
    async handleDistribute() {
      let validateRes = await this.$refs.distributeAction.validate();
      if (!validateRes) {
        return;
      }
      let data = this.$refs.distributeAction.form;
      if (this.eventType == 'resend') {
        if (this.actionRow.fkUserId == data.fkUserId) {
          this.$message.warning('处理人无变化，无需重派');
          return;
        }
        data.fkFormerUserId = this.actionRow.fkUserId;
      }

      this.sendRequest('handleDistribute', data, '操作成功', '操作失败');
    },
    /**@desc 认领 */
    async handleClaim(row) {
      let confirm = await this.$confirm('确定认领该工单吗？', '提示').catch(
        res => res
      );
      if (confirm == 'cacel') {
        this.actionRow = {};
        return;
      }
      let data = {
        pkWsTaskId: row.pkWsTaskId,
        fkUserId: this.$store.state.common.userInfo.employeeId
      };
      this.sendRequest('handleOrderClaim', data, '认领成功', '认领失败');
    },
    /**@desc 终止 */
    async handleEndOrder() {
      let validate = await this.$refs.pauseOrEndAction.validate();
      if (!validate) {
        return;
      }
      let data = this.$refs.pauseOrEndAction.form;
      data.pkWsTaskId = this.actionRow.pkWsTaskId;
      data.yesOrNo = '1';

      this.sendRequest('handleEndOrder', data, '操作成功', '操作失败');
    },
    async handleShowDetail(row) {
      await this.getDetailData(row);
      this.title = '工单详情';
      this.showDetail = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  height: calc(100% - #{$theme-interval});
}
.bottom-content,
.top-content {
  padding: $box-padding-16;
  border-radius: $box-border-radius-4;
  background: #fff;
}
.top-content {
  margin-bottom: $theme-interval;
  color: #333333;
}
.today-service-data {
  color: #666;
  margin: 0 24px;
}
.today-item {
  color: #333;
  margin-right: 24px;
  &:first-child {
    margin-left: 8px;
  }
  .active-item {
    cursor: pointer;
  }
  .today-count {
    width: 32px;
    height: 20px;
    margin-left: 8px;
    background: #ffeaea;
    border-radius: 10px;
    text-align: center;
    color: #ff5d5d;
  }
  &:nth-child(n + 4) .today-count {
    background: #e8eef8;
    border-radius: 10px;
    text-align: center;
    color: #5260ff;
  }
}
.bottom-content {
  flex: 1;
  overflow: hidden;
}
.ts-tabs {
  height: 100%;
  ::v-deep .el-tabs__content {
    flex: 1;
  }
  ::v-deep .el-tab-pane {
    height: 100%;
  }
}

.tab-dot .dot {
  &.none {
    display: none;
  }
  background: #f45555;
  border-radius: 10px;
  position: absolute;
  top: 0px;
  right: 0px;
  line-height: 14px;
  width: 20px;
  height: 14px;
  justify-content: center;
  align-items: center;
  div {
    color: #fff;
    font-size: 12px;
    line-height: 14px;
    transform: scale(0.8);
  }
}
::v-deep .tab-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
::v-deep .el-table .cell.el-tooltip {
  min-width: unset !important;
}

/deep/ .order-action-content {
  padding: $theme-interval 0;
  margin-top: $theme-interval;
  border-top: 1px solid #e8eef8;
  .order-action-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 22px;
    margin-bottom: $theme-interval;
    &::before {
      content: '';
      height: 18px;
      width: 4px;
      background-color: $theme-color;
      margin-right: 4px;
    }
  }
}
</style>
