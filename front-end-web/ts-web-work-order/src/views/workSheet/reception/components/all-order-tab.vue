<template>
  <div class="tab-content">
    <ts-search-bar
      ref="searchBar"
      v-model="form"
      :formList="searchList"
      :elementCol="14"
      :actions="actions"
      @search="refreshTable"
    >
    </ts-search-bar>

    <base-table
      ref="table"
      border
      stripe
      :columns="columns"
      @refresh="refreshTable"
      :default-sort="{ order: 'descending', prop: 'createTime' }"
      @sort-change="handleSortChange"
    >
      <template slot="action">
        <i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>
      </template>
    </base-table>
  </div>
</template>

<script>
import api from '@/api/ajax/workSheetSetUp';
import common from '../common/common.js';
// import { computedFaultDeion } from '../common/common.js';
import BaseActionCell from '@/components/base-action-cell/index.vue';
import { statusBtn } from '../common/data';

export default {
  data() {
    const createElement = this.$createElement;
    return {
      form: {},
      searchList: [
        {
          label: '工单编号',
          value: 'workNumber',
          element: 'el-input',
          type: 'number',
          elementProp: {
            placeholder: '请输入工单编号'
          },
          event: {
            change: this.handleChange
          }
        },
        {
          label: '故障描述',
          value: 'faultDeion',
          element: 'el-input',
          elementProp: {
            placeholder: '请输入故障描述'
          }
        },
        {
          label: '报修时间',
          value: 'repaireTimeList',
          element: 'el-date-picker',
          elementProp: {
            type: 'daterange',
            rangeSeparator: '-',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期'
          }
        },
        {
          label: '状态',
          value: 'workStatusValue',
          element: 'el-select',
          childNodeList: [
            {
              element: 'el-option',
              label: '全部',
              value: ''
            },
            {
              element: 'el-option',
              label: '待派单',
              value: '1'
            },
            {
              element: 'el-option',
              label: '待接单',
              value: '2'
            },
            {
              element: 'el-option',
              label: '处理中',
              value: '3'
            },
            {
              element: 'el-option',
              label: '待验收',
              value: '4'
            },
            {
              element: 'el-option',
              label: '待评价',
              value: '5'
            },
            {
              element: 'el-option',
              label: '已完成',
              value: '6'
            },
            {
              element: 'el-option',
              label: '已暂停',
              value: '7'
            },
            {
              element: 'el-option',
              label: '已终止',
              value: '8'
            }
          ]
        },
        {
          label: '报修科室',
          value: 'repairManId',
          element: 'input-tree',
          elementProp: {
            treeData: []
          }
        },
        {
          label: '处理人',
          value: 'userId',
          element: 'InputSelect',
          elementProp: {
            placeholder: '请选择处理人',
            labelName: '${prop}-${deptName}',
            valueName: 'userId'
          },
          event: {
            load: this.handleLoadHandlePerson
          }
        },
        {
          label: '报修人',
          value: 'repairManId',
          element: 'InputSelect',
          elementProp: {
            placeholder: '请选择报修人'
          },
          event: {
            load: this.handleLoadRepairMan
          }
        },
        {
          label: '要求日期',
          value: 'timeList',
          element: 'el-date-picker',
          elementProp: {
            type: 'daterange',
            rangeSeparator: '-',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期'
          }
        }
      ],
      actions: [
        {
          label: '创建工单',
          prop: {
            type: 'primary'
          },
          click: this.hanldeOpenCreateOrder
        },
        {
          label: '导出',
          click: this.handleExport
        }
      ],

      columns: [
        {
          type: 'index',
          align: 'center',
          width: 35,
          index: index => {
            let pageNo = (this.$refs.table && this.$refs.table.pageNo) || 1,
              pageSize = (this.$refs.table && this.$refs.table.pageSize) || 100;

            return (pageNo - 1) * pageSize + index + 1;
          }
        },
        {
          label: '工单号',
          prop: 'workNumber',
          align: 'center',
          minWidth: 100
        },
        {
          label: '报修科室',
          prop: 'repairManDeptName',
          minWidth: 100
        },
        {
          label: '报修人',
          prop: 'repairManName',
          minWidth: 65
        },
        {
          label: '报修时间',
          prop: 'createTime',
          minWidth: 100,
          sortable: true,
          sortKey: 'a.create_time',
          align: 'center'
        },
        {
          label: '故障描述',
          prop: 'faultDeion',
          minWidth: 290,
          formatter: common.computedFaultDeion.bind(this, createElement)
        },
        {
          label: '建单人',
          prop: 'createByName',
          minWidth: 65
        },
        {
          label: '要求日期',
          prop: 'requiredCompletionTime',
          minWidth: 100,
          sortable: true,
          align: 'center',
          sortKey: 'a.required_completion_time',
          formatter: common.computedRequestDate.bind(this, createElement)
        },
        {
          label: '处理人',
          prop: 'fkUserName',
          minWidth: 65,
          formatter: function(row, opt, cell) {
            return (
              <span title={row.assistName ? '协助人：' + row.assistName : ''}>
                {cell}
              </span>
            );
          }
        },
        {
          label: '状态',
          prop: 'workStatusName',
          minWidth: 70,
          align: 'center'
        },
        {
          label: '催办次数',
          prop: 'hatenCount',
          minWidth: 100,
          align: 'center',
          sortable: true,
          sortKey: 'haten_count',
          formatter: (row, opt, cell) => {
            return (
              <div
                class="work-sheet-deion-content"
                onClick={() => this.handleShowhatenDetail(row)}>
                <span class="work-sheet-deion">{cell}</span>
              </div>
            );
          }
        },
        {
          headerSlots: 'action',
          minWidth: 40,
          resizable: false,
          align: 'center',
          formatter: (row, opt, cell) => {
            let btnList = statusBtn[row.workStatusName] || [];

            return (
              <BaseActionCell
                actions={btnList}
                on={{
                  'action-select': (value, action) =>
                    this.$emit('action', value, action.label, row)
                }}
              />
            );
          }
        }
      ],

      sidx: 'a.create_time',
      sord: 'desc'
    };
  },
  watch: {
    sidx() {
      this.refresh();
    },
    sord() {
      this.refresh();
    }
  },
  methods: {
    refresh() {
      this.getDeptTree();
      this.refreshTable();
    },
    /** @desc 刷新 table数据 */
    refreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;

      this.ajax
        .getOrderTableDataList(13, {
          ...this.form,
          pageNo,
          pageSize,
          sord: this.sord,
          sidx: this.sidx
        })
        .then(res => {
          if (res.success == false) {
            this.$message.error(res.message || '工单列表数据获取失败');
            return;
          }
          this.$refs.table.refresh(res);
        });
    },
    /** @desc 表格排序 发生变化事件 */
    handleSortChange({ column, prop, order }) {
      let col = this.columns.find(item => item.prop == prop),
        sord = { descending: 'desc', ascending: 'asc' }[order];

      this.sord = sord;
      this.sidx = col.sortKey || 'a.create_time';
    },
    handleChange(val) {},
    /** @desc 获取报修科室 搜索栏数据 */
    async getDeptTree() {
      const tree = await api.getTree();
      if (!tree.success) {
        this.$message.error(tree.message || '出错啦');
        return;
      }
      let deptIndex = this.searchList.findIndex(
        item => item.value == 'repairManId'
      );
      let newItem = this.searchList[deptIndex];
      newItem.elementProp.treeData = tree.object;
      this.$set(this.searchList, deptIndex, newItem);
    },
    /**@desc 加载处理人 数据 */
    handleLoadHandlePerson(data, callback) {
      let { text = '', pageNo = 1 } = data;
      this.ajax
        .getOrderHandlePersonList({ employeeName: text, pageNo, pageSize: 15 })
        .then(res => {
          if (res.success == false) {
            callback(false);
            return;
          }

          callback(res.rows || []);
        });
    },
    handleLoadRepairMan(data, callback) {
      let { text = '', pageNo = 1 } = data;
    },
    hanldeOpenCreateOrder() {},
    handleExport() {
      this.$emit('export', 13);
    },

    /** @desc 点击 故障描述 显示 工单详情 */
    handleShowWorkSheetDetail(row) {
      this.$emit('show-detail', row);
    },
    /** @desc 显示 催办次数 详情 */
    handleShowhatenDetail(row) {}
  }
};
</script>

<style lang="scss" scoped>
@import '@/views/workSheet/comment/css/workSheetTable.scss';
</style>
