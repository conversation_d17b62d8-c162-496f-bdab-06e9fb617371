<template>
  <div class="order-action-content">
    <div class="order-action-title">终止/暂停工单</div>
    <ts-form ref="form" :model="form" :rules="rules">
      <ts-form-item label="终止/暂停说明" prop="remark">
        <ts-input
          v-model="form.remark"
          placeholder="请输入终止/暂停说明"
          clearable
        ></ts-input>
      </ts-form-item>
    </ts-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {},
      rules: {
        remark: [{ required: true, message: '必填' }]
      }
    };
  },
  methods: {
    async validate(cb) {
      let res = await this.$refs.form.validate().catch(e => e);
      if (cb) {
        cb(res);
      } else {
        return res;
      }
    }
  }
};
</script>
