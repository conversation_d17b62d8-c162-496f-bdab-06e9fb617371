<template>
  <div class="order-action-content">
    <div class="order-action-title">派单/重新指派</div>
    <ts-form ref="form" :model="form" :rules="rules">
      <div class="flex">
        <div class="flex-grow">
          <ts-form-item label="处理科室" prop="businessDeptId">
            <InputSelect
              v-model="form.businessDeptId"
              :inputText.sync="form.businessDeptName"
              valueName="deptId"
              labelName="deptName"
              width="200"
              :loadOnce="true"
              @load="getHandleDeptData"
            ></InputSelect>
          </ts-form-item>
        </div>

        <div class="flex-grow">
          <ts-form-item
            v-show="
              form.businessDeptId == data.businessDeptId || !form.businessDeptId
            "
            label="业务类型"
            prop="fkFaultTypeId"
          >
            <InputTree
              ref="faultTypeTree"
              :treeData="faultTypeTree"
              textName="fullPath"
              v-model="form.fkFaultTypeId"
              :disabled="!form.businessDeptId"
            ></InputTree>
          </ts-form-item>
        </div>
      </div>
      <div class="flex">
        <div
          class="flex-grow"
          v-if="
            form.businessDeptId == data.businessDeptId || !form.businessDeptId
          "
        >
          <ts-form-item label="处理人" prop="fkUserId">
            <div class="flex-col-center">
              <InputSelect
                ref="handlerSelect"
                v-model="form.fkUserId"
                :inputText.sync="form.fkUserName"
                valueName="userId"
                labelName="name"
                :loadOnce="true"
                @load="getHandlePersonData"
                :disabled="!form.fkFaultTypeId"
                class="flex-grow"
              >
                <template v-slot:default="{ data }">
                  {{ data.name }}-{{ data.deptName }}-{{
                    data.processCountString
                  }}
                </template>
              </InputSelect>
              <el-button style="margin-left: 8px;" @click="handleByMySelf">
                由我处理
              </el-button>
            </div>
          </ts-form-item>
        </div>

        <div class="flex-grow">
          <ts-form-item label="备注说明">
            <el-input
              v-model="form.remark"
              placeholder="请输入备注说明"
              :maxlength="100"
            ></el-input>
          </ts-form-item>
        </div>
      </div>
    </ts-form>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {},
      rules: {
        businessDeptId: [{ required: true, message: '请选择处理科室' }],
        fkUserId: [{ required: true, message: '请选择处理人' }],
        fkFaultTypeId: [{ required: true, message: '请选择故障类型' }]
      },
      faultTypeTree: []
    };
  },
  watch: {
    data: {
      handler() {
        this.$set(this, 'form', JSON.parse(JSON.stringify(this.data)));
        // this.form = JSON.parse(JSON.stringify(this.data));
        this.$nextTick(() => {
          this.$refs.form && this.$refs.form.clearValidate();
        });
      },
      immediate: true,
      deep: true
    },
    'form.businessDeptId': {
      handler(val, oldVal) {
        if (oldVal || !val) {
          this.form.fkFaultTypeId = null;
          this.form.fkUserId = null;
        }
        if (!val) {
          return;
        }
        this.getFaultTypeTreeData(val);
      },
      immediate: true
    },
    'form.fkFaultTypeId': {
      handler(val) {
        if (!val) {
          return;
        }
        this.$nextTick(() => {
          this.$refs.handlerSelect && (this.$refs.handlerSelect.pageNo = 1);
          this.$refs.handlerSelect && this.$refs.handlerSelect.loadList();
        });
      },
      immediate: true
    }
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  methods: {
    /**@desc 获取 处理科室 信息 */
    getHandleDeptData(data, callback) {
      let { text = '', pageNo = 1 } = data;
      this.ajax
        .meauList({ deptName: text, pageNo, pageSize: 999 })
        .then(res => {
          if (res.success == false) {
            callback(false);
            return;
          }

          callback(res.object || []);
        });
    },
    /**@desc 获取 故障类型 信息 */
    getFaultTypeTreeData(deptId) {
      this.ajax.getFaultTypeTreeDataByStatus(deptId).then(res => {
        this.faultTypeTree = res.object;
      });
    },
    /**@desc 获取 处理人 信息 */
    getHandlePersonData({ text = '', pageNo = 1 }, callback) {
      this.ajax
        .getHandlePersonDataByStatus({
          employeeName: text,
          pageNo,
          pageSize: 20,
          faultTypeId: this.form.fkFaultTypeId,
          deptId: this.form.businessDeptId
        })
        .then(res => {
          callback(res.rows);
        });
    },
    /**@desc 由我处理 */
    async handleByMySelf() {
      this.$set(this.form, 'fkUserId', this.userInfo.employeeId);
      await this.$nextTick();
      this.$refs.handlerSelect.oldValOptionName = this.$refs.handlerSelect.selectOptionName = `${this.userInfo.employeeName}-${this.userInfo.orgName}`;
    },
    /**@desc 数据校验 */
    async validate(cb) {
      let res = await this.$refs.form.validate().catch(res => res);
      if (cb) {
        cb(res);
      } else {
        return res;
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
