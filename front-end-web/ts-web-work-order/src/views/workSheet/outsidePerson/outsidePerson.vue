<template>
  <div class="content ts-container">
    <div class="table-content flex flex-column">
      <div class="top flex flex-row-between">
        <div class="search-bar flex">
          <el-input
            placeholder="输入单位或姓名关键字搜索"
            class="search-input"
            v-model="fuzzy"
          ></el-input>
          <el-button class="trasen-btn trasen-perpul" @click="refresh()"
            >搜索</el-button
          >
          <div
            class="trasen-search-reset"
            @click="
              () => {
                fuzzy = '';
                refresh();
              }
            "
          >
            <i class="el-icon-refresh-right"></i>
          </div>
        </div>
        <el-button class="trasen-btn trasen-perpul" @click="openEditModal()"
          >新增</el-button
        >
      </div>
      <div class="flex-grow">
        <CellTable v-bind="tableOptions" ref="table">
          <template slot="actionRow" slot-scope="scope">
            <ActionCell :renderFunc="renderAction" :scope="scope"></ActionCell>
          </template>
        </CellTable>
      </div>
    </div>

    <el-dialog
      :title="editModalTitle"
      :visible.sync="showEditModal"
      @close="closeEditModal"
      :close-on-click-modal="false"
      width="640px"
    >
      <el-form
        :model="editData"
        :rules="editFormRules"
        ref="editForm"
        label-width="110px"
      >
        <el-form-item label="单位" prop="institutionalAffiliations">
          <InputSelect
            v-model="editData.institutionalAffiliations"
            labelName="institutional_affiliations"
            valueName="institutional_affiliations"
            :loadOnce="true"
            :inputUsefull="true"
            v-if="showEditModal"
            @load="handleRenderInstitution"
          ></InputSelect>
        </el-form-item>

        <el-form-item label="职位">
          <InputSelect
            v-model="editData.position"
            valueName="position"
            labelName="position"
            :inputUsefull="true"
            :loadOnce="true"
            v-if="showEditModal"
            @load="handleRenderPosition"
          ></InputSelect>
        </el-form-item>

        <el-form-item label="所属管辖科室" prop="belongsDeptId">
          <InputTree
            :treeData="deptTreeData"
            v-model="editData.belongsDeptId"
            @change="handleDeptChange"
            v-if="showEditModal"
          ></InputTree>
        </el-form-item>

        <el-form-item label="人员名称" prop="userName">
          <el-input
            v-model="editData.userName"
            maxlength="10"
            :show-word-limit="true"
          ></el-input>
        </el-form-item>

        <el-form-item label="联系方式" prop="phone">
          <el-input
            v-model="editData.phone"
            :disabled="editModalTitle == '编辑'"
          ></el-input>
          <!-- onkeyup="this.value = this.value.replace(/[^\d]/g,'');" -->
        </el-form-item>

        <el-form-item label="职责说明">
          <el-input
            v-model="editData.jobDeion"
            maxlength="80"
            :show-word-limit="true"
            :autosize="{ minRows: 4, maxRows: 4 }"
            resize="none"
            type="textarea"
          ></el-input>
        </el-form-item>
        <el-form-item label="账号状态" v-if="editModalTitle == '编辑'">
          <el-switch v-model="editData.status"></el-switch>
        </el-form-item>
      </el-form>
      <template slot="footer">
        <div class="flex flex-center">
          <el-button
            class="trasen-btn trasen-perpul"
            type="primary"
            style="margin-left:0;"
            @click="handleSavaEditData()"
            >保存</el-button
          >
          <el-button class="trasen-btn" @click="closeEditModal">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import cellTable from '../comment/components/cellTabel.vue'; //表格
import actionCell from '../comment/components/actionCell.vue'; //表格的操作栏
import inputTree from '@/components/input-tree/inputTree.vue'; //可搜索下拉选择树
import inputSelect from '@/components/input-select/inputSelect.vue'; //可搜索选择下拉框

export default {
  components: {
    CellTable: cellTable,
    ActionCell: actionCell,
    InputTree: inputTree,
    InputSelect: inputSelect
  },
  data() {
    return {
      API: {
        getTableList:
          '/ts-worksheet/externalPersonnel/externalPersonnelPageList', //获取院外人员表格数据
        changeStatus: '/ts-worksheet/externalPersonnel/enableOrDisable', //启用、禁用账号
        editPerson: '/ts-worksheet/externalPersonnel/save', //新增、编辑账号
        queryOrderStatus:
          '/ts-worksheet/externalPersonnel/QueryingPersonnelStatus/', //禁用前查询是否存在处理中的工单
        getHisInstitution:
          '/ts-worksheet/externalPersonnel/selectAllInstitutionalAffiliations', //查询历史所有机构名称
        getHisPosition: '/ts-worksheet/externalPersonnel/selectAllPosition', //获取历史职位名称
        getDptTreeData: '/ts-basics-bottom/organization/getTree' //获取科室树
      },

      fuzzy: '', //表格查询 单位、姓名关键词搜索

      showEditModal: false, //控制是否显示新增、编辑弹框
      deptTreeData: [], //所属管辖科室数据
      editModalTitle: '新增', //新增、编辑弹框标题
      editData: {}, //新增、编辑数据
      editFormRules: {
        //新增编辑规则
        institutionalAffiliations: [
          { required: true, message: '单位不能为空' }
        ],
        userName: [{ required: true, message: '人员名称不能为空' }],
        belongsDeptId: [{ required: true, message: '所属科室不能为空' }],
        phone: [
          { required: true, message: '联系方式不能为空' },
          {
            message: '请输入正确的电话号码',
            trigger: 'change',
            validator: function(rule, value, callback) {
              let numberTest = new RegExp(/[^\d]/g), //数字验证
                phoneTest = new RegExp(/^1[^{1,2}]\d{9}$/); //电话号码验证
              if (numberTest.test(value)) {
                callback('请输入正确的电话号码');
                return;
              }

              if (!phoneTest.test(value)) {
                callback('请输入正确的手机号');
                return;
              }

              callback();
            }
          }
        ]
      },

      tableSearchData: null, //表格搜索输入框字段
      tableOptions: {
        showIndex: true,
        columns: [
          {
            label: '归属单位',
            align: 'left',
            prop: 'institutionalAffiliations'
          },
          {
            label: '人员名称',
            align: 'left',
            width: 100,
            prop: 'userName'
          },
          {
            label: '联系方式',
            align: 'center',
            width: 130,
            prop: 'phone'
          },
          {
            label: '职位',
            align: 'left',
            width: 100,
            prop: 'position'
          },
          {
            label: '职责说明',
            align: 'left',
            prop: 'jobDeion'
          },
          {
            label: '账号状态',
            align: 'center',
            width: 80,
            prop: 'status',
            formatter: function(row, column, val) {
              return ['停用', '启用'][val];
            }
          }
        ],
        refresh: data => {
          return this.refreshTable(data);
        }
      }
    };
  },
  methods: {
    refresh() {
      this.$api({
        url: this.API.getDptTreeData,
        method: 'post'
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '出错啦');
          return;
        }
        this.deptTreeData = res.object || [];
      });
      this.$refs.table.refreshTable();
    },
    refreshTable(data) {
      let searchData = {
        ...data,
        fuzzy: this.fuzzy
      };
      return this.$api({
        url: this.API.getTableList,
        method: 'get',
        data: searchData,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '出错啦');
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //打开新增、编辑弹框
    openEditModal(row) {
      this.editData = row
        ? JSON.parse(JSON.stringify(row))
        : {
            status: 1
          };
      this.editModalTitle = row ? '编辑' : '新增';
      // if(!row){
      //     this.editData
      // }
      this.editData.status
        ? (this.editData.status = true)
        : (this.editData.status = false);
      this.showEditModal = true;
    },
    //关闭新增、编辑弹框
    closeEditModal() {
      this.showEditModal = false;
      this.editData = {};
      this.$refs.editForm.resetFields();
    },
    //更改人员状态前， 查询人员是否具有处理中工单
    queryOrderStatus(row) {
      row.status
        ? this.$api({
            url: this.API.queryOrderStatus + row.pkExternalPersonnelId,
            method: 'get'
          }).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '出错了');
              return;
            }

            let tips = res.object;
            this.$confirm(tips, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
              .then(() => {
                this.handleChangePersonStatus(row);
              })
              .catch(() => {});
          })
        : this.handleChangePersonStatus(row);
    },
    //------------------------- 事件处理 -----------------------
    //处理更改人员状态
    handleChangePersonStatus(row) {
      this.$api({
        url: this.API.changeStatus,
        method: 'post',
        data: JSON.stringify({
          pkExternalPersonnelId: row.pkExternalPersonnelId,
          status: row.status ? 0 : 1
        }),
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '出错啦');
          return;
        }

        this.$message.success('操作成功');
        this.refresh();
      });
    },
    //处理保存操作
    handleSavaEditData() {
      this.$refs.editForm.validate(res => {
        if (!res) return;
        this.editData.status
          ? (this.editData.status = 1)
          : (this.editData.status = 0);
        this.$api({
          url: this.API.editPerson,
          method: 'post',
          data: JSON.stringify(this.editData),
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '出错啦');
            return;
          }

          this.closeEditModal();
          this.$message.success(this.editModalTitle + '成功');
          this.refresh();
        });
      });
    },
    //处理远程加载单位历史
    handleRenderInstitution(data, callback) {
      this.$api({
        url: this.API.getHisInstitution,
        method: 'post',
        data: {
          institutionalAffiliations: data.text
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      })
        .then(res => {
          if (!res.success) {
            callback();
            return;
          }
          //待完成，可能有问题
          callback(res.object);
        })
        .catch(res => {
          //加载失败的时候
          callback();
        });
    },
    //远程处理加载职位历史
    handleRenderPosition(data, callback) {
      this.$api({
        url: this.API.getHisPosition,
        method: 'post',
        data: {
          position: data.text
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
        }
      })
        .then(res => {
          if (!res.success) {
            callback();
            return;
          }
          //待完成，可能有问题
          callback(res.object);
        })
        .catch(res => {
          callback();
        });
    },
    //处理科室改变，修改科室名
    handleDeptChange(val, node, tree) {
      if (val) {
        this.editData.belongsDeptName = val.name || null;
      } else {
        this.editData.belongsDeptName = null;
      }
    },
    //渲染操作栏
    renderAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row));

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.queryOrderStatus(row)}>
                  <i class="fa fa-pencil-square-o"></i>
                  {row.status ? '禁用' : '启用'}
                </div>
              </ElDropdownItem>
              <ElDropdownItem class="action-item">
                <div onClick={() => this.openEditModal(row)}>
                  <i class="fa fa-pencil-square-o"></i>编辑
                </div>
              </ElDropdownItem>
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );

      let buttons = '',
        buttonList = [
          <ElButton
            class="trasen-btn"
            type="primary"
            onClick={() => {
              this.openEditModal(row);
            }}>
            编辑
          </ElButton>,
          <ElButton
            class="trasen-btn"
            onClick={() => {
              this.queryOrderStatus(row);
            }}>
            {row.status ? '禁用' : '启用'}
          </ElButton>
        ];
      return (
        <div class="action-row">
          <templates>
            <ElDropdown trigger="hover">
              <div class="more-action-icon">
                <i class="el-icon-more-outline"></i>
              </div>
              <ElDropdownMenu slot="dropdown">
                <ElDropdownItem class="action-item">
                  {buttonList[1]}
                </ElDropdownItem>
                <ElDropdownItem class="action-item">
                  {buttonList[0]}
                </ElDropdownItem>
                <ElDropdownItem class="action-item"></ElDropdownItem>
              </ElDropdownMenu>
            </ElDropdown>
          </templates>
        </div>
      );
    }
  }
};
</script>
<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.content {
  height: 100%;
  padding: 10px;
}
.table-content {
  height: 100%;
}
.top {
  margin-bottom: 10px;
}
.search-input {
  width: 200px;
}
</style>
