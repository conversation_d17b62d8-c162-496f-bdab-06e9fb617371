<template>
  <div class="content ts-container">
    <div class="top-content flex">
      <div class="flex flex-col-center">
        <div
          class="card-item"
          @click="handleTabChange(0)"
          :class="{ active: active == 0 }"
        >
          未读({{ unReadedList.length }})
        </div>
        <div
          class="card-item"
          @click="handleTabChange(1)"
          :class="{ active: active == 1 }"
        >
          已读({{ readedList.length }})
        </div>
      </div>
      <span
        class="set-all-read flex flex-col-end"
        @click="handleSetAllReaded"
        v-show="active == 0 && unReadedList.length"
        >全部置为已读</span
      >
    </div>
    <div class="message-list" v-show="active == 0">
      <el-scrollbar class="scrollbar">
        <template v-for="(item, index) of unReadedList">
          <div
            class="message-item"
            :key="index"
            @click="handleMessageBoxClick(item)"
          >
            <div
              class="flex flex-row-between message-item-title-content flex-col-center"
            >
              <div class="title">· 消息标题：{{ item.title }}</div>
              <div class="time">{{ item.time }}</div>
            </div>
            <div v-html="item.messageContent"></div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <div class="message-list" v-show="active == 1">
      <el-scrollbar class="scrollbar">
        <template v-for="(item, index) of readedList">
          <div
            class="message-item"
            :key="index"
            @click="handleMessageBoxClick(item)"
          >
            <div
              class="flex flex-row-between message-item-title-content flex-col-center"
            >
              <div class="title">· 消息标题：{{ item.title }}</div>
              <div class="time">{{ item.time }}</div>
            </div>
            <div v-html="item.messageContent"></div>
          </div>
        </template>
      </el-scrollbar>
    </div>
    <DataMessageComponent
      v-if="showDetail"
      v-model="showDetail"
      :messageInfo="orderDetail"
    />
  </div>
</template>

<script>
import { $api } from '@/api/ajax';
import DataMessageComponent from '../workOrderHomePage/components/DataMessageComponent.vue';
import api from '@/api/ajax/workOrderHomePage';

export default {
  components: {
    DataMessageComponent
  },
  data() {
    return {
      active: 0, //当前页签选中元素
      unReadedList: [], //未读消息列表
      readedList: [], //已读消息列表
      showDetail: false, //是否展示工单详情
      orderDetail: {} //详情信息
    };
  },
  methods: {
    refresh() {
      this.getMessageList(0);
      this.getMessageList(1);
    },
    //获取消息列表
    async getMessageList(isRead) {
      let res = await $api({
        url: '/ts-worksheet/message/selectMessagePageList',
        method: 'GET',
        params: { isRead, sidx: 'create_time', sord: 'desc' }
      });
      if (res.success == false) {
        this.$message.error(res.message || '请求错误，请稍后再试');
        return;
      }
      let newList = [];
      res.object &&
        res.object.forEach(item => {
          newList.push({
            title: item.messageTitle,
            messageContent: item.messageContent,
            time: item.createTime,
            pkWsMessageId: item.pkWsMessageId,
            isRead,
            workNumber: item.workNumber
          });
        });

      if (isRead) {
        this.readedList = newList;
      } else {
        this.$devopParentTypeFun({
          type: 'headRightMessageChange',
          data: newList.length
        });
        this.unReadedList = newList;
      }
    },
    handleTabChange(index) {
      if (index == this.active) {
        return;
      }
      this.active = index;
      this.getMessageList(index);
    },
    //---------------------- 事件处理 --------------------
    //处理全部置为已读事件
    handleSetAllReaded() {
      $api({
        url: '/ts-worksheet/message/updateMessageAllByFkUserId',
        method: 'get'
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '全部置为已读失败');
          return;
        }
        this.$message.success('全部置为已读成功');
        this.getMessageList(0);
        this.getMessageList(1);
      });
    },
    //处理消息盒子点击事件
    async handleMessageBoxClick(row) {
      const detailRes = await api.workSheetInfo(row.workNumber);
      if (detailRes.success == false) {
        this.$message.error(
          detailRes.message || '详细数据加载失败，请稍后再试'
        );
        return;
      }
      this.showDetail = true;
      this.orderDetail = detailRes.object;

      let res = await $api({
        url: `/ts-worksheet/message/selectOneWsWsMessageListOutVoById/${row.pkWsMessageId}/${row.isRead}`,
        method: 'GET'
      });
      if (res.success == false) {
        this.$message.error(res.message || '状态修改失败');
        return;
      }
      this.getMessageList(0);
      this.getMessageList(1);
    }
  }
};
</script>
<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.content {
  height: 100%;
}
.card-item {
  height: 36px;
  width: 100px;
  line-height: 36px;
  text-align: center;
  border: 2px solid #e8ecf2;
  cursor: pointer;
  background-color: #e8ecf2;
  &:first-child {
    border-radius: 4px 0 0 4px;
  }
  &:last-child {
    border-radius: 0 4px 4px 0;
  }
  &.active {
    background-color: #fff;
  }
}
.set-all-read {
  margin-left: 15px;
  line-height: 25px;
  text-decoration: underline;
  color: #002bff;
}
.message-list {
  height: calc(100% - 36px);
  overflow: auto;
  .message-item {
    padding: 20px 15px;
    border-bottom: 1px solid;
    cursor: pointer;
    .message-item-title-content {
      margin-bottom: 8px;
    }
    .title {
      font-weight: 600;
      font-size: 16px;
    }

    &:hover {
      background-color: #5260ff14;
    }
  }
}
.scrollbar {
  height: 100%;
  /deep/.el-scrollbar__wrap {
    overflow-x: hidden;
  }
}
</style>
