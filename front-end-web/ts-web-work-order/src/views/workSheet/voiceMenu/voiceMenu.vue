<template>
  <div class="voice-box ts-container">
    <div class="top">
      <el-button @click="handleSetVoice">配置语音菜单</el-button>
      <el-button class="trasen-perpul" @click="addHandle">
        新增
      </el-button>
      <el-button @click="selectDelHandle">批量删除</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="table"
      :data="tableData"
      border
      stripe
      style="margin: 10px 0"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="39"> </el-table-column>
      <el-table-column
        align="left"
        prop="deptName"
        label="科室名字"
        show-overflow-tooltip
        :resizable="false"
      />
      <el-table-column
        align="center"
        orderKnowledgeBase
        prop="px"
        label="排序字段"
        show-overflow-tooltip
        :resizable="false"
      />
      <el-table-column
        align="center"
        orderKnowledgeBase
        prop="inputContent"
        label="数字"
        show-overflow-tooltip
        :resizable="false"
      />
      <el-table-column
        align="center"
        orderKnowledgeBase
        prop="fileRequired"
        label="附件必填"
        show-overflow-tooltip
        :formatter="
          (data, row) => {
            return data.fileRequired ? '是' : '否';
          }
        "
        :resizable="false"
      />
      <el-table-column
        label="操作"
        width="50"
        align="center"
        :resizable="false"
      >
        <template slot-scope="scope">
          <ActionCell :renderFunc="renderAction" :scope="scope"></ActionCell>
        </template>
      </el-table-column>
    </el-table>

    <addEditVoiceMenuComp
      v-model="dialogVisible"
      :row="row"
    ></addEditVoiceMenuComp>
  </div>
</template>

<script>
import ActionCell from '@/views/workSheet/comment/components/actionCell'; //表格的操作栏
import addEditVoiceMenuComp from './components/addEditVoiceMenuComp';
import Sortable from 'sortablejs'; //行拖拽

export default {
  name: 'voiceMenu',
  components: {
    ActionCell,
    addEditVoiceMenuComp
  },
  data: () => ({
    loading: false,
    tableData: [],
    pageParams: {
      pageNo: 1,
      pageSize: 10
    },
    type: '',
    row: null,
    dialogVisible: false,
    multipleSelection: []
  }),
  mounted() {
    this.$nextTick(() => {
      const tbody = this.$refs.table.$el.querySelector(
        '.el-table__body-wrapper tbody'
      );
      Sortable.create(tbody, {
        ghostClass: 'ghost-wrapper',
        onEnd: async ({ newIndex, oldIndex }) => {
          if (newIndex == oldIndex) {
            return;
          }
          //还原调整位置
          let lineList = this.$refs.table.$el.querySelectorAll(
              '.el-table__body-wrapper tbody .el-table__row'
            ),
            endLine = lineList[oldIndex + 1],
            newLine = lineList[newIndex];
          if (oldIndex + 1 == lineList.length) {
            newLine.parentNode.appendChild(newLine);
          } else {
            newLine.parentNode.insertBefore(newLine, endLine);
          }

          const currRow = this.tableData.splice(oldIndex, 1)[0];
          this.tableData.splice(newIndex, 0, currRow);
          this.tableData.forEach((item, index) => {
            item.px = index;
          });

          let sortList = this.tableData.map((item, index) => {
            return {
              pkOmMeauId: item.pkOmMeauId,
              px: index
            };
          });
          await this.ajax.changeVoiceSort(sortList);
          this.refresh();
        }
      });
    });
  },
  methods: {
    refresh() {
      this.refreshTable();
    },
    //表格刷新方法
    async refreshTable(data) {
      let searchData = {
        // ...data,
        // ...this.pageParams,
        sidx: 'px',
        sord: 'asc'
      };
      try {
        this.loading = true;
        const res = await this.ajax.selectOmMeauList(searchData);
        if (res.success === false) {
          throw res.message;
        }
        this.tableData = res.object;
        this.loading = false;
      } catch (e) {
        this.$message.error(e || '出错啦');
      }
    },
    addHandle() {
      this.type = 'add';
      this.row = {};
      this.dialogVisible = true;
    },
    openEditModal(row) {
      this.type = 'edit';
      this.row = JSON.parse(JSON.stringify(row));
      this.dialogVisible = true;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleSetVoice() {
      this.$api({
        url: '/ts-worksheet/omMeau/configuringTheVoiceMenu',
        method: 'get'
      }).then(res => {
        if (res.message == false) {
          this.$message.error(res.message);
        } else {
          this.$message.success(res.object);
        }
      });
    },
    selectDelHandle() {
      if (!this.multipleSelection.length) {
        this.$message.warning('尚未选择删除行');
        return;
      }
      const ids = this.multipleSelection.map(item => item.pkOmMeauId).join(',');
      this.deleteHandle(ids);
    },
    async deleteHandle(ids) {
      try {
        const result = await this.$confirm(
          '此操作将删除该语音菜单, 是否继续?',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        );

        if (result === 'confirm') {
          const res = await this.ajax.deleteOmMeau(ids);
          if (res.statusCode === 200) {
            this.$message.success('删除成功!');
            this.refreshTable();
          }
        }
      } catch (e) {}
    },
    //渲染操作栏
    renderAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row));

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.openEditModal(row)}>
                  <i class="fa fa-pencil-square-o"></i>编辑
                </div>
              </ElDropdownItem>
              <ElDropdownItem class="action-item">
                <div onClick={() => this.deleteHandle(row.pkOmMeauId)}>
                  <i class="fa fa-trash-o"></i>删除
                </div>
              </ElDropdownItem>
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    }
  }
};
</script>

<style scoped lang="scss">
.voice-box {
  padding: 5px;
  .top {
    display: flex;
    justify-content: flex-end;
  }
  /deep/ th {
    .cell {
      color: #000;
      font-weight: 700;
    }
  }
}
/deep/ .more-action-icon {
  cursor: pointer;
  min-width: 29px;
}
/deep/.ghost-wrapper > * {
  background: $theme-color-8 !important;
}
</style>
