<template>
  <el-dialog
    width="400px"
    :title="title"
    :before-close="handleClose"
    :visible.sync="value"
    class="edit-modal"
  >
    <el-form
      :model="ruleForm"
      :rules="rules"
      ref="ruleForm"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="科室" prop="deptId" label-width="90px">
        <InputTree
          v-model="ruleForm.deptId"
          v-if="value"
          :treeData="deptTreeData"
          @change="handleDeptChange"
        ></InputTree>
      </el-form-item>
      <el-form-item label="数字" prop="inputContent" label-width="90px">
        <el-input v-model="ruleForm.inputContent"></el-input>
      </el-form-item>
      <el-form-item label="附件必填" prop="fileRequired" label-width="90px">
        <el-radio-group v-model="ruleForm.fileRequired">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="trasen-perpul" @click="submitHandle">保 存</el-button>
      <el-button @click="handleClose">取 消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/workSheetSetUp';
import InputTree from '@/components/input-tree/inputTree.vue';
export default {
  name: 'addEditVoiceMenuComp',
  model: {
    event: 'change',
    prop: 'value'
  },
  components: {
    InputTree
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => {}
    }
  },
  created() {
    this.getTreeHandle();
  },
  watch: {
    row: {
      handler(val) {
        if (val && JSON.stringify(val) !== '{}') {
          this.type = 'edit';
          this.title = '修改语音菜单';
          this.ruleForm = val;
        } else {
          this.type = 'add';
          this.title = '新增语音菜单';
          this.ruleForm = {
            fileRequired: 0
          };
        }
      }
    }
  },
  data: () => ({
    title: '',
    type: '',
    deptTreeData: [],
    ruleForm: {
      deptId: '',
      deptName: '',
      inputContent: '',
      fileRequired: 0
    },
    rules: {
      inputContent: [
        { required: true, message: '请输入数字', trigger: 'blur' }
      ],
      deptId: [{ required: true, message: '请选择科室', trigger: 'change' }],
      px: [{ required: true, message: '请输入排序值', trigger: 'blur' }],
      fileRequired: [{ required: true, trigger: 'blur' }]
    }
  }),
  methods: {
    async getTreeHandle() {
      let _this = this;
      try {
        const tree = await _this.ajax.getTree();
        if (!tree.success) {
          throw tree.message;
        }
        _this.deptTreeData = tree.object || [];
      } catch (e) {
        _this.$message.error(e || '出错啦');
      }
    },
    handleDeptChange(e) {
      this.ruleForm.deptName = e && e.name;
      if (this.ruleForm.deptName && this.ruleForm.deptId) {
        this.$refs.ruleForm.clearValidate(['deptId']);
      }
    },
    async submitHandle() {
      const result = await this.$refs['ruleForm'].validate();

      if (result) {
        const data = {
          ...this.ruleForm
        };

        try {
          const res = await api.saveOrUpdate(data);
          if (!res.success) {
            throw res.message;
          }

          const title = this.type === 'add' ? '新增成功!' : '修改成功!';
          if (res.statusCode === 200) {
            this.$message.success(title);
            this.handleClose();
            this.$parent.refreshTable();
          }
        } catch (e) {
          this.$message.error(e);
        }
      }
    },
    handleClose() {
      this.$refs.ruleForm.clearValidate();
      this.ruleForm = {};
      this.$emit('change', false);
    },
    handlePxInput(val) {
      let newVal = val.replace(/[^0-9]/g, '');
      if (newVal > 99999) {
        newVal = 99999;
      }
      this.ruleForm.px = newVal;
    }
  }
};
</script>

<style lang="scss" scoped>
.edit-modal /deep/.el-dialog__body {
  padding-bottom: 24px !important;
}
</style>
