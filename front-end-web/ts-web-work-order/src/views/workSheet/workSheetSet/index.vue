<template>
  <div class="ts-container">
    <!-- <div class="title flex-col-center">验收及评价规则</div>
    <div class="setting-item">
      <div class="flex-col-center">
        <span>自动验收</span>
        <el-switch v-model="automatedAcceptance"> </el-switch>
        <span>开启后，待验收工单将根据设置的天数自动验收通过</span>
      </div>
    </div>

    <div class="setting-item">
      <div class="flex-col-center">
        <span>自动评价</span>
        <el-switch v-model="automatedEvaluate"> </el-switch>
        <span>开启后，待评价工单将根据设置的天数自动按规则评分</span>
      </div>
    </div>

    <div class="setting-item">
      <div class="flex-col-center">
        <span>坐席分机</span>
        <el-switch v-model="telExt"> </el-switch>
        <span>
          开启后，服务台来电将支持分机模式（语音菜单、来电分配坐席等）
        </span>
      </div>
    </div> -->

    <div class="title flex-col-center">时效性设置</div>
    <div class="setting-item">
      <div class="flex-col-center">
        <span>要求接单时间</span>
        <el-input
          v-model="receivingTime"
          style="width: 200px; margin-left: 8px;"
          placeholder="请输入要求接单时间"
          clearable
          @input="validateTowDecimalPlaces"
          @blur="handleTowDecimalPlacesBlur()"
        >
          <template slot="append">H</template>
        </el-input>
      </div>
    </div>

    <div class="title flex-col-center">报修设置</div>
    <div class="setting-item">
      <div class="flex-col-center">
        <span>多院区办公</span>
        <el-switch v-model="multiHospitalDistrict"> </el-switch>
        <span>开启后，支持院区维护，报修必填院区</span>
      </div>

      <div class="yard-set" v-show="multiHospitalDistrict">
        <span @click="hanldeOpenSetYardSet">设置</span>
        <span>
          {{ yardListLabel }}
        </span>
      </div>
    </div>

    <div class="setting-item">
      <div class="flex-col-center">
        <span>报修地址是否必填</span>
        <el-switch v-model="repairAddressRequired"></el-switch>
        <span>开启后，填报工单时，报修地址将会是必填项</span>
      </div>
    </div>

    <div class="flex" style="margin-top: 40px;margin-left: 110px;">
      <el-button class="trasen-perpul" @click="handleSaveConfig">
        确定
      </el-button>
      <el-button @click="refresh">取消</el-button>
    </div>

    <yard-manage
      :show="showOpenSetYardSet"
      @close="handleYardSetModalClose"
      @refresh="refresh"
    ></yard-manage>
  </div>
</template>

<script>
import yardManage from './components/yard-manage.vue';
export default {
  components: { yardManage },
  data() {
    return {
      automatedAcceptance: false, //是否自动验收
      automatedEvaluate: false, //是否自动评价
      telExt: false, //是否开启电话分机
      multiHospitalDistrict: false, //是否开启多院区
      yardList: [], //多院区院区列表
      showOpenSetYardSet: false, //显示打开
      pkSysConfigId: null,

      repairAddressRequired: false, //报修地址是否必填

      receivingTime: '' // 要求接单时间
    };
  },
  computed: {
    yardListLabel: function() {
      return this.yardList.map(item => item.hospitalDistrictName).join(',');
    }
  },
  methods: {
    refresh() {
      this.ajax.getSystemConfig().then(res => {
        if (!res.success) {
          return;
        }
        this.automatedAcceptance = res.object.automatedAcceptance == 1;
        this.automatedEvaluate = res.object.automatedEvaluate == 1;
        this.telExt = res.object.telExt == 1;
        this.pkSysConfigId = res.object.pkSysConfigId;

        this.yardList = res.object.hospitalDistrictList || [];
        this.multiHospitalDistrict = res.object.multiHospitalDistrict == 1;

        this.repairAddressRequired = res.object.repairAddressRequired == 1;

        this.receivingTime = res.object.requestReceivingTime;
      });
    },
    hanldeOpenSetYardSet() {
      this.showOpenSetYardSet = true;
    },
    handleYardSetModalClose() {
      this.showOpenSetYardSet = false;
    },
    handleSaveConfig() {
      if (!String(this.receivingTime)) {
        this.$message.error('请填写要求接单时间');
        return;
      }
      let data = {
        automatedAcceptance: this.automatedAcceptance ? '1' : '0',
        automatedEvaluate: this.automatedEvaluate ? '1' : '0',
        telExt: this.telExt ? '1' : '0',
        multiHospitalDistrict: this.multiHospitalDistrict ? '1' : '0',
        showOpenSetYardSet: this.showOpenSetYardSet ? '1' : '0',
        pkSysConfigId: this.pkSysConfigId || undefined,
        repairAddressRequired: this.repairAddressRequired ? '1' : '0',
        requestReceivingTime: this.receivingTime
      };
      this.ajax.saveSystemConfig(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '保存失败');
          return;
        }
        this.$message.success('保存成功');
        this.refresh();
      });
    },
    /**@desc 处理要求接单时间输入 保证输入为整数 */
    validateTowDecimalPlaces(value) {
      let matchList = value.match(
        /^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2}|\.{1})?/
      ) || [''];
      this.receivingTime = matchList[0];
    },
    handleTowDecimalPlacesBlur() {
      if (this.receivingTime) {
        this.receivingTime = parseFloat(this.receivingTime);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  color: $theme-font-color;
  line-height: 19px;
  margin-bottom: 15px;
  &::before {
    content: '';
    width: 4px;
    height: 16px;
    background: $theme-color;
    margin-right: $theme-interval;
  }
  &:not(:first-child) {
    margin-top: 24px;
  }
}
.setting-item {
  margin-bottom: 10px;
  margin-left: 14px;
  .el-switch {
    margin-left: $theme-interval;
    margin-right: 16px;
  }
  > div:first-child {
    margin-bottom: 16px;
    span:last-child {
      font-size: 12px;
      color: $theme-font-color-grey;
      line-height: 17px;
    }
  }
}
.yard-set span:first-child {
  margin-left: 24px;
  margin-right: 16px;
  color: $theme-color;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}
</style>
