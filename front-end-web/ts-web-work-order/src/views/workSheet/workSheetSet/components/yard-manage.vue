<template>
  <el-dialog
    :visible="visible"
    title="多院区设置"
    width="600px"
    :close-on-click-modal="false"
    :show-close="false"
  >
    <div class="flex-end create-btn" @click="handleAddNewLine">新增院区</div>
    <el-table :data="tableDataList" stripe border>
      <el-table-column prop="hospitalDistrictName" label="院区名" width="435">
        <template slot-scope="scope">
          <div
            class="table-cell"
            v-if="!scope.row.edit"
            @dblclick="handleEditLine(scope.row, scope.$index)"
          >
            {{ scope.row.hospitalDistrictName }}
          </div>
          <div v-else>
            <el-input
              v-model="editData.hospitalDistrictName"
              placeholder="请输入院区名"
            ></el-input>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" :formatter="computedAction">
      </el-table-column>
    </el-table>
    <div slot="footer">
      <el-button class="trasen-perpul" @click="handleClickInSubmit">
        确定
      </el-button>
      <el-button @click="handleCloseModal">取消</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'yard-manage',
  props: {
    show: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      visible: false,
      tableDataList: [],
      editData: {}
    };
  },
  watch: {
    show: {
      handler: function(val) {
        if (val) {
          this.getList();
        }
        this.visible = val;
      },
      immediate: true
    }
  },
  methods: {
    getList() {
      this.ajax.getHospitalYardList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败');
          this.handleCloseModal();
          return;
        }
        this.tableDataList = res.object || [];
      });
    },
    //处理点击确定操作
    async handleClickInSubmit() {
      if (Object.keys(this.editData).length) {
        await this.handleSaveLine();
      }
      this.handleCloseModal();
    },
    handleCloseModal() {
      this.$emit('close');
    },
    computedAction(row, scope) {
      if (row.edit) {
        return (
          <div class="flex-row-evenly">
            <span class="label-btn" onclick={this.handleSaveLine.bind(this)}>
              保存
            </span>
            <span
              class="label-btn"
              style="color: #333;"
              onclick={this.handleCancelEdit.bind(this, row, scope.$index)}>
              取消
            </span>
          </div>
        );
      }
      return (
        <div class="flex-row-between">
          <span
            class={{
              'label-btn': true,
              'disabled-label-btn': row.hospitalDistrictStatus == 1
            }}
            onclick={this.handleChangeHospitalYardStatus.bind(this, row)}>
            启用
          </span>
          <span
            class={{
              'label-btn': true,
              'warning-label-btn': true,
              'disabled-label-btn': row.hospitalDistrictStatus != 1
            }}
            onclick={this.handleChangeHospitalYardStatus.bind(this, row)}>
            停用
          </span>
          <span
            class="label-btn"
            onclick={() => {
              this.handleEditLine(row, scope.$index);
            }}>
            编辑
          </span>
        </div>
      );
    },
    async handleSaveLine() {
      if (!this.editData.hospitalDistrictName) {
        this.$message.error('院区名字不能为空');
        return;
      }
      await this.ajax.saveHospitalYardItem(this.editData).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '保存失败');
          return;
        }
        this.editData = {};
        this.$message.success('保存成功');
        this.getList();
        this.$emit('refresh');
      });
    },
    handleEditLine(row, index) {
      this.closeOtherEditLine();

      row.edit = true;
      this.$set(this.tableDataList, index, row);
      this.editData = JSON.parse(JSON.stringify(row));
    },
    handleAddNewLine() {
      this.closeOtherEditLine();

      this.editData = {};
      this.tableDataList.push({
        edit: true
      });
    },
    handleCancelEdit(row) {
      this.closeOtherEditLine();

      if (!row.pkHospitalDistrictId) {
        this.tableDataList.pop();
      }
      this.editData = {};
    },
    handleChangeHospitalYardStatus(row) {
      this.ajax
        .changeHospitalYardStatus(
          row.pkHospitalDistrictId,
          row.hospitalDistrictStatus == 1 ? 0 : 1
        )
        .then(res => {
          if (!res.success) {
            this.$message.error(res.message || '操作失败');
            return;
          }

          this.$message.success('修改状态成功');
          this.getList();
          this.$emit('refresh');
        });
    },
    closeOtherEditLine() {
      this.tableDataList.forEach((item, index) => {
        if (item.edit) {
          item.edit = false;
          this.$set(this.tableDataList, index, item);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.create-btn {
  color: $theme-color;
  cursor: pointer;
  line-height: 20px;
  margin-bottom: 4px;
  &:hover {
    opacity: 0.8;
  }
}
.table-cell {
  line-height: 30px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}
.label-btn {
  color: $theme-color;
  cursor: pointer;
  &:hover {
    opacity: 0.8;
  }
}

.warning-label-btn {
  color: $error-color;
}

.disabled-label-btn {
  color: $disabled-color;
  cursor: default;
  &:hover {
    opacity: 1;
  }
}
</style>
