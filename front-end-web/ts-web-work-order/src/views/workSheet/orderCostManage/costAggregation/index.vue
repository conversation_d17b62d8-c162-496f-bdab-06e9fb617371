<template>
  <div class="ts-container">
    <base-search-bar
      ref="searchBar"
      :formList="searchList"
      :elementCol="14"
      @change="handleChange"
    >
      <el-button slot="right" @click="handleExport">导出</el-button>
    </base-search-bar>

    <div class="total-cost-content">填报经费共计 {{ totalMoney }}元</div>
    <el-scrollbar
      style="height: calc(100% - 124px);"
      wrap-style="overflow-x: hidden;"
    >
      <el-table
        :data="tableData"
        stripe
        border
        :default-sort="{ prop: 'costTime', order: 'descending' }"
        @sort-change="handleSortChange"
        style="width: 100%"
      >
        <el-table-column
          v-for="(item, index) of columns"
          :key="index"
          v-bind="item"
          show-overflow-tooltip
        ></el-table-column>
      </el-table>
    </el-scrollbar>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="pageNo"
      :page-sizes="[20, 40, 60, 80, 100, 200, 500, 1000, 2000]"
      :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="flex-end table-pager"
    >
    </el-pagination>
    <CostDetail
      :visible="showCostDetail"
      :data="detailData"
      @close="showCostDetail = false"
    ></CostDetail>
    <DataMessageComponent
      v-if="showOrderDetail"
      v-model="showOrderDetail"
      :messageInfo="orderDetail"
    />
  </div>
</template>

<script>
import baseSearchBar from '@/components/base-search-bar/index.vue';
import CostDetail from '../../comment/components/costRegister/costDetail.vue';
import DataMessageComponent from '../../workOrderHomePage/components/DataMessageComponent';

import searchJs from './search';

export default {
  name: 'cost-aggregation',
  components: {
    baseSearchBar,
    CostDetail,
    DataMessageComponent
  },
  mixins: [searchJs],
  data() {
    return {
      columns: [
        {
          label: '',
          prop: 'rowIndex',
          align: 'center',
          width: 35,
          resizable: false
        },
        {
          label: '工单编号',
          prop: 'workNumber',
          align: 'center',
          width: 105,
          formatter: (rowObject, options, cellvalue) => {
            return (
              <p class="detail-span">
                <span
                  class="deal-link"
                  onclick={this.handlePreviewWorkOrder.bind(this, rowObject)}>
                  {cellvalue}
                </span>
              </p>
            );
          }
        },
        {
          label: '处理科室',
          prop: 'businessDeptName',
          width: 120
        },
        {
          label: '金额(元)',
          prop: 'money',
          align: 'left',
          width: 110,
          resizable: false,
          formatter: function(rowObject, options, cellvalue) {
            let labelStr = cellvalue.toLocaleString();
            if (labelStr.split('.').length > 1) {
              labelStr.split('.')[1].length == 1 ? (labelStr += '0') : null;
            }
            labelStr.split('.').length == 1 ? (labelStr += '.00') : null;
            return labelStr;
          }
        },
        {
          label: '费用描述',
          prop: 'costDeion',
          resizable: false,
          formatter: (rowObject, options, cellvalue) => {
            return (
              <p class="detail-span">
                <span
                  class="deal-link"
                  onclick={this.handlePreview.bind(this, rowObject)}>
                  {cellvalue}
                </span>
              </p>
            );
          }
        },
        {
          label: '发生时间',
          prop: 'costTime',
          align: 'center',
          sortable: 'custom',
          sortOrders: ['ascending', 'descending'],
          resizable: false,
          width: 140
        },
        {
          label: '填报科室',
          prop: 'fillDeptName',
          resizable: false,
          width: 110
        },
        {
          label: '填报人',
          prop: 'fillUser',
          resizable: false,
          width: 80
        },
        {
          label: '填报时间',
          prop: 'createTime',
          align: 'center',
          width: 140,
          resizable: false
        },
        {
          label: '附件',
          prop: 'fileCount',
          align: 'center',
          resizable: false,
          width: 60
        }
      ],
      tableData: [],
      total: 0,
      pageNo: 1,
      pageSize: 100,

      showCostDetail: false,
      detailData: {},

      showOrderDetail: false,
      orderDetail: {}
    };
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      this.$refs.searchBar.handleSearchClick();
    },
    handleCurrentChange(val) {
      this.$refs.searchBar.handleSearchClick();
    },
    handlePreviewWorkOrder(row) {
      this.ajax.workSheetInfo(row.workNumber).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '详情获取失败');
          return;
        }
        this.orderDetail = res.object;
        this.showOrderDetail = true;
      });
    },
    handlePreview(row) {
      this.ajax.workSheetInfo(row.workNumber).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '详情获取失败');
          return;
        }
        this.detailData = row;
        this.detailData.faultDeion = res.object.wsWsSheetInfoOutVo.faultDeion;
        this.showCostDetail = true;
      });
    },
    handleSortChange(prop) {
      this.sord = prop.order == 'ascending' ? 'asc' : 'desc';
      switch (prop.prop) {
        case 'costTime':
        default:
          this.sidx = 'create_time';
          break;
      }
      this.$refs.searchBar.handleSearchClick();
    }
  }
};
</script>

<style lang="scss" scoped>
.detail-span {
  cursor: pointer;
}
.total-cost-content {
  padding: $theme-interval 0;
  font-size: 16px;
  color: #ec7b25;
}
.table-pager {
  margin-top: $theme-interval;
}
/deep/ {
  .el-table__header-wrapper {
    position: fixed;
    z-index: 1;
  }
  .el-table__body-wrapper {
    margin-top: 30px;
  }
}
</style>
