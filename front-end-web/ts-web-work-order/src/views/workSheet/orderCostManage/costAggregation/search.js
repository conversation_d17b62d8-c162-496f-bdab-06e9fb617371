export default {
  data() {
    return {
      sidx: 'create_time',
      sord: 'desc',
      searchList: [
        {
          label: '工单处理科室',
          value: 'businessDeptId',
          element: 'el-select',
          elementProp: {
            placeholder: '请选择处理科室'
          },
          childNodeList: []
        },
        {
          label: '工单编号',
          value: 'workNumber',
          element: 'el-input',
          elementProp: {
            placeholder: '请输入工单编号'
          }
        },
        {
          label: '费用发生时间',
          value: 'costTimeList',
          element: 'el-date-picker',
          elementProp: {
            type: 'daterange',
            rangeSeparator: '-',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期'
          }
        },
        {
          label: '费用填报科室',
          value: 'fillDeptId',
          element: 'input-tree',
          elementProp: {
            placeholder: '请输入费用填报科室',
            treeData: []
          },
          event: {
            change: this.handleFillDeptChange
          }
        },
        {
          label: '费用填报人',
          value: 'fillUserId',
          element: 'input-select',
          elementCol: 14,
          elementProp: {
            placeholder: '请选择处理人',
            labelName: '${employeeName}-${orgName}',
            valueName: 'employeeId'
          },
          event: {
            load: this.handleLoadFillUser
          }
        },
        {
          label: '费用填报时间',
          value: 'createTimeList',
          element: 'el-date-picker',
          elementProp: {
            type: 'datetimerange',
            defaultTime: ['00:00:00', '23:59:59'],
            rangeSeparator: '-',
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期'
          }
        }
      ],
      totalMoney: '0.00'
    };
  },
  methods: {
    refresh() {
      this.getHandleDpetList();
      this.getDeptTreeData();
      let data = (this.$refs.searchBar && this.$refs.searchBar.form) || {};
      this.handleChange(data);
    },
    computSearchData(param = {}) {
      let data = JSON.parse(JSON.stringify(param));

      let [costBeiginTime, costEndTime] = data.costTimeList || [],
        [createBeiginTime, createEndTime] = data.createTimeList || [],
        searchData = {
          pageNo: this.pageNo,
          pageSize: this.pageSize,
          sidx: this.sidx,
          sord: this.sord
        };
      costBeiginTime
        ? (searchData.costBeiginTime = this.$dayjs(costBeiginTime).format(
            'YYYY-MM-DD'
          ))
        : null;
      costEndTime
        ? (searchData.costEndTime = this.$dayjs(costEndTime).format(
            'YYYY-MM-DD'
          ))
        : null;
      createBeiginTime
        ? (searchData.createBeiginTime = this.$dayjs(createBeiginTime).format(
            'YYYY-MM-DD'
          ))
        : null;
      createEndTime
        ? (searchData.createEndTime = this.$dayjs(createEndTime).format(
            'YYYY-MM-DD'
          ))
        : null;
      delete data.costTimeList;
      delete data.createTimeList;

      let keyList = Object.keys(data);
      keyList.forEach(key => {
        if (data[key]) {
          searchData[key] = data[key];
        }
      });
      searchData.workNumber
        ? (searchData.workNumber = searchData.workNumber.trim())
        : null;
      return searchData;
    },
    getHandleDpetList() {
      this.ajax.meauList().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '数据获取失败，请刷新重试');
          return;
        }
        let optionList = res.object.map(item => {
          return {
            element: 'el-option',
            label: item.deptName,
            value: item.deptId
          };
        });
        let index = this.searchList.findIndex(
            item => item.value == 'businessDeptId'
          ),
          businessItem = this.searchList[index];
        businessItem.childNodeList = optionList;
        this.$set(this.searchList, index, businessItem);
      });
    },
    getDeptTreeData() {
      this.ajax.getTree().then(res => {
        if (!res.success) {
          return;
        }
        let index = this.searchList.findIndex(
            item => item.value == 'fillDeptId'
          ),
          businessItem = this.searchList[index];
        businessItem.elementProp.treeData = res.object;
        this.$set(this.searchList, index, businessItem);
      });
    },
    //搜索点击事件
    handleChange(data = {}) {
      let searchData = this.computSearchData(data);
      this.ajax.getWorkOrderCostSum(searchData).then(res => {
        if (res.success == false) {
          return;
        }
        let moneyStr = (res.object || 0).toLocaleString();
        if (moneyStr.split('.').length > 1) {
          moneyStr.split('.')[1].length == 2 ? null : (moneyStr += '0');
        }
        moneyStr.split('.').length == 1 ? (moneyStr += '.00') : null;
        this.totalMoney = moneyStr;
      });
      this.ajax.getCostRigistList(searchData).then(res => {
        if (res.success == false) {
          return;
        }
        let tableData = [];
        res.rows.forEach((item, index) => {
          tableData.push({
            ...item,
            rowIndex: (this.pageNo - 1) * this.pageSize + index + 1
          });
        });

        this.tableData = tableData;
        this.total = res.totalCount;
        this.pageNo;
      });
    },
    //填报科室改变重置填报人
    handleFillDeptChange(item, event, dom) {
      this.$refs.searchBar.form.fillUserId = '';
    },
    //获取填报人选项
    handleLoadFillUser(searchVal, callback) {
      this.ajax
        .getEmployeePageList({
          pageNo: searchVal.pageNo,
          pageSize: 15,
          employeeName: searchVal.text
        })
        .then(res => {
          if (res.success == false) {
            callback('finished');
            return;
          }
          callback(res.rows);
        });
    },
    handleExport() {
      let searchData = this.computSearchData(this.$refs.searchBar.form),
        downloadStr = ['type=all'];
      Object.keys(searchData).forEach(item => {
        downloadStr.push(`${item}=${searchData[item]}`);
      });
      let aDom = document.createElement('a');
      aDom.href = '/ts-worksheet/exportExcel?' + downloadStr.join('&');
      aDom.click();
    }
  }
};
