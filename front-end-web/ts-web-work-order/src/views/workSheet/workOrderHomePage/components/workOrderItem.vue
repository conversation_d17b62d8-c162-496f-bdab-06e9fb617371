<template>
  <div class="work_order_box">
    <div class="title_top">
      <div class="top_font flex-col-center">
        <span class="marginRight">{{ workOrderType }}</span>
        <span>({{ num }})</span>
      </div>
      <span class="more flex-center" @click="moreClickHandle(type)">
        更多
        <img :src="moreIcon" />
      </span>
    </div>
    <div
      v-for="(item, index) in list"
      :key="index"
      @click="workOrderClickHandle(item)"
      class="work_order_item"
    >
      <div class="work_order_message">
        <div class="message_image">
          <img
            :src="item.fkUserUrl || require('@/assets/img/boy.png')"
            alt=""
          />
          <div class="message_status">
            <div
              class="type"
              v-if="type === '1'"
              style="color: #ff0000; background-color: #ffebeb;"
            >
              超期{{ item.cqDay }}天
            </div>
            <div
              class="type"
              v-if="type === '2'"
              style="color: #ff6600; background-color: #fff0e6;"
            >
              催办{{ item.counts }}次
            </div>
            <div
              class="type"
              v-if="type === '3'"
              style="color: #ff0000; background-color: #ffebeb;"
            >
              {{ item.comprehensiveScore }}分
            </div>
            <div
              class="type"
              v-if="type === '4'"
              :style="
                item.workStatusName == '已暂停'
                  ? { color: '#ff0000', backgroundColor: '#ffebeb' }
                  : { color: '#666', backgroundColor: '#eeeeee' }
              "
            >
              {{ item.workStatusName == '已暂停' ? '暂停' : '终止' }}
            </div>
            <div
              class="type"
              v-if="type === '5'"
              style="color: #25b700; background-color: #e6f5e2;"
            >
              打回{{ item.counts }}次
            </div>
            <div
              class="type"
              v-if="type === '6' && item.theAssistFlag == '1'"
              style="color: #25b700; background-color: #e6f5e2;"
            >
              已协助
            </div>
          </div>
        </div>
        <div class="message_font">
          <span>
            <span :title="item.fkUserDeptName" class="hint">
              {{ item.fkUserDeptName }}:
              <span
                :title="item.fkUserName"
                class="hint content"
                style="margin: 0 5px;"
              >
                {{ item.fkUserName }}
              </span>
            </span>
            <!--要求时间 完成时间（超期、打回）-->
            <span
              v-if="type !== '5' && type !== '6'"
              class="time hint"
              :title="item.requiredCompletionTime"
            >
              {{ item.requiredCompletionTime ? '要求时间' : '' }}
              <span class="content">{{ item.requiredCompletionTime }}</span>
            </span>

            <span
              v-else-if="type === '6'"
              class="time hint"
              :title="item.requiredCompletionTime"
            >
              {{ item.requiredCompletionTime ? '要求协助时间' : '' }}
              <span class="content">{{ item.requiredCompletionTime }}</span>
            </span>
            <span v-else class="time hint" :title="item.actualCompletionTime">
              {{ item.actualCompletionTime ? '完成时间' : '' }}
              <span class="content">{{ item.requiredCompletionTime }}</span>
            </span>
          </span>

          <!--故障描述-->
          <span class="flex">
            <span style="white-space: nowrap;">工单：</span>
            <IndentSpan
              :content="item.faultDeion"
              class="content fault-desion flex-grow flex-col-center firstText"
            ></IndentSpan>
          </span>
          <!--来源 最新更新（超期工单、催办工单）-->
          <span
            v-if="type !== '2'"
            class="form hint"
            :title="
              item.repairManDeptName +
                '-' +
                item.repairManName +
                item.createTime
            "
            >来源：
            <span class="content">
              {{ item.repairManDeptName }}-{{ item.repairManName }}
              {{ item.createTime }}
            </span>
          </span>
          <span v-else class="form hint" :title="item.taskCreateTime">
            最新更新:
            <span class="content">{{ item.taskCreateTime }}</span>
          </span>
          <!--接单 催办 办结 （超期工单、催办工单、差评工单）-->
          <span
            v-if="type === '1'"
            class="start_time hint"
            :title="item.taskCreateTime"
            >接单:
            <span class="content">{{ item.taskCreateTime }}</span>
          </span>
          <span
            v-if="type === '2'"
            class="start_time hint"
            :title="item.operatingTime"
          >
            最近催办:
            <span class="content">{{ item.operatingTime }}</span>
          </span>
          <span
            v-if="type === '3'"
            class="start_time hint"
            :title="item.actualCompletionTime"
          >
            办结:
            <span class="content">{{ item.actualCompletionTime }}</span>
          </span>

          <!--暂停原因、终止原因、打回原因-->
          <IndentSpan
            v-if="type === '7' || type === '4' || type === '5'"
            class="info"
            :content="item.takeRemark"
          ></IndentSpan>
        </div>
      </div>
    </div>
    <div v-if="num == 0" class="flex-center flex-grow">
      <div v-if="type != 3" class="empty-style flex-center">
        <img src="@/assets/img/workSheet/workOrderHomePage/empty-box.png" />
        <div class="toast">
          {{
            ['4', '7'].indexOf(type) == -1
              ? `今日没有${workOrderType}`
              : { '4': '今日没有终止工单', '7': '今日没有暂停工单' }[type]
          }}
          ~
        </div>
      </div>
      <div v-else class="chaping-order-empty flex-center">
        <img
          src="@/assets/img/workSheet/workOrderHomePage/icon_noChaPing.png"
        />
      </div>
    </div>
  </div>
</template>

<script>
import dictionaries from '@/views/workSheet/workOrderHomePage/publicData/dictionaries';
import IndentSpan from '@/components/indent-span';
export default {
  name: 'workOrderItem',
  components: {
    IndentSpan
  },
  props: {
    type: {
      type: String,
      default: () => ''
    },
    num: {
      type: [Number, String],
      default: () => 1
    },
    workOrderClickHandle: {
      type: Function,
      default: () => {}
    },
    moreClickHandle: {
      type: Function,
      default: () => {}
    },
    list: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    workOrderType() {
      return dictionaries.workOrder[this.type];
    }
  },
  data() {
    return {
      moreIcon: require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>')
    };
  }
};
</script>

<style lang="scss" scoped>
.work_order_box {
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  padding: 8px;
  min-height: 260px;
  background: #ffffff;
  box-shadow: 0px 2px 4px 0px #e6eaf0;
  border-radius: 4px;
  .hint {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  .title_top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .top_font {
      &,
      span {
        font-weight: 600;
        color: #333333;
        line-height: 22px;
        font-size: 14px;
        white-space: nowrap;
      }
      .marginRight {
        margin-right: 8px;
      }
      &:before {
        content: '';
        background-color: #5260ff;
        margin-right: 4px;
        height: 16px;
        width: 4px;
      }
    }
    .more {
      font-size: 14px;
      color: #666666;
      line-height: 20px;
      white-space: nowrap;
      img {
        height: 16px;
        margin-left: 4px;
      }
      &:hover {
        cursor: pointer;
      }
    }
  }
  .work_order_item {
    margin-left: 8px;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    min-height: 68px;
    &:hover {
      cursor: pointer;
    }
    .work_order_message {
      display: flex;
      overflow: hidden;
      .message_image {
        display: flex;
        flex-direction: column;
        align-items: center;
        text-align: center;
        flex-shrink: 0;
        img {
          width: 40px;
          height: 40px;
          border-radius: 50%;
          margin-bottom: 8px;
        }
      }
      .message_font {
        margin-left: 10px;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        span {
          line-height: 22px;
        }
        .content {
          color: #333;
          line-height: 22px;
          font-size: 14px;
        }
        > span {
          color: #666666;
          font-size: 14px;
          &.info {
            color: #f45555;
          }
          &.time,
          &.content {
            color: #000;
          }
          &.content {
            font-size: 16px;
          }
          &:last-child {
            margin-bottom: 0px;
          }
        }
        .fault-desion {
          color: #5260ff !important;
          font-weight: 600;
          white-space: nowrap;
        }
      }
    }
    .message_status {
      border-radius: 2px;
      .type {
        padding: 3px 6px;
        min-width: 56px;
        text-align: center;
        white-space: nowrap;
        line-height: 18px;
        font-size: 12px;
      }
    }
  }
  .have-no {
    text-align: center;
    margin-top: 50px;
  }
}
.empty-style {
  position: relative;
  img {
    width: 165px;
  }
  .toast {
    color: #999999;
    line-height: 17px;
    position: absolute;
    top: calc(50% + 45px);
    font-size: 12px;
    z-index: 9;
  }
}
.chaping-order-empty {
  position: relative;
  &::after {
    content: '今天优秀，大家都很满意～';
    color: #999999;
    line-height: 17px;
    position: absolute;
    top: calc(50% + 57px);
    font-size: 12px;
    z-index: 9;
  }
  img {
    width: 234px;
  }
}
</style>
