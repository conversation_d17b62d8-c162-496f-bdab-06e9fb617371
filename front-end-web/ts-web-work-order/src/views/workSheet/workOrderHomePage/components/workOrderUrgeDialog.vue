<template>
  <el-dialog
    title="催办"
    :visible.sync="value"
    width="400px"
    :before-close="handleClose"
  >
    <div class="urge-box flex-center">
      <div v-if="urgeTitle.length">
        本工单共催办
        <span style="color: #F45555;">{{ urgeTitle[0] }}次</span>
        ，上次催办时间{{ urgeTitle[1] }}
      </div>
      <div v-else>暂无催办信息</div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="urgeHandle" class="trasen-perpul">催办</el-button>
      <el-button @click="handleClose">取消</el-button>
    </span>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/workOrderHomePage';
export default {
  name: 'workOrderUrgeDialog',
  model: {
    event: 'change',
    prop: 'value'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    pkWsTaskId: {
      type: String,
      default: () => ''
    },
    urgeTitle: {
      type: Array,
      default: () => []
    },
    workOrderInfo: {
      type: Object,
      default: () => {}
    }
  },
  methods: {
    async urgeHandle() {
      const data = {
        fkWsTaskId: this.pkWsTaskId,
        workNumber: this.workOrderInfo.wsWsSheetInfoOutVo.workNumber
      };
      try {
        const res = await api.workSheetHasten(data);
        if (!res.success) {
          throw res.message;
        }

        if (res.success && res.statusCode === 200) {
          this.$message.success('催办成功');
          this.$emit(
            'refresh',
            this.workOrderInfo.wsWsSheetInfoOutVo.workNumber
          );
        }
      } catch (e) {
        this.$message.error(e);
      }
    },
    handleClose() {
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.urge-box {
  min-height: 75px;
}
</style>
