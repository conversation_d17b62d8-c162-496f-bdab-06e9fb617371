<template>
  <el-dialog
    :title="title"
    :visible.sync="value"
    width="900px"
    :before-close="handleClose"
  >
    <div class="button-box">
      <el-button size="mini" type="warning" @click="exportHandle"
        >导出</el-button
      >
    </div>
    <el-table ref="table" :data="obj.rows" border style="margin: 10px 0">
      <el-table-column
        prop="fkUserName"
        label="处理人姓名"
        width="100"
        show-overflow-tooltip
      />
      <el-table-column
        prop="fkUserDeptName"
        label="处理人科室名称"
        width="150"
        show-overflow-tooltip
      />
      <!--要求时间 完成时间（超期、打回）-->
      <el-table-column
        v-if="type !== '5'"
        prop="requiredCompletionTime"
        label="要求时间"
        width="150"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        v-else
        prop="actualCompletionTime"
        label="完成时间"
        width="150"
        align="center"
        show-overflow-tooltip
      />
      <!--故障描述-->
      <el-table-column prop="faultDeion" label="故障描述" show-overflow-tooltip>
        <template slot-scope="scope">
          <span class="faultDeion" @click="itemGoOrderInfo(scope.row)">{{
            scope.row.faultDeion
          }}</span>
        </template>
      </el-table-column>
      <!--来源 最新更新（超期工单、催办工单）-->
      <el-table-column
        v-if="type !== '2'"
        prop="requiredCompletionTime"
        label="来源"
        show-overflow-tooltip
      >
        <template slot-scope="scope">
          <div>
            {{ scope.row.repairManDeptName }}-{{ scope.row.repairManName }}
            {{ scope.row.createTime }}
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-else
        prop="taskCreateTime"
        label="最新更新"
        show-overflow-tooltip
      />
      <!--接单 催办 办结 （超期工单、催办工单、差评工单）-->
      <el-table-column
        v-if="type === '1'"
        prop="taskCreateTime"
        label="接单时间"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        v-else-if="type === '2'"
        prop="operatingTime"
        label="最近催办"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        v-else-if="type === '3'"
        prop="actualCompletionTime"
        label="办结"
        width="150"
        align="right"
        show-overflow-tooltip
      />
      <!--暂停原因、终止原因、打回原因-->
      <el-table-column
        v-if="type === '7' || type === '4' || type === '5'"
        prop="takeRemark"
        :label="
          type === '7' ? '暂停原因' : type === '4' ? '终止原因' : '打回原因'
        "
        width="150"
        show-overflow-tooltip
      />
      <!--超期天数、催办次数、综合评分、工单状态、打回次数-->
      <el-table-column
        v-if="type === '1'"
        prop="cqDay"
        label="超期天数"
        width="100"
        align="right"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="type === '2'"
        prop="counts"
        label="催办次数"
        width="100"
        align="right"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="type === '3'"
        prop="comprehensiveScore"
        label="综合评分"
        width="100"
        align="right"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="type === '4'"
        prop="workStatusName"
        label="工单状态"
        width="100"
        align="center"
        show-overflow-tooltip
      />
      <el-table-column
        v-if="type === '5'"
        prop="counts"
        label="打回次数"
        width="100"
        align="right"
        show-overflow-tooltip
      />
    </el-table>
    <div class="pagination">
      <el-pagination
        :total="pagination.total"
        :page-size="pagination.pageSize"
        :current-page="pagination.pageNo"
        :page-sizes="[5, 10, 20, 30]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      >
      </el-pagination>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关 闭</el-button>
    </div>

    <DataMessageComponent
      v-if="orderInfoDialog"
      v-model="orderInfoDialog"
      :messageInfo="messageInfoData"
      :appendToBody="true"
    />
  </el-dialog>
</template>

<script>
import dictionaries from '@/views/workSheet/workOrderHomePage/publicData/dictionaries';
import api from '@/api/ajax/workOrderHomePage';
import DataMessageComponent from '../components/DataMessageComponent';

export default {
  components: {
    DataMessageComponent
  },
  model: {
    event: 'change',
    prop: 'value'
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    obj: {
      type: Object,
      default: () => []
    },
    type: {
      type: String,
      default: () => ''
    }
  },
  data: () => ({
    pagination: {
      pageSize: undefined,
      pageNo: undefined,
      total: undefined
    },
    messageInfoData: {},
    orderInfoDialog: false
  }),
  watch: {
    obj: {
      handler(val) {
        this.pagination.pageSize = val.pageSize;
        this.pagination.pageNo = val.pageNo;
        this.pagination.total = val.totalCount;
      }
    }
  },
  computed: {
    title() {
      return '更多' + dictionaries.workOrder[this.type] + '列表';
    }
  },
  methods: {
    // pageSize分页事件
    handleSizeChange(pageSize) {
      this.pagination.pageSize = pageSize;
      this.$emit('changePageSizeOrPageNo', {
        type: this.type,
        pageSize,
        pageNo: this.pagination.pageNo
      });
    },
    // pageNo分页事件
    handleCurrentChange(pageNo) {
      this.pagination.pageNo = pageNo;
      this.$emit('changePageSizeOrPageNo', {
        type: this.type,
        pageNo,
        pageSize: this.pagination.pageSize
      });
    },
    async itemGoOrderInfo(row) {
      try {
        const { object } = await api.workSheetInfo(row.workNumber);
        this.messageInfoData = object;
        this.orderInfoDialog = true;
      } catch (e) {}
    },
    // 导出
    exportHandle() {
      if (this.obj.rows.length === 0) {
        return;
      }
      let cols = this.$refs.table.columns.map(item => {
        return {
          label: item.label,
          prop: item.property
        };
      });
      let newTable = document.createElement('table'),
        newHead = document.createElement('tr');
      cols.forEach(item => {
        let th = document.createElement('th');
        th.innerHTML = item.label;
        newHead.append(th);
      });
      newTable.append(newHead);
      this.obj.rows.forEach(item => {
        let tr = document.createElement('tr');
        cols.forEach(col => {
          let td = document.createElement('td');
          td.innerHTML = item[col.prop];
          tr.append(td);
        });
        newTable.append(tr);
      });
      let _this = this;

      var tableToExcel = (function() {
        var uri = 'data:application/vnd.ms-excel;base64,',
          template =
            '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
          base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)));
          },
          // 下面这段函数作用是：将template中的变量替换为页面内容ctx获取到的值
          format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
              return c[p];
            });
          };
        return function(table, name) {
          // 获取表单的名字和表单查询的内容
          var ctx = { worksheet: name || 'Worksheet', table: table.innerHTML };
          // format()函数：通过格式操作使任意类型的数据转换成一个字符串
          // base64()：进行编码
          let a = document.createElement('a');
          a.href = uri + base64(format(template, ctx));
          a.download = dictionaries.workOrder[_this.type] + '表格.xls'; //设置被下载的超链接目标（文件名）
          a.click();
        };
      })();

      tableToExcel(newTable);
    },
    handleClose() {
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.button-box {
  text-align: right;
}
.pagination {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}
.faultDeion {
  color: #00a0e9;
  &:hover {
    cursor: pointer;
  }
}
</style>
