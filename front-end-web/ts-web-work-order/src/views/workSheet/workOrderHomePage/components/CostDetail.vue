<template>
  <div class="content">
    <div class="cost-top flex-row-between flex-col-end">
      <div>填报经费共计 {{ allCostCount }} 元</div>
      <div class="flex">
        <el-button
          v-if="['2', '3', '4', '5'].indexOf(workOrderInfo.peopleType) >= 0"
          class="trasen-perpul"
          @click="handleOpenEditModal()"
        >
          新增
        </el-button>
        <el-button @click="handleExport">导出</el-button>
      </div>
    </div>
    <div style="height: calc(100% - 40px); display: flex; overflow: hidden;">
      <base-table
        ref="table"
        border
        stripe
        :pager="false"
        :columns="columns"
        @refresh="refresh"
        :default-sort="{ order: 'descending', prop: 'costTime' }"
        @sort-change="handleSortChange"
      >
        <template slot="action">
          <i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>
        </template>
      </base-table>
    </div>

    <CostEditModal
      :showCostEdit="showCostEdit"
      :workOrderDetail="workOrderInfo"
      :editData="editCostData"
      @refresh="handleRefreshCostList"
      @close="showCostEdit = false"
    ></CostEditModal>
    <CostDetail
      :visible="showCostDetail"
      :data="detailData"
      @close="showCostDetail = false"
    ></CostDetail>
  </div>
</template>

<script>
import CostEditModal from '../../comment/components/costRegister/costRegister.vue';
import CostDetail from '../../comment/components/costRegister/costDetail.vue';

export default {
  components: {
    CostEditModal,
    CostDetail
  },
  props: {
    workOrderInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      allCostCount: '0.00',
      sidx: 'create_time',
      sord: 'desc',
      columns: [
        {
          label: '',
          prop: 'rowIndex',
          align: 'center',
          width: 35,
          resizable: false
        },
        {
          label: '金额(元)',
          prop: 'money',
          align: 'left',
          width: 100,
          resizable: false,
          formatter: function(rowObject, options, cellvalue) {
            let labelStr = cellvalue.toLocaleString();
            if (labelStr.split('.').length > 1) {
              labelStr.split('.')[1].length == 1 ? (labelStr += '0') : null;
            }
            labelStr.split('.').length == 1 ? (labelStr += '.00') : null;
            return labelStr;
          }
        },
        {
          label: '费用描述',
          prop: 'costDeion',
          resizable: false,
          formatter: (rowObject, options, cellvalue) => {
            return (
              <p class="detail-span">
                <span
                  class="deal-link"
                  onclick={this.handlePreview.bind(this, rowObject)}>
                  {cellvalue}
                </span>
              </p>
            );
          }
        },
        {
          label: '发生时间',
          prop: 'costTime',
          align: 'center',
          sortable: 'custom',
          sortOrders: ['ascending', 'descending'],
          resizable: false,
          width: 140
        },
        {
          label: '填报科室',
          prop: 'fillDeptName',
          resizable: false,
          width: 110
        },
        {
          label: '填报人',
          prop: 'fillUser',
          resizable: false,
          width: 80
        },
        {
          label: '填报时间',
          prop: 'createTime',
          align: 'center',
          width: 140,
          resizable: false
        },
        {
          label: '附件',
          prop: 'fileCount',
          align: 'center',
          resizable: false,
          width: 60
        },
        {
          headerSlots: 'action',
          width: 90,
          resizable: false,
          formatter: this.computedAction
        }
      ],
      tableData: [],

      showCostEdit: false,
      editCostData: {},

      showCostDetail: false,
      detailData: {}
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.refresh();
    });
  },
  methods: {
    async refresh() {
      let res = await this.ajax.getCostRigistList({
        pageSize: 9999,
        pageNo: 1,
        workNumber: this.workOrderInfo.workNumber,
        sidx: this.sidx,
        sord: this.sord
      });

      if (res.success == false) {
        this.$message.error(res.message || '费用明细信息获取失败');
        return {};
      }
      let tableData = [],
        allMoney = 0;
      res.rows.forEach((item, index) => {
        tableData.push({ ...item, rowIndex: index + 1 });
        allMoney += item.money;
      });
      let moneyStr = allMoney.toLocaleString();
      if (moneyStr.split('.').length > 1) {
        moneyStr.split('.')[1].length == 2 ? null : (moneyStr += '0');
      }
      moneyStr.split('.').length == 1 ? (moneyStr += '.00') : null;

      this.allCostCount = moneyStr;
      this.tableData = tableData;
      this.$refs.table.refresh({
        rows: tableData
      });
    },
    computedAction(row, option, cellValue) {
      let nowUserCode = this.$store.state.common.userInfo.employeeId;
      if (row.fillUserId == nowUserCode) {
        return (
          <span>
            <span
              class="table-action-item"
              onclick={this.handleOpenEditModal.bind(this, row)}>
              编辑
            </span>
            <span
              class="table-action-item"
              style="margin-left: 8px;"
              onclick={this.handleDelete.bind(this, row)}>
              删除
            </span>
          </span>
        );
      }
      return '';
    },
    handleOpenEditModal(data = {}) {
      this.showCostEdit = true;
      this.editCostData = data;
    },
    handleDelete(row) {
      this.$confirm('确定删除？', '提示').then(() => {
        this.ajax.deleteCostData([row.pkWsCostId]).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '删除失败');
            return;
          }
          this.$message.success('删除成功');
          this.refresh();
        });
      });
    },
    handleRefreshCostList() {
      this.showCostEdit = false;
      this.refresh();
    },
    handleExport() {
      let aDom = document.createElement('a');
      aDom.href = '/ts-worksheet/exportExcel/' + this.workOrderInfo.workNumber;
      aDom.click();
    },
    handlePreview(row) {
      this.showCostDetail = true;
      this.detailData = { ...row, faultDeion: this.workOrderInfo.faultDeion };
    },
    handleSortChange(prop) {
      this.sord = prop.order == 'ascending' ? 'asc' : 'desc';
      switch (prop.prop) {
        case 'costTime':
        default:
          this.sidx = 'create_time';
          break;
      }
      this.refresh();
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
}
.cost-top {
  margin-bottom: $theme-interval;
  > div:first-child {
    font-size: 16px;
    color: #ec7b25;
  }
}
/deep/ {
  .el-table__body-wrapper {
    margin-top: 30px !important;
    tbody .el-table__row td:first-child .cell {
      padding: 0 !important;
      min-width: unset;
    }
  }
  .table-action-item {
    cursor: pointer;
    color: $theme-color;
    &:hover {
      opacity: 0.8;
    }
  }
}
/deep/.detail-span {
  cursor: pointer;
}
/deep/.el-table .cell.el-tooltip {
  min-width: unset;
}
</style>
