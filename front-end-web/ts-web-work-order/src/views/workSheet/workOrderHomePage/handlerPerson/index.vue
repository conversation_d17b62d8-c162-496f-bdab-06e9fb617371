<template>
  <div ref="contentBody" class="handler_box">
    <el-scrollbar
      style="width: 100%; height: 100%;"
      wrapClass="content-scroll-wrap"
      viewClass="content-scroll-view flex-column"
    >
      <!-- 我的工单 -->
      <!-- <div class="my-report flex">
        <div class="top_title">我的报修</div>
        <div class="flex-row-between" style="width: calc(100% - 111px);">
          <div
            class="my-report-statu-list-item flex-center"
            v-for="(item, index) of myReportIconList"
            :key="index"
          >
            <img :src="item" />
            <span>待完成</span>
            <span>{{ index == 1 ? 12 : 2 }}</span>
          </div>
        </div>
      </div> -->
      <!--搜索栏-->
      <!-- <div v-if="isAdmin" class="search">
        <div class="name_list">
          <div @click="transformHandle('left')" class="left">
            <img src="@/assets/img/workSheet/workOrderHomePage/icon_left.png" />
          </div>
          <div class="name_content">
            <div class="real_content">
              <span
                v-for="item in personList"
                :key="item.employeeId"
                class="name_item"
                @click="itemClickPersonHandle(item.employeeId)"
              >
                <span :class="{ active: nameActive === item.employeeId }">{{
                  item.employeeName
                }}</span>
              </span>
            </div>
          </div>
          <div @click="transformHandle('right')" class="right">
            <img
              src="@/assets/img/workSheet/workOrderHomePage/icon_right.png"
            />
          </div>
        </div>

        <div class="search_input">
          <el-input placeholder="搜索人员、部门" v-model="searchName">
            <div class="search-icon" slot="prefix">
              <img
                :src="
                  require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>')
                "
              />
            </div>
            <div slot="suffix" style="display:flex;">
              <i
                v-show="searchName.length > 0"
                class="el-input__icon el-icon-close clear-icon"
                @click="clearHandle"
              ></i>
            </div>
          </el-input>
          <el-button @click="searchPersonHandle">搜索</el-button>
        </div>
      </div> -->
      <!--for workOrder-->
      <div class="workOrder_data">
        <!--工单总览-->
        <div class="overview">
          <div class="top_title">工单总览</div>
          <div class="data_box flex-row-between">
            <div
              v-for="(item, index) in workOrderStatusList"
              :key="index"
              class="flex go-router"
              @click="goOldProjectHandle(item.work_status)"
            >
              <img :src="workOrderStatusIcon[index]" />
              <div>
                <div class="data_title">
                  {{ workOrderStatus[item.work_status] }}
                </div>
                <div class="data_num">{{ item.counts }}</div>
              </div>
            </div>
          </div>
        </div>
        <!--工单评分-->
        <div class="score">
          <div class="top_title">工单处理评分</div>
          <div class="flex-row-center flex-column" style="height: 80px;">
            <div class="flex-col-center five-star-content">
              <div class="data_title">{{ (scoreData[0] || {}).label }}</div>
              <div class="five-star">
                <img
                  :src="
                    require('@/assets/img/workSheet/workOrderHomePage/empty-star.png')
                  "
                />
                <div
                  class="full-score"
                  :style="{ width: computedWidth((scoreData[0] || {}).val) }"
                >
                  <img
                    :src="
                      require('@/assets/img/workSheet/workOrderHomePage/five-star.png')
                    "
                  />
                </div>
              </div>
              <div class="data_num">{{ (scoreData[0] || {}).val }}</div>
            </div>

            <div class="flex other-score">
              <div class="flex-col-center">
                <div class="data_title">{{ (scoreData[1] || {}).label }}</div>
                <div class="data_num">{{ (scoreData[1] || {}).val }}</div>
              </div>
              <div class="flex-col-center">
                <div class="data_title">{{ (scoreData[2] || {}).label }}</div>
                <div class="data_num">{{ (scoreData[2] || {}).val }}</div>
              </div>
              <div class="flex-col-center">
                <div class="data_title">{{ (scoreData[3] || {}).label }}</div>
                <div class="data_num">{{ (scoreData[3] || {}).val }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--    工单列表-->
      <div class="type_work_order flex-grow">
        <el-scrollbar
          style="width: 100%; height: 100%;"
          wrapClass="content-scroll-wrap"
          viewClass="scroll-content flex-column"
        >
          <div class="type_col bottom">
            <!-- 等待协助 -->
            <div class="item">
              <workOrderItem
                type="6"
                :num="assist"
                :list="assitWorkOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
            <!-- 打回工单 -->
            <div class="item margin">
              <workOrderItem
                type="5"
                :num="dh"
                :list="dhWorkOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
            <!-- 超期工单 -->
            <div class="item">
              <workOrderItem
                ref="workOrderBoxTemplate"
                type="1"
                :num="cq"
                :list="cqWorkOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
          </div>
          <div class="type_col">
            <!-- 催办工单 -->
            <div class="item">
              <workOrderItem
                type="2"
                :num="cb"
                :list="cbWorkOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
            <!-- 今日终止 -->
            <div class="item margin">
              <workOrderItem
                type="4"
                :num="zzzz"
                :list="zzzzWorkOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
            <!-- 暂停工单 -->
            <div class="item">
              <workOrderItem
                type="7"
                :num="ztgd"
                :list="ztgdWordOrderList"
                :workOrderClickHandle="workOrderClickHandle"
                :moreClickHandle="moreClickHandle"
                class="card"
              />
            </div>
          </div>
        </el-scrollbar>
      </div>

      <data-message-component
        v-if="DataMessageComponent"
        v-model="DataMessageComponent"
        :messageInfo="messageInfoData"
      ></data-message-component>
      <data-table-component
        v-model="itemTableDataDialog"
        :obj="tableWorkOrderObj"
        :type="typeList"
        @changePageSizeOrPageNo="changePageSizeOrPageNo"
      ></data-table-component>
    </el-scrollbar>
  </div>
</template>

<script>
import workOrderItem from '../components/workOrderItem';
import api from '@/api/ajax/workOrderHomePage';
import moreApi from '@/views/workSheet/workOrderHomePage/Minix/moreApi';
import data from '@/views/workSheet/workOrderHomePage/Minix/data';
import DataMessageComponent from '../components/DataMessageComponent';
import DataTableComponent from '../components/DataTableComponent';

export default {
  mixins: [moreApi, data],
  components: {
    workOrderItem,
    DataMessageComponent,
    DataTableComponent
  },
  data: () => ({
    personList: [], // 科室人员
    pageData: {
      pageSize: 9,
      pageNo: 1,
      total: undefined
    }, // 科室人员分页数据
    searchPageSize: 2, //搜索的页面大小
    nameActive: '2',

    searchName: '',

    myReportIconList: [
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_daipaidan.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_daijiedan.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_chulizhong.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_daiqueren.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_daipingjia.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_yizanting.png'),
      require('@/assets/img/workSheet/workOrderHomePage/icon_my_report_yizhongzhi.png')
    ],

    workOrderStatusList: [], //工单状态数量总览
    workOrderStatusIcon: [
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>')
    ],

    scoreData: [], // 工单评分

    // 超期工单及列表
    cq: '',
    cqWorkOrderList: [],

    // 今日催办工单数量及列表
    cb: '',
    cbWorkOrderList: [],

    // 今日终止工单数量及列表
    zzzz: '',
    zzzzWorkOrderList: [],

    // 协助工单数量及列表
    assist: '',
    assitWorkOrderList: [],

    // 打回工单数量及列表
    dh: '',
    dhWorkOrderList: [],

    //暂停工单数量及列表
    ztgd: '',
    ztgdWordOrderList: [],

    messageInfoData: {},
    DataMessageComponent: false,
    tableWorkOrderObj: {},
    typeList: undefined,
    itemTableDataDialog: false
  }),
  watch: {
    nameActive(val) {
      const params = {};
      if (val === '2') {
        params.type = val;
        params.fkUserDeptId = this.deptId;
      } else {
        params.type = '4';
        params.fkUserId = val;
      }

      this.getCountGroupByWorkStatusHandle();
      this.initWorkOrderList(params);
    }
  },
  mounted() {
    // let bodyWidth = this.$refs.contentBody.offsetWidth;
  },
  methods: {
    async refresh() {
      await new Promise((resolve, reject) => {
        setTimeout(resolve, 500);
      });
      let templateHeight =
        this.$refs.workOrderBoxTemplate.$el.clientHeight - 16 - 22;
      this.searchPageSize = parseInt(templateHeight / 104);
      const params = {
        type: '4',
        fkUserId: this.fkUserId
      };
      // if (this.isAdmin) {
      //   // 是否为管理员 是 则查询所有科室下的异常工单
      //   //            否 查询当前用户 （个人）的异常工单
      //   this.getPersonHandle(this.pageData);
      //   params.type = '2';
      //   params.fkUserDeptId = this.deptId;
      // } else {
      //   params.type = '4';
      //   params.fkUserId = this.fkUserId;
      // }
      this.initWorkOrderList(params);
      this.getCountGroupByWorkStatusHandle();
      this.getScoreHandle();
    },
    async initWorkOrderList(params) {
      await this.getWorkOrderDataNum(params);
      //超期工单
      this.getTypeWorkOrderListHandle(params, 'getExceedTimeWorkSheets').then(
        (res = {}) => {
          this.cqWorkOrderList = res.rows || [];
        }
      );
      // 催办工单
      this.getTypeWorkOrderListHandle(params, 'getHastenWorkSheets').then(
        (res = {}) => {
          this.cbWorkOrderList = res.rows || [];
        }
      );
      // const { rows: rows3 } = await this.getTypeWorkOrderListHandle(
      //   params,
      //   'getSuspendTerminateSheets'
      // );

      // 协助工单
      this.getTypeWorkOrderListHandle(params, 'getAssistWorkOrder').then(
        (res = {}) => {
          this.assitWorkOrderList = res.rows || [];
        }
      );

      // 打回工单
      this.getTypeWorkOrderListHandle(params, 'getBackSheets').then(
        (res = {}) => {
          this.dhWorkOrderList = res.rows || [];
        }
      );

      //今日暂停
      this.getTypeWorkOrderListHandle(params, 'getTodaySuspendedSheets').then(
        (res = {}) => {
          this.ztgdWordOrderList = res.rows || [];
        }
      );
      // 今日终止
      this.getTypeWorkOrderListHandle(params, 'getTodayTerminationSheets').then(
        (res = {}) => {
          this.zzzzWorkOrderList = res.rows || [];
        }
      );
      // this.zzzzWorkOrderList = rows3;
    },
    // 获取科室人员数据
    async getPersonHandle(params, isSearch = false) {
      try {
        const data = {
          orgId: this.deptId,
          ...params,
          pageSize: params.pageNo == 1 ? 8 : 9
        };
        const {
          rows,
          totalCount,
          pageNo,
          pageSize
        } = await api.getEmployeePageList(data);
        if (!isSearch) {
          if (pageNo === 1) {
            rows.unshift({
              employeeId: '2',
              employeeName: '全部'
            });
          }
        }
        if (rows && rows.length === 0) {
          this.$message.info('未查询到人员数据');
          return;
        }
        if (this.nameActive == '2' && this.pageData.pageNo == 1) {
          this.nameActive = rows[0] && rows[0].employeeId;
        }
        this.personList = rows;
        this.pageData.total = totalCount;
        this.pageData.pageNo = pageNo;
        this.pageData.pageSize = pageSize;
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 科室人员分页
    async transformHandle(position) {
      const isLeft = position === 'left';
      if (isLeft && this.pageData.pageNo === 1) {
        this.$message.warning('已经是第一页数据!');
        return;
      }
      const num =
        this.pageData.total - this.pageData.pageNo * this.pageData.pageSize;
      if (num < 0 && !isLeft) {
        this.$message.warning('暂无更多数据!');
        return;
      }
      isLeft ? this.pageData.pageNo-- : this.pageData.pageNo++;
      await this.getPersonHandle(this.pageData);
    },
    // 工单综合评分
    async getScoreHandle() {
      try {
        const res = await api.getComprehensiveScoreOfWorkOrder({
          fkUserId: this.fkUserId
        });
        if (!res.success) throw res.message;
        let Dictionaries = {
          avgProcessSpeed: '处理速度',
          avgScore: '总得分',
          avgServiceAttituude: '服务态度',
          avgTechnicalLevel: '技术水平'
        };
        for (let item in res.object) {
          this.scoreData.push({
            label: Dictionaries[item],
            val: res.object[item]
          });
        }
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 处理人各状态工单数量
    async getCountGroupByWorkStatusHandle() {
      try {
        const params = {
          fkUserId: this.fkUserId,
          type: '4'
        };
        const res = await api.getCountGroupByWorkStatus(params);
        if (!res.success) throw res.message;
        this.workOrderStatusList = res.object.filter(item => {
          return item.work_status !== '1';
        });
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 异常工单各种状态统计
    async getWorkOrderDataNum(params) {
      try {
        const res = await api.getAbnormalWorkSheetStatisCounts(params);
        if (!res.success) throw res.message;
        this.cq = res.object.cq;
        this.cb = res.object.cb;
        this.zzzz = res.object.zz;
        this.assist = res.object.assist;
        this.dh = res.object.dh;
        this.ztgd = res.object.zt;
      } catch (error) {
        this.$message.error(error);
      }
    },

    async getTypeWorkOrderListHandle(params, functionKey) {
      try {
        const pageData = {
          pageNo: '1',
          pageSize: this.searchPageSize,
          ...params
        };

        const res = await api[functionKey](pageData);
        if (!res.success && res.statusCode === 999) throw res.message;
        return res;
      } catch (e) {
        this.$message.error(e);
        return {
          rows: []
        };
      }
    },
    // 搜索人员
    searchPersonHandle() {
      const params = {
        employeeName: this.searchName,
        ...this.pageData
      };
      let isSearch = this.searchName ? true : false;
      this.getPersonHandle(params, isSearch);
    },
    clearHandle() {
      this.searchName = '';
      const params = {
        employeeName: '',
        ...this.pageData
      };
      this.getPersonHandle(params);
    },
    itemClickPersonHandle(id) {
      this.nameActive = id;
    },
    // 查看工单详情
    async workOrderClickHandle(item) {
      try {
        const { object } = await api.workSheetInfo(item.workNumber);
        this.messageInfoData = object;
        this.DataMessageComponent = true;
      } catch (e) {}
    },
    // type 工单更多
    async moreClickHandle(type) {
      // const data = this.getParams();
      const data = {
        type: '4',
        fkUserId: this.fkUserId
      };
      let params = {
        pageNo: 1,
        pageSize: 10,
        ...data
      };

      this.typeList = type;
      this.tableWorkOrderObj = await this.typeWorkOrderRequestMethod(
        type,
        params
      );
      this.itemTableDataDialog = true;
    },

    // table 分页
    async changePageSizeOrPageNo(params) {
      const data = this.getParams();

      this.tableWorkOrderObj = await this.typeWorkOrderRequestMethod(
        params.type,
        {
          pageNo: params.pageNo,
          pageSize: params.pageSize,
          ...data
        }
      );
    },
    getParams() {
      const params = {};
      if (this.isAdmin && this.nameActive === '2') {
        params.type = '2';
        params.fkUserDeptId = this.deptId;
      } else {
        params.type = '4';
        params.fkUserId = !this.isAdmin ? this.fkUserId : this.nameActive;
      }

      return params;
    },
    goOldProjectHandle(type) {
      this.$jumpOpenDialog({
        path: '/myHandleOrder',
        type,
        isWorkOrder: true
      });
    },
    //计算五星宽度
    computedWidth(score) {
      let starWidth = score * 18,
        emptyWidth = Math.ceil(score) >= 1 ? (Math.ceil(score) - 1) * 10 : 0;

      return (starWidth + emptyWidth) / 1.3 + '%';
    }
  }
};
</script>

<style lang="scss" scoped>
.go-router {
  width: 114px;
  height: 80px;
  background: #ffffff;
  border-radius: 2px;
  border: 1px solid #e0e6f0;
  display: flex;
  align-items: center;
  justify-content: center;
  img {
    width: 28px;
    margin-right: 8px;
  }
  &:hover {
    cursor: pointer;
  }
  .data_title {
    font-weight: 400;
    color: #333;
    line-height: 22px;
    font-size: 14px;
    white-space: nowrap;
  }
  .data_num {
    font-weight: 600;
    color: #5260ff;
    line-height: 30px;
    font-size: 24px;
  }
}
.handler_box {
  // overflow: auto;
  height: 100%;
  background-color: transparent;
  .search {
    min-width: 1190px;
    height: 48px;
    padding: 10px 8px 10px 16px;
    display: flex;
    justify-content: space-between;
    border-radius: 4px;
    box-shadow: 0px 2px 4px 0px #e6eaf0;
    margin-bottom: 8px;
    background-color: #fff;
    .name_list {
      padding-right: 26px;
      flex: 1;
      display: flex;
      align-items: center;
      .left,
      .right {
        > img {
          width: 24px;
          height: 24px;
          margin-top: 4px;
          &:hover {
            cursor: pointer;
          }
        }
      }
      .name_content {
        min-width: 784px;
        height: 30px;
        overflow: hidden;
        position: relative;
        margin: 0 12px;
        .real_content {
          transition: all 0.5s;
          white-space: nowrap;
          display: flex;
          width: 784px;
        }
        .name_item {
          &:not(:last-child) {
            margin-right: 8px;
          }
          > span {
            display: inline-block;
            width: 80px;
            height: 30px;
            line-height: 30px;
            border-radius: 3px;
            border: 1px solid #dcdfe6;
            text-align: center;
            color: #666666;
            &.active {
              color: #5260ff;
              border: 1px solid #5260ff;
            }
            &:hover {
              cursor: pointer;
            }
          }
        }
      }
    }
    .search_input {
      width: 400px;
      display: flex;
      align-items: center;
      /deep/ .el-input__suffix {
        right: 0 !important;
      }
      /deep/ .el-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 68px;
        height: 30px;
        border: 0;
        background-color: #5260ff;
        color: #fff;
        margin-left: 8px;
        &:hover,
        &:active {
          opacity: 0.8;
        }
      }
      /deep/ .el-input {
        width: 200px;
        input {
          border-color: #e0e6f0;
        }
      }
    }
  }
  .workOrder_data {
    display: flex;
    border-radius: 5px;
    background-color: #fff;
    padding: 8px;
    margin-bottom: 6px;
    min-width: 1190px;

    .text_align {
      align-items: center;
      display: flex;
    }
    .data_box {
      display: flex;
      justify-content: space-around;
    }
    .overview {
      flex: 1;
      .top_title {
        margin-bottom: 10px;
      }
      .data_box {
        padding-left: 8px;
      }
      .go-router {
        width: 13%;
      }
      .data_box {
        justify-content: space-between;
      }
    }
    .score {
      padding-right: 12px;
      margin-left: 19px;
      .five-star-content {
        .data_title {
          color: #333333;
          line-height: 22px;
          font-size: 14px;
        }
        .data_num {
          color: #ff4846;
          line-height: 22px;
          font-size: 16px;
        }
        .five-star {
          height: 18px;
          margin: 0 17px;
          position: relative;
          img {
            height: 18px;
            width: 130px;
          }
        }
        .full-score {
          position: absolute;
          left: 0;
          top: 0;
          z-index: 2;
          overflow: hidden;
        }
      }
      .top_title {
        margin-bottom: 10px;
      }
      .other-score {
        margin-top: 12px;
        .data_num,
        .data_title {
          font-weight: 400;
          line-height: 20px;
        }
        .data_title {
          color: #666666;
          margin-right: 8px;
        }
        .data_num {
          color: #5260ff;
        }
        > div:not(:last-child) {
          margin-right: 19px;
        }
      }
    }
  }
  .type_work_order {
    height: calc(100% - 162px);
    min-width: 1190px;
    overflow: hidden;
    /deep/ .el-scrollbar__bar.is-vertical {
      right: 8px;
    }
    /deep/.scroll-content {
      height: 100%;
      .type_col {
        overflow-x: hidden;
        flex: 1;
      }
    }
    .type_col {
      display: flex;
      .item {
        overflow-x: hidden;
        display: flex;
        flex: 1;
        > div {
          flex: 1;
        }
        &.margin {
          margin: 0 8px;
        }
        &.none {
          visibility: hidden;
        }
        .card {
          min-width: 391px;
        }
      }
      &.bottom {
        margin-bottom: 8px;
      }
    }
  }
  .clear-icon {
    &:hover {
      cursor: pointer;
    }
  }
}
/deep/ .content-scroll-wrap {
  height: calc(100% + 17px);
}
/deep/ .content-scroll-view {
  height: 100%;
  padding-bottom: 8px;
}
.top_title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333333;
  line-height: 22px;
  font-size: 14px;
  &::before {
    content: '';
    height: 16px;
    width: 4px;
    background-color: #5260ff;
    margin-right: 3px;
  }
}
.my-report {
  min-width: 1190px;
  padding: 8px;
  padding-right: 24px;
  background-color: #fff;
  margin-bottom: 8px;
  height: 56px;
  box-shadow: 0px 2px 4px 0px #e6eaf0;
  border-radius: 4px;
  .top_title {
    margin-right: 40px;
    white-space: nowrap;
  }
  .my-report-statu-list-item {
    width: 12.25%;
    height: 40px;
    background: #f4f4f4;
    padding: 0 8px;
    border-radius: 4px;
    img {
      height: 24px;
      width: 24px;
    }
    span {
      white-space: nowrap;
    }
    span:first-of-type {
      margin: 0 8px;
      font-weight: 400;
      color: rgba(51, 51, 51, 0.7);
      line-height: 20px;
    }
    span:last-of-type {
      font-weight: bold;
      color: #5260ff;
      line-height: 28px;
      font-size: 24px;
    }
  }
}
.search-icon {
  position: absolute;
  top: 50%;
  left: 4px;
  transform: translate(0, -40%);
  img {
    height: 14px;
    width: 14px;
  }
}
</style>
