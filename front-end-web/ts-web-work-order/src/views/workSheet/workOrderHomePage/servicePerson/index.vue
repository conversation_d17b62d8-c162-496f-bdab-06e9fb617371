<template>
  <div class="service_box">
    <el-scrollbar
      style="width: 100%; height: 100%;"
      wrapClass="content-scroll-wrap"
      viewClass="content-scroll-view flex-column"
    >
      <!--搜索-->
      <div class="search_box" v-if="departmentArr.length">
        <el-radio-group class="radio_group" v-model="searchValue">
          <el-radio-button label="0" v-if="departmentArr.length > 1">
            全部
          </el-radio-button>
          <el-radio-button
            v-for="item in departmentArr"
            :key="item.deptId"
            :label="item.deptId"
            >{{ item.deptName }}</el-radio-button
          >
          <!-- <el-radio-button label="3">第三方公司</el-radio-button> -->
        </el-radio-group>
      </div>

      <div class="service_data_num">
        <!--代办-->
        <div class="agency flex-column">
          <div class="top_title">待办事项</div>
          <div class="data_box">
            <div class="flex-column">
              <div class="data_title">待派单</div>
              <div class="data_num">
                {{ dpd || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
            <div class="flex-column">
              <div class="data_title">未接来电</div>
              <div class="data_num">
                {{ wj || 0 }}<span class="data_unit">(个)</span>
              </div>
            </div>
          </div>
        </div>

        <!--工作量-->
        <div class="workload flex-column">
          <div class="top_title">工作量分析</div>
          <div class="data_box">
            <div class="flex-column">
              <div class="data_title">今天接听来电</div>
              <div class="data_num">
                {{ dayyj || 0 }}<span class="data_unit">(个)</span>
              </div>
            </div>
            <div class="flex-column">
              <div class="data_title">今日呼出</div>
              <div class="data_num">
                {{ dayhb || 0 }}<span class="data_unit">(个)</span>
              </div>
            </div>
            <div class="flex-column">
              <div class="data_title">今日服务台提单</div>
              <div class="data_num">
                {{ dayfwtjd || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
            <!-- <div class="flex-column">
              <div class="data_title">今日派单</div>
              <div class="data_num">
                {{ daypd || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div> -->
            <div class="flex-column">
              <div class="data_title">今日电话解决</div>
              <div class="data_num">
                {{ daydhyjj || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
          </div>
        </div>

        <!--总体情况-->
        <div class="General flex-column">
          <div class="top_title">总体情况</div>
          <div class="data_box">
            <div class="flex-column">
              <div class="data_title">今日总提单</div>
              <div class="data_num">
                {{ dayztd || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
            <div class="flex-column">
              <div class="data_title">今日办结</div>
              <div class="data_num">
                {{ dayzbj || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
            <div class="flex-column">
              <div class="data_title">未办结工单</div>
              <div class="data_num">
                {{ wbj || 0 }}<span class="data_unit">(单)</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!--工单列表-->
      <div class="work_order_box_parent flex-grow">
        <el-scrollbar
          style="width: 100%; height: 100%;"
          class="inner-scroll-bar"
          wrapClass="content-scroll-wrap"
          viewClass="flex scroll-view"
        >
          <div class="one_item flex-column">
            <workOrderItem
              ref="workOrderBoxTemplate"
              :workOrderClickHandle="workOrderClickHandle"
              :moreClickHandle="moreClickHandle"
              type="1"
              class="marginBottom"
              :num="cq"
              :list="cqWorkOrderList"
            />
            <workOrderItem
              :workOrderClickHandle="workOrderClickHandle"
              :moreClickHandle="moreClickHandle"
              type="3"
              :num="cp"
              :list="cpWorkOrderList"
            />
          </div>
          <div class="center_item flex-column">
            <workOrderItem
              :workOrderClickHandle="workOrderClickHandle"
              :moreClickHandle="moreClickHandle"
              class="marginBottom"
              type="2"
              :num="cb"
              :list="cbWorkOrderList"
            />
            <workOrderItem
              :workOrderClickHandle="workOrderClickHandle"
              :moreClickHandle="moreClickHandle"
              type="5"
              :num="dh"
              :list="dhWorkOrderList"
            />
          </div>
          <div class="right_item flex-column">
            <workOrderItem
              :workOrderClickHandle="workOrderClickHandle"
              :moreClickHandle="moreClickHandle"
              class="marginBottom"
              type="4"
              :num="zzzz"
              :list="zzzzWorkOrderList"
            />
            <div style="padding: 8px;"></div>
          </div>
        </el-scrollbar>
      </div>

      <DataMessageComponent
        v-if="DataMessageComponent"
        v-model="DataMessageComponent"
        :messageInfo="messageInfoData"
      />
      <DataTableComponent
        :obj="tableWorkOrderObj"
        v-model="itemTableDataDialog"
        :type="typeList"
        @changePageSizeOrPageNo="changePageSizeOrPageNo"
      />
    </el-scrollbar>
  </div>
</template>

<script>
import workOrderItem from '../components/workOrderItem';
import DataMessageComponent from '../components/DataMessageComponent';
import DataTableComponent from '../components/DataTableComponent';
import api from '@/api/ajax/workOrderHomePage';
import moreApi from '@/views/workSheet/workOrderHomePage/Minix/moreApi';
export default {
  mixins: [moreApi],
  components: {
    workOrderItem,
    DataMessageComponent,
    DataTableComponent
  },
  data: () => ({
    searchValue: '0',
    searchPageSize: 2, //工单搜索一页的数据大小
    DataMessageComponent: false,
    itemTableDataDialog: false,
    dpd: '', // 待派单
    wj: '', // 未接来电
    dayyj: '', // 今日接听来电
    dayhb: '', // 今日呼出
    dayfwtjd: '', // 今日服务台提单
    daypd: '', // 今日派单
    daydhyjj: '', // 今日电话已解决
    dayztd: '', // 今日总提单
    dayzbj: '', // 今日办结
    wbj: '', // 未办结工单
    departmentArr: [],
    cq: '', // 超期
    cqWorkOrderList: [],

    cb: '', // 催办
    cbWorkOrderList: [],

    cp: '', // 差评
    cpWorkOrderList: [],

    zzzz: '', // 终止/暂停
    zzzzWorkOrderList: [],

    dh: '', // 打回
    dhWorkOrderList: [],

    //工单详情
    messageInfoData: {},
    // 工单数据列表
    typeList: undefined,
    tableWorkOrderObj: {}
  }),
  computed: {
    deptId() {
      return this.$store.state.common.userMessage.deptId;
    }
  },
  async mounted() {
    await this.getMeauListHandle();
    let templateHeight =
      this.$refs.workOrderBoxTemplate.$el.clientHeight - 16 - 22;
    this.searchPageSize = parseInt(templateHeight / 104);
    await this.getWorkOrderDataNum({
      type: '0'
    });
    await this.getServiceDataNum();
    let { rows: rows1 } = await this.getTypeWorkOrderListHandle(
      {
        sidx: 'cqDay',
        sord: 'desc'
      },
      'getExceedTimeWorkSheets'
    );
    this.cqWorkOrderList = rows1;
    let { rows: rows2 } = await this.getTypeWorkOrderListHandle(
      {
        sidx: 'a.counts',
        sord: 'desc'
      },
      'getHastenWorkSheets'
    );
    this.cbWorkOrderList = rows2;
    let { rows: rows3 } = await this.getTypeWorkOrderListHandle(
      {},
      'getSuspendTerminateSheets'
    );
    this.zzzzWorkOrderList = rows3;
    let { rows: rows4 } = await this.getTypeWorkOrderListHandle(
      {},
      'getBadReviewSheets'
    );
    this.cpWorkOrderList = rows4;
    let { rows: rows5 } = await this.getTypeWorkOrderListHandle(
      {},
      'getBackSheets'
    );
    this.dhWorkOrderList = rows5;
  },
  watch: {
    searchValue: {
      async handler(type) {
        let params = {};
        params.type = type;
        if (type !== '0' && type !== '3') {
          params.type = '2';
          params.fkUserDeptId = type;
        }
        this.getServiceDataNum();
        await this.getWorkOrderDataNum(params);
        let { rows: rows1 } = await this.getTypeWorkOrderListHandle(
          {
            ...params,
            sidx: 'cqDay',
            sord: 'desc'
          },
          'getExceedTimeWorkSheets'
        );
        this.cqWorkOrderList = rows1;
        let { rows: rows2 } = await this.getTypeWorkOrderListHandle(
          {
            ...params,
            sidx: 'a.counts',
            sord: 'desc'
          },
          'getHastenWorkSheets'
        );
        this.cbWorkOrderList = rows2;
        let { rows: rows3 } = await this.getTypeWorkOrderListHandle(
          {
            ...params
          },
          'getSuspendTerminateSheets'
        );
        this.zzzzWorkOrderList = rows3;
        let { rows: rows4 } = await this.getTypeWorkOrderListHandle(
          {
            ...params
          },
          'getBadReviewSheets'
        );
        this.cpWorkOrderList = rows4;
        let { rows: rows5 } = await this.getTypeWorkOrderListHandle(
          {
            ...params
          },
          'getBackSheets'
        );
        this.dhWorkOrderList = rows5;
      }
    }
  },
  methods: {
    // 获取服务台人员页面-统计指标
    async getServiceDataNum() {
      try {
        let deptId = this.searchValue == '0' ? null : this.searchValue;
        const res = await api.getServiceDeskStaffStatisticalIndicators(deptId);
        if (!res.success) throw res.message;

        this.dpd = res.object.dpd;
        this.wj = res.object.wj;
        this.dayyj = res.object.dayyj;
        this.dayhb = res.object.dayhb;
        this.dayfwtjd = res.object.dayfwtjd;
        this.daypd = res.object.daypd;
        this.daydhyjj = res.object.daydhyjj;
        this.dayztd = res.object.dayztd;
        this.dayzbj = res.object.dayzbj;
        this.wbj = res.object.wbj;
      } catch (error) {
        this.$message.error(error);
      }
    },
    // 获取处理科室菜单列表
    async getMeauListHandle() {
      try {
        const list = await api.meauPermissionsList();
        if (!list.success) throw list.message;

        this.departmentArr = list.object;
        this.departmentArr.length == 1
          ? (this.searchValue = this.departmentArr[0].deptId)
          : null;
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 异常工单各种状态统计
    async getWorkOrderDataNum(params) {
      try {
        const res = await api.getAbnormalWorkSheetStatisCounts(params);
        if (!res.success) throw res.message;

        this.cq = res.object.cq;
        this.cb = res.object.cb;
        this.cp = res.object.cp;
        this.zzzz = res.object.zzzz;
        this.dh = res.object.dh;
      } catch (error) {
        this.$message.error(error);
      }
    },
    async getTypeWorkOrderListHandle(params, functionKey) {
      try {
        const pageData = {
          pageNo: '1',
          pageSize: this.searchPageSize || 2,
          type: this.searchValue,
          sidx: 'b.create_time',
          sord: 'desc',
          ...params
        };

        if (this.searchValue !== '0' && this.searchValue !== '3') {
          pageData.type = '2';
          pageData.fkUserDeptId = this.searchValue;
        }
        const res = await api[functionKey](pageData);
        if (!res.success && res.statusCode === 999) throw res.message;
        return res;
      } catch (e) {
        this.$message.error(e);
        return {
          rows: []
        };
      }
    },

    async workOrderClickHandle(item) {
      try {
        const res = await api.workSheetInfo(item.workNumber);
        if (!res.success) {
          throw res.message;
        }
        this.messageInfoData = res.object;
        this.DataMessageComponent = true;
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 更多
    async moreClickHandle(type) {
      let params = {
        pageNo: 1,
        pageSize: 10,
        type: this.searchValue
      };

      if (this.searchValue !== '0' && this.searchValue !== '3') {
        params.type = '2';
        params.fkUserDeptId = this.searchValue;
      }

      this.typeList = type;
      this.tableWorkOrderObj = await this.typeWorkOrderRequestMethod(
        type,
        params
      );
      this.itemTableDataDialog = true;
    },
    // 分页
    async changePageSizeOrPageNo(params) {
      this.tableWorkOrderObj = await this.typeWorkOrderRequestMethod(
        params.type,
        {
          pageNo: params.pageNo,
          pageSize: params.pageSize
        }
      );
    }
  }
};
</script>

<style scoped lang="scss">
.service_box {
  height: 100%;
}
.service_data_num {
  min-width: 1190px;
  display: flex;
  padding: 8px 8px;
  background-color: #fff;
  box-shadow: 0px 2px 4px 0px #e6eaf0;
  border-radius: 4px;
  margin-bottom: 8px;
  > div:not(:first-child) {
    margin-left: 14px;
  }
  .agency .data_num {
    color: #f45555;
  }
  .top_title {
    margin-bottom: 10px;
  }
  .text_align {
    align-items: center;
    display: flex;
  }
  .data_box {
    display: flex;
    // justify-content: space-between;
    align-items: center;
    .flex-column {
      border-radius: 2px;
      border: 1px solid #e0e6f0;
      // padding: 0 13px;
      height: 80px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      > div {
        white-space: nowrap;
      }
    }
  }
  .data_num {
    font-size: 24px;
    font-weight: 600;
    color: #5260ff;
    line-height: 30px;
  }
  .data_title {
    font-weight: 400;
    color: #666666;
    line-height: 20px;
    font-size: 14px;
  }
  .agency {
    flex: 1;
    .data_box {
      padding-left: 8px;
    }
  }
  .workload {
    flex: 3;
    .flex-column {
      width: 8.3vw;
    }
  }
  .General {
    flex: 1.5;
    .flex-column:last-child .data_num {
      color: #f45555;
    }
  }
  .flex-column:not(:last-child) {
    margin-right: 8px;
  }
  .data_unit {
    color: #999999;
    line-height: 17px;
    font-size: 12px;
  }
  .agency .flex-column,
  .General .flex-column {
    width: 7.17vw;
  }
}
.search_box {
  display: flex;
  padding: 8px;
  background-color: #fff;
  margin-bottom: 8px;
  box-shadow: 0px 2px 4px 0px #e6eaf0;
  border-radius: 4px;
  min-width: 1190px;
  .radio_group {
    /deep/ .el-radio-button {
      margin-right: 8px;
      .el-radio-button__inner {
        min-width: 80px;
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
      .el-radio-button__orig-radio:checked + .el-radio-button__inner {
        box-shadow: none;
      }
    }
  }
}
.work_order_box_parent {
  display: flex;
  height: calc(100% - 159px);
  min-width: 1190px;
  overflow-y: auto;
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
    background-color: transparent;
    position: absolute;
    top: 0;
  }
  &:hover.work_order_box_parent::-webkit-scrollbar-thumb {
    border-radius: 6px;
    background-color: rgba(153, 153, 153, 0.4);
    &:hover {
      background-color: rgba(153, 153, 153, 0.8);
    }
  }
  .one_item,
  .center_item,
  .right_item {
    flex: 1;
    > div {
      flex: 1;
    }
    > .marginBottom {
      margin-bottom: 8px;
    }
  }
  .center_item {
    margin: 0 8px;
  }
}
/deep/ .content-scroll-wrap {
  height: calc(100% + 17px);
}
/deep/ .content-scroll-view {
  height: 100%;
  padding-bottom: 8px;
}
/deep/ .scroll-view {
  height: 100%;
}
.top_title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333333;
  line-height: 22px;
  font-size: 14px;
  &::before {
    content: '';
    height: 16px;
    width: 4px;
    background-color: #5260ff;
    margin-right: 3px;
  }
}
/deep/.radio_group
  .el-radio-button__orig-radio:checked
  + .el-radio-button__inner {
  background-color: #fff !important;
  color: #5260ff;
  border-color: #5260ff;
}
/deep/ .inner-scroll-bar .el-scrollbar__bar.is-vertical {
  right: 8px;
}
</style>
