<template>
  <div class="service_person_box">
    <el-scrollbar
      style="width: 100%; height: 100%;"
      wrapClass="content-scroll-wrap"
      viewClass="content-scroll-view flex-column"
    >
      <!--for top-->
      <div class="for_my_workOrder_know">
        <!--我的工单-->
        <div class="item my_work_order">
          <div class="top_title">我的工单</div>
          <div class="data_box">
            <div
              v-for="(item, index) in workOrderStatusList"
              :key="index"
              class="flex-center text_align go-router"
              @click="goOldProjectHandle(item.work_status)"
            >
              <img :src="workStatusIconList[index]" />
              <div>
                <div class="data_title">
                  {{ workOrderStatus[item.work_status] }}
                </div>
                <div class="data_num">{{ item.counts }}</div>
              </div>
            </div>
          </div>
        </div>
        <!--我的知识点-->
        <div class="item my_know_list">
          <div class="top_title">我的知识点</div>
          <div class="data_box">
            <div
              class="flex-center text_align go-router"
              @click="goKnowledgeBaseHandle('submit')"
            >
              <img :src="knowledgeStatusIconList[0]" />
              <div>
                <div class="data_title">已提交</div>
                <div class="data_num">{{ Submitted }}</div>
              </div>
            </div>
            <div
              class="flex-center text_align go-router"
              @click="goKnowledgeBaseHandle('audit')"
            >
              <img :src="knowledgeStatusIconList[1]" />
              <div>
                <div class="data_title">审核中</div>
                <div class="data_num">{{ examine }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!--top-->
      <div class="top_service">
        <!--工单处理-->
        <div class="flex_item work_order_item">
          <div class="top_title">工单处理</div>
          <div class="bottom_icon">
            <div
              class="icon_font go-router"
              @click="goOldProjectHandle('openAddDialog')"
            >
              <img
                src="@/assets/img/workSheet/workOrderHomePage/icon_work.png"
              />
              <span>填报工单</span>
            </div>

            <img
              class="icon_next"
              src="@/assets/img/workSheet/workOrderHomePage/icon_next.png"
            />

            <div class="icon_font">
              <img
                src="@/assets/img/workSheet/workOrderHomePage/icon_pengding.png"
              />
              <span>进度跟进</span>
            </div>
            <img
              class="icon_next"
              src="@/assets/img/workSheet/workOrderHomePage/icon_next.png"
            />
            <div class="icon_font">
              <img
                src="@/assets/img/workSheet/workOrderHomePage/icon_success.png"
              />
              <span>确认及评价</span>
            </div>
          </div>
        </div>
        <!--知识库-->
        <div class="flex_item knowledge_base">
          <div class="top_title">知识库</div>
          <div class="bottom_icon">
            <div
              class="icon_font go-router"
              @click="goKnowledgeBaseHandle('open')"
            >
              <img src="@/assets/img/workSheet/workOrderHomePage/icon_up.png" />
              <span>提交知识点</span>
            </div>
            <img
              class="icon_next"
              src="@/assets/img/workSheet/workOrderHomePage/icon_next.png"
            />
            <div class="icon_font">
              <img
                src="@/assets/img/workSheet/workOrderHomePage/icon_examain.png"
              />
              <span>知识点审批</span>
            </div>
            <img
              class="icon_next"
              src="@/assets/img/workSheet/workOrderHomePage/icon_next.png"
            />
            <div
              class="icon_font go-router"
              @click="goKnowledgeBaseHandle('close')"
            >
              <img
                src="@/assets/img/workSheet/workOrderHomePage/icon_search.png"
              />
              <span>知识库查询</span>
            </div>
          </div>
        </div>
      </div>

      <!--workOrderList-->
      <div class="work_order_list">
        <!--工单列表-->
        <div ref="workSheetList" class="list">
          <div class="top_title">工单列表</div>
          <el-scrollbar
            style="width: 100%; height: calc(100% - 30px);"
            wrapClass="content-scroll-wrap"
          >
            <template v-if="workOrderList && workOrderList.length > 0">
              <div
                v-for="(item, index) in workOrderList"
                :key="index"
                :class="[itemActive === item.workNumber ? 'active' : '']"
                class="work_order_item"
                @click="clickOrderItem(item.workNumber)"
              >
                <div class="item_message flex">
                  <div class="item_status">
                    【{{ item.workNumber }}-{{ item.workStatusName }}】
                  </div>
                </div>
                <IndentSpan :content="item.faultDeion"></IndentSpan>
                <div class="time">报修时间：{{ item.createTime }}</div>
              </div>
            </template>
            <ts-empty v-else></ts-empty>
          </el-scrollbar>
        </div>
        <!--工单详情-->
        <div class="details">
          <div class="real_details flex-column">
            <template v-if="Object.keys(orderInfo).length > 0">
              <!--工单详情-title-->
              <div class="top_title">
                <div>
                  {{ orderInfo.faultDeion }}
                </div>
              </div>
              <!--工单详情-信息与操作-->
              <div class="message_status">
                <!--工单详情-信息-->
                <div class="order_info">
                  <img
                    src="@/assets/img/workSheet/workOrderHomePage/icon_urgent.png"
                  />
                  <span>
                    {{ orderInfo.faultEmergencyValue }}
                  </span>
                  <img
                    src="@/assets/img/workSheet/workOrderHomePage/icon_rangeType.png"
                  />
                  <span> {{ orderInfo.faultAffectScopeValue }} </span>
                  <img
                    src="@/assets/img/workSheet/workOrderHomePage/icon_time.png"
                  />
                  <span class="title">要求时间:</span>
                  <span>{{ orderInfo.requiredCompletionTime || '--' }}</span>
                  <img
                    src="@/assets/img/workSheet/workOrderHomePage/icon_type.png"
                  />
                  <span class="title">
                    {{
                      orderInfo.repairManDeptName
                        ? orderInfo.repairManDeptName + '：'
                        : ''
                    }}
                    <span>
                      {{ orderInfo.repairManName }}
                      ({{ orderInfo.repairPhone }})
                    </span>
                  </span>
                </div>
                <!--工单详情-操作-->
                <div class="order_menu">
                  <el-button
                    v-if="orderInfo.workStatusVaule === '处理中'"
                    size="mini"
                    type="primary"
                    class="action-btn-pause action-btn"
                    @click="suspendWorkHandle(orderInfo.workNumber)"
                    >暂停</el-button
                  >
                  <el-button
                    v-if="
                      ['待派单', '待接单', '处理中'].indexOf(
                        orderInfo.workStatusVaule
                      ) >= 0
                    "
                    size="mini"
                    type="danger"
                    class="action-btn-end action-btn"
                    @click="stopWorkHandle(orderInfo.workNumber)"
                    >终止</el-button
                  >
                  <el-button
                    v-if="orderInfo.workStatusVaule === '处理中'"
                    size="mini"
                    type="success"
                    class="action-btn-urge action-btn"
                    @click="urgeWorkHandle(orderInfo.workNumber)"
                    >催办</el-button
                  >
                  <el-button
                    v-if="orderInfo.workStatusVaule === '待验收'"
                    size="mini"
                    type="success"
                    class="action-btn-check action-btn"
                    @click="showWorkOrderCheck = true"
                    >验收</el-button
                  >
                  <el-button
                    v-if="orderInfo.workStatusVaule === '待评价'"
                    size="mini"
                    type="success"
                    class="action-btn-evaluate action-btn"
                    @click="showWorkOrderEvaluate = true"
                    >评价</el-button
                  >
                  <el-button
                    v-if="orderInfo.workStatusVaule === '已暂停'"
                    size="mini"
                    type="success"
                    class="action-btn-open action-btn"
                    @click="showWorkOrderOpen = true"
                    >开启</el-button
                  >
                </div>
              </div>
              <div class="message_main flex-grow">
                <div class="flex message_tips">
                  <div class="tips_item">
                    <span>业务类型:</span>
                    <span>{{ orderInfo.fkFaultType }}</span>
                  </div>

                  <div class="tips_item">
                    <span>故障设备:</span>
                    <span>{{ orderInfo.faultEquipmentName || '--' }}</span>
                  </div>

                  <div class="tips_item">
                    <span>故障说明:</span>
                    <span>{{ orderInfo.faultDeion }}</span>
                  </div>
                  <FileShow
                    class="nafla"
                    :showTitle="false"
                    :workOrderInfo="workOrderInfo"
                  />
                </div>
                <div class="flex">
                  <ProgressShow
                    class="progress_two"
                    :progressList="orderProgress"
                  />
                </div>
              </div>
            </template>
            <ts-empty v-else class="empty-style flex-center" />
          </div>
        </div>
      </div>
    </el-scrollbar>

    <DataMessageComponent
      v-if="operationAndInfoDialog"
      v-model="operationAndInfoDialog"
      :messageInfo="messageInfoData"
      :operationType="operationType"
      :pkWsTaskId="pkWsTaskId"
      @refresh="refreshHandle"
      class="message-dialog"
    />

    <workOrderUrgeDialog
      v-if="urgeDialog"
      v-model="urgeDialog"
      :pkWsTaskId="pkWsTaskId"
      :urgeTitle="urgeTitle"
      :workOrderInfo="workOrderInfo"
      @refresh="refreshHandle"
    />
    <!-- 评价 -->
    <work-order-evaluate
      v-if="showWorkOrderEvaluate"
      :workOrder="actionOrder"
      @close="handleWorkOrderEvaluateClose"
    />
    <!-- 验收 -->
    <work-order-check
      v-if="showWorkOrderCheck"
      :workOrder="actionOrder"
      @close="handleWorkOrderCheckClose"
    />
    <!-- 开启 -->
    <work-order-open
      v-if="showWorkOrderOpen"
      :workOrder="actionOrder"
      @close="handleWorkOrderOpenClose"
    />
  </div>
</template>

<script>
import api from '@/api/ajax/workOrderHomePage';
import data from '@/views/workSheet/workOrderHomePage/Minix/data';
import FileShow from '@/views/workSheet/comment/components/work-sheet-status-list/file-show.vue';
import ProgressShow from '@/views/workSheet/comment/components/work-sheet-status-list/progress-show.vue';
import IndentSpan from '@/components/indent-span';
import DataMessageComponent from '../components/DataMessageComponent';
import workOrderUrgeDialog from '../components/workOrderUrgeDialog';
import WorkOrderEvaluate from '@/views/workSheet/comment/components/work-order-evaluate.vue';
import WorkOrderCheck from '@/views/workSheet/comment/components/work-order-check.vue';
import WorkOrderOpen from '@/views/workSheet/comment/components/work-order-open.vue';

export default {
  mixins: [data],
  components: {
    FileShow,
    ProgressShow,
    IndentSpan,
    DataMessageComponent,
    workOrderUrgeDialog,
    WorkOrderEvaluate,
    WorkOrderCheck,
    WorkOrderOpen
  },
  data: () => ({
    workOrderStatusList: [],
    //头部我的工单icon 图标列表
    workStatusIconList: [
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>')
    ],
    knowledgeStatusIconList: [
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>'),
      require('@/assets/img/workSheet/workOrderHomePage/<EMAIL>')
    ],

    Submitted: '',
    examine: '',
    workOrderList: [],
    itemActive: undefined,
    actionOrder: {},
    orderInfo: {},
    orderProgress: [],
    workOrderInfo: {},

    messageInfoData: {},
    operationAndInfoDialog: false,

    operationType: '',
    pkWsTaskId: '',

    urgeDialog: false,
    urgeTitle: [],
    listDom: null,
    pageNo: 1,
    totalCount: 0,

    //操作弹窗显示属性
    showWorkOrderEvaluate: false,
    showWorkOrderCheck: false,
    showWorkOrderOpen: false
  }),
  created() {
    this.refresh();
  },
  mounted() {
    this.$nextTick(() => {
      this.listDom = this.$refs.workSheetList.querySelector(
        '.content-scroll-wrap'
      );
      this.listDom.addEventListener('scroll', this.handleScroll);
    });
  },
  methods: {
    refresh() {
      this.getCountGroupByWorkStatusHandle();
      this.getMyKnowledgeBaseCount();
      this.getWorkSheetListHandle('init');
    },
    handleScroll(e) {
      let sh = this.listDom.scrollHeight || this.listDom.scrollHeight; // 盒子整体高度
      let st = this.listDom.scrollTop || this.listDom.scrollTop; // 滚动条距离顶部距离
      let ch = this.listDom.clientHeight || this.listDom.clientHeight; // 滚动条以外容器高度

      if (st + ch >= sh) {
        if (this.totalCount && this.workOrderList.length < this.totalCount) {
          this.pageNo++;
          this.getWorkSheetListHandle();
        }
      }
    },
    // 处理人各状态工单数量
    async getCountGroupByWorkStatusHandle() {
      try {
        const params = {
          fkUserId: this.fkUserId,
          type: '5'
        };
        const res = await api.getCountGroupByWorkStatus(params);
        if (!res.success) throw res.message;
        this.workOrderStatusList = res.object;
      } catch (e) {
        this.$message.error(e);
      }
    },
    async getMyKnowledgeBaseCount() {
      try {
        const res = await api.getMyKnowledgeBaseCount(this.fkUserId);
        if (!res.success) throw res.message;
        this.Submitted = res.object.ytj;
        this.examine = res.object.sh;
      } catch (e) {
        this.$message.error(e);
      }
    },
    async getWorkSheetListHandle(type, orderId) {
      const params = {
        type: '1',
        workStatus: '0',
        pageNo: this.pageNo,
        pageSize: '8',
        sidx: 'create_time',
        sord: 'desc'
      };
      try {
        const res = await api.workSheetList(params);
        if (!res.success && res.statusCode === 999) throw res.message;
        if (res.rows && res.rows.length > 0) {
          this.totalCount = res.totalCount;
          if (type === 'init') {
            this.workOrderList = res.rows || [];
            this.clickOrderItem(orderId || res.rows[0].workNumber);
          } else {
            this.workOrderList = this.workOrderList.concat(res.rows);
          }
        }
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 查看工单详情
    async clickOrderItem(id) {
      this.itemActive = id;
      try {
        const res = await api.workSheetInfo(id);
        if (!res.success) throw res.message;
        this.workOrderList.forEach((item, index) => {
          if (item.workNumber === id) {
            this.workOrderList[index].workStatus =
              res.object.wsWsSheetInfoOutVo.workStatus;
            this.actionOrder = { ...item };
          }
        });
        this.orderInfo = res.object.wsWsSheetInfoOutVo;
        this.orderProgress = res.object.wsTaskInfoOutVoList;
        this.workOrderInfo = res.object;
        this.loading = false;
      } catch (e) {}
    },
    // 暂停
    suspendWorkHandle(id) {
      this.getActiveWorkOrder();
      this.operationType = 'break';
      this.getWorkOrderInfo(id);
    },
    // 终止
    stopWorkHandle(id) {
      this.getActiveWorkOrder();
      this.operationType = 'stop';
      this.getWorkOrderInfo(id);
    },
    // 催办
    async urgeWorkHandle(id) {
      try {
        const res = await api.getHastenInfo(id);
        if (!res.success) {
          throw res.message;
        }
        let title;

        if (res.object && Object.keys(res.object).length) {
          title = [res.object.counts, res.object.createTime];
        }

        this.urgeTitle = !res.object ? [] : title;

        this.getActiveWorkOrder();
        this.urgeDialog = true;
      } catch (e) {
        this.$message.error(e);
      }
    },
    // 更新节点id
    getActiveWorkOrder() {
      this.workOrderList.forEach(item => {
        if (item.workNumber === this.itemActive) {
          this.pkWsTaskId = item.pkWsTaskId;
        }
      });
    },
    refreshHandle(id) {
      this.urgeDialog = false;
      this.getWorkSheetListHandle('init', id);
      this.getMyKnowledgeBaseCount();
      this.getCountGroupByWorkStatusHandle();
    },
    // 获取节点信息
    async getWorkOrderInfo(id) {
      try {
        this.loading = true;
        const res = await api.workSheetInfo(id);
        if (!res.success) {
          throw res.message;
        } else {
          this.messageInfoData = res.object;
          this.operationAndInfoDialog = true;
        }
      } catch (e) {
        this.$message.error(e);
      } finally {
        this.loading = false;
      }
    },
    goOldProjectHandle(type) {
      this.$jumpOpenDialog({
        path: '/myWorkOrder',
        type,
        isWorkOrder: true
      });
    },
    goKnowledgeBaseHandle(type) {
      this.$jumpOpenDialog({
        path: '/ts-web-work-order/workSheet/orderKnowledgeBase',
        type,
        isWorkOrder: false
      });
    },
    handleWorkOrderEvaluateClose(type) {
      if (type) {
        this.getWorkSheetListHandle('init');
        this.getMyKnowledgeBaseCount();
        this.getCountGroupByWorkStatusHandle();
      }
      this.showWorkOrderEvaluate = false;
    },
    handleWorkOrderCheckClose(type) {
      if (type) {
        this.getWorkSheetListHandle('init', this.actionOrder.workNumber);
        this.getCountGroupByWorkStatusHandle();
        this.getMyKnowledgeBaseCount();
      }
      this.showWorkOrderCheck = false;
    },
    handleWorkOrderOpenClose(type) {
      if (type) {
        this.getWorkSheetListHandle('init', this.actionOrder.workNumber);
        this.getCountGroupByWorkStatusHandle();
        this.getMyKnowledgeBaseCount();
      }
      this.showWorkOrderOpen = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.go-router {
  &:hover {
    cursor: pointer;
  }
}
.service_person_box {
  height: 100%;
  .top_service {
    min-width: 1190px;
    display: flex;
    margin-bottom: 8px;
    > .flex_item {
      flex: 1;
      flex-direction: column;
      padding: 8px;
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px #e6eaf0;
      border-radius: 4px;
      .top_title {
        margin-bottom: 10px;
      }
      &:first-child {
        margin-right: 8px;
      }
      .bottom_icon {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 8px;
        > div {
          width: 120px;
          height: 48px;
          background: #f4f5f6;
          border-radius: 4px;
          display: flex;
          justify-content: center;
          align-items: center;
          img {
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }
          &:not(:first-child) {
            span {
              color: #999999;
            }
          }
          &:first-child {
            span {
              font-weight: 600;
              color: #333333;
            }
          }
        }
        img {
          width: 16px;
          height: 16px;
        }
      }
      .icon_next {
        margin: 0 16px;
      }
      .icon_font {
        display: flex;
        align-items: center;
        // flex-direction: column;
      }
    }
  }
  .for_my_workOrder_know {
    background: #ffffff;
    box-shadow: 0px 2px 4px 0px #e6eaf0;
    border-radius: 4px;
    display: flex;
    margin-bottom: 8px;
    min-width: 1190px;
    padding: 8px;
    .top_title {
      margin-bottom: 10px;
    }
    > .item {
      .text_align {
        align-items: center;
        display: flex;
      }
      .data_box {
        display: flex;
        justify-content: space-around;
        .data_num {
          font-size: 34px;
          color: blue;
        }
        .data_title {
          color: #333;
        }
      }
    }
    .my_work_order {
      flex: 7;
      .go-router {
        border-radius: 2px;
        border: 1px solid #e0e6f0;
        width: 114px;
        height: 80px;
        img {
          height: 32px;
          margin-right: 8px;
        }
        .data_title {
          color: #333;
          line-height: 22px;
          font-size: 14px;
        }
        .data_num {
          font-weight: 600;
          color: #5260ff;
          line-height: 30px;
          font-size: 24px;
        }
      }
    }
    .my_know_list {
      flex: 2;
      margin-left: 14px;
      > .data_box {
        .go-router {
          width: 114px;
          height: 80px;
          border-radius: 2px;
          border: 1px solid #e0e6f0;
          img {
            height: 32px;
            max-width: 32px;
            margin-right: 8px;
          }
          .data_title {
            color: #333;
            line-height: 22px;
            font-size: 14px;
          }
          .data_num {
            font-weight: 600;
            color: #5260ff;
            line-height: 30px;
            font-size: 24px;
          }
        }
      }
    }
  }
  .work_order_list {
    min-width: 1190px;
    display: flex;
    height: calc(100% - 259px);
    .list {
      flex: 1;
      background: #ffffff;
      box-shadow: 0px 2px 4px 0px #e6eaf0;
      border-radius: 4px;
      margin-right: 8px;
      min-width: 320px;
      padding: 8px;
      overflow: hidden;
      // overflow-y: auto;
      .top_title {
        margin-bottom: 8px;
      }
      // &::-webkit-scrollbar {
      //   background-color: transparent;
      //   width: 8px;
      // }
      // &:hover.list::-webkit-scrollbar-thumb {
      //   border-radius: 8px;
      //   background-color: rgba(0, 0, 0, 0.2);
      // }
      .no-data {
        margin-top: 100px;
        text-align: center;
      }
      .work_order_item {
        padding: 8px 0;
        &.active {
          background: #5260ff14;
        }
        &:hover {
          cursor: pointer;
          background: #5260ff14;
        }
        .item_status {
          color: #333333;
          font-weight: 600;
        }
        .item_message * {
          color: #333;
          font-size: 14px;
          line-height: 20px;
        }
        .time {
          color: #666666;
          font-size: 12px;
          line-height: 20px;
        }
      }
    }
    .details {
      flex: 3;
      overflow-y: auto;
      padding: 8px;
      background-color: #fff;
      border-radius: 4px;
      &::-webkit-scrollbar {
        background-color: transparent;
        width: 6px;
        height: 6px;
      }
      &:hover.details::-webkit-scrollbar-thumb {
        border-radius: 6px;
        background-color: rgba(153, 153, 153, 0.4);
        &:hover {
          background-color: rgba(153, 153, 153, 0.8);
        }
      }
      .top_title {
        margin-bottom: 10px;
        div {
          font: inherit;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .real_details {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        .message_status {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          .order_info {
            display: flex;
            align-items: center;
            > img {
              width: 20px;
              height: 20px;
              margin-right: 4px;
              &:first-child {
                margin-left: 8px;
              }
            }
            span {
              margin-right: 20px;
              color: #333;
              white-space: nowrap;
            }
            .title {
              color: #666;
            }
          }
          .order_menu {
            display: flex;
            align-items: center;
            &.stop {
              margin: 0px 10px;
            }
          }
        }
        .message_main {
          padding-left: 8px;
          display: flex;
          overflow-x: hidden;
          height: 100%;
          overflow-y: auto;
          &::-webkit-scrollbar {
            background-color: transparent;
            width: 6px;
            height: 6px;
          }
          &:hover::-webkit-scrollbar-thumb {
            border-radius: 6px;
            background-color: rgba(153, 153, 153, 0.4);
            &:hover {
              background-color: rgba(153, 153, 153, 0.8);
            }
          }
          > .flex {
            &:last-child {
              flex: 19;
            }
            .progress_two {
              /deep/ {
                .el-step__line {
                  background-color: #e6ecf4;
                  left: 6px;
                }
                .el-step__icon {
                  border: none;
                  background-color: #5260ff;
                  width: 12px;
                  height: 12px;
                  top: 4px;
                  left: 1px;
                }
                .el-step__head {
                  flex-shrink: 0;
                }
                .el-step__description {
                  margin-bottom: 24px;
                }
                .el-step__description {
                  .progress_message {
                    margin-bottom: 0;
                    &:last-child span {
                      white-space: unset;
                    }
                    &:first-child {
                      flex-wrap: wrap;
                      > span:first-child {
                        border: none;
                        background-color: transparent;
                        padding: 0;
                        color: #333;
                        line-height: 20px;
                        &::before {
                          content: '【';
                        }
                        &::after {
                          content: '】';
                        }
                      }
                      > span:not(:first-child) {
                        font-size: 12px;
                        line-height: 20px;
                        color: #666;
                      }
                      > span:not(:nth-child(2)) {
                        margin: 0;
                      }
                      > span:nth-child(2) {
                        margin-right: 8px;
                      }
                    }
                    &:not(:first-child) {
                      span {
                        font-size: 12px;
                        line-height: 20px;
                        color: #666;
                      }
                    }
                  }
                }
                .el-step:first-child {
                  .el-step__icon {
                    top: -3px;
                  }
                  .el-step__main .el-step__title {
                    padding: 0;
                  }
                }
                .el-step {
                  flex-basis: unset !important;
                }
                .el-step:last-child {
                  visibility: hidden;
                  // flex-basis: auto !important;
                  height: 0;
                }
                .el-step:nth-last-child(2) {
                  flex: 1;
                }
                .el-step__line-inner {
                  color: #e6ecf4;
                  border-color: #e6ecf4;
                }
              }
              &.message_progress {
                // margin-left: 0;
                // transform: translateX(0%);
                > span:not(:first-child) {
                  font-size: 12px;
                }
              }
            }
          }
          .message_tips {
            display: flex;
            flex: 24;
            flex-direction: column;
            .tips_item {
              color: #666666;
              span {
                line-height: 24px;
                &:first-child {
                  margin-right: 10px;
                }
                &:last-child {
                  color: #333;
                }
              }
            }
            .nafla {
              font-size: 14px;
              color: #666666;
              &.multi_media {
                align-items: flex-start;
              }
              /deep/ .audio-file {
                margin-bottom: 10px !important;
              }
              /deep/ .picture {
                margin-bottom: 10px !important;
              }
              /deep/ .file {
                margin-bottom: 10px !important;
              }
              /deep/ .label {
                font-size: 14px;
              }
            }
          }
        }
        .empty-style {
          height: calc(100% - 37px);
          width: 350px;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          /deep/ img {
            height: auto;
          }
        }
      }
    }
  }
}
/deep/ .content-scroll-wrap {
  height: calc(100% + 17px);
}
/deep/ .content-scroll-view {
  height: 100%;
  padding-bottom: 8px;
}
.top_title {
  display: flex;
  align-items: center;
  font-weight: 600;
  color: #333333;
  line-height: 22px;
  font-size: 14px;
  &::before {
    content: '';
    height: 16px;
    width: 4px;
    background-color: #5260ff;
    margin-right: 3px;
    flex-shrink: 0;
  }
}
</style>
