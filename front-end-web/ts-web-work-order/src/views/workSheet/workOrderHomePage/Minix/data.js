export default {
  data: () => ({
    itemDatas: [
      [
        {
          label: '工单编号',
          prop: 'workNumber'
        },
        {
          label: '报修人',
          prop: 'repairManName'
        }
      ],
      [
        {
          label: '报修科室',
          prop: 'repairManDeptName'
        },
        {
          label: '报修电话',
          prop: 'repairPhone'
        }
      ],
      [
        {
          label: '报修地址',
          prop: 'repairDeptAddress',
          colspan: 3
        }
      ],
      [
        {
          label: '处理科室',
          prop: 'businessDeptName'
        },
        {
          label: '业务类型',
          prop: 'fkFaultType'
        }
      ],

      [
        {
          label: '故障描述',
          prop: 'faultDeion',
          colspan: 3,
          activeColor: '',
          activeClass: 'heightlight-cell'
        }
      ],

      [
        {
          label: '故障设备',
          prop: 'faultEquipmentName'
        },
        {
          label: '保修方式',
          prop: 'repairTypeValue'
        }
      ],
      [
        {
          label: '紧急程度',
          prop: 'faultEmergencyValue',
          activeColor: '',
          activeClass: ''
        },
        {
          label: '影响范围',
          prop: 'faultAffectScopeValue',
          activeColor: '',
          activeClass: ''
        }
      ],
      [
        {
          label: '建单人',
          prop: 'createByName'
        },
        {
          label: '建单时间',
          prop: 'createTime'
        }
      ],
      [
        {
          label: '要求完成时间',
          prop: 'requiredCompletionTime',
          activeColor: '',
          activeClass: ''
        },
        {
          label: '接单时间',
          prop: 'revTime'
        }
      ],
      [
        {
          label: '派单人',
          prop: 'dispatcher'
        },
        {
          label: '派单时间',
          prop: 'sendTime'
        }
      ],
      [
        {
          label: '处理人',
          prop: 'fkUserName',
          colspan: 3
        }
      ],
      [
        {
          label: '总处理用时',
          prop: 'workHours',
          colspan: 3
        }
      ],
      [
        {
          label: '完成时间',
          prop: 'actualCompletionTime'
        },
        {
          label: '确认时间',
          prop: 'confirmTime'
        }
      ]
    ]
  }),
  computed: {
    isAdmin() {
      return this.$store.state.common.userMessage.businessexception;
    },
    isWebSocket() {
      return this.$store.state.common.userMessage.webSocket;
    },
    deptId() {
      return this.$store.state.common.userMessage.deptId;
    },
    deptName() {
      return this.$store.state.common.userMessage.deptName;
    },
    fkUserId() {
      return this.$store.state.common.userMessage.userId;
    },
    isLead() {
      return this.$store.state.common.userMessage.isLead;
    },
    isFullScreen() {
      return this.$store.state.common.isFullScreen;
    },
    workOrderStatus: () => ({
      '1': '待派单',
      '2': '待接单',
      '3': '处理中',
      '4': '待验收',
      '5': '待评价',
      '6': '已完成',
      '7': '已暂停',
      '8': '已终止'
    })
  }
};

// 工单编号 workNumber;
// 报修人 repairManName;
// 保修科室 repairManDeptName;
// 报修地址 repairDeptAddress;
// 报修电话 repairPhone; (是联系电话？)
// 故障类型 fkFaultType;
// 故障设备名称 faultEquipmentName;
// 故障描述 faultDeion;
// 故障紧急程度 faultEmergencyValue;
// 故障范围 faultAffectScopeValue;
// 要求完成时间 requiredCompletionTime;
// 报修方式 repairTypeValue;
// 提单人 createByName;
// 提单时间 createTime;
// 派单时间 sendOrderTime;
// 接单时间 revTime;
// 处理人 fkUserName;
// (处理总用时间)工时 workHours;
// 备注 remark;
// 完成时间 actualCompletionTime;
