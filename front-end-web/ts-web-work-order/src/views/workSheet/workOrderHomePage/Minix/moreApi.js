export default {
  methods: {
    async typeWorkOrderRequestMethod(type, params) {
      let obj;
      switch (type) {
        case '1':
          obj = await this.getTypeWorkOrderListHandle(
            {
              ...params,
              sidx: 'cqDay',
              sord: 'desc'
            },
            'getExceedTimeWorkSheets'
          );
          break;

        case '2':
          obj = await this.getTypeWorkOrderListHandle(
            {
              ...params,
              sidx: 'a.counts',
              sord: 'desc'
            },
            'getHastenWorkSheets'
          );
          break;

        case '3':
          obj = await this.getTypeWorkOrderListHandle(
            params,
            'getBadReviewSheets'
          );
          break;

        case '4':
          obj = await this.getTypeWorkOrderListHandle(
            params,
            // 'getSuspendTerminateSheets'
            'getTodayTerminationSheets'
          );
          break;

        case '5':
          obj = await this.getTypeWorkOrderListHandle(params, 'getBackSheets');
          break;

        case '6':
          obj = await this.getTypeWorkOrderListHandle(
            params,
            'getAssistWorkOrder'
          );
          break;
        case '7':
          obj = await this.getTypeWorkOrderListHandle(
            params,
            'getTodaySuspendedSheets'
          );
          break;
      }

      return obj;
    }
  }
};
