<template>
  <div ref="largeScreenContainer" class="large-screen-body">
    <div class="large-screen-content flex flex-column" ref="largeScreenBody">
      <div class="screen-title-box flex">
        <div class="screen-refresh flex-grow">
          <i
            class="oa-icon"
            :class="isFullScreen ? 'oa-pc-tuichuquanping' : 'oa-pc-quanping'"
            style="margin-left: -18px; font-size: 22px; cursor: pointer; margin-right: 12px;"
            @click="handleFullScreen"
          >
          </i>
          60s刷新一次数据
        </div>
        <div class="screen-title flex-col-center ">
          <div
            ref="largeScreenTitle"
            class="flex-col-center"
            style="-webkit-background-clip: text; background-clip: text;"
            :style="{ letterSpacing: screenTItleSpace }"
          >
            <img :src="screenLogo" :style="{ marginRight: screenTItleSpace }" />
            {{ screenTitle }}
            <div
              ref="dot"
              class="dot"
              :style="{ marginRight: screenTItleSpace }"
            ></div>
            {{ deptName }}
          </div>
        </div>
        <div class="screen-time flex-grow">{{ nowTime }}</div>
      </div>

      <div class="screen-content flex-column flex-grow">
        <div class="screen-col-top flex">
          <month-work-order
            :dataList="thisMonthOrderList"
            class="screen-card"
          />
          <today-work-order
            :todayCreatCount="todayOrderCount"
            :dataList="todayOrderList"
            class="screen-card"
          />
        </div>

        <div class="screen-bottom flex-grow flex">
          <div class="screen-bottom-col-1">
            <month-work-order-quality
              :dataObject="monthOrderQuality"
              class="screen-card"
            />
            <this-month-sub-order-dept
              :subList="monthSubDeptList"
              class="screen-card"
            />
          </div>

          <handing-order
            :handingOrderList="handingOrderList"
            :totalHanding="totalHanding"
            class="screen-card screen-bottom-col-2"
          />

          <div class="screen-bottom-col-1 flex-column" style="height: 774px;">
            <engineer-situation
              ref="engineerContent"
              :todayUnAssignCount="todayUnAssign"
              :engineerList="engineerList"
              class="screen-card"
            />

            <today-duty-officer :dutyList="dutyList" class="screen-card">
            </today-duty-officer>
            <img
              :src="
                require('@/assets/img/workSheet/largeScreen/trasen_logo.png')
              "
              class="trasen-logo"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MonthWorkOrder from './components/monthWorkOrder.vue'; //本月工单
import TodayWorkOrder from './components/todayWorkOrder.vue'; //今日工单
import MonthWorkOrderQuality from './components/monthWorkOrderQuality.vue'; //本月工单质量
import ThisMonthSubOrderDept from './components/thisMonthSubOrderDept.vue'; //本月提单科室
import HandingOrder from './components/handingOrder.vue'; //处理中的工单
import EngineerSituation from './components/engineerSituation.vue'; //今日建单分配情况
import TodayDutyOfficer from './components/today-duty-officer.vue'; //今日值班人员

export default {
  components: {
    MonthWorkOrder,
    TodayWorkOrder,
    MonthWorkOrderQuality,
    ThisMonthSubOrderDept,
    HandingOrder,
    EngineerSituation,
    TodayDutyOfficer
  },
  data() {
    return {
      screenLogo: '',
      screenTitle: '北海市第二人民医院',
      screenTItleSpace: 0,

      todayOrderCount: 0, //今日建单总数
      todayOrderList: [], //顶部今日工单完成情况
      thisMonthOrderList: [], //左上角本月工单完成情况
      todayUnAssign: 0, //今日未指派
      monthOrderQuality: {}, //本月工单质量
      monthSubDeptList: [], //本月提单科室

      engineerPageCount: 0,
      engineerPageNo: 1,
      engineerPageSize: 8,
      engineerList: [],

      handingRefreshTimer: null, //工单刷新
      totalHanding: 0, //总共正在处理的工单
      handingPageNo: 1,
      handingOrderList: [],

      pageRefreshTimer: null, //页面刷新定时器
      intervalTimer: null, //右上角时间刷新定时器
      nowTime: '', //右上角当前时间
      dutyList: [] //值班人员列表
    };
  },
  computed: {
    isFullScreen() {
      return this.$store.state.common.isFullScreen;
    },
    deptName() {
      return this.$store.state.common.userMessage.deptName;
    },
    fkDeptId() {
      return this.$store.state.common.userMessage.deptId;
    },
    isLead() {
      return this.$store.state.common.userMessage.lead;
    }
  },
  created() {
    if (this.isFullScreen) {
      this.$nextTick(() => {
        this.$devopParentTypeFun(true);
      });
    }

    this.refreshTime();
    this.intervalTimer = setInterval(() => {
      this.refreshTime();
    }, 30000);
    this.type = this.isLead ? null : this.deptId;
    window.addEventListener('resize', this.handleWindowResize);

    this.setRefreshTimer();
    this.refresh();
  },
  async mounted() {
    await this.getLogoAndHostName();

    this.$nextTick(() => {
      this.computedBodyContent();

      let length = this.screenTitle.length + this.deptName.length,
        otherWidth = 650 - length * 31 - 31 - 6;
      this.screenTItleSpace = otherWidth / (length + 2) + 'px';
    });
  },
  activated() {
    window.dispatchEvent(new Event('resize'));
  },
  methods: {
    refresh() {
      //刷新本月工单和今日工单
      this.getOrderDetailList();
      this.getMonthOrderQuality();
      this.getMonthSubDept();
      this.getHandingList();
      this.getTodayDuty();

      this.handingRefreshTimer && clearInterval(this.handingRefreshTimer);
      this.handingRefreshTimer = setInterval(() => {
        if (this.$route.path != '/workSheet/largeScreen') {
          return;
        }
        this.getHandingList(1);
        this.getEngineerList(1);
      }, 15050);
    },
    //-------------------------- 接口请求 ---------------------------------
    async getLogoAndHostName() {
      await this.ajax
        .getGlobalSetting({
          isAll: 'Y'
        })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.screenLogo =
            '/ts-basics-bottom/fileAttachment/downloadFile/' +
            res.object.hospitalLogo;
          this.screenTitle = res.object.webTitle;
        });
    },
    //获取工单处理数据
    getOrderDetailList() {
      this.$api({
        url: '/ts-worksheet/statisticsData/getKeyDataIndicatorsOfWorkOrder',
        method: 'get',
        params: {
          fkDeptId: this.isLead ? '' : this.fkDeptId
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }

        this.thisMonthOrderList = [
          res.object.monthjd,
          res.object.monthwwc,
          Math.round(res.object.monthwcl)
        ];

        this.todayOrderList = [];
        for (let i = 1; i < 9; i++) {
          this.todayOrderList.push(Number(res.object[i]));
        }

        let finishedCount = this.todayOrderList[4] + this.todayOrderList[5];
        this.todayOrderList[5] = finishedCount;
        this.todayOrderList.splice(4, 1);

        this.todayOrderCount = res.object.dayjrjd;
        this.todayUnAssign = res.object.wzpgdsl;
      });
    },
    //获取本月工单质量
    getMonthOrderQuality() {
      this.$api({
        url: '/ts-worksheet/statisticsData/getDepartmentWorkOrderQuality',
        method: 'get',
        params: {
          fkDeptId: this.isLead ? '' : this.fkDeptId,
          limit: 3
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }

        let object = res.object || {};
        let list = [
          {
            name: '满意',
            value: object.my
          },
          {
            name: '非常满意',
            value: object.fcmy
          },
          {
            name: '不满意',
            value: object.bmy
          },
          {
            name: '一般',
            value: object.yb
          },
          {
            name: '很不满意',
            value: object.hbmy
          }
        ].filter(item => item.value > 0);

        let rankingList = [];
        (object.wsEvaluationTopOutVo || []).forEach(item => {
          rankingList.push({
            name: item.fkUserName,
            grade: item.avgSocre
          });
        });

        this.monthOrderQuality = {
          list,
          avg: object.avgScore,
          rankingList
        };
      });
    },
    //获取本月提单科室
    getMonthSubDept() {
      this.$api({
        url: '/ts-worksheet/statisticsData/getDeptCountTopDatas',
        method: 'get',
        params: {
          fkDeptId: this.isLead ? '' : this.fkDeptId,
          sidx: 'total desc, wsTotal',
          sord: 'desc',
          pageNo: 1,
          pageSize: 5
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }

        this.monthSubDeptList = [];
        (res.rows || []).forEach(item => {
          this.monthSubDeptList.push({
            name: item.name,
            finished: item.wsTotal,
            unfinished: item.wwcTotal,
            total: item.total
          });
        });
      });
    },
    //获取正在处理工单
    getHandingList(type) {
      if (this.totalHanding && this.totalHanding < 13 && type) {
        return;
      }

      this.$api({
        url: '/ts-worksheet/statisticsData/wsSheetScreenPageList',
        method: 'get',
        params: {
          fkDeptId: this.isLead ? '' : this.fkDeptId,
          pageNo: this.handingPageNo,
          pageSize: 12
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '服务台报错，请稍后再试');
          return;
        }
        this.totalHanding = res.totalCount;
        this.handingPageNo =
          this.handingPageNo == res.pageCount ? 1 : this.handingPageNo + 1;

        this.handingOrderList = res.rows || [];
      });
    },
    //获取今日建单分配情况
    getEngineerList(type) {
      this.$api({
        url: '/ts-worksheet/statisticsData/wsSheetDistributionScreenPageList',
        method: 'get',
        params: {
          fkDeptId: this.isLead ? '' : this.fkDeptId,
          pageNo: this.engineerPageNo,
          pageSize: this.engineerPageSize
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }
        this.engineerPageNo < res.pageCount
          ? this.engineerPageNo++
          : (this.engineerPageNo = 1);

        this.engineerList = res.rows || [];
      });
    },
    //获取今日值班人员
    async getTodayDuty() {
      let res = await this.$api({
        url: '/ts-hrms/scheduleinfo/getSchedule',
        method: 'post',
        data: JSON.stringify({
          empOrgId: this.$store.state.common.userInfo.orgId,
          startDate: this.$dayjs().format('YYYY-MM-DD'),
          endDate: this.$dayjs().format('YYYY-MM-DD')
        }),
        headers: {
          'Content-Type': 'application/json'
        }
      });
      if (!res.success) {
        return;
      }

      this.dutyList = res.object
        .filter(item => {
          let isNowDuty = false;
          let dutyList = item.frequencyTime.split(',') || [];
          dutyList.forEach(duty => {
            let timeList = duty
              .split('-')
              .map(
                time => `${this.$dayjs().format('YYYY-MM-DD')} ${time.trim()}`
              );
            if (
              this.$dayjs().isBefore(this.$dayjs(timeList[1])) &&
              this.$dayjs().isAfter(this.$dayjs(timeList[0]))
            ) {
              isNowDuty = true;
            }
          });
          return isNowDuty;
        })
        .map(item => item.employeeName);

      this.$nextTick(() => {
        this.getEngineerList();
      });
    },
    //-------------------------- 事件处理 ---------------------------------
    /**@desc 主动请求全屏 */
    handleFullScreen() {
      if (this.isFullScreen) {
        document.exitFullscreen();
        return;
      }
      this.$refs.largeScreenContainer.requestFullscreen();
    },
    handleWindowResize() {
      this.$devopParentTypeFun(this.isFullScreen);

      this.windowResizeTimer && window.clearTimeout(this.windowResizeTimer);
      this.windowResizeTimer = window.setTimeout(() => {
        this.computedBodyContent();
      }, 200);
    },

    // ----------------------- 其他方法 ----------------------------
    computedBodyContent() {
      let containWidth =
          this.$refs.largeScreenContainer.clientWidth ||
          this.$refs.largeScreenContainer.offsetWidth,
        containHeight =
          this.$refs.largeScreenContainer.clientHeight ||
          this.$refs.largeScreenContainer.offsetHeight;

      let widthRadio = containWidth / 1920,
        heightRadio = containHeight / 1080,
        radio = widthRadio < heightRadio ? widthRadio : heightRadio;
      this.$refs.largeScreenBody.setAttribute(
        'style',
        `transform: translate(-50%, -50%) scale(${radio});
                -webkit-transform:  translate(-50%, -50%) scale(${radio});
            `
      );
    },
    //设置定时刷新
    setRefreshTimer() {
      this.pageRefreshTimer && setTimeout(this.pageRefreshTimer);
      this.pageRefreshTimer = setTimeout(() => {
        this.setRefreshTimer();
        if (this.$route.path != '/workSheet/largeScreen') {
          return;
        }
        this.refresh();
      }, 60000);
    },
    refreshTime() {
      this.nowTime =
        this.$dayjs().format('YYYY-MM-DD HH:mm') +
        ' 星期' +
        ['日', '一', '二', '三', '四', '五', '六'][this.$dayjs().day()];
    },
    clearCache() {
      this.intervalTimer && clearInterval(this.intervalTimer);
      this.pageRefreshTimer && clearInterval(this.pageRefreshTimer);
      this.handingRefreshTimer && clearInterval(this.handingRefreshTimer);
      window.removeEventListener('resize', this.handleWindowResize);
    }
  },
  beforeDestroy() {
    this.clearCache();
  }
};
</script>

<style lang="scss" scoped>
@import url('../comment/css/workSheetCommont.scss');
.large-screen-body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  background-image: url('../../../assets/img/workSheet/largeScreen/backgroundImg.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -moz-background-size: 100% 100%;

  .large-screen-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transform-origin: center;
    width: 1920px;
    height: 1080px;

    padding-top: 15px;
  }

  .screen-title-box {
    width: 1920px;
    height: 60px;
    margin-bottom: 27px;
    position: relative;
    color: #fff;
    font-weight: 600;
    color: #ffffff;
    line-height: 33px;

    // background-image: url('../../../assets/img/workSheet/largeScreen/title_background_image.png');
    // background-repeat: no-repeat;
    // background-size: 100% 100%;
    // -moz-background-size: 100% 100%;

    .screen-title {
      width: 690px;
      padding: 0 20px;
      background-image: url(../../../assets/img/workSheet/largeScreen/title_bg_img.png);
      background-size: 100% 100%;
      > div {
        font-size: 31px;
        font-weight: 600;
        color: transparent;
        line-height: 50px;
        height: 60px;
        background-image: linear-gradient(180deg, #2fb1ff 0%, #23f9ff 100%);
        -webkit-background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      img {
        width: 31px;
        height: 31px;
        border-radius: 50%;
      }
      .dot {
        background-color: #e7e517;
        border-radius: 50%;
        width: 6px;
        height: 6px;
      }
    }
    .screen-refresh {
      padding-left: 20px;
      line-height: 70px;
      height: 50px;
      background-image: url('../../../assets/img/workSheet/largeScreen/title_background_left.png');
      background-size: 100% 100%;
    }
    .screen-time {
      display: flex;
      justify-content: flex-end;
      padding-right: 20px;
      font-size: 26px;
      height: 50px;
      line-height: 70px;
      background-image: url('../../../assets/img/workSheet/largeScreen/title_background_right.png');
      background-size: 100% 100%;
    }
  }
}

.screen-col-top {
  height: 164px;
  margin-bottom: 20px;
  > div:first-child {
    margin-right: 20px;
  }
}
.screen-card {
  background: rgba(11, 57, 109, 0.3);
  box-shadow: 0px 0px 10px 0px #103062 inset;
}
.screen-content {
  padding: 0 20px;
}
.screen-bottom-col-1 > div:not(:last-child) {
  margin-bottom: 20px;
}
.screen-bottom-col-2 {
  margin: 0 20px;
}
.today-duty-officer {
  min-height: 85px;
  width: 360px;
  padding: 16px;
  img {
    width: 200px;
  }
  div {
    font-weight: 600;
    color: #fff;
    line-height: 28px;
    margin-left: 24px;
    font-size: 18px;
    &:first-child {
      margin-left: 0;
      color: rgba(255, 255, 255, 0.4);
    }
  }
}
.trasen-logo {
  width: 360px;
  height: 38px;
}
</style>
