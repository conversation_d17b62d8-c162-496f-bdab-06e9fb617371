<template>
  <div class="content flex-col-center">
    <div class="today-creat flex-column flex-col-center">
      <div class="data-title">今日建单</div>
      <div class="data">{{ todayCreatCount }}</div>
    </div>
    <div class="line"></div>
    <div class="flex-grow flex flex-row-between">
      <template v-for="(item, index) of orderStatusList">
        <div class="flex-column flex-col-center" :key="index" v-if="item.title">
          <div class="data-title">{{ item.title }}</div>
          <div :class="item.class" class="data">{{ dataList[index] }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    todayCreatCount: {
      type: Number,
      default: () => 0
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      orderStatusList: [
        {
          title: '待派单',
          class: 'error-toast'
        },
        {
          title: '待接收',
          class: 'error-toast'
        },
        {
          title: '处理中'
        },
        {
          title: '待验收'
        },
        {
          title: '已完成',
          class: 'sucess-toast'
        },
        {
          title: '已暂停',
          class: 'warning-toast'
        },
        {
          title: '已终止',
          class: 'warning-toast'
        }
      ]
    };
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 1400px;
  height: 164px;
  padding: 32px;
  padding-right: 40px;
}
.line {
  width: 2px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(
    180deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  margin: 0 45px;
}
.data-title {
  font-weight: 600;
  color: #88c1ff;
  line-height: 32px;
  font-size: 32px;
}
.data {
  line-height: 71px;
  font-size: 58px;
  font-weight: 600;
  color: #fff;
}
.today-creat .data {
  color: #2cf3ff;
}
.error-toast {
  color: #ff6068;
}
.sucess-toast {
  color: #33d647;
}
.warning-toast {
  color: #e7e517;
}
</style>
