<template>
  <div class="content flex-col-center flex-column">
    <div class="title">当前值班人员</div>
    <div class="line"></div>
    <div class="flex-col-center" style="width: 100%;">
      <img
        :src="require('@/assets/img/workSheet/largeScreen/duty-icon.png')"
        class="dept-icon"
      />
      <div class="flex-wrap flex flex-grow duty-list-content">
        <div v-for="(item, index) of dutyList" :key="index" class="duty-item">
          {{ item }}
        </div>
        <div class="empty-content" v-if="!dutyList.length">
          未找到当前值班人员信息
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dutyList: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 360px;
  padding-top: 10px;
  padding-bottom: 16px;
}
.title {
  font-weight: 600;
  color: #ffffff;
  line-height: 32px;
  font-size: 24px;
}
.line {
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );
  border-radius: 50%;
  margin-top: 4px;
  margin-bottom: 8px;
}
.duty-item {
  font-size: 20px;
  font-weight: 600;
  color: #ffffff;
  margin-right: 24px;
}
.dept-icon {
  height: 44px;
  width: 44px;
  margin: 0 24px;
  margin-right: 15px;
  flex-shrink: 0;
}
.empty-content {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 600;
}
</style>
