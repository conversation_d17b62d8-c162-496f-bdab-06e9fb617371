<template>
  <div class="content flex-column flex-col-center">
    <div class="title">本月工单质量</div>
    <div class="line"></div>
    <div class="flex-grow pie-content" style="width: 100%;">
      <div class="quality-ranking flex-column">
        <div class="ranking-title">质量TOP3</div>
        <div class="ranking-list flex-column flex-grow ">
          <template v-for="(item, index) of rankingList">
            <div :key="index" class="ranking-item flex-col-center">
              <img :src="rankingImgList[index]" />
              <div>
                <div>{{ item.name }}</div>
                <div>{{ item.grade }}分</div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="pie">
        <div ref="pie" style="width: 100%; height: 100%;"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    dataObject: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      rankingImgList: [
        require('@/assets/img/workSheet/largeScreen/order_quality_no_1.png'),
        require('@/assets/img/workSheet/largeScreen/order_quality_no_2.png'),
        require('@/assets/img/workSheet/largeScreen/order_quality_no_3.png')
      ],

      rankingList: [],
      pie: null
    };
  },
  watch: {
    dataObject: {
      handler: function(newVal) {
        let { list, avg, rankingList } = newVal;
        this.rankingList = rankingList;
        this.$nextTick(() => {
          this.renderPie(list, avg);
        });
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.pie = this.$echarts.init(this.$refs.pie);
    });
  },
  methods: {
    renderPie(data, avg) {
      let options = {
        series: [
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['35%', '55%'],
            data: [{ value: 1, name: '' }],
            showEmptyCircle: false,
            hoverAnimation: false,
            color: 'transparent',
            label: {
              borderWidth: 0,
              //饼图中间文字设置
              normal: {
                show: true,
                position: 'center',
                textBorderColor: 'transparent',
                textBorderWidth: 0,
                formatter: function() {
                  return `{a|${avg || 0}}\n{b|综合评分}`;
                },
                rich: {
                  a: {
                    fontSize: 24,
                    lineHeight: 32,
                    fontWeight: 600,
                    color: '#F65656',
                    marginBotttom: 6
                  },
                  b: {
                    color: '#88C1FF',
                    lineHeight: 22,
                    fontSize: 16,
                    fontWeight: 600
                  }
                }
              },
              emphasis: {
                //中间文字显示
                show: true
              }
            }
          },
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['35%', '55%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
            data: data,
            minAngle: 30,
            labelLine: {
              length2: 0
            },
            label: {
              formatter: function(params) {
                // let int = params.percent.toFixed(0);
                let int = params.percent;
                return `${params.name}\n${int}%`;
              },
              // padding: [0, -10],
              fontSize: 16,
              fontWeight: 600,
              lineHeight: 22,
              textBorderColor: 'transparent',
              textBorderWidth: 0,
              color: '#FFF'
            }
          }
        ],
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%'
        }
      };

      if (!this.pie) {
        this.pie = this.$echarts.init(this.$refs.pie);
      }
      this.pie.clear();
      this.pie.setOption(options);
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 460px;
  height: 350px;
}
.title {
  margin-top: 12px;
  font-size: 24px;
  color: #ffffff;
  line-height: 32px;
  font-weight: 600;
}
.line {
  border-radius: 50%;
  width: 180px;
  height: 2px;
  background: linear-gradient(
    270deg,
    rgba(54, 229, 255, 0) 0%,
    #36e5ff 52%,
    rgba(54, 229, 255, 0) 100%
  );

  margin-top: 4px;
  margin-bottom: 27px;
}
.quality-ranking {
  margin-bottom: 43px;
  margin-left: 17px;
}
.ranking-title {
  font-weight: 600;
  color: #88c1ff;
  line-height: 22px;
  font-size: 18px;
  margin-bottom: 24px;
  white-space: nowrap;
}
.ranking-item:not(:last-child) {
  margin-bottom: 19px;
}
.ranking-item img {
  width: 22px;
  height: 36px;
  margin-right: 12px;
}
.ranking-item > div div {
  font-weight: 600;
  color: #ffffff;
  line-height: 25px;
  font-size: 18px;
  max-width: 72px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.pie-content {
  position: relative;
}
.pie {
  position: absolute;
  right: -15px;
  top: 0;
  width: 85%;
  height: 100%;
}
</style>
