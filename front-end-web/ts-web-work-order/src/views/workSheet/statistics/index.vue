<template>
  <div class="content ts-container">
    <el-tabs v-model="active" type="card">
      <el-tab-pane name="1" label="工单处理情况" class="situation-content">
        <OrderHandle ref="paneContent1"></OrderHandle>
      </el-tab-pane>
      <!-- <el-tab-pane name="2" label="故障分类统计">
        <FaultType ref="paneContent2"></FaultType>
      </el-tab-pane> -->
      <el-tab-pane name="2" label="时效性统计">
        <Timeliness ref="paneContent2" />
      </el-tab-pane>
      <el-tab-pane name="3" label="完成质量分析">
        <Finished ref="paneContent3"></Finished>
      </el-tab-pane>
      <el-tab-pane name="4" label="知识点分析">
        <Knowledge ref="paneContent4"></Knowledge>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import orderHandleSituation from './orderHandleSituation.vue'; //工单处理情况
import faultTypeSituation from './faultTypeSituation.vue'; //故障分类统计
import finishedSituation from './finishedSituation.vue'; //完成质量分析
import knowledgeSituation from './knowledgeSituation.vue'; //知识点分析
import timeliness from './timeliness.vue'; //时效性统计

export default {
  components: {
    OrderHandle: orderHandleSituation,
    // FaultType: faultTypeSituation,
    Finished: finishedSituation,
    Knowledge: knowledgeSituation,
    Timeliness: timeliness
  },
  data() {
    return {
      active: '1'
    };
  },
  watch: {
    active: function(newVal, oldVal) {
      this.$refs[`paneContent${newVal}`].refreshByTabChange();
    }
  },
  methods: {
    refresh() {
      let refName = 'paneContent' + this.active;
      refName ? this.$refs[refName].refreshByTabChange() : null;
    }
  }
};
</script>

<style scoped lang="scss">
.content {
  height: 100%;
  /deep/.el-tabs.el-tabs--card.el-tabs--top {
    height: 100%;
    .el-tabs__content {
      overflow: auto;
      height: calc(100% - 56px);
    }
  }
  /deep/ .el-tabs__item {
    width: 110px;
  }

  /deep/.el-tab-pane {
    height: 100%;
  }
}
/deep/ {
  .inform-title {
    font-weight: 600;
    color: #333333;
    line-height: 22px;
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background: #5260ff;
      margin-right: 3px;
    }
  }
}
</style>
