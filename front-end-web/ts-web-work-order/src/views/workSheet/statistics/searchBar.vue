<template>
  <div class="search-content flex flex-col-center">
    <div class="flex flex-col-center">
      统计时间：
      <el-date-picker
        type="daterange"
        v-model="dates"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        @change="handleTimesChange"
      >
      </el-date-picker>
      <el-button
        class="trasen-btn trasen-perpul"
        style="margin-left: 8px;"
        @click="refresh"
      >
        搜索
      </el-button>
    </div>
    <el-radio-group
      v-model="quickSelectIndex"
      class="quick-select-content"
      @change="handleQuickSelectChange"
    >
      <el-radio-button label="0">今日</el-radio-button>
      <el-radio-button label="1">本周</el-radio-button>
      <el-radio-button label="2">本月</el-radio-button>
      <el-radio-button label="3">本年</el-radio-button>
      <el-radio-button label="4">全部</el-radio-button>
    </el-radio-group>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dates: [], //当前选中的统计时间
      oldSearchVal: 2, //旧的搜索数据
      quickSelectIndex: 2 //快捷搜索的下标
    };
  },
  methods: {
    //刷新数据
    refresh() {
      this.$emit('refresh', this.getSearchData());
    },
    //获取搜索数据
    getSearchData() {
      let searchData = {};
      if (this.dates.length) {
        let [beginTime, endTime] = this.dates;
        this.oldSearchVal = [...this.dates];
        beginTime += ' 00:00:00';
        endTime += ' 23:59:59';
        //计算相差时间

        let differTime = this.$dayjs(endTime).diff(
          this.$dayjs(beginTime),
          'day'
        );

        searchData = {
          type: 5,
          beginTime,
          endTime
        };

        //根据相差时间传给后端
        if (differTime > 365) {
          searchData.dayOrMonthType = 3;
        } else if (differTime > 31) {
          searchData.dayOrMonthType = 1;
        } else {
          searchData.dayOrMonthType = 0;
        }
      } else {
        searchData.type = this.quickSelectIndex;
        this.oldSearchVal = this.quickSelectIndex;
        if (this.quickSelectIndex == 3) {
          searchData.dayOrMonthType = 1;
        } else if (this.quickSelectIndex == 4) {
          searchData.dayOrMonthType = 3;
        } else {
          searchData.dayOrMonthType = 0;
        }
      }

      return searchData;
    },
    //处理时间改变
    handleTimesChange(vals) {
      if (vals && vals.length) {
        this.quickSelectIndex = null;
      } else {
        this.dates = [];
        // Array.isArray(this.oldSearchVal)
        //   ? setTimeout(() => {
        //       this.dates = this.oldSearchVal;
        //     }, 50)
        //   : (this.quickSelectIndex = this.oldSearchVal);
        this.quickSelectIndex = 4;
      }
      this.refresh();
    },
    //处理快捷选择改变
    handleQuickSelectChange() {
      this.dates = [];
      this.refresh();
    },
    blurs() {}
  }
};
</script>

<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.search-content {
  min-width: 858px;
  overflow: auto;
  margin-bottom: 16px;
  margin-left: 8px;
  /deep/.el-range-separator {
    width: auto;
  }
  .quick-select-content {
    margin-left: 16px;
    /deep/ .el-radio-button__inner {
      line-height: 28px;
      min-width: 60px;
      padding: 0 8px;
      height: 30px;
      font-size: 14px;
      border-left: none;
      border-right: none;
    }
    /deep/ .el-radio-button:last-child .el-radio-button__inner {
      border-right: 1px solid rgb(220, 223, 230);
    }
    /deep/ .el-radio-button:first-child .el-radio-button__inner {
      border-left: 1px solid rgb(220, 223, 230);
    }
    /deep/ .el-radio-button::after {
      content: '';
      background-color: rgb(220, 223, 230);
      height: 16px;
      width: 1px;
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
      z-index: 155;
    }
    /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #fff;
      box-shadow: none;
      color: #5260ff;
      font-weight: 600;
      border-color: rgb(220, 223, 230);
    }
  }
}
</style>
