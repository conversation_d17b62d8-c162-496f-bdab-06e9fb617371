<template>
  <div class="order-handle-situation-body">
    <SearchBar ref="searchBar" @refresh="refresh"></SearchBar>
    <div class="inform-content">
      <div class="flex" style="min-width: 1090px;">
        <div class="inform-title inform-col-1">
          建单及办结趋势
          <select-popover
            v-if="statisticsData"
            v-model="orderQualityDept"
            :options="depts"
            style="margin-left: 40px;"
          ></select-popover>
        </div>
        <!-- <div
          class="inform-title inform-col-2"
          style="height: 22px; margin-top: 0;"
        >
          各状态占比
        </div> -->
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="inform-col-1" style="position: relative;">
          <div class="sub-and-finished-top flex">
            <div class="top-item">
              {{ titleComputed }}
              <div>
                <span class="count">{{ subAndFinishedTopList[0] || 0 }}</span>
                单
              </div>
            </div>
            <div class="top-item">
              处理完成
              <div>
                <span class="count">{{ subAndFinishedTopList[1] || 0 }}</span>
                单
              </div>
            </div>
            <div class="top-item" v-if="isBhsdermyyOrgName">
              <el-tooltip
                class="item"
                effect="dark"
                content="总未完成数据统计：包含所有待派单、待接单、处理中、已暂停工单。"
                placement="top-start"
              >
                <img class="item-tips" src="@/assets/img/dd_tips.svg" alt="" />
              </el-tooltip>
              总未完成
              <div>
                <span class="count">{{ unfinishedNumber || '0' }}</span>
                单
              </div>
            </div>
          </div>
          <!-- 建单及办结趋势图 -->
          <div ref="subAndFinishTrend" class="cell"></div>
        </div>

        <div class="inform-col-2 flex flex-column">
          <!-- 各状态占比饼图 -->
          <div ref="eachStatuPie" class="cell"></div>
        </div>
      </div>

      <div class="flex" style="min-width: 1090px; margin-top: 16px;">
        <div class="inform-title inform-col-1">
          科室-人员接单及处理详情
        </div>
        <div
          class="inform-title inform-col-2"
          style="height: 22px; margin-top: 0;"
        >
          <div
            class="flex flex-row-between"
            style="font-size: 16px; font-weight: 600; color: #333; width: 100%;"
          >
            提单情况
            <div class="flex">
              <div
                class="orderSub-selections-item"
                :class="active == 'repair_type' ? 'selection-active' : ''"
                @click="active = 'repair_type'"
              >
                报修方式
              </div>
              <div
                class="orderSub-selections-item"
                :class="active == 'fault_emergency' ? 'selection-active' : ''"
                @click="active = 'fault_emergency'"
              >
                紧急程度
              </div>
              <div
                class="orderSub-selections-item"
                :class="
                  active == 'fault_affect_scope' ? 'selection-active' : ''
                "
                @click="active = 'fault_affect_scope'"
              >
                影响范围
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="inform-row flex flex-col-center">
        <div
          class="inform-col-1 has-grey-background"
          style="margin-right: 16px;"
        >
          <!-- 科室-人员接单及处理详情 -->
          <CellTable ref="table" v-bind="tableProps"></CellTable>
        </div>
        <div class="inform-col-2 tab-content has-grey-background">
          <div class="flex-grow">
            <div
              ref="repaireTypePie"
              class="cell"
              v-show="active == 'repair_type'"
            ></div>
            <div
              ref="emergencyPie"
              class="cell"
              v-show="active == 'fault_emergency'"
            ></div>
            <div
              ref="affectPie"
              class="cell"
              v-show="active == 'fault_affect_scope'"
            ></div>
          </div>
        </div>
      </div>

      <div class="flex" style="min-width: 1090px; margin-top: 16px;">
        <div class="inform-title flex flex-col-center inform-col-1">
          科室提单情况
          <i
            class="el-icon-setting setting-icon"
            @click="showSettingInput = !showSettingInput"
          ></i>
          <div
            :class="
              showSettingInput ? 'setting-input-open' : 'setting-input-close'
            "
          >
            <span>设置基准线： </span>
            <el-input-number
              v-model="referenceVal"
              :min="0"
              :max="maxReference"
              :step-strictly="true"
              controls-position="right"
              placeholder="基准值"
            ></el-input-number>
          </div>
        </div>
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="inform-col-1">
          <!-- 科室提单情况 -->
          <div ref="deptSubOrderCell" class="cell"></div>
        </div>
      </div>

      <!-- 人员接口情况 -->
      <div class="flex" style="min-width: 1090px; margin-top: 16px;">
        <div class="inform-title inform-col-1">
          <div
            class="flex flex-row-between"
            style="font-size: 16px; font-weight: 600; color: #333; width: 100%; align-items: center"
          >
            人员接单情况
            <el-button @click="handleExportWorkSheetDatas">导出</el-button>
          </div>
        </div>
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="inform-col-1">
          <!-- 科室提单情况 -->
          <CellTable ref="tablePerson" v-bind="tablePropsPerson"></CellTable>
        </div>
      </div>

      <div class="flex" style="min-width: 1090px; margin-top: 16px;">
        <div class="inform-title inform-col-1">一级故障分类</div>
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="inform-col-1">
          <!-- 一级故障分类   柱状图 -->
          <div ref="no1FaultTypeCell" class="cell"></div>
        </div>
        <div class="inform-col-2">
          <!-- 一级故障分类   饼图 -->
          <div ref="no1FaultTypePie" class="cell"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import cellTable from '../comment/components/cellTabel.vue'; //表格数据组件
import searchBar from './searchBar.vue'; //头部搜索栏
import api from '@/api/ajax/workSheetSetUp';
import data from '../workOrderHomePage/Minix/data';
import selectPopover from './selectPopover.vue';
import JsExportExcel from 'js-export-excel';

export default {
  components: {
    CellTable: cellTable,
    SearchBar: searchBar,
    selectPopover
  },
  data() {
    return {
      API: {
        getSubAndFinishApi:
          '/ts-worksheet/statisticsData/getWorkOrderProcessing', //建单和完成趋势数据
        getEachStatusApi:
          '/ts-worksheet/statisticsData/getWorkGroupByWorkStatusDatas', //获取各状态占比
        getSubOrderApi: '/ts-worksheet/statisticsData/getDeptCountDatas', //提单情况
        getWorkOrderUnfinished:
          '/ts-worksheet/statisticsData/getWorkOrderUnfinished' //总未完成工单
      },
      unfinishedNumber: '', // 总未完成工单

      active: 'repair_type', //第二个饼状图的当前展示，默认选中报修方式
      showSettingInput: true, //是否展示设置inpu框
      referenceVal: null, //基准值
      maxReference: 0, //最大基准值
      referenceChangeTimer: null, //基准值改变计时器，避免快速点击

      orderQualityDept: '', //工单处理科室选择
      depts: [], //工单处理科室

      subAndFinishCell: null, //办结及提单趋势图
      subAndFinishedTopList: [], //提单及办结趋势头部图
      eachStatuPie: null, //各状态占比饼图
      tableProps: {
        hiddenActionCell: true, //隐藏操作栏
        columns: [
          {
            label: '科室/外单位',
            prop: 'fk_user_dept_name',
            minWidth: 120
          },
          {
            label: '合计',
            prop: 'sum',
            align: 'right',
            width: 90
          },
          {
            label: '待派单',
            prop: 'dispatch',
            align: 'right',
            width: 70
          },
          {
            label: '待接单',
            prop: 'waiting',
            align: 'right',
            width: 70
          },
          {
            label: '处理中',
            prop: 'processing',
            align: 'right',
            width: 70
          },
          {
            label: '待验收',
            prop: 'acceptance',
            align: 'right',
            width: 70
          },
          {
            label: '待评价',
            prop: 'evaluate',
            align: 'right',
            width: 70
          },
          {
            label: '已完结',
            prop: 'completed',
            align: 'right',
            width: 70
          },
          {
            label: '已暂停',
            prop: 'suspended',
            align: 'right',
            width: 70
          },
          {
            label: '已终止',
            prop: 'terminat',
            align: 'right',
            width: 70
          }
        ],
        refresh: data => {
          return this.refreshTable(data);
        }
      },
      tablePropsPerson: {
        hiddenActionCell: true, //隐藏操作栏
        columns: [
          {
            label: '工号',
            prop: 'fk_user_id',
            align: 'center',
            minWidth: 70
          },
          {
            label: '姓名',
            prop: 'fk_user_name',
            align: 'center',
            minWidth: 70
          },
          {
            label: '单位/科室',
            prop: 'fk_user_dept_name',
            align: 'center',
            minWidth: 120
          },
          {
            label: '待接单',
            prop: 'waiting',
            align: 'center',
            width: 70
          },
          {
            label: '处理中',
            prop: 'processing',
            align: 'center',
            width: 70
          },
          {
            label: '待验收',
            prop: 'acceptance',
            align: 'center',
            width: 70
          },
          {
            label: '待评价',
            prop: 'evaluate',
            align: 'center',
            width: 70
          },
          {
            label: '已完结',
            prop: 'completed',
            align: 'center',
            width: 70
          },
          {
            label: '已暂停',
            prop: 'suspended',
            align: 'center',
            width: 70
          },
          {
            label: '已终止',
            prop: 'terminat',
            align: 'center',
            width: 70
          },
          {
            label: '合计',
            prop: 'sum',
            align: 'center',
            width: 90
          }
        ],
        refresh: data => {
          return this.refreshTablePerson(data);
        }
      },
      deptSubOrderCell: null, //科室提单情况
      repaireTypePie: null, //报修方式饼图
      emergencyPie: null, //紧急程度饼图
      affectPie: null, //影响范围饼图
      no1FaultTypeCell: null, //一级故障分类——柱状图
      no1FaultTypePie: null, //一级故障分类——饼图

      oldSearchVal: 2 //旧的搜索数据
    };
  },
  mounted() {
    //首次赋值
    this.subAndFinishCell = this.$echarts.init(this.$refs.subAndFinishTrend);
    this.eachStatuPie = this.$echarts.init(this.$refs.eachStatuPie);
    this.deptSubOrderCell = this.$echarts.init(this.$refs.deptSubOrderCell);
    this.repaireTypePie = this.$echarts.init(this.$refs.repaireTypePie);
    this.emergencyPie = this.$echarts.init(this.$refs.emergencyPie);
    this.affectPie = this.$echarts.init(this.$refs.affectPie);
    this.no1FaultTypeCell = this.$echarts.init(this.$refs.no1FaultTypeCell);
    this.no1FaultTypePie = this.$echarts.init(this.$refs.no1FaultTypePie);

    window.addEventListener('resize', () => {
      this.resizeAll();
    });

    this.$refs.searchBar.refresh();
  },
  computed: {
    statisticsData() {
      return this.$store.state.common.userMessage.statisticsData;
    },
    isBhsdermyyOrgName() {
      return this.$getParentStoreInfo('globalSetting')?.orgCode === 'bhsdermyy';
    },
    titleComputed() {
      return this.isBhsdermyyOrgName ? '建单' : '总建单';
    }
  },
  watch: {
    active: function(newVal) {
      this.handleActiveChange(newVal);
    },
    referenceVal: function(newVal) {
      this.referenceChangeTimer && clearTimeout(this.referenceChangeTimer);
      this.referenceChangeTimer = setTimeout(() => {
        this.handleReferenceChange(newVal);
      }, 300);
    },
    orderQualityDept: {
      handler(val) {
        this.refreshOrderQuality();
      }
    }
  },
  methods: {
    //由于tab变化刷新数据
    async refreshByTabChange() {
      try {
        const res = await api.selectOmMeauList();
        if (res.success === false) {
          throw res.message;
        }
        this.depts =
          res.object.map(item => {
            return { label: item.deptName, value: item.deptId };
          }) || [];
        this.depts.length == 1
          ? (this.orderQualityDept = this.depts[0].value)
          : this.depts.unshift({ value: '', label: '全部' });

        if (
          this.depts.some(
            item => item.value == this.$store.state.common.userInfo.orgId
          )
        ) {
          this.orderQualityDept = this.$store.state.common.userInfo.orgId;
        }
      } catch (e) {
        this.$message.error(e || '出错啦');
      }
      this.$refs.searchBar.refresh();
      this.$nextTick(() => {
        this.resizeAll();
      });
    },
    //刷新数据
    refresh(data) {
      let searchData = data;

      !this.statisticsData &&
        (searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId);

      //提单及办结趋势以及各状态占比图刷新
      this.refreshOrderQuality();

      //科室人员接单及处理详情表格刷新
      this.$refs.table.refreshTable(); //刷新表格

      //人员接单及处理详情表格刷新
      this.$refs.tablePerson.refreshTable(); //刷新表格

      //报修方式刷新
      this.$api({
        url: this.API.getEachStatusApi,
        method: 'post',
        data: { ...searchData, statusType: 'repair_type' },
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        let datas = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        if (Array.isArray(res.object)) {
          datas = res.object.filter(item => item.value);
        }
        this.renderTreePie(datas, this.repaireTypePie);
      });

      //故障紧急程度刷新
      this.$api({
        url: this.API.getEachStatusApi,
        method: 'post',
        data: { ...searchData, statusType: 'fault_emergency' },
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        let datas = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        if (Array.isArray(res.object)) {
          datas = res.object.filter(item => item.value);
        }
        this.renderTreePie(datas, this.emergencyPie);
      });

      //故障影响范围刷新
      this.$api({
        url: this.API.getEachStatusApi,
        method: 'post',
        data: { ...searchData, statusType: 'fault_affect_scope' },
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        let datas = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        if (Array.isArray(res.object)) {
          datas = res.object.filter(item => item.value);
        }
        this.renderTreePie(datas, this.affectPie);
      });

      //科室提单情况刷新
      this.$api({
        url: this.API.getSubOrderApi,
        method: 'get',
        data: {
          ...searchData,
          sidx: 'total',
          sord: 'desc'
        }
      }).then(res => {
        let data = [],
          cols = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }

        (res.object || []).forEach(item => {
          data.push(item.total);
          cols.push(item.name);
          if (item.total > this.maxReference) {
            this.maxReference = Number(item.total);
          }
        });
        if (!data.length && !cols.length) {
          data = [0];
          // cols = new Array(12).fill('').map((item, index) => index + 1);
          cols = [''];
        }
        this.referenceVal = Math.max(...data) / 2;
        this.renderDeptSubOrderCell(data, cols);
      });

      //一级故障类型渲染
      this.$api({
        url: '/ts-worksheet/statisticsData/getLevelOneFaultTypeDatasToDate',
        method: 'get',
        params: searchData
      }).then(res => {
        let totalData = [],
          successData = [],
          pieData = [],
          cols = [];

        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }

        (res.object || []).forEach(item => {
          if (Number(item.total) > 0) {
            totalData.push(Number(item.total) || '');
            successData.push(item.wcTotal || '');
            cols.push(item.categoryName);

            pieData.push({
              name: item.categoryName,
              value: item.total
            });
          }
        });

        if (!totalData.length) {
          totalData = [0];
          cols = [''];
        }

        this.testRenderCell(
          totalData,
          successData,
          cols,
          this.no1FaultTypeCell
        );
        this.renderEachStatuPie(pieData, this.no1FaultTypePie);
      });
    },
    //重置所有表格大小
    resizeAll() {
      this.subAndFinishCell.resize();
      this.eachStatuPie.resize();
      this.deptSubOrderCell.resize();
      this.repaireTypePie.resize();
      this.emergencyPie.resize();
      this.affectPie.resize();
      this.no1FaultTypeCell.resize();
      this.no1FaultTypePie.resize();
    },
    //表格导出，所见即所得
    handleExportWorkSheetDatas() {
      let { col, data } = this.$refs.tablePerson.getTableData(),
        headers = [],
        filters = [];

      (col || []).forEach(item => {
        headers.push(item.label);
        filters.push(item.prop);
      });

      let options = {
        fileName: '人员接单情况',
        datas: [
          {
            sheetData: data,
            sheetFilter: filters,
            sheetHeader: headers
          }
        ]
      };
      var toExcel = new JsExportExcel(options);
      toExcel.saveExcel();
      return;
    },
    refreshTablePerson(data) {
      let searchData = {
        ...data,
        ...this.$refs.searchBar.getSearchData(),
        sidx: 'sum',
        sord: 'desc'
      };
      !this.statisticsData &&
        (searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId);

      return this.$api({
        url: '/ts-worksheet/statisticsData/getDeptUserReceiveWorkSheetDatas',
        method: 'get',
        data: searchData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '出错啦');
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //刷新表格数据
    refreshTable(data) {
      let searchData = { ...data, ...this.$refs.searchBar.getSearchData() };
      !this.statisticsData &&
        (searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId);

      return this.$api({
        url: '/ts-worksheet/statisticsData/getDeptReceiveWorkSheetDatas',
        method: 'get',
        data: searchData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '出错啦');
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //刷新提单及办结趋势以及各状态占比
    refreshOrderQuality() {
      let searchData = this.$refs.searchBar.getSearchData();

      if (this.statisticsData) {
        this.orderQualityDept
          ? (searchData.fkUserDeptId = this.orderQualityDept)
          : null;
      } else {
        searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId;
      }

      //获取总未完成工单
      if (this.isBhsdermyyOrgName) {
        this.$api({
          url: this.API.getWorkOrderUnfinished,
          method: 'post',
          data: searchData,
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '数据加载失败');
          }
          this.unfinishedNumber = res.object;
        });
      }

      //建单及办结趋势刷新
      this.$api({
        url: this.API.getSubAndFinishApi,
        method: 'post',
        data: searchData,
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        let subList = [],
          finishedList = [],
          cols = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        let subCount = 0,
          finishedCount = 0,
          object = res.object || {};
        let index =
          object.dayOrMonthType == 0 ? 2 : object.dayOrMonthType == 1 ? 1 : 0;
        (object.list || []).forEach(item => {
          subCount += Number(item.tdcounts);
          finishedCount += Number(item.bjcounts);

          subList.push(item.tdcounts);
          finishedList.push(item.bjcounts);
          let dateList = item.date.split('-');
          cols.push(dateList[index]);
        });
        if (!subList.length) {
          subList = [0];
        }
        this.subAndFinishedTopList = [subCount, finishedCount];
        this.renderSubAndFinishedTrendCell(subList, finishedList, cols);
      });

      //各状态占比图刷新
      this.$api({
        url: this.API.getEachStatusApi,
        method: 'post',
        data: { ...searchData, statusType: 'work_status' },
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        let datas = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        if (Array.isArray(res.object)) {
          datas = res.object.filter(item => item.value);
        }
        this.renderEachStatuPie(datas, this.eachStatuPie);
      });
    },
    //----------------------- 事件处理 ---------------------
    //监听active改变，解决echarts因tab改变导致显示错误问题
    handleActiveChange(val) {
      let options = {};
      switch (val) {
        case 'repair_type':
          options = this.repaireTypePie.getOption();
          this.$nextTick(() => {
            this.repaireTypePie.resize();
            this.repaireTypePie.clear();
            this.repaireTypePie.setOption(options);
          });
          break;
        case 'fault_emergency':
          options = this.emergencyPie.getOption();
          this.$nextTick(() => {
            this.emergencyPie.resize();
            this.emergencyPie.clear();
            this.emergencyPie.setOption(options);
          });
          break;
        case 'fault_affect_scope':
          options = this.affectPie.getOption();
          this.$nextTick(() => {
            this.affectPie.resize();
            this.affectPie.clear();
            this.affectPie.setOption(options);
          });
          break;
        default:
          break;
      }
    },
    //处理基准值改变事件，重新渲染基准线
    handleReferenceChange(val) {
      let data = [];
      if (val) {
        data = [
          {
            value: val,
            yAxis: val
          }
        ];
      }

      this.deptSubOrderCell.setOption({
        series: [
          {
            id: 'deptSubOrderData',
            markLine: {
              data
            }
          }
        ]
      });
    },
    //----------------------- 内部方法 ----------------------
    //渲染提单及办结趋势
    renderSubAndFinishedTrendCell(subList, finishedList, cols) {
      let options = {
        legend: {
          icon: 'circle',
          itemWidth: 10,
          top: '10%'
        },
        xAxis: [
          {
            name: '日期',
            data: cols || [],
            // data: [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              // interval: 0
            }
          }
        ],
        yAxis: [
          {
            name: '工单数',
            nameTextStyle: {
              // fontSize:
            },
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            name: this.titleComputed,
            type: 'line',
            data: subList,
            symbol:
              subList.length == 1 && subList[0] == 0 && !finishedList.length
                ? 'none'
                : 'circle',
            itemStyle: {
              normal: {
                color: '#5260ff',
                lineStyle: {
                  color: '#5260ff'
                }
              }
            },
            label: {
              show: true
            }
          },
          {
            name: '处理完成',
            type: 'line',
            data: finishedList,
            symbol: 'circle',
            itemStyle: {
              normal: {
                color: '#F03C6E',
                lineStyle: {
                  color: '#F03C6E'
                }
              }
            },
            label: {
              show: true
            }
          }
        ],
        grid: {
          top: '23%',
          left: '3%',
          right: '8%',
          bottom: '0%',
          containLabel: true
        }
      };
      if (!this.subAndFinishCell) {
        this.subAndFinishCell = this.$echarts.init(
          this.$refs.subAndFinishTrend
        );
        window.addEventListener('resize', () => {
          this.subAndFinishCell.resize();
        });
      }
      this.subAndFinishCell.clear();
      this.subAndFinishCell.setOption(options);
    },
    //渲染状态占比
    renderEachStatuPie(data, content) {
      let options = {
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>${params.value}个`;
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 12,
          orient: 'vertical',
          x: 'right',
          y: '25%',
          textStyle: {
            width: 84,
            overflow: 'truncate'
          },
          tooltip: {
            show: true
          }
        },
        series: [
          {
            type: 'pie',
            center: ['40%', '45%'],
            radius: ['25%', '45%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
            labelLine: {
              show: true,
              length2: 0
            },
            label: {
              show: true,
              formatter: function(params) {
                let int = params.percent.toFixed(0);
                return `${params.name}\n${int}%`;
              },
              fontSize: 16,
              fontStyle: 'normal',
              align: 'left',
              padding: [0, 0],
              width: 80, //文字显示宽度
              rich: {
                a: {
                  fontSize: 16,
                  lineHeight: 22
                }
              }
            },
            data: data,
            emphasis: { scale: true },
            showEmptyCircle: true,
            emptyCircleStyle: {
              color: '#E9F0FF'
            }
          }
        ],
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(options);
    },
    //渲染报修方式、紧急程度、影响范围
    renderTreePie(data, pieBox) {
      let options = {
        legend: {
          icon: 'circle',
          itemWidth: 12,
          orient: 'vertical',
          x: 'right',
          y: '25%',
          tooltip: {
            show: true
          }
        },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>${params.value}个`;
          }
        },
        series: [
          {
            type: 'pie',
            center: ['40%', '45%'],
            radius: ['25%', '45%'],
            labelLine: {
              show: true,
              length2: 0
            },
            label: {
              show: true,
              formatter: function(params) {
                let int = params.percent.toFixed(0);
                return `${params.name}\n${int}%`;
              },
              fontSize: 16,
              fontStyle: 'normal',
              align: 'left',
              padding: [0, 0],
              rich: {
                a: {
                  fontSize: 16,
                  lineHeight: 22
                }
              }
            },
            emphasis: {
              scale: true
            },
            showEmptyCircle: true,
            emptyCircleStyle: {
              color: '#E9F0FF'
            },
            data: data
          }
        ],
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        }
      };
      pieBox.clear();
      pieBox.setOption(options);
    },
    //渲染科室提单情况
    renderDeptSubOrderCell(data, cols) {
      let option = {
        xAxis: [
          {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            name: '次数',
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            id: 'deptSubOrderData',
            type: 'bar',
            data: data,
            barWidth: 50,
            label: {
              show:
                data.length == 1 &&
                data[0] == 0 &&
                cols.length == 1 &&
                cols[0] == ''
                  ? false
                  : true,
              position: 'top'
            },
            markLine: {
              symbol: 'none',
              label: {
                show: true,
                position: 'insideEndTop',
                color: '#f9c659',
                formatter: function(param) {
                  return `基准线：${param.value}`;
                }
              },
              lineStyle: {
                color: '#f9c659',
                width: 2,
                type: 'solid'
              },
              data: [
                {
                  value: 52,
                  yAxis: 50
                }
              ]
            }
          }
        ],
        grid: {
          top: '15%',
          left: '8px',
          right: '8px',
          bottom: '0%',
          containLabel: true
        }
      };

      this.deptSubOrderCell.clear();
      this.deptSubOrderCell.setOption(option);
      this.referenceVal
        ? this.deptSubOrderCell.setOption({
            series: [
              {
                id: 'deptSubOrderData',
                markLine: {
                  data: {
                    value: this.referenceVal,
                    yAxis: this.referenceVal
                  }
                }
              }
            ]
          })
        : null;
    },
    //渲染一级故障分类柱状图
    testRenderCell(totalData, successData, cols, content) {
      let option = {
        legend: {
          data: ['总数', '完成数'],
          icon: 'circle',
          itemWidth: 12
        },
        xAxis: [
          {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0
            }
          }
        ],
        yAxis: [
          {
            name: '工单数',
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            name: '总数',
            type: 'bar',
            data: totalData,
            barWidth: 25,
            label: {
              show:
                totalData.length == 1 &&
                totalData[0] == 0 &&
                cols.length == 1 &&
                cols[0] == ''
                  ? false
                  : true,
              position: 'top'
            }
          },
          {
            name: '完成数',
            type: 'bar',
            data: successData,
            barWidth: 25,
            label: {
              show: true,
              position: 'top'
            }
          }
        ],
        grid: {
          top: '15%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(option);
    }
  }
};
</script>

<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.order-handle-situation-body {
  height: 100%;
  overflow: auto;
}
.inform-content {
  width: 100%;
  height: calc(100% - 52px);
  overflow: auto;
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 10px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background-color: rgba(153, 153, 153, 0.8);
    }
  }
  &:hover::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  .inform-row {
    // margin-top: 20px;
    min-width: 1090px;
    width: 100%;
    height: 320px;
  }
  .inform-col-1 {
    height: 100%;
    flex: 1;
  }
  .inform-col-2 {
    // background-color: pink;
    width: 500px;
    margin: 20px 0;
    height: 100%;
    > div {
      height: 100%;
      width: 100%;
    }
  }
  .inform-title {
    font-weight: 600;
    color: #333333;
    line-height: 22px;
    font-size: 14px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background: #5260ff;
      margin-right: 3px;
    }
  }
}
.cell {
  height: 100%;
  width: 100%;
}
.sub-and-finished-top {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 99999999;
  .top-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #666;
    border-radius: 17px;
    padding-right: 40px;
    > div {
      color: #5260ff;
      font-size: 12px;
    }
    .count {
      margin-left: 8px;
      font-size: 24px;
      color: #5260ff;
      line-height: 30px;
      font-weight: 600;
    }
  }
}
/deep/.div__body {
  padding: 0;
  height: 100%;
  width: 100%;
}
.tab-content {
  /deep/.el-tabs__nav {
    // width: 99%;
    width: 100%;
  }
  /deep/.el-tabs__item {
    // width: 33%;
    width: 33.3333333%;
    text-align: center;
  }
  /deep/.el-tabs.el-tabs--card.el-tabs--top {
    height: 100%;
  }
  /deep/.el-tab-pane {
    height: 100%;
  }
  /deep/.el-tabs__content {
    width: 100%;
    min-width: 100% !important;
    height: calc(100% - 56px);
  }
}
.title {
  margin: 10px;
  line-height: 32px;
  margin-top: 0;
  font-weight: 600;
  font-size: 16px;
}
.setting-icon {
  margin: 0 10px;
  font-size: 24px;
}
.setting-input-open {
  -webkit-animation: openInRow 0.2s ease-in 1;
  animation: openInRow 0.2s ease-in 1;
  transform-origin: left;
}
@-webkit-keyframes openInRow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}
@keyframes openInRow {
  0% {
    transform: scaleX(0);
  }
  100% {
    transform: scaleX(1);
  }
}
.setting-input-close {
  -webkit-animation: closeInRow 0.2s ease-in 1;
  animation: closeInRow 0.2s ease-in 1;
  transform-origin: left;
  transform: scale(0);
}
@-webkit-keyframes closeInRow {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}
@keyframes closeInRow {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}
.orderSub-selections-item {
  font-size: 14px;
  font-weight: 400;
  color: #666666;
  line-height: 20px;
  position: relative;
  padding: 0 8px;
  cursor: pointer;
  &:not(:last-child)::after {
    content: '';
    background-color: #d4d8e8;
    height: 16px;
    width: 1px;
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    z-index: 155;
    margin-left: 8px;
  }
  &:hover {
    color: #5260ff;
  }
}
.orderSub-selections-item.selection-active {
  color: #5260ff;
  font-weight: 600;
}
.order-quality-select {
  /deep/ input {
    border: none;
    color: #5260ff;
    text-align: right;
  }
}
.inform-content .has-grey-background {
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e7ebf0;
  padding: 8px;
}
/deep/ {
  .table::-webkit-scrollbar-track,
  .el-table__body-wrapper {
    background: #fafafa;
  }
}
.export-button {
  border: 1px solid #bbbbbb;
  color: #101010;
  font-size: 14px;
  line-height: 30px;
  width: 80px;
  text-align: center;
}
.item-tips {
  width: 20px;
  height: 20px;
  transform: translateY(-2px);
}
</style>
