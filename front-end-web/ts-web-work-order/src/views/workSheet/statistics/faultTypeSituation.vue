<template>
  <div class="fault-type-situation-body">
    <SearchBar ref="searchBar" @refresh="refresh"></SearchBar>
    <div class="inform-content">
      <div class="inform-row flex flex-col-center">
        <el-card class="inform-col-1">
          <div class="col cell flex flex-column">
            <div class="title">一级故障分类</div>
            <!-- 一级故障分类   柱状图 -->
            <div class="flex-grow">
              <div ref="no1FaultTypeCell" class="cell"></div>
            </div>
          </div>
        </el-card>
        <el-card class="inform-col-2 flex-grow col">
          <!-- 一级故障分类   饼图 -->
          <div ref="no1FaultTypePie" class="cell"></div>
        </el-card>
      </div>

      <div class="inform-row flex flex-col-center">
        <el-card class="inform-col-1 col">
          <div class="col cell flex flex-column">
            <div class="title">信息设备故障分类详情统计</div>
            <!-- 信息设备故障分类详情统计   柱状图 -->
            <div class="flex-grow">
              <div ref="informationFaultTypeCell" class="cell"></div>
            </div>
          </div>
        </el-card>
        <el-card class="inform-col-2 flex-grow col">
          <!-- 信息设备故障分类详情统计   饼图 -->
          <div ref="informationFaultTypePie" class="cell"></div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import searchBar from './searchBar.vue'; //搜索栏组件

export default {
  components: {
    SearchBar: searchBar
  },
  data() {
    return {
      no1FaultTypeCell: null, //一级故障分类——柱状图
      no1FaultTypePie: null, //一级故障分类——饼图
      informationFaultTypeCell: null, //信息设备故障分类详情统计-柱状图
      informationFaultTypePie: null //信息设备故障分类详情统计-饼图
    };
  },
  mounted() {
    this.no1FaultTypeCell = this.$echarts.init(this.$refs.no1FaultTypeCell);
    this.no1FaultTypePie = this.$echarts.init(this.$refs.no1FaultTypePie);
    this.informationFaultTypeCell = this.$echarts.init(
      this.$refs.informationFaultTypeCell
    );
    this.informationFaultTypePie = this.$echarts.init(
      this.$refs.informationFaultTypePie
    );

    window.addEventListener('resize', () => {
      this.resizeAll();
    });
    this.$refs.searchBar.refresh();
  },
  methods: {
    //由于tab变化刷新数据
    refreshByTabChange() {
      this.$refs.searchBar.refresh();
      this.$nextTick(() => {
        this.resizeAll();
      });
    },
    //刷新数据
    refresh(data) {
      //一级故障类型渲染
      this.$api({
        url: '/ts-worksheet/statisticsData/getLevelOneFaultTypeDatasToDate',
        method: 'get',
        data
      }).then(res => {
        let totalData = [],
          successData = [],
          pieData = [],
          cols = [];

        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }

        (res.object || []).forEach(item => {
          totalData.push(Number(item.total) || '');
          successData.push(item.wcTotal || '');

          cols.push(item.categoryName);

          if (Number(item.total) > 0) {
            pieData.push({
              name: item.categoryName,
              value: item.total
            });
          }
        });

        this.testRenderCell(
          totalData,
          successData,
          cols,
          this.no1FaultTypeCell
        );

        this.renderPie(pieData, this.no1FaultTypePie);
      });

      //信息设备故障分类数据统计
      this.$api({
        url: '/ts-worksheet/statisticsData/getFaultEquipment',
        method: 'get',
        data
      }).then(res => {
        let data = [],
          pieData = [],
          wcData = [],
          cols = [];

        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }

        (res.object || []).forEach(item => {
          data.push(Number(item.total) || '');
          cols.push(item.equipmentName);
          wcData.push(item.wsTotal || '');

          if (Number(item.total) > 0) {
            pieData.push({
              name: item.equipmentName,
              value: item.total
            });
          }
        });
        this.renderCell(data, wcData, cols, this.informationFaultTypeCell);
        this.renderPie(pieData, this.informationFaultTypePie);
      });
    },
    //重置所有表格大小
    resizeAll() {
      this.no1FaultTypeCell.resize();
      this.no1FaultTypePie.resize();
      this.informationFaultTypeCell.resize();
      this.informationFaultTypePie.resize();
    },
    //渲染柱状图
    testRenderCell(totalData, successData, cols, content) {
      let option = {
        legend: {
          data: ['总数', '完成数']
        },
        xAxis: [
          {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0
            }
          }
        ],
        yAxis: [
          {
            name: '工单数',
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            name: '总数',
            type: 'bar',
            data: totalData,
            barWidth: 25,
            label: {
              show: true,
              position: 'top'
            }
          },
          {
            name: '完成数',
            type: 'bar',
            data: successData,
            barWidth: 25,
            label: {
              show: true,
              position: 'top'
            }
          }
        ],
        grid: {
          top: '15%',
          left: '10%',
          right: '10%',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(option);
    },
    //渲染柱状图
    renderCell(data, wcData, cols, content) {
      let option = {
        legend: {
          data: ['总数', '完成数']
        },
        xAxis: [
          {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              interval: 0,
              // rotate: -40,
              formatter: function(value) {
                let ret = ''; //拼接加\n返回的类目项
                let maxLength = 4; //每项显示文字个数
                let valLength = value.length; //X轴类目项的文字个数
                let rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                if (rowN > 1) {
                  //如果类目项的文字大于3,
                  for (let i = 0; i < rowN; i++) {
                    let temp = ''; //每次截取的字符串
                    let start = i * maxLength; //开始截取的位置
                    let end = start + maxLength; //结束截取的位置
                    //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                    temp = value.substring(start, end) + '\n';
                    ret += temp; //凭借最终的字符串
                  }
                  return ret;
                } else {
                  return value;
                }
              }
            }
          }
        ],
        yAxis: [
          {
            name: '工单数',
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            name: '总数',
            type: 'bar',
            data: data || [],
            barWidth: 15,
            label: {
              show: true,
              position: 'top'
            }
          },
          {
            name: '完成数',
            type: 'bar',
            data: wcData || [],
            barWidth: 15,
            label: {
              show: true,
              position: 'top'
            }
          }
        ],
        grid: {
          top: '20%',
          left: '10%',
          right: '10%',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(option);
    },
    //渲染饼图
    renderPie(data, content) {
      let option = {
        legend: {
          type: 'scroll'
        },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: function(params) {
            return `${params.value}个`;
          }
        },
        series: [
          {
            type: 'pie',
            center: ['50%', '55%'],
            radius: ['35%', '60%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
            labelLine: {
              show: true,
              length: 20,
              length1: 0
            },
            label: {
              // formatter: '{a|{d}}%\n{c}',
              formatter: function(params) {
                let int = params.percent.toFixed(0);
                if (int > 0) {
                  return `${params.name}\n{a|${int}%}`;
                } else {
                  return '';
                }
              },
              fontSize: 16,
              fontStyle: 'normal',
              // color: '#FFFFFF',
              align: 'left',
              padding: [0, 0],
              // width: 500, //文字显示宽度
              // overflow: 'break', //超出部分换行显示
              rich: {
                a: {
                  fontSize: 16,
                  lineHeight: 22
                }
              }
            },
            data: data
          }
        ],
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(option);
    }
  }
};
</script>

<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.fault-type-situation-body {
  height: 100%;
  overflow: auto;
}
.search-content {
  margin-bottom: 20px;
  /deep/.el-range-separator {
    width: auto;
  }
  .quick-select-content {
    margin-left: 20px;
    /deep/ .el-radio-button__inner {
      line-height: 28px;
      min-width: 60px;
      padding: 0 8px;
      height: 30px;
      font-size: 14px;
    }
  }
}
.inform-content {
  width: 100%;
  height: calc(100% - 52px);
  overflow: auto;
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 10px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background: rgba(153, 153, 153, 0.8);
    }
  }
  &::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  .inform-row {
    // margin-top: 20px;
    min-width: 1090px;
    width: 100%;
    height: 400px;
    padding: 10px;
  }
  .inform-col-1 {
    width: 60%;
    // background-color: blue;
    height: 100%;
    margin-right: 20px;
  }
  .inform-col-2 {
    // background-color: pink;
    margin: 20px 0;
    height: 100%;
    > div {
      height: 100%;
      width: 100%;
    }
  }
}
.cell {
  height: 100%;
  width: 100%;
}
.title {
  margin: 10px;
  line-height: 32px;
  margin-top: 0;
  font-weight: 600;
  font-size: 16px;
}
/deep/.el-card__body {
  padding: 0;
  height: 100%;
  width: 100%;
}
.col {
  padding: 10px;
  // box-shadow: 0 0 3px #000;
}
</style>
