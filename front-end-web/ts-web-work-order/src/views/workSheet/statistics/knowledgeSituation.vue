<template>
  <div class="knowledge-situation-body">
    <SearchBar ref="searchBar" @refresh="refresh"></SearchBar>
    <div class="inform-content">
      <div class="flex" style="min-width: 1090px;">
        <div class="inform-col-1 inform-title">知识点提交趋势</div>
        <div class="inform-title inform-col-2" style="width: 495px;">
          一级分类占比
        </div>
      </div>
      <div class="inform-row flex flex-col-center">
        <div class="inform-col-1 has-grey-background">
          <div class="cell col">
            <!-- 知识点提交趋势   柱状图 -->
            <div ref="knowledgeSubCell" class="cell"></div>
          </div>
        </div>
        <div class="inform-col-2 has-grey-background">
          <div class="cell">
            <!-- 一级分类占比   饼图 -->
            <div ref="no1FaultTypePie" class="cell"></div>
          </div>
        </div>
      </div>

      <div class="inform-title" style="margin-top: 8px;">提交排名Top20</div>
      <div class="inform-row">
        <div class="cell has-grey-background">
          <div ref="subTop20" class="cell"></div>
        </div>
      </div>

      <div class="inform-title" style="margin-top: 8px;">总点赞排名Top20</div>
      <div class="inform-row has-grey-background">
        <div class="cell">
          <div ref="usefulTop20" class="cell"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import searchBar from './searchBar.vue'; //搜索栏组件

export default {
  components: {
    SearchBar: searchBar
  },
  data() {
    return {
      subTop20: null, // 知识点提交top20 柱状图
      usefulTop20: null, // 点赞top20 柱状图
      no1FaultTypePie: null, // 一级分类占比 饼图
      knowledgeSubCell: null //知识点提交趋势 柱状图
    };
  },
  mounted() {
    this.knowledgeSubCell = this.$echarts.init(this.$refs.knowledgeSubCell);
    this.no1FaultTypePie = this.$echarts.init(this.$refs.no1FaultTypePie);
    this.usefulTop20 = this.$echarts.init(this.$refs.usefulTop20);
    this.subTop20 = this.$echarts.init(this.$refs.subTop20);

    window.addEventListener('resize', () => {
      this.resizeAll();
    });
  },
  methods: {
    //由于tab变化刷新数据
    refreshByTabChange() {
      this.$refs.searchBar.refresh();
      this.$nextTick(() => {
        this.resizeAll();
      });
    },
    //刷新数据
    refresh(data) {
      //知识点提交趋势折线图渲染
      this.$api({
        url: '/ts-worksheet/statisticsData/getKnowledgeBaseCountByDate',
        method: 'get',
        data
      }).then(res => {
        let data = [],
          cols = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }

        let object = res.object || {};
        let index =
          object.dayOrMonthType == 0 ? 2 : object.dayOrMonthType == 1 ? 1 : 0;

        (object.list || []).forEach(item => {
          let dates = item.date.split('-');

          cols.push(parseInt(dates[index]));
          data.push(item.counts);
        });
        this.renderKnowlegeSubCell(data, cols);
      });
      //一级分类占比饼状图
      this.$api({
        url: '/ts-worksheet/statisticsData/getLevelOneKnowledgeBaseTypeDatas',
        method: 'get',
        data
      }).then(res => {
        let data = [];
        if (!res.success) {
          this.$message.error(res.message || '数据加载失败');
        }
        (res.object || []).forEach(item => {
          if (Number(item.counts) > 0) {
            data.push({
              name: item.categoryName,
              value: item.counts
            });
          }
        });
        this.renderNo1FaultTypePie(data);
      });

      //提交排名Top20
      this.$api({
        url: '/ts-worksheet/statisticsData/getKnowledgeBaseSubmitTopDatas',
        method: 'get',
        data: { ...data, pageSize: 20, pageNo: 1 }
      }).then(res => {
        let data = [],
          cols = [];
        if (res.success == false) {
          this.$message.error(res.message || '数据加载失败');
        }
        (res.rows || []).forEach(item => {
          cols.push(item.fkUserName);
          data.push(item.total);
        });
        this.renderCell(data, cols, this.subTop20);
      });

      //点赞排名Top20
      this.$api({
        url: '/ts-worksheet/statisticsData/getKnowledgeLikeCountTop',
        method: 'get',
        data: { ...data, pageSize: 20, pageNo: 1 }
      }).then(res => {
        let data = [],
          cols = [];
        if (res.success == false) {
          this.$message.error(res.message || '数据加载失败');
        }
        (res.rows || []).forEach(item => {
          cols.push(item.fkUserName);
          data.push(item.total);
        });
        this.renderCell(data, cols, this.usefulTop20);
      });
    },
    //重置所有表格大小
    resizeAll() {
      this.knowledgeSubCell.resize();
      this.no1FaultTypePie.resize();
      this.usefulTop20.resize();
      this.subTop20.resize();
    },
    //喧嚷知识点提交趋势线性表
    renderKnowlegeSubCell(data = [], cols = []) {
      let options = {
        xAxis: [
          {
            name: '日期',
            data: cols.length ? cols : [''],
            // data: [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              // interval: 0
            }
          }
        ],
        yAxis: [
          {
            name: '知识点数量',
            nameTextStyle: {
              // fontSize:
            },
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            type: 'line',
            data: data.length ? data : [0],
            symbol: data.length ? 'circle' : 'none',
            label: {
              show: true
            }
          }
        ],
        grid: {
          top: '32px',
          left: '8px',
          right: '8px',
          bottom: '0',
          containLabel: true
        }
      };

      this.knowledgeSubCell.clear();
      this.knowledgeSubCell.setOption(options);
    },
    //一级分类占比饼状图
    renderNo1FaultTypePie(data) {
      let isNull = true;
      data.map(item => {
        isNull = isNull && item.value == 0;
      });

      let option = {
        legend: {
          type: 'scroll',
          icon: 'circle',
          itemWidth: 12,
          orient: 'vertical',
          x: 'right',
          y: '25%',
          textStyle: {
            width: 84,
            overflow: 'truncate'
          },
          tooltip: {
            show: true
          }
        },
        tooltip: {
          show: isNull ? false : true,
          trigger: 'item',
          formatter: function(params) {
            return `${params.name}<br/>${params.value}个`;
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        }
      };
      let seriesItem = {
        type: 'pie',
        center: isNull ? ['50%', '50%'] : ['40%', '45%'],
        radius: ['25%', '45%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
        labelLine: {
          show: isNull ? false : true,
          length2: 0
        },
        data: data,
        showEmptyCircle: true
      };

      if (isNull) {
        data[0] = { value: 1 };
        seriesItem.hoverAnimation = false;
        seriesItem.color = ['#E9F0FF'];
        seriesItem.label = {
          formatter: function(params) {
            if (params.value != 1) {
              return '';
            }
          },
          fontSize: 16,
          fontStyle: 'normal',
          align: 'left',
          padding: [0, 0]
        };
        seriesItem.emphasis = {
          scale: false,
          itemStyle: {
            color: '#E9F0FF'
          }
        };
        delete option.legend;
      } else {
        seriesItem.label = {
          formatter: function(params) {
            let int = params.percent.toFixed(0);
            if (int > 0) {
              return `${params.name}\n{a|${int}%}`;
            } else {
              return '';
            }
          },
          fontSize: 16,
          fontStyle: 'normal',
          align: 'left',
          padding: [0, 0],
          // overflow: 'break', //超出部分换行显示
          rich: {
            a: {
              fontSize: 16,
              lineHeight: 22
            }
          }
        };
      }
      option.series = [seriesItem];

      this.no1FaultTypePie.clear();
      this.no1FaultTypePie.setOption(option);
    },
    //渲染柱状图
    renderCell(data = [], cols = [], content) {
      let option = {
        xAxis: [
          {
            data: cols.length ? cols : [''],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            }
          }
        ],
        yAxis: [
          {
            name: '数量',
            axisLabel: {
              show: true
            }
          }
        ],
        series: [
          {
            type: 'bar',
            data: data.length ? data : [0],
            barWidth: 50,
            label: {
              show: data.length ? true : false,
              position: 'top'
            }
          }
        ],
        grid: {
          top: '15%',
          left: '8px',
          right: '8px',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(option);
    }
  }
};
</script>

<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.knowledge-situation-body {
  height: 100%;
}
.inform-content {
  width: 100%;
  height: calc(100% - 52px);
  overflow: auto;
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 10px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background-color: rgba(153, 153, 153, 0.8);
    }
  }
  &:hover::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  .inform-row {
    // margin-top: 20px;
    min-width: 1090px;
    width: 100%;
    height: 320px;
  }
  .inform-col-1 {
    // background-color: blue;
    height: 100%;
    margin-right: 16px;
    flex: 1;
  }
  .inform-col-2 {
    // background-color: pink;
    // margin: 20px 0;
    width: 500px;
    height: 100%;
    > div {
      height: 100%;
      width: 100%;
    }
  }
}
.cell {
  height: 100%;
  width: 100%;
}
.title {
  margin: 10px;
  line-height: 32px;
  margin-top: 0;
  font-weight: 600;
  font-size: 16px;
}
/deep/.el-card__body {
  padding: 0;
  height: 100%;
  width: 100%;
}
.has-grey-background {
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e7ebf0;
  padding: 8px;
}
</style>
