<template>
  <div class="finished-situation-body">
    <SearchBar ref="searchBar" @refresh="refresh"></SearchBar>

    <div class="inform-content">
      <div class="flex">
        <div class="inform-title inform-col-1">
          科室工单质量
          <select-popover
            v-if="statisticsData"
            v-model="orderQualityDept"
            :options="depts"
            style="margin-left: 40px;"
          ></select-popover>
        </div>
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="inform-col-1">
          <div class="cell flex flex-column">
            <!-- 完成得分   饼图 -->
            <div class="flex-grow">
              <div
                ref="finishedScorePie"
                v-show="scoreActive == 0"
                class="cell"
              ></div>
              <div
                ref="finishedSpeedPie"
                v-show="scoreActive == 1"
                class="cell"
              ></div>
              <div
                ref="finishedAttitudePie"
                v-show="scoreActive == 2"
                class="cell"
              ></div>
              <div
                ref="finishedTecPie"
                v-show="scoreActive == 3"
                class="cell"
              ></div>
            </div>
            <div class="flex flex-row-evenly box-tab-content">
              <div
                class="tab-box flex flex-column flex-center"
                @click="scoreActive = 0"
                :class="{ active: scoreActive == 0 }"
              >
                <span>{{ (scoreList[0] || 0).toFixed(1) }}</span>
                总评分
              </div>

              <div
                class="tab-box flex flex-column flex-center"
                @click="scoreActive = 1"
                :class="{ active: scoreActive == 1 }"
              >
                <span>{{ (scoreList[1] || 0).toFixed(1) }}</span>
                处理速度
              </div>

              <div
                class="tab-box flex flex-column flex-center"
                @click="scoreActive = 2"
                :class="{ active: scoreActive == 2 }"
              >
                <span>{{ (scoreList[2] || 0).toFixed(1) }}</span>
                服务态度
              </div>

              <div
                class="tab-box flex flex-column flex-center"
                @click="scoreActive = 3"
                :class="{ active: scoreActive == 3 }"
              >
                <span>{{ (scoreList[3] || 0).toFixed(1) }}</span>
                技术水平
              </div>
            </div>
          </div>
        </div>
        <div class="inform-col-2 flex-grow">
          <!-- 完成得分   折线图 -->
          <div ref="finishedScoreLine" class="cell"></div>
        </div>
      </div>

      <div
        class="flex-row-between flex-col-center"
        style="margin-bottom: 8px; width: calc(100% - 1px);"
      >
        <div class="inform-title" style="margin-bottom: 0;">服务质量排名</div>
        <el-radio-group
          v-model="serviceActive"
          class="service-action-selections"
        >
          <el-radio-button label="0">总得分</el-radio-button>
          <el-radio-button label="1">处理速度</el-radio-button>
          <el-radio-button label="2">服务态度</el-radio-button>
          <el-radio-button label="3">技术水平</el-radio-button>
          <el-radio-button label="4">平均用时</el-radio-button>
        </el-radio-group>
      </div>
      <div class="inform-row flex flex-col-center has-grey-background">
        <div class="cell flex flex-column">
          <div
            class="flex-grow"
            ref="serviceScoreBar"
            v-show="serviceActive == 0"
          ></div>
          <div
            class="flex-grow"
            ref="serviceSpeedBar"
            v-show="serviceActive == 1"
          ></div>
          <div
            class="flex-grow"
            ref="serviceAttitudeBar"
            v-show="serviceActive == 2"
          ></div>
          <div
            class="flex-grow"
            ref="serviceTecLevelBar"
            v-show="serviceActive == 3"
          ></div>
          <div
            class="flex-grow"
            ref="serviceTimeBar"
            v-show="serviceActive == 4"
          ></div>
        </div>
      </div>

      <div class="flex title" style="margin: 8px 0;min-width: 1090px;">
        <div class="inform-title" style="margin-bottom: 0;">
          人员服务质量情况
        </div>
        <div
          class="flex flex-grow table-search-content flex-row-between"
          style="margin-left: 8px;"
        >
          <div class="flex">
            <InputTree
              placeholder="被评价人科室"
              :treeData="deptList"
              class="table-search-input-tree"
              v-model="evaluationDept"
              @change="handleTableSearch"
            ></InputTree>
            <el-autocomplete
              v-model="beEvaluationName"
              :fetch-suggestions="beEvaluationSearch"
              :clearable="true"
              @select="handleAutoComSelect"
              value-key="label"
              placeholder="被评价人姓名"
            ></el-autocomplete>
            <el-button
              class="trasen-btn trasen-perpul"
              @click="handleTableSearch"
              >搜索</el-button
            >
          </div>
          <el-button
            type="primary"
            @click="handleTableExport"
            class="trasen-btn"
            >导出</el-button
          >
        </div>
      </div>
      <div class="inform-row bigger-row has-grey-background">
        <div class="cell flex flex-column">
          <div class="flex-grow">
            <CellTabel
              ref="table"
              v-bind="tableOption"
              @refresh="refreshTable"
            ></CellTabel>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import searchBar from './searchBar.vue'; //搜索栏组件
import cellTabel from '../comment/components/cellTabel.vue'; //表格组件
import inputTree from '@/components/input-tree/inputTree.vue'; //可本地搜索下拉树选择框
import api from '@/api/ajax/workSheetSetUp';
import JsExportExcel from 'js-export-excel';
import selectPopover from './selectPopover.vue';

export default {
  components: {
    SearchBar: searchBar,
    CellTabel: cellTabel,
    InputTree: inputTree,
    selectPopover
  },
  data() {
    return {
      scoreList: [], //得分列表，用来渲染四个tab
      depts: [], //维修科室列表
      scoreActive: 0, //得分表格选中的状态值
      serviceActive: 0, //服务类型排名tab选中状态值

      orderQualityDept: '', //工单质量搜索选择框值

      finishedScorePie: null, //完成得分 饼图
      finishedSpeedPie: null, //处理速度 饼图
      finishedAttitudePie: null, //服务态度 饼图
      finishedTecPie: null, //技术水平 饼图
      finishedScoreLine: null, //完成得分 折线图
      serviceScoreBar: null, //服务质量得分排名 柱状图
      serviceSpeedBar: null, //服务处理速度排名 柱状图
      serviceAttitudeBar: null, //服务态度排名 柱状图
      serviceTecLevelBar: null, //服务技术水平排名 柱状图
      serviceTimeBar: null, //服务平均用时排名 柱状图

      deptList: [], //评价人科室选择树
      evaluationDept: null, //评价人科室 表格搜索
      beEvaluationName: null, //被评价人姓名 表格搜索
      tabelFkUserId: null, //被评价人ID 搜索后选择
      beEvaInputTimer: null, //被评价人输入防抖
      tableOption: {
        hiddenActionCell: true,
        columns: [
          {
            label: '工号',
            prop: 'employeeNo'
          },
          {
            label: '姓名',
            prop: 'fkUserName'
          },
          {
            label: '单位/科室',
            prop: 'fkUserDeptName'
          },
          {
            label: '办结量',
            align: 'right',
            prop: 'bjl'
          },
          {
            label: '综合得分',
            align: 'right',
            prop: 'avgSum',
            formatter: function(row, option, cell) {
              return cell.toFixed(1);
            }
          },
          {
            label: '处理速度',
            align: 'right',
            prop: 'avgProcessSpeed',
            formatter: function(row, option, cell) {
              return cell.toFixed(1);
            }
          },
          {
            label: '服务态度',
            align: 'right',
            prop: 'avgServiceAttituude',
            formatter: function(row, option, cell) {
              return cell.toFixed(1);
            }
          },
          {
            label: '技术水平',
            align: 'right',
            prop: 'avgTechnicalLevel',
            formatter: function(row, option, cell) {
              return cell.toFixed(1);
            }
          },
          {
            label: '平均用时(H)',
            align: 'right',
            width: 100,
            prop: 'avgWorkHours',
            formatter: function(row, option, cell) {
              return cell.toFixed(1);
            }
          },
          {
            label: '验收通过率',
            align: 'right',
            prop: 'tgl',
            formatter: function(row, option, cell) {
              return cell.toFixed(1) + '%';
            }
          },
          {
            label: '未完成数',
            prop: 'wwc',
            align: 'right'
          },
          {
            label: '未完成占比',
            prop: 'wwczb',
            align: 'right',
            formatter: (row, option, cell) => {
              return cell + '%';
            }
          }
        ]
      }
    };
  },
  computed: {
    statisticsData() {
      return this.$store.state.common.userMessage.statisticsData;
    }
  },
  created() {
    this.$api({
      url: '/ts-basics-bottom/organization/getTree',
      method: 'post'
    }).then(res => {
      if (!res.success) {
        this.$message.error(res.message || '出错啦');
        return;
      }
      this.deptList = res.object || [];
    });
  },
  mounted() {
    this.finishedScorePie = this.$echarts.init(this.$refs.finishedScorePie);
    this.finishedScoreLine = this.$echarts.init(this.$refs.finishedScoreLine);
    this.serviceScoreBar = this.$echarts.init(this.$refs.serviceScoreBar);
    this.serviceAttitudeBar = this.$echarts.init(this.$refs.serviceAttitudeBar);
    this.serviceSpeedBar = this.$echarts.init(this.$refs.serviceSpeedBar);
    this.serviceTecLevelBar = this.$echarts.init(this.$refs.serviceTecLevelBar);
    this.serviceTimeBar = this.$echarts.init(this.$refs.serviceTimeBar);
    this.finishedSpeedPie = this.$echarts.init(this.$refs.finishedSpeedPie);
    this.finishedAttitudePie = this.$echarts.init(
      this.$refs.finishedAttitudePie
    );
    this.finishedTecPie = this.$echarts.init(this.$refs.finishedTecPie);

    window.addEventListener('resize', () => {
      this.resizeAll();
    });
  },
  watch: {
    scoreActive: function(newVal, oldVal) {
      this.handleScoreActionChange(newVal);
    },
    serviceActive: function(newVal, oldVal) {
      this.handleServiceActionChange(newVal);
    },
    orderQualityDept: {
      handler(val) {
        this.refreshOrderQuality();
      }
    },
    beEvaluationName: function() {
      this.handleTableSearch();
    }
  },
  methods: {
    //由于tab变化刷新数据
    async refreshByTabChange() {
      try {
        const res = await api.selectOmMeauList();
        if (res.success === false) {
          throw res.message;
        }
        this.depts =
          res.object.map(item => {
            return { label: item.deptName, value: item.deptId };
          }) || [];
        this.depts.length == 1
          ? (this.orderQualityDept = this.depts[0].value)
          : this.depts.unshift({ value: '', label: '全部' });

        if (
          this.depts.some(
            item => item.value == this.$store.state.common.userInfo.orgId
          )
        ) {
          this.orderQualityDept = this.$store.state.common.userInfo.orgId;
        }
      } catch (e) {
        this.$message.error(e || '出错啦');
      }

      this.$refs.searchBar.refresh();
      this.$nextTick(() => {
        this.resizeAll();
      });
    },
    //重置所有表格
    resizeAll() {
      this.finishedScorePie.resize();
      this.finishedScoreLine.resize();
      this.serviceScoreBar.resize();
      this.serviceSpeedBar.resize();
      this.serviceAttitudeBar.resize();
      this.serviceTecLevelBar.resize();
      this.serviceTimeBar.resize();
      this.finishedSpeedPie.resize();
      this.finishedAttitudePie.resize();
      this.finishedTecPie.resize();
    },
    //刷新数据
    refresh(opt) {
      let searchData = opt;
      !this.statisticsData &&
        (searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId);

      //表格刷新
      this.handleTableSearch();

      this.refreshOrderQuality();

      // 四个维度柱状图刷新;
      let barList = [
        this.serviceScoreBar,
        this.serviceSpeedBar,
        this.serviceAttitudeBar,
        this.serviceTecLevelBar
      ];
      for (let i = 0; i < 4; i++) {
        this.$api({
          url: '/ts-worksheet/statisticsData/getDeptEvaluationAverageScore',
          method: 'get',
          data: { ...searchData, evaluationType: i }
        }).then(res => {
          let data = [],
            cols = [];
          if (res.success == false) {
            this.$message.error(res.message || '数据加载失败');
          }
          (res.object || {}).forEach(item => {
            data.push(item.avg);
            cols.push(item.fkUserDeptName);
          });
          if (res.object && !res.object.length) {
            data = [0];
            cols = [''];
          }

          this.renderServiceBar(data, cols, barList[i]);
        });
      }

      //平均用时刷新
      this.$api({
        url: '/ts-worksheet/statisticsData/getDeptQualityOfService',
        method: 'get',
        data: searchData
      }).then(res => {
        let data = [],
          cols = [];
        if (res.success == false) {
          this.$message.error(res.message || '数据加载失败');
        }
        (res.object || []).forEach(item => {
          data.push(item.avgWorkHours);
          cols.push(item.fkUserDeptName || '数据错误');
        });
        if (res.object && !res.object.length) {
          data = [0];
          cols = [''];
        }

        this.renderServiceBar(data, cols, this.serviceTimeBar);
      });
    },
    //刷新工单质量
    refreshOrderQuality() {
      let searchData = this.$refs.searchBar.getSearchData();

      if (this.statisticsData) {
        this.orderQualityDept
          ? (searchData.fkUserDeptId = this.orderQualityDept)
          : null;
      } else {
        searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId;
      }

      //四个维度得分刷新
      this.$api({
        url: '/ts-worksheet/statisticsData/geTScoreAnalysis',
        method: 'get',
        data: searchData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '数据加载失败');
        }
        let object = res.object || {};
        this.scoreList = [
          object.avgScore || 0,
          object.avgProcessSpeed || 0,
          object.avgServiceAttituude || 0,
          object.avgTechnicalLevel || 0
        ];
      });

      //四个维度得分 饼图 刷新
      let pieList = [
        this.finishedScorePie,
        this.finishedSpeedPie,
        this.finishedAttitudePie,
        this.finishedTecPie
      ];
      for (let i = 0; i < 4; i++) {
        this.$api({
          url: '/ts-worksheet/statisticsData/getEvaluationLevel',
          method: 'get',
          data: { ...searchData, evaluationType: i }
        }).then(res => {
          if (res.success == false) {
            this.$message.error(res.message || '数据加载失败');
          }
          let object = res.object || {},
            data = [];
          if (Object.keys(object).length) {
            data = this.generatePieList(object);
          } else {
            data = [
              {
                value: 100,
                name: ''
              }
            ];
          }
          this.renderScorePie(data, pieList[i]);
        });
      }

      //完成得分 折线图图刷新
      this.$api({
        url: '/ts-worksheet/statisticsData/getEvaluationAverageScore',
        data: searchData,
        method: 'get'
      }).then(res => {
        let speedList = [0],
          attitudeList = [0],
          sumList = [0],
          tecList = [0],
          cols = [''];
        if (res.success == false) {
          this.$message.error(res.message || '数据加载失败');
        }

        let object = res.object || {};
        let index =
          object.dayOrMonthType == 0 ? 2 : object.dayOrMonthType == 1 ? 1 : 0;

        (object.list || []).forEach(item => {
          let dates = item.date.split('-');
          cols.push(parseInt(dates[index]));

          speedList.push(item.avgProcessSpeed);
          attitudeList.push(item.avgServiceAttituude);
          sumList.push(item.avgSum);
          tecList.push(item.avgTechnicalLevel);
        });

        this.renderScoreLine(sumList, speedList, attitudeList, tecList, cols);
      });
    },
    //刷新表格数据
    refreshTable(data, callback) {
      let searcBarhData = this.$refs.searchBar.getSearchData(),
        searchData = { ...data, ...searcBarhData };
      this.$api({
        url: '/ts-worksheet/statisticsData/getQualityOfPersonnelService',
        method: 'get',
        data: searchData
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '数据加载错误');
          callback();
          return;
        }
        let data = {
          list: res.rows || [],
          pageNo: res.pageNo || 1,
          pageSize: res.pageSize || 20,
          total: res.totalCount || 0
        };
        callback(data);
        return;
      });
    },
    //被评价人搜索
    beEvaluationSearch(searchVal, callback) {
      if (!searchVal) {
        callback([]);
        return;
      }
      this.tabelFkUserId = null;
      let data = searchVal.split('-');
      this.beEvaInputTimer && clearTimeout(this.beEvaInputTimer);
      this.beEvaInputTimer = setTimeout(() => {
        this.$api({
          url: '/ts-worksheet/workSheetPeopple/getPeopleInfoList',
          method: 'post',
          data: { employeeName: data[0] },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
          }
        }).then(res => {
          if (res.success == false) {
            callback([]);
            return;
          }
          let data = [];
          (res.rows || []).forEach(item => {
            data.push({
              label: item.name + (item.deptName ? '-' + item.deptName : ''),
              value: item.userId
            });
          });

          callback(data);
        });
      }, 500);
    },
    //------------------ 事件处理 ----------------------
    //表格导出，所见即所得
    handleTableExport() {
      let { col, data } = this.$refs.table.getTableData(),
        headers = [],
        filters = [];

      (col || []).forEach(item => {
        headers.push(item.label);
        filters.push(item.prop);
      });

      let options = {
        fileName: '人员服务质量.xls',
        datas: [
          {
            sheetData: data,
            sheetFilter: filters,
            sheetHeader: headers
          }
        ]
      };
      var toExcel = new JsExportExcel(options);
      toExcel.saveExcel();
      return;
    },
    //scoreAction改变，重新渲染表格
    handleScoreActionChange(val) {
      let content;
      switch (val) {
        case 0:
          content = this.finishedScorePie;
          break;
        case 1:
          content = this.finishedSpeedPie;
          break;
        case 2:
          content = this.finishedAttitudePie;
          break;
        case 3:
          content = this.finishedTecPie;
          break;
        default:
          break;
      }

      this.$nextTick(() => {
        let option = content.getOption() || {};
        content.resize();
        content.clear();
        content.setOption(option);
      });
    },
    //serviceActive 改变，重新渲染表格
    handleServiceActionChange(val) {
      let content;
      switch (val) {
        case '0':
          content = this.serviceScoreBar;
          break;
        case '1':
          content = this.serviceSpeedBar;
          break;
        case '2':
          content = this.serviceAttitudeBar;
          break;
        case '3':
          content = this.serviceTecLevelBar;
          break;
        case '4':
          content = this.serviceTimeBar;
          break;
        default:
          break;
      }
      this.$nextTick(() => {
        let option = content.getOption() || {};
        content.resize();
        content.clear();
        content.setOption(option);
        content.resize();
      });
    },
    //处理表格搜索事件
    handleTableSearch() {
      let searchData = {};

      if (this.statisticsData) {
        this.evaluationDept
          ? (searchData.fkUserDeptId = this.evaluationDept)
          : null;
      } else {
        searchData.fkUserDeptId = this.$store.state.common.userMessage.deptId;
      }

      this.tabelFkUserId
        ? (searchData.fkUserId = this.tabelFkUserId)
        : this.beEvaluationName
        ? (searchData.fkUserName = this.beEvaluationName.split('-')[0])
        : null;

      // let searchData = {
      //   fkUserDeptId: this.evaluationDept,
      //   fkUserId: this.beEvaluationName
      // };
      this.$refs.table.refreshTable(searchData);
    },
    //处理表格搜索处理人选择事件
    handleAutoComSelect(data) {
      this.tabelFkUserId = data.value;
    },
    //------------------ echarts图形渲染 ---------------
    //渲染四个维度得分饼图
    renderScorePie(data, content) {
      let seriesItem = {
        type: 'pie',
        center: ['50%', '50%'],
        radius: ['35%', '60%'], //饼状图半径， 内半径 外半径，内半径为0则为饼状
        labelLine: {
          show: true
        },
        data: data
      };

      if (data.length == 1 && data[0].name == '') {
        seriesItem.color = ['#E9F0FF'];
        seriesItem.label = {
          formatter: function(params) {
            // return '暂无数据';
          },
          fontSize: 16,
          fontStyle: 'normal',
          // color: '#FFFFFF',
          align: 'left',
          padding: [0, 0]
        };
        seriesItem.emphasis = {
          scale: false,
          itemStyle: {
            color: '#E9F0FF'
          }
        };
      } else {
        seriesItem.label = {
          formatter: function(params) {
            let int = params.percent.toFixed(0);
            if (int > 0) {
              return `{a|${params.name}}\n${int}%`;
            } else {
              return '';
            }
          },
          fontSize: 16,
          fontStyle: 'normal',
          align: 'left',
          padding: [0, 0],
          // width: 500, //文字显示宽度
          // overflow: 'break', //超出部分换行显示
          rich: {
            a: {
              fontSize: 16,
              lineHeight: 22
            }
          }
        };
      }
      let option = {
        series: seriesItem,
        tooltip: {
          show: data.length == 1 && data[0].name == '' ? false : true,
          trigger: 'item',
          formatter: function(params) {
            return `${params.value}个`;
          }
        },
        legend: {
          icon: 'circle',
          itemWidth: 10,
          orient: 'vertical',
          y: 'center',
          x: 'left'
        }
      };

      content.clear();
      content.setOption(option);
    },
    //渲染四个维度得分折线图
    renderScoreLine(totalData, speedData, attitudeData, tecData, cols) {
      let options;
      if (cols.length == 1 && cols[0] == '') {
        options = {
          legend: {
            icon: 'circle',
            itemWidth: 10,
            top: '10%'
          },
          xAxis: {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              //interval: 0
            }
          },
          yAxis: {
            name: '平均分值',
            axisLabel: {
              show: true
            }
          },
          series: [
            {
              type: 'line',
              data: [0],
              symbol: 'none'
            }
          ],
          grid: {
            top: '20%',
            left: '3%',
            right: '6.55%',
            bottom: '10%',
            containLabel: true
          }
        };
      } else {
        options = {
          legend: {
            icon: 'circle',
            itemWidth: 10,
            top: '10%'
          },
          xAxis: {
            data: cols || [],
            nameTextStyle: {
              overflow: true
            },
            axisTick: {
              show: false
            },
            axisLabel: {
              //interval: 0
            }
          },
          yAxis: {
            name: '平均分值',
            axisLabel: {
              show: true
            }
          },
          series: [
            {
              name: '总评分',
              type: 'line',
              data: totalData,
              symbol: 'circle',
              label: {
                show: true,
                formatter: function(param) {
                  return param.value.toFixed(1);
                }
              }
            },
            {
              name: '处理速度',
              type: 'line',
              data: speedData,
              symbol: 'circle',
              label: {
                show: true,
                formatter: function(param) {
                  return param.value.toFixed(1);
                }
              }
            },
            {
              name: '服务态度',
              type: 'line',
              data: attitudeData,
              symbol: 'circle',
              label: {
                show: true,
                formatter: function(param) {
                  return param.value.toFixed(1);
                }
              }
            },
            {
              name: '技术水平',
              type: 'line',
              data: tecData,
              symbol: 'circle',
              label: {
                show: true,
                formatter: function(param) {
                  return param.value.toFixed(1);
                }
              }
            }
          ],
          grid: {
            top: '23%',
            left: '3%',
            right: '6.55%',
            bottom: '10%',
            containLabel: true
          }
        };
      }

      this.finishedScoreLine.clear();
      this.finishedScoreLine.setOption(options);
    },
    //渲染四个维度得分排名 柱状图
    renderServiceBar(data, cols, content) {
      let options = {
        xAxis: {
          data: cols || [],
          nameTextStyle: {
            overflow: true
          },
          nameTextStyle: {
            overflow: true
          },
          axisTick: {
            show: false
          }
        },
        yAxis: {
          name: '得分',
          axisLabel: {
            show: true
          }
        },
        series: [
          {
            type: 'bar',
            data,
            barWidth: 50,
            label: {
              show: cols.length == 1 && cols[0] == '' ? false : true,
              position: 'top',
              formatter: function(param) {
                return param.value.toFixed(1);
              }
            }
          }
        ],
        grid: {
          top: '40px',
          left: '8px',
          right: '8px',
          bottom: '0%',
          containLabel: true
        }
      };

      content.clear();
      content.setOption(options);
    },
    //----------------- 内部方法 -----------------------
    generatePieList(object = {}) {
      let map = {
        fcmy: '非常满意',
        my: '满意',
        yb: '一般',
        hbmy: '很不满意',
        bmy: '不满意'
      };
      const arr = [];
      for (const item in object) {
        if (object[item]) {
          arr.push({
            name: map[item],
            value: object[item]
          });
        }
      }
      return arr;
    }
  }
};
</script>

<style scoped lang="scss">
@import url('../comment/css/workSheetCommont.scss');
.finished-situation-body {
  height: 100%;
  overflow: auto;
}
.inform-content {
  width: 100%;
  height: calc(100% - 52px);
  overflow: auto;
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }
  &:hover::-webkit-scrollbar-thumb {
    border-radius: 10px;
    height: 50px;
    background: rgba(153, 153, 153, 0.4);
    &:hover {
      background: rgba(153, 153, 153, 0.8);
    }
  }
  &:hover::-webkit-scrollbar-track {
    box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    background: #fff;
  }
  .inform-row {
    // margin-top: 20px;
    min-width: 1090px;
    width: 100%;
    height: 320px;
    padding-bottom: 10px;
  }
  .inform-col-1 {
    width: calc(50% - 10px);
    // background-color: blue;
    height: 100%;
    margin-right: 20px;
  }
  .inform-col-2 {
    height: 100%;
    > div {
      height: 100%;
      width: 100%;
    }
  }
  .inform-title {
    font-weight: 600;
    color: #333333;
    line-height: 22px;
    font-size: 16px;
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      background: #5260ff;
      margin-right: 3px;
    }
  }
}
.cell {
  height: 100%;
  width: 100%;
}
.title {
  margin: 10px;
  line-height: 32px;
  margin-top: 0;
  font-weight: 600;
  font-size: 16px;
}
/deep/.div__body {
  padding: 0;
  height: 100%;
  width: 100%;
}
.col {
  padding: 10px;
  // box-shadow: 0 0 3px #000;
}
.box-tab-content {
  margin-bottom: 16px;
  .tab-box {
    font-size: 12px;
    // width: 35px;
    width: 63px;
    height: 58px;
    border-radius: 4px;
    background-color: transparent;
    color: #838383;
    border: 1px solid #838383;
    cursor: pointer;
    > span {
      font-size: 22px;
      font-weight: 600;
    }
    &.active {
      color: $theme-color;
      border: 1px solid $theme-color;
    }
    &:hover {
      opacity: 0.8;
    }
  }
}
.inform-row.bigger-row {
  height: 400px;
}

.table-search-input-tree {
  margin-right: 8px;
}
/deep/.order-quality-select input {
  border-radius: 16px;
}
.service-action-selections {
  /deep/ .el-radio-button__inner {
    font-size: 14px;
    border: none;
  }
  /deep/ .el-radio-button::after {
    content: '';
    background-color: rgb(220, 223, 230);
    height: 16px;
    width: 1px;
    position: absolute;
    top: 50%;
    transform: translate(0, -50%);
    z-index: 155;
  }
  /deep/ .el-radio-button:last-child::after {
    background-color: transparent;
  }
  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background-color: #fff;
    box-shadow: none;
    color: #5260ff;
    font-weight: 600;
    border-color: rgb(220, 223, 230);
  }
}
.has-grey-background {
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e7ebf0;
  padding: 8px;
}
/deep/ {
  .table::-webkit-scrollbar-track,
  .el-table__body-wrapper {
    background: #fafafa;
  }
}
</style>
