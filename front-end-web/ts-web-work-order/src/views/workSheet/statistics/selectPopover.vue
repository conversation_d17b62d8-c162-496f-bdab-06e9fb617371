<template>
  <el-popover
    ref="popover"
    :visible-arrow="false"
    trigger="click"
    :placement="placement"
    @show="popoverVisible = true"
    @hide="popoverVisible = false"
  >
    <div slot="reference" class="selected-popover">
      {{ selectLabel || '全部' }}
      <span
        :class="popoverVisible ? 'is-active' : ''"
        class="el-icon-arrow-up"
      ></span>
    </div>

    <ul class="options-ul">
      <li
        v-for="item of options"
        :key="item.value"
        class="option-item"
        :class="item.value == value ? 'selected' : ''"
        @click="handleSelect(item)"
      >
        {{ item.label }}
      </li>
    </ul>
  </el-popover>
</template>

<script>
export default {
  model: {
    prop: 'value',
    event: 'valuechange'
  },
  props: {
    value: {
      type: String
    },
    options: {
      type: Array,
      default: () => []
    },
    placement: {
      type: String,
      default: () => 'bottom-end'
    }
  },
  data() {
    return {
      selectLabel: '',
      popoverVisible: false
    };
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          let checked =
            this.options.find(item => item.value == this.value) || {};
          this.selectLabel = checked.label;
        }
      },
      immediate: true
    }
  },
  created() {
    let selected = this.options.find(item => item.value == this.value);
    this.selectLabel = (selected || {}).label;
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.popover.$refs.popper.style.padding = '0';
      this.$refs.popover.$refs.popper.style.border = 'none';
      this.$refs.popover.$refs.popper.style.overflow = 'hidden';
    });
  },
  methods: {
    handleSelect(item) {
      this.selectLabel = item.label;
      this.$emit('valuechange', item.value);
      this.$refs.popover.doClose();
    }
  }
};
</script>
<style lang="scss" scoped>
.selected-popover {
  color: #5260ff;
  cursor: pointer;
  span {
    transition: transform 0.3s ease 0s;
    transform: rotateZ(180deg);
    margin-left: 4px;
  }
  .is-active {
    transform: rotateZ(0deg);
  }
}
.options-ul {
  list-style: none;
  .option-item {
    padding: 4px 8px;
    cursor: pointer;
    line-height: 20px;
    &:hover {
      background-color: #5260ff33;
    }
    &.selected {
      background-color: #5260ff33;
    }
  }
}
</style>
