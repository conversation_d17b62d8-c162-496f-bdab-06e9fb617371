.come-form-tech {
  cursor: pointer;
  user-select: none;
}

.text-label {
  display: inline-block; 
  border-radius: 9px;
  line-height: 17px;
  font-size: 12px;
  padding: 0 9px;
  cursor: pointer;
  &.urgent {
    background: #FFEFDF;
    border: 1px solid #FF6010;
    color:#FF6010; 
  }
  &.very-urgent {
    background: #FFEEEF;
    border: 1px solid #F93A4A;
    color:#F93A4A;
  }
  &.reback-label {
    color: #999999;
    background: #F5F5F5;
    border: 1px solid #999999;
  }
}
.text-label + .text-label {
  margin-left: 4px;
}
.text-label + .work-sheet-deion,
.come-form-tech + .work-sheet-deion {
  margin-left: $theme-interval;
}

.work-sheet-deion-content {
  overflow: hidden;
  cursor: pointer;
  text-overflow: ellipsis;
  &:hover .work-sheet-deion {
    color: $theme-color;
    text-decoration: underline;
  }
}

.table-can-action-line {
  cursor: pointer;
  &:hover {
    color: $theme-color;
    text-decoration: underline;
  }
}