<template>
  <el-dialog title="评价" width="400px" :visible="true" @close="handleCancle">
    <div class="flex-center col">
      处理速度 <el-rate v-model="saveData.processSpeed"></el-rate>
    </div>
    <div class="flex-center col">
      服务态度 <el-rate v-model="saveData.serviceAttituude"></el-rate>
    </div>
    <div class="flex-center col">
      技术水平 <el-rate v-model="saveData.technicalLevel"></el-rate>
    </div>

    <div slot="footer" class="flex-center">
      <el-button class="trasen-perpul" @click="handleEvaluateOrder">
        提交
      </el-button>
      <el-button style="margin-left: 8px;" @click="handleCancle">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/workOrderHomePage.js';

export default {
  props: {
    workOrder: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      saveData: {
        processSpeed: 5,
        serviceAttituude: 5,
        technicalLevel: 5
      }
    };
  },
  methods: {
    async handleEvaluateOrder() {
      let data = { pkWsTaskId: this.workOrder.pkWsTaskId, ...this.saveData };
      let res = await api.workOrderEvaluate(JSON.stringify(data));
      if (!res.success) {
        this.$message.error(res.message || '服务器出错了，请稍后再试');
        this.$emit('close');
        return;
      }
      this.$message.success('评价成功');
      this.$emit('close', true);
    },
    handleCancle() {
      this.$emit('close');
    }
  }
};
</script>

<style scoped lang="scss">
.col {
  margin-bottom: 8px;
}
</style>
