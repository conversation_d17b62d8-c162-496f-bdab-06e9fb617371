<template>
  <div class="detail-content">
    <el-tabs v-model="messageTabs" type="card">
      <el-tab-pane ref="tabContent" label="基本信息" name="1">
        <TableItems
          name="worderTable"
          :itemDatas="itemDatas"
          :formDatas="messageInfo.wsWsSheetInfoOutVo || {}"
        />
      </el-tab-pane>

      <el-tab-pane :label="fileTabContent" name="2">
        <FileShow :workOrderInfo="messageInfo" />
      </el-tab-pane>

      <el-tab-pane label="费用明细" name="3">
        <cost-detail
          ref="costDetailList"
          :workOrderInfo="{
            ...messageInfo.wsWsSheetInfoOutVo,
            peopleType: '2'
          }"
        ></cost-detail>
      </el-tab-pane>

      <el-tab-pane label="操作日志" name="4">
        <ProgressShow :progressList="messageInfo.wsTaskInfoOutVoList" />
      </el-tab-pane>
    </el-tabs>
    <span class="day">
      {{ tips }}
    </span>
  </div>
</template>

<script>
import TableItems from '@/components/table-items/index';
import FileShow from './file-show.vue';
import ProgressShow from './progress-show.vue';
import data from '@/views/workSheet/workOrderHomePage/Minix/data';
import CostDetail from '@/views/workSheet/workOrderHomePage/components/CostDetail.vue';

export default {
  mixins: [data],
  components: {
    TableItems,
    FileShow,
    ProgressShow,
    CostDetail
  },
  props: {
    messageInfo: {
      type: Object,
      default: () => {}
    },
    pkWsTaskId: {
      type: String,
      default: () => ''
    }
  },
  data: () => ({
    messageTabs: '1'
  }),
  mounted() {
    this.$nextTick(() => {
      let tabContent = this.$refs.tabContent,
        siblings = tabContent.$el.parentElement.children, //兄弟元素
        windowHeight = document.querySelector(
          'body > .qiankun-layout-container'
        ).clientHeight,
        tabContentHeight = tabContent.$el.clientHeight + 1,
        contentMaxHeight = windowHeight - 265, //最大内容高度
        setHeight = height => {
          for (let i = 0; i < siblings.length; i++) {
            let item = siblings[i];
            item.style.height = height;
          }
        };
      contentMaxHeight < tabContentHeight
        ? setHeight(contentMaxHeight + 'px')
        : setHeight(tabContentHeight + 'px');
    });
  },
  computed: {
    tips() {
      let urge = '';
      let exceed = '';
      const info = this.messageInfo.wsWsSheetInfoOutVo || {};
      if (info.hatencount > 0) {
        urge = `工单已催办${this.messageInfo.wsWsSheetInfoOutVo.hatencount}次`;
      }

      if (info.cqDays > 0) {
        exceed = `工单已超期${info.cqDays}天`;
      } else if (info.cqDays === 0) {
        exceed = '工单今天到期';
      } else if (info.cqDays < 0) {
        exceed = `工单还有${Math.abs(info.cqDays)}天到期`;
      }

      if (info.requiredCompletionTime === null) {
        exceed = '';
      }

      if (!urge) {
        return exceed;
      } else if (!exceed) {
        return urge;
      } else if (!urge && !exceed) {
        return '';
      } else {
        return urge + ' - ' + exceed.slice(2) + ' 需尽快处理。';
      }
    },
    fileTabContent() {
      return (
        '资源文件' +
        (this.messageInfo.wsFileOutVoList &&
        this.messageInfo.wsFileOutVoList.length
          ? `(${this.messageInfo.wsFileOutVoList.length})`
          : '')
      );
    }
  },
  watch: {
    messageInfo: {
      handler(val) {
        let { wsWsSheetInfoOutVo = {} } = val || {};
        wsWsSheetInfoOutVo.repairDeptAddress = [
          wsWsSheetInfoOutVo.hospitalDistrictName,
          wsWsSheetInfoOutVo.repairDeptAddress
        ]
          .filter(item => item)
          .join('-');
        //当前处理人如果有协助人的，在处理人后面用括号展示出来
        if (wsWsSheetInfoOutVo.assist) {
          wsWsSheetInfoOutVo.fkUserName += ` (${wsWsSheetInfoOutVo.assist})`;
        }
        //紧急程度（常规处理不需要）、影响范围（个人事件不需要）
        this.itemDatas.forEach(item => {
          item.forEach(child => {
            if (child.prop === 'faultAffectScopeValue') {
              child.activeClass =
                wsWsSheetInfoOutVo.faultAffectScope === 1
                  ? ''
                  : 'heightlight-cell';
            }
            if (child.prop === 'faultEmergencyValue') {
              child.activeClass =
                wsWsSheetInfoOutVo.faultEmergency === 3
                  ? ''
                  : 'heightlight-cell';
            }
          });
        });
      },
      immediate: true
    },
    messageTabs: function(val) {
      if (val == 3) {
        this.$refs.costDetailList && this.$refs.costDetailList.refresh();
        this.$refs.costDetailList.$refs.table.computedTableStyle();
      }
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
/deep/ .detail-content {
  position: relative;
  // min-height: 550px;
  .day {
    position: absolute;
    top: 26px;
    right: 10px;
    color: red;
  }
  .el-tabs__header {
    margin-bottom: 8px;
  }
  .el-tabs__content {
    // height: 372px;
    overflow-y: auto;
    &::-webkit-scrollbar {
      background-color: transparent;
      width: 6px;
      height: 6px;
    }
    &:hover::-webkit-scrollbar-thumb {
      border-radius: 6px;
      background-color: rgba(153, 153, 153, 0.4);
      &:hover {
        background-color: rgba(153, 153, 153, 0.8);
      }
    }
  }
}
.operation-box {
  margin-top: 8px;
  border-top: 1px solid #eee;
  padding-top: 8px;
  .operation-title {
    font-size: 16px;
    color: #333;
    font-weight: 600;
    &::before {
      content: ' ';
      width: 4px;
      height: 16px;
      margin-right: 3px;
      background-color: #5260ff;
    }
  }
  .operation-form {
    margin-top: 20px;
  }
}
/deep/ {
  .el-tabs__content [name='worderTable'] {
    border: 0.5px solid #e0e6f0;
    border-right: none;
    border-bottom: none;
    &.table-data tr {
      border: none !important;
    }
    &.table-data tr > td {
      border: none;
      border-right: 0.5px solid #e0e6f0;
      border-bottom: 0.5px solid #e0e6f0;
      height: 30px;
      color: #333;
      > span {
        color: #333;
        font-size: 14px;
        line-height: 20px;
      }
    }
    &.table-data tr > td.label-class {
      color: #666;
      font-size: 14px;
      background: #f5f7fa;
      border-left: 0.5px solid #e0e6f0;
    }
    .heightlight-cell {
      color: #5260ff !important;
      font-weight: 600;
    }
  }
}
</style>
