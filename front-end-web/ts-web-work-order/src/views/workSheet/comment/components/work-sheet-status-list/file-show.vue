<template>
  <div class="multi_media">
    <!--保修音频-->
    <div class="audio-file">
      <span class="label" v-if="showTitle">报修音频:</span>
      <template
        v-if="
          workOrderInfo.audioFileList && workOrderInfo.audioFileList.length > 0
        "
      >
        <div v-for="(item, index) in workOrderInfo.audioFileList" :key="index">
          <audio controls :src="item.fileUrl">
            您的浏览器不支持 audio 标签。
          </audio>
        </div>
      </template>
      <span v-else-if="showTitle" class="no-resource">暂无资源</span>
    </div>
    <div class="picture">
      <!--保修图片-->
      <span class="label" v-if="showTitle">报修图片:</span>
      <template
        v-if="
          workOrderInfo.pictureFileList &&
            workOrderInfo.pictureFileList.length > 0
        "
      >
        <div
          class="picture_item"
          v-for="(picture, index) in workOrderInfo.pictureFileList"
          :key="index"
        >
          <div>
            <el-image
              :ref="`image${index}`"
              :src="picture.url"
              :preview-src-list="workOrderInfo.picUrlList || [picture.url]"
              :z-index="9999"
            />
            <!-- 预览等操作图标 -->
            <div class="image-wrapper flex-col-center">
              <div class="flex-row-evenly">
                <i
                  class="fa fa-search"
                  @click="handleImagePreviewClick(index)"
                ></i>
                <i
                  class="fa fa-caret-down"
                  @click="downloadHandle(picture)"
                ></i>
              </div>
            </div>
          </div>
        </div>
      </template>
      <span v-else-if="showTitle" class="no-resource">暂无资源</span>
    </div>
    <div class="file">
      <!--保修附件-->
      <span class="label" v-if="showTitle">报修附件:</span>
      <template
        v-if="
          workOrderInfo.otherFileList && workOrderInfo.otherFileList.length > 0
        "
      >
        <div class="download_item">
          <div
            v-for="(item, index) in workOrderInfo.otherFileList"
            :key="index"
            class="download_item_box"
          >
            <img src="@/assets/img/workSheet/workOrderHomePage/icon_file.png" />
            <span class="download_font" @click="handlePerviewFile(item)">{{
              item.fkFileName
            }}</span>
            <!-- <span class="download_button" @click="handlePerviewFile(item)">
              预览
            </span> -->
            <span class="download_button" @click="downloadHandle(item)">
              下载
            </span>
          </div>
        </div>
      </template>
      <span v-else-if="showTitle" class="no-resource">暂无资源</span>
    </div>
  </div>
</template>

<script>
import { Base64 } from 'js-base64';

export default {
  props: {
    workOrderInfo: {
      type: Object,
      default: () => []
    },
    showTitle: {
      type: Boolean,
      default: () => {
        return true;
      }
    }
  },
  watch: {
    workOrderInfo: {
      handler(val) {
        val.audioFileList = [];
        val.pictureFileList = [];
        val.otherFileList = [];
        val.picUrlList = [];
        val.wsFileOutVoList &&
          val.wsFileOutVoList.forEach(item => {
            const fkFileName = item.fkFileName;
            const type = fkFileName.slice(fkFileName.indexOf('.') + 1);
            if (type === 'wav') {
              val.audioFileList.push(item);
            } else if (type === 'png' || type === 'jpeg' || type === 'jpg') {
              if (item.fileUrl.indexOf('ts-basics-bottom') >= 0) {
                item.url = item.fileUrl;
              } else {
                item.url =
                  '/ts-document/attachment/downloadFile/' + item.fkFileId;
              }
              val.pictureFileList.push(item);
              val.picUrlList.push(item.url);
            } else {
              val.otherFileList.push(item);
            }
          });
      },
      immediate: true
    }
  },
  methods: {
    downloadHandle(message) {
      const file = this.workOrderInfo.wsFileOutVoList.filter(
        item => item.fkFileId === message.fkFileId
      )[0];
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href =
        (file.fileUrl.indexOf('ts-basics-bottom') >= 0
          ? '/ts-basics-bottom/fileAttachment/downloadFile/'
          : '/ts-document/attachment/downloadFile/') + file.fkFileId;
      link.setAttribute('download', file.fkFileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    handleImagePreviewClick(index) {
      if (this.$refs[`image${index}`]) {
        this.$refs[`image${index}`][0].showViewer = true;
      }
    },
    handlePerviewFile(file) {
      let a = document.createElement('a');
      a.target = '_blank';
      a.href =
        `${location.origin}/ts-preview/onlinePreview?url=` +
        Base64.encode(
          `${this.$config.DOCUMENT_BASE_HOST}${file.fileUrl}?fullfilename=${
            file.fkFileId
          }.${file.fileSuffix || file.fkFileName.split('.')[1]}`
        );
      a.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.download_button {
  cursor: pointer;
  color: $theme-color;
  &:hover {
    opacity: 0.8;
  }
}
.multi_media {
  padding-left: 20px;
  .audio-file,
  .picture,
  .file {
    display: flex;
    min-width: 500px;
    margin-bottom: 8px;
    .label {
      margin-right: 10px;
    }
    .picture_item {
      display: flex;
      flex-direction: column;
      align-items: center;
      > div {
        position: relative;
      }
      .image-wrapper {
        width: 100px;
        height: 100px;
        background-color: rgba(0, 0, 0, 0.3);
        position: absolute;
        top: 0;
        z-index: 9;
        display: none;
        &:hover {
          display: flex;
        }
        > div {
          width: 100%;
        }
        i {
          color: #fff;
          &:hover {
            cursor: pointer;
          }
        }
      }
      /deep/ .el-image {
        width: 100px;
        height: 100px;
        margin: 0px 10px 10px 0;
        &:hover + .image-wrapper {
          display: flex;
        }
      }
    }
  }
  .file {
    align-items: flex-start;
  }
  .picture {
    flex-wrap: wrap;
  }
  .download_item {
    display: flex;
    flex-direction: column;
    align-items: center;
    .download_item_box {
      min-width: 350px;
      display: flex;
      align-items: center;
      > *:not(:first-child) {
        margin-right: 8px;
      }
      img {
        width: 25px;
        height: 25px;
      }
      .download_font {
        min-width: 120px;
        cursor: pointer;
      }
    }
  }
}
.no-resource {
  line-height: 24px;
}
</style>
