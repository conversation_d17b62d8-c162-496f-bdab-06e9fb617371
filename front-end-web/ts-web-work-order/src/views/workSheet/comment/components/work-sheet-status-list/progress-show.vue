<template>
  <div class="message_progress">
    <el-steps direction="vertical" space="80px">
      <el-step v-for="(item, index) in progressList" :key="index">
        <template slot="description">
          <div class="progress_message">
            <span :title="item.taskNameVaule" class="type">{{
              item.taskNameVaule
            }}</span>
            <span :title="item.createByName"
              >操作人：{{ item.createByName }}</span
            >
            <span :title="item.createTime">{{ item.createTime }}</span>
          </div>
          <div
            class="progress_message"
            v-for="(child, childIndex) in item.takeRemark"
            :key="childIndex"
          >
            <span :title="child">{{ child }}</span>
          </div>

          <div class="flex flex-wrap">
            <div
              v-for="(pic, picIndex) of item.picFiles"
              :key="picIndex"
              class="pic-item"
            >
              <img :src="pic.fileUrl" />
              <div class="action">
                <i
                  class="el-icon-search"
                  @click="handlePreview(pic, item.picFiles)"
                ></i>
                <a class="el-icon-caret-bottom" :href="pic.fileUrl"></a>
              </div>
            </div>
          </div>

          <div
            v-for="txt of item.textFiles"
            :key="txt.fkFileId"
            class="flex file-item"
          >
            <div>
              <i class="el-icon-paperclip"></i>
              {{ txt.fkFileName }}
            </div>
            <div
              v-if="txt.fkFileName.toLowerCase().indexOf('.mhtml') == -1"
              @click="handlePreview(txt)"
            >
              预览
            </div>
            <a :href="txt.fileUrl">下载</a>
          </div>
        </template>
      </el-step>
      <el-step />
    </el-steps>

    <el-image
      ref="preview"
      style="display:none;"
      :z-index="19699"
      :src="previewSrc"
      :preview-src-list="previewList"
    ></el-image>
  </div>
</template>

<script>
import { Base64 } from 'js-base64';

export default {
  props: {
    progressList: {
      type: Array,
      default: () => {}
    }
  },
  data() {
    return {
      previewSrc: '',
      previewList: []
    };
  },
  watch: {
    progressList: {
      handler(val) {
        // 进度数据切割
        let str = '#cut@';
        if (val && val.length > 0) {
          val.forEach(item => {
            item.takeRemark = item.takeRemark || '';
            item.takeRemark =
              item.takeRemark.indexOf(str) !== -1
                ? item.takeRemark.split(str)
                : item.takeRemark.split();

            let textFiles = [],
              picFiles = [];
            item.files &&
              item.files.forEach(file => {
                if (
                  /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(
                    file.fkFileName.toLowerCase()
                  )
                ) {
                  picFiles.push(file);
                } else {
                  textFiles.push(file);
                }
              });
            item.textFiles = textFiles;
            item.picFiles = picFiles;
          });
        }
      },
      immediate: true
    }
  },
  methods: {
    handlePreview(file, list) {
      if (
        /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(
          file.fkFileName.toLowerCase()
        )
      ) {
        this.previewList = list.map(item => item.fileUrl);
        this.previewSrc = file.fileUrl;
        this.$refs.preview.showViewer = true;
      } else {
        let a = document.createElement('a');
        a.target = '_blank';
        a.href =
          `${location.origin}/ts-preview/onlinePreview?url=` +
          Base64.encode(
            `${this.$config.DOCUMENT_BASE_HOST}${file.fileUrl}?fullfilename=${
              file.fkFileId
            }.${file.fileSuffix || file.fkFileName.split('.')[1]}`
          );
        a.click();
        a = null;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
span {
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  &:hover {
    cursor: default;
  }
}
.pic-item {
  position: relative;
  margin-right: 8px;
  margin-bottom: 8px;
  img {
    height: 80px;
    width: 80px;
  }
  &:hover .action {
    opacity: 1;
  }
  .action {
    height: 80px;
    width: 80px;
    background: rgba($color: #000000, $alpha: 0.15);
    position: absolute;
    top: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    * {
      cursor: pointer;
      color: #fff;
      &:hover {
        opacity: 0.8;
      }
      &:last-child {
        margin-left: 8px;
      }
    }
  }
}
.file-item {
  color: #333;
  *:first-child {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  *:not(:first-child) {
    white-space: nowrap;
    margin-left: 8px;
    color: $theme-color;
    cursor: pointer;
    &:hover {
      opacity: 0.8;
    }
  }
}
.message_progress {
  width: 100%;
  margin-top: 10px;
  /deep/ .el-step__icon-inner {
    display: none;
  }
  /deep/ .el-step__icon {
    width: 14px;
    height: 14px;
  }
  /deep/ .el-step.is-vertical .el-step__line {
    left: 7px;
    background: #e6ecf4;
  }
  /deep/ .el-step__head.is-wait {
    color: #5260ff;
    border-color: #5260ff;
  }
  .progress_message {
    display: flex;
    align-items: center;
    color: #000;
    margin-bottom: 10px;
    span {
      margin-right: 10px;
    }
    .type {
      padding: 3px;
      background: #2fb1ff;
      color: #fff;
      white-space: nowrap;
    }
    .progress_message {
      color: #000;
    }
  }
}
/deep/ {
  .el-step__line {
    background-color: #e6ecf4;
  }
  .el-step__icon {
    border: none !important;
    background-color: #5260ff;
    width: 12px;
    height: 12px;
    top: 4px;
    left: 1px;
  }
  .el-step__head {
    flex-shrink: 0;
  }
  .el-step__description {
    margin-bottom: 8px;
    padding-right: 0;
  }
  .el-step__description {
    .progress_message {
      margin-bottom: 0;
      &:last-child span {
        white-space: unset;
      }
      &:first-child {
        flex-wrap: wrap;
        > span:first-child {
          border: none;
          background-color: transparent;
          padding: 0;
          color: #333;
          line-height: 20px;
          &::before {
            content: '【';
          }
          &::after {
            content: '】';
          }
        }
        > span:not(:first-child) {
          font-size: 12px;
          line-height: 20px;
          color: #666;
        }
        > span:not(:nth-child(2)) {
          margin: 0;
        }
        > span:nth-child(2) {
          margin-right: 8px;
        }
      }
      &:not(:first-child) {
        span {
          font-size: 12px;
          line-height: 20px;
          color: #666;
        }
      }
    }
  }
  .el-step:first-child {
    .el-step__icon {
      top: -3px;
    }
    .el-step__main .el-step__title {
      padding: 0;
    }
  }
  .el-step {
    flex-basis: unset !important;
  }
  .el-step:last-child {
    visibility: hidden;
    // flex-basis: auto !important;
    height: 0;
  }
  .el-step:nth-last-child(2) {
    flex: 1;
  }
  .el-step__line-inner {
    color: #e6ecf4;
    border-color: #e6ecf4;
  }
}
.message_progress {
  > span:not(:first-child) {
    font-size: 12px;
  }
}
</style>
