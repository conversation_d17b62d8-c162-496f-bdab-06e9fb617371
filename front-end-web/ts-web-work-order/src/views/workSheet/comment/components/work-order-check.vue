<template>
  <el-dialog title="评价" width="400px" :visible="true" @close="handleCancle">
    <div class="title col flex-col-center">是否验收</div>
    <el-form
      ref="form"
      :model="saveData"
      :rules="rules"
      label-width="80px"
      style="height: 128px;"
    >
      <el-form-item label="验收确认" prop="yesOrNo">
        <el-radio-group v-model="saveData.yesOrNo">
          <el-radio :label="1">已解决</el-radio>
          <el-radio :label="0">未解决</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="showRemark" label="备注说明" prop="remark">
        <el-input
          v-model="saveData.remark"
          type="textarea"
          placeholder="将退回至处理人继续处理，请输入不通过说明"
          :autosize="{ minRows: 3, maxRows: 3 }"
          resize="none"
        ></el-input>
      </el-form-item>
    </el-form>

    <div slot="footer" class="flex-center">
      <el-button class="trasen-perpul" @click="handleOk">
        提交
      </el-button>
      <el-button style="margin-left: 8px;" @click="handleCancle">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/workOrderHomePage.js';

export default {
  props: {
    workOrder: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      saveData: {
        yesOrNo: 1
      },
      rules: {
        yesOrNo: [{ required: true, message: '是否通过必填', trigger: 'blur' }],
        remark: [
          { required: true, message: '未通过原因不能为空', trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    showRemark: function() {
      return this.saveData.yesOrNo == 0;
    }
  },
  methods: {
    async handleOk() {
      let formRes = await this.$refs.form.validate();
      if (!formRes) {
        return;
      }

      let data = {
        ...this.saveData,
        fkUserId: this.workOrder.fkUserId,
        pkWsTaskId: this.workOrder.pkWsTaskId
      };
      let res = await api.workOrderAccept(JSON.stringify(data));
      if (res.success == false) {
        this.$message.error(res.message || '服务器出错了，请稍后再试');
        this.$emit('close');
        return;
      }

      this.$message.success('验收成功');
      this.$emit('close', true);
    },
    handleCancle() {
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: 600;
  color: #333;
  line-height: 22px;
  font-size: 16px;
  &::before {
    content: '';
    height: 16px;
    width: 4px;
    background-color: rgb(45, 118, 235);
    margin-right: 3px;
  }
}
.col {
  margin-bottom: 8px;
}
</style>
