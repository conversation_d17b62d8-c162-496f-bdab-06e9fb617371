<template>
  <el-dialog title="开启" width="400px" :visible="true" @close="handleCancle">
    <div class="title col flex-col-center">开启工单</div>
    <div class="col flex-center">
      {{ message.createTime }}：{{ message.createByName }}暂停工单
    </div>
    <div class="col flex-center">暂停原因：{{ message.takeRemark }}</div>

    <div slot="footer" class="flex-center">
      <el-button class="trasen-perpul" @click="handleOk">
        确认
      </el-button>
      <el-button style="margin-left: 8px;" @click="handleCancle">
        取消
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
import api from '@/api/ajax/workOrderHomePage.js';

export default {
  props: {
    workOrder: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      message: {}
    };
  },
  async created() {
    let res = await api.openNodeInfo(this.workOrder.pkWsTaskId);
    if (res.success == false) {
      this.$emit('close');
      this.$message.error(res.message || '服务器出错了，请稍后再试');
    } else {
      this.message = res.object || {};
    }
  },
  methods: {
    async handleOk() {
      let data = {
        fkUserId: this.workOrder.fkUserId,
        pkWsTaskId: this.workOrder.pkWsTaskId
      };

      let res = await api.openWorkOrder(JSON.stringify(data));
      if (res.success == false) {
        this.$message.error(res.message || '服务器出错了，请稍后再试');
        this.$emit('close');
        return;
      }

      this.$message.success('开启成功');
      this.$emit('close', true);
    },
    handleCancle() {
      this.$emit('close');
    }
  }
};
</script>

<style lang="scss" scoped>
.title {
  font-weight: 600;
  color: #333;
  line-height: 22px;
  font-size: 16px;
  &::before {
    content: '';
    height: 16px;
    width: 4px;
    background-color: rgb(45, 118, 235);
    margin-right: 3px;
  }
}
.col {
  margin-bottom: 8px;
}
</style>
