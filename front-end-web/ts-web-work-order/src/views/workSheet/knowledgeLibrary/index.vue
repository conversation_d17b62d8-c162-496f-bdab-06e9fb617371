<template>
  <div class="content">
    <div class="realContent">
      <div class="content-left">
        <el-input
          v-model="categoryName"
          placeholder="输入关键字进行搜索"
          @keyup.native="handleRefreshKnowledgeTree"
        ></el-input>

        <el-scrollbar
          ref="treeScroll"
          class="tree-scroll"
          wrapClass="tree-scroll-wrap"
          viewClass="tree-scroll-view"
        >
          <el-tree
            ref="knowledgeTree"
            default-expand-all
            node-key="id"
            empty-text="暂无数据"
            :data="knowledgeTypeTreeData"
            :props="{ label: computTreeName }"
            :highlight-current="true"
            :expand-on-click-node="false"
            :filter-node-method="filterKnowledgeTree"
            @node-click="handleTypeTreeClick"
            @node-expand="handleTypeTreeChange"
            @node-collapse="handleTypeTreeChange"
            style="min-width: 234px;"
          >
            <span class="custom-tree-node" slot-scope="{ node }">
              <span class="flex-center"
                ><i
                  :class="
                    node.level == 1
                      ? 'first-tree-icon'
                      : !node.isLeaf && (node.childNodes || []).length > 0
                      ? 'has-child-tree-icon'
                      : 'leaf-tree-icon'
                  "
                ></i
                >{{ node.label }}</span
              >
            </span>
          </el-tree>
        </el-scrollbar>
      </div>
      <div class="content-right">
        <!-- 表格数据 -->
        <el-tabs v-model="active" type="card" @tab-click="refresh">
          <!-- 知识库 -->
          <el-tab-pane :label="`发布中(${tabCountList[0] || 0})`" name="4">
            <div class="searchBar">
              <el-col :span="6">
                <el-input
                  v-model="knowledgeSearchData"
                  placeholder="请输入知识主题"
                  clearable
                  @keyup.enter.native="refreshTable"
                ></el-input>
              </el-col>
              <el-button
                class="trasen-search"
                @click="refreshTable"
                style="margin-left: 8px;"
              >
                搜索
              </el-button>
              <div
                class="trasen-search-reset"
                @click="
                  () => {
                    knowledgeSearchData = '';
                    refreshTable();
                  }
                "
              >
                <i class="el-icon-refresh-right"></i>
              </div>
              <div class="float-right">
                <el-button
                  class="trasen-btn trasen-perpul"
                  type="primary"
                  @click="openEditModal()"
                >
                  新增
                </el-button>
                <el-button class="trasen-btn" @click="handleExport"
                  >导出</el-button
                >
              </div>
            </div>

            <div class="tableBox">
              <content-table v-bind="knowledgeLibrary" ref="table4">
                <template slot="actionRow" slot-scope="scope">
                  <ActionCell
                    :renderFunc="renderKnowledgeAction"
                    :scope="scope"
                  ></ActionCell>
                </template>
              </content-table>
            </div>
          </el-tab-pane>

          <!-- 审核中 -->
          <el-tab-pane :label="`审核中(${tabCountList[1] || 0})`" name="1">
            <div class="searchBar">
              <el-col :span="6">
                <el-input
                  v-model="knowledgeSearchData"
                  placeholder="请输入知识主题"
                  clearable
                  @keyup.enter.native="refreshTable"
                ></el-input>
              </el-col>
              <el-button class="trasen-search" @click="refreshTable"
                >搜索</el-button
              >
              <div
                class="trasen-search-reset"
                @click="
                  () => {
                    knowledgeSearchData = '';
                    refreshTable();
                  }
                "
              >
                <i class="el-icon-refresh-right"></i>
              </div>
              <div class="float-right">
                <el-button
                  class="trasen-btn trasen-perpul"
                  type="primary"
                  @click="openEditModal()"
                  >新增</el-button
                >
                <el-button class="trasen-btn" @click="handleExport"
                  >导出</el-button
                >
              </div>
            </div>

            <div class="tableBox">
              <content-table v-bind="examineTable" ref="table1">
                <template slot="actionRow" slot-scope="scope">
                  <ActionCell
                    :renderFunc="renderExamineAction"
                    :scope="scope"
                  ></ActionCell>
                </template>
              </content-table>
            </div>
          </el-tab-pane>

          <!-- 未通过 -->
          <el-tab-pane :label="`未通过(${tabCountList[2] || 0})`" name="-1">
            <div class="searchBar">
              <el-col :span="6">
                <el-input
                  v-model="knowledgeSearchData"
                  placeholder="请输入知识主题"
                  clearable
                  @keyup.enter.native="refreshTable"
                ></el-input>
              </el-col>
              <el-button class="trasen-search" @click="refreshTable"
                >搜索</el-button
              >
              <div
                class="trasen-search-reset"
                @click="
                  () => {
                    knowledgeSearchData = '';
                    refreshTable();
                  }
                "
              >
                <i class="el-icon-refresh-right"></i>
              </div>
              <div class="float-right">
                <el-button
                  class="trasen-btn trasen-perpul"
                  type="primary"
                  @click="openEditModal()"
                  >新增</el-button
                >
                <el-button class="trasen-btn" @click="handleExport"
                  >导出</el-button
                >
              </div>
            </div>

            <div class="tableBox">
              <content-table v-bind="failTable" ref="table-1">
                <template slot="actionRow" slot-scope="scope">
                  <ActionCell
                    :renderFunc="renderFailAction"
                    :scope="scope"
                  ></ActionCell>
                </template>
              </content-table>
            </div>
          </el-tab-pane>

          <!-- 已移除 -->
          <el-tab-pane :label="`已移除(${tabCountList[3] || 0})`" name="3">
            <div class="searchBar">
              <el-col :span="6">
                <el-input
                  v-model="knowledgeSearchData"
                  placeholder="请输入知识主题"
                  clearable
                  @keyup.enter.native="refreshTable"
                ></el-input>
              </el-col>
              <el-button class="trasen-search" @click="refreshTable"
                >搜索</el-button
              >
              <div
                class="trasen-search-reset"
                @click="
                  () => {
                    knowledgeSearchData = '';
                    refreshTable();
                  }
                "
              >
                <i class="el-icon-refresh-right"></i>
              </div>
              <div class="float-right">
                <el-button
                  class="trasen-btn trasen-perpul"
                  type="primary"
                  @click="openEditModal()"
                  >新增</el-button
                >
                <el-button class="trasen-btn" @click="handleExport"
                  >导出</el-button
                >
              </div>
            </div>

            <div class="tableBox">
              <content-table v-bind="removedTable" ref="table3">
                <template slot="actionRow" slot-scope="scope">
                  <ActionCell
                    :renderFunc="renderRemovedAction"
                    :scope="scope"
                  ></ActionCell>
                </template>
              </content-table>
            </div>
          </el-tab-pane>

          <!-- 草稿箱 -->
          <el-tab-pane :label="`草稿箱(${tabCountList[4] || 0})`" name="0">
            <div class="searchBar">
              <el-col :span="6">
                <el-input
                  v-model="knowledgeSearchData"
                  placeholder="请输入知识主题"
                  clearable
                  @keyup.enter.native="refreshTable"
                ></el-input>
              </el-col>
              <el-button class="trasen-search" @click="refreshTable"
                >搜索</el-button
              >
              <div
                class="trasen-search-reset"
                @click="
                  () => {
                    knowledgeSearchData = '';
                    refreshTable();
                  }
                "
              >
                <i class="el-icon-refresh-right"></i>
              </div>
              <div class="float-right">
                <el-button
                  class="trasen-btn trasen-perpul"
                  type="primary"
                  @click="openEditModal()"
                  >新增</el-button
                >
                <el-button class="trasen-btn" @click="handleExport"
                  >导出</el-button
                >
              </div>
            </div>

            <div class="tableBox">
              <content-table v-bind="draftsTable" ref="table0">
                <template slot="actionRow" slot-scope="scope">
                  <ActionCell
                    :renderFunc="renderDraftsAction"
                    :scope="scope"
                  ></ActionCell>
                </template>
              </content-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 新增、编辑、审核弹框 -->
    <el-dialog
      :title="editModalTitle"
      :visible.sync="showEditModal"
      @close="closeEditModal"
      v-if="showEditModal"
      :close-on-click-modal="false"
      :modal-append-to-body="false"
      custom-class="edit-modal"
      class="editForm"
      width="900px"
    >
      <el-form :model="editData" ref="editForm" :rules="editRules">
        <div
          :class="{
            'examine-knowledge-content': editModalTitle == '知识点审核'
          }"
        >
          <el-row>
            <el-col :span="10">
              <el-form-item label="知识类型" prop="fkKnowledgeTypeId">
                <InputTree
                  :treeData="knowledgeTypeTreeData"
                  placeholder="请选择知识类型"
                  textName="fullPath"
                  style="width: 350px;"
                  v-model="editData.fkKnowledgeTypeId"
                  v-if="showEditModal"
                ></InputTree>
              </el-form-item>
            </el-col>
            <el-col :span="4">&emsp;</el-col>

            <el-col :span="10">
              <el-form-item label="推荐工时" prop="recommendedWorkHours">
                <el-input
                  v-model="editData.recommendedWorkHours"
                  class="addform-input"
                  placeholder="请输入推荐工时"
                  onkeyup="value = (value.match(/\d{1,4}(\.\d{0,1}|\.{0,1})/) || [''])[0]"
                  @blur="
                    editData.recommendedWorkHours = parseFloat(
                      editData.recommendedWorkHours
                    )
                  "
                ></el-input>
                <div class="hour">H</div>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item label="知识主题" prop="knowledgeTitle">
            <el-input
              v-model="editData.knowledgeTitle"
              maxlength="250"
              class="addform-input"
              placeholder="请输入知识点标题"
            ></el-input>
          </el-form-item>

          <div class="flex textarea-content">
            <span style="white-space: nowrap;margin: 0 7px 0 -30px;"
              >描述及解决方案</span
            >
            <Tynimce
              :content="editData.knowledgeContent"
              ref="tyni"
              style="width: 100%"
              v-if="showEditModal"
            ></Tynimce>
          </div>
        </div>

        <div class="examine-content" v-if="editModalTitle == '知识点审核'">
          <el-form-item
            label="审核结果"
            prop="knowledgeStatus"
            class="reset-el-form-item"
          >
            <el-radio-group v-model="editData.knowledgeStatus">
              <el-radio :label="2">通过</el-radio>
              <el-radio :label="-1">不通过</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item
            label="打回原因"
            prop="remark"
            v-if="editData.knowledgeStatus == '-1'"
          >
            <el-input
              placeholder="请输入不通过原因"
              v-model="editData.backReason"
              maxlength="100"
              :show-word-limit="true"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <template slot="footer">
        <div class="flex flex-center" v-show="editModalTitle != '知识点审核'">
          <el-button class="trasen-btn" @click="handleSaveAsDrafts"
            >存草稿</el-button
          >
          <el-button
            class="trasen-btn trasen-perpul"
            type="primary"
            @click="handleAddKnowledge"
          >
            提交
          </el-button>
          <el-button class="trasen-btn" @click="closeEditModal">取消</el-button>
        </div>
        <div class="flex flex-center" v-show="editModalTitle == '知识点审核'">
          <el-button class="trasen-btn" type="primary" @click="handleExamine">
            审核提交
          </el-button>
          <el-button class="trasen-btn" @click="closeEditModal">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 撤回原因 -->
    <el-dialog title="撤回" :visible.sync="showWithDrawModal">
      <el-form :model="reasonForWD" ref="withDrawForm" :rules="withdrawRules">
        <el-form-item label="" prop="remark">
          <el-input
            type="textarea"
            :rows="8"
            resize="none"
            v-model="reasonForWD.remark"
            placeholder="请输入撤回原因"
            :show-word-limit="true"
            maxlength="300"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeWDModal">取 消</el-button>
        <el-button type="primary" @click="handleWithDraw">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 知识点详情 -->
    <el-dialog
      title="知识内容"
      @close="closeKnowledgeDetail"
      :visible.sync="showKnowledgeDetail"
      :destroy-on-close="true"
      :close-on-click-modal="false"
      width="900px"
      class="detailModal"
      :modal-append-to-body="false"
    >
      <div class="top-title">
        <span>类型:</span>
        <span class="content-span">{{ knowlegeDetail.typeName }}</span>
        <span>贡献人:</span>
        <span class="content-span"
          >{{ knowlegeDetail.deptName }} - {{ knowlegeDetail.name }}</span
        >
        <span>贡献时间:</span>
        <span class="content-span">{{ knowlegeDetail.time }}</span>
        <span>推荐处理工时:</span
        ><span class="content-span">{{ knowlegeDetail.hours }}</span>
      </div>
      <div class="top-theme">
        <div class="left"></div>
        <span class="right">{{ knowlegeDetail.theme }}</span>
      </div>
      <div class="clear-class" v-html="knowlegeDetail.detail"></div>
      <div slot="footer" v-if="knowledgeCanZan">
        <div class="flex flex-center">
          <img
            class="zan-img"
            :src="knowlegeDetail.isLike ? zanBtn.zanfill : zanBtn.zan"
            @click="handleChangeUsefull"
          />
          {{ knowlegeDetail.usefulNumbers }}人认为有用
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import cellTable from '../comment/components/cellTabel.vue';
import actionCell from '../comment/components/actionCell.vue';
import tynimce from '@/components/tinymce/index';
import inputTree from '@/components/input-tree/inputTree.vue';

export default {
  components: {
    'content-table': cellTable,
    ActionCell: actionCell,
    Tynimce: tynimce,
    InputTree: inputTree
  },
  data() {
    return {
      active: '4',
      categoryName: '', //知识类型分类名称 用来做模糊搜索
      knowledgeTypeTreeData: [], //知识类型树 数据
      categoryTimer: null, //输入定时器
      currentKey: {}, //当前选中的current
      tabCountList: [], //头部tab数据列表

      API: {
        tabCountRefresh: '/ts-worksheet/knowledgeBase/selectCountsGroupStatus', //知识库头部刷新
        usefulKnowledge: '/ts-worksheet/knowledgeBase/giveALike/', //知识库点赞
        uselessKnowledge: '/ts-worksheet/knowledgeBase/cancelThePraise/', //知识库取消点赞
        knowledgeRefresh: '/ts-worksheet/knowledgeBase/knowledgeBasePageList', //知识库刷新 通过knowledgeStatus 来刷新
        removeKnowledge: '/ts-worksheet/knowledgeBase/remove', //移除知识点
        addKnowledge: '/ts-worksheet/knowledgeBase/save', //新增 保存 知识点
        getKnowledgeTypeTreeData:
          '/ts-worksheet/knowledgeBase/selectKnowledgeTreeAllList', //获取知识库类型树
        submitKnowledge: '/ts-worksheet/knowledgeBase/submit', //知识库草稿箱提交
        withdrawKnowledge: '/ts-worksheet/knowledgeBase/withdraw', //知识库审核中撤回
        knowledgeMoveIn: '/ts-worksheet/knowledgeBase/move', //知识库移入操作
        deleteDraft: '/ts-worksheet/knowledgeBase/delete' //草稿箱删除
      },

      //---------------- 弹框显示控制属性 及 弹框相关值 -----------------
      showEditModal: false, //控制  新增/编辑弹框
      editModalTitle: '新增知识点', // 新增/编辑 弹框标题
      editData: {}, // 新增/编辑  表单数据
      editRules: {
        // 新增/编辑    表单规则
        fkKnowledgeTypeId: [{ required: true, message: '知识类型不能为空' }],
        knowledgeTitle: [{ required: true, message: '知识点标题不能为空' }],
        backReason: [{ required: true, message: '不通过原因不能为空' }],
        recommendedWorkHours: [
          {
            message: '请输入正确的工时',
            trigger: 'blur',
            validator: function(rule, value, callback) {
              if (!value) {
                callback();
                return;
              }

              let val = Number(value);
              if (isNaN(val) || val <= 0) {
                callback('请输入正确的工时');
                return;
              }

              callback();
            }
          }
        ]
      },

      showWithDrawModal: false, //控制 撤回弹框 填写撤回原因
      reasonForWD: {}, // 撤回原因
      wdData: {}, //撤回对象
      withdrawRules: {
        remark: [{ required: true, message: '撤回原因不能为空' }]
      },

      knowlegeDetail: '', //知识点内容
      showKnowledgeDetail: false, //知识点内容详情展示 控制
      knowledgeCanZan: true, //知识点是否可以点赞
      zanBtn: {
        zan: require('@/assets/img/workSheet/knowledgeLibrary/zan.png'),
        zanfill: require('@/assets/img/workSheet/knowledgeLibrary/zan_fill.png')
      },
      usefullTimer: null, //点赞计时器
      //------------------------ 结束 ----------------------------------

      //发布中表格搜索数据
      knowledgeSearchData: '',
      //发布中表格配置
      knowledgeLibrary: {
        showIndex: true,
        defaultSort: { prop: 'contributionTime', order: 'descending' },
        columns: [
          {
            label: '知识主题',
            prop: 'knowledgeTitle',
            formatter: (row, column, val) => {
              return (
                <p class="knowledge-tableCell-title">
                  <span
                    class="knowledge-tableCell-title-text deal-link"
                    onClick={() => {
                      this.openKnowledgeDetail(row, true);
                    }}>
                    {val}
                  </span>
                </p>
              );
            }
          },
          {
            label: '推荐工时(H)',
            align: 'center',
            prop: 'recommendedWorkHours',
            width: 110
          },
          {
            label: '有用次数',
            prop: 'usefulNumbers',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'useful_numbers'
          },
          {
            label: '贡献人',
            prop: 'fkUserName',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'fk_user_name'
          },
          {
            label: '贡献时间',
            prop: 'contributionTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'contribution_time'
          }
        ],
        refresh: data => {
          return this.libraryRefresh(data);
        }
      },

      //审核中搜索数据
      examineSearchData: '',
      //审核中表格配置
      examineTable: {
        defaultSort: { prop: 'contributionTime', order: 'descending' },
        columns: [
          {
            label: '知识主题',
            prop: 'knowledgeTitle',
            formatter: (row, column, val) => {
              return (
                <p class="knowledge-tableCell-title">
                  <span
                    class="knowledge-tableCell-title-text deal-link"
                    onClick={() => {
                      this.openKnowledgeDetail(row);
                    }}>
                    {val}
                  </span>
                </p>
              );
            }
          },
          {
            label: '推荐工时(H)',
            prop: 'recommendedWorkHours',
            align: 'center',
            width: 110
          },
          {
            label: '有用次数',
            prop: 'usefulNumbers',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'useful_numbers'
          },
          {
            label: '贡献人',
            prop: 'fkUserName',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'fk_user_name'
          },
          {
            label: '贡献时间',
            prop: 'contributionTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'contribution_time'
          }
        ],
        refresh: data => {
          return this.examineRefresh(data);
        }
      },

      //未通过搜索数据
      failSearchData: '',
      //未通过表格配置
      failTable: {
        defaultSort: { prop: 'contributionTime', order: 'descending' },
        columns: [
          {
            label: '知识主题',
            prop: 'knowledgeTitle',
            formatter: (row, column, val) => {
              return (
                <p class="knowledge-tableCell-title">
                  <span
                    class="knowledge-tableCell-title-text deal-link"
                    onClick={() => {
                      this.openKnowledgeDetail(row);
                    }}>
                    {val}
                  </span>
                </p>
              );
            }
          },
          {
            label: '不通过原因',
            prop: 'backReason'
          },
          {
            label: '贡献人',
            prop: 'fkUserName',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'fk_user_name'
          },
          {
            label: '贡献时间',
            prop: 'contributionTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'contribution_time'
          },
          {
            label: '审批时间',
            prop: 'reviewTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'review_time'
          }
        ],
        refresh: data => {
          return this.failRefresh(data);
        }
      },

      //已移除搜索数据
      removedSearchData: '',
      //已移除表格配置
      removedTable: {
        defaultSort: { prop: 'contributionTime', order: 'descending' },
        columns: [
          {
            label: '知识主题',
            prop: 'knowledgeTitle',
            formatter: (row, column, val) => {
              return (
                <p class="knowledge-tableCell-title">
                  <span
                    class="knowledge-tableCell-title-text deal-link"
                    onClick={() => {
                      this.openKnowledgeDetail(row);
                    }}>
                    {val}
                  </span>
                </p>
              );
            }
          },
          {
            label: '有用次数',
            prop: 'usefulNumbers',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'useful_numbers'
          },
          {
            label: '贡献人',
            prop: 'fkUserName',
            width: 110,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'fk_user_name'
          },
          {
            label: '贡献时间',
            prop: 'contributionTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'contribution_time'
          },
          {
            label: '移除时间',
            prop: 'removeTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'remove_time'
          }
        ],
        refresh: data => {
          return this.removedRefresh(data);
        }
      },

      //草稿箱搜索数据
      draftsSearchData: '',
      // 草稿箱表格配置
      draftsTable: {
        defaultSort: { prop: 'createTime', order: 'descending' },
        columns: [
          {
            label: '知识主题',
            prop: 'knowledgeTitle',
            formatter: (row, column, val) => {
              return (
                <p class="knowledge-tableCell-title">
                  <span
                    class="knowledge-tableCell-title-text deal-link"
                    onClick={() => {
                      this.openKnowledgeDetail(row);
                    }}>
                    {val}
                  </span>
                </p>
              );
            }
          },
          {
            label: '推荐工时(H)',
            align: 'center',
            width: 110,
            prop: 'recommendedWorkHours'
          },
          {
            label: '创建时间',
            prop: 'createTime',
            width: 140,
            align: 'center',
            sortable: 'custom',
            sortOrders: ['ascending', 'descending'],
            sortIndex: 'create_time',
            formatter: (row, column, val) => {
              return this.$dayjs(val).format('YYYY-MM-DD HH:mm');
            }
          },
          {
            label: '备注',
            prop: 'remark',
            formatter: function(row, column, val, index) {
              return val ? '【撤回原因】' + val : '';
            }
          }
        ],
        refresh: data => {
          return this.draftsRefresh(data);
        }
      }
    };
  },
  created() {
    const type = this.$route.query.type;
    if (type === 'open') {
      setTimeout(() => {
        this.showEditModal = true;
      }, 500);
    } else if (type === 'submit') {
      this.active = '4';
    } else if (type === 'audit') {
      this.active = '1';
    }

    this.refreshTypeTree();
    this.refreshTabCount();
  },
  beforeRouteEnter(to, form, next) {
    next(vm => {
      const type = to.query.type;
      if (type === 'open') {
        vm.$nextTick(() => {
          vm.showEditModal = true;
        });
      } else if (type === 'submit') {
        vm.active = '4';
      } else if (type === 'audit') {
        vm.active = '1';
      }
    });
  },
  methods: {
    //处理表格框更改
    refresh() {
      this.refreshTypeTree();
      this.$refs[`table${this.active}`].refreshTable();
    },
    //刷新树列表
    refreshTypeTree() {
      this.$api({
        url: this.API.getKnowledgeTypeTreeData,
        method: 'get'
      }).then(res => {
        if (!res.success) {
          return;
        }
        this.knowledgeTypeTreeData = res.object || [];
        if (this.currentKey.select) {
          this.$nextTick(() => {
            this.$refs.knowledgeTree.setCurrentKey(this.currentKey.select);
          });
        }
      });
    },
    //刷新表格
    refreshTable(data) {
      if (Object.prototype.toString.call(data) != '[object Object]') {
        data = null;
      }
      this.refreshTabCount();
      this.$refs[`table${this.active}`].refreshTable(data);
    },
    //刷新页签数据
    refreshTabCount() {
      this.$api({
        url: this.API.tabCountRefresh,
        method: 'get',
        params: {
          knowledgeTitle: this.knowledgeSearchData || '',
          fkKnowledgeTypeId: this.currentKey.ids
        }
      }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '服务器出错了，请稍后再试');
          return;
        }
        this.tabCountList = [
          res.object.zskCounts,
          res.object.shzCounts,
          res.object.wtgCounts,
          res.object.yycCounts,
          res.object.cgxCounts
        ];
      });
    },
    //导出事件
    handleExport() {
      let tableData = this.$refs[`table${this.active}`].getTableData();
      let cols = tableData.col;
      let newTable = document.createElement('table'),
        newHead = document.createElement('tr');
      cols.forEach(item => {
        let th = document.createElement('th');
        th.innerHTML = item.label;
        newHead.append(th);
      });
      newTable.append(newHead);
      tableData.data.forEach(item => {
        let tr = document.createElement('tr');
        cols.forEach(col => {
          let td = document.createElement('td');
          td.innerHTML = item[col.prop];
          tr.append(td);
        });
        newTable.append(tr);
      });
      let _this = this;

      var tableToExcel = (function() {
        var uri = 'data:application/vnd.ms-excel;base64,',
          template =
            '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
          base64 = function(s) {
            return window.btoa(unescape(encodeURIComponent(s)));
          },
          // 下面这段函数作用是：将template中的变量替换为页面内容ctx获取到的值
          format = function(s, c) {
            return s.replace(/{(\w+)}/g, function(m, p) {
              return c[p];
            });
          };
        return function(table, name) {
          // 获取表单的名字和表单查询的内容
          var ctx = { worksheet: name || 'Worksheet', table: table.innerHTML };
          // format()函数：通过格式操作使任意类型的数据转换成一个字符串
          // base64()：进行编码
          let a = document.createElement('a');
          a.href = uri + base64(format(template, ctx));
          let title = '';
          switch (_this.active) {
            case '4': //知识库
              title = '知识库';
              break;
            case '1': //审核中
              title = '审核中';
              break;
            case '': //未通过
              title = '未通过';
              break;
            case '3': //已移除
              title = '已移除';
              break;
            case '0': //草稿箱
              title = '草稿箱';
              break;
            default:
              break;
          }
          a.download = title + '表格.xls'; //设置被下载的超链接目标（文件名）
          a.click();
        };
      })();

      tableToExcel(newTable);
    },
    //-------------------- 弹框管理 -----------------------
    //打开新增/编辑弹框
    openEditModal(row) {
      this.editModalTitle = '新增知识点';
      this.editData = {};

      if (row) {
        this.editData = row;
        this.editModalTitle = '编辑';
      }
      this.showEditModal = true;
    },
    //关闭新增/编辑弹框
    closeEditModal() {
      this.showEditModal = false;
      this.$refs.editForm.resetFields();
      this.editData = {};
    },

    //以审批的方式打开新增弹框
    openExamineModal(row) {
      this.editModalTitle = '知识点审核';
      this.editData = {
        ...row,
        knowledgeStatus: 2
      };
      this.showEditModal = true;
    },

    //打开撤回弹框
    openWDModal(row) {
      this.reasonForWD = {
        pkKnowledgeBaseId: row.pkKnowledgeBaseId,
        knowledgeStatus: 4
      };
      // this.wdData = row;
      this.showWithDrawModal = true;
      this.$refs.withDrawForm.clearValidate();
    },
    //关闭撤回弹框
    closeWDModal() {
      this.showWithDrawModal = false;
      this.reasonForWD = {};
      this.$refs.withDrawForm.clearValidate();
      // this.wdData = {};
    },

    //打开知识库详情
    openKnowledgeDetail(row, canZan = false) {
      this.knowlegeDetail = {
        detail: row.knowledgeContent,
        oldLike: row.isLike,
        isLike: row.isLike,
        oldUseful: row.usefulNumbers,
        usefulNumbers: row.usefulNumbers,
        id: row.pkKnowledgeBaseId,
        typeName: row.fkKnowledgeTypeName,
        deptName: row.fkUserDeptName,
        name: row.fkUserName,
        hours: row.recommendedWorkHours,
        time: row.contributionTime,
        theme: row.knowledgeTitle
      };

      this.knowledgeCanZan = canZan;
      this.showKnowledgeDetail = true;
    },
    //关闭知识库详情
    closeKnowledgeDetail() {
      this.showKnowledgeDetail = false;
      this.$nextTick(() => {
        this.knowlegeDetail = {};
      });
    },

    //--------------------- 表格刷新 ---------------------------
    //知识库刷新
    libraryRefresh(searchData) {
      let data = {
        knowledgeStatus: 4,
        fkKnowledgeTypeId: this.currentKey.ids,
        ...searchData,
        knowledgeTitle: this.knowledgeSearchData || undefined
      };

      return this.$api({
        method: 'post',
        url: this.API.knowledgeRefresh,
        data: data,
        timeout: 5000
      }).then(res => {
        if (res.success == false) {
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //审核中表格刷新
    examineRefresh(searchData) {
      let data = {
        knowledgeStatus: 1,
        fkKnowledgeTypeId: this.currentKey.ids,
        ...searchData,
        knowledgeTitle: this.knowledgeSearchData || undefined
      };

      return this.$api({
        method: 'post',
        url: this.API.knowledgeRefresh,
        data: data,
        timeout: 5000
      }).then(res => {
        if (res.success == false) {
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //已移除表格刷新
    removedRefresh(searchData) {
      let data = {
        knowledgeStatus: 3,
        fkKnowledgeTypeId: this.currentKey.ids,
        ...searchData,
        // knowledgeTitle: this.removedSearchData || undefined
        knowledgeTitle: this.knowledgeSearchData || undefined
      };

      return this.$api({
        method: 'post',
        url: this.API.knowledgeRefresh,
        data: data,
        timeout: 5000
      }).then(res => {
        if (res.success == false) {
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //草稿箱表格刷新
    draftsRefresh(searchData) {
      let data = {
        knowledgeStatus: 0,
        fkKnowledgeTypeId: this.currentKey.ids,
        ...searchData,
        // knowledgeTitle: this.draftsSearchData || undefined
        knowledgeTitle: this.knowledgeSearchData || undefined
      };

      return this.$api({
        method: 'post',
        url: this.API.knowledgeRefresh,
        data: data,
        timeout: 5000
      }).then(res => {
        if (res.success == false) {
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },
    //审核失败表格刷新
    failRefresh(searchData) {
      let data = {
        knowledgeStatus: -1,
        fkKnowledgeTypeId: this.currentKey.ids,
        ...searchData,
        // knowledgeTitle: this.failSearchData || undefined
        knowledgeTitle: this.knowledgeSearchData || undefined
      };

      return this.$api({
        method: 'post',
        url: this.API.knowledgeRefresh,
        data: data,
        timeout: 5000
      }).then(res => {
        if (res.success == false) {
          return Promise.reject();
        }

        let data = {
          list: res.rows || [],
          pageNo: res.pageNo,
          pageSize: res.pageSize,
          total: res.totalCount || 0
        };
        return data;
      });
    },

    //-------------------- 事件处理 ---------------------
    //处理转发事件
    handleForward(row) {
      this.$devopParentTypeFun({
        type: 'changePath',
        data: '/email/emailManagement'
      });
      this.$devopParentTypeFun({
        type: 'sendMessageToOldFrame',
        detail: {
          type: 'orderSheeetShareEmail',
          data: { type: 'inside', data: row }
        }
      });
    },
    //处理移除事件
    handleRemove(row) {
      this.$confirm('确定要移除知识点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          //待完成 ——等待能够使用接口
          let data = {
            knowledgeStatus: 1,
            pkKnowledgeBaseId: row.pkKnowledgeBaseId
          };

          this.$api({
            url: this.API.removeKnowledge,
            method: 'POST',
            data: JSON.stringify(data),
            headers: {
              'Content-Type': 'application/json; charset=utf-8'
            }
          }).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '出错啦');
              return Promise.reject();
            }

            this.$message.success('成功移除');
            this.refreshTable();
          });
        })
        .catch(() => {});
    },
    //处理审核事件
    handleExamine() {
      this.$refs.editForm.validate(res => {
        if (!res) {
          return;
        }

        let {
          fkKnowledgeTypeId,
          knowledgeContent,
          knowledgeStatus,
          knowledgeTitle,
          pkKnowledgeBaseId,
          recommendedWorkHours,
          backReason
        } = this.editData;
        let saveData = {
          fkKnowledgeTypeId,
          knowledgeContent,
          knowledgeStatus,
          knowledgeTitle,
          pkKnowledgeBaseId,
          recommendedWorkHours,
          backReason
        };
        if (saveData.knowledgeStatus == 2) {
          saveData.backReason = null;
        }

        this.$api({
          url: this.API.addKnowledge,
          method: 'POST',
          data: JSON.stringify(saveData),
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          this.closeEditModal();
          if (!res.success) {
            this.$message.error(res.message || '出错啦');
            return;
          }

          this.$message.success('审核成功');
          this.refreshTable();
        });
      });
    },
    //处理撤回事件
    handleWithDraw(row) {
      this.$refs.withDrawForm.validate(res => {
        if (!res) {
          return;
        }

        this.$api({
          url: this.API.withdrawKnowledge,
          method: 'post',
          data: this.reasonForWD,
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          this.closeWDModal();
          if (!res) {
            this.$message.error(res.message || '出错啦');
          }

          this.$message.success('撤回成功');
          this.refreshTable();
        });
      });
    },
    //处理移入操作
    handleMoveIn(row) {
      this.$confirm('确定要移入知识库吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api({
            url: this.API.knowledgeMoveIn,
            method: 'post',
            data: JSON.stringify({
              knowledgeStatus: 5,
              pkKnowledgeBaseId: row.pkKnowledgeBaseId
            }),
            headers: {
              'Content-Type': 'application/json; charset=utf-8'
            }
          }).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '出错啦');
              return;
            }

            this.$message.success('移入成功');
            this.refreshTable();
          });
        })
        .catch(() => {});
    },
    //处理草搞箱删除
    handleDeleteDrafts(row) {
      this.$confirm('删除后无法恢复，确定要删除该知识点吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$api({
            url: this.API.deleteDraft,
            method: 'post',
            data: JSON.stringify({
              knowledgeStatus: 7,
              pkKnowledgeBaseId: row.pkKnowledgeBaseId
            }),
            headers: {
              'Content-Type': 'application/json; charset=utf-8'
            }
          }).then(res => {
            if (!res.success) {
              this.$message.error(res.message || '出错啦');
              return;
            }
            this.$message.success('删除成功');
            this.refreshTable();
          });
        })
        .catch(() => {});
    },
    //处理提交事件
    handleSubmit(row) {
      let saveData = {
        knowledgeStatusj: 6,
        pkKnowledgeBaseId: row.pkKnowledgeBaseId
      };
      this.$api({
        url: this.API.submitKnowledge,
        method: 'POST',
        data: JSON.stringify(saveData),
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '出错啦');
          return;
        }

        this.$message.success('提交成功');
        this.refreshTable();
      });
    },
    //新增/编辑 存草稿操作
    handleSaveAsDrafts() {
      let knowledgeContent = this.$refs.tyni.getData();
      let saveData = {
        ...this.editData,
        knowledgeContent,
        knowledgeStatus: 0
      };

      this.$api({
        url: this.API.addKnowledge,
        method: 'POST',
        data: JSON.stringify(saveData),
        headers: {
          'Content-Type': 'application/json; charset=utf-8'
        }
      }).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '出错啦');
          return;
        }
        this.$message.success('保存成功');
        this.closeEditModal();
        this.refreshTable();
      });
    },
    //新增/编辑 保存，提交操作
    handleAddKnowledge() {
      //待完成
      this.$refs.editForm.validate(res => {
        if (!res) {
          return;
        }

        let knowledgeContent = this.$refs.tyni.getData();
        //待完成
        let saveData = {
          ...this.editData,
          knowledgeContent,
          knowledgeStatus: 1
        };
        this.$api({
          url: this.API.addKnowledge,
          method: 'POST',
          data: JSON.stringify(saveData),
          headers: {
            'Content-Type': 'application/json; charset=utf-8'
          }
        }).then(res => {
          this.closeEditModal();
          if (!res.success) {
            this.$message.error(res.message || '出错啦');
            return;
          }
          this.$message.success('保存成功');
          this.refreshTable();
        });
      });
    },
    //左侧知识类型树搜索
    handleRefreshKnowledgeTree(event) {
      this.categoryTimer && clearTimeout(this.categoryTimer);

      if (event.keyCode == 13) {
        this.$refs.knowledgeTree.filter(this.categoryName);
        // this.handleRefreshTree();
      } else {
        this.categoryTimer = setTimeout(() => {
          this.$refs.knowledgeTree.filter(this.categoryName);
          // this.handleRefreshTree();
        }, 500);
      }
    },
    //处理类型树点击
    handleTypeTreeClick(data) {
      if (this.currentKey.select == data.id) {
        this.$refs.knowledgeTree.setCurrentKey(null);
        this.currentKey.select = null;
        this.currentKey.ids = '';
      } else {
        this.currentKey.select = data.id;
        let newCurrent = [data.id];
        this.getAllTypeId(data.children || [], newCurrent);
        this.currentKey.ids = newCurrent.join(',');
      }
      this.refreshTable();
    },
    //刷新知识库树
    handleRefreshTree() {
      if (!this.categoryName) {
        this.currentKey = null;
        this.$api({
          url: this.API.getKnowledgeTypeTreeData,
          method: 'get'
        }).then(res => {
          if (!res.success) {
            this.knowledgeTypeTreeData = [];
            this.$message.error(res.message || '知识库类型树加载失败');
            return;
          }

          this.knowledgeTypeTreeData = res.object || [];
          this.currentKey = null;
          this.refreshTable();
        });
      } else {
        this.$api({
          url: this.API.getKnowledgeTypeTreeData,
          method: 'get',
          data: { categoryName: this.categoryName }
        }).then(res => {
          if (!res.success) {
            this.knowledgeTypeTreeData = [];
            this.$message.error(res.message || '知识库类型树加载失败');
            return;
          }
          this.knowledgeTypeTreeData = res.object || [];
          let newCurrent = [];
          this.getAllTypeId(this.knowledgeTypeTreeData, newCurrent);
          this.currentKey = newCurrent.join(',');
          this.refreshTable();
        });
      }
    },
    //知识点点赞、取消点赞
    handleChangeUsefull() {
      let url = this.knowlegeDetail.oldLike
        ? this.API.uselessKnowledge
        : this.API.usefulKnowledge;
      this.usefullTimer && clearTimeout(this.usefullTimer);

      if (this.knowlegeDetail.oldLike) {
        this.knowlegeDetail.usefulNumbers = this.knowlegeDetail.oldUseful - 1;
        this.knowlegeDetail.isLike = 0;
      } else {
        this.knowlegeDetail.usefulNumbers =
          this.knowlegeDetail.oldUseful / 1 + 1;
        this.knowlegeDetail.isLike = 1;
      }

      this.usefullTimer = setTimeout(() => {
        this.$api({
          url: url + this.knowlegeDetail.id,
          type: 'get'
        })
          .then(res => {
            if (res.success) {
              if (this.knowlegeDetail.oldLike) {
                this.knowlegeDetail.oldUseful =
                  this.knowlegeDetail.oldUseful - 1;
                this.knowlegeDetail.oldLike = 0;
              } else {
                this.knowlegeDetail.oldUseful =
                  this.knowlegeDetail.oldUseful / 1 + 1;
                this.knowlegeDetail.oldLike = 1;
              }
              this.refreshTable();
            } else {
              this.$message.error(res.message || '出错啦');
              this.knowlegeDetail.isLike = this.knowlegeDetail.oldLike;
              this.knowlegeDetail.usefulNumbers = this.knowlegeDetail.oldUseful;
            }
          })
          .catch(err => {});
      }, 500);
    },
    //处理树改变，导致的el-scrollbar为刷新的情况
    handleTypeTreeChange() {
      setTimeout(() => {
        this.$refs.treeScroll.update();
      }, 350);
    },

    //------------------------ 渲染操作 -------------------------
    //渲染知识库操作栏
    renderKnowledgeAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row)); // 行数据

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleForward(row)}>
                  <i class="fa fa-share-square-o"></i>转发
                </div>
              </ElDropdownItem>
              {row.peopleType == 1 ? (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.handleRemove(row)}>
                    <i class="fa fa-unlink"></i>移除
                  </div>
                </ElDropdownItem>
              ) : (
                ''
              )}
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },

    //渲染审核中操作栏
    renderExamineAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row)); // 行数据

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              {row.thePeople ? (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.openWDModal(row)}>
                    <i class="fa fa-repeat"></i>撤回
                  </div>
                </ElDropdownItem>
              ) : (
                ''
              )}
              {row.peopleType == 1 ? (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.openExamineModal(row)}>
                    <i class="fa fa-pencil-square-o"></i>审批
                  </div>
                </ElDropdownItem>
              ) : (
                ''
              )}
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },
    //渲染已移除操作栏
    renderRemovedAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row)); // 行数据

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              {row.peopleType == 0 ? (
                ''
              ) : (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.handleMoveIn(row)}>
                    <i class="fa fa-link"></i>移入
                  </div>
                </ElDropdownItem>
              )}
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },
    //渲染草稿箱操作栏
    renderDraftsAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row)); // 行数据

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleSubmit(row)}>
                  <i class="fa fa-external-link"></i>提交
                </div>
              </ElDropdownItem>
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleDeleteDrafts(row)}>
                  <i class="fa fa-trash-o"></i>删除
                </div>
              </ElDropdownItem>
              {row.thePeople ? (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.openEditModal(row)}>
                    <i class="fa fa-pencil-square-o"></i>编辑
                  </div>
                </ElDropdownItem>
              ) : (
                ''
              )}
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },
    //渲染审核失败操作栏
    renderFailAction(scope) {
      let row = JSON.parse(JSON.stringify(scope.row)); // 行数据

      if (!row.thePeople) {
        return (
          <div class="action-row">
            <ElDropdown trigger="hover" placement="bottom-end">
              <div class="more-action-icon">
                <i class="layui-icon layui-icon-more-vertical"></i>
              </div>
              <ElDropdownMenu
                slot="dropdown"
                placement="bottom-end"></ElDropdownMenu>
            </ElDropdown>
          </div>
        );
      }

      return (
        <div class="action-row">
          <ElDropdown trigger="hover" placement="bottom-end">
            <div class="more-action-icon">
              <i class="layui-icon layui-icon-more-vertical"></i>
            </div>
            <ElDropdownMenu slot="dropdown" placement="bottom-end">
              <ElDropdownItem class="action-item">
                <div onClick={() => this.handleSubmit(row)}>
                  <i class="fa fa-external-link"></i>重提
                </div>
              </ElDropdownItem>
              {row.num != 20 ? (
                <ElDropdownItem class="action-item">
                  <div onClick={() => this.openEditModal(row)}>
                    <i class="fa fa-pencil-square-o"></i>编辑
                  </div>
                </ElDropdownItem>
              ) : (
                ''
              )}
            </ElDropdownMenu>
          </ElDropdown>
        </div>
      );
    },

    //过滤知识类型树
    filterKnowledgeTree(value, data, node) {
      if (!value) {
        return true;
      }
      let parentNode = node.parent,
        labels = [node.label],
        level = 1;
      while (level < node.level) {
        labels = [...labels, parentNode.label];
        parentNode = parentNode.parent;
        level++;
      }
      return labels.some(label => label.indexOf(value) !== -1);
    },
    //冲新渲染树的label
    computTreeName(data, node) {
      return `${data.name} (${data.count})`;
    },
    getAllTypeId(list, resList) {
      list.forEach(item => {
        resList.push(item.id);
        if (item.children) {
          this.getAllTypeId(item.children, resList);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
/deep/.v-modal {
  z-index: 500 !important;
}
/deep/.reset-el-form-item {
  display: flex;
  align-items: center;
  .el-form-item__content {
    margin-bottom: 0 !important;
  }
}
.examine-content {
  width: calc(100% + 100px);
  margin-top: 10px;
  padding: 0 50px;
  padding-top: 10px;
  margin-left: -50px;
  border-top: 2px solid #d0d0d0;
}
.examine-knowledge-content {
  max-height: 60vh;
  overflow: auto;
  padding-right: 10px;
  padding-bottom: 20px;
}
.examine-knowledge-content::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
.examine-knowledge-content::-webkit-scrollbar-thumb {
  border-radius: 6px;
  height: 50px;
  background: rgba(153, 153, 153, 0.4);
  &:hover {
    background: rgba(153, 153, 153, 0.8);
  }
}
.examine-knowledge-content::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  background: #fff;
}

.knowledge-tableCell-title {
  cursor: pointer;
}

.zan-img {
  width: 16px;
  margin-right: 10px;
}
.editForm {
  z-index: 501 !important;
}
.content {
  width: 100%;
  height: 100%;
  // padding: 80px 10px 10px 170px;
}
.realContent {
  height: 100%;
  width: 100%;
  display: flex;
  padding-bottom: 8px;

  .content-left {
    margin-right: 8px;
    padding: 8px;
    background-color: #fff;
    border-radius: 4px;
    width: 220px;
    max-width: 220px;
    min-width: 220px;
    overflow: hidden;
    > .el-input {
      margin-bottom: 8px;
    }
    // /deep/ {
    //   .el-scrollbar {
    //     height: calc(100% - 40px);
    //     width: 100%;
    //   }
    //   .el-scrollbar__wrap {
    //     overflow-x: auto;
    //     height: calc(100% + 20px);
    //   }
    //   .el-tree-node > .el-tree-node__children {
    //     overflow: initial;
    //   }
    // }
  }

  .content-right {
    flex: 1;
    position: relative;
    background-color: #fff;
    border-radius: 4px;
    padding: 8px;

    .el-tabs {
      height: 100%;
      display: flex;
      flex-direction: column;
      /deep/.el-tabs__content {
        height: 100%;
        flex: 1;
        padding-bottom: 2px;
      }

      .el-tab-pane {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
  }
}
.textarea-content {
  min-height: 70vh;
}
.hour {
  height: 30px;
  width: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  background-color: #4e6ef2;
  margin-left: 5px;
  border-radius: 3px;
  font-weight: 600;
}
.editForm {
  /deep/ .el-form {
    padding: 0 50px;
  }
  /deep/.el-form-item__content {
    display: flex;
  }
}

.searchBar {
  margin-bottom: 8px;
  .float-right {
    float: right;
  }
}

.more-action-icon {
  height: 30px;
  width: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover i {
    color: #5260ff;
  }
}

.flex {
  display: flex;
}
.flex-center {
  justify-content: center;
  align-items: center;
}
.action-row {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
}
.tableBox {
  height: 100%;
  flex: 1;
}
.trasen-btn {
  // border: 1px transparent solid;
  border-radius: 2px;
  line-height: 28px;
  min-width: 60px;
  padding: 0 8px;
  height: 30px;
  font-size: 14px;
}
.trasen-search {
  background-color: #5260ff;
  // display: inline-block;
  border: 1px #5260ff solid;
  border-radius: 2px;
  color: #fff;
  line-height: 28px;
  min-width: 60px;
  padding: 0 8px;
  height: 30px;
  font-size: 14px;
  margin-left: 8px;
}
.trasen-search-reset {
  display: inline-block;
  background-color: #fff;
  // border: 1px #222 solid;
  // border-radius: 2px;
  // color: #222;
  border: none;
  line-height: 30px;
  // min-width: 60px;
  width: 30px;
  text-align: center;
  padding: 0;
  height: 30px;
  font-size: 14px;
  margin-left: 5px;
  cursor: pointer;
}
.trasen-search-reset:hover {
  color: #5260ff;
}

.detailModal .top-title {
  margin-bottom: 15px;
}

.clear-class {
  /deep/ * {
    font: revert;
    padding: revert;
    margin: initial;
    list-style-type: revert;
    text-decoration: revert;
  }
}
.detailModal .top-title > span {
  &.content-span {
    color: #0c0c0c;
    margin: 0 15px 0 5px;
  }
}

.detailModal .top-theme {
  display: flex;
  height: 20px;
  align-items: center;
  margin-bottom: 10px;
  .left {
    width: 5px;
    height: 100%;
    background: #5260ff;
    margin-right: 10px;
  }
  .right {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    line-height: 22px;
  }
}
/deep/.detailModal .el-dialog__body {
  // height: 50vh;
  overflow: auto;
  padding-top: 5px;
}
/deep/.detailModal .el-dialog__body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
/deep/.detailModal .el-dialog__body:hover::-webkit-scrollbar-thumb {
  border-radius: 6px;
  height: 50px;
  background: rgba(153, 153, 153, 0.4);
  &:hover {
    background: rgba(153, 153, 153, 0.8);
  }
}
/deep/.detailModal .el-dialog__body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  background: #fff;
}
.first-tree-icon {
  background: url(../../../assets/img/other/ztree_all.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.has-child-tree-icon {
  background: url(../../../assets/img/other/ztree_folder.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.leaf-tree-icon {
  background: url(../../../assets/img/other/ztree_file.png) no-repeat;
  display: inline-block;
  height: 16px;
  width: 16px;
  margin-right: 2px;
}
.tree-scroll {
  width: 100%;
  height: calc(100% - 40px);
}
/deep/ {
  .tree-scroll-wrap {
    height: calc(100% + 17px);
  }
  .el-tree {
    display: inline-block;
  }
}
</style>
