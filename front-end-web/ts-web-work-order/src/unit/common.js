import { Base64 } from 'js-base64';

export default {
  isDoc: function(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ofd|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg: function(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  random4: function() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  },
  createUUID: function() {
    return (
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4() +
      this.random4()
    );
  },
  viewerDocBase: function(path, filename) {
    var url = '';
    if (path.indexOf('http') >= 0) {
      url = path + '?fullfilename=' + filename;
    } else {
      url = `${this.$config.DOCUMENT_BASE_HOST}${path}?fullfilename=${filename}`;
    }
    let a = document.createElement('a');
    a.target = '_blank';
    a.href =
      location.origin +
      '/ts-preview/onlinePreview?url=' +
      encodeURIComponent(Base64.encode(url));
    a.click();
    return false;
  }
};
