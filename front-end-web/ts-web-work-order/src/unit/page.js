export default {
  data() {
    return {
      multipleSelection: [], //选中数据
      query: {
        pageNo: 0, //页码
        pageSize: 0 //页的大小
      },
      page: {
        total: 0
      }
    };
  },

  created() {
    this.query.pageSize = this.$store.state.common.pageSize;
  },
  methods: {
    /**@desc 点击查询**/
    queryClick() {
      this.query.pageNo = 1;
      this.pageSearch();
    },
    /**@desc  修改每页显示多少条
     * @param {Number} val 数据类型
     * **/
    handleSizeChange(val) {
      this.query.pageNo = 1;
      this.query.pageSize = val;
      this.pageSearch();
    },
    /**@desc 修改页码**/
    handleCurrentChange(val) {
      this.query.pageNo = val;
      this.pageSearch();
    },
    /**@desc table checkbox 全选反选**/

    handleSelectionChange(val) {
      this.multipleSelection = val;
      if (this.SelectionChange) {
        this.SelectionChange();
      }
    }
  }
};
