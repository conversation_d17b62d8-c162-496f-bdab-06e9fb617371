export default {
  data() {
    return {
      outerVisible: false, //第一层弹框
      innerVisible: false //第二层弹框
    };
  },
  watch: {
    outerVisible() {
      if (this.outerVisible) {
        this.openOuterVisibleFun && this.openOuterVisibleFun();
        this.$emit('openOuterVisibleFun');
      } else {
        this.closeOuterVisibleFun && this.closeOuterVisibleFun();
        this.$emit('closeOuterVisibleFun');
      }
    },
    innerVisible() {
      if (this.innerVisible) {
        this.openInnerVisibleFun && this.openInnerVisibleFun();
        this.$emit('openInnerVisibleFun');
      } else {
        this.closeInnerVisibleFun && this.closeInnerVisibleFun();
        this.$emit('closeInnerVisibleFun');
      }
    }
  },
  methods: {
    /**@desc 打开第一层弹框 并调用一个外用函数**/
    openOuterVisible() {
      this.outerVisible = true;
    },
    /**@desc 关闭第一层弹框 并调用一个外用函数**/
    closeOuterVisible() {
      this.outerVisible = false;
    },
    /**@desc 打开第二层弹框 并调用一个外用函数**/
    openInnerVisible() {
      this.innerVisible = true;
    },
    /**@desc 关闭第二层弹框 并调用一个外用函数**/
    closeInnerVisible() {
      this.innerVisible = false;
    }
  }
};
