export default {
  methods: {
    /**@desc 根据id获取详情
     * @param {Number} id
     * @param {String} funName
     * **/
    async getById(id, funName = 'detailHosts') {
      let res = await this.ajax[funName](id);
      if (res.code == 200) {
        let form = JSON.parse(JSON.stringify(this.form));
        for (let key in form) {
          form[key] = res.data[key];
        }
        this.form = form;
      }
    }
  }
};
