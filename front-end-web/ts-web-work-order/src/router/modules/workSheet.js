export default [
  {
    path: '/workSheet/orderKnowledgeBase',
    component: resolve =>
      require([`@/views/workSheet/knowledgeLibrary/index.vue`], resolve),
    styleName: '',
    name: '知识库'
  },
  {
    path: '/workSheet/knowledgeType',
    component: resolve =>
      require([`@/views/workSheet/knowledgeType/knowledgeType.vue`], resolve),
    styleName: '',
    name: '知识类型维护'
  },
  {
    path: '/workSheet/outsidePerson',
    component: resolve =>
      require([`@/views/workSheet/outsidePerson/outsidePerson.vue`], resolve),
    styleName: '',
    name: '院外人员维护'
  },
  {
    path: '/workSheet/voiceMenu',
    component: resolve =>
      require([`@/views/workSheet/voiceMenu/voiceMenu.vue`], resolve),
    styleName: '',
    name: '语音菜单维护'
  },
  {
    path: '/workSheet/largeScreen',
    component: resolve =>
      require([`@/views/workSheet/largeScreen/index.vue`], resolve),
    styleName: '',
    name: '大屏'
  },
  {
    path: '/workSheet/situation',
    component: resolve =>
      require([`@/views/workSheet/statistics/index.vue`], resolve),
    styleName: '',
    name: '数据统计'
  },
  {
    path: '/workSheet/message',
    component: resolve =>
      require([`@/views/workSheet/message/index.vue`], resolve),
    styleName: '',
    name: '消息中心'
  },
  {
    path: '/workSheet/workOrderHomePage/handlerPerson',
    component: resolve =>
      require([
        '@/views/workSheet/workOrderHomePage/handlerPerson/index.vue'
      ], resolve),
    styleName: '',
    name: '办理人首页'
  },
  {
    path: '/workSheet/workOrderHomePage/applicantPerson',
    component: resolve =>
      require([
        `@/views/workSheet/workOrderHomePage/applicantPerson/index.vue`
      ], resolve),
    styleName: '',
    name: '申请人首页'
  },
  {
    path: '/workSheet/workOrderHomePage/servicePerson',
    component: resolve =>
      require([
        `@/views/workSheet/workOrderHomePage/servicePerson/index.vue`
      ], resolve),
    styleName: '',
    name: '服务台首页'
  },
  {
    path: '/workSheet/reception',
    component: resolve =>
      require([`@/views/workSheet/reception/index.vue`], resolve),
    styleName: '',
    name: '服务台'
  },
  {
    path: '/workSheet/workSheetSet',
    component: resolve =>
      require([`@/views/workSheet/workSheetSet/index.vue`], resolve),
    styleName: '',
    name: '多院区维护'
  },
  {
    path: '/workSheet/costManage/costAggregation',
    component: resolve =>
      require([
        `@/views/workSheet/orderCostManage/costAggregation/index.vue`
      ], resolve),
    styleName: '',
    name: '费用汇总'
  },
  {
    path: '/workSheet/costManage/myOrderCost',
    component: resolve =>
      require([
        `@/views/workSheet/orderCostManage/myOrderCost/index.vue`
      ], resolve),
    styleName: '',
    name: '我的费用'
  }
];
