export default {
  data() {
    return {
      showEditModal: false,
      baseInfo: {},
      editData: {},
      editActiveTab: 'basic',
      editType: 'registerEdit',
      editRules: {
        requiredRow: [{ required: true, message: '必填' }],
        isNeedApproval: [{ required: true, message: '必填字段请勿为空' }],
        distributeRules: [
          {
            required: true,
            message: '必填',
            validator: (rule, value, callback) => {
              if (
                this.editType == 'registerAdd' &&
                this.editData.isSolved == 2
              ) {
                let {
                  handledUserOrgCode,
                  handledUserCode,
                  requiredTime,
                  templateRemarkText
                } = this.editData;
                if (
                  !handledUserCode &&
                  !handledUserOrgCode &&
                  !requiredTime &&
                  !templateRemarkText
                ) {
                  callback();
                } else {
                  if (handledUserCode && handledUserOrgCode && requiredTime) {
                    callback();
                  } else if (value) {
                    callback();
                  } else {
                    callback(false);
                  }
                }
              } else {
                callback(value ? '' : 'false');
              }
            }
          }
        ],
        handleDept: [{ required: true, message: '必填字段请勿为空' }],
        templateRemarkText: [
          { required: { registerAdd: false }[this.editType], message: '必填' }
        ]
      },

      templatePageNo: 1,
      templateList: [],
      templateContentHeight: '30px'
    };
  },
  computed: {
    title() {
      return {
        registerAdd: '登记',
        registerEdit: '编辑',
        reject: '驳回',
        distribute: '分发',
        deal: '处理',
        approval: '审批',
        finished: '办结',
        file: '归档'
      }[this.editType];
    },
    hasVerticalReason() {
      return (
        ['file', 'registerEdit', 'registerAdd'].indexOf(this.editType) == -1 ||
        (this.editType == 'registerAdd' && this.editData.isSolved == 2)
      );
    },
    templateRemarkTitle() {
      if (this.editType == 'registerAdd' && this.editData.isSolved == 2) {
        return '处理意见';
      }

      return {
        reject: '驳回原因',
        distribute: '处理意见',
        deal: '处理情况（反馈记录及处理结果）',
        approval: '审批情况（反馈记录及处理结果）',
        finished: '办结情况（反馈记录及处理结果）'
      }[this.editType];
    }
  },
  methods: {
    /**@desc 刷新表格数据 */
    handleGetTableData() {
      let searchData = this.$refs.allTable.searchForm;
      let pageData =
        (this.tableDatas[this.activeTable] &&
          this.tableDatas[this.activeTable].pageData) ||
        {};
      this.ajax.getMyEventList({ ...searchData, ...pageData }).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '表格数据获取失败');
          return;
        }
        let data = { ...this.tableDatas[this.activeTable] };
        data.tableData = res.rows;
        data.pageData = {
          ...pageData,
          totalCount: res.totalCount
        };
        this.$set(this.tableDatas, this.activeTable, data);
      });
    },
    /**@desc 分发表格操作 */
    handleEvent(e, row) {
      this.editType = e;
      if (row.id) {
        this.ajax.getDisputeDetial(row.id).then(res => {
          if (!res.success) {
            this.$message.error(res.message || '纠纷详情获取失败');
            return;
          }
          this.showEditModal = true;
          this.baseInfo = res.object;
        });
      } else {
        this.baseInfo = {};
        this.showEditModal = true;
      }
      switch (e) {
        case 'registerAdd':
          this.editData = {
            isSolved: '1'
          };
          break;
        case 'deal':
          this.editData = {
            isSolved: '2',
            handledTime: this.$dayjs().format('YYYY-MM-DD')
          };
          break;
        default:
          this.editData = {};
          break;
      }

      this.$nextTick(() => {
        this.$refs.form && this.$refs.form.clearValidate();
      });
      //刷新编辑涉及到的数据
      this.refreshEditDatas();
    },
    /**@desc 关闭 操作 弹窗 */
    handleCancelModal() {
      this.editType = 'registerEdit';
      this.showEditModal = false;
      this.editData = {};
      this.baseInfo = {};
    },
    /**@desc 操作 弹窗 确认操作 */
    async handleSubmit() {
      let type = this.editType[0].toUpperCase() + this.editType.slice(1);
      this['handle' + type]();
    },
    /**@desc 点击选择模板 重新加载模板数据 */
    handleTriggerLoadTemplate() {
      this.templatePageNo = 1;
      this.templateList = [];
      let dom = this.$refs.templateContent.querySelector('.template-scrollbar');
      dom && dom.resetInfinityScrolling();
    },
    /**@desc 获取模板数据 */
    handleGetTemplate(cb) {
      setTimeout(() => {
        this.templateList = this.templateList.concat(
          new Array(10)
            .fill(1)
            .map((item, index) => index + (this.templatePageNo - 1) * 10)
        );
        cb(this.templatePageNo >= 4);
        this.templatePageNo++;

        this.$nextTick(() => {
          let height =
            (this.$refs.templateList && this.$refs.templateList.offsetHeight) ||
            30;
          this.$refs.scroll && this.$refs.scroll.update();
          this.templateContentHeight = height > 200 ? '200px' : height + 'px';
        });
      }, 5000);
    },
    /**@desc 使用模板填充驳回原因 */
    handleFillWithTemplate(template) {
      this.editData.templateRemarkText = template;
    },
    async handleReject() {
      this.$message.success('驳回成功');
      this.handleCancelModal();
    },
    /**@desc 登记 */
    async handleRegisterAdd() {
      let validateRes = await this.$refs.baseForm.validate(),
        validate = await this.$refs.form.validate().catch(res => res);
      if (!validateRes || !validate) {
        return;
      }
      let data = Object.assign({}, this.$refs.baseForm.form);

      if (editData.isSolved == 1) {
        data = Object.assign(data, this.editData);
        data.handledContent = data.templateRemarkText;
      } else {
        data.isSolved = 2;
      }

      this.ajax.handleRegisterDispute(data).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '登记失败');
          return;
        }
        this.$message.success('登记成功');
        this.handleTabClick();
        this.handleCancelModal();
      });
    },
    /**@desc 处理 */
    async handleDeal() {
      let validate = await this.$refs.form.validate().catch(res => res);
      if (!validate) {
        return;
      }
      let data = { ...this.editData };
      data.id = this.baseInfo.id;
      data.handledContent = data.templateRemarkText;
      delete data.templateRemarkText;

      let res = await this.ajax.handleDealDispute(data);
      if (!res.success) {
        this.$message.error(res.message || '操作失败');
        return;
      }

      this.handleTabClick();
      this.handleCancelModal();
      this.$message.success('操作成功');
    },
    /**@desc 编辑 */
    async handleRegisterEdit() {
      let validate = await this.$refs.baseForm.validate();
      if (!validate) {
        return;
      }
      let data = this.$refs.baseForm.form;

      let res = await this.ajax.handleRegisterEditDispute(data);
      if (!res.success) {
        this.$message.error(res.message || '编辑失败');
        return;
      }
      this.handleTabClick();
      this.handleCancelModal();
      this.$message.success('编辑成功');
    }
  }
};
