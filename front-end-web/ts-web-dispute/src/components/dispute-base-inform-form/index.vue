<template>
  <div class="dialog-content">
    <ts-form ref="baseForm" :model="form" label-width="83px">
      <template>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item
              label="事件类型"
              prop="eventType"
              :rules="rules.eventType"
            >
              <ts-select v-model="form.eventType" placeholder="请选择事件类型">
                <ts-option
                  v-for="item in eventTypeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="投诉时间"
              prop="occurrenceTime"
              :rules="rules.occurrenceTime"
            >
              <ts-date-picker
                v-model="form.occurrenceTime"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              ></ts-date-picker>
            </ts-form-item>
          </ts-col>
          <ts-col
            :span="6"
            v-if="renderType == 'registerAdd' || renderType == 'registerEdit'"
          >
            <ts-form-item
              label="事发医院:"
              prop="hospital"
              :rules="rules.hospital"
            >
              <ts-select
                v-model="form.hospital"
                placeholder="请选择事发医院"
                @change="handleHospitalChange"
              >
                <ts-option
                  v-for="item in options.hospitalList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.organizationId"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="投诉来源" prop="source" :rules="rules.source">
              <ts-select v-model="form.source" placeholder="请选择来源投诉">
                <ts-option
                  v-for="item in sourceList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="诉求分类"
              prop="appealType"
              :rules="rules.appealType"
            >
              <ts-select v-model="form.appealType" placeholder="请选择诉求分类">
                <ts-option
                  v-for="item of appealTypeList"
                  :key="item.itemNameValue"
                  :label="item.itemName"
                  :value="item.itemNameValue"
                ></ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="诉求程度"
              prop="appealDegree"
              :rules="rules.appealDegree"
            >
              <ts-select
                v-model="form.appealDegree"
                placeholder="请选择诉求程度"
              >
                <ts-option
                  v-for="item in options.appealDegreeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <!-- <ts-col :span="6">
            <ts-form-item>
              <ts-checkbox v-model="form.isMinorityCheckBox">
                少数民族案件
              </ts-checkbox>
            </ts-form-item>
          </ts-col> -->
        </ts-row>
      </template>
      <template>
        <p class="content-title">投诉人信息</p>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item
              label="投诉人"
              prop="complainName"
              :rules="rules.requiredRow"
            >
              <ts-input
                v-model="form.complainName"
                placeholder="请输入投诉人姓名"
                clearable
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="投诉时间"
              prop="complainTime"
              :rules="rules.complainTime"
            >
              <ts-date-picker
                v-model="form.complainTime"
                showTime
                format="YYYY-MM-DD HH:mm:ss"
              ></ts-date-picker>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="联系方式"
              prop="complainTel"
              :rules="rules.telephone"
            >
              <ts-input
                v-model="form.complainTel"
                maxlength="11"
                @input="
                  value => (form.complainTel = (value.match(/\d+/g) || [''])[0])
                "
                clearable
                placeholder="请输入手机号码"
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="与患者关系"
              prop="patientRelationship"
              label-width="90px"
              :rules="rules.requiredRow"
            >
              <ts-select
                v-model="form.patientRelationship"
                placeholder="请选择与患者关系"
                clearable
              >
                <ts-option
                  v-for="item in patientRelationshipList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </template>
      <template>
        <p class="content-title">患者信息</p>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item
              label="患者姓名"
              prop="patientName"
              :rules="rules.patientName"
            >
              <ts-input
                v-model="form.patientName"
                placeholder="请输入患者姓名"
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="身份证号"
              prop="patientIdcard"
              :rules="rules.patientIdcard"
            >
              <ts-input
                v-model="form.patientIdcard"
                placeholder="请输入身份证号码"
                maxlength="18"
                clearable
                @blur="form.patientIdcard = form.patientIdcard.trim()"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="年龄" prop="patientAge">
              <ts-input
                v-model="form.patientAge"
                placeholder="请输入患者年龄"
                @input="validateInputIntNum($event, 'form', 'patientAge')"
                clearable
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="性别" prop="patientSex">
              <ts-select v-model="form.patientSex" placeholder="请选择患者性别">
                <ts-option
                  v-for="item in options.sexList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="联系方式"
              prop="patientTel"
              :rules="rules.telephone"
            >
              <ts-input
                v-model="form.patientTel"
                maxlength="11"
                @input="
                  value => (form.patientTel = (value.match(/\d+/g) || [''])[0])
                "
                clearable
                placeholder="请输入手机号码"
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item label="地址" prop="patientAddress">
              <ts-input
                v-model="form.patientAddress"
                clearable
                placeholder="请输入地址"
              ></ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="12">
            <ts-form-item
              label="住院号/门诊号"
              prop="outpatientNo"
              label-width="110px"
            >
              <ts-input
                v-model="form.outpatientNo"
                clearable
                placeholder="请输入住院号/门诊号"
              ></ts-input>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </template>

      <template>
        <p class="content-title">被投诉人信息</p>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item label="被投诉人" prop="complainedOgrUser">
              <BaseSelect
                v-model="form.complainedOgrUser"
                :inputText.sync="form.complainedOgrUserName"
                :loadMethod="getPeopleListData"
                placeholder="请选择当事人"
                label="employeeName"
                value="employeeNo"
                searchInputName="employeeName"
                inputUsefull
                @select="handleComplainUserSelect"
              />
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item
              label="所属科室"
              prop="complainedOrg"
              :rules="rules.requiredRow"
            >
              <ts-ztree-select
                ref="deptSelect"
                :data="options.deptTreeData"
                :inpText.sync="form.complainedOrgName"
                :inpVal.sync="form.complainedOrg"
                @onCreated="handleExpand"
                placeholder="请选择所属科室"
                @change="handleSelectComplainedOrg"
              ></ts-ztree-select>
            </ts-form-item>
          </ts-col>
        </ts-row>
      </template>
      <template>
        <p class="content-title">事件详情</p>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item label="索赔金额" prop="claimAmount">
              <ts-input
                v-model="form.claimAmount"
                placeholder="请输入索赔金额"
                @input="validateTowDecimalPlaces($event, 'form', 'claimAmount')"
                @blur="handleTowDecimalPlacesBlur('form', 'claimAmount')"
              >
                <template slot="append">元</template>
              </ts-input>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="过激行为">
              <ts-select
                v-model="form.isExtreme"
                placeholder="请选择是否有过激行为"
                clearable
              >
                <ts-option label="有" :value="1"> </ts-option>
                <ts-option label="无" :value="0"> </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-row>
          <ts-col :span="6">
            <ts-form-item label="是否出警">
              <ts-select
                v-model="form.isCallPolice"
                placeholder="请选择是否出警"
                clearable
              >
                <ts-option label="是" :value="1"> </ts-option>
                <ts-option label="否" :value="0"> </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="警察是否立案" label-width="100px">
              <ts-select
                v-model="form.isCase"
                placeholder="请选择是否立案"
                clearable
              >
                <ts-option label="是" :value="1"> </ts-option>
                <ts-option label="否" :value="0"> </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
          <ts-col :span="6">
            <ts-form-item label="是否尸检">
              <ts-select
                v-model="form.isAutopsy"
                placeholder="请选择是否尸检"
                clearable
              >
                <ts-option label="有" :value="1"> </ts-option>
                <ts-option label="无" :value="0"> </ts-option>
              </ts-select>
            </ts-form-item>
          </ts-col>
        </ts-row>
        <ts-form-item label="附件上传">
          <BaseUpload v-model="form.businessId" tips="" />
        </ts-form-item>
        <ts-form-item label="投诉内容">
          <ts-input
            v-model="form.complainedContent"
            type="textarea"
            resize="none"
            maxlength="500"
            show-word-limit
            rows="4"
            placeholder="请输入投诉内容"
          ></ts-input>
        </ts-form-item>
        <ts-form-item label="科室初步处理">
          <ts-input
            v-model="form.treatmentMeasures"
            type="textarea"
            resize="none"
            maxlength="500"
            show-word-limit
            rows="4"
            placeholder="请输入事件处理及措施"
          ></ts-input>
        </ts-form-item>
        <ts-form-item v-if="renderType == 'registerAdd'" label="核实情况">
          <ts-input
            v-model="form.verification"
            type="textarea"
            resize="none"
            maxlength="500"
            show-word-limit
            rows="4"
            placeholder="请输入核实情况"
          ></ts-input>
        </ts-form-item>
      </template>
    </ts-form>
  </div>
</template>

<script>
import { appealDegreeList, sexList } from '@/assets/js/constants.js';
import baseUpload from '@/components/base-upload/index.vue';
import baseSelect from '@/components/base-select/index.vue';
import { commonUtils } from '@/utils/index';
import asyncData from '@/assets/js/getAsyncData.js';

export default {
  mixins: [asyncData],
  components: {
    BaseUpload: baseUpload,
    BaseSelect: baseSelect
  },
  props: {
    renderType: {
      type: String,
      default: ''
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {},
      rules: {
        requiredRow: [
          { required: true, message: '必填', trigger: ['blur', 'change'] }
        ],
        eventType: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        source: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        appealType: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        appealDegree: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        occurrenceTime: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        complainTime: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        hospital: [{ required: true, message: '必填', trigger: 'blur' }],
        patientName: [{ required: true, message: '必填', trigger: 'blur' }],
        patientAge: [{ required: true, message: '必填', trigger: 'blur' }],
        patientSex: [
          { required: true, message: '必选', trigger: ['blur', 'change'] }
        ],
        telephone: [
          { required: true, message: '必填', trigger: 'blur' },
          {
            message: '请输入正确的手机号',
            pattern: /1[^1-2]\d{9}/g
          }
        ],
        patientIdcard: [
          {
            message: '请输入正确的身份证号码格式',
            trigger: ['blur', 'change'],
            validator: (prop, value, callback) => {
              let trimVal = String(value).trim();

              let reg = /((1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|8[1-3])\d{4})(\d{4})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])(\d{3}([0-9xX]))/;

              if (!value || reg.test(trimVal)) {
                callback();
              } else {
                callback('请输入正确格式');
              }
            }
          }
        ]
      },
      options: {
        appealDegreeList,
        sexList,
        deptTreeData: [],
        hospitalList: []
      },
      loading: false,
      page: {
        num: 0,
        size: 10,
        total: 0
      }
    };
  },
  watch: {
    data: {
      handler(val) {
        this.handleDataChange(val);
      },
      immediate: true
    },
    'form.isMinorityCheckBox': {
      handler(val) {
        this.form.isMinority = val ? '1' : '2';
      },
      immediate: true
    },
    'form.complainTime'(val) {
      if (val) {
        this.$set(
          this.form,
          'complainTime',
          this.$dayjs(val).format('YYYY-MM-DD HH:mm:ss')
        );
      }
    },
    'form.patientIdcard'(val) {
      let reg = /((1[1-5]|2[1-3]|3[1-7]|4[1-6]|5[0-4]|6[1-5]|8[1-3])\d{4})(\d{4})(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])(\d{3}([0-9xX]))/;
      if (val && reg.test(val)) {
        //根据身份证计算性别
        let genderStr = parseInt(val.slice(16, 17)) % 2 == 1 ? '男' : '女',
          patientSex = (
            this.options.sexList.find(item => item.label == genderStr) || {}
          ).value;
        this.$set(this.form, 'patientSex', patientSex);
      }
    }
  },
  created() {
    this.getAppealType();
    this.getEventTypeList();
    this.getSourceList();
    this.getPatientRelationshipList();
  },
  methods: {
    async handleDataChange(val = {}) {
      let {
        eventType = '',
        source = '',
        appealType = '',
        appealDegree = '',
        occurrenceTime = '',
        complainTime = '',
        hospital = '',
        hospitalName = '',
        patientName = '',
        patientAge = '',
        patientSex = '',
        patientTel = '',
        patientRelationship = '',
        complainedOgrUser = '',
        complainedOrg = '',
        complainedContent = '',
        isMinority = '2',
        businessId = ''
      } = val;
      let isMinorityCheckBox = isMinority == '2' ? false : true;

      if (!hospital) {
        let hospitalMessage = await this.getUserHospital();
        hospital = hospitalMessage && hospitalMessage.organizationId;
        hospitalName = hospitalMessage && hospitalMessage.name;

        this.$set(
          this.options,
          'hospitalList',
          hospitalMessage ? [hospitalMessage] : []
        );
      }

      this.form = {
        ...val,
        eventType,
        source,
        appealType,
        appealDegree,
        occurrenceTime,
        complainTime,
        hospital,
        hospitalName,
        patientName,
        patientAge,
        patientSex,
        patientTel,
        patientRelationship,
        complainedOgrUser,
        complainedOrg,
        complainedContent,
        isMinority,
        isMinorityCheckBox,
        businessId
      };
      this.getDeptTreeData();
      this.getHospitalList();
      this.$nextTick(() => {
        this.$refs.baseForm.clearValidate();
      });
    },
    async getComplainedOgrUser(query) {
      if (query !== '') {
        this.loading = true;
        const complainedOgrUserResult = await this.ajax.getEmployeeList({
          ...page,
          empName: query
        });
        this.options = [...this.options, ...complainedOgrUserResult.row];
        this.loading = false;
      } else {
        this.options = [];
      }
    },
    async validate(cb) {
      let res = await this.$refs.baseForm.validate().catch(res => res);
      if (cb) {
        cb(res);
      } else {
        return res;
      }
    },
    /**@desc 获取被投诉人信息 */
    async getPeopleListData(data) {
      let res = await this.ajax.getEmployeeByPage({
        ...data,
        orgId: this.form.complainedOrg,
        pageSize: 15
      });

      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    getDeptTreeData() {
      this.ajax.getDeptTree().then(res => {
        if (!res.success) {
          this.$message.error(res.message || '科室数据获取失败');
          return;
        }
        this.$set(this.options, 'deptTreeData', res.object);
      });
    },
    /**@desc 获取当前登录人医院信息 */
    async getUserHospital() {
      let res = await this.ajax.getLoginUserHospitalMessage();
      if (!res.success) {
        this.$message.error(res.message || '当前登录人医院信息获取失败');
        return;
      }

      return res.object;
    },
    /**@desc 获取医院列表 */
    async getHospitalList() {
      let res = await this.ajax.getHospitalList();
      if (!res.success) {
        this.$message.error(res.message || '医院数据获取失败');
        return '';
      }

      this.$set(this.options, 'hospitalList', res.object || []);
    },
    handleHospitalChange(value) {
      let checked =
        this.options.hospitalList.find(item => item.organizationId == value) ||
        {};
      this.form.hospitalName = checked.name;
    },
    /**@desc 校验输入两位小数 */
    validateTowDecimalPlaces(value, obj, attr) {
      let newVal = commonUtils.inputTowDecimalPlaces(value);
      this.$set(this[obj], attr, newVal);
    },
    /**@desc 规范两位小数输入框内容 */
    handleTowDecimalPlacesBlur(obj, attr) {
      let val = (this[obj] && this[obj][attr]) || '';
      if (val) {
        this[obj][attr] = String(parseFloat(val));
      }
    },
    /**@desc 校验输入整数 */
    validateInputIntNum(value, obj, attr) {
      let newVal = value.match(/\d+/);
      newVal = newVal && newVal[0];
      this.$set(this[obj], attr, newVal);
    },
    /**@desc 展开树到指定层数 */
    handleExpand(treeObj) {
      if (this.$refs.deptSelect.searchVal != this.form.complainedOrgName) {
        treeObj.expandAll(true);
      } else {
        let nodes = treeObj.getNodes();
        nodes &&
          nodes.map(node => {
            treeObj.expandNode(node, true);
          });
      }
    },
    /**@desc 被投诉人自动带上所属科室 */
    handleComplainUserSelect(selectItem) {
      let { orgId, orgName } = selectItem || {};
      this.$set(this.form, 'complainedOrg', orgId);
      this.$set(this.form, 'complainedOrgName', orgName);
      this.$nextTick(() => {
        this.$refs.deptSelect.$forceUpdate();
      });
    },
    handleSelectComplainedOrg() {
      delete this.form.complainedOgrUser;
      delete this.form.complainedOgrUserName;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-content {
  max-height: calc(100vh - 143px);
  & .content-title {
    font-size: 16px;
    font-weight: bold;
    color: #333333;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      height: 18px;
      width: 4px;
      border-radius: 6px;
      background-color: $primary-blue;
      margin-right: 8px;
    }
  }
}
.complained-table {
  margin: $primary-spacing 0;
}
/deep/ {
  .ant-calendar-picker.ts-date-picker {
    min-width: unset !important;
    .ant-calendar-picker-input {
      padding-right: 31px;
    }
  }
  .ant-calendar-today-btn {
    line-height: inherit;
  }
}
</style>
