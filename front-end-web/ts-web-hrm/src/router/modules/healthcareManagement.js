/**@desc 路由组件混入获取菜单权限 */
function routerResolve(resolve) {
  let _this = this;
  return function(module) {
    let component = module.default;
    !component.mixins && (component.mixins = []);
    component.mixins.push({
      data() {
        return {
          menuLimits: [] // 路由按钮权限按钮
        };
      },
      mounted() {
        this.getMenuLimitList();
      },
      methods: {
        async getMenuLimitList() {
          this.menuLimits = await this.$getMenuSource();
        }
      }
    });
    return resolve.call(_this, module);
  };
}
export default [
  // 日常行政事务
  {
    path: '/healthcare/administrative/medical-special-projects',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/medical-special-projects/index.vue`
      ], resolve),
    styleName: '',
    name: '医疗专项项目'
  },
  {
    path: '/healthcare/administrative/person-support-allocation-manage',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/person-support-allocation-manage/index.vue`
      ], resolve),
    styleName: '',
    name: '人力支援调配管理'
  },
  {
    path: '/healthcare/handover/handover-ledger',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-ledger/index.vue`
      ], resolve),
    styleName: '',
    name: '交接发起事项'
  },
  {
    path: '/healthcare/handover/handover-matters',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-matters/index.vue`
      ], resolve),
    styleName: '',
    name: '交接接班事项'
  },
  {
    path: '/healthcare/handover/handover-initiate',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-initiate/index.vue`
      ], resolve),
    styleName: '',
    name: '交班'
  },
  {
    path: '/healthcare/handover/handover-receive',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-receive/index.vue`
      ], resolve),
    styleName: '',
    name: '接班'
  },
  {
    path: '/healthcare/handover/handover-ledgers',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-ledgers/index.vue`
      ], resolve),
    styleName: '',
    name: '台账'
  },
  {
    path: '/healthcare/administrative/medical-major-event',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/medical-major-event/index.vue`
      ], resolve),
    styleName: '',
    name: '医务大事记'
  },
  {
    path: '/healthcare/administrative/department-honor-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/department-honor-management/index.vue`
      ], resolve),
    styleName: '',
    name: '科室荣誉管理'
  },
  {
    path: '/healthcare/administrative/work-plan-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/work-plan-management/index.vue`
      ], resolve),
    styleName: '',
    name: '工作计划管理'
  },
  {
    path: '/healthcare/administrative/points-reward-and-punishment-level',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-affairs/points-reward-and-punishment-level/index.vue`
      ], resolve),
    styleName: '',
    name: '积分奖惩登记'
  },
  // 医师档案管理
  // 医务排班管理
  {
    path: '/healthcare/workforce/workforce-setting',
    component: resolve =>
      require([
        `@/views/healthcare-management/workforce-management/workforce-setting/index.vue`
      ], resolve),
    styleName: '',
    name: '医师排班设置'
  },
  {
    path: '/healthcare/workforce/workforce-overivew',
    component: resolve =>
      require([
        `@/views/healthcare-management/workforce-management/workforce-overivew/index.vue`
      ], resolve),
    styleName: '',
    name: '医师排班总览'
  },
  {
    path: '/healthcare/workforce/workforce-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/workforce-management/workforce-management/index.vue`
      ], resolve),
    styleName: '',
    name: '医师排班管理'
  },
  {
    path: '/healthcare/workforce/workforce-statistics',
    component: resolve =>
      require([
        `@/views/healthcare-management/workforce-management/workforce-statistics/index.vue`
      ], resolve),
    styleName: '',
    name: '排班统计'
  },
  {
    path: '/healthcare/handover/handover-matters',
    component: resolve =>
      require([
        `@/views/healthcare-management/handover-management/handover-matters/index.vue`
      ], resolve),
    styleName: '',
    name: '医师交接班事项'
  },
  // 科室患者管理
  {
    path: '/healthcare/patient/patient-information',
    component: resolve =>
      require([
        `@/views/healthcare-management/patient-management/patient-information/index.vue`
      ], resolve),
    styleName: '',
    name: '患者基本信息'
  },
  {
    path: '/healthcare/patient/patient-information-sry',
    component: resolve =>
      require([
        `@/views/healthcare-management/patient-management/patient-information-sry/index.vue`
      ], resolve),
    styleName: '',
    name: '患者基本信息'
  },
  // 医师资质授权
  {
    path: '/healthcare/qualification/overview-authorized',
    component: resolve =>
      require([
        `@/views/healthcare-management/qualification-authorization/overview-authorized/index.vue`
      ], resolve),
    styleName: '',
    name: '授权总览'
  },
  {
    path: '/healthcare/qualification/all-overview-authorized',
    component: resolve =>
      require([
        `@/views/healthcare-management/all-qualification-authorization/index.vue`
      ], resolve),
    styleName: '',
    name: '授权总览'
  },
  {
    path: '/healthcare/qualification/authorized-approval',
    component: resolve =>
      require([
        `@/views/healthcare-management/qualification-authorization/authorized-approval-optimize/index.vue`
      ], resolve),
    styleName: '',
    name: '授权审批'
  },
  {
    path: '/healthcare/qualification/authorized-board',
    component: resolve =>
      require([
        `@/views/healthcare-management/qualification-authorization/bulletin-board/index.vue`
      ], resolve),
    styleName: '',
    name: '授权看板'
  },
  {
    path: '/healthcare/qualification/authorized-project',
    component: resolve =>
      require([
        `@/views/healthcare-management/qualification-authorization/authorized-project/index.vue`
      ], resolve),
    styleName: '',
    name: '授权项目配置'
  },
  {
    path: '/healthcare/qualification/highrisk-authorized-project',
    component: resolve =>
      require([
        `@/views/healthcare-management/qualification-authorization/highrisk-authorized-project/index.vue`
      ], resolve),
    styleName: '',
    name: '高风险项目配置'
  },
  // 医师轮科管理
  {
    path: '/healthcare/physician/rotation-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/physician-management/physician-rotation-management/index.vue`
      ], resolve),
    styleName: '',
    name: '医师轮科管理'
  },
  // 放射诊疗管理
  {
    path: '/healthcare/radiodiagnosis/personnel-registration',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiodiagnosis-management/personnel-registration/index.vue`
      ], resolve),
    styleName: '',
    name: '放射人员登记'
  },
  {
    path: '/healthcare/radiodiagnosis/certificate-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiodiagnosis-management/certificate-management/index.vue`
      ], resolve),
    styleName: '',
    name: '资质证件管理'
  },
  {
    path: '/healthcare/radiodiagnosis/training-registration',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiodiagnosis-management/training-registration/index.vue`
      ], resolve),
    styleName: '',
    name: '培训状况登记'
  },
  {
    path: '/healthcare/radiodiagnosis/dose-registration',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiodiagnosis-management/dose-registration/index.vue`
      ], resolve),
    styleName: '',
    name: '剂量监测登记'
  },
  {
    path: '/healthcare/radiodiagnosis/physical-registration',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiodiagnosis-management/physical-registration/index.vue`
      ], resolve),
    styleName: '',
    name: '体检状况登记'
  },
  // 院内会诊管理
  {
    path: '/healthcare/consultation/consultation-arrangement',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-arrangement-optimize/index.vue`
      ], routerResolve(resolve)),
    styleName: '',
    name: '会诊排班'
  },
  {
    path: '/healthcare/consultation/consultation-scheduling',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-scheduling/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊安排'
  },
  {
    path: '/healthcare/consultation/consultation-overview',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-overview/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊一览'
  },
  {
    path: '/healthcare/consultation/consultation-board',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-board/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊看板'
  },
  // 标准版本 全院大会诊
  {
    path: '/healthcare/consultation/consultation-authorization',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-authorization/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊资质'
  },
  {
    path: '/healthcare/consultation/consultation-special-drug',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-special-drug/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊特殊药物'
  },
  {
    path: '/healthcare/consultation/consultation-application',
    component: resolve =>
      require([
        `@/views/healthcare-management/consultation-management/consultation-application/index.vue`
      ], resolve),
    styleName: '',
    name: '会诊申请单'
  },
  // 质控办
  // 质控专员管理
  {
    path: '/healthcare/consultation/control-specialist',
    component: resolve =>
      require([
        `@/views/healthcare-management/quality-control-office/quality-control-specialist-management/index.vue`
      ], resolve),
    styleName: '',
    name: '质控专员管理'
  },
  {
    path: '/healthcare/consultation/new-control-specialist',
    component: resolve =>
      require([
        `@/views/healthcare-management/quality-control-office/new-quality-control-management/index.vue`
      ], resolve),
    styleName: '',
    name: '质控专员管理(新)'
  },
  // 行政查房管理
  {
    path: '/healthcare/rounds/qualificationForRounds',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/qualification-for-rounds/index.vue`
      ], resolve),
    styleName: '',
    name: '查房资质管理'
  },
  {
    path: '/healthcare/rounds/workforceManagement',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/workforce-management/index.vue`
      ], resolve),
    styleName: '',
    name: '查房排班管理'
  },
  {
    path: '/healthcare/rounds/taskManagement',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/task-management/index.vue`
      ], resolve),
    styleName: '',
    name: '查房任务管理'
  },
  {
    path: '/healthcare/rounds/scoreOverivew',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/score-overview/index.vue`
      ], resolve),
    styleName: '',
    name: '查房得分一览'
  },
  {
    path: '/healthcare/rounds/qualityReport',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/quality-report/index.vue`
      ], resolve),
    styleName: '',
    name: '查房质量报告'
  },
  {
    path: '/healthcare/rounds/board',
    component: resolve =>
      require([
        `@/views/healthcare-management/administrative-rounds/board/index.vue`
      ], resolve),
    styleName: '',
    name: '查房看板'
  },
  // 全院危机值管理
  // 医德医风管理
  // 继续教育管理
  // 预警上报管理
  // 培训考试管理
  // 科研成果管理
  // 药品管理看板
  {
    path: '/healthcare/drug-dashboard',
    component: resolve =>
      require([
        `@/views/healthcare-management/drug-dashboard/index.vue`
      ], resolve),
    styleName: '',
    name: '药品管理'
  },
  {
    path: '/healthcare/consumables-dashboard',
    component: resolve =>
      require([
        `@/views/healthcare-management/consumables-dashboard/index.vue`
      ], resolve),
    styleName: '',
    name: '耗材管理'
  },
  // 术前讨论
  {
    path: '/healthcare/preoperative-discussion',
    component: resolve =>
      require([
        `@/views/healthcare-management/medical-risk-assessment/preoperative-discussion/index.vue`
      ], resolve),
    styleName: '',
    name: '四级手术术前多学科讨论'
  },
  // 三级医师授权管理
  {
    path: '/healthcare/physician-authorization',
    component: resolve =>
      require([
        `@/views/healthcare-management/physician-authorization/index.vue`
      ], resolve),
    styleName: '',
    name: '三级医师授权'
  },
  // 医生360
  {
    path: '/healthcare/doctor-training',
    component: resolve =>
      require([
        `@/views/healthcare-management/doctor-training/index.vue`
      ], resolve),
    styleName: '',
    name: '医师360'
  },
  {
    path: '/healthcare/doctor-training-normal',
    component: resolve =>
      require([
        `@/views/healthcare-management/doctor-training-normal/index.vue`
      ], resolve),
    styleName: '',
    name: '医师360'
  },
  // 医务工作台
  {
    path: '/healthcare/workbench',
    component: resolve =>
      require([`@/views/healthcare-management/workbench/index.vue`], resolve),
    styleName: '',
    name: '医务工作台(新)'
  },
  // 行风看板
  {
    path: '/healthcare/code-of-conduct-board',
    component: resolve =>
      require([
        `@/views/healthcare-management/code-of-conduct-board/index.vue`
      ], resolve),
    styleName: '',
    name: '行风准则看板'
  },
  // 外送检验管理
  {
    path: '/healthcare/inspection-setting',
    component: resolve =>
      require([
        `@/views/healthcare-management/delivery-inspection-management/delivery-inspection-setting/index.vue`
      ], resolve),
    styleName: '',
    name: '外送检验项目配置'
  },
  {
    path: '/healthcare/inspection-table',
    component: resolve =>
      require([
        `@/views/healthcare-management/delivery-inspection-management/delivery-inspection-table/index.vue`
      ], resolve),
    styleName: '',
    name: '外送检验项目报表'
  },
  // 危急值管理
  {
    path: '/healthcare/critical-value-handling',
    component: resolve =>
      require([
        `@/views/healthcare-management/critical-value-management/critical-value-handling/index.vue`
      ], resolve),
    styleName: '',
    name: '危急值处理'
  },
  {
    path: '/healthcare/critical-value-query',
    component: resolve =>
      require([
        `@/views/healthcare-management/critical-value-management/critical-value-query/index.vue`
      ], resolve),
    styleName: '',
    name: '危急值查询'
  },
  {
    path: '/healthcare/critical-value-analysis',
    component: resolve =>
      require([
        `@/views/healthcare-management/critical-value-management/critical-value-analysis/index.vue`
      ], resolve),
    styleName: '',
    name: '危急值分析'
  },
  {
    path: '/healthcare/radiation-personnel',
    component: resolve =>
      require([
        `@/views/healthcare-management/radiation-personnel/index.vue`
      ], resolve),
    styleName: '',
    name: '放射诊疗'
  },
  // 新技术新项目
  {
    path: '/healthcare/new-technology-management',
    component: resolve =>
      require([
        `@/views/healthcare-management/new-technology-management/index.vue`
      ], resolve),
    styleName: '',
    name: '新技术新项目管理'
  }
];
