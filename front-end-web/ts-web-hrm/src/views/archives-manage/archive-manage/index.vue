<template>
  <div class="container">
    <div class="left">
      <new-base-search-tree
        class="node-tree"
        ref="searchTree"
        :apiFunction="ajax.organizationZTree3List"
        placeholder="输入组织机构进行搜索"
        title="科室分类"
        :params="deptParams"
        showNodeNum
        numKey="pNumber"
        showCheckbox
        :chkboxType="{
          Y: 's',
          N: 's'
        }"
        @nodeCheck="clickItemTree"
      />
    </div>

    <div class="right">
      <ts-search-bar
        v-model="searchForm"
        :actions="filterBtn"
        :formList="searchList"
        :elementCol="14"
        :showLength="3"
        :resetData="resetData"
        @search="search"
        @reset="reset"
      >
        <template v-slot:searchKey>
          <ts-input
            placeholder="搜索工号/姓名/手机号码"
            v-model="searchForm.searchKey"
            clearable
            @keyup.enter.native="search"
          />
        </template>
      </ts-search-bar>
      <!--去除table Id,否则与自定义表头排序冲突，（id会产生table缓存）-->
      <TsVxeTemplateTable
        v-loading="exportLoading"
        ref="table"
        :defaultSort="defaultSort"
        :defaultPageSize="100"
        :columns="columns"
        :scroll-y="{ enabled: true }"
        @refresh="handleRefreshTable"
        @selection-change="handleSelectChange"
      />

      <dialog-add-archives
        ref="DialogAddArchives"
        @refresh="handleRefreshTableAndTree"
      />

      <dialog-employee-setting
        ref="DialogEmployeeSetting"
        @refresh="handleRefreshTable"
      />

      <dialog-reset-password
        ref="DialogResetPassword"
        @refresh="handleRefreshTable"
      />

      <dialog-update-no ref="DialogUpdateNo" @refresh="handleRefreshTable" />

      <drawer-customize-header
        ref="DrawerCustomizeHeader"
        @refreshAll="handleRefreshTableAndHead"
      />

      <drawer-customize-search
        ref="DrawerCustomizeSearch"
        @refreshSearchTable="handleRefreshSearchTable"
      />

      <dialog-archive-manage-import
        ref="DialogArchiveManageImport"
        @refresh="handleRefreshTableAndTree"
      />

      <dialog-emp-collection ref="DialogEmpCollection" />

      <drawer-customize-export
        ref="DrawerCustomizeExport"
        @export="() => this.handleExport('customize')"
      />

      <dialog-my-archives
        ref="DialogMyArchives"
        @refresh="handleRefreshTable"
      />

      <dialog-employee-job-description
        ref="DialogEmployeeJobDescription"
        @refresh="handleRefreshTable"
      />
      <dialog-set-sort ref="DialogSetSort" @refresh="handleRefreshTable" />
    </div>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash-es';
import { customizeSearchItem } from '@/views/archives-manage/config/dictionary.js';

import tableMixins from './mixins/tableMixins.js';
import DialogAddArchives from './components/dialog-add-archives.vue';
import DialogEmployeeSetting from './components/dialog-employee-setting.vue';
import DialogResetPassword from './components/dialog-reset-password.vue';
import DialogUpdateNo from './components/dialog-update-no.vue';
import DrawerCustomizeHeader from './components/drawer-customize-header.vue';
import DrawerCustomizeSearch from './components/drawer-customize-search.vue';
import DialogArchiveManageImport from './components/dialog-archive-manage-import.vue';
import DrawerCustomizeExport from './components/drawer-customize-export.vue';
import DialogEmpCollection from './components/dialog-emp-collection.vue';
import DialogEmployeeJobDescription from './components/dialog-employee-job-description.vue';
import DialogMyArchives from '@/views/archives-manage/archive-manage/components/dialog-my-archives.vue';
import DialogSetSort from './components/dialog-set-sort.vue';
import { commonUtils } from '@/util/common';
export default {
  mixins: [tableMixins],
  components: {
    DialogAddArchives,
    DialogEmployeeSetting,
    DialogResetPassword,
    DialogUpdateNo,
    DrawerCustomizeHeader,
    DrawerCustomizeSearch,
    DialogEmpCollection,
    DialogEmployeeJobDescription,
    DialogArchiveManageImport,
    DrawerCustomizeExport,
    DialogMyArchives,
    DialogSetSort
  },

  data() {
    return {
      active: '1',
      orgIds: '',
      noQueryMap: ['searchKey', 'employeeStatuses', 'gender'],
      exportLoading: false,
      initData: {},
      actions: [],
      selectList: []
    };
  },
  async created() {
    //菜单参数获取
    let type = this.$route.params.type;
    if (type) {
      this.archivesType = type;
      // this.actions = cloneDeep(this.localActions);
      // this.actions.splice(0, 1);
    } else {
      this.archivesType = null;
      // this.actions = cloneDeep(this.localActions);
    }
    //获取查询跳转参数
    let {
      gender,
      employeeStatuses,
      education_type,
      establishment_type,
      personal_identity,
      jobtitleCategoryLevel,
      emp_age_min,
      emp_age_max,
      year_work_min,
      year_work_max
    } = this.$route.query;
    if (employeeStatuses) {
      this.indexSearchStatus = employeeStatuses.split(',');
    }
    // 初始化页面查询条件
    const hospitalCode = this.$store.state.common.hospitalCode;
    this.initData = {
      employeeStatuses:
        hospitalCode === 'pjxdyrmyy'
          ? ['1', '99', '9']
          : this.indexSearchStatus && this.indexSearchStatus.length > 0
          ? this.indexSearchStatus
          : this.isNormalInstance
          ? ['1', '6', '12', '99', '9']
          : ['89']
    };
    this.resetData = {
      ...this.initData,
      gender: gender ? gender : '',
      education_type: education_type ? [education_type.replace(',', '')] : [],
      establishment_type: establishment_type ? [establishment_type] : [],
      personal_identity: personal_identity
        ? [personal_identity.replace(',', '')]
        : [],
      jobtitleCategoryLevel: jobtitleCategoryLevel
        ? [jobtitleCategoryLevel.replace(',', '')]
        : [],
      emp_age: {
        start: emp_age_min,
        end: emp_age_max
      },
      year_work: {
        start: year_work_min,
        end: year_work_max
      }
    };
    // if (Object.keys(this.$route.query).length == 0) {
    //   this.refresh();
    // }
  },
  watch: {
    $route: {
      handler: function(to, from) {
        if (to.path.indexOf('archives-manage/archive-manage') > -1) {
          //菜单参数获取
          let type = this.$route.params.type;
          if (type) {
            this.archivesType = type;
            // this.actions = cloneDeep(this.localActions);
            // this.actions.splice(0, 1);
          } else {
            this.archivesType = null;
            // this.actions = cloneDeep(this.localActions);
          }
          //获取查询跳转参数
          this.searchForm = cloneDeep(this.initData);
          this.resetData = cloneDeep(this.initData);
          let {
            gender,
            employeeStatuses,
            education_type,
            establishment_type,
            personal_identity,
            jobtitleCategoryLevel,
            emp_age_min,
            emp_age_max,
            year_work_min,
            year_work_max
          } = this.$route.query;
          if (gender) {
            this.$set(this.resetData, 'gender', gender);
            this.$set(this.searchForm, 'gender', gender);
          }
          if (employeeStatuses) {
            this.indexSearchStatus = employeeStatuses.split(',');
            //员工状态
            this.$set(
              this.resetData,
              'employeeStatuses',
              this.indexSearchStatus
            );
            this.$set(
              this.searchForm,
              'employeeStatuses',
              this.indexSearchStatus
            );
          }
          if (education_type) {
            //学历
            this.$set(this.resetData, 'education_type', [
              education_type.replace(',', '')
            ]);
            this.$set(this.searchForm, 'education_type', [
              education_type.replace(',', '')
            ]);
          }
          if (establishment_type) {
            //编制类型
            this.$set(this.resetData, 'establishment_type', [
              establishment_type
            ]);
            this.$set(this.searchForm, 'establishment_type', [
              establishment_type
            ]);
          }
          if (personal_identity) {
            //岗位
            this.$set(this.resetData, 'personal_identity', [
              personal_identity.replace(',', '')
            ]);
            this.$set(this.searchForm, 'personal_identity', [
              personal_identity
            ]);
          }
          //职称
          if (jobtitleCategoryLevel) {
            this.$set(this.resetData, 'jobtitleCategoryLevel', [
              jobtitleCategoryLevel.replace(',', '')
            ]);
            this.$set(this.searchForm, 'jobtitleCategoryLevel', [
              jobtitleCategoryLevel.replace(',', '')
            ]);
          }

          //员工年龄
          this.$set(this.resetData, 'emp_age', {
            start: emp_age_min,
            end: emp_age_max
          });
          this.$set(this.searchForm, 'emp_age', {
            start: emp_age_min,
            end: emp_age_max
          });
          //员工工龄
          this.$set(this.resetData, 'year_work', {
            start: year_work_min,
            end: year_work_max
          });
          this.$set(this.searchForm, 'year_work', {
            start: year_work_min,
            end: year_work_max
          });
          // 每次路由变化时会调用这个方法
          this.$nextTick(() => {
            this.refresh();
          });
        }
      }
    }
  },
  computed: {
    defaultSort() {
      let sortDatas = this.$store.state.common?.personalSortData ?? {},
        { sidx = 'create_date', sord = 'desc' } = sortDatas;
      // 加上前缀
      switch (sidx) {
        case 'seq_no':
          sidx = 'o.' + sidx;
          break;
        default:
          sidx = 's.' + sidx;
          break;
      }
      return { sord, sidx };
    },
    isNormalInstance() {
      return this.$route.path !== '/archives-manage/archive-manage-params';
    },
    deptParams() {
      let params = {};
      if (!this.isNormalInstance) {
        params.employeeStatus = '89';
      }
      params.archivesType = this.archivesType;
      return params;
    }
  },
  mounted() {
    window.addEventListener('popstate', this.goBack);
  },
  destroyed() {
    window.removeEventListener('popstate', this.goBack);
  },
  methods: {
    goBack() {
      this.$refs.DialogMyArchives?.close();
    },
    clickItemTree(node) {
      this.orgIds = node.map(e => e.id).join(',');
      this.search();
    },
    // 省人医院特殊处理 --start
    handleSelectChange(e) {
      this.selectList = e;
    },
    // --end
    async refresh() {
      this.searchForm = cloneDeep(this.resetData);
      const [
        _,
        employeeStatusList,
        sexList,
        settingSearchList
      ] = await Promise.all([
        this.handleGetTableHead(),
        this.handleGetDictItemList('employee_status'),
        this.handleGetDictItemList('SEX'),
        this.handleGetSettingSearchList()
      ]);

      const setChildNodeList = (key, list) => {
        const findItem = this.localSearchList.find(f => f.value === key);
        if (findItem) findItem.childNodeList = list;
      };
      setChildNodeList('employeeStatuses', employeeStatusList);
      setChildNodeList('gender', sexList);

      this.searchList = [...this.localSearchList, ...settingSearchList];
      this.$nextTick(this.handleRefreshTable);
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    reset() {
      this.orgIds = '';
      this.$refs.searchTree.treeClass.checkAllNodes(false);

      this.search();
    },

    handleChangeDept() {
      this.$nextTick(() => {
        this.handleRefreshTable();
      });
    },

    async handleGetDictItemList(type) {
      try {
        let res = await this.ajax.getDataByDataLibrary(type);
        if (!res.success) {
          this.$message.error(res.message || '字典数据获取失败');
          return;
        }
        return (res.object || []).map(m => ({
          label: m.itemName,
          value: m.itemNameValue,
          element: 'ts-option'
        }));
      } catch (error) {}
    },

    handleAddArchives() {
      this.$refs.DialogAddArchives.open({
        title: '员工档案新增',
        type: 'add',
        renderType: 'ADMIN',
        employeeId: '',
        archivesType: this.archivesType,
        isNormalInstance: this.isNormalInstance,
        data: {}
      });
    },

    async handleEditArchives() {
      let selectField = this.$refs.table.handleGetCurrentRecord();
      if (!selectField) {
        this.$message.warning('请选择一条记录进行操作！');
        return false;
      }

      let employeeId = selectField.employee_id;
      let res = await this.ajax.customEmployeeBaseDetails(employeeId, {
        archivesType: this.archivesType,
        groupId: 'all'
      });
      if (!res.success) {
        this.$message.error(res.message || '获取档案管理信息数据失败!');
        return;
      }
      this.$refs.DialogAddArchives.open({
        title: '员工档案编辑',
        type: 'edit',
        renderType: 'ADMIN',
        employeeId,
        archivesType: this.archivesType,
        isNormalInstance: this.isNormalInstance,
        data: res.object
      });
    },

    async handleDeleteArchives() {
      let selectField = this.$refs.table.handleGetCurrentRecord();
      if (!selectField) {
        this.$message.warning('请选择一条记录进行操作！');
        return false;
      }
      try {
        await this.$confirm('确定要删除该条数据吗？', '提示', {
          type: 'warning'
        });
        let employeeId = selectField.employee_id;
        let res = await this.ajax.customEmployeeBaseDelete(employeeId);
        if (!res.success) {
          this.$message.error(res.message || '操作失败,请联系管理员!');
          return;
        }
        this.handleRefreshTableAndTree();
      } catch (error) {}
    },

    handleEmployeeSetting(row) {
      this.$refs.DialogEmployeeSetting.open({
        row
      });
    },

    handleResetPassword({ employee_no: empCode }) {
      this.$refs.DialogResetPassword.open({
        empCode
      });
    },

    handleUpdateNo({ employee_id: employeeId, employee_no: empCode }) {
      this.$refs.DialogUpdateNo.open({
        employeeId,
        empCode
      });
    },

    handleEnableAccount(row) {
      let data = {
        id: row.employee_id,
        status: '1'
      };
      this.handleDisableAndEnable(data, row);
    },

    handleDisableAccount(row) {
      let data = {
        id: row.employee_id,
        status: '0'
      };
      this.handleDisableAndEnable(data, row);
    },

    async handleDisableAndEnable(data, row) {
      try {
        const res = await this.ajax.customEmployeeBaseDisable(data);
        if (!res.success) {
          this.$message.error(res.message || '操作失败,请联系管理员!');
          return;
        }

        this.$message.success('操作成功!');
        row.is_enable = data.status;
        this.$forceUpdate();
      } catch (error) {
        console.error(error, 'error');
      }
    },

    async handleUnlockAwccount({ employee_id: id }) {
      try {
        const res = await this.ajax.customEmployeeBaseUnlock({ id });
        if (!res.success) {
          this.$message.error(res.message || '操作失败,请联系管理员!');
          return;
        }
        this.$message.success(res.message || '解锁成功!');
      } catch (error) {
        console.error(error, 'error');
      }
    },

    handleSetSort({ employee_id: id, emp_sort: empSort }) {
      this.$refs.DialogSetSort.open({
        id,
        empSort
      });
    },

    handleImport() {
      this.$refs.DialogArchiveManageImport.open({
        isNormalInstance: this.isNormalInstance
      });
    },

    handleCustomizeHeader() {
      this.$refs.DrawerCustomizeHeader.open();
    },

    handleCustomizeSearch() {
      this.$refs.DrawerCustomizeSearch.open();
    },

    handleCustomizeExport() {
      this.$refs.DrawerCustomizeExport.open({
        archivesType: this.archivesType
      });
    },

    handleEmpCollection({ employee_name, employee_id: employeeId }) {
      this.$refs.DialogEmpCollection.open({
        title: `${employee_name}-个人信息采集`,
        employeeId
      });
    },

    handleEmpJobDescription(data) {
      this.$refs.DialogEmployeeJobDescription.open({
        title: `${data?.employee_name}-岗位说明书`,
        data
      });
    },

    handleShowPersonArchives({ employee_id: employeeId }) {
      employeeId &&
        this.$refs.DialogMyArchives.open({
          type: 'details',
          title: '人才画像',
          archivesType: 'all',
          employeeId
        });
    },

    async handleRefreshTableAndHead() {
      await this.handleGetTableHead();
      await this.handleGetTableData();
    },

    async handleRefreshTable() {
      await this.handleGetTableData();
    },

    async handleRefreshTableAndTree() {
      let lastCheckIds = this.$refs.searchTree.$refs.tsTree
        .getCheckedNodes()
        .map(e => e.id);
      await this.$refs.searchTree.getTreeData();

      this.$nextTick(() => {
        this.$refs.searchTree.input();
        this.$refs.searchTree.initNodeNum();
        setTimeout(() => {
          this.$refs.searchTree.$refs.tsTree.setCheckedKeys(lastCheckIds);
        });
      });

      await this.handleGetTableData();
      this.$forceUpdate();
    },

    async handleRefreshSearchTable() {
      this.searchForm = cloneDeep(this.resetData);
      let settingSearchList = await this.handleGetSettingSearchList();
      this.searchList = [...this.localSearchList, ...settingSearchList];
      this.handleRefreshTable();
    },

    async handleGetTableHead() {
      let columns = [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 70,
          fixed: 'left'
        }
      ];
      // let checkOrgCode = commonUtils.orgCodeHidden(this, 'hnsrmyy');
      // if (checkOrgCode) {
      //   this.columns.unshift({
      //     type: 'checkbox',
      //     width: 40,
      //     align: 'center',
      //     fixed: 'left'
      //   });
      // }
      let res = await this.ajax.customEmployeeFieldGetHeadField();
      if (res.success == false) {
        this.$message.error(res.message || '表头数据获取失败');
        return;
      }
      if (res.object.length) {
        res.object.forEach(f => {
          if (f.prop === 'employee_name') {
            f.render = (h, { row }) => {
              let className = row.is_enable == 0 ? 'red' : 'primary-span';
              return h(
                'div',
                {
                  class: className,
                  on: { click: () => this.handleShowPersonArchives(row) }
                },
                row.employee_name
              );
            };
          }
        });

        columns.push({
          label: '操作',
          align: 'center',
          width: 60,
          fixed: 'left',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '员工设置',
                icon: 'vxe-icon-setting-fill',
                event: this.handleEmployeeSetting
              },
              {
                icon: 'vxe-icon-file-markdown',
                label: '重置密码',
                event: this.handleResetPassword
              },
              {
                icon: 'vxe-icon-edit',
                label: '更新工号',
                event: this.handleUpdateNo
              },
              {
                icon: 'vxe-icon-success-circle-fill',
                label: '启用账号',
                event: this.handleEnableAccount
              },
              {
                icon: 'vxe-icon-warning-circle-fill',
                label: '停用账号',
                event: this.handleDisableAccount
              },
              {
                icon: 'vxe-icon-unlock-fill',
                label: '解锁账号',
                event: this.handleUnlockAwccount
              },
              {
                icon: 'vxe-icon-sort',
                label: '设置排序',
                event: this.handleSetSort
              }
            ];

            let globalSetting = this.$getParentStoreInfo('globalSetting');
            if (globalSetting?.orgCode === 'lyszyyy') {
              actionList.unshift({
                label: '信息采集',
                icon: 'vxe-icon-association-form',
                event: this.handleEmpCollection
              });
              actionList.unshift({
                label: '岗位职责',
                icon: 'vxe-icon-paste',
                event: this.handleEmpJobDescription
              });
            }

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        });
        columns.push(...res.object);
        this.columns = columns;
      }
    },

    async handleGetTableData() {
      let pageNo = this.$refs.table.pageNo;
      let pageSize = this.$refs.table.pageSize;
      let sidx = this.$refs.table.sidx;
      let sord = this.$refs.table.sord;
      let find = this.columns.find(f => f.prop === sidx);
      if (find) sidx = find.index;

      let submitSearchFormData = this.handleGetSearchFromData();
      if (typeof submitSearchFormData === 'string') {
        this.$newMessage('warning', submitSearchFormData);
        return;
      }
      let searchForm = {
        ...submitSearchFormData,
        orgIds: this.orgIds,
        pageNo,
        pageSize,
        sidx,
        sord
      };

      let res = await this.ajax.customEmployeeBaseList(searchForm);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    async handleGetSettingSearchList() {
      try {
        let res = await this.ajax.customEmployeeFieldHeadGetListByType({
          type: '2'
        });

        if (!res.success) {
          this.$message.error(res.message || '获取表头配置失败, 请联系管理员!');
          return;
        }
        let list = cloneDeep(res.object || []);
        let filterSearchList = list.filter(
          f => !customizeSearchItem.includes(f.fieldName) && f.setValue === 1
        );
        this.filterSearchList = filterSearchList;

        const concurrentRequests = filterSearchList.map(async field => {
          const defaultItem = { label: field.showName, value: field.fieldName };
          switch (field.fieldType) {
            case 'input':
              return {
                ...defaultItem,
                element: 'ts-input',
                elementProp: {
                  clearable: true,
                  placeholder: `请输入${field.showName}`
                }
              };
            case 'number':
              this.$set(this.resetData, [field.fieldName], {
                start: '',
                end: ''
              });
              return { ...defaultItem, element: 'ts-number-range' };
            case 'date':
              return {
                ...defaultItem,
                element: 'base-date-range-picker'
              };
            case 'select':
              const childNodeList = await this.getSelectOptions(field);
              return {
                ...defaultItem,
                element: 'ts-select',
                elementProp: {
                  clearable: true,
                  multiple: true,
                  placeholder: '请选择'
                },
                childNodeList
              };
            default:
              return {
                ...defaultItem,
                element: 'ts-input',
                elementProp: {
                  clearable: true,
                  placeholder: `请输入${field.showName}`
                }
              };
          }
        });

        return Promise.all(concurrentRequests);
      } catch (error) {
        this.$message.error('发生错误，请联系管理员!');
        console.error(error);
        return [];
      }
    },

    async getSelectOptions(field) {
      if (['concurrent_position', 'position_id'].includes(field.fieldName)) {
        const { rows = [] } = await this.ajax.archiveReportPositionList();
        return rows.map(m => ({
          label: m.positionName,
          value: m.positionId,
          element: 'ts-option'
        }));
      }

      if (field.dataSource == '1' && field.optionValue) {
        return field.optionValue
          .split('|')
          .map(option => option.split(':').map(item => item.trim()))
          .filter(([label, value]) => label && value)
          .map(([label, value]) => ({ label, value, element: 'ts-option' }));
      }

      if (field.dataSource == '4' && field.dictSource) {
        return await this.handleGetDictItemList(field.dictSource);
      }
      return [];
    },

    handleExport(type) {
      let api = {
        all: '/ts-basics-bottom/api/customEmployeeBase/export',
        customize: '/ts-basics-bottom/api/customEmployeeBase/customeExport'
      };
      let searchForm = this.handleGetSearchFromData();
      if (typeof submitSearchFormData === 'string') {
        this.$newMessage('warning', submitSearchFormData);
        return;
      }
      if (searchForm.queryMap) {
        searchForm.queryMapStr = JSON.stringify(searchForm.queryMap);
        delete searchForm.queryMap;
      }

      const iframe = document.createElement('iframe');
      iframe.style.display = 'none';
      iframe.id = 'down-file-iframe';

      const form = document.createElement('form');
      form.method = 'post';
      form.target = iframe.id;
      form.action = api[type];

      Object.keys(searchForm).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = searchForm[key];
        form.appendChild(input);
      });

      iframe.appendChild(form);
      document.body.appendChild(iframe);

      form.submit();
      document.body.removeChild(iframe);

      this.exportLoading = true;
      setTimeout(() => {
        this.exportLoading = false;
      }, 1500);
    },

    handleGetSearchFromData() {
      let searchForm = cloneDeep(this.searchForm);
      let dateCheck = false;
      let deteCheckString = '';
      this.filterSearchList.forEach(f => {
        const value = searchForm[f.fieldName];
        if (f.fieldType === 'date' && Array.isArray(value) && value.length) {
          const [start, end] = value;
          if (!dateCheck) {
            let num = 0;
            start && num++;
            end && num++;
            if (num == 1) {
              deteCheckString = f.showName;
              dateCheck = true;
            }
          }
          searchForm[`start_${f.fieldName}`] = start;
          searchForm[`end_${f.fieldName}`] = end;
          delete searchForm[f.fieldName];
        }

        if (f.fieldType === 'number') {
          if (value && Object.keys(value).length > 0) {
            const { start, end } = value;
            if (start) {
              searchForm[`numStart_${f.fieldName}`] = start;
            }
            if (end) {
              searchForm[`numEnd_${f.fieldName}`] = end;
            }
          }

          delete searchForm[f.fieldName];
        }
      });
      if (dateCheck) {
        return `请完整选择【${deteCheckString}】的开始时间和结束时间`;
      }
      for (const key in searchForm) {
        const value = searchForm[key];
        if (Array.isArray(value)) {
          if (value.length > 1) {
            searchForm[key] = value.join(',');
          } else if (value.length == 1) {
            searchForm[key] = value + ',';
          } else {
            delete searchForm[key];
          }
        }
        if (
          value === null ||
          value == undefined ||
          value == '' ||
          value == {} ||
          JSON.stringify(value) === '{}'
        ) {
          delete searchForm[key];
        }
      }
      let queryMap = {};
      Object.entries(searchForm).forEach(([key, value]) => {
        if (!this.noQueryMap.includes(key)) {
          queryMap[key] = value;
          delete searchForm[key];
        }
      });
      if (Object.keys(queryMap).length) {
        searchForm.queryMap = queryMap;
      }
      searchForm.orgIds = this.orgIds;
      searchForm.archivesType = this.archivesType;
      return searchForm;
    }
  }
};
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  background: #eee !important;
  padding: 0px !important;
  display: flex;
  .left {
    width: 248px;
    margin-right: 8px;
    padding: 8px 8px 0 8px;
    background: #fff !important;
    overflow: hidden;
    height: 100%;
    position: relative;
    .tabs-container {
      position: absolute;
      right: 8px;
      top: 10px;
      display: flex;
      .tabs-item {
        padding: 2px 4px;
        border: 1px solid $primary-blue;
        color: $primary-blue;
        cursor: pointer;
        &:first-child {
          margin-right: 4px;
        }
        &.active {
          background-color: $primary-blue;
          color: #fff;
        }
      }
    }
  }
  .right {
    ::v-deep {
      .more-text-btn {
        width: 140px;
        > span {
          max-width: 140px !important;
        }
      }

      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      padding: 8px 8px 0 8px;
      background: #fff !important;
      .form-table {
        background: #fff !important;

        .primary-span {
          color: $primary-blue;
          cursor: pointer;
        }
        .red {
          color: red !important;
          color: $primary-blue;
        }
      }
    }
  }
}

.export-select-field-popper {
  .popper-container {
    display: flex;
    flex-direction: column;
  }

  .column-container {
    margin-right: 8px;
    margin-bottom: 8px;
    display: flex;
    flex-wrap: wrap;
    li {
      width: 120px;
    }
  }

  .footer {
    border-top: 1px solid #eee;
    padding-top: 8px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
