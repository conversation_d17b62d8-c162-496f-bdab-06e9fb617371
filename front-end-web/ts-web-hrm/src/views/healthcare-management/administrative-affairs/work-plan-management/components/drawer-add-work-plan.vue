<template>
  <el-drawer
    custom-class="ts-custom-default-drawer drawer-add-work-plan"
    :visible.sync="visible"
    :append-to-body="true"
    :title="title"
    @close="close"
    size="70%"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>

    <div class="content-container">
      <el-scrollbar
        style="width: 100%;height: calc(100% - 50px);"
        wrap-style="overflow-x: hidden;"
      >
        <div class="form-container">
          <ts-form ref="form" :model="form">
            <div class="form-card-box">
              <colmun-head title="计划信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item
                    label="责任人工号"
                    prop="workplanUser"
                    :rules="rules.required"
                  >
                    <base-select
                      v-model="form.workplanUser"
                      :inputText.sync="form.workplanUser"
                      :loadMethod="handleGetPersonList"
                      label="empCode"
                      value="empCode"
                      searchInputName="seachKey"
                      :clearable="false"
                      placeholder="请选择责任人"
                      style="width: 100%;"
                      :disabled="isDetail"
                      @select="handlePersonSelect"
                    >
                      <template slot-scope="options">
                        <p
                          style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                          :title="
                            `${options.data['empCode']}--${options.data['empDeptName']}--${options.data['empName']}`
                          "
                        >
                          {{ options.data['empCode'] }}--{{
                            options.data['empDeptName']
                          }}--{{ options.data['empName'] }}
                        </p>
                      </template>
                    </base-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="责任人"
                    prop="workplanUserName"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.workplanUserName"
                      disabled
                      :maxlength="30"
                      placeholder="选择责任人工号会自动填充"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="所属科室"
                    prop="workplanOrgName"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.workplanOrgName"
                      disabled
                      :maxlength="30"
                      placeholder="选择责任人工号会自动填充"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="岗位名称"
                    prop="workplanIdentity"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.workplanIdentity"
                      disabled
                      :maxlength="30"
                      placeholder="选择责任人工号会自动填充"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="预计开始时间"
                    prop="startTime"
                    :rules="rules.required"
                  >
                    <ts-date-picker
                      class="date-picker-show-h-m"
                      style="width: 100%;"
                      :disabled="isDetail"
                      v-model="form.startTime"
                      placeholder="请选择事件日期"
                      clearable
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      valueFormat="YYYY-MM-DD HH:mm"
                      :disabledDate="
                        current => isDateDisabled(current, 'start')
                      "
                      :disabledTime="
                        selectedDate => isTimeDisabled(selectedDate, 'start')
                      "
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="预计结束时间"
                    prop="endTime"
                    :rules="rules.required"
                  >
                    <ts-date-picker
                      class="date-picker-show-h-m"
                      style="width: 100%;"
                      :disabled="isDetail"
                      v-model="form.endTime"
                      placeholder="请选择事件日期"
                      clearable
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      valueFormat="YYYY-MM-DD HH:mm"
                      :disabledDate="current => isDateDisabled(current, 'end')"
                      :disabledTime="
                        selectedDate => isTimeDisabled(selectedDate, 'end')
                      "
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="优先级">
                    <ts-select
                      style="width: 100%"
                      v-model="form.priority"
                      clearable
                      :disabled="isDetail"
                      placeholder="请选择优先级"
                    >
                      <ts-option label="高" value="高" />
                      <ts-option label="中" value="中" />
                      <ts-option label="低" value="低" />
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item
                    label="完成状态"
                    prop="completeStatus"
                    :rules="rules.required"
                  >
                    <ts-select
                      style="width: 100%"
                      v-model="form.completeStatus"
                      clearable
                      :disabled="isDetail"
                      placeholder="请选择"
                    >
                      <ts-option label="未完成" value="0" />
                      <ts-option label="进行中" value="1" />
                      <ts-option label="已完成" value="2" />
                    </ts-select>
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item
                    label="工作计划名称"
                    prop="workplanName"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="form.workplanName"
                      :maxlength="100"
                      :disabled="isDetail"
                      placeholder="请输入"
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="工作内容">
                    <ts-input
                      v-model="form.workplanContent"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="150"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="资源需求">
                    <ts-input
                      v-model="form.resource"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      :disabled="isDetail"
                      maxlength="150"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="目标和指标">
                    <ts-input
                      v-model="form.target"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="150"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="备注">
                    <ts-input
                      v-model="form.remark"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="150"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="反馈与改进">
                    <ts-input
                      v-model="form.feedback"
                      placeholder="请输入"
                      type="textarea"
                      class="textarea"
                      maxlength="150"
                      :disabled="isDetail"
                      show-word-limit
                    />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="24">
                  <ts-form-item label="附件上传">
                    <base-upload
                      class="upload_Button"
                      ref="files"
                      v-model="form.files"
                      :onlyRead="isDetail"
                      moduleName="healthCare"
                    >
                      <ts-button type="primary">
                        <i class="el-icon-upload"></i>
                        上传附件
                      </ts-button>
                    </base-upload>
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div class="form-card-box">
              <colmun-head title="参与人员">
                <template slot="right" v-if="!isDetail">
                  <ts-button type="primary" @click="handleImport"
                    >导入</ts-button
                  >
                </template>
              </colmun-head>
              <form-table
                ref="FormTable"
                class="mrgT8 pdR8"
                :formData="form"
                operateDataKey="playerList"
                :loaclColumns="[]"
                :disabled="true"
                :columns="patientColumns.filter(e => e.show)"
              >
                <template slot="action-bottom">
                  <ts-button
                    type="primary"
                    v-if="!isDetail"
                    @click="handleAddTable('FormTable')"
                  >
                    添加行
                  </ts-button>
                </template>
                <template v-slot:playerUser="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`playerList.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <base-select
                      v-model="row[column.property]"
                      :inputText.sync="row.playerUser"
                      :loadMethod="handleGetPersonList"
                      label="empCode"
                      value="empCode"
                      searchInputName="seachKey"
                      :clearable="false"
                      placeholder="请选择"
                      style="width: 100%;"
                      :disabled="isDetail"
                      @select="item => handleItemPersonSelect(item, row)"
                    >
                      <template slot-scope="options">
                        <p
                          style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                          :title="
                            `${options.data['empCode']}--${options.data['empName']}`
                          "
                        >
                          {{ options.data['empCode'] }}--
                          {{ options.data['empName'] }}
                        </p>
                      </template>
                    </base-select>
                  </ts-form-item>
                </template>
                <template v-slot:playerUserName="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`playerList.${index}.${column.property}`"
                    :rules="rules.required"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :maxlength="10"
                      disabled
                      placeholder="请输入"
                      style="width: 100% !important; min-width: 100% !important;"
                    />
                  </ts-form-item>
                </template>
                <template v-slot:playerIdentity="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`playerList.${index}.${column.property}`"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :maxlength="20"
                      disabled
                      placeholder="请输入"
                    />
                  </ts-form-item>
                </template>
                <template v-slot:playerGender="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`playerList.${index}.${column.property}`"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :maxlength="10"
                      disabled
                      placeholder="请输入"
                      style="width: 100% !important; min-width: 100% !important;"
                    />
                  </ts-form-item>
                </template>
                <template v-slot:playerOrg="{ row, column, index }">
                  <ts-form-item
                    class="flex-item"
                    label=""
                    label-width="0"
                    :prop="`playerList.${index}.${column.property}`"
                  >
                    <ts-input
                      v-model="row[column.property]"
                      :maxlength="20"
                      disabled
                      placeholder="请输入"
                    />
                  </ts-form-item>
                </template>
                <template v-slot:actions="{ row, column, index }">
                  <ts-form-item class="flex-item" label="" label-width="0">
                    <div
                      v-show="!isDetail"
                      @click="delRow('playerList', index)"
                      class="action del"
                    >
                      删除
                    </div>
                  </ts-form-item>
                </template>
              </form-table>
            </div>
            <div v-if="type != 'add'" class="form-card-box">
              <colmun-head title="记录信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item label="姓名">
                    <ts-input v-model="form.createUserName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="工号">
                    <ts-input v-model="form.createUser" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="记录日期">
                    <ts-input v-model="form.createDate" disabled />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
            <div v-if="form.examineUserName" class="form-card-box">
              <colmun-head title="审核信息" />
              <ts-row class="mrgT8 pdR8">
                <ts-col :span="12">
                  <ts-form-item label="姓名">
                    <ts-input v-model="form.examineUserName" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="工号">
                    <ts-input v-model="form.examineUser" disabled />
                  </ts-form-item>
                </ts-col>
                <ts-col :span="12">
                  <ts-form-item label="审核日期">
                    <ts-input v-model="form.examineDate" disabled />
                  </ts-form-item>
                </ts-col>
              </ts-row>
            </div>
          </ts-form>
        </div>
      </el-scrollbar>
      <div class="drawer-footer">
        <ts-button
          type="primary"
          v-if="!isDetail"
          :loading="submitLoading"
          @click="submit"
        >
          提 交
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </div>
    </div>

    <!-- 公用导入 -->
    <base-import
      ref="baseImport"
      :ImportConfiguration="ImportConfiguration"
      @refresh="handleImportSuccess"
    />
  </el-drawer>
</template>

<script>
import FormTable from '@/components/form-table/form-table.vue';
import { deepClone } from '@/unit/commonHandle.js';
import moment from 'moment';

export default {
  components: {
    FormTable
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      submitLoading: false,
      isDetail: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      ImportConfiguration: {
        importTempalteApi: '/ts-hrms/api/workplanRecord/downloadTemplate',
        importTempalteName: '工作计划参与人员导入模板',
        importApi: 'importMedWorkplanPlayer'
      },

      patientColumns: [
        {
          prop: 'playerUser',
          label: '工号',
          required: true,
          show: true
        },
        {
          prop: 'playerUserName',
          width: '100px',
          label: '姓名',
          show: true
        },
        {
          prop: 'playerIdentity',
          label: '岗位名称',
          show: true
        },
        {
          prop: 'playerGender',
          width: '100px',
          label: '性别',
          show: true
        },
        {
          prop: 'playerOrg',
          label: '所属科室',
          show: true
        },
        {
          prop: 'actions',
          width: '100px',
          label: '操作',
          show: true
        }
      ]
    };
  },
  methods: {
    async open({ title, data, type }) {
      this.title = title;
      this.type = type;
      if (type === 'add') {
        this.$set(this, 'form', {
          playerList: [],
          completeStatus: '0',
          priority: '高'
        });
      } else {
        let res = await this.ajax.workplanRecordDetail(data.id);
        if (res.success) this.form = res.object || {};
      }

      if (type === 'details') {
        this.isDetail = true;
        this.patientColumns[5].show = false;
      } else {
        this.patientColumns[5].show = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    },

    isDateDisabled(current, type) {
      if (!current) return false;
      const { startTime, endTime } = this.form;
      if (type === 'start' && endTime) {
        return moment(current).isAfter(moment(endTime), 'minute');
      }
      if (type === 'end' && startTime) {
        return moment(current).isBefore(moment(startTime), 'minute');
      }
      return false;
    },

    isTimeDisabled(selectedDate, type) {
      const { startTime, endTime } = this.form;
      if (!selectedDate || (!startTime && !endTime)) return {};

      const targetTime = moment(type === 'start' ? endTime : startTime);
      const isSameDay = moment(selectedDate).isSame(targetTime, 'day');

      if (!isSameDay) return {};

      const [startHour, endHour] =
        type === 'start' ? [0, targetTime.hour()] : [targetTime.hour(), 23];
      const [startMinute, endMinute] =
        type === 'start' ? [0, targetTime.minute()] : [targetTime.minute(), 59];

      return {
        disabledHours: () => this.generateRange(0, 23, startHour, endHour),
        disabledMinutes: hour =>
          hour === targetTime.hour()
            ? this.generateRange(0, 59, startMinute, endMinute)
            : [],
        disabledSeconds: () => []
      };
    },

    generateRange(min, max, start, end) {
      return Array.from({ length: max - min + 1 }, (_, i) => i + min).filter(
        value => value < start || value > end
      );
    },

    async handleGetPersonList(data) {
      let res = await this.ajax.oaGetEmployeeList({
        pageSize: 15,
        status: 1,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$newMessage('error', res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },

    handlePersonSelect(item) {
      this.$set(this.form, 'workplanUserName', item.empName);
      this.$set(this.form, 'workplanUser', item.empCode);
      this.$set(this.form, 'workplanOrg', item.empDeptCode);
      this.$set(this.form, 'workplanOrgName', item.empDeptName);
      this.$set(this.form, 'workplanIdentity', item.empDutyName);
    },

    handleAddTable(ref) {
      this.$refs[ref].handleFormTableAddRow();
    },

    handleItemPersonSelect(item, row) {
      let existUserCode = this.form.playerList.filter(
        f => f.playerUser === item.empCode
      );
      if (existUserCode.length > 1) {
        let tips = item.empName || `工号为 ${item.empCode}`;
        this.$newMessage('warning', `参与人员已存在 ${tips}`);

        let findIndex = this.form.playerList.findIndex(item => item === row);
        if (findIndex != -1) this.form.playerList.splice(findIndex, 1);
        return;
      }

      this.$set(row, 'playerUserName', item.empName);
      this.$set(row, 'playerOrg', item.empDeptName);
      this.$set(row, 'playerIdentity', item.empDutyName);
      this.$set(
        row,
        'playerGender',
        item.empSex == '1' ? '女' : item.empSex == '0' ? '男' : '未知'
      );
    },

    handleImport() {
      this.$refs.baseImport.open({
        title: '导入参与人员',
        increment: true,
        quantity: false
      });
    },

    handleImportSuccess(importRes) {
      let existNoSet = new Set(this.form.playerList.map(m => m.playerUser));

      let exist = importRes.filter(({ playerUser }) =>
        existNoSet.has(playerUser)
      );
      let userCodes = exist.map(m => m.playerUser).join(',');
      setTimeout(() => {
        userCodes &&
          this.$newMessage(
            'warning',
            `参与人员已存在工号 ${userCodes}, 已去重`
          );
      }, 400);

      this.form.playerList = [
        ...this.form.playerList,
        ...importRes.filter(({ playerUser }) => !existNoSet.has(playerUser))
      ];
    },

    delRow(key, index) {
      this.form[key].splice(index, 1);
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = deepClone(this.form);
        this.submitLoading = true;

        let API = null;
        let doText = '';
        if (this.type === 'add') {
          doText = '新增';
          API = this.ajax.workplanRecordSave;
        } else {
          doText = '修改';
          API = this.ajax.workplanRecordUpdate;
        }

        const res = await API(formData);
        if (res.success && res.statusCode === 200) {
          this.submitLoading = false;
          this.$newMessage('success', `【${doText}】成功`);
          this.$emit('refresh');
          this.close();
        } else {
          this.submitLoading = false;
          this.$newMessage('error', `【${doText}】失败`);
        }
      } catch (error) {
        console.error(error);
      }
    },

    close() {
      this.title = '';
      this.type = '';
      this.isDetail = false;
      this.$set(this, 'form', {});
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.date-picker-show-h-m {
  width: 100% !important;
  ::v-deep .ant-calendar-time-picker-select {
    width: 50% !important;
  }
  ::v-deep .ant-calendar-time-picker-select:last-child {
    display: none;
  }
}

.flex-item {
  ::v-deep {
    .el-form-item__content {
      display: flex;
      align-items: center;
    }
  }
}
.action {
  width: 100%;
  text-align: center;
  cursor: pointer;
  .del {
    color: red;
  }
}

::v-deep {
  .textarea {
    .el-textarea__inner {
      min-height: 70px !important;
      max-height: 110px !important;
    }
  }
}
</style>
