<template>
  <div class="drug-use-top flex-column borders">
    <colmun-head title="药品使用TOP10" background="#efeff4">
      <template slot="right">
        <div class="slotRight">
          <div class="buttons">
            <div
              class="buttons-left"
              :class="activeIndex == '1' ? 'active' : ''"
              @click="switchType(1)"
            >
              科室
            </div>
            <div
              class="buttons-right"
              :class="activeIndex == '2' ? 'active' : ''"
              @click="switchType(2)"
            >
              医师
            </div>
          </div>
        </div>
      </template>
    </colmun-head>
    <div class="useTopModule">
      <div class="echarts mrg8 flex-column">
        <colmun-head
          :title="`药占比排名(${labels})`"
          background="#fff"
          showPopover
          class="noBorder"
        >
          <template #popverContent>
            <p v-for="item in popoverObject[activeIndex][1]" :key="item">
              {{ item }}
            </p>
          </template>
          <template slot="right">
            <div class="slotRight">
              <div class="buttons borders">
                <div
                  class="buttons-left"
                  :class="activeIndex1 == '1' ? 'active' : ''"
                  @click="switchType1(1)"
                >
                  门急诊
                </div>
                <div
                  class="buttons-right"
                  :class="activeIndex1 == '2' ? 'active' : ''"
                  @click="switchType1(2)"
                >
                  住院
                </div>
              </div>
            </div>
          </template>
        </colmun-head>
        <div class="echarts" ref="echarts1"></div>
      </div>
      <div class="echarts mrg8 flex-column">
        <colmun-head
          :title="`长期处方排名(${labels})`"
          background="#fff"
          showPopover
          class="noBorder"
        >
          <template #popverContent>
            <p v-for="item in popoverObject[activeIndex][2]" :key="item">
              {{ item }}
            </p>
          </template>
        </colmun-head>
        <div class="echarts" ref="echarts2"></div>
      </div>
      <div class="echarts mrg8 flex-column">
        <colmun-head
          :title="title"
          background="#fff"
          showPopover
          class="noBorder"
        >
          <template #popverContent>
            <p v-for="item in popoverObject[activeIndex][3]" :key="item">
              {{ item }}
            </p>
          </template>
        </colmun-head>
        <div class="echarts" ref="echarts3"></div>
      </div>
      <div class="echarts mrg8 flex-column">
        <colmun-head
          :title="`超适应症用药排名(${labels})`"
          background="#fff"
          showPopover
          class="noBorder"
        >
          <template #popverContent>
            <p v-for="item in popoverObject[activeIndex][4]" :key="item">
              {{ item }}
            </p>
          </template>
        </colmun-head>
        <div class="echarts" ref="echarts4"></div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      activeIndex: '1',
      activeIndex1: '1',
      date: [],
      echarts1: null,
      echarts2: null,
      echarts3: null,
      echarts4: null,
      echarObject: {
        1: {
          API: [
            this.ajax.getKsTopByFzFmCode,
            this.ajax.getKsTopByCode,
            this.ajax.getKsTopByCode,
            this.ajax.getKsTopByCode
          ]
        },
        2: {
          API: [
            this.ajax.getYsTopByFzFmCode,
            this.ajax.getYsTopByCode,
            this.ajax.getYsTopByCode,
            this.ajax.getYsTopByCode
          ]
        }
      },
      popoverObject: {
        1: {
          1: [
            '药品指标说明：',
            '1、药占比(%)：总药品收入÷总开单科室总收入',
            '2、门诊药占比(%)：门诊药品收入÷ 门诊开单科室总收入',
            '3、住院药占比(%)：住院药品收入÷ 住院所属科室总收入'
          ],
          2: [
            '药品指标说明：',
            '1、长期处方：门诊开具疗程（天数）大于等于30天以上的处方例数'
          ],
          3: ['药品指标说明：', '1.抗肿瘤药物消耗金额：科室抗肿瘤药物消耗金额'],
          4: ['药品指标说明：', '1、超适应症用药：被超适应症使用的药品例数']
        },
        2: {
          1: [
            '药品指标说明：',
            '1、药占比(%)：总药品收入÷总开单科室总收入',
            '2、门诊药占比(%)：门诊药品收入÷ 门诊开单科室总收入',
            '3、住院药占比(%)：住院药品收入÷ 住院所属科室总收入'
          ],
          2: [
            '药品指标说明：',
            '1、长期处方：门诊开具疗程（天数）大于等于30天以上的处方例数'
          ],
          3: [
            '药品指标说明：',
            '1、门诊大处方数：同一患者同一医生单日药品处方总金额＞1000元'
          ],
          4: ['药品指标说明：', '1、超适应症用药：被超适应症使用的药品例数']
        }
      },
      defaultParam: {
        title: [
          '药占比',
          '长期触发排名',
          '抗肿瘤药物消耗金额排名',
          '超适应症用药排名'
        ],
        params: [
          {
            fzCode: '10001,20001',
            fmCode: '10002,20002'
          },
          {
            busiCode: '10024'
          },
          {
            busiCode: '10007'
          },
          {
            busiCode: '10004,20004'
          }
        ],
        ranking: '10',
        echarts: ['echarts1', 'echarts2', 'echarts3', 'echarts4'],
        valueType: ['%', '例', '元', '例'],
        color: ['#73C2F8', '#B7AF8B', '#A4B9CB', '#539DF4']
      }
    };
  },
  watch: {
    activeIndex: {
      handler(val) {
        this.renderData();
      }
    }
  },
  computed: {
    labels() {
      return this.activeIndex == '1' ? '科室' : '医师';
    },
    title() {
      return this.activeIndex == '1'
        ? `抗肿瘤药物消耗金额排名(${this.labels})`
        : `门诊大处方数示警(${this.labels})`;
    }
  },
  methods: {
    async refresh(date) {
      this.date = date;
      this.$nextTick(() => {
        this.renderData();
      });
    },
    switchType(index) {
      this.activeIndex = index;
    },
    switchType1(indexs) {
      if (this.activeIndex1 == indexs) return;
      this.activeIndex1 = indexs;
      let param = {
        busiDateBegin: this.date[0],
        busiDateEnd: this.date[1]
      };
      let index = 0;
      let i = this.defaultParam.echarts[0];
      let API = this.echarObject[this.activeIndex].API[index];
      let params = { ...this.defaultParam.params[index] };
      params.fzCode = params.fzCode.split(',')[this.activeIndex1 - 1];
      params.fmCode = params.fmCode.split(',')[this.activeIndex1 - 1];
      API({
        ...param,
        ranking: this.defaultParam.ranking,
        ...params
      }).then(res => {
        if (!res.success) {
          let title = this.defaultParam.title[index];
          this.$newMessage('error', `${title}数据获取失败！`);
          return;
        }
        let data = res.object || [];
        data = data.map(e => {
          let value = e.F_VAL;
          if (index == 0) {
            value = (e.F_VAL * 100).toFixed(2);
          }
          return {
            name: e.KSMC || e.YSMC,
            value
          };
        });
        let valueType = this.defaultParam.valueType[index];
        this.renderTop10Echarts(
          data,
          i,
          this.defaultParam.color[index],
          valueType
        );
      });
    },
    renderData() {
      let param = {
        busiDateBegin: this.date[0],
        busiDateEnd: this.date[1]
      };
      this.defaultParam.echarts.forEach((i, index) => {
        let API = this.echarObject[this.activeIndex].API[index];
        let params = { ...this.defaultParam.params[index] };
        if (index == 0) {
          params.fzCode = params.fzCode.split(',')[this.activeIndex1 - 1];
          params.fmCode = params.fmCode.split(',')[this.activeIndex1 - 1];
        }
        if (index == 2 && this.activeIndex == 2) {
          params = {
            busiCode: '10006'
          };
        }
        API({
          ...param,
          ranking: this.defaultParam.ranking,
          ...params
        }).then(res => {
          if (!res.success) {
            let title = this.defaultParam.title[index];
            if (index == 2 && this.activeIndex == 2) {
              title = '门诊大处方数示警';
            }
            this.$newMessage('error', `${title}数据获取失败！`);
            return;
          }
          let data = res.object || [];
          data = data.map(e => {
            let value = e.F_VAL;
            if (index == 0) {
              value = (e.F_VAL * 100).toFixed(2);
            }
            return {
              name: e.KSMC || e.YSMC,
              value
            };
          });
          let valueType = this.defaultParam.valueType[index];
          if (index == 2 && this.activeIndex == 2) {
            valueType = '例';
          }
          this.renderTop10Echarts(
            data,
            i,
            this.defaultParam.color[index],
            valueType
          );
        });
      });
    },
    renderTop10Echarts(data, echarts, color, valueType = '') {
      data.reverse();
      let yData = data.map(e => e.name);
      let serData = data.map(e => e.value);
      let options = {
        legend: null,
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          formatter: params => {
            let num = Number(params[0].value).toLocaleString('en-US');
            return `${params[0].name}-${num}${valueType}`;
          }
        },
        grid: {
          left: '2%',
          right: '20%',
          bottom: '3%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'value',
          show: false,
          axisLine: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            formatter: `{value}${valueType}`
          }
        },
        yAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          data: yData
        },
        series: [
          {
            name: '',
            type: 'bar',
            label: {
              show: true,
              position: 'right',
              formatter: params => {
                let num = Number(params.value).toLocaleString('en-US');
                return `${num}${valueType}`;
              }
            },
            emphasis: {
              focus: 'series'
            },
            itemStyle: {
              color
            },
            data: serData
          }
        ]
      };
      let _self = this;
      if (!_self[echarts]) {
        _self[echarts] = _self.$echarts.init(_self.$refs[echarts]);
      }
      _self[echarts].clear();
      _self[echarts].setOption(options);
    }
  }
};
</script>

<style lang="scss" scoped>
.drug-use-top {
  flex: 1;
  height: 100%;
  /deep/ {
    .noBorder {
      .lefts {
        display: none;
      }
    }
    .useTopModule {
      flex: 1;
      height: 100%;
      padding: 4px;
      display: flex;
      .echarts {
        flex: 1;
      }
    }
    .slotRight {
      display: flex;
    }
    .buttons {
      width: 120px;
      height: 26px;
      border-radius: 4px;
      display: flex;
      margin-right: 8px;
      margin-top: 2px;
      background: #fff;
      &.borders {
        border: 1px solid #eee !important;
      }
      .buttons-left,
      .buttons-right {
        flex: 1;
        text-align: center;
        line-height: 24px;
        border-radius: 4px;
        cursor: pointer;
        &.active {
          color: $primary-blue;
          background: #d2dcfc;
        }
      }
    }
    .mrg8 {
      margin-right: 8px;
    }
    .quality-title {
      position: relative;
      display: inline-flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      background: #f6f8fc;
      padding: 2px 4px;
    }

    /* 菱形图标样式 */
    .diamond-icon {
      color: #4285f4; /* 蓝色菱形 */
      font-size: 18px;
      margin-right: 5px;
    }
    /* 标题文字样式 */
    .title-text {
      border-bottom: 1px solid #65bdfd;
      border-radius: 0px 0px 10px 0px;
      padding-right: 10px;
      padding-bottom: 4px;
      color: $primary-blue;
    }
    .line {
      flex-grow: 1;
      border-top: 1px solid #65bdfd;
      height: 10px;
      margin-top: 12px;
      margin-left: -3px;
      border-radius: 10px 0 0 0;
    }
    .lines {
      width: auto;
      color: #65bdfd;
      padding-left: 10px;
    }
  }
}
</style>
