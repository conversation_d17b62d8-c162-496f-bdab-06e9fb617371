<template>
  <el-drawer
    custom-class="ts-custom-default-drawer darwer-handover"
    direction="rtl"
    size="90%"
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <template slot="title">
      <span class="dialog-title">{{ title }}</span>
    </template>
    <div class="content-container" id="custom-container">
      <div class="cantainer-left" v-if="type != 'add' && MedShiftRecord">
        <searchList
          ref="searchList"
          :recordId="MedShiftRecord.id"
          :activeId="form.patientId"
          @refresh="data => this.detailPaient(data, true)"
        />
        <!-- @success="getUserList" -->
      </div>
      <div class="cantainer-right flex-column">
        <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
          <div class="form-container">
            <ts-form ref="ruleForm" :model="form" labelWidth="120px">
              <!-- 患者信息 -->
              <div class="form-card-box">
                <colmun-head title="患者信息" />
                <ts-row class="mrgT8 pdR8">
                  <ts-col :span="6">
                    <ts-form-item
                      label="患者姓名"
                      prop="patientId"
                      :rules="rules.required"
                    >
                      <base-select
                        v-if="type == 'add'"
                        v-model="form.patientId"
                        :inputText.sync="form.patnName"
                        :loadMethod="handleGetPersonList"
                        label="id"
                        value="id"
                        searchInputName="name"
                        :clearable="false"
                        placeholder="请选择"
                        style="width: 100%;"
                        :disabled="isDetail"
                        @select="handlePersonSelect"
                      >
                        <template slot-scope="options">
                          <p
                            style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                            :title="`${options.data['name']}`"
                          >
                            {{ options.data['name'] }}
                          </p>
                        </template>
                      </base-select>
                      <ts-input v-else v-model="form.patnName" disabled />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="床号">
                      <ts-input v-model="form.bedNo" disabled />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="住院号">
                      <ts-input
                        v-model="form.patnNo"
                        placeholder="选择人员自动填充住院号"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="性别">
                      <ts-input
                        v-model="form.patnSexName"
                        placeholder="选择人员自动填充性别"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="年龄">
                      <ts-input
                        v-model="form.patnAge"
                        placeholder="选择人员自动填充年龄"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="院区">
                      <ts-input
                        v-model="form.areaText"
                        placeholder="选择患者自动填充院区"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="科室">
                      <ts-input
                        v-model="form.deptName"
                        placeholder="选择患者自动填充科室"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item label="入院时间">
                      <ts-input
                        v-model="form.inDate"
                        placeholder="选择患者自动填充入院时间"
                        disabled
                      />
                    </ts-form-item>
                  </ts-col>
                  <ts-col :span="6">
                    <ts-form-item
                      label="是否床第交接"
                      prop="bedShift"
                      :rules="rules.required"
                    >
                      <ts-select
                        style="width: 100%;"
                        v-model="form.bedShift"
                        :disabled="type == 'details'"
                        placeholder="请选择"
                      >
                        <ts-option label="是" value="1"></ts-option>
                        <ts-option label="否" value="0"></ts-option>
                      </ts-select>
                    </ts-form-item>
                  </ts-col>
                </ts-row>
              </div>
              <!-- 指标信息 -->
              <div class="form-card-box tableHeight">
                <colmun-head title="指标信息">
                  <!-- <template slot="right">
                    <ts-button type="primary">同步</ts-button>
                    <el-popover
                      placement="right"
                      width="400"
                      trigger="hover"
                      popper-class="popverContent"
                    >
                      <i
                        class="vxe-icon-question-circle popverIcon"
                        slot="reference"
                      ></i>
                      <p>同步说明：</p>
                      <p>只同步值班时间范围内的患者指标内容。</p>
                    </el-popover>
                  </template> -->
                </colmun-head>
                <form-vxe-table
                  ref="formVxeTable"
                  id="dialog-quality-details_cf"
                  class="formVxeTable mrgT8 pdlr8"
                  :data="tableData"
                  :min-height="tableHeight"
                  :columns="columnsApprove"
                  :edit-rules="validRulesApprove"
                  :edit-config="editConfig"
                  :cell-class-name="cellClassName"
                  :span-method="mergeRowMethod"
                  @edit-activated="handleEditActivated"
                >
                  <template v-slot:inputTextArea="{ row, column }">
                    <ts-input
                      v-model="row[column.property]"
                      placeholder="请输入"
                      ref="inputTextArea"
                      type="textarea"
                      class="editFormtextarea"
                      :maxlength="row.maxLength ?? 2000"
                      show-word-limit
                      @change="asyncOtherData(row, column.property)"
                    />
                  </template>
                  <template v-slot:inputNumber="{ row, column }">
                    <el-input-number
                      style="width: 220px;"
                      class="el-input-format"
                      ref="inputNumber"
                      v-model="row[column.property]"
                      :controls="false"
                    />
                  </template>
                  <template v-slot:inputDatePicker="{ row, column }">
                    <!-- <el-date-picker
                      style="width: 220px;"
                      ref="inputDatePicker"
                      v-model="row[column.property]"
                      placeholder="请选择"
                      clearable
                      format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd"
                      @input="asyncOtherData(row, column.property)"
                      @change="asyncOtherData(row, column.property)"
                      @blur="asyncOtherData(row, column.property)"
                    /> -->
                    <!-- <ts-date-picker
                      style="width: 220px"
                      ref="inputDatePicker"
                      v-model="row[column.property]"
                      valueFormat="YYYY-MM-DD"
                      placeholder="请选择"
                      @change="asyncOtherData(row, column.property)"
                    /> -->
                    <ts-date-picker
                      class="archive-ts-date date-picker-show-h-m"
                      style="width: 220px;"
                      ref="inputDatePicker"
                      v-model="row[column.property]"
                      placeholder="请选择"
                      showTime
                      autofocus
                      valueFormat="YYYY-MM-DD HH:mm"
                      :getPopupContainer="
                        () => document.querySelector('#custom-container')
                      "
                      @change="asyncOtherData(row, column.property)"
                    />
                  </template>
                </form-vxe-table>
              </div>
            </ts-form>
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="drawer-footer">
      <ts-button type="primary" v-if="type != 'details'" @click="submit"
        >提交</ts-button
      >
      <ts-button @click="close" class="shallowButton">关 闭</ts-button>
    </div>
  </el-drawer>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import searchList from './searchList.vue';
import darwerHandover from './darwer-handover';
import formVxeTable from './form-vxe-table.vue';
export default {
  mixins: [darwerHandover],
  components: { searchList, formVxeTable },
  data() {
    return {
      visible: false,
      isDetail: false,
      title: '',
      type: '',
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      MedShiftRecord: null
    };
  },
  computed: {
    tableHeight() {
      return window.innerHeight - 345;
    }
  },
  methods: {
    async open({ data = null, title, type, MedShiftRecord = null }) {
      if (data) {
        this.form = data;
        this.detailPaient(data);
        this.form.patnSexName = this.form.patnSex == '1' ? '男' : '女';
      }
      this.title = title;
      this.type = type;
      this.MedShiftRecord = MedShiftRecord;
      if (type == 'detail') {
        this.isDetail = true;
      }
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.ruleForm?.clearValidate();
      });
    },
    async fullValidEvent() {
      const $table = this.$refs.formVxeTable.$refs.tsVxeTable;
      const errMap = await $table.validate(true);
      if (errMap) {
        let rows = errMap.IndicatorContents[0].row;
        this.$newMessage(
          'warning',
          `请完善【${rows.groupName}-${rows.IndicatorName}】指标数据`
        );
        return true;
      }
      return false;
    },
    async submit() {
      try {
        let validate = await this.fullValidEvent();
        if (validate) return false;
        await this.$refs.ruleForm.validate();
        let data = deepClone(this.form);
        data = { ...data, ...this.formatPointData() };
        data.recordId = this.MedShiftRecord.id;
        let API = this.ajax.medShiftPatientSave;
        if (this.type != 'add') {
          API = this.ajax.medShiftPatientUpdate;
          data = { id: this.pointId, ...this.formatPointData() };
        }
        const res = await API(data);
        if (res.success && res.statusCode === 200) {
          this.$newMessage('success', '【操作】成功!');
          this.$emit('refresh', this.type);
          this.close();
        } else {
          this.$newMessage('error', res.message || '【操作】失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },

    close() {
      this.visible = false;
      this.type = undefined;
      this.form = {};
      this.isDetail = false;
      this.tableData = [];
      this.pointId = '';
      this.MedShiftRecord = null;
    }
  }
};
</script>

<style lang="scss" scoped>
.archive-ts-date {
  ::v-deep {
    .ant-input {
      background: #eaebfa !important;
    }
  }
}
/deep/ .ant-calendar-picker-container {
  z-index: 9999 !important;
}
.date-picker-show-h-m {
  width: 100% !important;
  ::v-deep .ant-calendar-time-picker-select {
    width: 50% !important;
  }
  ::v-deep .ant-calendar-time-picker-select:last-child {
    display: none;
  }
}
::v-deep {
  .darwer-handover {
    .content-container {
      display: flex;
      flex-direction: row !important;
      height: calc(100% - 100px);
      .cantainer-left {
        width: 200px;
        height: 100%;
      }
      .cantainer-right {
        flex: 1;
        height: 100%;
        width: 100%;
        margin-left: 8px;
        border: 1px solid $primary-blue;
        border-radius: 4px;
        overflow: hidden;
        .form-card-box {
          border: none !important;
          margin-top: 0 !important;
          margin-bottom: 8px;
          position: relative;
          .popverIcon {
            position: relative;
            font-size: 22px;
            top: 4px;
            margin-left: 5px;
          }
        }
        .editFormtextarea {
          .el-textarea__inner {
            min-height: 140px !important;
          }
        }
      }
    }
    .drawer-footer {
      display: flex;
      height: 40px;
      margin-top: 8px;
      align-items: center;
      justify-content: flex-end;
      background: #f5f7fa;
    }
  }
  .formVxeTable {
    padding-bottom: 8px;
    .disabled_cell {
      background-color: #f2f2f2;
    }
    .preContent {
      line-height: 25px;
      font-size: 16px;
      margin: 0;
      white-space: pre-wrap;
      font-family: 'Microsoft Kai', '楷体', serif;
      color: #000;
    }
  }
  .textarea {
    .el-textarea__inner {
      min-height: 120px !important;
      max-height: 120px !important;
    }
  }
  .flex-item .el-input {
    min-width: auto;
  }
}
</style>
