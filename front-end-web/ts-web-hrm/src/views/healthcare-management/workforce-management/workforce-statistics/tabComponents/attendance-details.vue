<template>
  <div class="template-two-zone">
    <div class="right">
      <new-ts-search-bar
        v-model="searchForm"
        :formList="searchList"
        :elementCol="14"
        @search="search"
        :resetData="reset"
        :showMoreSearch="true"
      >
        <template slot="dept">
          <input-ztree
            v-model="searchForm.deptIds"
            ref="tree"
            placeholder="请选择科室"
            :treeData="deptTreeData"
            :defaultExpandedKeys="defaultExpandedKeys"
            style="width: 100%"
            @ok="handleSelectOk"
            key="code"
          />
        </template>
      </new-ts-search-bar>
      <div class="flex-row-between mtb8">
        <div class="buttons">
          <div
            class="buttons-left"
            :class="activeIndex == '1' ? 'active' : ''"
            @click="switchType(1)"
          >
            个人
          </div>
          <div
            class="buttons-right"
            :class="activeIndex == '2' ? 'active' : ''"
            @click="switchType(2)"
          >
            科室
          </div>
        </div>
        <ts-button class="shallowButton" @click="handleExportDarwer">
          自定义导出
        </ts-button>
      </div>
      <TsVxeTemplateTable
        id="table_attendance_details"
        class="form-table"
        ref="table"
        :columns="columns"
        :defaultSort="{
          sidx: activeIndex == '1' ? 't1.org_id' : 't1.organization_id',
          sord: 'desc'
        }"
        :scroll-y="{ enabled: true }"
        :scroll-x="{ enabled: true }"
        :show-footer="activeIndex == '2'"
        :footer-data="footerData"
        :footer-cell-class-name="footerCellClassName2"
        @refresh="handleRefreshTable"
      />
    </div>
    <calendar-user ref="calendarUser" />
    <drawer-customize-export
      ref="drawerCustomizeExport"
      @export="handleExport"
    />
    <drawer-user-details ref="drawerUserDetails" />
    <drawer-dept-details
      ref="drawerDeptDetails"
      :classList="classList"
      :oaTypeList="oaTypeList"
    />
  </div>
</template>

<script>
import table from '../mixins/attendance-details';
import { checkSearchFormDate } from '@/unit/commonHandle.js';
import calendarUser from '../../workforce-overivew/components/calendar-user.vue';
import drawerCustomizeExport from '../components/drawer-customize-export.vue';
import drawerUserDetails from '../components/drawer-user-details.vue';
import drawerDeptDetails from '../components/drawer-dept-details.vue';
import moment from 'moment';
export default {
  mixins: [table],
  components: {
    calendarUser,
    drawerCustomizeExport,
    drawerUserDetails,
    drawerDeptDetails
  },
  data() {
    return {};
  },
  watch: {
    activeIndex: {
      handler() {
        this.$nextTick(() => {
          this.search();
        });
      }
    }
  },
  methods: {
    async refresh() {
      await this.getAllDictItemList();
      this.$nextTick(() => {
        this.search();
      });
    },
    reset() {
      this.$refs.tree.fullPath = '';
      return {
        wheelDate: [
          moment()
            .add(-1, 'month')
            .startOf('month')
            .format('YYYY-MM-DD'),
          moment()
            .add(-1, 'month')
            .endOf('month')
            .format('YYYY-MM-DD')
        ]
      };
    },
    async search() {
      if (!checkSearchFormDate(this)) return;
      this.$refs.table.pageNo = 1;
      if (this.activeIndex == 2) await this.getFooterData();
      await this.handleRefreshTableCol();
      await this.handleRefreshTable();
    },
    handleExport() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm } = this.getQueryParam();
      let aDom = document.createElement('a');
      let conditionList = Object.keys(searchForm).map(key => {
        let val = searchForm[key];
        return `${key}=${val}`;
      });
      aDom.href = `/ts-hrms/api/scheduleStatistics/export?${conditionList.join(
        '&'
      )}`;
      aDom.click();
    },
    async handleRefreshTable() {
      if (!checkSearchFormDate(this)) return;
      let { searchForm, pageNo, pageSize } = this.getQueryParam();
      let res = await this.ajax.getScheduleTableDataList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/template.scss';
/deep/ .Attributes {
  color: #fff;
  border-radius: 12px;
  padding: 4px 6px;
  font-size: 12px;
  line-height: 24px;
  margin-left: 4px;
  &.blue {
    background-color: $primary-blue;
  }
  &.green {
    background-color: $success-color;
  }
  &.yellow {
    background-color: $warning-color;
  }
}
/deep/ .font-bold {
  font-weight: bold;
}
.buttons {
  min-width: 120px;
  height: 26px;
  border-radius: 4px;
  display: flex;
  margin-right: 8px;
  margin-top: 2px;
  background: #fff;
  border: 1px solid #aaa;
  .buttons-left,
  .buttons-right {
    flex: 1;
    text-align: center;
    line-height: 24px;
    border-radius: 4px;
    cursor: pointer;
    &.active {
      color: $primary-blue;
      background: #d2dcfc;
    }
  }
}
</style>
