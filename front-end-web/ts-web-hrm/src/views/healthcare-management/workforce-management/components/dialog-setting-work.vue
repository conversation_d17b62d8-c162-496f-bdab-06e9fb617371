<template>
  <div v-show="visible" class="draggable-float-window" :style="windowStyle">
    <div class="float-header" @mousedown="startDrag">
      <span>{{ title }}</span>
      <i class="el-icon-close" @click.stop="close"></i>
    </div>
    <ts-tabs v-model="activeTab">
      <ts-tab-pane name="1" label="按班次排班"></ts-tab-pane>
      <ts-tab-pane name="2" label="组合班次排班"></ts-tab-pane>
    </ts-tabs>
    <div class="float-content" v-show="activeTab == '1'">
      <div class="contents">
        <div class="contents-left flex-column">
          <div class="search-top">
            <ts-input
              v-model="searchVal"
              clearable
              style="width: 50%;"
              placeholder="搜索班次"
            />
          </div>
          <PurityVxeTable
            id="table_dialog-setting-work"
            class="form-table"
            ref="table"
            :defaultSort="{
              sidx: 'create_date',
              sord: 'desc'
            }"
            :checkboxConfig="{
              reserve: true
            }"
            :columns="columns"
            @refresh="handleRefreshTable"
            @selection-change="handleSelectionChange"
          />
        </div>
        <div class="contents-rigt flex-column">
          <colmun-head :title="`已选班次(${scheduList.length})`" />
          <el-scrollbar
            style="flex: 1;width: 100%; margin-top: 8px;"
            wrap-style="overflow-x:hidden;"
          >
            <div class="boxList col">
              <div
                class="boxP"
                v-for="(item, i) in scheduList"
                :key="item.classesId"
                :style="{
                  backgroundColor: `${item.classesColor}32`,
                  border: `1px solid ${item.classesColor}`,
                  color: '#333'
                }"
              >
                <p :title="item.classesName">{{ item.classesName }}</p>
                <i
                  class="el-icon-close"
                  v-if="item.type == '1' || item.type == '5'"
                  @click="delItem(i)"
                ></i>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <div class="remark">
        <span>备注：</span>
        <ts-input
          style="width: calc(100% - 50px);"
          v-model="remark"
          placeholder="请输入"
          maxlength="20"
          show-word-limit
        />
      </div>
      <div class="dialog-footer">
        <span>已选班次：{{ scheduList.length }}个班次</span>
        <div>
          <ts-button type="primary" @click="submit">保 存</ts-button>
          <ts-button class="shallowButton" @click="close">关 闭</ts-button>
        </div>
      </div>
    </div>
    <div class="float-content" v-show="activeTab == '2'">
      <div class="search-top">
        <ts-input
          v-model="searchVal1"
          clearable
          style="width: 50%;"
          placeholder="搜索组合班次"
        />
      </div>
      <el-scrollbar
        style="flex: 1;width: 100%;"
        wrap-style="overflow-x:hidden;"
      >
        <div class="boxList wrap">
          <div
            class="box"
            :class="activeGroupId == item.id ? 'active' : ''"
            v-for="item in filteredGroupList"
            :key="item.id"
            :style="{
              backgroundColor: `#fff`,
              border: `1px solid #333`,
              color: '#333'
            }"
            @click="checkGroup(item)"
          >
            {{ item.composeName }}
          </div>
        </div>
      </el-scrollbar>
      <el-divider>已选班次</el-divider>
      <el-scrollbar
        style="flex: 1;width: 100%;"
        wrap-style="overflow-x:hidden;"
      >
        <div class="boxList">
          <div
            class="box"
            v-for="item in checkGroupList"
            :key="item.id"
            :style="{
              backgroundColor: `${item.classesColor}32`,
              border: `1px solid ${item.classesColor}`,
              color: '#333'
            }"
            @click="composeName(item)"
          >
            {{ item.classesName }}
          </div>
        </div>
      </el-scrollbar>
      <div class="dialog-footer" style="justify-content: flex-end;">
        <div>
          <ts-button type="primary" @click="submitGroup">保 存</ts-button>
          <ts-button class="shallowButton" @click="close">关 闭</ts-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment';
export default {
  name: 'DraggableFloatWindow',
  data() {
    return {
      visible: false,
      dragging: false,
      posX: 0,
      posY: 0,
      startX: 0,
      startY: 0,
      searchVal: '',
      searchVal1: '',
      remark: '',
      data: {},
      scheduList: [],
      workList: [],
      date: '',
      title: '',
      activeTab: '1',
      activeGroupId: '',
      checkGroupList: [],
      groupList: [],
      weeks: [
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六',
        '星期日'
      ],
      weekdays: [
        '星期日',
        '星期一',
        '星期二',
        '星期三',
        '星期四',
        '星期五',
        '星期六'
      ],
      type: '',
      debounceTimer: null,
      debounceConfig: {
        delay: 500,
        immediate: false
      },
      columns: [
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '班次名称',
          align: 'center',
          prop: 'classesName'
        },
        {
          label: '班次时间',
          align: 'left',
          prop: 'classesWorktime',
          render: (h, { row }) => {
            let list = [];
            row.classesWorktime.split(',').forEach(e => {
              list.push(h('p', {}, e));
            });
            return h('div', { class: 'timeList' }, [...list]);
          }
        }
      ]
    };
  },
  computed: {
    windowStyle() {
      return {
        left: `${this.posX}px`,
        top: `${this.posY}px`
      };
    },
    filteredGroupList() {
      return this.groupList.filter(
        e => e.composeName.indexOf(this.searchVal1) > -1
      );
    }
  },
  watch: {
    searchVal(newVal) {
      this.debouncedSearch(newVal);
    }
  },
  methods: {
    isActive(item) {
      let ids = this.scheduList
        .map(e => {
          return e.classesId;
        })
        .join(',');
      if (ids == '') return '';
      return ids.indexOf(item.id) > -1 ? 'active' : '';
    },
    checkGroup(item) {
      this.checkGroupList = [];
      if (this.activeGroupId == item.id) {
        this.activeGroupId = '';
      } else {
        this.activeGroupId = item.id;
        let colorList = item.composeColours.split('|');
        let composeContent = item.composeContent.split('|');
        let composeContentId = item.composeContentId.split('|');
        let composeTime = item.composeTime.split('|');
        let composeTypeId = item.composeTypeId.split('|');
        colorList.forEach((i, index) => {
          this.checkGroupList.push({
            employeeId: this.data.employeeId,
            typeId: composeTypeId[index],
            classesId: composeContentId[index],
            classesColor: colorList[index],
            classesWorktime: composeTime[index],
            classesName: composeContent[index]
          });
        });
      }
    },
    async open({ x, y, data, date, scheduList, type = 'date' }) {
      this.close();
      this.visible = true;
      const maxX = window.innerWidth - 650;
      const maxY = window.innerHeight - 400;
      this.posX = Math.max(0, Math.min(x, maxX));
      this.posY = Math.max(0, Math.min(y, maxY));
      this.data = data;
      this.scheduList = scheduList;
      this.date = date;
      this.type = type;
      await this.getGroupList();
      if (type == 'week') {
        this.title = `${data.employeeName}-${this.weeks[date - 1]}`;
      } else {
        let week = this.weekdays[new Date(date).getDay()];
        this.title = `${data.employeeName}-${date}-${week}`;
      }
      if (scheduList.length) {
        this.remark = scheduList[0].remark;
        let list = scheduList.map(e => {
          return { id: e.classesId };
        });
        this.$refs.table.tsVxeTableRef().setCheckboxRow(list, true);
      }
      this.$nextTick(() => {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
      });
    },
    async getGroupList() {
      let group = await this.ajax.medScheduleComposeClasseslist({
        pageNo: 1,
        pageSize: 1000,
        isSchedule: 'Y',
        sidx: 'create_date',
        sord: 'desc',
        employeeId: this.data.employeeId
      });
      this.groupList = group.rows || [];
    },
    handleSelectionChange(selection) {
      this.scheduList = [];
      selection.forEach(item => {
        let obj = {
          employeeId: this.data.employeeId,
          typeId: item.typeId,
          classesId: item.id,
          classesColor: item.classesColor,
          classesWorktime: item.classesWorktime,
          classesName: item.classesName
        };
        if (this.type == 'week') {
          obj.scheduleWeek = this.date;
        } else {
          obj.scheduleDate = this.date;
        }
        this.scheduList.push(obj);
      });
    },
    delItem(index) {
      this.$refs.table
        .tsVxeTableRef()
        .setCheckboxRow({ id: this.scheduList[index].classesId }, false);
      this.scheduList.splice(index, 1);
    },
    debouncedSearch: function() {
      const config = this.debounceConfig;
      if (this.debounceTimer) clearTimeout(this.debounceTimer);
      if (config.immediate && !this.debounceTimer) {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
        this.debounceTimer = setTimeout(() => {
          this.debounceTimer = null;
        }, config.delay);
        return;
      }
      this.debounceTimer = setTimeout(() => {
        this.$refs.table.pageNo = 1;
        this.handleRefreshTable();
        this.debounceTimer = null;
      }, config.delay);
    },
    startDrag(event) {
      event.preventDefault();
      this.dragging = true;

      // 获取初始位置
      this.startX = event.clientX - this.posX;
      this.startY = event.clientY - this.posY;

      // 绑定移动事件
      document.addEventListener('mousemove', this.onDrag);
      document.addEventListener('mouseup', this.stopDrag);
    },
    submit() {
      let key = 'scheduleDate';
      if (this.type == 'week') {
        key = 'scheduleWeek';
      }
      let list = this.data.scheduleRecords.filter(
        e => e[key] != this.date || e.id == null
      );
      let lists = this.scheduList.map(e => {
        return { ...e, remark: this.remark };
      });
      this.$set(this.data, 'scheduleRecords', [...list, ...lists]);
      this.$emit('refresh');
      this.close();
    },
    submitGroup() {
      let key = 'scheduleDate';
      if (this.type == 'week') {
        key = 'scheduleWeek';
      }
      this.checkGroupList.forEach((i, iIndex) => {
        let date = '';
        if (this.type != 'week') {
          date = moment(this.date)
            .add(iIndex, 'days')
            .format('YYYY-MM-DD');
        } else {
          date = Number(this.date) + Number(iIndex);
        }
        this.data.scheduleRecords.forEach((j, index) => {
          if (date == j[key]) {
            this.data.scheduleRecords.splice(index, 1);
          }
        });
        let obj = { ...i };
        obj[key] = date;
        this.data.scheduleRecords.push(obj);
      });
      this.$set(this.data, 'scheduleRecords', this.data.scheduleRecords);
      this.$emit('refresh');
      this.close();
    },
    // 同步班次数据
    async getWorkClass(employeeId) {
      let res2 = await this.ajax.getScheduleClassesListNoPage({ employeeId });
      this.workList = res2.object || [];
    },
    // 班次表格
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        searchForm = {
          pageNo,
          pageSize: this.$refs.table.pageSize,
          classesName: this.searchVal,
          employeeId: this.data.employeeId,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] == null ||
          searchForm[key] == undefined ||
          searchForm[key] == ''
        ) {
          delete searchForm[key];
        }
      });
      let res = await this.ajax.getScheduleClassesList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },
    close() {
      this.visible = false;
      this.posX = 0;
      this.posY = 0;
      this.searchVal = '';
      this.data = {};
      this.remark = '';
      this.date = '';
      this.scheduList = [];
      this.title = '';
      this.searchVal1 = '';
      this.activeGroupId = '';
      this.checkGroupList = [];
      this.type = '';
      this.workList = [];
      this.activeTab = '1';
      this.$refs.table.tsVxeTableRef().clearCheckboxRow();
    },
    onDrag(event) {
      if (this.dragging) {
        // 计算新位置
        const newX = event.clientX - this.startX;
        const newY = event.clientY - this.startY;

        // 边界限制
        const maxX = window.innerWidth - this.$el.offsetWidth;
        const maxY = window.innerHeight - this.$el.offsetHeight;
        this.posX = Math.max(0, Math.min(newX, maxX));
        this.posY = Math.max(0, Math.min(newY, maxY));
      }
    },
    stopDrag() {
      this.dragging = false;
      document.removeEventListener('mousemove', this.onDrag);
      document.removeEventListener('mouseup', this.stopDrag);
    }
  }
};
</script>

<style lang="scss" scoped>
.draggable-float-window {
  position: fixed;
  background: white;
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  width: 650px;
  height: 400px;
  z-index: 9999;
  user-select: none;
  display: flex;
  flex-direction: column;
}

.float-header {
  padding: 4px;
  height: 30px;
  background: #d5dcf9;
  display: flex;
  justify-content: space-between;
  span {
    height: 30px;
  }
  i {
    cursor: pointer;
    font-size: 16px;
    margin-top: 3px;
  }
}
/deep/ .el-tabs__header {
  margin: 0 !important;
}
/deep/ .timeList {
  p {
    margin: 0;
    line-height: 20px;
    text-align: center;
  }
}
.float-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-top: 1px solid #eee;
  padding: 8px;
  .search-top {
    margin-bottom: 4px;
  }
  .boxList {
    display: flex;
    &.warp {
      flex-wrap: wrap;
    }
    &.col {
      flex-direction: column;
    }
    .box {
      padding: 4px 12px;
      border-radius: 2px;
      margin-right: 2px;
      margin-bottom: 4px;
      position: relative;
      cursor: pointer;
      &.active {
        &::before {
          content: '';
          position: absolute;
          right: 0px;
          bottom: 0px;
          height: 0;
          width: 0;
          border: 10px solid $primary-blue;
          border-left-color: transparent;
          border-top-color: transparent;
        }
        &::after {
          content: '✓';
          position: absolute;
          right: 0;
          bottom: 0px;
          color: #fff;
          font-size: 15px;
          font-weight: bold;
          line-height: 14px;
        }
      }
    }
    .boxP {
      display: flex;
      justify-content: space-between;
      padding: 4px 2px 4px 12px;
      border-radius: 2px;
      margin-right: 2px;
      margin-bottom: 4px;
      position: relative;
      cursor: pointer;
      p {
        width: 140px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        margin: 0;
        line-height: 20px;
      }
      .el-icon-close {
        color: $error-color;
        font-size: 16px;
        margin-top: 2px;
      }
    }
  }
  .remark {
    width: 100%;
    display: flex;
    span {
      flex: 1;
      line-height: 30px;
    }
  }
  .dialog-footer {
    margin-top: 3px;
    display: flex;
    justify-content: space-between;
    background: #d5dcf980;
    padding: 4px;
    span {
      line-height: 30px;
      color: $primary-blue;
    }
  }
  .contents {
    flex: 1;
    display: flex;
    gap: 8px;
    max-height: 245px;
    height: 245px;
    .contents-left {
      width: 70%;
    }
    .contents-rigt {
      width: calc(30% - 8px);
    }
  }
}

/* 拖动时的视觉反馈 */
.draggable-float-window:active .float-header {
  opacity: 0.7;
  cursor: grabbing;
}
</style>
