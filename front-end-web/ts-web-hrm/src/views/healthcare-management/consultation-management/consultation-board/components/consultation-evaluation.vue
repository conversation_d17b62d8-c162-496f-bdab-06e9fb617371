<template>
  <div class="consultation-opinion">
    <div class="content">
      <div class="content-item contract-contaiern">
        <div class="title">会诊质量评价分析</div>
        <div class="contract-echarts-module" ref="pie1"></div>
      </div>

      <div class="content-item contract-contaiern">
        <div class="title">
          <p>
            会诊医生-会诊质量评价
          </p>
          <div class="rightTitle">
            <ts-input
              style="width: 140px;"
              v-model="searchVal3"
              clearable
              :placeholder="placeholder[1]"
              @change="search(3)"
            />
          </div>
        </div>
        <TsVxeTemplateTable
          id="table_consultation-opinion"
          class="form-table"
          ref="table3"
          :columns="columns1"
          :hasPage="false"
          :scroll-y="{ enabled: true }"
          @refresh="handleRefreshTable(3)"
        />
      </div>

      <div class="content-item contract-contaiern">
        <div class="title">
          <p>
            会诊科室-会诊质量评价
          </p>
          <div class="rightTitle">
            <ts-input
              style="width: 140px;"
              v-model="searchVal4"
              clearable
              :placeholder="placeholder[2]"
              @change="search(4)"
            />
          </div>
        </div>
        <TsVxeTemplateTable
          id="table_consultation-opinion"
          class="form-table"
          ref="table4"
          :columns="columns2"
          :hasPage="false"
          :scroll-y="{ enabled: true }"
          @refresh="handleRefreshTable(4)"
        />
      </div>
    </div>
    <dialog-pei ref="dialogPei" />
  </div>
</template>

<script>
import dialogPei from './dialog-pei.vue';
export default {
  components: { dialogPei },
  data() {
    return {
      pie1: undefined,
      columns1: [
        {
          label: '归属科室',
          prop: 'name',
          width: 120,
          align: 'center'
        },
        {
          label: '会诊医生',
          prop: 'employee_name',
          minWidth: 80,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                style: 'color: #295cf9; cursor: pointer;',
                on: { click: () => this.handleDetails(row) }
              },
              row.employee_name
            );
          }
        },
        {
          label: '技术职称',
          prop: 'technical',
          minWidth: 80,
          align: 'center'
        },
        {
          label: '会诊质量分数',
          prop: 'avgScore',
          width: 100,
          align: 'center'
        },
        {
          label: '评价率',
          prop: 'pjRate',
          width: 80,
          align: 'center',
          render: (h, { row }) => {
            return h('span', {}, row.pjRate + '%');
          }
        }
      ],
      columns2: [
        {
          label: '被邀科室',
          prop: 'orgName',
          minWidth: 120,
          align: 'center',
          render: (h, { row }) => {
            return h(
              'span',
              {
                style: 'color: #295cf9; cursor: pointer;',
                on: { click: () => this.handleDetails(row) }
              },
              row.orgName
            );
          }
        },
        {
          label: '院区',
          prop: 'areaText',
          width: 80,
          align: 'center'
        },
        {
          label: '会诊质量分数',
          prop: 'avgScore',
          width: 100,
          align: 'center'
        },
        {
          label: '评价率',
          prop: 'pjRate',
          width: 80,
          align: 'center',
          render: (h, { row }) => {
            return h('span', {}, row.pjRate + '%');
          }
        }
      ],
      searchVal3: '',
      searchVal4: '',
      date: [],
      opinionIndex: 1,
      placeholder: {
        1: '请输入姓名检索',
        2: '请输入科室名称搜索'
      }
    };
  },
  methods: {
    async render(date, opinionIndex = 1) {
      this.date = date;
      this.opinionIndex = opinionIndex;
      // 调用接口查询
      let param = {
        startDate: date[0],
        endDate: date[1],
        index: 2
      };
      let res = await this.ajax.scduDetlHzjgfxList(param);
      this.renderDoctorEcharts(res.object || {});
      this.tableTeansfer(this.opinionIndex);
    },
    handleDetails(data) {
      this.$refs.dialogPei.open({ data, index: 2, date: this.date });
    },
    //会诊医生-会诊结果分析
    renderDoctorEcharts(data) {
      if (data.length == 0) return;
      data = data.map(item => {
        return {
          name: item.pjlx,
          legend: `${item.pjlx}--${item.pjCount}`,
          value: Number(item.pjCount)
        };
      });
      let total = data.reduce((acc, curr) => acc + (curr.value || 0), 0);
      let pre = ((data[0].value / total) * 100).toFixed(2);
      data[0].percent = pre;
      let options = {
        title: [
          {
            text: data[0].percent + '%',
            subtext: data[0].name,
            textStyle: {
              fontSize: 12,
              color: 'black'
            },
            subtextStyle: {
              fontSize: 12,
              color: 'black'
            },
            textAlign: 'center',
            x: '28%',
            y: '40%'
          }
        ],
        tooltip: {
          trigger: 'item',
          formatter: params => {
            this.pie1.setOption({
              title: {
                text: params.percent + '%',
                subtext: `${params.data.legend}`
              }
            });
            return `${params.data.legend}</br>${params.data.value}`;
          }
        },
        legend: {
          type: 'scroll',
          orient: 'vertical',
          left: '60%',
          top: '10%',
          align: 'left',
          textStyle: {
            color: '#8C8C8C',
            rich: {
              customStyle: {
                color: '#333',
                fontWeight: 'bold',
                fontSize: 12
              }
            }
          },
          icon: 'circle'
        },
        series: [
          {
            name: '标题',
            type: 'pie',
            center: ['30%', '45%'],
            radius: ['38%', '55%'],
            clockwise: false,
            avoidLabelOverlap: false,
            label: {
              show: false
            },
            labelLine: {
              smooth: false
            },
            data: data
          }
        ]
      };
      let _self = this;
      if (!_self.pie1) {
        _self.pie1 = _self.$echarts.init(_self.$refs.pie1);
      }
      _self.pie1.clear();
      _self.pie1.setOption(options);
    },
    tableTeansfer(index) {
      this.tableIndex = index;
      this.searchVal3 = '';
      this.searchVal4 = '';
      this.search(3);
      this.search(4);
    },
    search(index) {
      this.$nextTick(() => {
        this.$refs[`table${index}`].pageNo = 1;
        this.handleRefreshTable(index);
      });
    },
    async handleRefreshTable(index) {
      let searchForm = {
        searchKey: this[`searchVal${index}`],
        startDate: this.date[0],
        endDate: this.date[1],
        index
      };
      let res = await this.ajax.scduDetlZlpffxList(searchForm);
      if (res.success == false) {
        this.$newMessage('error', res.message || '列表数据获取失败');
        return;
      }
      let rows = res.object || [];
      this.$refs[`table${index}`].refresh({ rows });
    }
  }
};
</script>

<style lang="scss" scoped>
.consultation-opinion {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .content {
    flex: 1;
    display: flex;
    padding: 8px;
    overflow: hidden;
    box-sizing: border-box;

    .content-item {
      flex: 1;
      margin-right: 10px;
      overflow: hidden;
      &.flex2 {
        flex: 2;
      }
      &:last-child {
        margin-right: 0;
      }

      .title {
        font-size: 13px;
        font-weight: bold;
        color: #333333;
        padding-left: 8px;
        line-height: 28px;
        border: 1px solid #eee;
        // background-color: rgba(#12257c, 0.08);
        display: flex;
        justify-content: space-between;
        p {
          margin: 0;
          font-weight: bold;
          line-height: 30px;
        }
        .popverIcon {
          font-size: 18px;
          margin-left: 5px;
          position: relative;
          top: 2px;
        }
        .rightTitle {
          display: flex;
          .slotButton {
            margin: 2px 8px 2px 8px;
            line-height: 26px;
            height: 26px;
            border-radius: 4px;
            padding: 0 4px;
            background: #12257c14;
            cursor: pointer;
            &.active {
              border: 1px solid $primary-blue;
              color: $primary-blue;
            }
            &.first {
              margin-right: 0px !important;
            }
          }
        }
      }

      &.contract-contaiern {
        display: flex;
        flex-direction: column;

        .contract-echarts-module {
          flex: 1;
          overflow: hidden;
          border: 1px solid #eee;
          border-top: 0px;
        }
        .form-table {
          flex: 2;
          overflow: hidden;
          border: 1px solid #eee;
          border-top: 0px;
        }
      }
    }
  }
}
</style>
