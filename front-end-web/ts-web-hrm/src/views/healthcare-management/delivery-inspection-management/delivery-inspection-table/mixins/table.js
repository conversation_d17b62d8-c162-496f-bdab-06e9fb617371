export default {
  data() {
    return {
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'searchKey',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索住院号/门诊号/患者姓名'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '项目名称',
          value: 'projectName',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索项目名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '申请院区',
          value: 'applyArea',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索申请院区'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '申请医师',
          value: 'applyName',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索申请医师'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '申请日期',
          value: 'doDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { doDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = doDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          }
        },
        {
          label: '审批日期',
          value: 'wheelDate',
          element: 'base-date-range-picker',
          event: {
            change: () => {
              let { wheelDate = [] } = this.searchForm,
                [startDate = '', endDate = ''] = wheelDate;
              if (startDate && endDate) {
                this.search();
              }
            }
          }
        }
      ],
      columns: [
        {
          label: '序号',
          type: 'seq',
          align: 'center',
          width: 40
        },
        {
          label: '流程',
          prop: '',
          width: 40,
          align: 'center',
          render: (h, { row }) => {
            let imag = require('@/assets/img/from_process.svg');
            return h('div', { class: 'workName' }, [
              h('div', {
                class: 'process',
                style: `background-image: url(${imag});`,
                on: { click: () => this.handleProcess(row) }
              })
            ]);
          }
        },
        {
          label: '项目名称',
          prop: 'projectName',
          minWidth: 180,
          align: 'center'
        },
        {
          label: '申请医师',
          prop: 'applyName',
          width: 120,
          align: 'center'
        },
        {
          label: '联系电话',
          prop: 'applyTel',
          width: 120,
          align: 'center'
        },
        {
          label: '住院号/门诊号',
          prop: 'inPatientNo',
          width: 160,
          align: 'center'
        },
        {
          label: '患者姓名',
          prop: 'patientName',
          width: 120,
          align: 'center'
        },
        {
          label: '性别',
          prop: 'sex',
          width: 80,
          align: 'center'
        },
        {
          label: '年龄',
          prop: 'age',
          width: 80,
          align: 'center'
        },
        {
          label: '主要诊断',
          prop: 'diagnosis',
          minWidth: 180,
          align: 'center'
        },
        {
          label: '患者病情简介',
          prop: 'illness',
          minWidth: 180,
          align: 'center'
        },
        {
          label: '样本类型',
          prop: 'sampleType',
          width: 120,
          align: 'center'
        },
        {
          label: '样本量',
          prop: 'sampleDose',
          width: 100,
          align: 'center'
        },
        {
          label: '外送检验机构',
          prop: 'testOrg',
          width: 180,
          align: 'center'
        },
        {
          label: '费用',
          prop: 'cost',
          width: 120,
          align: 'center'
        },
        {
          label: '外送原因',
          prop: 'reason',
          width: 120,
          align: 'center'
        },
        {
          label: '项目内涵',
          prop: 'connotation',
          width: 140,
          align: 'center'
        },
        {
          label: '申请日期',
          prop: 'applyDate',
          width: 180,
          align: 'center'
        },
        {
          label: '审批通过日期',
          prop: 'createDate',
          width: 180,
          align: 'center'
        },
        {
          label: '申请科室',
          prop: 'applyOrg',
          width: 120,
          align: 'center'
        },
        {
          label: '申请院区',
          prop: 'applyArea',
          width: 120,
          align: 'center'
        }
      ]
    };
  },
  methods: {
    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'hosp_area'
      });
      this.searchList[1].childNodeList = res.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    },
    // 查看流程
    async handleProcess(row) {
      let res = await this.ajax.workflowById(row.workflowId);
      if (res.success) {
        let {
          workflowNo,
          businessId,
          wfInstanceId: workflowInstId,
          currentStepName
        } = res.object;
        let url = encodeURI(
          `/view-new/processView/see/index.html?workflowNo=${workflowNo}&businessId=${businessId}&currentStepName=${currentStepName}&wfInstanceId=${workflowInstId}&currentStepNo=end&role=deal&type=see`
        );
        window.open(url);
      }
    },
    getQueryParam() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize,
        { wheelDate = [], doDate = [] } = this.searchForm,
        [startDate = '', endDate = ''] = wheelDate,
        [applyDateStart = '', applyDateEnd = ''] = doDate,
        searchForm = {
          ...this.searchForm,
          pageNo,
          pageSize,
          orgIds: this.treeCode,
          startDate,
          endDate,
          applyDateStart,
          applyDateEnd,
          sidx: this.$refs.table.sidx,
          sord: this.$refs.table.sord
        };
      delete searchForm.wheelDate;
      delete searchForm.doDate;
      Object.keys(searchForm).map(key => {
        if (
          searchForm[key] === null ||
          searchForm[key] === undefined ||
          searchForm[key] === ''
        ) {
          delete searchForm[key];
        }
      });
      return { searchForm, pageSize, pageNo };
    }
  }
};
