export default {
  data() {
    return {
      doseType: [],
      searchForm: {},
      searchList: [
        {
          label: '',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索工号/姓名'
          }
        },
        {
          label: '剂量计类型',
          value: 'doseType',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择剂量计类型'
          },
          childNodeList: []
        },
        {
          label: '监测周期',
          value: 'monitoringPeriod',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择监测周期'
          },
          childNodeList: [
            {
              label: '季度',
              value: 1,
              element: 'ts-option'
            },
            {
              label: '年度',
              value: 2,
              element: 'ts-option'
            }
          ]
        },
        {
          label: '监测年份',
          value: 'monitoringYear',
          element: 'ts-input',
          elementProp: {
            placeholder: '搜索监测年份'
          }
        },
        {
          label: '监测季度',
          value: 'monitoringQuarter',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            {
              label: '第一季度',
              value: 1,
              element: 'ts-option'
            },
            {
              label: '第二季度',
              value: 2,
              element: 'ts-option'
            },
            {
              label: '第三季度',
              value: 3,
              element: 'ts-option'
            },
            {
              label: '第四季度',
              value: 4,
              element: 'ts-option'
            }
          ]
        }
      ],
      columns: [
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 40
        },
        {
          label: '剂量计类型',
          prop: 'doseTypeText',
          width: 100,
          align: 'center'
        },
        {
          label: '登记人员',
          prop: 'employeeName',
          width: 100,
          align: 'center'
        },
        {
          label: '科室名称',
          prop: 'orgName',
          width: 80,
          align: 'center'
        },
        {
          label: '监测周期',
          prop: 'monitoringPeriod',
          width: 200,
          align: 'center',
          render: (h, { row }) => {
            let label = row.monitoringPeriod == 1 ? '[季度] ' : '[年度]';
            return h('span', {}, `${label}${row.monitoringDate}`);
          }
        },
        {
          label: '辐射品质',
          prop: 'radiationQuality',
          width: 120,
          align: 'center'
        },
        {
          title: '光子辐射个人剂量当量',
          filed: '',
          align: 'center',
          children: [
            {
              title: 'H p(10)',
              field: 'personalDoseEquivalentHp10',
              key: 'personalDoseEquivalentHp10',
              align: 'center',
              width: 100
            },
            {
              title: 'H p(3)',
              field: 'personalDoseEquivalentHp3',
              key: 'personalDoseEquivalentHp3',
              align: 'center',
              width: 100
            },
            {
              title: 'H p(007)',
              field: 'personalDoseEquivalentHp007',
              key: 'personalDoseEquivalentHp007',
              align: 'center',
              width: 100
            }
          ]
        },
        {
          label: '状态',
          prop: 'monitorStatus',
          width: 120,
          align: 'center',
          render: (h, { row }) => {
            if (row.monitorStatus == '0') {
              return h('span', { style: 'color: red' }, '不合格');
            }
            return h('span', {}, '合格');
          }
        },
        {
          label: '注释',
          prop: 'remarks',
          width: 120,
          align: 'center'
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 120,
          headerSlots: 'action',
          fixed: 'right',
          render: (h, { row }) => {
            let actionList = [
              {
                label: '查看',
                event: this.handleDetail
              },
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                className: 'actionDel',
                event: this.handleDelete
              }
            ];
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ]
    };
  },
  methods: {
    // 获取所需的全部字典数据
    async getAllDictItemList() {
      let res1 = await this.ajax.getDictItemByTypeCode({
        typeCode: 'dose_type'
      });
      this.doseType = res1.object || [];
      this.searchList[1].childNodeList = res1.object.map(e => {
        return {
          label: e.itemName,
          value: e.itemCode,
          element: 'ts-option'
        };
      });
    }
  }
};
