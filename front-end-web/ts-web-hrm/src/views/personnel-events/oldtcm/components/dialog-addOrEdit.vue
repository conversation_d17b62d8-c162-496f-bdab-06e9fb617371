<template>
  <vxe-modal
    width="800"
    :title="title"
    v-model="visible"
    showFooter
    :before-hide-method="close"
    className="dialog-edit-form"
  >
    <template #default>
      <div class="content">
        <ts-form ref="form" :model="form" labelWidth="100px">
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                label="姓名"
                :rules="rules.required"
                prop="employeeId"
              >
                <base-select
                  v-if="api == 'save'"
                  v-model="form.employeeId"
                  :inputText.sync="form.employeeName"
                  :loadMethod="handleGetPersonList"
                  label="employeeName"
                  value="employeeId"
                  searchInputName="employeeName"
                  :clearable="false"
                  placeholder="请选择"
                  @select="handlePersonSelect"
                  style="width: 100%;"
                >
                  <template slot-scope="options">
                    <p
                      style="line-height: 36px;margin: 0;width: 100%;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;"
                      :title="
                        `${options.data['employeeNo']}--${options.data['orgName']}--${options.data['employeeName']}`
                      "
                    >
                      {{ options.data['employeeNo'] }}--{{
                        options.data['orgName']
                      }}--{{ options.data['employeeName'] }}
                    </p>
                  </template>
                </base-select>
                <ts-input v-else v-model="form.employeeName" disabled />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                prop="employeeNo"
                :rules="rules.required"
                label="工号"
              >
                <ts-input
                  v-model="form.employeeNo"
                  disabled
                  placeholder="请输入工号"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item prop="oldOrgName" label="部门">
                <ts-input
                  v-model="form.oldOrgName"
                  disabled
                  placeholder="请输入部门"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item prop="oldPositionName" label="职务">
                <ts-input
                  v-model="form.oldPositionName"
                  disabled
                  placeholder="请输入职务"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item prop="oldPersonalIdentityName" label="岗位名称">
                <ts-input
                  v-model="form.oldPersonalIdentityName"
                  disabled
                  placeholder="请输入岗位名称"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item prop="identityNumber" label="身份证号">
                <ts-input
                  v-model="form.identityNumber"
                  disabled
                  placeholder="请输入身份证号"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-row>
            <ts-col :span="12">
              <ts-form-item
                prop="yearDate"
                :rules="rules.required"
                label="年度"
              >
                <ts-input
                  v-model="form.yearDate"
                  :disabled="isDetail"
                  placeholder="请输入年度"
                />
              </ts-form-item>
            </ts-col>
            <ts-col :span="12">
              <ts-form-item
                prop="dsxm"
                :rules="rules.required"
                label="导师姓名"
              >
                <ts-input
                  v-model="form.dsxm"
                  :disabled="isDetail"
                  placeholder="请输入导师姓名"
                />
              </ts-form-item>
            </ts-col>
          </ts-row>
          <ts-form-item prop="remark" label="备注">
            <ts-input
              v-model="form.remark"
              type="textarea"
              :disabled="isDetail"
              placeholder="请输入备注"
              class="textarea"
              maxlength="200"
              show-word-limit
            />
          </ts-form-item>
        </ts-form>
      </div>
    </template>
    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button
          type="primary"
          :loading="submitLoading"
          @click="submit"
          v-if="!isDetail"
          >提 交</ts-button
        >
        <ts-button @click="close" class="shallowButton">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  data() {
    return {
      visible: false,
      submitLoading: false,
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      },
      title: '新增',
      api: '',
      isDetail: false
    };
  },
  methods: {
    open(data = null, type = 'edit') {
      if (data) {
        this.form = deepClone(data);
        this.api = 'update';
        this.title = '编辑';
      } else {
        this.api = 'save';
        this.title = '新增';
      }
      if (type == 'details') {
        this.isDetail = true;
        this.title = '详情';
      }
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
      this.visible = true;
    },
    async handleGetPersonList(data) {
      let res = await this.ajax.getBasicMyEmployeeList({
        employeeStatusList: ['1', '6', '12', '99', '9', '8', '5'],
        pageSize: 15,
        sidx: 'create_date',
        sord: 'desc',
        ...data
      });
      if (res.success == false) {
        this.$message.error(res.message || '人员数据获取失败');
        return false;
      }
      return res.rows;
    },
    handlePersonSelect(item) {
      this.$set(this.form, 'employeeNo', item.employeeNo);
      this.$set(this.form, 'oldOrgId', item.orgId);
      this.$set(this.form, 'oldOrgName', item.orgName);
      this.$set(this.form, 'oldPositionId', item.positionId);
      this.$set(this.form, 'oldPositionName', item.positionName);
      this.$set(this.form, 'identityNumber', item.identityNumber);
      this.$set(this.form, 'oldPersonalIdentity', item.personalIdentity);
      this.$set(
        this.form,
        'oldPersonalIdentityName',
        item.personalIdentityName
      );
    },
    async submit() {
      try {
        await this.$refs.form.validate();
        const data = deepClone(this.form);
        this.submitLoading = true;
        const res = await this.ajax.oldTcmSave(data, this.api);

        if (res.success && res.statusCode === 200) {
          this.submitLoading = false;
          this.$message.success('操作成功!');
          this.$emit('ok');
          this.close();
        } else {
          this.submitLoading = false;
          this.$message.error(res.message || '操作失败!');
        }
      } catch (error) {
        console.error(error);
      }
    },
    close() {
      this.visible = false;
      this.form = {};
      this.api = '';
      this.isDetail = false;
      this.$refs.form.clearValidate();
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-edit-form {
  ::v-deep {
    .textarea {
      .el-textarea__inner {
        min-height: 110px !important;
        max-height: 200px !important;
      }
    }
  }
}
</style>
