export default {
  data() {
    return {
      searchForm: {},

      actions: [],

      searchList: [
        {
          label: '关键字',
          value: 'employeeName',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入姓名/工号'
          }
        },
        {
          label: '科室名称',
          value: 'employeeNo',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入科室名称'
          }
        },
        {
          label: '审批状态',
          value: 'approvalStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true
          },
          childNodeList: [
            {
              label: '待审批',
              value: '1',
              element: 'ts-option'
            },
            {
              label: '已审批',
              value: '4',
              element: 'ts-option'
            }
          ]
        }
      ],
      columns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          type: 'seq',
          width: 40,
          align: 'center'
        },
        {
          label: '审批状态',
          prop: 'approvalStatusText',
          align: 'center',
          sortable: true,
          width: 100,
          sortBy: 'approval_status',
          render: (h, { row }) => {
            let color = '#333';
            let cellvalue = '已审批';
            if (row.approvalStatus != null && row.approvalStatus === '1') {
              color = '#DC851F';
              cellvalue = '待审批';
            }
            return h('span', { style: `color: ${color}` }, cellvalue);
          }
        },
        {
          label: '工号',
          prop: 'employeeNo',
          align: 'center',
          width: 80,
          sortable: true,
          sortBy: 'employee_no'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          width: 100,
          sortable: true,
          align: 'center',
          sortBy: 'employee_name',
          render: (h, { row }) => {
            return h(
              'div',
              {
                class: 'primary-span',
                on: { click: () => this.handleDetails(row) }
              },
              row.employeeName
            );
          }
        },
        {
          label: '科室',
          prop: 'orgName',
          width: 100,
          align: 'center',
          sortable: true
        },
        {
          label: '毕业学校',
          prop: 'schoolName',
          align: 'center',
          width: 120,
          sortable: true,
          sortBy: 'school_name'
        },
        {
          label: '专业',
          width: 120,
          prop: 'professional',
          align: 'center',
          sortable: true
        },
        {
          label: '学历',
          width: 120,
          prop: 'educationTypeText',
          align: 'center',
          sortable: true
        },
        {
          label: '学制',
          width: 120,
          prop: 'schoolSystemText',
          align: 'center',
          sortable: true
        },
        {
          label: '学习形式',
          width: 120,
          prop: 'learnWayText',
          align: 'center',
          sortable: true
        },
        {
          label: '开始时间',
          prop: 'startTime',
          width: 120,
          align: 'center',
          sortable: true,
          sortBy: 'start_time'
        },
        {
          label: '结束时间',
          prop: 'endTime',
          width: 120,
          align: 'center',
          sortable: true,
          sortBy: 'end_time'
        },
        {
          label: '最高学历',
          prop: 'highestLevel',
          width: 100,
          align: 'center',
          sortable: true,
          sortBy: 'highest_level',
          render: (h, { row }) => {
            let cellValue = row.highestLevel == 1 ? '是' : '否';
            return h('span', {}, cellValue);
          }
        },
        {
          label: '操作',
          align: 'center',
          prop: 'actions',
          width: 100,
          fixed: 'right',
          headerSlots: 'action',
          render: (h, { row }) => {
            let arr = [];
            if (row.approvalStatus == 1) {
              arr.push({
                label: '编辑',
                event: this.handleEdit,
                show: this.hasOperateBtn.includes('edit')
              });
              arr.push({
                label: '删除',
                event: this.handleDelete,
                className: 'actionDel',
                show: this.hasOperateBtn.includes('delete')
              });
            } else {
              arr.push({
                label: '查看',
                event: this.handleDetails,
                show: true
              });
            }
            arr = arr.filter(e => e.show);
            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: arr }
            });
          }
        }
      ]
    };
  },
  computed: {
    hasOperateBtn() {
      return this.menuLimits.map(m => m.resourceId);
    }
  }
};
