<template>
  <ts-dialog
    custom-class="dialog-calculation-rules"
    title="计薪规则"
    fullscreen
    :visible.sync="visible"
    :append-to-body="true"
    @close="close"
  >
    <el-scrollbar style="flex: 1;" wrap-style="overflow-x: hidden;">
      <div class="salary-group-calculation-rules">
        <div class="operate-btn">
          <h1>{{ title }}</h1>

          <div>
            <ts-button type="primary" @click="handleAddGroup">
              添加分组
            </ts-button>
            <ts-button
              type="primary"
              class="import-btn-width"
              @click="handleImportSalaryGroup"
            >
              从其他薪资组导入
            </ts-button>
          </div>
          <!-- <ts-button @click="handleSort">排序</ts-button> -->
        </div>

        <div class="table-container">
          <div
            class="table-item-container"
            v-for="(item, index) in salaryProgrammeGroup"
            :key="index"
          >
            <div class="top-title-operate">
              <div class="left">
                <span class="item-tips">
                  {{ item.itemGroup }}
                </span>

                <span class="rules-operate" @click="handleEditGroup(item)">
                  <img src="@/assets/img/pay/edit.svg" />
                  <span class="primary-text">编辑</span>
                </span>

                <span class="rules-operate" @click="handleDeleteGroup(item)">
                  <img src="@/assets/img/pay/delete.svg" />
                  <span class="red-text">删除</span>
                </span>
              </div>

              <div class="right">
                <ts-button @click="handleQuote(item)">
                  引用薪酬项目
                </ts-button>
                <ts-button
                  class="import-btn-width"
                  @click="handleCustomProject(item)"
                >
                  自定义薪酬项目
                </ts-button>
              </div>
            </div>

            <!-- <base-table
              :ref="`table${index}`"
              class="form-table"
              border
              stripe
              :hasPage="false"
              :columns="columns"
            /> -->
            <TsVxeTemplateTable
              :id="'table_calculation_rules' + index"
              class="form-table"
              :ref="`table${index}`"
              :min-height="30"
              :hasPage="false"
              :columns="columns"
            />
          </div>
        </div>
      </div>
    </el-scrollbar>

    <span slot="footer" class="dialog-footer">
      <ts-button @click="close">关 闭</ts-button>
    </span>

    <dialog-add-edit-group
      ref="DialogAddEditGroup"
      @addSuccess="refreshGroupList"
    />

    <dialog-import-group
      ref="DialogImportGroup"
      @addSuccess="refreshGroupList"
    />

    <dialog-custom-project
      ref="DialogCustomProject"
      @addSuccess="refreshGroupList"
    />

    <salary-item-quote-dialog
      ref="SalaryItemQuoteDialog"
      @submit="handleSubmitQuote"
    />

    <dialog-change-salary-group
      ref="DialogChangeSalaryGroup"
      @addSuccess="refreshGroupList"
    />

    <dialog-sort-salary-item
      ref="DialogSortSalaryItem"
      @addSuccess="refreshGroupList"
    />
  </ts-dialog>
</template>

<script>
import SalaryItemQuoteDialog from '@/views/pay-manager/components/salary-item-quote-dialog.vue';

import DialogAddEditGroup from './dialog-add-edit-group.vue';
import DialogCustomProject from './dialog-custom-project.vue';
import DialogImportGroup from './dialog-import-group.vue';

import DialogChangeSalaryGroup from './dialog-change-salary-group.vue';
import DialogSortSalaryItem from './dialog-sort-salary-item.vue';
import { deepClone } from '@/unit/commonHandle.js';
import Sortable from 'sortablejs';

export default {
  components: {
    SalaryItemQuoteDialog,
    DialogAddEditGroup,
    DialogChangeSalaryGroup,
    DialogCustomProject,
    DialogImportGroup,
    DialogSortSalaryItem
  },
  data() {
    return {
      columns: [
        {
          label: '薪酬项目名称',
          prop: 'itemName',
          width: 220,
          align: 'center',
          fixed: 'left'
        },
        {
          label: '规则',
          prop: 'countFormulaText',
          align: 'center',
          render: (h, { row }) => {
            let dir = {
              1: '手工录入',
              2: '固定值',
              3: '关联系统数据',
              4: row['countFormulaText'],
              5: '自定义规则'
            };
            return h('div', dir[row.itemRule]);
          }
        },
        {
          label: '加减项',
          prop: 'countType',
          align: 'center',
          width: 90,
          render: (h, { row }) => {
            let dic = {
              '1': '加项',
              '2': '减项',
              '3': '不参与计算'
            };
            return h('div', dic[row.countType || '3']);
          }
        },
        {
          label: '备注',
          width: 140,
          prop: 'remark',
          align: 'center'
        },
        {
          label: '状态',
          prop: 'isEnable',
          width: 85,
          align: 'center',
          render: (h, { row }) => {
            let isEnable = row.isEnable == '2' ? 'red' : '';
            let isEnableLabel = row.isEnable == '2' ? '停用' : '启用';
            return h('div', { class: isEnable }, isEnableLabel);
          }
        },
        {
          label: '操作',
          align: 'center',
          width: 200,
          headerSlots: 'action',
          render: (h, { row }) => {
            let changeStatusLabel = row.isEnable == '1' ? '停用' : '启用';
            let operateType = row.isEnable == '1' ? '2' : '1';

            let actionList = [
              {
                label: '编辑',
                event: this.handleEditSalaryGroupItem
              },
              {
                label: changeStatusLabel,
                event: () => {
                  this.handleChangeStatus(row.uid, operateType);
                }
              },
              {
                label: '修改分组',
                event: this.handleChangeSalaryGroup
              }
            ];

            row.isEnable == '2' &&
              actionList.push({
                label: '移除',
                className: 'red',
                event: this.handleDeleteSalaryGroupItem
              });

            return h('BaseActionCell', {
              on: { 'action-select': event => event(row) },
              attrs: { actions: actionList }
            });
          }
        }
      ],
      visible: false,
      title: '',
      salaryProgrammeGroup: [],

      optionId: undefined,
      form: {}
    };
  },
  methods: {
    async open({ data, title }) {
      this.optionId = data.optionId;
      this.title = title;
      await this.handleGetCalculationGroupList();
      this.visible = true;
      this.handleRefreshGroupTable();
    },

    async handleGetCalculationGroupList() {
      this.salaryProgrammeGroup = [];
      const res = await this.ajax.salaryItemGroupList({
        optionId: this.optionId
      });
      if (!res.success) {
        this.$message.error(res.message || '获取分组失败!');
        return;
      }
      this.salaryProgrammeGroup = deepClone(res.object);
    },

    handleRefreshGroupTable() {
      this.salaryProgrammeGroup.forEach((f, i) => {
        this.$nextTick(() => {
          let refName = `table${i}`;
          this.$refs[refName][0].refresh(f.salaryItem);

          let table = document.getElementsByClassName('table-item-container');
          let tableBody = table[i].querySelector('tbody');

          Sortable.create(tableBody, {
            animation: 150,
            onEnd: async ({ newIndex, oldIndex }) => {
              const currRow = f.salaryItem.splice(oldIndex, 1)[0];
              f.salaryItem.splice(newIndex, 0, currRow);

              let salaryItem = deepClone(f.salaryItem) || [];
              let data = salaryItem.map((m, i) => {
                return {
                  uid: m.uid,
                  sortNo: i
                };
              });
              const res = await this.ajax.salaryItemSortNo(data);
              if (res.success) {
                this.salaryProgrammeGroup = [];
                this.refreshGroupList();
              } else {
                this.$message.error(res.message || '操作失败!');
              }
            }
          });
        });
      });
    },

    async refreshGroupList() {
      await this.handleGetCalculationGroupList();
      this.handleRefreshGroupTable();
    },

    handleSort() {
      this.$refs.DialogSortSalaryItem.open({
        data: deepClone(this.salaryProgrammeGroup)
      });
    },

    handleAddGroup() {
      this.$refs.DialogAddEditGroup.open({
        title: '新增分组',
        type: 'add',
        data: {},
        optionId: this.optionId
      });
    },

    handleEditGroup(data) {
      this.$refs.DialogAddEditGroup.open({
        title: '编辑分组',
        type: 'edit',
        data: deepClone(data),
        optionId: this.optionId
      });
    },

    async handleDeleteGroup({ id }) {
      try {
        await this.$confirm('是否删除该条数据?', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.salaryItemGroupDelete(id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.refreshGroupList();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleDeleteSalaryGroupItem({ uid, id }) {
      try {
        await this.$confirm('是否删除该条数据?', '提示', {
          type: 'warning'
        });
        const res = await this.ajax.salaryItemDeletedById(uid, id);
        if (res.success && res.statusCode === 200) {
          this.$message.success('操作成功!');
          this.refreshGroupList();
        } else {
          this.$message.error(res.message || '操作失败!');
        }
      } catch (e) {
        console.error(e);
      }
    },

    async handleChangeStatus(uid, enable) {
      let dic = {
          '1': '启用',
          '2': '停用'
        },
        title = dic[enable];
      try {
        await this.$confirm(`您正在做${title}操作，是否确定？`, '提示', {
          type: 'warning'
        });

        const res = await this.ajax.salaryItemEnable(uid, enable);
        if (!res.success) {
          this.$message.error(res.message || '操作失败!');
          return;
        }
        this.$message.success('操作成功!');
        this.refreshGroupList();
      } catch (e) {
        console.error(e, '?e');
      }
    },

    handleChangeSalaryGroup(data) {
      this.$refs.DialogChangeSalaryGroup.open({
        data,
        optionId: this.optionId
      });
    },

    handleCustomProject({ id: groupId }) {
      this.$refs.DialogCustomProject.open({
        title: '新增自定义工资项',
        type: 'add',
        data: {
          groupId,
          optionId: this.optionId
        }
      });
    },

    handleEditSalaryGroupItem(data) {
      this.$refs.DialogCustomProject.open({
        title: '编辑自定义工资项',
        type: 'edit',
        data
      });
    },

    // 从其他方案导入 计薪规则 分组
    handleImportSalaryGroup() {
      this.$refs.DialogImportGroup.open({
        optionId: this.optionId
      });
    },

    // 分组导入工资项
    handleImportSalaryItem(data) {
      this.$refs.DialogImportSalaryItem.open({
        groupId: data.id
      });
    },

    async handleQuote({ id: groupId }) {
      const res = await this.ajax.salaryItemGetItemLibraryData();
      if (!res.success) {
        this.$message.error(res.message || '获取方案失败!');
        return;
      }
      let data = deepClone(res.object);

      this.$refs.SalaryItemQuoteDialog.open({
        data,
        title: '引用薪酬项目',
        groupId
      });
    },

    async handleSubmitQuote(arr, groupId) {
      const res = await this.ajax.salaryItemGroupImportItem(groupId, arr);
      if (!res.success) {
        this.$message.error(res.message || '操作失败!');
        return;
      }
      this.$message.success('操作成功!');
      this.refreshGroupList();
    },

    close() {
      this.salaryProgrammeGroup = [];
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .dialog-calculation-rules {
    .el-dialog__footer {
      width: 1310px !important;
    }

    .el-dialog__body {
      width: 1310px !important;
      height: calc(100% - 80px);
      display: flex;
      overflow: hidden;

      .red {
        color: red;
      }

      .salary-group-calculation-rules {
        height: 100%;
        .operate-btn {
          display: flex;
          justify-content: space-between;
        }

        .import-btn-width {
          span {
            max-width: 180px !important;
          }
        }

        .table-item-container {
          padding: 8px 0;

          .top-title-operate {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;

            .rules-operate {
              margin-right: 12px;
              cursor: pointer;
              img {
                width: 20px;
                height: 20px;
                font-weight: 700;
                cursor: pointer;
                transform: translateY(-1px);
              }

              .primary-text {
                color: #4959f9;
              }

              .red-text,
              .red {
                color: #d2180e;
              }
            }

            .left {
              display: flex;
              align-items: center;
            }
          }

          .item-tips {
            font-size: 16px;
            font-weight: 700;
            margin-right: 8px;

            &::before {
              content: '1';
              color: rgb(82, 96, 255);
              height: 16px;
              width: 4px;
              border-radius: 4px;
              background-color: rgb(82, 96, 255);
            }
          }
        }
      }
    }
  }
}
</style>
