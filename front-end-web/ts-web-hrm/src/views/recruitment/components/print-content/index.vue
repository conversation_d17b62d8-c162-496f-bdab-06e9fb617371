<template>
  <div ref="pdfContent" class="pdf-content">
    <table class="noborder">
      <thead class="show-none">
        <tr class="noborder">
          <th class="noborder">
            <div class="logo">
              <img :src="require('@/assets/img/print_pdf_logo.png')" />
            </div>
          </th>
        </tr>
      </thead>

      <tr class="noborder">
        <th class="noborder">
          <!-- <div class="title hospital-name show-none">长沙经开医院有限公司</div> -->
          <h2 class="title table-name show-none">应聘人员信息登记表</h2>
          <div class="time-post-info">
            <p>
              <span class="title no-weight">应聘岗位： </span>
              <span class="title no-weight value">{{
                printData.interviewPath
              }}</span>
            </p>
            <p>
              <span class="title no-weight">人员类别： </span>
              <span class="title no-weight value">{{
                printData.personnelCategory
              }}</span>
            </p>
            <p>
              <span class="title no-weight">填表日期： </span>
              <span class="title no-weight value">{{
                printData.createDate
              }}</span>
            </p>
          </div>
        </th>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table style="border-top: 1px solid #333 !important;">
              <tr>
                <td class="font-th" :colspan="7">基本信息</td>
              </tr>
              <tr>
                <th style="width: 14%;">姓名</th>
                <td style="width: 15%;">{{ printData.employeeName }}</td>
                <th style="width: 14%;">性别</th>
                <td style="width: 14%;">{{ printData.gender }}</td>
                <th style="width: 15%;">年龄</th>
                <td style="width: 15%;">{{ printData.age }}</td>
                <td style="width: 15%;" rowspan="5">
                  <img
                    style="width: 168px;height: 180px;display: inline-block;"
                    :src="printData.avatarPath"
                  />
                </td>
              </tr>
              <tr>
                <th>政治面貌</th>
                <td>{{ printData.politicalStatus }}</td>
                <th>身高/体重</th>
                <td>{{ printData.shenggao }} / {{ printData.tizhong }}</td>
                <th>民族</th>
                <td>{{ printData.nationality }}</td>
              </tr>
              <tr>
                <th>外语等级</th>
                <td>{{ printData.waiyudengji }}</td>
                <th>是否有驾照</th>
                <td>{{ printData.drivingLicence }}</td>
                <th>目前职业状态</th>
                <td>{{ printData.zhiyezhuangtai }}</td>
                <!-- <th>驾驶证级别</th>
            <td>{{ printData.drivingLicenceLevel }}</td> -->
              </tr>
              <tr>
                <th>参加工作时间</th>
                <td>{{ printData.entryDate }}</td>
                <th>期望月薪</th>
                <td>{{ printData.qiwangyuexin }}</td>
                <th>最快入职日期</th>
                <td>{{ printData.zuikuairuzhi }}</td>
              </tr>
              <tr>
                <th>婚育状况</th>
                <td colspan="2">{{ printData.marriageStatus }}</td>
                <th>籍贯</th>
                <td colspan="2">{{ printData.birthplace }}</td>
              </tr>
              <tr>
                <th>户口所在地</th>
                <td colspan="3">
                  {{ printData.hukousuozaidi }}
                </td>
                <th>户籍类型</th>
                <td colspan="2">{{ printData.hujileixing }}</td>
              </tr>
              <tr>
                <th>通信地址</th>
                <td colspan="3">{{ printData.address }}</td>
                <th>身份证号码</th>
                <td colspan="2">{{ printData.identityNumber }}</td>
              </tr>
              <tr>
                <th>联系方式</th>
                <td colspan="6">
                  <div class="contact-item">
                    <span>手机：{{ printData.iphone }}</span>
                    <span>Email：{{ printData.email }}</span>
                    <span
                      >紧急联络人及电话：{{ printData.jinjilianxiren }} /
                      {{ printData.renxirendianhua }}</span
                    >
                  </div>
                </td>
              </tr>
              <tr>
                <th colspan="2">个人专长/业余爱好</th>
                <td colspan="5">
                  <div class="text-left text-area-height">
                    {{ printData.gerenzhuanchang }}
                  </div>
                </td>
              </tr>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">
                    学 习 经 历（从最高学历填起）
                  </td>
                </tr>
                <tr>
                  <th style="width: 24%;">起止时间</th>
                  <th style="width: 25%;">毕业院校</th>
                  <th style="width: 25%;">专业</th>
                  <th style="width: 12%;">学历</th>
                  <th style="width: 14%;">是否全日制</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in printData.hrmsEducationInfo" :key="item.id">
                  <td>{{ item.qizhishijian }}</td>
                  <td>{{ item.biyeyuanxiao }}</td>
                  <td>{{ item.zhuanye }}</td>
                  <td>{{ item.xueli }}</td>
                  <td>{{ item.fullTime }}</td>
                </tr>
                <tr>
                  <td class="font-th" :colspan="2">在校期间获奖情況</td>
                  <td :colspan="4">
                    <div class="text-left text-area-height">
                      {{ printData.zaixiaohuojiang }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">
                    工 作 经 历（从最近经历填起）
                  </td>
                </tr>
                <tr>
                  <th style="width: 23%;">起止时间</th>
                  <th style="width: 15%;">单位名称</th>
                  <th style="width: 15%;">科室/职务</th>
                  <th style="width: 10%;">薪资</th>
                  <th style="width: 19%;">证明人及电话</th>
                  <th style="width: 18%;">离职原因</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in printData.hrmsZpglWorkrecord" :key="item.id">
                  <td>{{ item.qizhishijian }}</td>
                  <td>{{ item.danweimingcheng }}</td>
                  <td>{{ (item.keshi || '') + '/' + (item.zhiwu || '') }}</td>
                  <td>{{ item.xinzi }}</td>
                  <td>{{ item.zhengmingren }}</td>
                  <td>{{ item.lizhiyuanyingList }}</td>
                </tr>
                <tr>
                  <td class="font-th" :colspan="6">
                    自我评价及业务擅长
                  </td>
                </tr>
                <tr>
                  <td :colspan="6">
                    <div class="text-left text-area-height">
                      {{ printData.ziwopingjia }}
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">学术团体任职情况</td>
                </tr>
                <tr>
                  <th :colspan="2" style="width: 23%;">起止时间</th>
                  <th style="width: 31%;" :colspan="2">
                    学术团体或专业杂志名称
                  </th>
                  <th style="width: 31%;">职务</th>
                  <th style="width: 15%;">证明人</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in printData.hrmsZpglAcademy" :key="item.id">
                  <td :colspan="2">{{ item.qizhishijian }}</td>
                  <td :colspan="2">{{ item.xueshituanti }}</td>
                  <td>{{ item.zhiwu }}</td>
                  <td>{{ item.zhengmingren }}</td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">进修培训经历</td>
                </tr>
                <tr>
                  <th :colspan="2" style="width: 23%;">起止时间</th>
                  <th>培训项目</th>
                  <th>培训机构/授课老师</th>
                  <th>取得职业资格或认证</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in printData.hrmsZpglContinueLearning"
                  :key="item.id"
                >
                  <td :colspan="2">{{ item.qizhishijian }}</td>
                  <td>{{ item.peixunxiangmu }}</td>
                  <td>{{ item.peixunjigou }}</td>
                  <td>{{ item.zhiyezige }}</td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">职称晋升（由高到低）</td>
                </tr>
                <tr>
                  <th :colspan="2">职称</th>
                  <th>职称的专业</th>
                  <th style="width: 15%;">取得时间</th>
                  <th :colspan="2">职称取得地点</th>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in printData.hrmsZpglProfessional"
                  :key="item.id"
                >
                  <td :colspan="2">{{ item.zhichengmingcheng }}</td>
                  <td>{{ item.zhuenye }}</td>
                  <td>{{ item.qudeshijian }}</td>
                  <td :colspan="2">{{ item.qudedidian }}</td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table>
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">家庭主要成员</td>
                </tr>
                <tr>
                  <th style="width: 13%;">关系</th>
                  <th style="width: 13%;">姓名</th>
                  <th style="width: 15%;">出生日期</th>
                  <th :colspan="2">工作单位</th>
                  <th style="width: 18%;">联系方式</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="item in printData.hrmsZpglFamily" :key="item.id">
                  <td>{{ item.guanxi }}</td>
                  <td>{{ item.xingming }}</td>
                  <td>{{ item.chushengriqi }}</td>
                  <td :colspan="2">{{ item.gongzuodanwei }}</td>
                  <td>{{ item.phone }}</td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>

      <tr class="noborder">
        <td class="noborder">
          <template>
            <table style="border-bottom: 1px solid #333;">
              <tr>
                <td :colspan="6">
                  <div class="tips-item">
                    <span class="item-label">你是否有亲友在汇一集团工作？</span>
                    <span class="item-result">{{ printData.isHhyongzuo }}</span>
                    <span class="item-value">{{
                      printData.isHhyongzuo === '是'
                        ? `(姓名和关系:${printData.isHhyongzuotext})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="6" style="text-align: left;">
                  <div class="tips-item">
                    <span class="item-label">你是否有重大疾病/手术记录？</span>
                    <span class="item-result">{{ printData.isShoushu }}</span>
                    <span class="item-value">{{
                      printData.isShoushu === '是'
                        ? `(请说明:${printData.isShoushutext})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="6">
                  <div class="tips-item">
                    <span class="item-label"
                      >你是否有可能影响你完成所申请工作的健康缺陷或其他缺陷？</span
                    >
                    <span class="item-result">{{ printData.isQuexian }}</span>
                    <span class="item-value">{{
                      printData.isQuexian === '是'
                        ? `(请说明:${printData.isQuexiantext})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="6">
                  <div class="tips-item">
                    <span class="item-label"
                      >你是否受到其它单位记过、察看、开除或其他严重处分？</span
                    >
                    <span class="item-result">{{ printData.isChufeng }}</span>
                    <span class="item-value">{{
                      printData.isChufeng === '是'
                        ? `(请说明:${printData.isChufengtext})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="6">
                  <div class="tips-item">
                    <span class="item-label"
                      >你是否曾因触犯法律受到刑事处罚或治安处罚？</span
                    >
                    <span class="item-result">{{ printData.isWeifa }}</span>
                    <span class="item-value">{{
                      printData.isWeifa === '是'
                        ? `(请说明:${printData.isWeifatext})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="6">
                  <div class="tips-item">
                    <span class="item-label">你从何处得知本招聘信息？</span>
                    <span class="item-result">{{
                      printData.jianlilaiyuan
                    }}</span>
                    <span class="item-value">{{
                      printData.jianlilaiyuan === '内部推荐'
                        ? `(请说明:${printData.referrerName})`
                        : ''
                    }}</span>
                  </div>
                </td>
              </tr>
              <tr>
                <td :colspan="2" class="text-left">
                  是否接受公司以考察你个人素质为目的相关测试（结果保密）？
                </td>
                <td style="width: 10%;">{{ printData.isXiangguanceshi }}</td>
                <td :colspan="2" class="text-left">
                  是否接受工作调配？
                </td>
                <td style="width: 10%;">{{ printData.isGongzuodaodong }}</td>
              </tr>
            </table>

            <table class="show-none">
              <thead>
                <tr>
                  <td class="font-th" :colspan="6">
                    应 聘 者 声 明
                  </td>
                </tr>
              </thead>
              <tbody>
                <tr class="noborder">
                  <td class="noborder text-left">
                    1、本人确保所提供的任何信息和资料《简历、资质证明、证件及复印件、电话等》均直实无误，若有任何虚假之处，本人愿无条件接受公司的处理。
                  </td>
                </tr>
                <tr class="noborder">
                  <td class="noborder text-left">
                    2、本人接受公司对我的背景调查。
                  </td>
                </tr>
                <tr class="noborder">
                  <td class="text-r">
                    <span class="item-label">本人签章: </span>
                    <span class="item-result">
                      <img
                        style="width: 110px;height: 45px;"
                        :src="printData.signatureImgPath"
                      />
                    </span>
                    <span class="item-label">日期: </span>
                    <span style="margin-right: 8px;" class="item-result">
                      {{ printData.createDate }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </template>
        </td>
      </tr>
    </table>
  </div>
</template>

<script>
import print from '@/unit/print.js';
export default {
  props: {
    printData: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    print() {
      print(this.$refs.pdfContent);
    }
  }
};
</script>

<style lang="scss">
.pdf-content {
  font-family: Times New Roman !important;

  .noborder {
    border: 0px !important;
  }

  .show-none {
    display: none;
  }

  thead {
    //display: none;
    border-bottom: 1px solid #333;
  }

  .hospital-name {
    font-size: 20px;
    margin-bottom: 8px;
  }

  .table-name {
    font-size: 18px;
  }

  .time-post-info {
    display: flex;
    justify-content: space-around;
    font-size: 16px;
    p {
      margin-bottom: 4px;
      .value {
        border-bottom: 1px solid #333;
      }
    }
  }

  .title {
    color: #000;
    font-weight: 700;
    text-align: center;
    &.no-weight {
      font-weight: 400;
    }
  }

  table {
    width: 100% !important;
    border: 1px solid #333;
    border-top: 0px !important;

    font-weight: 400 !important;
    font-family: Times New Roman !important;
    page-break-inside: avoid;

    thead {
      width: 100%;

      .logo {
        width: 100%;
        height: 39px;
        overflow: hidden;
        img {
          width: 100%;
          height: 40px;
        }
      }
    }

    tbody {
      // border-bottom: 0px !important;
    }

    tr {
      height: 28px;
      border-bottom: 1px solid #333;
    }

    th {
      padding: 2px;
      font-weight: normal;
      text-align: center;
      font-family: Times New Roman !important;
      border-right: 1px solid #333;
    }

    td {
      text-align: center;
      font-family: Times New Roman !important;
      border-right: 1px solid #333;

      .contact-item {
        display: flex;
        justify-content: space-around;
        span {
          font-family: Times New Roman !important;
        }
      }
    }

    .text-area-height {
      padding: 2px;
      min-height: 70px;
    }

    .font-th {
      font-weight: 500;
    }

    .text-left {
      text-align: left;
      padding-left: 8px;
      font-family: Times New Roman !important;
    }

    .text-r {
      text-align: right;
    }

    .tips-item {
      display: flex;
      span {
        padding: 0 8px;
        text-align: left;
        font-family: Times New Roman !important;
      }
      .item-label {
        flex-shrink: 0;
        white-space: nowrap;
      }
      .item-result {
        flex-shrink: 0;
        min-width: 30px;
      }
      .item-value {
        flex-wrap: wrap;
        flex-shrink: 1;
      }
    }
  }
}
@media print {
  thead {
    display: table-header-group !important;
  }

  .show-none {
    display: block !important;
  }
}
</style>
