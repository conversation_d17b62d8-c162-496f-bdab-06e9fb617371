import { deepClone } from '@/unit/commonHandle.js';
import moment from 'moment';
export default {
  data() {
    return {
      actions: [],
      searchForm: {
        year: moment().format('YYYY'),
        employeeStatus: ['1', '6', '12', '99', '9']
      },
      resetData: {
        year: moment().format('YYYY-MM'),
        employeeStatus: ['1', '6', '12', '99', '9']
      },
      searchList: [
        {
          label: '姓名/工号',
          value: 'employeeName',
          element: 'TsInput',
          elementProp: {
            clearable: true,
            placeholder: '请输入姓名/工号'
          }
        },
        {
          label: '年份',
          value: 'year'
        },
        {
          label: '员工状态',
          value: 'employeeStatus',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            multiple: true,
            placeholder: '请选择员工状态'
          },
          childNodeList: []
        },
        {
          label: '是否已折现',
          value: 'isConvertWage',
          element: 'ts-select',
          elementProp: {
            clearable: true,
            placeholder: '请选择'
          },
          childNodeList: [
            { label: '全部', value: '', element: 'ts-option' },
            { label: '是', value: '1', element: 'ts-option' },
            { label: '否', value: '0', element: 'ts-option' }
          ]
        }
      ],
      columns: [
        {
          type: 'selection',
          width: 40,
          selectable: row => {
            return row.isConvertWage != '1';
          }
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center'
        },
        {
          label: '工号',
          prop: 'employeeNo',
          width: 130,
          align: 'center',
          sortable: true,
          sortBy: 'b.employee_no'
        },
        {
          label: '姓名',
          prop: 'employeeName',
          width: 120,
          align: 'center',
          sortable: true,
          sortBy: 'b.employee_name'
        },
        {
          label: '身份证号',
          prop: 'identityNumber',
          width: 180,
          align: 'center'
        },
        {
          label: '科室名称',
          prop: 'orgName',
          width: 160,
          align: 'center'
        },
        {
          label: '员工状态',
          prop: 'employeeStatus',
          align: 'center',
          width: 90,
          sortable: true,
          sortBy: 'b.employee_status',
          formatter: row => {
            return <div>{row.employeeStatusName}</div>;
          }
        },
        {
          label: '工龄开始时间',
          prop: 'glkssj',
          width: 120,
          align: 'center',
          sortable: true,
          sortBy: 'glkssj'
        },
        {
          label: '年份',
          prop: 'year',
          width: 80,
          align: 'center'
        },
        {
          label: '本年可休年假',
          prop: 'availableAnnualLeaveDays',
          columnSlots: 'availableAnnualLeaveDays',
          width: 120,
          align: 'center',
          sortable: true,
          sortBy: 'available_annual_leave_days',
          headerSlots: 'availableAnnualLeaveDaysHeader'
        },
        {
          label: '上年结转年假',
          prop: 'remainingAnnualLeaveDays',
          width: 150,
          align: 'center',
          sortable: true,
          sortBy: 'remaining_annual_leave_days',
          headerSlots: 'remainingAnnualLeaveDaysHeader'
        },
        {
          label: '剩余年假',
          prop: 'remainingDays',
          width: 100,
          align: 'center',
          headerSlots: 'remainingDaysHeader'
        },
        {
          label: '已休年假',
          prop: 'useAnnualLeaveDays',
          columnSlots: 'useAnnualLeaveDays',
          width: 100,
          align: 'center',
          sortable: true,
          sortBy: 'use_annual_leave_days'
        },
        {
          label: '上年失效年假',
          prop: 'lapsedAnnualLeaveDays',
          width: 100,
          align: 'center',
          sortable: true,
          sortBy: 'lapsed_annual_leave_days'
        },
        {
          label: '失效年假已折现',
          prop: 'isConvertWage',
          width: 120,
          align: 'center',
          formatter: row => {
            let text = row.isConvertWage == '1' ? '是' : '否';
            return <div>{text}</div>;
          }
        }
      ]
    };
  },
  computed: {
    hasOperateBtn() {
      return this.menuLimits.map(m => m.resourceId);
    }
  },
  methods: {
    //检索
    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },
    handleSelectChange(e) {
      this.selectList = e;
    },
    async handleRefreshTable() {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let formData = {
        ...this.searchForm,
        ...this.org,
        pageNo,
        pageSize,
        sidx: this.$refs.table.sidx,
        sord: this.$refs.table.sord
      };
      if (
        this.searchForm.employeeStatus &&
        this.searchForm.employeeStatus.length > 0
      ) {
        let employeeStatuses = this.searchForm.employeeStatus.join(',');
        formData['employeeStatuses'] = employeeStatuses;
        delete formData['employeeStatus'];
      }
      Object.keys(formData).map(key => {
        if (
          formData[key] == null ||
          formData[key] == undefined ||
          formData[key] == ''
        ) {
          delete formData[key];
        }
      });
      let res = await this.ajax.getEmployeeAnnualLeaveData(formData);
      if (res.success == false) {
        this.$message.error(res.message || '列表数据获取失败');
        return;
      }
      let rows = res.rows.map((item, i) => {
        let index = (pageNo - 1) * pageSize + i + 1;
        return {
          index,
          ...item
        };
      });
      this.isBatchAdjust = false;
      this.oldData = deepClone(rows);
      this.data = rows;
      this.$refs.table.refresh({
        ...res,
        rows
      });
    }
  }
};
