import { Base64 } from 'js-base64';

export const commonUtils = {
  digit: function(t, e) {
    var i = '';
    (t = String(t)), (e = e || 2);
    for (var a = t.length; a < e; a++) i += '0';
    return t < Math.pow(10, e) ? i + (0 | t) : t;
  },
  toDateString: function(t, e) {
    var i = this,
      a = new Date(t || new Date()),
      n = [
        i.digit(a.getFullYear(), 4),
        i.digit(a.getMonth() + 1),
        i.digit(a.getDate())
      ],
      r = [
        i.digit(a.getHours()),
        i.digit(a.getMinutes()),
        i.digit(a.getSeconds())
      ];
    return (
      (e = e || 'yyyy-MM-dd HH:mm:ss'),
      e
        .replace(/yyyy/g, n[0])
        .replace(/MM/g, n[1])
        .replace(/dd/g, n[2])
        .replace(/HH/g, r[0])
        .replace(/mm/g, r[1])
        .replace(/ss/g, r[2])
    );
  },
  //实现时间向上取整(分钟)
  roundTime: function(time, num = 5) {
    let step = num * 60 * 1000;
    let date = new Date(time);
    return new Date(Math.ceil(date / step) * step);
  },
  /**
   * 检查数组长度是否大于一
   * @param obj
   * @returns {*}
   */
  arrayLength: function(text) {
    if (
      Object.prototype.toString.call(text) === '[object Array]' &&
      text.length > 0
    ) {
      return true;
    } else {
      return false;
    }
  },
  /**
   * 过滤对象中为空的属性
   * @param obj
   * @returns {*}
   */
  filterObj: function(obj) {
    if (!(typeof obj == 'object')) {
      return;
    }

    for (let key in obj) {
      if (
        obj.hasOwnProperty(key) &&
        (obj[key] == null || obj[key] == undefined || obj[key] === '')
      ) {
        delete obj[key];
      }
    }
    return obj;
  },
  /**@desc 获取唯一的ID */
  guid: function() {
    function S4() {
      return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
    }
    return (
      S4() +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      '-' +
      S4() +
      S4() +
      S4()
    );
  },
  filterSize: function(size) {
    if (!size) return '';
    return size < 1024
      ? size + ' B'
      : size < pow1024(2)
      ? (size / 1024).toFixed(2) + ' KB'
      : size < pow1024(3)
      ? (size / pow1024(2)).toFixed(2) + ' MB'
      : size < pow1024(4)
      ? (size / pow1024(3)).toFixed(2) + ' GB'
      : (size / pow1024(4)).toFixed(2) + ' TB';
  },
  applyEmployeeStr: function(employee) {
    if (!employee) return '';
    return `${employee.orgName ? employee.orgName + '-' : ''}${
      employee.employeeName
    }${employee.phoneNumber ? '(' + employee.phoneNumber + ')' : ''}`;
  },
  // 防抖
  debounce: function(func, wait, immediate) {
    let timeout, args, context, timestamp, result;
    const later = function() {
      // 据上一次触发时间间隔
      const last = +new Date() - timestamp;
      // 上次被包装函数被调用时间间隔last小于设定时间间隔wait
      if (last < wait && last > 0) {
        timeout = setTimeout(later, wait - last);
      } else {
        timeout = null;
        // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
        if (!immediate) {
          result = func.apply(context, args);
          if (!timeout) context = args = null;
        }
      }
    };
    return function(...args) {
      context = this;
      timestamp = +new Date();
      const callNow = immediate && !timeout;
      // 如果延时不存在，重新设定延时
      if (!timeout) timeout = setTimeout(later, wait);
      if (callNow) {
        result = func.apply(context, args);
        context = args = null;
      }
      return result;
    };
  },
  isDoc(str) {
    return /\.(doc|docx|xls|xlsx|pdf|ofd|ppt|pptx|txt|mp4|zip|rar|7z|)$/.test(
      str.toLowerCase()
    );
  },
  isImg(str) {
    return /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(str.toLowerCase());
  },
  /**
   * 线上预览文件
   * @param String path 文件路径
   * @param String filename 文件名字
   */
  viewerDocBase(path, filename) {
    var url = '';
    if (path.indexOf('http') >= 0) {
      url = path + '?fullfilename=' + filename;
    } else {
      url = 'http://127.0.0.1:9088' + path + '?fullfilename=' + filename;
    }

    let a = document.createElement('a');
    a.target = '_blank';
    a.href =
      location.origin +
      '/ts-preview/onlinePreview?url=' +
      encodeURIComponent(Base64.encode(url));
    a.click();
  }
};

// 递归路由 将所有路由路径 存入一个数组
export const handleRouterMenu = menu => {
  const handleRecursionGetAllRouterPath = menu => {
    for (let i = 0; i < menu.length; i++) {
      const item = menu[i];
      if (item.children && item.children.length > 0) {
        handleRecursionGetAllRouterPath(item.children);
      } else {
        pathArr.push(item.path);
      }
    }
  };

  let pathArr = [];
  handleRecursionGetAllRouterPath(menu);
  return pathArr;
};

export let randomColor = () => {
  var h = Math.floor(Math.random() * 360); // 0 到 359 之间的随机色调值
  var s = 40 + Math.floor(Math.random() * 30); // 饱和度范围在 40% 到 70% 之间
  var v = 50 + Math.floor(Math.random() * 25); // 亮度范围在 50% 到 75% 之间

  // 将 HSV 颜色值转换为 RGB 颜色值
  var c = ((1 - Math.abs((2 * v) / 100 - 1)) * s) / 100;
  var x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  var m = v / 100 - c / 2;
  var r, g, b;

  if (h >= 0 && h < 60) {
    r = c;
    g = x;
    b = 0;
  } else if (h >= 60 && h < 120) {
    r = x;
    g = c;
    b = 0;
  } else if (h >= 120 && h < 180) {
    r = 0;
    g = c;
    b = x;
  } else if (h >= 180 && h < 240) {
    r = 0;
    g = x;
    b = c;
  } else if (h >= 240 && h < 300) {
    r = x;
    g = 0;
    b = c;
  } else {
    r = c;
    g = 0;
    b = x;
  }

  // 将 RGB 颜色值转换为十六进制表示
  var color =
    '#' +
    Math.round((r + m) * 255)
      .toString(16)
      .padStart(2, '0') +
    Math.round((g + m) * 255)
      .toString(16)
      .padStart(2, '0') +
    Math.round((b + m) * 255)
      .toString(16)
      .padStart(2, '0');
  return color;
};

// 求次幂
function pow1024(num) {
  return Math.pow(1024, num);
}
