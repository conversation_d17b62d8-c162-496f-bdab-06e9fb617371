<template>
  <el-dialog
    custom-class="meeting-details-modal"
    title="会议详情"
    :visible.sync="dialogVisible"
    :close-on-press-escape="false"
    fullscreen
  >
    <div class="container">
      <div class="header">
        <div class="avatar">
          <el-image
            class="img-box"
            :z-index="3000"
            :src="getImgUrl(dataSource.emphasis, true)"
            :preview-src-list="[getImgUrl(dataSource.emphasis, true)]"
          >
            <div slot="error" class="image-slot">
              <svg-icon icon-class="icon_upload" class="icon_upload"></svg-icon>
            </div>
          </el-image>
        </div>
        <div class="header-content">
          <!-- 标题 -->
          <div class="title">
            <el-tooltip
              :open-delay="1000"
              class="item"
              :content="getTitle"
              placement="top"
            >
              <span class="title">{{ getTitle }} </span>
            </el-tooltip>
          </div>
          <div class="footer">
            <el-row :gutter="80" style="height: 100%">
              <el-col :span="12" style="height: 100%">
                <div class="footer-left">
                  <!-- 预定时间 -->
                  <div class="time">
                    <div>
                      预约时段:
                      {{
                        `${moment(boardRoomMeeting.startTime).format(
                          'YYYY-MM-DD'
                        )}  ${moment(boardRoomMeeting.startTime).format(
                          'HH:mm'
                        )}-${moment(boardRoomMeeting.endTime).format('HH:mm')}`
                      }}
                    </div>
                    <div v-if="boardRoomMeeting.meetingStatus == '4'">
                      实际结束时间:
                      {{
                        moment(boardRoomMeeting.meetingEndTime).format(
                          'YYYY-MM-DD HH:mm'
                        )
                      }}
                    </div>
                  </div>
                  <!-- 位置 -->
                  <div class="address">
                    <el-tooltip
                      :open-delay="1000"
                      class="item"
                      :content="getAddressName"
                      placement="top"
                    >
                      <span>{{ getAddressName }}</span>
                    </el-tooltip>
                    <a
                      v-if="dataSource.positionPicture"
                      @click="viewPositionPicture(dataSource)"
                    >
                      查看资源位置示意图
                    </a>
                  </div>
                  <div>
                    {{ applyEmployeeStr(boardRoomMeeting.applyEmployee) }}
                  </div>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="footer-right">
                  <div class="footer-right-title">
                    <span class="line"></span>
                    <span>会议纪要</span>
                    <span v-if="boardRoomMeeting.summaryUpdateTime">
                      更新于{{ boardRoomMeeting.summaryUpdateTime }}
                    </span>
                  </div>
                  <div class="file-container">
                    <img src="@/assets/img/meeting/icon_word.png" />
                    <div class="file-info" v-if="dataSource.summaryUrl">
                      <div>
                        {{ dataSource.summaryName }}.{{
                          dataSource.summaryExtension
                        }}
                      </div>
                      <div>
                        <span>
                          <div>{{ filterSize(dataSource.summarySize) }}</div>
                        </span>
                        <div class="action">
                          <a @click="customDownloadFile(dataSource)">下载</a>
                          <!-- <a>收藏</a> -->
                          <a
                            v-if="hasPermissions"
                            @click="openMinutesMeetingDetails"
                          >
                            编辑
                          </a>
                          <a v-if="hasPermissions"> 删除 </a>
                        </div>
                      </div>
                    </div>
                    <div class="add-summary" v-else>
                      暂无会议纪要
                      <a
                        v-if="
                          hasPermissions && boardRoomMeeting.meetingStatus != 4
                        "
                        @click="openMinutesMeetingDetails"
                      >
                        新增
                      </a>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <!-- 相关资料 -->
      <div class="meeting-details-card">
        <div class="card-title">
          <span class="line"></span>
          相关资料{{ fileList.length > 0 ? `(${fileList.length})` : '' }}
          <div class="action">
            <el-upload
              v-if="hasPermissions && boardRoomMeeting.meetingStatus != 4"
              class="upload"
              :action="importExcelUrl"
              multiple
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
            >
              <a>上传资料</a>
            </el-upload>
            <a @click="batchUpload" v-if="fileList.length > 0">批量下载</a>
            <a
              @click="batchDetele"
              v-if="fileList.length > 0 && hasPermissions"
              style="color: rgba(51, 51, 51, 0.3)"
            >
              批量删除
            </a>
          </div>
        </div>
        <div class="card-container">
          <div class="file-list">
            <div class="file-item" v-for="item in fileList" :key="item.id">
              <el-checkbox
                class="ts-checkbox-input"
                v-model="item.checked"
              ></el-checkbox>
              <div class="file-container">
                <img :src="getFileImage(item)" />
                <div class="file-info">
                  <div>{{ item.originalName }}</div>
                  <div>
                    <span>
                      {{ filterSize(item.fileSize) }}
                    </span>
                    <div class="action">
                      <a @click.stop="handleDownload(item)">下载</a>
                      <a
                        @click.stop="deleteFile(item, index)"
                        v-if="hasPermissions"
                      >
                        删除
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 主要议程 -->
      <div class="meeting-details-card">
        <div class="card-title">
          <span class="line"></span>
          主要议程{{
            boardRoomMeeting.agendaList.length > 0
              ? `(${boardRoomMeeting.agendaList.length})`
              : ''
          }}
          <!-- 新增编辑页，暂时去掉主要议程编辑 -->
          <!-- <div class="action">
            <a
              v-if="hasPermissions"
              @click="handleEditMainAgenda(boardRoomMeeting.agendaList)"
            >
              编辑
            </a>
          </div> -->
        </div>
        <div class="card-container">
          <div class="agenda-list">
            <div
              class="agenda-item"
              v-for="(item, idx) in boardRoomMeeting.agendaList"
              :key="idx"
            >
              <div>
                <el-tooltip
                  :open-delay="500"
                  class="item"
                  :content="getContent(item)"
                  placement="top"
                >
                  <span>{{ getContent(item) }}</span>
                </el-tooltip>
              </div>
              <span style="margin-left: 24px">
                负责人：{{ item.functionary }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 签到详情 -->
      <div class="meeting-details-card">
        <div class="card-title">
          <span class="line"></span>
          签到详情
        </div>
        <div class="card-container">
          <div class="sign-in-list">
            <div class="nav">
              <div class="nav-item">
                应到{{ boardRoomMeeting.boardroomSigninCount.planNum }}人
              </div>
              <div class="nav-item">
                <span></span>
                已签到{{ boardRoomMeeting.boardroomSigninCount.signInNum }}人
              </div>
              <div class="nav-item">
                <span></span>
                未签到{{ boardRoomMeeting.boardroomSigninCount.noSignInNum }}人
              </div>
              <div class="nav-item">
                <span></span>
                请假{{ boardRoomMeeting.boardroomSigninCount.leaveNum }}人
              </div>
              <div class="nav-item">
                <span></span>
                签退{{ boardRoomMeeting.boardroomSigninCount.signOut }}人
              </div>
              <div
                class="nav-item"
                v-if="
                  boardRoomMeeting.boardroomSigninCount.noInviteSignInNum > 0
                "
              >
                <span></span>
                临时参会人
                {{ boardRoomMeeting.boardroomSigninCount.noInviteSignInNum }}
                人
              </div>
              <div class="extra_box">
                <el-select v-model="signInStatusValue" placeholder="请选择">
                  <el-option
                    v-for="item in signInStatus"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-button
                  @click="handleSearchSignStatus"
                  class="ts-button primary extra"
                >
                  搜索
                </el-button>
                <img
                  @click="handleResetSignStatus"
                  class="icon_reset ml-8"
                  src="@/assets/img/meeting/reset.svg"
                />
                <el-button
                  v-if="
                    [-1, 0, 1].indexOf(boardRoomMeeting.meetingStatus) !== -1 &&
                      boardRoomMeeting.currentEmpPower.indexOf('2') !== -1
                  "
                  @click="
                    handleQrCode(
                      '会议签到二维码',
                      boardRoomMeeting.signInQrCode
                    )
                  "
                  class="ts-button extra"
                >
                  签到二维码
                </el-button>
                <el-button
                  v-if="
                    [-1, 0, 1].indexOf(boardRoomMeeting.meetingStatus) !== -1 &&
                      boardRoomMeeting.currentEmpPower.indexOf('2') !== -1 &&
                      dataSource.signOutType == 1
                  "
                  @click="
                    handleQrCode(
                      '会议签退二维码',
                      boardRoomMeeting.signOutQrCode
                    )
                  "
                  class="ts-button extra"
                >
                  签退二维码
                </el-button>
                <el-button
                  @click="exportBoardRoomSignIn"
                  class="ts-button extra"
                >
                  导出
                </el-button>
              </div>
            </div>
            <div class="sign-in-list-modal-table">
              <el-table :data="tableData" border stripe>
                <!-- 科室 -->
                <el-table-column
                  prop="signinOrgName"
                  label="科室"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 姓名 -->
                <el-table-column
                  prop="signinUsername"
                  label="姓名"
                  align="center"
                  width="120"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 工号 -->
                <el-table-column
                  prop="signinUsercode"
                  label="工号"
                  align="center"
                  width="120"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 手机 -->
                <el-table-column
                  prop="signinPhoneNumber"
                  label="手机"
                  width="120"
                  align="center"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 签到状态 -->
                <el-table-column
                  prop="signinStatusLable"
                  label="签到状态"
                  width="120"
                  align="center"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 操作时间 -->
                <el-table-column
                  prop="signinTime"
                  label="操作时间"
                  width="160"
                  align="center"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 签退时间 -->
                <el-table-column
                  prop="signinOutTime"
                  label="签退时间"
                  width="160"
                  align="center"
                  :show-overflow-tooltip="true"
                ></el-table-column>
                <!-- 备注 -->
                <el-table-column
                  prop="remarks"
                  label="备注"
                  :show-overflow-tooltip="true"
                ></el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <!-- 取消情况 -->
      <div
        class="meeting-details-card"
        v-if="boardRoomMeeting.meetingStatus == '4'"
      >
        <div class="card-title">
          <span class="line"></span>
          取消情况
        </div>
        <div class="card-container">
          <div class="mb_4 text_style">
            取消人：{{ boardRoomMeeting.cancelEmployee.employeeName || '' }}
          </div>
          <div class="mb_4 text_style">
            取消时间：{{ boardRoomMeeting.cancelTime }}
          </div>
          <div class="mb_4 text_style">
            取消原因：{{ boardRoomMeeting.cancelRemark }}
          </div>
        </div>
      </div>
    </div>
    <div slot="footer"></div>
    <el-image
      style="display: none"
      class="my-img"
      v-if="previewImage"
      ref="previewImage"
      :z-index="3001"
      :src="previewImage"
      :preview-src-list="[previewImage]"
    >
    </el-image>
    <!-- 会议纪要 -->
    <minutes-meeting-details
      ref="minutesMeetingDetails"
      @ok="minutesMeetingDetailsOk"
    ></minutes-meeting-details>
    <!-- 会议议程 -->
    <main-agenda-modal
      ref="mainAgendaModal"
      @ok="mainAgendaModalOk"
    ></main-agenda-modal>
    <el-dialog
      :title="qrCodeConf.title"
      :visible.sync="qrCodeVisible"
      width="80%"
      v-dialogDrag
      custom-class="qr_code_2_modal"
      append-to-body
      :before-close="qrCodeClose"
    >
      <div class="qr_code_2_modal_container">
        <div class="qr-code-content">
          <img :src="getImgUrl(qrCodeConf.qrCode)" alt="" srcset="" />

          <div class="info_box">
            <div class="title">
              请使用{{ mobilePlatformName }}扫码{{
                qrCodeConf.title.slice(2, 4)
              }}
            </div>
            <div class="total">
              应到{{ boardRoomMeeting.boardroomSigninCount.planNum }}人
            </div>
            <div class="subtotal" v-for="item in signInCount" :key="item.value">
              <span :style="getStyle(item)"></span>
              <span class="mar">{{ item.label }}</span>
              <span>{{ item.total }}人</span>
            </div>
          </div>
        </div>
        <div class="footer">
          <div
            class="refresh_box"
            v-if="boardRoomMeeting.autoRefreshSignInQrCodeType === 1"
          >
            {{ refreshTime }}S自动刷新
          </div>
          <div v-else>
            <el-button v-print="print" class="ts-button primary ">
              二维码打印
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <div id="qrCodeImg">
      <div class="print-img-container">
        <div
          class="qrCodeTitle"
          style="width: 100%;text-align: center;font-size: 32px;"
        >
          {{ dataSource.motif }}
        </div>
        <div
          class="qr_code_img"
          style="position: absolute;top: 40%;left: 0;right: 0;transform: translateY(-50%);text-align: center;"
        >
          <img
            style="width: 70%;"
            :src="getImgUrl(qrCodeConf.qrCode)"
            alt=""
            srcset=""
          />
          <div
            style="margin-top: 16px;display: block !important;font-size: 18px;"
            class="desc"
          >
            {{ qrCodeConf.title }}
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import MinutesMeetingDetails from '@/views/meetingManagement/minutesMeeting/details.vue';
import MainAgendaModal from './components/mainAgendaModal.vue';
import moment from 'moment';
import { commonUtils } from '@/utils/index.js';

let TIME_COUNT = 5;
export default {
  components: {
    MinutesMeetingDetails,
    MainAgendaModal
  },
  data() {
    return {
      signInStatus: [
        { label: '未签到', value: 0 },
        { label: '已签到', value: 1 },
        { label: '已请假', value: 2 },
        { label: '迟到', value: 3 }
      ],
      signInStatusValue: '',
      dialogVisible: false,
      dataSource: {
        accessoryId: '',
        emphasis: '',
        appTypeLable: '',
        motif: '',
        location: '',
        floor: '',
        boardroomName: '',
        startTime: '',
        endTime: '',

        summaryName: '',
        summarySize: '',
        summaryUpdateTime: '',
        summaryUrl: '',

        currentEmpPower: '',
        meetingEndTime: ''
      },
      boardRoomMeeting: {
        agendaList: [],
        currentEmpPower: '',
        boardroomSigninCount: {
          leaveNum: 0,
          noSignInNum: 0,
          planNum: 0,
          signInNum: 0,
          signOut: 0,
          noInviteSignInNum: 0
        }
      },
      previewImage: '',
      fileList: [],
      tableData: [],
      qrCodeVisible: false,
      qrCodeConf: {
        title: '',
        qrCode: ''
      },
      print: {
        id: 'qrCodeImg',
        popTitle: '二维码打印', // 打印配置页上方标题
        extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
        preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
        previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
        previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
        zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
        previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
        previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
        beforeOpenCallback() {}, // 开启打印前的回调事件
        openCallback() {}, // 调用打印之后的回调事件
        closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
        url: '',
        standard: '',
        extraCss: ''
      },
      timer: '',
      refreshTime: TIME_COUNT,
      nd: moment().valueOf(),
      signInCount: [],
      TimerSignIn: null
    };
  },
  methods: {
    moment,
    applyEmployeeStr: commonUtils.applyEmployeeStr,
    filterSize: commonUtils.filterSize,
    async handleSearchSignStatus() {
      const res = await this.ajax.getBoardRoomSignInList({
        applyId: this.boardRoomMeeting.applyId,
        signinStatus: this.signInStatusValue
      });
      this.tableData = res.object || [];
    },
    handleResetSignStatus() {
      this.signInStatusValue = '';
      this.handleSearchSignStatus();
    },
    async open(row) {
      try {
        const boardRoomMeeting = await this.getBoardRoomMeeting(row.meetingId);
        this.boardRoomMeeting = boardRoomMeeting.object;
        const fileList = await this.ajax.getFileAttachmentByBusinessId({
          businessId: row.accessoryId
        });
        this.fileList = fileList.object || [];
        this.fileList = this.fileList.map(e => {
          return { ...e, checked: false };
        });
        const res = await this.ajax.getBoardRoomSignInList({
          applyId: row.applyId
        });
        this.tableData = res.object || [];
        this.dataSource = Object.assign(this.dataSource, row);
        this.dialogVisible = true;
      } catch (error) {}
    },
    getData() {
      return this.ajax
        .getBoardRoomMeeting({ meetingId: this.dataSource.meetingId })
        .then(res => {
          this.boardRoomMeeting = res.object;
        });
    },
    async mainAgendaModalOk() {
      try {
        const boardRoomMeeting = await this.getBoardRoomMeeting(
          this.dataSource.meetingId
        );
        this.boardRoomMeeting = boardRoomMeeting.object;
      } catch (error) {}
    },
    getBoardRoomMeeting(meetingId) {
      return new Promise(async (resolve, reject) => {
        try {
          const res = await this.ajax.getBoardRoomMeeting({ meetingId });
          resolve(res);
        } catch (error) {
          reject(error);
        }
      });
    },
    getImgUrl(filePath, refresh) {
      if (filePath) {
        return `${location.origin}${filePath}${
          filePath.indexOf('?') == -1 ? '?' : '&'
        }nd=${refresh ? '' : this.nd}`;
      } else {
        return `${location.origin}${filePath}`;
      }
    },
    viewPositionPicture(row) {
      this.previewImage = this.getImgUrl(row.positionPicture, true);
      this.$nextTick(() => {
        this.$refs.previewImage.showViewer = true;
      });
    },
    // 文件上传成功回调
    async handleAvatarSuccess(response, file, fileList) {
      if (response.success) {
        const fileList = await this.ajax.getFileAttachmentByBusinessId({
          businessId: this.dataSource.accessoryId
        });
        this.fileList = fileList.object || [];
        this.fileList = this.fileList.map(e => {
          return { ...e, checked: false };
        });
      } else {
        this.$message({
          showClose: true,
          message: response.message,
          type: 'error'
        });
      }
    },
    // 单个文件下载
    handleDownload(row) {
      this.ajax.downloadFile(row.id, row.originalName).then(data => {});
    },
    // 自定义下载地址
    customDownloadFile(row) {
      this.ajax
        .customDownloadFile(
          row.summaryUrl,
          row.summaryName + '.' + (row.summaryExtension || 'docx')
        )
        .then(data => {});
    },
    // 文件删除
    deleteFile(row, idx) {
      this.$confirm('删除后无法恢复，确定要删除该文件吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        cancelButtonClass: 'btn-custom-cancel ts-button',
        confirmButtonClass: 'btn-custom-cancel ts-button primary',
        type: 'warning'
      })
        .then(() => {
          this.ajax
            .deleteFileId({
              fileid: row.id
            })
            .then(res => {
              this.fileList.splice(idx, 1);
            })
            .catch(error => {});
        })
        .catch(() => {});
    },
    // 批量下载
    batchUpload() {
      const idList = this.fileList.map(e => {
        return e.id;
      });
      const nameStr = this.dataSource.motif + '.zip';
      this.ajax.batchUpload(idList, nameStr).then(response => {
        this.fileList = this.fileList.map(e => {
          return { ...e, checked: false };
        });
      });
    },
    // 批量删除
    batchDetele() {
      const vm = this;
      const idList = vm.fileList
        .filter(e => {
          return e.checked;
        })
        .map(e => {
          return e.id;
        });
      if (idList.length === 0) return;
      var promises = idList.map(function(id) {
        return vm.ajax.deleteFileId({
          fileid: id
        });
      });
      Promise.all(promises)
        .then(function(posts) {
          vm.ajax
            .getFileAttachmentByBusinessId({
              businessId: vm.dataSource.accessoryId
            })
            .then(fileList => {
              vm.fileList = fileList.object || [];
            });
        })
        .catch(function(reason) {});
    },
    getContent(row) {
      return `${row.agenda ? '【' + row.agenda + '】 ' : ''}${row.content}`;
    },
    // 会议纪要
    openMinutesMeetingDetails() {
      this.$refs.minutesMeetingDetails.open(this.dataSource);
    },
    minutesMeetingDetailsOk(record) {
      if (!this.dataSource.accessoryUrl)
        this.dataSource = Object.assign(this.dataSource, {
          summaryUpdateTime: moment().format('YYYY-MM-DD HH:mm'),
          summaryExtension: record.fileExtension,
          summarySize: record.accessorySize,
          summaryName: record.accessoryName,
          summaryUrl: record.accessoryUrl
        });
      this.$parent.loadData();
    },
    getFileImage(row) {
      let iconImg;
      switch (row.fileExtension) {
        case 'png':
        case 'jpeg':
        case 'jpg':
        case 'gif':
          iconImg = require('@/assets/img/file_icon/imp.png');
          break;
        case 'docx':
        case 'doc':
          iconImg = require('@/assets/img/file_icon/doc.png');
          break;
        case 'html':
          iconImg = require('@/assets/img/file_icon/html.png');
          break;
        case 'mp3':
          iconImg = require('@/assets/img/file_icon/mp3.png');
          break;
        case 'mp4':
          iconImg = require('@/assets/img/file_icon/mp4.png');
          break;
        case 'pdf':
          iconImg = require('@/assets/img/file_icon/pdf.png');
          break;
        case 'ppt':
          iconImg = require('@/assets/img/file_icon/ppt.png');
          break;
        case 'txt':
          iconImg = require('@/assets/img/file_icon/txt.png');
          break;
        case 'xlxs':
        case 'xls':
          iconImg = require('@/assets/img/file_icon/xls.png');
          break;
        case 'zip':
          iconImg = require('@/assets/img/file_icon/zip.png');
          break;
        default:
          iconImg = require(`@/assets/img/file_icon/unfile.png`);
          break;
      }
      return iconImg;
    },
    //议程编辑
    handleEditMainAgenda(row) {
      this.$refs.mainAgendaModal.open(this.boardRoomMeeting.applyId, row);
    },
    // 导出
    exportBoardRoomSignIn() {
      this.ajax
        .customDownloadFile(
          `/ts-oa/boardRoomSignIn/export?meetingId=${this.dataSource.meetingId}&signinStatus=${this.signInStatusValue}`,
          `${this.dataSource.motif}签到表.xlsx`
        )
        .then(data => {});
    },
    getStyle(row) {
      let style = {};
      switch (row.value) {
        case 'noSignInNum':
          style.background = '#ECB89C';
          break;
        case 'signInNum':
          style.background = '#73A0FA';
          break;
        case 'leaveNum':
          style.background = '#73DEB3';
          break;
        default:
          break;
      }
      return style;
    },
    async handleGetSignInformation() {
      try {
        let signInCount = await this.ajax.getSignInCount({
          meetingId: this.dataSource.meetingId
        });
        this.signInCount = [];
        if (this.qrCodeConf.title.slice(2, 4) === '签到') {
          this.signInCount = [
            {
              label: '已签到',
              value: 'signInNum',
              total: signInCount.object.signInNum
            },
            {
              label: '未签到',
              value: 'noSignInNum',
              total: signInCount.object.noSignInNum
            },
            {
              label: '请假',
              value: 'leaveNum',
              total: signInCount.object.leaveNum
            }
          ];
        } else {
          this.signInCount = [
            {
              label: '签退',
              value: 'signOut',
              total: signInCount.object.signOut
            }
          ];
        }
      } catch (error) {
        console.log(error);
      }
    },
    async handleQrCode(title, qrCode) {
      this.qrCodeConf = {
        title,
        qrCode
      };
      await this.handleGetSignInformation();
      this.TimerSignIn = setInterval(() => {
        this.handleGetSignInformation();
      }, 5000);
      this.qrCodeVisible = true;

      TIME_COUNT = this.dataSource.autoRefreshSignInQrCodeDate || 30;
      this.refreshTime = this.dataSource.autoRefreshSignInQrCodeDate || 30;

      if (this.boardRoomMeeting.autoRefreshSignInQrCodeType === 1) {
        this.getCode();
      }
    },
    async qrCodeClose() {
      this.qrCodeVisible = false;
      if (this.timer) {
        clearInterval(this.timer);
      }

      this.timer = '';
      this.refreshTime = TIME_COUNT;
      if (this.TimerSignIn) {
        clearInterval(this.TimerSignIn);
      }

      await this.getData();
      const res = await this.ajax.getBoardRoomSignInList({
        applyId: this.boardRoomMeeting.applyId
      });
      this.tableData = res.object || [];

      let qrCode = document.querySelector('.qr_code_2_modal');
      qrCode.style.top = '0';
      qrCode.style.left = '0';
    },
    getCode() {
      const vm = this;
      if (!this.timer) {
        this.timer = setInterval(() => {
          if (this.refreshTime > 1 && this.refreshTime <= TIME_COUNT) {
            this.refreshTime--;
          } else {
            this.refreshTime = TIME_COUNT;
            this.nd = moment().valueOf();
          }
        }, 1000);
      }
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.TimerSignIn) {
      clearInterval(this.TimerSignIn);
    }
  },
  computed: {
    mobilePlatformName() {
      return sessionStorage.getItem('mobilePlatformName');
    },
    // 获取会议室标题
    getTitle() {
      return `${
        this.dataSource.appTypeLable
          ? '【' + this.dataSource.appTypeLable + '】'
          : ''
      }   ${this.dataSource.motif || ''}`;
    },
    // 获取位置名称
    getAddressName() {
      const row = this.dataSource;
      return `${row.location ? row.location + '-' : ''}${
        row.floor ? row.floor + '层' : ''
      }${row.boardroomName ? '-' + row.boardroomName : ''}`;
    },
    importExcelUrl() {
      return `${location.origin}/ts-basics-bottom/fileAttachment/upload?businessId=${this.dataSource.accessoryId}&moduleName=meeting`;
    },
    // 权限判断
    hasPermissions() {
      if (!this.boardRoomMeeting.currentEmpPower) return false;
      const list = this.boardRoomMeeting.currentEmpPower.split(',');
      if (list.indexOf('2') !== -1) {
        return true;
      } else {
        return false;
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.meeting-details-modal {
  .container {
    width: 1190px;
    height: 100%;
    margin: 0 auto;
    background-color: white;
    border-radius: 4px;
    padding: 16px;
    .add-summary {
      width: calc(100% - 44px);
      height: 36px;
      display: flex;
      align-items: flex-end;
      color: rgba(51, 51, 51, 0.7);
      a {
        cursor: pointer;
        margin-left: 16px;
        color: #5260ff;
      }
    }
    .header {
      display: flex;
      margin-bottom: 16px;
      .avatar {
        margin-right: 14px;
        width: 87px;
        height: 87px;
        .img-box {
          width: 100%;
          height: 100%;
          /deep/.image-slot {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #e4e4e4;
            .icon_upload {
              width: 40px;
              height: 40px;
            }
          }
        }
      }
      .header-content {
        width: calc(100% - 103px);
        height: 87px;
        .title {
          font-size: 14px;
          font-weight: bold;
          color: #333333;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-bottom: 8px;
        }
        .footer {
          height: calc(100% - 27px);
          .footer-left {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .time {
              display: flex;
              div {
                font-size: 12px;
                font-weight: 400;
                color: rgba(51, 51, 51, 0.7);
              }
              div + div {
                margin-left: 16px;
              }
            }
            .address {
              display: flex;
              align-items: center;
              span,
              a {
                font-size: 12px;
                font-weight: 400;
                color: rgba(51, 51, 51, 0.7);
              }
              a {
                margin-left: 16px;
                color: #5260ff;
                cursor: pointer;
              }
            }
            & > div {
              font-size: 12px;
              font-weight: 400;
              color: rgba(51, 51, 51, 0.7);
            }
          }
          .footer-right {
            .footer-right-title {
              margin-bottom: 4px;
              display: flex;
              align-items: center;
              & > span:nth-child(1) {
                display: inline-block;
                width: 2px;
                height: 16px;
                background: #5260ff;
              }
              & > span:nth-child(2) {
                font-size: 14px;
                font-weight: 400;
                color: #333333;
                margin: 0 16px 0 8px;
              }
              & > span:nth-child(3) {
                font-size: 12px;
                font-weight: 400;
                color: rgba(51, 51, 51, 0.5);
              }
            }
            .file-container {
              display: flex;
              justify-content: space-between;
              padding-left: 10px;
              img {
                width: 36px;
                height: 36px;
                margin-right: 8px;
              }
              .file-info {
                width: calc(100% - 44px);
                height: 36px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                & > div:first-child {
                  font-size: 12px;
                  font-weight: 400;
                  color: #333333;
                }
                & > div:last-child {
                  display: flex;
                  align-items: center;
                  & > span {
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 0.5);
                  }
                  .action {
                    margin-left: 24px;
                    a {
                      font-size: 12px;
                      font-weight: 400;
                      color: #5260ff;
                      cursor: pointer;
                    }
                    a + a {
                      margin-left: 8px;
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .meeting-details-card {
      .card-title {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        .line {
          display: inline-block;
          width: 2px;
          height: 16px;
          background: #5260ff;
          margin-right: 8px;
        }
        font-size: 14px;
        font-weight: 400;
        color: #333333;

        .action {
          margin-left: 27px;
          display: flex;
          align-items: center;
          a {
            cursor: pointer;
            font-size: 12px;
            font-weight: 400;
            color: #5260ff;
            margin-right: 16px;
          }
        }
      }
      .card-container {
        .file-list {
          display: flex;
          flex-wrap: wrap;
          //   justify-content: space-between;
          align-items: center;

          .file-item {
            padding-right: 16px;
            // width: calc(20% - 16px);
            width: 20%;
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            .file-container {
              width: calc(100% - 14px);
              display: flex;
              justify-content: space-between;
              padding-left: 8px;
              img {
                width: 36px;
                height: 36px;
                margin-right: 8px;
              }
              .file-info {
                width: calc(100% - 44px);
                height: 36px;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
                & > div:first-child {
                  font-size: 12px;
                  font-weight: 400;
                  color: #333333;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }
                & > div:last-child {
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  & > span {
                    font-size: 12px;
                    font-weight: 400;
                    color: rgba(51, 51, 51, 0.5);
                  }
                  .action {
                    a {
                      font-size: 12px;
                      font-weight: 400;
                      color: #5260ff;
                      cursor: pointer;
                    }
                    a + a {
                      margin-left: 8px;
                    }
                  }
                }
              }
            }
          }
        }
        .agenda-list {
          .agenda-item {
            display: flex;
            align-items: center;
            span {
              font-size: 12px;
              font-weight: 400;
              color: #333333;
            }
          }
          .agenda-item + .agenda-item {
            margin-top: 8px;
          }
        }
        .sign-in-list {
          .nav {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            .nav-item {
              font-size: 12px;
              font-weight: 400;
              color: rgba(51, 51, 51, 0.7);
              span {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 5px;
              }
            }
            .nav-item + .nav-item {
              margin-left: 16px;
            }
            & > .nav-item:first-child {
              color: #333333;
            }
            & > .nav-item:nth-child(2) {
              span {
                background: #73a0fa;
              }
            }
            & > .nav-item:nth-child(3) {
              span {
                background: #eca49c;
              }
            }
            & > .nav-item:nth-child(4) {
              span {
                background: #73deb3;
              }
            }
            & > .nav-item:nth-child(5) {
              span {
                background: #b073de;
              }
            }
            & > .nav-item:nth-child(6) {
              span {
                background: #666666;
              }
            }
          }
        }
      }
    }
    .meeting-details-card + .meeting-details-card {
      margin-top: 16px;
    }
  }
}
.qr_code_2_modal {
  .qr_code_2_modal_container {
    display: flex;
    justify-content: center;
    flex-direction: column;
    .qr-code-content {
      display: flex;
      justify-content: center;
      width: 100%;
      img {
        width: 398px;
        height: 398px;
        margin-right: 40px;
      }
      .info_box {
        .title {
          font-size: 50px;
          font-weight: bold;
          color: #333333;
          margin-bottom: 12px;
        }
        .total {
          font-size: 48px;
          color: #333333;
          margin-bottom: 12px;
        }
        .subtotal {
          display: flex;
          align-items: center;
          & > span:first-child {
            width: 48px;
            height: 48px;
            background: #73a0fa;
            border-radius: 24px;
          }
          span {
            font-size: 44px;
            color: #333333;
          }
          .mar {
            margin: 0 50px 0 30px;
          }
        }
        .subtotal + .subtotal {
          margin-top: 12px;
        }
      }
    }
  }
  .footer {
    margin-top: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    .refresh_box {
      font-size: 22px;
      color: #999999;
      height: 44px;
      background: #f4f4f4;
      border-radius: 22px;
      padding: 0 30px;
      line-height: 44px;
      text-align: center;
    }
  }
}
/deep/.meeting-details-modal {
  & > .el-dialog__header {
    box-shadow: 0px 1px 0px 0px #e4e4e4;
  }
  & > .el-dialog__body {
    height: calc(100% - 81px);
    background-color: #f4f4f4;
    padding: 0 !important;
  }
  & > .el-dialog__footer {
    height: 40px;
    box-shadow: 0px -1px 0px 0px rgba(0, 0, 0, 0.15);
  }
  background: #ffffff;
}
.mb_4 {
  margin-bottom: 4px;
}
.text_style {
  font-size: 12px;
  font-weight: 400;
  color: #333333;
}
.extra_box {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  .extra {
    margin-left: 8px;
  }
}
.print-img-container {
  display: none;
}
</style>
<style edia="print" lang="scss">
.ts-checkbox-input .el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #5260ff;
  border-color: #5260ff;
}
.ts-checkbox-input .el-checkbox__input .el-checkbox__inner:hover {
  border-color: #5260ff;
}
@media print {
  .print-img-container {
    display: block !important;
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    .qrCodeTitle {
      width: 100%;
      text-align: center;
      font-size: 32px;
    }
    .qr_code_img {
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      transform: translateY(-50%);
      text-align: center;
    }
    .qr_code_img img {
      width: 70%;
    }
    .qr_code_img .desc {
      margin-top: 16px;
      display: block !important;
      font-size: 18px;
    }
  }
}
</style>
