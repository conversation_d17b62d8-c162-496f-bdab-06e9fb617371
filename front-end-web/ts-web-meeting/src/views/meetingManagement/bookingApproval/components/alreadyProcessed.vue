<template>
  <div class="already-processed">
    <div class="search-box mb-8">
      <div class="ts-form-item">
        <span class="label">主题</span>
        <div class="value" style="width:150px">
          <el-input
            v-model="queryParam.motif"
            placeholder="请输入主题"
          ></el-input>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">状态</span>
        <div class="value" style="width:150px">
          <el-select v-model="queryParam.status" placeholder="请选择状态">
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="ts-form-item">
        <span class="label">预约时间</span>
        <div class="value" style="width:350px">
          <!-- <el-date-picker
            v-model="byTheTime"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker> -->
          <base-date-range-picker
            v-model="byTheTime"
            :placeholder="['开始日期', '结束日期']"
            type="daterange"
          ></base-date-range-picker>
        </div>
      </div>
      <el-button @click="loadData(1)" class="ts-button primary ml-8">
        搜索
      </el-button>
      <img
        @click="reset"
        class="icon_reset ml-8"
        src="@/assets/img/meeting/reset.svg"
      />
    </div>
    <div class="content-table">
      <el-table
        :data="dataSource"
        style="width:100%"
        height="100%"
        border
        stripe
        @sort-change="sortChange"
      >
        <el-table-column
          prop="motif"
          label="主题"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="boardroomName"
          :show-overflow-tooltip="true"
          label="资源名称"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="startTime"
          label="预约开始时间"
          width="140px"
        >
          <template slot-scope="scope">
            {{
              scope.row.startTime
                ? moment(scope.row.startTime).format('YYYY-MM-DD HH:mm')
                : ''
            }}
          </template>
        </el-table-column>

        <el-table-column
          sortable="custom"
          prop="destineDate"
          label="申请时间"
          width="140px"
        >
          <template slot-scope="scope">
            {{
              scope.row.destineDate
                ? moment(scope.row.destineDate).format('YYYY-MM-DD HH:mm')
                : ''
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="预约科室"
          sortable="custom"
          prop="applyOrgName"
          :show-overflow-tooltip="true"
          width="120"
        ></el-table-column>
        <el-table-column
          label="预约人"
          :show-overflow-tooltip="true"
          width="120"
        >
          <template slot-scope="scope">
            {{
              `${scope.row.applyEmpName}${
                scope.row.applyPhoneNumber
                  ? '(' + scope.row.applyPhoneNumber + ')'
                  : ''
              }`
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="办理时间"
          sortable="custom"
          prop="checkTime"
          width="140"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            {{
              scope.row.checkTime
                ? moment(scope.row.checkTime).format('YYYY-MM-DD HH:mm')
                : ''
            }}
          </template>
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="statusLable"
          align="center"
          label="审批状态"
          width="120"
        >
        </el-table-column>
        <el-table-column
          prop="stepName"
          label="当前节点"
          :show-overflow-tooltip="true"
          width="120"
        >
        </el-table-column>
        <el-table-column width="100" align="center" label="操作">
          <template slot-scope="scope">
            <a class="handle-approve" @click="handleCommand('view', scope.row)"
              >查看流程</a
            >
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-pagination
      class="ts-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="ipagination.current"
      :page-sizes="ipagination.pageSizeOptions"
      :page-size="ipagination.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="ipagination.total"
    >
    </el-pagination>
  </div>
</template>

<script>
import { statusList } from '../js/config';
import { commonUtils } from '@/utils/index.js';
import { TSTableMixin } from '@/mixins/TSTableMixin';
import moment from 'moment';
export default {
  mixins: [TSTableMixin],
  data() {
    return {
      statusList,
      queryParam: {
        sidx: 'a2.CREATE_DATE',
        sord: 'desc',
        endTime: '',
        motif: '',
        startTime: '',
        workflowType: 3,
        status: ''
      },
      /* 分页参数 */
      ipagination: {
        current: 1,
        pageSize: 100,
        pageSizeOptions: [20, 40, 60, 80, 100, 200, 500, 1000, 2000],
        total: 0
      },
      byTheTime: '',
      dataSource: []
    };
  },
  methods: {
    moment,
    async loadData(arg) {
      if (arg === 1) {
        this.ipagination.current = 1;
      }
      try {
        let queryParam = JSON.parse(JSON.stringify(this.queryParam));
        queryParam.pageNo = this.ipagination.current;
        queryParam.pageSize = this.ipagination.pageSize;
        queryParam = commonUtils.filterObj(queryParam);
        queryParam.applyStatus = queryParam.status;
        let num = 0;
        this.queryParam.startTime && num++;
        this.queryParam.endTime && num++;
        if (num == 1) {
          this.$message.warning(
            '请完整选择【会议开始时间】的开始时间和结束时间'
          );
          return;
        }
        const res = await this.ajax.getListWorkflowList(queryParam);
        this.dataSource = res.object.rows || [];
        this.ipagination.total = res.object.totalCount;
      } catch (error) {}
    },
    handleSizeChange(val) {
      this.ipagination.pageSize = val;
      this.loadData();
    },
    handleCurrentChange(val) {
      this.ipagination.current = val;
      this.loadData();
    },
    reset() {
      this.byTheTime = '';
      this.queryParam = {
        sidx: 'a2.CREATE_DATE',
        sord: 'desc',
        endTime: '',
        motif: '',
        startTime: '',
        workflowType: 3,
        status: ''
      };
      this.loadData(1);
    },

    async handleCommand(command, row) {
      switch (command) {
        // 查看流程
        case 'view':
          this.$parent.openProcessModal('alreadyProcessed', row);
          break;

        default:
          break;
      }
    }
  },
  created() {
    this.loadData();
  },
  watch: {
    byTheTime: {
      handler(val) {
        if (!val) return;
        this.queryParam.startTime = val[0];
        this.queryParam.endTime = val[1];
      },
      deep: true,
      immediate: true
    }
  }
};
</script>
<style lang="scss" scoped>
.already-processed {
  width: 100%;
  height: 100%;
  .search-box {
    display: flex;
    align-items: center;
    .icon_reset {
      width: 16px;
      height: 16px;
      cursor: pointer;
    }
  }
  .content-table {
    height: calc(100% - 70px);
  }
  .ts-pagination {
    float: right;
  }
  .rotate-90 {
    transform: rotate(90deg);
    color: #333333;
  }
  .bg-color {
    background-color: transparent !important;
  }
}
.handle-approve {
  cursor: pointer;
  color: #5260ff;
}
// 设置滚动条的宽度
/deep/ .el-table__body-wrapper::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}
// 设置滚动条的背景色和圆角
/deep/ .el-table__body-wrapper::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 8px;
}
</style>
