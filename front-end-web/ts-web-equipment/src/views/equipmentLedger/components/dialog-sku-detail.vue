<template>
  <vxe-modal
    className="dialog-sku-detail"
    title="选择设备"
    v-model="visible"
    width="85%"
    height="80%"
    showFooter
    mask
    :before-hide-method="close"
  >
    <template #default>
      <div class="content">
        <ts-search-bar-new
          v-model="searchForm"
          :actions="searchActions"
          :formList="searchList"
          @search="search"
        >
          <template slot="right">
            <ts-button
              @click="handleAddEquipment"
              class="shallowButton"
              type="primary"
            >
              新增设备
            </ts-button>
          </template>
        </ts-search-bar-new>
        <ts-vxe-base-table
          id="table_equipment_model_sku_detail"
          ref="table"
          :columns="columns"
          :checkbox-config="{ reserve: true, showHeader: false }"
          @selection-change="handleSelectionChange"
          @refresh="handleRefreshTable"
        />
      </div>

      <DialogDictionaryDetail
        :categoryTreeData="categoryTreeData"
        :deviceClassifyTree="deviceClassifyTree"
        :amsSkuType="amsSkuType"
        @submit="emitSuccessRefresh"
        ref="dialogDictionaryDetail"
      />
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">
          确 定
        </ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import DialogDictionaryDetail from '@/views/equipmentDictionary/components/dialog-dictionary-detail.vue';

export default {
  components: { DialogDictionaryDetail },
  data() {
    return {
      visible: false,
      searchForm: {},
      searchActions: [],

      categoryTreeData: [],
      deviceClassifyTree: [],
      amsSkuType: [],

      searchList: [
        {
          label: '字典名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入字典名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '规格型号',
          value: 'model',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入规格型号'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        }
      ],
      columns: [
        {
          type: 'checkbox',
          align: 'center',
          width: 50
        },
        {
          label: '序号',
          prop: 'pageIndex',
          width: 60,
          align: 'center'
        },
        {
          label: '字典名称',
          align: 'letf',
          prop: 'name'
        },
        {
          label: '规格型号',
          align: 'center',
          width: 110,
          prop: 'model'
        },
        {
          label: '品牌',
          align: 'center',
          width: 120,
          prop: 'brandName'
        },
        {
          label: '厂家',
          align: 'center',
          width: 140,
          prop: 'manufacturerName'
        },
        {
          label: '医疗器械分类',
          align: 'center',
          width: 140,
          prop: 'category22Name'
        }
      ]
    };
  },
  methods: {
    show({ selectedId = '' }) {
      this.visible = true;
      this.getCategoryTree();
      this.getDeviceClassifyTree();
      this.handleGetDicAMS_SKU_TYPE();

      this.$nextTick(async () => {
        this.$refs.table.pageNo = 1;
        await this.handleRefreshTable(selectedId);
        selectedId && this.handleSelectCheckboxRow();
      });
    },
    async getCategoryTree() {
      try {
        const res = await this.ajax.getClassificationTree();
        if (!res.success) {
          throw res.message;
        }
        this.categoryTreeData = res.object || [];
      } catch (e) {
        this.$newMessage('error', e || '分类数据获取失败!');
      }
    },

    async getDeviceClassifyTree() {
      try {
        const res = await this.ajax.getDeviceClassifyTree();
        if (!res.success) {
          throw res.message;
        }
        this.deviceClassifyTree = res.object || [];
      } catch (e) {
        this.$newMessage('error', res.message || '分类数据获取失败!');
      }
    },

    async handleGetDicAMS_SKU_TYPE() {
      const res = await this.ajax.getDataByDataLibrary('AMS_SKU_TYPE');
      if (!res.success) {
        this.$newMessage('error', '字典数据获取失败!');
        return false;
      }
      let arr = (res.object || []).map(item => ({
        element: 'ts-option',
        label: item.itemName,
        value: item.itemNameValue
      }));
      this.amsSkuType = arr;
    },

    handleSelectionChange(selection) {
      if (selection.length > 1) {
        let deleteRow = selection.shift();
        this.$refs.table.$refs.formTable.$refs.tsVxeTable.setCheckboxRow(
          deleteRow,
          false
        );
      }
    },
    search() {
      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },
    async handleRefreshTable(selectedId) {
      let pageNo = this.$refs.table.pageNo,
        pageSize = this.$refs.table.pageSize;
      let data = {
        ...this.searchForm,
        pageNo,
        pageSize
      };

      if (typeof selectedId === 'string' && selectedId) {
        data.selectedId = selectedId;
      }
      let res = await this.ajax.getEquipmentDictionary(data);
      if (res.success == false) {
        this.$newMessage('error', res.message || '表格数据获取失败!');
        return;
      }
      let rows = res.rows.map((item, index) => {
        return {
          ...item,
          pageIndex: index + 1 + (pageNo - 1) * pageSize
        };
      });
      this.$refs.table.refresh({
        ...res,
        rows
      });
    },

    handleAddEquipment() {
      this.$refs.dialogDictionaryDetail.show({
        type: 'add'
      });
    },

    async emitSuccessRefresh(selectedId) {
      await this.handleRefreshTable(selectedId);
      this.handleSelectCheckboxRow();
    },

    handleSelectCheckboxRow() {
      this.$refs.table.clearSelection();

      let example = this.$refs.table.tsVxeTableRef();
      example.setCheckboxRow(this.$refs.table.rows[0], true);
    },

    clearSelection() {
      this.$refs.table.$refs.formTable.$refs.tsVxeTable.clearCheckboxRow();
      this.$refs.table.$refs.formTable.$refs.tsVxeTable.clearCheckboxReserve();
      this.$refs.table.handleSelectionChange({
        reserves: [],
        records: []
      });
    },

    submit() {
      let example = this.$refs.table.tsVxeTableRef();
      let selection = [
        ...example.getCheckboxRecords(),
        ...example.getCheckboxReserveRecords()
      ];
      this.$emit('change', selection[0]);
      this.close();
    },
    close() {
      this.$refs.table.clearSelection();
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-sku-detail {
  ::v-deep {
    > .vxe-modal--box {
      > .vxe-modal--body {
        > .vxe-modal--content {
          > .content {
            width: 100%;
            height: 100%;

            display: flex;
            flex-direction: column;
            .table-container {
              flex: 1;
              overflow: hidden;
            }

            > .trasen-search-content {
              .left-search-content {
                .search-item {
                  width: 365px;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
