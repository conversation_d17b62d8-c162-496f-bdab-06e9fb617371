<!-- 新增/编辑 供应商 -->
<template>
  <vxe-modal
    className="dialog-add-material-supplier"
    v-model="visible"
    width="75%"
    height="88%"
    :title="title"
    showFooter
  >
    <template #default>
      <div class="content-container">
        <ts-tabs :type="null" v-model="activeTab" @tab-click="handleTabChange">
          <ts-tab-pane
            v-for="item in tabList"
            :key="item.name"
            :label="item.label"
            :name="item.name"
          />
        </ts-tabs>

        <div class="tabs-content">
          <ts-form ref="form" :model="form" :rules="rules">
            <div :id="tabList[0].name" class="form-group-tips">
              <span>{{ tabList[0].label }}</span>
            </div>
            <material-supplier-base-info-form
              :form="form"
              :rules="rules"
              :enterpriseNatureList="enterpriseNatureList"
            />
            <div class="divider"></div>

            <div :id="tabList[1].name" class="form-group-tips">
              <span>{{ tabList[1].label }}</span>
              <ts-button class="shallowButton" @click="handleAddCert">
                新增
              </ts-button>
            </div>
            <material-supplier-certificate-info-form
              :form="form"
              :rules="rules"
              :certTypeList="certTypeList"
            />
            <div class="divider"></div>
          </ts-form>
        </div>
      </div>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">确 定</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
import materialSupplierBaseInfoForm from './material-supplier-base-info-form.vue';
import materialSupplierCertificateInfoForm from './material-supplier-certificate-info-form.vue';
export default {
  components: {
    materialSupplierBaseInfoForm,
    materialSupplierCertificateInfoForm
  },
  data() {
    return {
      visible: false,
      title: '',
      type: '',
      activeTab: 'base',
      tabList: [
        { name: 'base', label: '基本信息' },
        { name: 'certificate', label: '证件信息' }
      ],
      enterpriseNatureList: [],
      certTypeList: [],

      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    isEdit() {
      return this.type === 'edit';
    }
  },
  methods: {
    // 打开弹窗
    async open({ data = {}, type, title }) {
      this.fetchDictData('AMS_ENTERPRISE_TYPE', 'enterpriseNatureList');
      this.fetchDictData('AMS_CERT_TYPE', 'certTypeList');

      this.type = type;
      this.title = title;
      let form = this.isEdit ? cloneDeep(data) : this.handleInitForm(data);
      this.$set(this, 'form', form);

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    // 处理标签页切换
    handleTabChange() {
      const element = document.getElementById(this.activeTab);
      element?.scrollIntoView({ behavior: 'smooth' });
    },

    // 初始化表单
    handleInitForm(data) {
      let form = {
        supplier: {},
        certList: []
      };
      return Object.assign(form, data);
    },

    // 通用获取字典数据方法
    async fetchDictData(dictKey, targetListName) {
      try {
        const res = await this.ajax.getDataByDataLibrary(dictKey);
        if (!res.success) {
          this.$newMessage('error', res.message || '字典数据获取失败');
          this[targetListName] = [];
          return;
        }
        this[targetListName] = (res.object || []).map(item => ({
          label: item.itemName,
          value: item.itemNameValue,
          itemCode: item.itemCode,
          element: 'ts-option'
        }));
      } catch (error) {
        this.$newMessage('error', '字典数据获取异常');
        this[targetListName] = [];
      }
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = cloneDeep(this.form);
        this.submitLoading = true;
        let API = this.ajax.materialSupplierInsert;
        if (this.isEdit) {
          API = this.ajax.materialSupplierEdit;
        } else {
          formData.supplier.status = '1';
        }
        const res = await API(formData);
        if (!res.success) {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【${this.title}】失败!`);
          return;
        }

        this.submitLoading = false;
        this.$newMessage('success', `【${this.title}】成功`);
        this.$emit('refresh');
        this.close();
      } catch (error) {
        console.error(error);
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    },

    handleAddCert() {
      this.form.certList.push(this.returnCertForm());
    },

    returnCertForm() {
      return {
        type: '',
        certNo: '',
        birthDate: '',
        validOverDate: '',
        isForever: '0',
        status: '',
        fileSet: ''
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-material-supplier {
  ::v-deep {
    .content-container {
      display: flex;
      flex-direction: column;
      background: #fff;

      .tabs-content {
        flex: 1;
        display: flex;
        overflow-y: auto;
        padding: 0 24px;

        .ts-form {
          width: 100%;
        }
      }

      .divider {
        width: 100%;
        height: 1px;
        background-color: #e5e5e5;
      }

      .form-group-tips {
        width: 100%;
        color: #333;
        font-weight: 800;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px;

        > span {
          font-weight: 800;
          &::before {
            content: '';
            display: inline-block;
            width: 4px;
            height: 16px;
            background-color: $primary-blue;
            margin-right: 8px;
            border-radius: 4px;
            transform: translateY(2px);
          }
        }
      }
    }
  }
}
</style>
