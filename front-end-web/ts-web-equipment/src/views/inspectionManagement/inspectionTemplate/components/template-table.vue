<template>
  <div class="template-table-box">
    <div class="template-name">{{ title }}</div>
    <div class="template-table">
      <el-scrollbar
        ref="scroll"
        style="flex: 1; display: flex;"
        wrap-style="flex: 1; height: unset;"
      >
        <table>
          <tbody>
            <tr>
              <td :colspan="columnLength + 10">
                <div>巡检科室：{{ editTableData.departNames || '' }}</div>
              </td>
            </tr>
            <tr v-if="type != 'edit'">
              <td :colspan="(columnLength + 9) / 5"><div>巡检工程师</div></td>
              <td :colspan="(columnLength + 9) / 5"><div>巡检日期</div></td>
              <td :colspan="(columnLength + 9) / 5"><div>开始时间</div></td>
              <td :colspan="(columnLength + 9) / 5"><div>结束时间</div></td>
              <td :colspan="(columnLength + 9) / 5"><div>巡检工时</div></td>
              <td :colspan="(columnLength + 9) / 5 + 2">
                <div>旅行时间(小时)</div>
              </td>
            </tr>
            <tr>
              <td rowspan="3"><div>二维码编号</div></td>
              <td rowspan="3"><div>设备编号</div></td>
              <td rowspan="3"><div>设备名称</div></td>
              <td rowspan="3"><div>设备型号</div></td>
              <td rowspan="3"><div>设备序列号</div></td>
              <td :colspan="columnLength + 5"><div>巡检评价</div></td>
            </tr>
            <tr>
              <td
                v-for="(item, index) in columnList"
                :key="`col_${index}`"
                :colspan="item.child.length"
              >
                <div v-if="isTemplate">
                  <ts-input v-model="item.inspectClassification"></ts-input>
                </div>
                <span v-else>{{ item.inspectClassification }}</span>
                <template v-if="isTemplate">
                  <span
                    class="addClass"
                    v-if="index == 0"
                    @click="handleAddClass"
                    >添加大类</span
                  >
                  <span class="delClass" v-else @click="handleDelClass(index)"
                    >删除大类</span
                  >
                </template>
              </td>
              <td rowspan="2"><div>日常保养</div></td>
              <td rowspan="2"><div>一级保养执行情况</div></td>
              <td rowspan="2"><div>计量设备是否在有效期</div></td>
              <td rowspan="2" colspan="2"><div>备注</div></td>
            </tr>
            <tr>
              <td
                v-for="(item, index) in columnTotalChildList"
                :key="`childCol_${index}`"
                :colspan="item.length"
              >
                <div>
                  <template v-if="isTemplate">
                    <ts-radio-group v-model="item.columnType">
                      <ts-radio label="radio">选择</ts-radio>
                      <ts-radio label="text">输入框</ts-radio>
                    </ts-radio-group>
                    <ts-input v-model="item.name"></ts-input>
                  </template>
                  <span v-else>{{ item.name }}</span>
                </div>
              </td>
            </tr>
            <tr v-for="(equItem, equIndex) in equList" :key="`equ_${equIndex}`">
              <td><div></div></td>
              <td><div></div></td>
              <td><div></div></td>
              <td><div></div></td>
              <td><div></div></td>
              <td
                v-for="(item, index) in columnTotalChildList"
                :key="`childCol_${index}`"
                :colspan="item.length"
              >
                <div v-if="item.columnType == 'radio' && isTemplate">
                  <span
                    style="display: block;"
                    v-for="(valueItem, valueIndex) in item.value"
                    :key="`value_${valueIndex}`"
                  >
                    <ts-input v-model="item.value[valueIndex]"> </ts-input>
                  </span>
                </div>
                <template v-if="isTemplate">
                  <span
                    class="addColumn"
                    v-if="item.columnIndex == 0"
                    @click="handleAddColumn(item.classIndex)"
                  >
                    添加项目
                  </span>
                  <span
                    class="delColumn"
                    v-else
                    @click="handleDelColumn(item.classIndex, item.columnIndex)"
                    >删除项目</span
                  >
                </template>
              </td>
              <td><div></div></td>
              <td><div></div></td>
              <td><div></div></td>
              <td colspan="2"><div></div></td>
            </tr>
          </tbody>
        </table>
        <div class="maintain-name" v-if="!isTemplate">
          <span>科室签字：<img /></span>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
export default {
  props: {
    editTableData: {
      type: Object,
      default: () => {}
    },
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'addTemplate'
    },
    equList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableData: {},
      columnList: []
    };
  },
  watch: {
    editTableData: {
      handler(val) {
        this.tableData = deepClone(val);
      }
    }
  },
  computed: {
    isTemplate() {
      return this.type == 'addTemplate' || this.type == 'editTemplate';
    },
    isReadonly() {
      return this.type == 'check';
    },
    columnLength: function() {
      var e = 0;
      return (
        this.columnList.forEach(function(t) {
          e += t.child.length;
        }),
        e
      );
    },
    columnTotalChildList: function() {
      let arr = [];
      this.columnList.forEach(function(classItem, classIndex) {
        arr = arr.concat(
          classItem.child.map((columnItem, columnIndex) => {
            columnItem.classIndex = classIndex;
            columnItem.columnIndex = columnIndex;
            return columnItem;
          })
        );
      });
      return arr;
    },
    newOption: function() {
      return ['', ''];
    },
    newItem: function() {
      return {
        columnType: 'radio',
        name: '',
        value: ['正常', '异常', '不适用']
      };
    }
  },
  methods: {
    initTemplete(e) {
      'important' === e &&
        (this.columnList = [
          {
            inspectClassification: '设备使用情况',
            child: [
              {
                columnType: 'radio',
                name: '设备外观',
                value: ['正常', '异常', '不适用']
              },
              {
                columnType: 'radio',
                name: '附件情况',
                value: ['正常', '异常', '不适用']
              },
              {
                columnType: 'radio',
                name: '系统时间',
                value: ['正常', '异常', '不适用']
              },
              {
                columnType: 'radio',
                name: '设备使用与存放环境',
                value: ['较好', '较差', '不适用']
              }
            ]
          }
        ]),
        'life' === e &&
          (this.columnList = [
            {
              inspectClassification: '设备使用情况',
              child: [
                {
                  columnType: 'radio',
                  name: '设备外观',
                  value: ['正常', '异常', '不适用']
                },
                {
                  name: '附件情况',
                  columnType: 'radio',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '系统时间',
                  value: ['正常', '异常', '不适用']
                }
              ]
            },
            {
              inspectClassification: '设备间、机房环境异常情况',
              child: [
                {
                  columnType: 'radio',
                  entity: '',
                  defaultValue: '',
                  name: '温度',
                  sort: 0,
                  value: ['正常', '异常', '不适用']
                },
                {
                  inspectClassification: '设备间、机房环境异常情况',
                  columnType: 'radio',
                  entity: '',
                  defaultValue: '',
                  name: '湿度',
                  sort: 0,
                  value: ['正常', '异常', '不适用']
                },
                {
                  inspectClassification: '设备间、机房环境异常情况',
                  columnType: 'radio',
                  entity: '',
                  defaultValue: '',
                  name: '卫生',
                  sort: 0,
                  value: ['正常', '异常', '不适用']
                }
              ]
            },
            {
              inspectClassification: '管线布局情况',
              child: [
                {
                  columnType: 'radio',
                  name: '水管',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '气体',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '电路',
                  value: ['正常', '异常', '不适用']
                }
              ]
            },
            {
              inspectClassification: '防护措施情况',
              child: [
                {
                  columnType: 'radio',
                  name: '电磁辐射',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '有害射线',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '噪音',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '震动',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '腐蚀',
                  value: ['较好', '较差', '不适用']
                }
              ]
            }
          ]),
        'common' === e &&
          (this.columnList = [
            {
              inspectClassification: '设备使用情况',
              child: [
                {
                  columnType: 'radio',
                  name: '设备外观',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '附件情况',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '系统时间',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '开机运行',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '设备使用与存放环境',
                  value: ['正常', '异常', '不适用']
                },
                {
                  columnType: 'radio',
                  name: '内置电池',
                  value: ['充满', '需充电', '无', '不适用']
                }
              ]
            }
          ]);
    },
    handleAddClass() {
      this.columnList.push({
        inspectClassification: '',
        child: [
          {
            ...this.newItem,
            classIndex: this.columnList.length,
            columnIndex: 0
          }
        ]
      });
    },
    handleDelClass(classIndex) {
      this.columnList.splice(classIndex, 1);
    },
    handleAddColumn(classIndex) {
      this.columnList[classIndex].child.push({
        ...this.newItem,
        classIndex,
        columnIndex: this.columnList[classIndex].child.length
      });
    },
    handleDelColumn(classIndex, columnIndex) {
      this.columnList[classIndex].child.splice(columnIndex, 1);
    }
  }
};
</script>

<style lang="scss" scoped>
.template-table-box {
  padding: 30px;
}
.template-name {
  font-size: 18px;
  color: #333;
  font-weight: bold;
  text-align: center;
  margin-bottom: 16px;
}
.template-table {
  width: 100%;
  overflow: hidden;
  display: flex;
  table {
    border: 1px solid #333;
    tr {
      min-height: 28px;
    }
    td {
      padding: 4px 6px;
      position: relative;
      border-top: 1px solid #333;
      border-right: 1px solid #333;
      div {
        color: #333;
        min-height: 24px;
        /deep/ .el-input {
          min-width: unset;
          width: 100%;
        }
        /deep/.el-radio {
          display: block;
        }
      }
      .addClass,
      .delClass {
        width: 1.5em;
        text-align: center;
        position: absolute;
        background: #28a7ff;
        padding: 4px 0;
        border-radius: 2px;
        cursor: pointer;
        color: #fff;
        top: -70px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
      }
      .addColumn,
      .delColumn {
        width: 1.5em;
        text-align: center;
        position: absolute;
        background: #28a7ff;
        padding: 4px 0;
        border-radius: 2px;
        cursor: pointer;
        color: #fff;
        bottom: -100px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 12px;
      }
      .delClass,
      .delColumn {
        background: #e55;
      }
    }
  }
  .maintain-name {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #333;
    padding-top: 10px;
  }
}
</style>
