<template>
  <div class="equipment-dictionary-box">
    <ts-search-bar-new
      ref="TsSearchBar"
      v-model="searchForm"
      :formList="searchList"
      @search="refresh"
    >
      <template slot="brand">
        <base-select
          style="width: 100%"
          v-model="searchForm.brandId"
          :inputText.sync="searchForm.brandName"
          :loadMethod="getBrandList"
          label="name"
          value="id"
          searchInputName="name"
        />
      </template>

      <template slot="category">
        <input-tree
          :spSearch="true"
          v-model="searchForm.category22Id"
          placeholder="请选择医疗器械分类"
          :treeData="categoryTreeData"
          :defaultExpandAll="false"
          key="category22Id"
        />
      </template>

      <template slot="assetCategory">
        <input-tree
          :spSearch="true"
          v-model="searchForm.categoryId"
          placeholder="请选择固定资产分类"
          :treeData="deviceClassifyTree"
          :defaultExpandAll="false"
        />
      </template>
    </ts-search-bar-new>

    <div class="flex-end mtb8">
      <div>
        <ts-button class="shallowButton" type="primary" @click="handleAdd">
          新增
        </ts-button>
        <ts-button class="shallowButton" type="primary" @click="handleImport">
          导入
        </ts-button>
        <ts-button class="shallowButton" type="primary" @click="handleExport">
          导出
        </ts-button>
      </div>
    </div>

    <ts-vxe-base-table
      id="table_equipment_dictionary"
      ref="table"
      :columns="columns"
      @refresh="handleRefreshTable"
    />

    <DialogDictionaryDetail
      :categoryTreeData="categoryTreeData"
      :deviceClassifyTree="deviceClassifyTree"
      :amsSkuType="amsSkuType"
      @submit="refresh"
      ref="dialogDictionaryDetail"
    />
    <DialogImportDictionary ref="dialogImportDictionary" @submit="refresh" />
    <base-import
      ref="baseImport"
      @refresh="refresh"
      :ImportConfiguration="ImportConfiguration"
    />
  </div>
</template>

<script>
import { cycleTimeUnit, calibrationType } from '@/assets/js/constants.js';
import InputTree from '@/components/input-tree/index.vue';
import DialogDictionaryDetail from './components/dialog-dictionary-detail.vue';
import DialogImportDictionary from './components/dialog-import-dictionary.vue';
export default {
  components: { InputTree, DialogDictionaryDetail, DialogImportDictionary },
  data() {
    return {
      loading: false,
      categoryTreeData: [],
      deviceClassifyTree: [],
      amsSkuType: [],

      searchForm: {},
      searchList: [
        {
          label: '字典名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入字典名称'
          },
          event: {
            change: () => {
              this.refresh();
            }
          }
        },
        {
          label: '资产类别',
          value: 'skuType',
          element: 'ts-select',
          elementProp: {
            placeholder: '请选择资产类别',
            filterable: true,
            clearable: true
          },
          childNodeList: []
        },
        {
          label: '固定资产分类',
          value: 'assetCategory'
        },
        {
          label: '医疗器械分类',
          value: 'category'
        },
        {
          label: '规格型号',
          value: 'model',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入规格型号'
          },
          event: {
            change: () => {
              this.refresh();
            }
          }
        },
        {
          label: '资产品牌',
          value: 'brand'
        }
      ],
      columns: [
        { label: '序号', prop: 'pageIndex', width: 60, align: 'center' },
        { label: '字典名称', align: 'center', prop: 'name', minWidth: 140 },
        { label: '字典编码', align: 'center', prop: 'code', minWidth: 140 },
        { prop: 'skuTypeShow', label: '资产类别', align: 'center', width: 75 },
        {
          label: '固定资产分类',
          align: 'center',
          prop: 'categoryName',
          width: 105
        },
        {
          label: '医疗器械分类',
          align: 'center',
          minWidth: 140,
          prop: 'category22Name'
        },
        { label: '规格型号', align: 'center', width: 110, prop: 'model' },
        { label: '单位', align: 'center', width: 110, prop: 'unit' },
        {
          label: '资产品牌',
          align: 'center',
          minWidth: 140,
          prop: 'brandName'
        },
        {
          label: '资产厂家',
          align: 'center',
          minWidth: 140,
          prop: 'manufacturerName'
        },
        {
          label: '报废年限',
          align: 'center',
          width: 80,
          prop: 'lifespanVal',
          render: (h, { row }) => {
            if (!row.lifespanVal) return '';
            let unit = cycleTimeUnit.find(
              item => item.value == row.lifespanUnit
            );
            return (
              <span>
                {row.lifespanVal}
                {unit ? unit.label : ''}
              </span>
            );
          }
        },
        {
          label: '计量类型',
          align: 'center',
          width: 80,
          prop: 'calibrationType',
          render: (h, { row }) => {
            if (!row.calibrationCycleVal) return '';
            let unit = cycleTimeUnit.find(
              item => item.value == row.calibrationCycleUnit
            );
            return (
              <span>
                {
                  calibrationType.find(
                    item => item.value == row.calibrationType
                  ).label
                }
              </span>
            );
          }
        },
        {
          label: '计量周期',
          align: 'center',
          width: 80,
          prop: 'calibrationCycleVal',
          render: (h, { row }) => {
            if (!row.calibrationCycleVal) return '';
            let unit = cycleTimeUnit.find(
              item => item.value == row.calibrationCycleUnit
            );
            return (
              <span>
                {row.calibrationCycleVal}
                {unit ? unit.label : ''}
              </span>
            );
          }
        },
        {
          label: '保养周期',
          align: 'center',
          width: 80,
          prop: 'maintCycleVal',
          render: (h, { row }) => {
            if (!row.maintCycleVal) return '';
            let unit = cycleTimeUnit.find(
              item => item.value == row.maintCycleUnit
            );
            return (
              <span>
                {row.maintCycleVal}
                {unit ? unit.label : ''}
              </span>
            );
          }
        },
        {
          label: '创建时间',
          align: 'center',
          width: 140,
          prop: 'createDate',
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.createDate).format('YYYY-MM-DD HH:mm')}
              </span>
            );
          }
        },
        {
          label: '创建人',
          align: 'center',
          width: 80,
          prop: 'createUserName'
        },
        {
          label: '更新时间',
          align: 'center',
          width: 140,
          prop: 'updateDate',
          render: (h, { row }) => {
            return (
              <span>
                {this.$dayjs(row.updateDate).format('YYYY-MM-DD HH:mm')}
              </span>
            );
          }
        },
        {
          label: '更新人',
          align: 'center',
          width: 80,
          prop: 'updateUserName'
        },
        {
          label: '操作',
          align: 'center',
          width: 100,
          headerSlots: 'actions',
          prop: 'actions',
          fixed: 'right',
          render: (h, { row }) => {
            let actions = [
              {
                label: '编辑',
                event: this.handleEdit
              },
              {
                label: '删除',
                className: 'actionDel',
                event: this.handleDelete
              }
            ];
            return h(
              'BaseActionCell',
              {
                on: {
                  'action-select': e => e(row)
                },
                attrs: {
                  actions: actions
                }
              },
              this.$slots.default
            );
          }
        }
      ],

      ImportConfiguration: {
        importTempalteApi: '/ts-ams/api/device/sku/tpl',
        importTempalteName: '设备字典导入模板',
        importApi: 'importEquipmentDictionary'
      }
    };
  },

  created() {
    this.getCategoryTree();
    this.getDeviceClassifyTree();
    this.handleGetDicAMS_SKU_TYPE();
  },

  methods: {
    async getBrandList(data) {
      let res = await this.ajax.getBrandList({
        pageSize: 15,
        ...data
      });

      if (res.success == false) {
        this.$newMessage('error', res.message || '品牌数据获取失败!');
        return false;
      }
      return res.rows;
    },

    async getCategoryTree() {
      try {
        const res = await this.ajax.getClassificationTree();
        if (!res.success) {
          throw res.message;
        }
        this.categoryTreeData = res.object || [];
      } catch (e) {
        this.$newMessage('error', res.message || '分类数据获取失败!');
      }
    },

    async handleGetDicAMS_SKU_TYPE() {
      const res = await this.ajax.getDataByDataLibrary('AMS_SKU_TYPE');
      if (!res.success) {
        this.$newMessage('error', '字典数据获取失败!');
        return false;
      }
      let arr = (res.object || []).map(item => ({
        element: 'ts-option',
        label: item.itemName,
        value: item.itemNameValue
      }));
      this.amsSkuType = arr;
      this.searchList.find(f => f.value === 'skuType').childNodeList = [
        { element: 'ts-option', label: '全部', value: '' },
        ...arr
      ];
    },

    refresh() {
      if (this.$refs.TsSearchBar) this.$refs.TsSearchBar.showMoreSearch = false;

      this.$refs.table.pageNo = 1;
      this.$refs.table.triggerRefresh();
    },

    async getDeviceClassifyTree() {
      try {
        const res = await this.ajax.getDeviceClassifyTree();
        if (!res.success) {
          throw res.message;
        }
        this.deviceClassifyTree = res.object || [];
      } catch (e) {
        this.$newMessage('error', res.message || '分类数据获取失败!');
      }
    },

    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize
        };
      Object.keys(data).map(key => {
        if (data[key] === null || data[key] === undefined) {
          delete data[key];
        }
      });

      this.ajax.getEquipmentDictionary(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    },

    handleAdd() {
      this.$refs.dialogDictionaryDetail.show({
        type: 'add'
      });
    },

    handleEdit(row) {
      this.$refs.dialogDictionaryDetail.show({
        type: 'edit',
        data: row
      });
    },

    handleDelete(row) {
      this.$newConfirm(
        `【<span style="color: red">删除</span>】选中的数据？`,
        '提示',
        {
          type: 'warning'
        }
      ).then(() => {
        this.ajax.deleteEquipmentDictionary(row.id).then(res => {
          if (res.success && res.statusCode === 200) {
            this.refresh();
          } else {
            this.$newMessage('error', res.message || '操作失败!');
          }
        });
      });
    },

    handleImport() {
      // this.$refs.dialogImportDictionary.show();
      this.$refs.baseImport.open({
        title: '导入设备字典',
        increment: true,
        quantity: false
      });
    },

    handleExport() {
      let aDom = document.createElement('a'),
        conditionList = Object.keys(this.searchForm).map(key => {
          let val = this.searchForm[key];
          if (val == null || val == undefined) {
            val = '';
          }
          return `${key}=${val}`;
        });
      aDom.href = '/ts-ams/api/device/sku/export?' + conditionList.join('&');
      aDom.click();
    }
  }
};
</script>

<style lang="scss" scoped>
.equipment-dictionary-box {
  height: 100%;
  width: 100%;
  padding: 8px;
  background: #fff;
  overflow: hidden;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}
::v-deep .trasen-search-content {
  .left-search-content {
    .search-item {
      width: 315px;

      &:nth-child(n + 2) {
        > div {
          &:last-child {
            width: 100%;
          }
        }
      }
    }
  }
}
.mtb8 {
  margin: 8px 0;
}
.form-table {
  flex: 1;
  overflow: hidden;
}
</style>
