export default {
  bind(el, binding, vnode) {
    let input = el.querySelector('.el-input__inner');
    if (!input && el.classList.contains('el-input__inner')) {
      input = el;
    }

    if (!input) {
      console.warn('enterNextInput directive: No input element found');
      return;
    }

    // 存储事件处理函数以便后续解绑
    const handleKeyDown = e => {
      if (e.key !== 'Enter') return;

      e.preventDefault();

      const inputs = Array.from(
        document.querySelectorAll('.el-input__inner[enter-next-input="true"]')
      );
      const currentIndex = inputs.indexOf(input);

      if (currentIndex === -1) return;
      if (binding?.value?.enterSubmit) {
        binding.value.enterSubmit();
      }

      if (currentIndex === inputs.length - 1) {
        input.blur();
      } else {
        inputs[currentIndex + 1].focus();
      }
    };

    // 保存事件处理函数的引用
    el.__enterNextHandler__ = handleKeyDown;

    input.setAttribute('enter-next-input', 'true');
    input.addEventListener('keydown', handleKeyDown);
  },

  unbind(el) {
    const input = el.querySelector('.el-input__inner');
    if (!input) return;

    input.removeAttribute('enter-next-input');
    if (el.__enterNextHandler__) {
      input.removeEventListener('keydown', el.__enterNextHandler__);
      delete el.__enterNextHandler__;
    }
  }
};
