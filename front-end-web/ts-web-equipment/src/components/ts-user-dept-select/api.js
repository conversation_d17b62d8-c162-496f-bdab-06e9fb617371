import { request } from '@/api/ajax';

const getDeptTreeList = function() {
  return request({
    url: `/ts-oa/thpsSysetm/getDeptTreeList`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    }
  });
};

const getOrgGroupTree = function(params) {
  return request({
    url: `/ts-basics-bottom/employee/orgGroup/getOrgGroupTree`,
    method: 'get',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8'
    },
    params
  });
};

const getEmployeeList = function(params, data) {
  return request({
    url: '/ts-oa/employee/list',
    method: 'post',
    params: params,
    data: data
  });
};

const getOrgGroupUser = function(params) {
  return request({
    url: '/ts-basics-bottom/employee/orgGroup/getOrgGroupUser',
    method: 'get',
    params: params
  });
};

/**@desc 获取数据字典 */
const getDataByDataLibrary = function(data) {
  return request({
    url: `/ts-basics-bottom/dictItem/getDictItemByTypeCode?typeCode=${data}`,
    method: 'get'
  });
};

export default {
  getDeptTreeList,
  getOrgGroupTree,
  getEmployeeList,
  getOrgGroupUser,
  getDataByDataLibrary
};
