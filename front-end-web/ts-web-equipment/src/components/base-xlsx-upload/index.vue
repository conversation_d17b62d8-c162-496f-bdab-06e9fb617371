<template>
  <div class="flex-grow">
    <ts-upload
      class="base-uplaod"
      ref="tsUpload"
      action=""
      :limit="limit"
      :fileList.sync="fileList"
      :show-file-list="false"
      :accept="accept"
      :http-request="handleUploadFile"
      :on-exceed="masterFileMax"
    >
      <ts-button>{{ fileText }}</ts-button>
      <div class="el-upload__tip">
        {{ tips }}
      </div>
    </ts-upload>
    <ts-upload-file-list
      :fileList.sync="fileList"
      :on-remove="onRemove"
      :on-abort="onAbort"
      :on-upload="onUpload"
      :onPreview="onPreview"
      :type="uploadFileListType"
    ></ts-upload-file-list>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import { commonUtils } from '@/util/common.js';

export default {
  model: {
    prop: 'fileId',
    event: 'input'
  },
  props: {
    fileId: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => '只能上传jpg/png文件，且不超过500kb'
    },
    fileText: {
      type: String,
      default: () => '文件上传'
    },
    limit: {
      type: Number,
      default: 99
    },
    accept: String,
    fileListType: String,
    getLocalFile: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      fileList: [],
      innerId: '',
      previewFile: '',
      previewFileList: []
    };
  },
  watch: {
    fileId: {
      handler(val) {
        if (val && val !== this.innerId && !this.getLocalFile) {
          this.getFileList();
        }
      },
      immediate: true
    }
  },
  computed: {
    uploadFileListType() {
      return (
        this.fileListType ||
        { image: 'picture', file: 'text' }[this.accept] ||
        'mixture'
      );
    }
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .recruitGetFileList({
          ids: this.fileId
        })
        .then(res => {
          this.fileList = res.object.map(item => ({
            ...item,
            fileName: `${item.id}.${item.fileExtension}`,
            fileRealName: item.originalName,
            fileId: item.id,
            uid: item.id,
            url: item.realPath,
            name: item.originalName,
            status: 'success'
          }));
        });
    },
    handleUploadFile(params) {
      let data = new FormData();
      data.append('file', params.file);
      if (this.getLocalFile) {
        this.$emit('SendLoaclFile', params.file);
        return false;
      }

      this.ajax.recruitUploadFileXlsx(data).then(res => {
        if (res.success) {
          this.fileList = [
            {
              ...res.object[0],
              realPath: res.object[0].filePath,
              url: res.object[0].filePath,
              uid: res.object[0].fileId,
              name: res.object[0].fileRealName
            }
          ];
          this.$emit('input', res.object[0].fileId);
        } else {
          this.$message.error(res.message || '上传失败');
        }
      });
    },
    /**@desc 删除 */
    onRemove(file) {
      let idx = this.fileList.findIndex(e => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      this.ajax.recruitDeleteFile(deleteFile.uid).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);

        if (!this.fileList.length) {
          this.$emit('input', '');
        }
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex(e => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = file.url;
      a.target = '_blank';
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (commonUtils.isDoc(file.fileName)) {
        commonUtils.viewerDocBase(file.realPath, file.fileName);
      }
    },
    masterFileMax() {
      this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
    }
  }
};
</script>

<style lang="scss">
.base-uplaod {
  .el-upload {
    text-align: left !important;
  }

  .picture-btn {
    width: 120px;
    height: 60px !important;

    img {
      width: 50px;
      height: 50px;
    }
  }
}
</style>
