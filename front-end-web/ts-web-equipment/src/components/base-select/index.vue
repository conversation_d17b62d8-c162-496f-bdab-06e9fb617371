<template>
  <div class="base-select-content">
    <el-popover
      ref="popover"
      popper-class="base-select-popover"
      placement="bottom-start"
      trigger="click"
      :disabled="disabled"
      :width="popoverWidth"
      @show="handlePopoverShow"
      @hide="handlePopoverHide"
    >
      <template slot="reference">
        <ts-input
          v-model="insideText"
          :disabled="disabled || !searchable"
          :placeholder="isFocus ? inputText : placeholder"
          class="search-input"
          ref="insideText"
          :class="{
            'is-focus': isFocus,
            'can-not-search': !searchable
          }"
          @input="handleInput"
          @focus="hanldeFocus"
          @keydown.native.enter.prevent="selectOption"
        >
          <span slot="suffix" class="action-suffix-content">
            <i
              class="el-input__icon el-icon-arrow-down"
              :class="{
                'has-clearable': clearable && insideText
              }"
            ></i>
            <i
              class="el-input__icon el-icon-circle-close"
              @click.stop="clearInput"
              v-if="!disabled && clearable && insideText"
            ></i>
          </span>
        </ts-input>
      </template>
      <div ref="templateContent" class="options-content">
        <el-scrollbar
          ref="scroll"
          :style="{
            height: '200px'
          }"
          v-infinity-scroll="{
            loadMethod: handleLoadData,
            selector: '.options-scrollbar',
            hasFinished: true,
            hasLoading: true,
            immediate: immediateLoadFn
          }"
          wrap-class="options-scrollbar"
          wrap-style="overflow-x: hidden;"
        >
          <ul ref="templateList" style="margin: 0;padding: 0;">
            <li
              v-for="(item, index) of options"
              :key="index"
              class="option-item flex-row-between flex-col-center"
              :class="{
                'active-option-item': modelValue == item[value]
              }"
              @click="handleSelect(item)"
            >
              <slot :data="item">
                {{ item[label] }}
              </slot>
              <span v-show="item.pdItem" class="pd-item">点击新增</span>
              <i class="el-icon-check"></i>
            </li>
          </ul>
        </el-scrollbar>
      </div>
    </el-popover>
  </div>
</template>

<script>
/**
 * @param {String} placeholder 当选择框值为空时 input 的 placeholder
 * @param {Function} loadMethod 加载数据的方法
 * @param {String} inputText 选中的值的文本显示
 * @param [Number, String] delay 输入防抖时间
 * @param String searchInputName 搜索时的input
 * @param String label 选项展示的内容名称
 * @param String value 选项选中的值名称
 * @param Boolean canQuit 再次点击是否可以取消选择
 * @param Boolean searchable 是否可以搜索
 * @param Boolean allowCreate 是否允许创建新条目，需配合 searchable 使用
 */
import infinityScroll from './infinityScroll';

export default {
  mixins: [infinityScroll],
  model: {
    prop: 'modelValue',
    event: 'input'
  },
  props: {
    modelValue: {},
    placeholder: {
      type: String,
      default: () => '请选择'
    },
    loadMethod: Function,
    inputText: String,
    delay: {
      type: [Number, String],
      default: () => 500
    },
    searchInputName: String,
    label: {
      type: String,
      default: () => 'name'
    },
    value: {
      type: String,
      default: () => 'id'
    },
    disabled: {
      type: Boolean,
      default: () => false
    },
    clearable: {
      type: Boolean,
      default: () => true
    },
    canQuit: Boolean,
    searchable: {
      type: Boolean,
      default: () => true
    },
    allowCreate: {
      type: Boolean,
      default: () => false
    },
    data: Array,
    // 是否立即加载数据
    immediateLoadFn: {
      type: Boolean,
      default: () => true
    },
    // 其他搜索条件
    otherSearchParams: Object
  },
  data() {
    return {
      wrapDom: null,
      insideText: '',
      insideValue: '',
      insidePlaceHolder: '',
      pageNo: 1,
      options: [],

      inputTimer: null,
      isFocus: false,
      popoverWidth: null,
      hasNewOption: false
    };
  },
  watch: {
    inputText: {
      handler(val) {
        this.insideText = val;
        this.insidePlaceHolder = val;
      },
      immediate: true
    },
    modelValue: {
      handler(val) {
        this.insideValue = val;
      },
      immediate: true
    }
  },
  async mounted() {
    await this.$nextTick();
    this.wrapDom = this.$refs.scroll.$el.querySelector('.options-scrollbar');
    this.popoverWidth = this.$refs.insideText.$el.clientWidth;
  },
  methods: {
    async handleLoadData(cb) {
      this.hasNewOption = false;
      if (this.loadMethod) {
        let data = { pageNo: this.pageNo };
        data[this.searchInputName || 'name'] = this.insideText;
        Object.assign(data, this.otherSearchParams);

        let res = await this.loadMethod(data);

        if (this.pageNo == 1) {
          this.options = [];
          if (this.searchable && this.allowCreate && !(res && res.length)) {
            this.hasNewOption = true;
            this.options.push({
              [this.label]: this.insideText,
              [this.value]: this.insideText,
              pdItem: true
            });
            cb(false);
            return;
          }
        }
        cb(!(res && res.length));
        if (res && res.length) {
          this.options.push(...res);
        }
        this.pageNo++;
      } else {
        let options = (this.data || []).filter(item => {
          return item[this.label].includes(this.insideText);
        });
        if (this.searchable && this.allowCreate && !options.length) {
          this.hasNewOption = true;
          options.push({
            [this.label]: this.insideText,
            [this.value]: this.insideText,
            pdItem: true
          });
        }
        this.options = options;
        cb(!options.length);
      }
    },
    handlePopoverShow() {
      this.pageNo = 1;
      this.options = [];
      this.isFocus = true;
      this.insideText = '';
      this.wrapDom.resetInfinityScrolling();
    },
    handlePopoverHide() {
      this.isFocus = false;
      this.insideText = this.inputText;
      this.$refs.insideText.blur();
    },
    /**@desc 输入防抖 */
    handleInput() {
      this.inputTimer && clearTimeout(this.inputTimer);
      this.inputTimer = setTimeout(() => {
        this.pageNo = 1;
        this.options = [];
        this.wrapDom.resetInfinityScrolling();
      }, Number(this.delay));
    },
    /**@desc focus 事件 */
    hanldeFocus() {
      this.insideText = '';
    },
    handleSelect(item) {
      let value = item[this.value],
        selectedVal = '';
      if (value == this.modelValue) {
        if (this.canQuit) {
          this.$emit('input', null);
          this.$emit('update:inputText', null);
        }
      } else {
        selectedVal = value;
        this.$emit('input', value);
        this.$emit('update:inputText', item[this.label]);
        if (this.hasNewOption) {
          this.$emit('createOption', item, id => {
            this.$emit('input', id);
          });
          this.hasNewOption = false;
        }
      }
      this.$emit('select', item, this.modelValue, selectedVal);
      this.$refs.popover.doClose();
    },
    clearInput() {
      if (this.isFocus) {
        this.insideText = '';
        this.pageNo = 1;
        this.options = [];
        this.$refs.insideText.focus();
        this.wrapDom.resetInfinityScrolling();
      } else {
        this.$emit('input', null);
        this.$emit('update:inputText', null);
        this.$refs.popover.doClose();
      }
    },
    selectOption() {
      if (this.isFocus) {
        if (this.options.length) {
          this.handleSelect(this.options[0]);
          // if (this.hasNewOption) {
          //   this.$emit('createOption', this.options[0]);
          //   this.hasNewOption = false;
          // }
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.base-select-content {
  display: inline-block;
}
.option-item {
  line-height: 30px;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  padding: 0 $primary-spacing;
  &:hover {
    background-color: $list-hover-color;
  }
  .el-icon-check {
    font-weight: inherit;
    display: none;
    flex-shrink: 0;
  }

  &.active-option-item {
    color: $primary-blue;
    font-weight: 600;
    .el-icon-check {
      display: inline-block;
    }
  }
}
.search-input {
  &.can-not-search.is-focus .el-icon-arrow-down,
  &.is-focus .el-icon-arrow-down {
    transform: rotate(180deg);
  }
  .action-suffix-content .el-icon-circle-close {
    display: none;
    cursor: pointer;
  }
  &:hover {
    .action-suffix-content .el-icon-circle-close {
      display: inline-block;
    }
    .el-icon-arrow-down.has-clearable {
      display: none;
    }
  }
  /deep/ &.can-not-search {
    &.is-focus .el-input__inner {
      border-color: $primary-blue !important;
    }
    .el-input__inner {
      cursor: pointer !important;
      background-color: #fff !important;
    }
  }
}
</style>

<style lang="scss">
.base-select-popover {
  padding: $primary-spacing 0;

  .pd-item {
    padding: 4px 6px;
    border-radius: 4px;
    background-color: #d2dcfc !important;
    color: #295cf9 !important;
    border: none !important;
  }
}
</style>
