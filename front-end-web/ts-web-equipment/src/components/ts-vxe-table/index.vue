<template>
  <div class="ts-vxe-table-box">
    <vxe-table
      ref="tsVxeTable"
      v-bind="{ ...$attrs, ...$props }"
      v-on="$listeners"
      :id="id"
    >
      <template v-for="(item, itemIndex) in columns">
        <table-child-column
          v-if="item.children && item.children.length"
          :column="item"
          :key="itemIndex"
        ></table-child-column>
        <vxe-table-column
          v-else
          show-header-overflow
          :show-overflow="
            JSON.stringify(item.showOverflow) === 'false' ? false : 'tooltip'
          "
          v-bind="item"
          :field="item.prop"
          :title="item.label"
          :key="item.prop"
        >
          <template
            v-if="item.slotName || item.prop || item.label || item.render"
            #default="scope"
          >
            <!-- 默认 -->
            <span v-if="!item.slotName && !item.render">
              {{ scope.row[item.prop] }}
            </span>
            <!-- 自定义插槽 -->
            <slot
              :name="item.slotName"
              v-if="item.slotName"
              :row="scope.row"
              :index="scope.rowIndex"
            ></slot>
            <!-- 渲染形式 -->
            <component-render
              v-if="item.render && !item.slotName"
              :renderContent="item.render"
              :scope="scope"
            ></component-render>
          </template>

          <template v-if="item.headerRender" #header>
            <component-render
              :renderContent="item.headerRender"
            ></component-render>
          </template>
          <table-child-column
            v-for="(c_item, index) in item.children"
            :column="c_item"
            :key="index"
          ></table-child-column>
        </vxe-table-column>
      </template>
    </vxe-table>
  </div>
</template>

<script>
import ComponentRender from './component-render.vue';
import TableChildColumn from './table-child-column.vue';
import Sortable from 'sortablejs';
export default {
  name: 'ts-vxe-table',
  components: {
    ComponentRender,
    TableChildColumn
  },
  props: {
    // 表格列项
    columns: {
      type: Array,
      default: () => []
    },
    id: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      sortable: null
    };
  },
  mounted() {
    if (this.columns) {
      // 显示表头
      this.columnDrop();
    }
  },
  methods: {
    columnDrop() {
      this.$nextTick(() => {
        const _this = this;
        let xTable = this.$refs.tsVxeTable;
        if (xTable) {
          this.sortable = Sortable.create(
            xTable.$el.querySelector(
              '.body--wrapper>.vxe-table--header .vxe-header--row'
            ),
            {
              handle: '.vxe-header--column:not(.col--fixed)',
              onEnd: ({ item, newIndex, oldIndex }) => {
                let {
                  fullColumn,
                  tableColumn,
                  collectColumn
                } = xTable.getTableColumn();
                let targetThElem = item;
                let wrapperElem = targetThElem.parentNode;
                let newColumn = collectColumn[newIndex];
                if (newColumn.fixed) {
                  // 错误的移动
                  if (newIndex > oldIndex) {
                    wrapperElem.insertBefore(
                      targetThElem,
                      wrapperElem.children[oldIndex]
                    );
                  } else {
                    wrapperElem.insertBefore(
                      wrapperElem.children[oldIndex],
                      targetThElem
                    );
                  }
                  this.$utils.msg(this, '固定列不允许拖动！', 'error');
                }
                // 转换真实索引
                let oldColumnIndex = xTable.getColumnIndex(
                  collectColumn[oldIndex]
                );
                let newColumnIndex = xTable.getColumnIndex(
                  collectColumn[newIndex]
                );
                // 移动到目标列
                let currRow = collectColumn.splice(oldColumnIndex, 1)[0];
                collectColumn.splice(newColumnIndex, 0, currRow);
                xTable.loadColumn(collectColumn);
                // // 触发列数据
                let currCol = _this.element.cols.splice(oldIndex - 1, 1)[0];
                _this.element.cols.splice(newIndex - 1, 0, currCol);
                // 发监听
                _this.$emit('columnDrop', oldIndex, newIndex);
              }
            }
          );
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-vxe-table-box {
  position: relative;
  width: 100%;
  height: 100%;
}
</style>
