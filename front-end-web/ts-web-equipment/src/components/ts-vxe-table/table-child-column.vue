<template>
  <vxe-colgroup
    header-class-name="ts-colgroup"
    v-bind="column"
    :field="item.prop"
    :title="item.label"
    :width="
      !isNaN(Number(column.width)) ? Number(column.width) + 20 : column.width
    "
  >
    <template v-if="column.children && column.children.length">
      <table-child-column
        v-for="(c_item, index) in column.children"
        :column="c_item"
        :key="index"
      ></table-child-column>
    </template>
  </vxe-colgroup>
</template>

<script>
export default {
  name: 'table-child-column',
  props: {
    column: {
      default: () => []
    }
  },
  data: () => {
    return {};
  }
};
</script>
