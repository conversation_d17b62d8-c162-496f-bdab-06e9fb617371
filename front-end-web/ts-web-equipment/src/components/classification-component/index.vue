<template>
  <div class="common-classification-container">
    <div class="content">
      <div class="left">
        <new-base-search-tree
          class="node-tree"
          ref="searchTree"
          parentId="pid"
          :title="renderSetting.title"
          :apiFunction="apiFunction"
          :activeId="this.treeNode ? this.treeNode.id : ''"
          placeholder="请输入分类名称进行搜索"
          @beforeClick="clickItemTree"
        >
          <template #actionBar>
            <div class="classAction">
              <ts-button type="primary" @click="handleAddClassification">
                新增
              </ts-button>
              <ts-button
                type="primary"
                class="edit"
                :disabled="treeNode?.id === allId"
                @click="handleEditClassification"
              >
                修改
              </ts-button>
              <ts-button
                type="primary"
                class="del"
                :disabled="treeNode?.id === allId"
                @click="handleDeleteClassification"
              >
                删除
              </ts-button>
            </div>
          </template>
        </new-base-search-tree>
      </div>
      <div class="right">
        <ts-search-bar-new
          v-model="searchForm"
          :formList="searchList"
          @search="search"
          @reset="reset"
        >
          <template slot="isEnable">
            <ts-radio-group v-model="searchForm.isEnable" @change="search">
              <ts-radio label="">全部</ts-radio>
              <ts-radio label="1">启用</ts-radio>
              <ts-radio label="2">禁用</ts-radio>
            </ts-radio-group>
          </template>
        </ts-search-bar-new>

        <ts-vxe-base-table
          :id="`table_equipment_${renderType}_classification`"
          ref="table"
          :columns="columns"
          @refresh="handleRefreshTable"
        />
      </div>
    </div>

    <dialog-add-class
      ref="dialogAddClass"
      :renderSetting="renderSetting"
      @refresh="refresh"
    />
  </div>
</template>

<script>
import { primaryBlue } from '@/styles/variables.scss';
import DialogAddClass from './component/dialog-add-class.vue';
export default {
  name: 'AssetClassification',
  props: {
    renderType: {
      type: String,
      default: 'asset'
    }
  },
  components: {
    DialogAddClass
  },
  data() {
    return {
      allId: '99999',
      searchForm: {
        isEnable: '',
        name: ''
      },
      searchList: [
        {
          label: '分类名称',
          value: 'name',
          element: 'ts-input',
          elementProp: {
            placeholder: '请输入分类名称'
          },
          event: {
            change: () => {
              this.search();
            }
          }
        },
        {
          label: '状态',
          value: 'isEnable'
        }
      ],

      columns: [
        { label: '序号', prop: 'pageIndex', width: 60, align: 'center' },
        { label: '分类编码', align: 'center', prop: 'code', width: 120 },
        { label: '分类名称', align: 'center', prop: 'name' },
        { label: '首拼码', align: 'center', prop: 'sp', width: 100 },
        { label: '全拼码', align: 'center', prop: 'qp', width: 150 },
        {
          label: '上级分类编码',
          align: 'center',
          prop: 'parentCode',
          width: 120
        },
        { label: '上级分类名称', align: 'center', prop: 'parentName' },
        {
          label: '状态',
          align: 'center',
          prop: 'isEnable',
          width: 60,
          render: (h, { row }) => {
            return h('ts-switch', {
              attrs: {
                value: row['isEnable'],
                activeValue: '1',
                inactiveValue: '2'
              },
              on: {
                change: () => {
                  this.handleChangeItemStatus(row, 'isEnable');
                }
              }
            });
          }
        }
      ],

      apiFunction: null,
      treeNode: null
    };
  },
  computed: {
    renderSetting() {
      let dic = {
        asset: {
          title: '固定资产分类',
          treeApi: this.ajax.getDeviceClassifyTree,
          saveApi: this.ajax.deviceClassifyTreeSave,
          updateApi: this.ajax.deviceClassifyTreeUpdate,
          deleteApi: this.ajax.deviceClassifyTreeDelete,
          detailsApi: this.ajax.deviceClassifyTreeDetails,
          listApi: this.ajax.deviceCategoryList
        },
        material: {
          title: '物资分类',
          treeApi: this.ajax.getMaterialClassifyTree,
          saveApi: this.ajax.materialClassifyTreeSave,
          updateApi: this.ajax.materialClassifyTreeUpdate,
          deleteApi: this.ajax.materialClassifyTreeDelete,
          detailsApi: this.ajax.materialClassifyTreeDetails,
          listApi: this.ajax.materialCategoryList
        }
      };
      return dic[this.renderType];
    }
  },
  created() {
    this.apiFunction = this.handleGetClassificationTree;
  },
  methods: {
    // 优化后的：提供给树组件的接口
    async handleGetClassificationTree() {
      try {
        const res = await this.renderSetting.treeApi();
        if (!res.success) {
          this.$newMessage('error', res.message || '获取分类列表失败');
          return {
            success: false,
            object: [],
            statusCode: 500
          };
        }

        const treeData = Array.isArray(res.object) ? res.object : [];
        const allNode = {
          id: this.allId,
          name: '全部',
          open: true,
          children: treeData.map(item => ({
            ...item,
            pid: this.allId
          }))
        };

        // 递归展开所有节点
        const expandAllNodes = (nodes = []) => {
          nodes.forEach(node => {
            if (Array.isArray(node.children) && node.children.length) {
              node.open = true;
              expandAllNodes(node.children);
            }
          });
        };
        expandAllNodes([allNode]);

        return {
          object: treeData.length ? [allNode] : [],
          success: true,
          statusCode: 200
        };
      } catch (error) {
        this.$newMessage('error', '获取分类列表异常');
        return {
          success: false,
          object: [],
          statusCode: 500
        };
      }
    },

    async refresh() {
      await this.$refs.searchTree.getTreeData();
      this.search();

      this.$nextTick(() => {
        // 初始化 如果没有选中Tree节点 则选中全部节点
        if (!this.treeNode) this.handleSetTreeFirstNode();
      });
    },

    // 选中全部节点
    handleSetTreeFirstNode() {
      let tree = this.$refs.searchTree?.treeClass;
      if (tree) {
        let node = tree?.getNodeByParam('id', this.allId);
        tree?.selectNode(node);
        this.treeNode = node;
      }
    },

    search() {
      this.$refs.table.pageNo = 1;
      this.handleRefreshTable();
    },

    reset() {
      this.searchForm = {
        isEnable: '',
        name: ''
      };
      this.handleSetTreeFirstNode();
    },

    // 树 item点击
    clickItemTree(select) {
      this.treeNode = select;
      this.search();
    },

    // 新增分类
    handleAddClassification() {
      let data = {
        data: this.treeNode
      };
      if (this.treeNode && this.treeNode.id !== this.allId) {
        data.parentId = this.treeNode.id;
        data.parentName = this.treeNode.name;
        data.parentCode = this.treeNode.code;
      }
      this.$refs.dialogAddClass.open({
        type: 'add',
        data,
        treeData: this.$refs.searchTree?.treeData || []
      });
    },

    // 修改分类
    async handleEditClassification() {
      if (!this.treeNode) {
        this.$newMessage('warning', '请选择需要【修改】的节点');
        return;
      }
      let res = await this.renderSetting.detailsApi(this.treeNode.id);
      if (!res.success) {
        let tips = '获取详情信息失败，请联系管理员!';
        this.$newMessage('error', res.message || tips);
        return;
      }

      this.$refs.dialogAddClass.open({
        data: res.object,
        type: 'edit',
        treeData: this.$refs.searchTree?.treeData || []
      });
    },

    async handleChangeItemStatus(data) {
      try {
        const isEnable = data['isEnable'] === '1';
        const actionText = isEnable ? '禁用' : '启用';
        const color = isEnable ? 'red' : primaryBlue;
        const confirmMsg = `确定【<span style="color: ${color}">${actionText}</span>】当前选中的记录吗?`;

        await this.$newConfirm(confirmMsg);

        const payload = { ...data, isEnable: isEnable ? '2' : '1' };
        const res = await this.renderSetting.updateApi(payload);

        if (!res.success) {
          this.$newMessage('error', res.message || `【${actionText}】失败!`);
          return;
        }
        this.$newMessage('success', `【${actionText}】成功!`);
        this.handleRefreshTable();
      } catch (e) {
        console.error(e);
      }
    },

    // 删除分类
    async handleDeleteClassification() {
      try {
        if (!this.treeNode) {
          this.$newMessage('warning', '请选择需要【删除】的节点');
          return;
        }

        await this.$newConfirm(
          `确认<span style="color: red">【删除】</span>当前选中的记录吗？`
        );
        const res = await this.renderSetting.deleteApi(this.treeNode.id);
        if (!res.success) {
          this.$newMessage('error', res.message || '【删除】失败!');
          return;
        }
        this.$newMessage('success', '【删除】成功!');
        this.handleSetTreeFirstNode();
        this.refresh();
      } catch (e) {
        console.error(e);
      }
    },

    handleRefreshTable(page = {}) {
      let { pageNo = 1, pageSize = 100 } = page,
        data = {
          ...this.searchForm,
          pageNo,
          pageSize,
          parentId: this.treeNode?.id || ''
        };
      Object.keys(data).map(key => {
        if (data[key] === null || data[key] === undefined) {
          delete data[key];
        }
      });

      if (data.parentId === this.allId) delete data.parentId;

      this.renderSetting.listApi(data).then(res => {
        if (res.success == false) {
          this.$newMessage('error', res.message || '表格数据获取失败!');
          return;
        }
        let rows = res.rows.map((item, index) => {
          return {
            ...item,
            pageIndex: index + 1 + (pageNo - 1) * pageSize
          };
        });
        this.$refs.table.refresh({
          ...res,
          rows
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.common-classification-container {
  width: 100%;
  height: 100%;
  background: #fff;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .el-radio-group {
    label {
      margin-right: 4px;
    }
  }
  ::v-deep {
    .content {
      display: flex;
      height: 100%;
      padding: 8px;

      .left {
        width: 216px;
        height: 100%;
        margin-right: 8px;
        background: #fff;
        .classAction {
          margin: 8px 0 0 8px;
          display: flex;
          .share {
            margin-left: 0;
            margin-top: 4px;
          }
          .refresh {
            margin-top: 4px;
            margin-left: 8px;
          }
        }
      }
      .right {
        padding: 12px 8px;
        border-radius: 3px;
        border: 1px solid#295cf9;
        flex: 1;
        height: 100%;
        background: #fff;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        position: relative;
      }
    }
  }
}
</style>
