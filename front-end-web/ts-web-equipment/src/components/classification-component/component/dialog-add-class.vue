<!-- 操作记录 -->
<template>
  <vxe-modal
    className="dialog-add-classification"
    width="400"
    v-model="visible"
    :title="title"
    showFooter
  >
    <template #default>
      <ts-form ref="form" :model="form" labelWidth="110px">
        <ts-form-item prop="parentName" label="上级分类">
          <ts-ztree-select
            :data="treeData"
            :inpText.sync="form.parentName"
            :inpVal.sync="form.parentId"
            style="width: 100%;"
            placeholder="请选择"
            :disabled="isEdit"
          />
        </ts-form-item>

        <ts-form-item label="分类编码">
          <ts-input v-model="form.code" placeholder="系统自动生成" disabled />
        </ts-form-item>

        <ts-form-item prop="name" label="分类名称" :rules="rules.required">
          <ts-input
            v-model="form.name"
            placeholder="请输入分类名称"
            maxlength="50"
          />
        </ts-form-item>

        <ts-form-item label="排序">
          <ts-input
            v-model="form.seqNo"
            placeholder="请输入排序"
            maxlength="4"
            @input="value => (form.seqNo = (value.match(/\d+/g) || [''])[0])"
          />
        </ts-form-item>

        <ts-form-item label="首拼码">
          <ts-input v-model="form.sp" placeholder="系统自动生成" disabled />
        </ts-form-item>

        <ts-form-item label="全拼码" disabled>
          <ts-input v-model="form.qp" placeholder="系统自动生成" disabled />
        </ts-form-item>
      </ts-form>
    </template>

    <template #footer>
      <span slot="footer" class="dialog-footer">
        <ts-button type="primary" @click="submit">确 定</ts-button>
        <ts-button class="shallowButton" @click="close">关 闭</ts-button>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import cloneDeep from 'lodash-es/cloneDeep';
export default {
  props: {
    renderSetting: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      visible: false,
      type: '',

      treeData: [],
      form: {},
      rules: {
        required: { required: true, message: '必填' }
      }
    };
  },
  computed: {
    isEdit() {
      return this.type === 'edit';
    },
    title() {
      return this.isEdit ? '修改分类' : '新增分类';
    }
  },
  methods: {
    async open({ data, type, treeData }) {
      this.type = type;
      this.treeData = treeData;
      this.$set(this, 'form', cloneDeep(data));

      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form?.clearValidate();
      });
    },

    async submit() {
      try {
        await this.$refs.form.validate();
        let formData = cloneDeep(this.form);
        this.submitLoading = true;
        let API = this.renderSetting.saveApi;
        if (this.isEdit) {
          API = this.renderSetting.updateApi;
        } else {
          formData.isEnable = '2';
        }
        const res = await API(formData);
        if (!res.success) {
          this.submitLoading = false;
          this.$newMessage('error', res.message || `【${this.title}】失败!`);
          return;
        }

        this.submitLoading = false;
        this.$newMessage('success', `【${this.title}】成功`);
        this.$emit('refresh');
        this.close();
      } catch (error) {
      } finally {
        this.submitLoading = false;
      }
    },

    close() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.dialog-add-classification {
  .tips {
    padding-left: 110px;
    color: red;
    margin: 0;
  }
}
</style>
