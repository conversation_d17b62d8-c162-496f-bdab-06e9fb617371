<template>
  <div class="search-tree-box flex-column" v-loading="loading">
    <div class="search-tree-input flex-space border-bottom">
      <span class="title">
        {{ title }}
      </span>
    </div>
    <slot name="actionBar"></slot>
    <div class="search-tree-input flex-space" v-if="showInput">
      <ts-input
        v-model="searchVal"
        @input="searchInput"
        clearable
        @clear="searchInput"
        :placeholder="placeholder"
      >
        <i slot="suffix" class="el-input__icon el-icon-search"></i>
      </ts-input>
    </div>
    <el-scrollbar
      class="flex-column tree-scrollbar"
      style="flex: 1;margin-top: 5px;padding-bottom: 14px;"
      wrap-style="overflow-x: hidden;"
    >
      <ts-ztree
        v-if="treeData.length > 0"
        class="tree"
        ref="tsTree"
        :data="treeData"
        :defaultExpandAll="defaultExpandAll"
        :showCheckbox="showCheckbox"
        :chkboxType="chkboxType"
        :currentNodeKey="activeId"
        @onCreated="onCreated"
        @before-click="beforeClick"
        @node-check="nodeCheck"
      />
      <div v-else class="noData">
        <img src="@/assets/img/newEmpty.png" />
        <div class="noDataTitle">暂无数据</div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { getTree } from '@/api/ajax/base.js';

export default {
  props: {
    isCheckedDefault: {
      type: [String, Boolean],
      default: () => false
    },
    showInput: {
      type: Boolean,
      default: () => true
    },
    title: {
      type: String,
      default: () => '分类'
    },
    activeId: {
      type: [String, Number],
      default: () => ''
    },
    placeholder: {
      type: String
    },
    isAll: {
      type: [String, Boolean],
      default: () => false
    },
    params: {
      type: Object,
      default: () => undefined
    },
    apiFunction: {
      type: Function,
      default: () => undefined
    },
    showCheckbox: {
      //节点是否可被选择
      type: [String, Boolean],
      default: false
    },
    showNodeNum: {
      type: Boolean,
      default: false
    },
    nodeKey: {
      type: String,
      default: 'id'
    },
    parentId: {
      type: String,
      default: 'parentid'
    },
    numKey: {
      // 统计数量字段
      type: String,
      default: 'num'
    },
    chkboxType: {
      //父子节点关联关系
      type: Object,
      default: () => ({
        Y: 'ps',
        N: 'ps'
      })
    }
  },
  data: () => ({
    loading: true,
    searchVal: '',
    localTreeData: [],
    treeData: [],
    treeMap: {},
    searchInput: () => {},
    defaultExpandAll: false,
    treeClass: null,
    fontCss: function(treeId, treeNode) {
      return treeNode.highlight
        ? {
            color: '#fff',
            fontWeight: 'bold',
            background: 'rgba(82, 96, 255, 0.35)'
          }
        : {
            color: 'inherit',
            fontWeight: 'normal',
            background: 'inherit'
          };
    }
  }),
  mounted() {
    this.searchInput = this.debounce(this.input, 300);
    if (this.$refs.tsTree) {
      this.$refs.tsTree.options.callback.onNodeCreated = this.handleTreeNodeCreated;
    }
  },
  watch: {
    isAll: {
      handler(val) {
        if (val) {
          this.defaultExpandAll = true;
        }
      },
      immediate: true
    }
  },
  created() {
    this.getTreeData();
  },
  methods: {
    onCreated(treeClass) {
      this.treeClass = treeClass;
      const selectArr = this.treeClass.getSelectedNodes() || [];
      if (selectArr.length > 0) {
        let select = selectArr[0];
        let allParentValArr = this.getAllParentValArrHandle(select);
        this.$emit('onCreatedGetAllParentId', allParentValArr);
      }
      if (this.showNodeNum) {
        this.$nextTick(() => {
          this.initNodeNum();
        });
      }
    },
    //展示获取子节点的数目
    initNodeNum() {
      let nodess = this.treeClass.transformToArray(this.treeClass.getNodes()); //获取所有节点

      for (let i = 0, l = nodess.length; i < l; i++) {
        //遍历节点数据
        let nodeId = nodess[i].id; //记录节点id
        let nodeName = nodess[i].name; //记录节点名称
        let isParent = nodess[i].isParent; //记录节点是否是父
        let pId = nodess[i].pId; //记录节点父id
        if (isParent) {
          //如果是父节点
          let count = 0;
          count = Number(count) + Number(nodess[i][this.numKey]);
          count = this.getAllChildrenNodes(nodess[i], count);
          let orgCount = '';
          let orgName = '';
          let newname = '';
          if (nodeName.indexOf('〔') > -1) {
            orgCount = nodeName.substring(
              nodeName.indexOf('〔') + 1,
              nodeName.length - 1
            ); //人数
            orgName = nodeName.substring(0, nodeName.indexOf('〔')); //名称
            newname = orgName + '〔' + count + '〕'; //计算上节点数并加上括号   〔〕
          } else {
            newname = nodeName + '〔' + count + '〕'; //计算上节点数并加上括号   〔〕
          }
          nodess[i].name = newname; //重新命名
        } else {
          let newName = nodess[i].name;
          nodess[i].name = newName + '〔' + nodess[i][this.numKey] + '〕'; //重新命名
        }
        this.treeClass.updateNode(nodess[i]); //并更新节点信息
      }
    },
    getAllChildrenNodes(treeNode, count) {
      //给定一个节点对象
      if (treeNode.isParent) {
        //如果是父
        let childrenNodes = treeNode.children;
        if (childrenNodes) {
          for (let i = 0; i < childrenNodes.length; i++) {
            count += Number(childrenNodes[i][this.numKey]);
            count = this.getAllChildrenNodes(childrenNodes[i], count);
          }
        }
      }
      return count;
    },
    handleTreeNodeCreated(event, treeId, treeNode) {
      if (treeNode.children && treeNode.children.length) {
        let node = this.$refs.tsTree.$el.querySelector(`#${treeNode.tId}_a`);
        node.classList.add('not-leaf-node');
      }
    },
    async getTreeData() {
      this.loading = true;
      let api = getTree;
      if (typeof this.apiFunction === 'function') {
        api = this.apiFunction;
      }
      const result = await api(this.params);
      if (result.statusCode === 200 && result.success) {
        let data = this.deepClone(result.object);
        this.treeToMap(data);
        // tree组件试图数据
        this.treeData = data;
        this.localTreeData = this.deepClone(data);
        this.$emit('tree', result.object);
        this.loading = false;
      }
    },
    beforeClick(select, data) {
      let allParentValArr = this.getAllParentValArrHandle(select);
      this.$emit('beforeClick', select, data, allParentValArr);
    },
    nodeCheck() {
      this.$emit('nodeCheck', this.$refs.tsTree.getCheckedNodes());
    },
    getAllParentValArrHandle(treeItemDom) {
      //  获取点击的 所有父级及自己id，name 数组
      let allParentValArr = [
        {
          deptId: treeItemDom.id,
          deptName: treeItemDom.name
        }
      ];
      let node = treeItemDom.getParentNode();
      while (node) {
        allParentValArr.unshift({
          deptId: node.id,
          deptName: node.name
        });
        node = node.getParentNode();
      }
      return allParentValArr;
    },
    input: function() {
      if (!this.searchVal.trim()) {
        this.treeData = this.deepClone(this.localTreeData);
        return;
      }

      // 将搜索名称的node树 父级 子级全部扁平绑定至result上
      let result = {};

      for (let key in this.treeMap) {
        let node = this.deepClone(this.treeMap[key]);
        if (new RegExp(this.searchVal).test(node.name)) {
          result[key] = node;
          this.findParent(result, node);
          this.findChildren(result, node);
        }
      }
      let res = [];
      for (let key in result) {
        let treeItem = result[key];
        treeItem.open = true;
        let pid = treeItem[this.parentId] || '';
        if (pid === '') {
          res.push(treeItem);
        } else {
          if (!result[pid]) {
            result[pid] = {
              children: []
            };
          }
          result[pid].children.push(treeItem);
        }
      }
      this.treeData = res;
    },

    // 递归 将所有id data 绑定数据上 进行搜索
    treeToMap(node) {
      for (let item of node) {
        this.treeMap[item[this.nodeKey]] = {
          ...item,
          children: item.children ? [] : null
        };
        if (item.children && item.children.length > 0) {
          this.treeToMap(item.children);
        }
      }
    },
    // 查找node的parent
    findParent(result, node) {
      if (node[this.parentId] && node[this.parentId] != '') {
        result[node[this.parentId]] = this.deepClone(
          this.treeMap[node[this.parentId]]
        );
        this.findParent(result, this.treeMap[node[this.parentId]]);
      }
    },
    // 查找node的children
    findChildren(result, node) {
      for (let key in this.treeMap) {
        let item = this.treeMap[key];
        if (item[this.parentId] === node[this.nodeKey]) {
          result[item[this.nodeKey]] = this.deepClone(item);
          this.findChildren(result, item);
        }
      }
    },
    deepClone(target) {
      let result;
      if (typeof target === 'object') {
        if (Array.isArray(target)) {
          result = [];
          for (let i in target) {
            result.push(this.deepClone(target[i]));
          }
        } else if (target === null) {
          result = null;
        } else if (target.constructor === RegExp) {
          result = target;
        } else {
          result = {};
          for (let i in target) {
            result[i] = this.deepClone(target[i]);
          }
        }
      } else {
        result = target;
      }
      return result;
    },
    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.search-tree-box {
  width: 100%;
  min-width: 200px;
  background: #f0f4fb;
  border-radius: 4px;
  border: 1px solid #e7ebf0;
  box-sizing: border-box;
  height: 100%;
  margin-right: 8px;
  position: relative;
  box-sizing: border-box;
  border: 1px solid#295cf9;
  .search-tree-input {
    padding: 8px 8px 0;
    &.border-bottom {
      border-bottom: 1px solid#ebebf0;
    }
  }
  .title {
    color: #333;
    font-size: 14px;
    font-weight: 600;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 16px;
      background-color: $primary-blue;
      margin-right: 4px;
      border-radius: 2px;
    }
  }
  /deep/ .el-scrollbar__bar {
    &.is-horizontal {
      height: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover {
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
    &.is-vertical {
      width: 10px !important;
      .el-scrollbar__thumb {
        background: rgba(153, 153, 153, 0.4) !important;
        &:hover {
          background: rgba(153, 153, 153, 0.8) !important;
        }
      }
    }
  }
  // ::v-deep .tree {
  //   position: absolute;
  //   left: 0;
  //   top: 5px;
  //   right: 0;
  //   bottom: 0;
  //   overflow-y: auto;

  //   .button.ico_open,
  //   .button.ico_close,
  //   .button.ico_docu {
  //     width: 0;
  //     height: 0;
  //     &::before {
  //       width: 0px;
  //       height: 0px;
  //       left: 0px;
  //       content: '';
  //       font-size: 0px;
  //     }
  //   }

  //   .button.ico_docu {
  //     &::before {
  //       width: 0px;
  //       height: 0px;
  //     }
  //   }
  // }
  ::v-deep .tree {
    a::after {
      border-bottom: 1px solid #ebebf0;
    }
  }
  .noData {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    img {
      width: 100px;
      height: 80px;
      position: relative;
      top: 15%;
      left: 25%;
      background-repeat: no-repeat;
    }
    .noDataTitle {
      position: absolute;
      top: calc(15% + 110px);
      width: 100%;
      text-align: center;
      color: #333;
      opacity: 0.25;
    }
  }
}
</style>
