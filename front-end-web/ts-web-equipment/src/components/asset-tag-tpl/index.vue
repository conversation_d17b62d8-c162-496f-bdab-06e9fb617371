<script>
import vueQr from 'vue-qr';

export default {
  name: 'assetTagTpl',
  components: { vueQr },
  props: {
    tplId: {
      type: String,
      default: '2'
    },
    tplData: {
      type: Object,
      default: () => ({})
    }
  }
};
</script>

<template>
  <div class="asset-tag-tpl-container">
    <div v-if="tplId == '1'" class="tpl">
      <table>
        <colgroup>
          <col width="1.54cm" height="0.58cm" />
          <col width="2.91cm" height="0.58cm" />
          <col width="1.87cm" height="0.58cm" />
        </colgroup>
        <tr>
          <td colspan="3" style="text-align: center; font-weight: 700;">
            {{ this.$getParentStoreInfo('globalSetting')['webTitle'] || '' }}
          </td>
        </tr>
        <tr>
          <td class="key">资产名称</td>
          <td class="v1">{{ tplData.name }}</td>
          <td class="qrcode" rowspan="3">
            <vue-qr
              :text="tplData.qrCodePath"
              :padding="0"
              :margin="0"
            ></vue-qr>
          </td>
        </tr>
        <tr>
          <td class="key">资产编码</td>
          <td class="v1">{{ tplData.assetCode }}</td>
        </tr>
        <tr>
          <td class="key">规格型号</td>
          <td class="v1">{{ tplData.model }}</td>
        </tr>
        <tr>
          <td class="key">使用科室</td>
          <td class="v2" colspan="2">{{ tplData.belongToOrgName }}</td>
        </tr>
        <tr>
          <td class="key">启用日期</td>
          <td class="v2" colspan="2">{{ tplData.createDate }}</td>
        </tr>
        <tr>
          <td class="key">厂家信息</td>
          <td class="v2" colspan="2">{{ tplData.manufacturerName }}</td>
        </tr>
      </table>
    </div>
    <div v-if="tplId == '2'" class="tpl">
      <table>
        <colgroup>
          <col width="1.54cm" height="0.58cm" />
          <col width="2.91cm" height="0.58cm" />
          <col width="1.87cm" height="0.58cm" />
        </colgroup>
        <tr>
          <td colspan="3" style="text-align: center; font-weight: 700;">
            {{ this.$getParentStoreInfo('globalSetting')['ssoOrgName'] || '' }}
          </td>
        </tr>
        <tr>
          <td class="key">资产编码</td>
          <td class="v2" colspan="2">{{ tplData.assetCode }}</td>
        </tr>
        <tr>
          <td class="key">设备名称</td>
          <td class="v2" colspan="2">{{ tplData.name }}</td>
        </tr>
        <tr>
          <td class="key">规格型号</td>
          <td class="v2" colspan="2">{{ tplData.model }}</td>
        </tr>
        <tr>
          <td class="key">使用科室</td>
          <td class="v1">{{ tplData.belongToOrgName }}</td>
          <td class="qrcode" rowspan="3" style="text-align: center">
            <vue-qr
              :text="tplData.qrCodePath"
              :padding="0"
              :margin="0"
            ></vue-qr>
          </td>
        </tr>
        <tr>
          <td class="key">启用日期</td>
          <td class="v1">{{ tplData.createDate }}</td>
        </tr>

        <tr>
          <td class="key">厂家信息</td>
          <td class="v1">{{ tplData.manufacturerName }}</td>
        </tr>
      </table>
    </div>
  </div>
</template>

<style scoped lang="scss">
.asset-tag-tpl-container {
  width: fit-content;
}

table {
  width: 6.5cm; // 246px * 0.02645833
  height: 4.4cm; // 166px * 0.02645833
  table-layout: fixed;
}

.key {
  min-width: 1.32cm; // 50px * 0.02645833
  text-align: center;
}

.v1 {
  white-space: nowrap;
  max-width: 3.38cm; // 128px * 0.02645833
  overflow: hidden;
}

.v2 {
  white-space: nowrap;
  max-width: 4.93cm; // 194px * 0.02645833
  overflow: hidden;
}

th,
tr,
td {
  height: 0.58cm; // 22px * 0.02645833
  border: 1px solid #000;
  text-align: left;
  vertical-align: middle;
  font-size: 0.26cm; // 10px * 0.02645833
}

th {
  background-color: #f2f2f2;
}

.qrcode {
  text-align: center;
  img {
    width: 100% !important;
    max-width: 100% !important;
    object-fit: cover;
  }
}
</style>
