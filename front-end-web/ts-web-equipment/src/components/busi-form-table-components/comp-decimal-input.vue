<template>
  <ts-form-item
    class="flex-item"
    label=""
    label-width="0"
    :prop="prop"
    :rules="rules"
  >
    <ts-input
      placeholder="请输入"
      v-model="inputValue"
      :disabled="disabled"
      @input="handleInput"
      @blur="handleBlur"
      v-enter-next-input
    />
  </ts-form-item>
</template>

<script>
import enterNextInput from '@/directive/enterNextInput';
import { mapGetters } from 'vuex';

export default {
  name: 'CompDecimalInput',
  directives: {
    enterNextInput
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    prop: {
      type: String,
      required: true
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    },
    ...mapGetters('material', ['handleTsInputDecimal'])
  },
  methods: {
    handleInput(value) {
      // 验证小数位数
      const newVal = this.handleTsInputDecimal(value);
      this.$emit('input', newVal);
    },
    handleBlur() {
      this.$emit('blur');
    }
  }
};
</script>
