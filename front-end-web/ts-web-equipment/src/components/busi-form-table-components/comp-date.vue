<template>
  <ts-form-item
    class="flex-item"
    label=""
    label-width="0"
    :prop="prop"
    :rules="rules"
  >
    <el-date-picker
      style="width: 100%"
      v-model="inputValue"
      :placeholder="placeholder"
      :disabled="disabled"
      :picker-options="{
        disabledDate: disabledDate
      }"
      ref="containerDate"
      format="yyyy-MM-dd"
      value-format="yyyy-MM-dd"
      v-enter-next-input="{ enterSubmit }"
    />
  </ts-form-item>
</template>

<script>
import enterNextInput from '@/directive/enterNextInput';
export default {
  name: 'CompDate',
  directives: {
    enterNextInput
  },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String],
      default: ''
    },
    prop: {
      type: String,
      default: ''
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请选择日期'
    },
    disabledDate: {
      type: Function,
      default: () => {}
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('change', val);
      }
    }
  },
  methods: {
    enterSubmit() {
      this.$refs.containerDate.hidePicker();

      if (!this.inputValue) return;
      const isValidDate = this.$dayjs(this.inputValue, 'YYYY-MM-DD').isValid();
      if (!isValidDate) {
        this.$newMessage('error', '请输入正确的日期格式');
        return;
      }
    }
  }
};
</script>
