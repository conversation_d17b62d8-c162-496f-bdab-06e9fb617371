<template>
  <ts-form-item
    class="flex-item"
    label=""
    label-width="0"
    :prop="prop"
    :rules="rules"
  >
    <ts-input
      ref="containerInput"
      placeholder="请输入"
      v-model="inputValue"
      maxlength="5"
      :disabled="disabled"
      @blur="handleBlur"
      @input="handleInput"
      v-enter-next-input
    />
  </ts-form-item>
</template>

<script>
import enterNextInput from '@/directive/enterNextInput';
export default {
  name: 'CompIntegerInput',
  directives: {
    enterNextInput
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    prop: {
      type: String,
      required: true
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    handleInput(value) {
      // 只允许输入数字
      const numericValue = (value.match(/\d+/g) || [''])[0];
      this.$emit('input', numericValue);
    },
    handleBlur() {
      this.$emit('blur');
    },
    cfocus() {
      this.$refs.containerInput.focus();
    }
  }
};
</script>
