<template>
  <ts-form-item
    class="flex-item"
    label=""
    label-width="0"
    :prop="prop"
    :rules="rules"
  >
    <ts-input
      ref="containerInput"
      v-model="inputValue"
      :placeholder="placeholder"
      :maxlength="maxlength"
      :disabled="disabled"
      v-enter-next-input
    />
  </ts-form-item>
</template>

<script>
import enterNextInput from '@/directive/enterNextInput';
export default {
  name: 'CompInput',
  directives: {
    enterNextInput
  },
  props: {
    value: {
      type: [String, Number],
      default: ''
    },
    prop: {
      type: String,
      default: ''
    },
    rules: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: '请输入'
    },
    maxlength: {
      type: [String, Number],
      default: 50
    }
  },
  computed: {
    inputValue: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  methods: {
    cfocus() {
      this.$refs.containerInput.focus();
    }
  }
};
</script>
