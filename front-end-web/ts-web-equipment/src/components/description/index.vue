<script>
export default {
  props: {
    columns: Array,
    column: [Number, String],
    tableData: Object
  },
  render() {
    let columns = [],
      data = this.tableData || {},
      colGroups = [],
      cols = Number(this.column);
    if (isNaN(cols)) {
      cols = 1;
    }
    for (let i = 0; i < cols; i++) {
      colGroups.push(<col style="width: 140px" />);
      colGroups.push(<col />);
    }

    this.renderColumns.map(cols => {
      let colDoms = [];
      cols.map(col => {
        let content = this.$scopedSlots[col.prop]
          ? this.$scopedSlots[col.prop]({ col: col })
          : col.formatter
          ? col.formatter(data[col.prop], data)
          : data[col.prop];
        colDoms.push(<td class="description-title">{col.label}</td>);
        colDoms.push(
          <td class="description-content" colspan={(col.span || 0) + 1}>
            {content}
          </td>
        );
      });
      columns.push(<tr class="description-row">{...colDoms}</tr>);
    });

    return (
      <table class="description-container">
        <colgroup>{...colGroups}</colgroup>
        {...columns}
      </table>
    );
  },
  computed: {
    renderColumns() {
      let cols = Number(this.column);
      if (isNaN(cols)) {
        cols = 1;
      }

      let list = [];
      for (let i = 0; i < this.columns.length; ) {
        let col = this.columns[i],
          colItem = [],
          colWidth = 0;
        while (colWidth < cols && i < this.columns.length) {
          colItem.push(col);
          colWidth += col.span || 1;
          i++;
          col = this.columns[i];
        }
        list.push(colItem);
      }
      return list;
    }
  }
};
</script>

<style lang="scss" scoped>
.description-container {
  width: 100%;
  table-layout: fixed;
}
.description-title {
  background-color: $container-bg;
  color: $toast-text-color;
}
td {
  padding: $primary-spacing;
  border: 1px solid $theme-border-color;
}
</style>
