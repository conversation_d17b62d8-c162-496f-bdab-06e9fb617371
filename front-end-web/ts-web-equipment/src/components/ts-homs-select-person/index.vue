<template>
  <vxe-modal
    className="ts-homs-select-person"
    :show-confirm-button="false"
    :title="title"
    width="1200"
    v-model="visible"
    showFooter
    :before-hide-method="clearData"
  >
    <template #default>
      <div class="container">
        <div class="tree-container">
          <div class="search-container">
            <ts-input placeholder="输入部门名称" v-model="filterText" />
          </div>

          <el-scrollbar
            class="flex-column tree-scrollbar"
            style="flex: 1;margin-top: 5px;"
            wrap-style="overflow-x: hidden;"
          >
            <el-tree
              class="tree-styles"
              :ref="treeNames.CONTACTS"
              node-key="id"
              :data="contactsTreeList"
              :show-checkbox="false"
              :render-content="renderContent"
              @node-click="handleContactsTreeClick"
            />
            <el-tree
              class="tree-styles"
              :ref="treeNames.DEPT"
              node-key="id"
              :data="deptTreeList.data"
              :show-checkbox="showOrganizationCheck"
              :render-content="renderContent"
              :default-expanded-keys="deptTreeList.defaultExpandedKeys"
              :filter-node-method="filterNode"
              :expand-on-click-node="false"
              @check-change="handleDeptCheckChange"
              @node-click="handleDeptTreeClick"
            />
            <el-tree
              v-if="systemList.data.length > 0"
              class="tree-styles"
              :ref="treeNames.SYSTEM_LIST"
              node-key="id"
              :data="systemList.data"
              :show-checkbox="showGroupCheck"
              :render-content="renderContent"
              :default-expanded-keys="systemList.defaultExpandedKeys"
              :expand-on-click-node="false"
              @check-change="handleGroupCheckChange"
              @node-click="handleSystemListTreeClick"
            />
            <el-tree
              v-if="personalList.data.length > 0"
              class="tree-styles"
              :ref="treeNames.PERSONAL_LIST"
              node-key="id"
              :data="personalList.data"
              :show-checkbox="showGroupCheck"
              :render-content="renderContent"
              :default-expanded-keys="personalList.defaultExpandedKeys"
              :expand-on-click-node="false"
              @check-change="handleGroupCheckChange"
              @node-click="handlePersonalListTreeClick"
            />
          </el-scrollbar>
        </div>

        <div class="table-container">
          <div class="search-container">
            <ts-input
              class="w180"
              v-model="empName"
              placeholder="工号/姓名搜索"
              suffix-icon="el-icon-search"
              clearable
              @input="loadData(1)"
            />
          </div>

          <ts-vxe-base-table
            id="table_homs_select_person"
            class="form-table"
            ref="multipleTable"
            auto-resize
            :showOverflow="'title'"
            :columns="columns"
            :checkboxConfig="{
              reserve: true
            }"
            :pagerCount="4"
            @selection-change="handleSelectionChange"
            @refresh="handleRefreshTable"
          />
        </div>

        <div class="sel-container">
          <div class="dept-container" v-if="showOrganizationCheck">
            <SelectModule
              :data="selectDeptData"
              selectTitle="已选科室"
              @handle-clear="handleDeptTreeClear"
              @handle-delete="handleDeptCheckRemoveItem"
            />
          </div>

          <div class="group-container" v-if="showGroupCheck">
            <SelectModule
              :data="selectGroupData"
              selectTitle="已选群组"
              @handle-clear="handleGroupTreeClear"
              @handle-delete="handleGroupCheckRemoveItem"
            />
          </div>

          <div class="staff-container">
            <SelectModule
              :data="selectPersonData"
              selectTitle="已选人员"
              showNameKey="empName"
              @handle-clear="handleClearPerson"
              @handle-delete="handleDeleteUser"
            />
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <span class="footer-container flex-space">
        <div>
          <span v-show="selectDeptData.length > 0">
            已选科室：{{ selectDeptData.length }} &nbsp;
          </span>

          <span v-show="selectGroupData.length > 0">
            已选群组：{{ selectGroupData.length }} &nbsp;
          </span>

          <span v-show="selectPersonData.length > 0">
            已选人员：{{ selectPersonData.length }} &nbsp;
          </span>
        </div>

        <div>
          <ts-button type="primary" @click="save">确 定</ts-button>
          <ts-button
            type="primary"
            class="shallowButton"
            @click="visible = false"
          >
            关 闭
          </ts-button>
        </div>
      </span>
    </template>
  </vxe-modal>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import SelectModule from './components/select-module.vue';
const folderIcon = require('@/assets/img/homs_icon_folder.svg');
const fileIcon = require('@/assets/img/homs_icon_file.svg');
const CONTACTS = 'contactsTree';
const DEPT = 'deptTree';
const SYSTEM_LIST = 'systemListTree';
const PERSONAL_LIST = 'personalListTree';

const treeNames = {
  CONTACTS,
  DEPT,
  SYSTEM_LIST,
  PERSONAL_LIST
};
const groupTreeArrs = [SYSTEM_LIST, PERSONAL_LIST];
export default {
  name: 'TsHomsSelectPerson',
  components: {
    SelectModule
  },
  data() {
    return {
      folderIcon,
      fileIcon,
      treeNames,
      groupTreeArrs,
      visible: false,

      title: '人员选择',
      treeType: '', // 当前选中的树类型
      empName: '',
      isRadio: false,
      showOrganizationCheck: true,
      showGroupCheck: true,
      filterText: '',
      field: '',

      contactsTreeList: [{ id: '1', name: '常用联系人' }],
      deptTreeList: {
        data: [],
        defaultExpandedKeys: []
      },
      systemList: {
        data: [],
        defaultExpandedKeys: []
      },
      personalList: {
        data: [],
        defaultExpandedKeys: []
      },
      selectDeptData: [],
      selectGroupData: [],
      selectPersonData: [],
      localColumns: [
        {
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          label: '序号',
          prop: 'index',
          align: 'center',
          width: 50
        },
        {
          label: '工号',
          prop: 'empCode',
          align: 'center',
          width: 120
        },
        {
          label: '姓名',
          prop: 'empName',
          align: 'center',
          width: 80
        },
        {
          label: '性别',
          prop: 'empSexLabel',
          align: 'center',
          width: 50
        },
        {
          label: '科室',
          prop: 'empDeptName',
          align: 'center'
        }
      ],
      columns: [],
      submitKeys: null
    };
  },
  computed: {
    /* 排序参数 */
    defaultSort() {
      let sortDatas = this.$store.state.common?.personalSortData ?? {},
        { sidx = 'create_date', sord = 'desc' } = sortDatas;
      return { sord, sidx };
    }
  },
  watch: {
    filterText(val) {
      this.$refs[this.treeNames.DEPT].filter(val);
    }
  },
  methods: {
    /**@desc 打开人员科室选择弹窗
     * @param {string} field 编辑key
     * @param {object} option 编辑配置
     *   @param {string}     title                      弹窗标题
     *   @param {empItem}    empList                    已选择人员列表
     *   @param {object}     deptList                   已选中组织架构列表
     *   @param {boolean}    showOrganizationCheck      组织机构是否可勾选
     *   @param {boolean}    showGroupCheck             群组是否可勾选
     *   @param {boolean}    isRadio                    人员是否为单选
     */
    open(
      field,
      {
        title = '选择',
        echoData = {},
        submitKeys = {
          dept: ['deptNs', 'deptVs'],
          group: ['groupNs', 'groupVs'],
          emp: ['empNs', 'empVs']
        },
        showOrganizationCheck = true,
        showGroupCheck = true,
        isRadio = false
      }
    ) {
      let options = {
        field,
        title,
        showOrganizationCheck,
        showGroupCheck,
        isRadio,
        submitKeys
      };
      Object.keys(options).map(key => {
        this[key] = options[key];
      });

      this.visible = true;

      this.$nextTick(async () => {
        await Promise.all([
          this.getDeptTree(),
          this.getTypeGroupListTree(0, 'systemList'),
          this.getTypeGroupListTree(1, 'personalList')
        ]);
        await this.loadData(1);

        this.columns = deepClone(this.localColumns);
        // 单选隐藏 复选框
        if (this.isRadio) {
          let findindex = this.columns.find(f => f.type === 'checkbox');
          findindex != -1 && this.columns.splice(findindex, 1);
        }

        await this.handleEchoData(echoData);
      });
    },

    async handleEchoData(echoData) {
      // 回显
      let {
        dept: [dl, dv] = ['', ''],
        group: [gl, gv] = ['', ''],
        emp: [el, ev] = ['', '']
      } = this.submitKeys;
      const deptCheckedKeys = echoData[dv] ? echoData[dv].split(',') : [];
      const groupIds = echoData[gv] ? echoData[gv].split(',') : [];

      if (this.showOrganizationCheck) {
        this.$refs[this.treeNames.DEPT].setCheckedKeys(deptCheckedKeys);

        this.$nextTick(() => {
          this.handleDeptCheckChange();
        });
      }

      if (this.showGroupCheck) {
        groupIds.forEach(id => {
          this.handleGroupItemSetStatus({ id }, true);
        });

        this.$nextTick(() => {
          this.handleGroupCheckChange();
        });
      }

      if (echoData[ev]) {
        const res = await this.ajax.employeeSelectEmpByUserCode({
          userCodeStr: echoData[ev]
        });
        if (!res.success) {
          this.$message.error(res.message || '获取回显人员失败!');
          return;
        }
        this.selectPersonData = res.object || [];
        if (this.isRadio) {
          this.tableInstance().setCurrentRow(this.selectPersonData[0]);
        } else {
          this.tableInstance().setCheckboxRow(this.selectPersonData, true);
        }
      } else {
        this.tableInstance().clearCheckboxRow();
        this.tableInstance().clearCheckboxReserve();
        this.selectPersonData = [];
      }
    },

    // 部门人员查询
    async loadData(arg) {
      let { CONTACTS, DEPT, SYSTEM_LIST, PERSONAL_LIST } = this.treeNames;
      let type = arguments[1] || this.treeType || DEPT;

      let pageNo = this.$refs.multipleTable.pageNo;
      let pageSize = this.$refs.multipleTable.pageSize;
      if (arg === 1) this.$refs.multipleTable.pageNo = 1;

      let pageParams = {
        pageNo,
        pageSize,
        empName: this.empName,
        ...this.defaultSort
      };
      if (this.empName) pageParams.empName = this.empName;

      try {
        let treeActiveId = this.getAllTreeSelectActive();
        let res;

        switch (type) {
          case CONTACTS:
            res = await this.ajax.customEmployeeBaseGetCommContact(pageParams);
            break;
          case DEPT:
            res = await this.ajax.getEmployeeList(
              { userSel: true },
              { deptCodeSeach: treeActiveId, ...pageParams }
            );
            break;
          case SYSTEM_LIST:
          case PERSONAL_LIST:
            res = await this.ajax.getOrgGroupUser({
              groupId: treeActiveId,
              ...pageParams
            });
            break;
          default:
            throw new Error('未知的类型');
        }

        const rows = this.formatRows(res.rows, pageNo, pageSize);
        this.$refs.multipleTable.refresh({
          ...res,
          rows
        });

        if (this.isRadio) {
          this.tableInstance().setCurrentRow(this.selectPersonData[0]);
        }
      } catch (error) {
        throw error;
      }
    },

    handleRefreshTable() {
      this.loadData();
    },

    // 多选 选人
    handleSelectionChange(val) {
      if (this.isRadio) this.handleClearPerson();
      this.selectPersonData = val;
    },

    // 删除选中某人
    handleDeleteUser({ id }) {
      this.tableInstance().setCheckboxRow({ id }, false);
      let index = this.selectPersonData.findIndex(f => f.id === id);
      this.selectPersonData.splice(index, 1);
    },

    // 清空选人
    handleClearPerson() {
      this.tableInstance().clearCheckboxRow();
      this.tableInstance().clearCheckboxReserve();
      this.selectPersonData = [];
    },

    // 选中科室
    handleDeptCheckChange() {
      let tN = this.treeNames.DEPT;
      this.selectDeptData = this.$refs[tN].getCheckedNodes();
    },

    // 移除单个科室
    handleDeptCheckRemoveItem(item) {
      let tN = this.treeNames.DEPT;
      this.$refs[tN].setChecked(item, false);
      this.$nextTick(this.handleDeptCheckChange);
    },

    // 清空科室
    handleDeptTreeClear() {
      this.$refs[this.treeNames.DEPT].setCheckedKeys([]);
      this.handleDeptCheckChange();
    },

    // 群组check勾选
    handleGroupCheckChange() {
      this.selectGroupData = this.groupTreeArrs
        .map(treeName => this.$refs[treeName]?.getCheckedNodes() || [])
        .flat();
    },

    // 移除单个群组
    handleGroupCheckRemoveItem(item) {
      this.handleGroupItemSetStatus(item, false);
      this.$nextTick(this.handleGroupCheckChange);
    },

    handleGroupItemSetStatus(removeItem, status) {
      this.groupTreeArrs.forEach(treeName => {
        const treeRef = this.$refs[treeName];
        if (treeRef) {
          const findNode = treeRef.getNode(removeItem);
          if (findNode) treeRef.setChecked(findNode, status);
        }
      });
    },

    // 清空群组
    handleGroupTreeClear() {
      this.groupTreeArrs.forEach(treeName => {
        this.$refs[treeName]?.setCheckedKeys([]);
      });
      this.handleGroupCheckChange();
    },

    async save() {
      try {
        if (
          Array.isArray(this.selectPersonData) &&
          this.selectPersonData.length
        ) {
          let data = this.selectPersonData.map(m => ({
            contactCode: m.empCode
          }));
          await this.ajax.customEmployeeBaseSaveContact(data);
        }

        this.$emit(
          'ok',
          { [this.field]: this.handleGetSelectSubmitData() },
          this.field
        );
        this.cancel();
      } catch (error) {}
    },

    handleGetSelectSubmitData() {
      try {
        let allData = {
          deptList: this.selectDeptData,
          groupList: this.selectGroupData,
          empList: this.selectPersonData
        };
        let {
          dept: [dl, dv] = ['', ''],
          group: [gl, gv] = ['', ''],
          emp: [el, ev] = ['', '']
        } = this.submitKeys;

        format(this.selectDeptData, dl, dv, 'name', 'id');
        format(this.selectGroupData, gl, gv, 'name', 'id');
        format(this.selectPersonData, el, ev, 'empName', 'empCode');
        allData['allNames'] = [allData[el], allData[dl], allData[gl]]
          .filter(Boolean)
          .join(',');

        function format(arr, sName, sValue, tName, tValue) {
          if (!Array.isArray(arr)) {
            throw new Error('数据源必须是数组');
          }
          if (sName && sValue) {
            allData[sName] = arr.map(item => item[tName]).join(',');
            allData[sValue] = arr.map(item => item[tValue]).join(',');
          }
        }
        return allData;
      } catch (e) {
        console.error(e);
      }
    },

    formatRows(rows, pageNo = 1, pageSize = 10) {
      return rows.map((item, i) => ({
        index: (pageNo - 1) * pageSize + i + 1,
        empSexLabel: item.empSex == '0' ? '男' : '女',
        ...item
      }));
    },

    // 科室树 搜索规则
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },

    // 获取科室树
    async getDeptTree() {
      try {
        let res = await this.ajax.getTree();
        if (!res.success) {
          this.$message.error(res.message || `获取科室树失败`);
          return;
        }
        if (Array.isArray(res.object)) {
          let parentId = res.object[0]?.id;
          this.deptTreeList = {
            data: res.object,
            defaultExpandedKeys: [parentId]
          };
          this.$nextTick(() => {
            this.$refs[this.treeNames.DEPT]?.setCurrentKey(parentId);
          });
        }
      } catch (error) {
        throw error;
      }
    },

    // 获取群组树
    async getTypeGroupListTree(groupType, saveKey) {
      try {
        let res = await this.ajax.getOrgGroupTree({ groupType });

        if (!res.success) {
          let title = groupType == 0 ? '系统' : '个人';
          this.$message.error(res.message || `获取${title}群组失败`);
          return;
        }
        if (Array.isArray(res.object)) {
          this[saveKey] = {
            data: res.object,
            defaultExpandedKeys: []
          };
        }
      } catch (error) {
        throw error;
      }
    },

    handleContactsTreeClick(data, checked, indeterminate) {
      let { CONTACTS } = this.treeNames;
      this.nodeClick(data, checked, indeterminate, CONTACTS);
    },
    handleDeptTreeClick(data, checked, indeterminate) {
      let { DEPT } = this.treeNames;
      this.nodeClick(data, checked, indeterminate, DEPT);
    },
    handleSystemListTreeClick(data, checked, indeterminate) {
      let { SYSTEM_LIST } = this.treeNames;
      this.nodeClick(data, checked, indeterminate, SYSTEM_LIST);
    },
    handlePersonalListTreeClick(data, checked, indeterminate) {
      let { PERSONAL_LIST } = this.treeNames;
      this.nodeClick(data, checked, indeterminate, PERSONAL_LIST);
    },

    // 点击tree
    nodeClick(data, checked, indeterminate, refName) {
      this.clearTreeSelectActive(refName);
      this.treeType = refName;
      this.loadData(1, this.treeType);
    },

    // 点击tree节点，清除其他tree选中高亮
    clearTreeSelectActive(name) {
      Object.values(this.treeNames).forEach(fN => {
        if (fN !== name) this.$refs[fN]?.setCurrentKey(null);
      });
    },

    // 获取当前tree选中
    getAllTreeSelectActive() {
      return Object.values(this.treeNames)
        .map(fN => this.$refs[fN]?.getCurrentKey())
        .filter(Boolean)
        .join('');
    },

    tableInstance() {
      return this.$refs.multipleTable.tsVxeTableRef();
    },

    renderContent(h, { node, data, store }) {
      return (
        <div class="custom-tree-node">
          <img
            class="icon-file"
            src={data.children ? this.folderIcon : this.fileIcon}
          />
          <span>{data.name}</span>
        </div>
      );
    },
    clearData() {
      this.cancel();
    },
    cancel() {
      this.title = '人员选择';
      this.treeType = '';
      this.empName = '';
      this.isRadio = false;
      this.showOrganizationCheck = true;
      this.showGroupCheck = true;
      this.field = '';
      this.filterText = '';

      this.deptTreeList = {
        data: [],
        defaultExpandedKeys: []
      };
      this.systemList = {
        data: [],
        defaultExpandedKeys: []
      };
      this.personalList = {
        data: [],
        defaultExpandedKeys: []
      };
      this.$refs.multipleTable.pageNo = 1;
      this.columns = [];
      this.clearTreeSelectActive('');
      this.handleClearPerson();
      this.handleDeptTreeClear();
      this.handleGroupTreeClear();

      this.visible = false;
    }
  }
};
</script>
<style lang="scss" scoped>
$main-bg: #5260ff;
$main-bg-8: #5260ff14;

.ts-homs-select-person {
  width: calc(100%) !important;
  height: calc(100%) !important;
  ::v-deep .vxe-modal--box {
    .vxe-modal--header-title {
      height: 50px;
      line-height: 50px;
      padding: 0 10px;
      font-size: 18px;
    }

    .vxe-modal--header-right {
      display: flex;
      align-items: center;
    }

    .footer-container {
      padding-right: 100px;
    }

    .container {
      height: calc(100vh - 280px);
      display: flex;
      gap: 8px;
      overflow: hidden;

      .search-container {
        padding: 8px;

        .w180 {
          width: 180px;
          min-width: 180px;
        }
      }

      .tree-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        border: 1px solid #e4e4e4;
        background: #f0f4fb;
        .el-tree {
          background-color: transparent;
        }
      }

      .table-container {
        flex: 2;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        border: 1px solid #e4e4e4;

        .form-table {
          border: 0px;
        }

        .text-style {
          font-size: 12px;
          color: #333333;
        }
      }

      .sel-container {
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .dept-container {
          height: 140px;
        }

        .group-container {
          height: 140px;
        }

        .staff-container {
          flex: 1;
          overflow: hidden;
        }
      }
    }
  }
}

/deep/ {
  .tree-styles {
    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: $primary-form-blue !important;
      border: 1px solid $primary-form-blue !important;
    }

    .is-current > .el-tree-node__content {
      background-color: rgba(137, 170, 252, 0.5) !important;
    }

    .el-tree-node__content:hover,
    .el-upload-list__item:hover {
      background-color: rgba(137, 170, 252, 0.3) !important;
    }

    .el-tree-node__content:hover .custom-tree-node span {
      color: $main-bg;
    }
  }
}
</style>

<style>
.ts-homs-select-person .custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  font-size: 14px;
  padding-right: 8px;
}

.ts-homs-select-person .custom-tree-node span {
  font-size: 12px;
  color: #342525;
  line-height: 16px;
}

.ts-homs-select-person .custom-tree-node .icon-file {
  width: 16px;
  height: 16px;
  margin-right: 4px;
}
</style>
