import ElementUi from '@trasen/trasen-element-ui/lib';
import elementuiExtend from '@/extends/index.js';
import BaseTable from './base-table/index.vue';
import BaseXlsxUpload from './base-xlsx-upload';
import BaseFileUpload from './base-file-upload';
import BaseActionCell from './base-action-cell';
import TsUserDeptSelect from './ts-user-dept-select';
import BaseIcon from './base-icon';
import BaseSearchTree from './base-search-tree';
import BaseUpload from './base-upload/index.vue';
import BaseSelect from './base-select/index.vue';
import BaseImport from './base-import/index.vue';
import TsVxeBaseTable from './ts-vxe-base-table';
import BaseDateRangePicker from './base-date-range-picker/index.vue';
import TsHomsSelectPerson from './ts-homs-select-person/index.vue';
import NewBaseSearchTree from './new-base-search-tree/index.vue';
import ColmunHead from './colmun-head/index.vue';

export default {
  /**@desc 这个文件主要用于初始化各种当前项目公用组件**/
  install(Vue) {
    Vue.use(ElementUi, { size: 'small' });
    Vue.use(elementuiExtend); //初始化ElementUi框架
    Vue.component('BaseTable', BaseTable);
    Vue.component('BaseXlsxUpload', BaseXlsxUpload);
    Vue.component('BaseFileUpload', BaseFileUpload);
    Vue.component('BaseActionCell', BaseActionCell);
    Vue.component('TsUserDeptSelect', TsUserDeptSelect);
    Vue.component('BaseIcon', BaseIcon);
    Vue.component('BaseSearchTree', BaseSearchTree);
    Vue.component('BaseUpload', BaseUpload);
    Vue.component('BaseSelect', BaseSelect);
    Vue.component('BaseImport', BaseImport);
    Vue.component('TsVxeBaseTable', TsVxeBaseTable);
    Vue.component('BaseDateRangePicker', BaseDateRangePicker);
    Vue.component('TsHomsSelectPerson', TsHomsSelectPerson);
    Vue.component('NewBaseSearchTree', NewBaseSearchTree);
    Vue.component('ColmunHead', ColmunHead);
  }
};
