<template>
  <div
    class="form-table-box"
    :style="{ 'margin-bottom': disabled ? '8px' : '35px' }"
  >
    <ts-table
      stripe
      :columns="loaclColumns"
      :data="formData[operateDataKey]"
      lazy
      @selection-change="handleSelectionChange"
      :show-summary="showSummary"
      :summary-method="getSummaries"
      :row-class-name="rowClassName"
    >
      <ts-table-column
        :show-overflow-tooltip="false"
        v-for="item in columns"
        v-bind="item"
        :key="item.prop"
      >
        <template v-slot:header="{ column }">
          <span v-show="item.requiredIcon" class="required-icon">*</span>
          {{ column.label }}
        </template>

        <template v-slot="{ row, column, $index }">
          <slot :name="item.prop" :row="row" :column="column" :index="$index" />
        </template>
      </ts-table-column>
    </ts-table>
    <div class="form-operation-box" v-if="!disabled && !hideOperation">
      <ul class="operation">
        <li>
          <ts-button
            type="text"
            icon="el-icon-document-add"
            @click="handleFormTableAddRow"
          >
            新增行
          </ts-button>
          <ts-button
            class="delel-btn"
            type="text"
            icon="el-icon-delete"
            @click="handleFormTableDeletRow"
          >
            删除行
          </ts-button>
          <!-- <ts-button
            class="copy-btn"
            type="text"
            icon="el-icon-copy-document"
            @click="handleFormTableCopyRow"
            >复制行
          </ts-button> -->
        </li>
        <li>
          <ts-button
            class="copy-btn"
            type="text"
            icon="el-icon-top"
            @click="handleFormTableMoveRow('-')"
          >
            上移
          </ts-button>
          <ts-button
            class="copy-btn"
            type="text"
            icon="el-icon-bottom"
            @click="handleFormTableMoveRow('+')"
          >
            下移
          </ts-button>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { deepClone } from '@/unit/commonHandle.js';
import { commonUtils } from '@/util/common.js';

import Decimal from 'decimal.js';
export default {
  props: {
    formData: {
      type: Object
    },
    disabled: {
      type: Boolean
    },
    operateDataKey: {
      type: String,
      default: ''
    },
    columns: {
      type: Array
    },
    hideOperation: {
      type: Boolean,
      default: false
    },
    showSummary: {
      type: Boolean,
      default: false
    },
    summaryColumns: {
      type: Array,
      default: () => []
    },
    rowClassName: {
      type: Function
    }
  },
  computed: {
    formDataKey() {
      let keyObject = {};
      return this.columns.reduce((prev, cur) => {
        prev[cur.prop] = cur.defaultValue || '';
        return keyObject;
      }, keyObject);
    }
  },
  data() {
    return {
      loaclColumns: [
        {
          type: this.disabled ? 'index' : 'selection',
          align: 'center',
          width: 50,
          label: this.disabled ? '序号' : ''
        }
      ],
      selectList: []
    };
  },
  methods: {
    returnSelectList() {
      return this.selectList;
    },
    handleSelectionChange(e) {
      this.selectList = e;
    },
    getSummaries({ columns, data }) {
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        const prop = column.property;
        if (this.summaryColumns.includes(prop)) {
          const values = data.map(item => new Decimal(item[prop] || 0));
          if (!values.every(value => value.isNaN())) {
            sums[index] = values
              .reduce((prev, curr) => {
                if (!curr.isNaN()) {
                  return prev.plus(curr);
                }
                return prev;
              }, new Decimal(0))
              .toString();

            sums[index] = Number(sums[index]).toLocaleString('zh-CN');
          } else {
            sums[index] = '';
          }
        } else {
          sums[index] = '';
        }
      });

      return sums;
    },
    // 新增
    handleFormTableAddRow() {
      const addData = deepClone(this.formDataKey);
      addData.id = commonUtils.guid();
      this.formData[this.operateDataKey].push(addData);
      this.$emit('changeRow');
    },
    // 删除
    handleFormTableDeletRow() {
      if (this.selectList.length === 0) {
        this.$newMessage('warning', '请至少选择一条记录【操作】!');
        return;
      }
      const ids = this.selectList.map(item => item.id);
      this.formData[this.operateDataKey] = this.formData[
        this.operateDataKey
      ].filter(item => !ids.includes(item.id));
      this.$emit('changeRow');
    },
    // 复制
    handleFormTableCopyRow() {
      if (this.selectList.length !== 1) {
        this.$newMessage('warning', '请选择一条记录【操作】!');
        return;
      }
      const copyObject = deepClone(this.selectList[0]);
      copyObject.id = commonUtils.guid();
      this.formData[this.operateDataKey].push(copyObject);
    },
    handleFormTableMoveRow(position) {
      if (this.selectList.length !== 1) {
        this.$newMessage('warning', '请选择一条记录【操作】!');
        return;
      }
      const selectId = this.selectList[0].id;
      const index = this.formData[this.operateDataKey].findIndex(
        item => item.id === selectId
      );
      let operateIndex;
      switch (position) {
        case '+':
          operateIndex = index + 1;
          break;
        case '-':
          operateIndex = index - 1;
          break;
      }

      this.formData[this.operateDataKey].splice(
        index,
        1,
        ...this.formData[this.operateDataKey].splice(
          operateIndex,
          1,
          this.formData[this.operateDataKey][index]
        )
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.form-table-box {
  position: relative;
  width: 100%;
  border: 1px solid #eee;
  .required-icon {
    color: red;
    margin-right: 4px;
  }

  ::v-deep {
    .ts-header-row-class {
      .cell {
        background-color: #eceef3;
      }
    }

    .el-table__footer-wrapper {
      .el-table__footer {
        td {
          .cell {
            padding: 0px 4px !important;
            text-align: right;
          }
        }
      }
      .label::before {
        content: '*';
        color: rgb(245, 108, 108);
        margin-right: 4px;
      }
    }

    .el-table__body-wrapper {
      .el-table__body {
        width: 100% !important;
      }

      box-sizing: border-box;

      &::-webkit-scrollbar {
        width: 10px;
        height: 10px;
      }

      // 滚动条的滑块
      &::-webkit-scrollbar-thumb {
        border-radius: 10px;
        background: rgba(153, 153, 153, 0.4);
        &:hover {
          background: rgba(153, 153, 153, 0.8);
        }
      }

      &::-webkit-scrollbar-track {
        box-shadow: inset 0 0 5px rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        background: #fff;
      }

      .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }

  .form-operation-box {
    position: absolute;
    width: 100%;
    height: 35px;
    left: 0;
    bottom: -35px;
    display: flex;
    align-items: center;

    .operation {
      width: 100%;
      padding: 0 8px;
      display: flex;
      justify-content: space-between;
      margin: 0;

      .delel-btn {
        color: red;
      }

      .copy-btn {
        color: #000;
      }
    }
  }
}
</style>
