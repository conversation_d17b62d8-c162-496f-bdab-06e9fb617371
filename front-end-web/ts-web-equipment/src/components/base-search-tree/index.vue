<template>
  <div class="search-tree-box" v-loading="loading">
    <div class="search-tree-input">
      <ts-input
        v-model="searchVal"
        @input="searchInput"
        :placeholder="placeholder"
      />
    </div>

    <ts-ztree
      class="tree"
      ref="tsTree"
      :data="treeData"
      :defaultExpandAll="defaultExpandAll"
      :showCheckbox="showCheckbox"
      :chkboxType="chkboxType"
      :currentNodeKey="activeId"
      @onCreated="onCreated"
      @before-click="beforeClick"
    />
  </div>
</template>

<script>
import { organizationZTreeList } from '@/api/ajax/common/index.js';

export default {
  props: {
    spSearch: {
      type: Boolean,
      default: () => false
    },
    isCheckedDefault: {
      type: [String, Boolean],
      defalut: () => false
    },
    activeId: {
      type: [String, Number],
      defalut: () => ''
    },
    placeholder: {
      type: String
    },
    isAll: {
      type: [String, Boolean],
      defalut: () => false
    },
    params: {
      type: Object,
      defalut: () => undefined
    },
    apiFunction: {
      type: Function,
      defalut: () => undefined
    },
    showCheckbox: {
      //节点是否可被选择
      type: [String, Boolean],
      default: false
    },
    chkboxType: {
      //父子节点关联关系
      type: Object,
      default: () => ({
        Y: 'ps',
        N: 'ps'
      })
    }
  },
  data: () => ({
    loading: true,
    searchVal: '',
    treeData: [],
    treeMap: {},
    searchInput: null,
    defaultExpandAll: false,
    treeClass: null
  }),
  mounted() {
    this.searchInput = this.debounce(this.input, 300);
    this.$refs.tsTree.options.callback.onNodeCreated = this.handleTreeNodeCreated;
  },
  watch: {
    isAll: {
      handler(val) {
        if (val) {
          this.defaultExpandAll = true;
        }
      },
      immediate: true
    }
  },
  created() {
    this.getTreeData();
  },
  methods: {
    onCreated(treeClass) {
      this.treeClass = treeClass;
      const selectArr = this.treeClass.getSelectedNodes() || [];
      if (selectArr.length > 0) {
        let select = selectArr[0];
        let allParentValArr = this.getAllParentValArrHandle(select);
        this.$emit('onCreatedGetAllParentId', allParentValArr);
      }
    },
    handleTreeNodeCreated(event, treeId, treeNode) {
      if (treeNode.children && treeNode.children.length) {
        let node = this.$refs.tsTree.$el.querySelector(`#${treeNode.tId}_a`);
        node.classList.add('not-leaf-node');
      }
    },
    async getTreeData() {
      this.loading = true;
      let api = organizationZTreeList;
      if (typeof this.apiFunction === 'function') {
        api = this.apiFunction;
      }

      const result = await api(this.params);
      if (result.statusCode === 200 && result.success) {
        this.treeToMap(result.object);
        // tree组件试图数据
        this.treeData = result.object;
        this.$emit('tree', result.object);
        this.loading = false;
      }
    },
    beforeClick(select, data) {
      let allParentValArr = this.getAllParentValArrHandle(select);
      this.$emit('beforeClick', select, data, allParentValArr);
    },
    getAllParentValArrHandle(treeItemDom) {
      //  获取点击的 所有父级及自己id，name 数组
      let allParentValArr = [
        {
          deptId: treeItemDom.id,
          deptName: treeItemDom.name
        }
      ];
      let node = treeItemDom.getParentNode();
      while (node) {
        allParentValArr.unshift({
          deptId: node.id,
          deptName: node.name
        });
        node = node.getParentNode();
      }
      return allParentValArr;
    },
    input: function() {
      // 将搜索名称的node树 父级 子级全部扁平绑定至result上
      let result = {};
      for (let key in this.treeMap) {
        let node = this.deepClone(this.treeMap[key]);

        let searchResult = undefined;
        if (this.spSearch) {
          searchResult =
            new RegExp(this.searchVal).test(node.name) ||
            node.qp?.includes(this.searchVal.toLowerCase()) ||
            node.sp?.includes(this.searchVal.toLowerCase());
        } else {
          searchResult = new RegExp(this.searchVal).test(node.name);
        }

        if (searchResult) {
          result[key] = node;
          this.findParent(result, node);
          this.findChildren(result, node);
        }
      }

      let res = [];
      for (let key in result) {
        let treeItem = result[key];
        let pid = treeItem.pid;
        if (pid === '') {
          res.push(treeItem);
        } else {
          if (!result[pid]) {
            result[pid] = {
              children: []
            };
          }
          result[pid].children.push(treeItem);
        }
      }

      if (this.searchVal.trim()) {
        this.defaultExpandAll = true;
      } else {
        this.defaultExpandAll = false;
      }
      this.treeData = res;
    },

    // 递归 将所有id data 绑定数据上 进行搜索
    treeToMap(node) {
      for (let item of node) {
        this.treeMap[item.id] = {
          ...item,
          children: item.children ? [] : null
        };
        if (item.children && item.children.length > 0) {
          this.treeToMap(item.children);
        }
      }
    },
    // 查找node的parent
    findParent(result, node) {
      if (node.pid && node.pid != '') {
        result[node.pid] = this.deepClone(this.treeMap[node.pid]);
        this.findParent(result, this.treeMap[node.pid]);
      }
    },
    // 查找node的children
    findChildren(result, node) {
      for (let key in this.treeMap) {
        let item = this.treeMap[key];
        if (item.pid === node.id) {
          result[item.id] = this.deepClone(item);
          this.findChildren(result, item);
        }
      }
    },
    deepClone(target) {
      let result;
      if (typeof target === 'object') {
        if (Array.isArray(target)) {
          result = [];
          for (let i in target) {
            result.push(this.deepClone(target[i]));
          }
        } else if (target === null) {
          result = null;
        } else if (target.constructor === RegExp) {
          result = target;
        } else {
          result = {};
          for (let i in target) {
            result[i] = this.deepClone(target[i]);
          }
        }
      } else {
        result = target;
      }
      return result;
    },
    debounce(fn, wait) {
      let timer;
      return function() {
        clearTimeout(timer);
        timer = setTimeout(() => {
          fn.apply(this, arguments);
        }, wait);
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.search-tree-box {
  width: 200px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e7ebf0;
  box-sizing: border-box;
  height: 100%;
  margin-right: 8px;
  position: relative;
  box-sizing: border-box;

  .search-tree-input {
    padding: 8px 8px 0;
  }

  ::v-deep .tree {
    position: absolute;
    left: 0;
    top: 44px;
    right: 0;
    bottom: 0;
    overflow-y: auto;

    .button.ico_open,
    .button.ico_close,
    .button.ico_docu {
      width: 0;
      height: 0;
      &::before {
        width: 0px;
        height: 0px;
        left: 0px;
        content: '';
        font-size: 0px;
      }
    }

    .button.ico_docu {
      &::before {
        width: 0px;
        height: 0px;
      }
    }
  }
}
</style>
