<template>
  <div class="flex-grow">
    <ts-upload
      ref="tsUpload"
      action=""
      :limit="limit"
      :fileList.sync="fileList"
      :show-file-list="false"
      :accept="accept"
      :http-request="handleUploadFile"
      :on-exceed="masterFileMax"
    >
      <ts-button class="basic-btn" v-show="showBtn">文件上传</ts-button>
      <div class="el-upload__tip">
        {{ tips }}
      </div>
    </ts-upload>
    <ts-upload-file-list
      :fileList.sync="fileList"
      :on-remove="onRemove"
      :on-abort="onAbort"
      :on-upload="onUpload"
      :onPreview="onPreview"
      :showPreview="showPreview"
      :showUpload="showUpload"
      :showRemove="showBtn"
      :type="uploadFileListType"
    ></ts-upload-file-list>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import { commonUtils } from '@/util/common.js';

export default {
  model: {
    prop: 'businessId',
    event: 'input'
  },
  props: {
    showBtn: {
      type: Boolean,
      default: () => true
    },
    showPreview: {
      type: Boolean,
      default: () => true
    },
    showUpload: {
      type: Boolean,
      default: () => true
    },
    businessId: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => '只能上传jpg/png文件，且不超过500kb'
    },
    limit: {
      type: Number,
      default: 99
    },
    accept: String,
    fileListType: String
  },
  data() {
    return {
      fileList: [],
      innerBusinessId: '',
      previewFile: '',
      previewFileList: []
    };
  },
  watch: {
    businessId: {
      handler(val) {
        if (val && val !== this.innerBusinessId) {
          // 回显查询
          this.innerBusinessId = val;
          this.getFileList();
        } else if (val && val === this.innerBusinessId) {
          // 继续添加附件
        } else {
          // 首次上传
          this.innerBusinessId = commonUtils.guid();
        }
      },
      immediate: true
    },
    fileList(val) {
      if (val instanceof Array && val.length && !this.businessId) {
        this.$emit('input', this.innerBusinessId);
      }
    }
  },
  computed: {
    uploadFileListType() {
      return (
        this.fileListType ||
        { image: 'picture', file: 'text' }[this.accept] ||
        'mixture'
      );
    }
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .getFileAttachmentByBusinessId({
          businessId: this.innerBusinessId
        })
        .then(res => {
          this.fileList = res.object.map(item => ({
            ...item,
            fileName: `${item.id}.${item.fileExtension}`,
            fileRealName: item.originalName,
            fileId: item.id,
            uid: item.id,
            url: item.realPath,
            name: item.originalName,
            status: 'success'
          }));
        });
    },
    /**@desc 替换原生上传 */
    handleUploadFile(params) {
      let data = new FormData();
      data.append('file', params.file);
      data.append('businessId', this.innerBusinessId);
      this.ajax.uploadFile(data).then(res => {
        if (res.success) {
          this.fileList.push({
            ...res.object[0],
            realPath: res.object[0].filePath,
            url: res.object[0].filePath,
            uid: res.object[0].fileId,
            name: res.object[0].fileName
          });
        } else {
          this.$message.error(res.message || '上传失败');
        }
      });
    },
    /**@desc 删除 */
    onRemove(file) {
      let idx = this.fileList.findIndex(e => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      this.ajax.deleteFile(deleteFile.uid).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);

        if (!this.fileList.length) {
          this.$emit('input', '');
        }
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex(e => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = file.url;
      a.target = '_blank';
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (commonUtils.isDoc(file.fileName)) {
        commonUtils.viewerDocBase(file.realPath, file.fileName);
      } else {
        this.previewFile = location.origin + file.realPath;
        this.previewFileList = this.fileList
          .filter(item => commonUtils.isImg(item.name))
          .map(item => location.origin + item.realPath);
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    },
    masterFileMax() {
      this.$message.warning(`请最多上传 ${this.limit} 个文件。`);
    }
  }
};
</script>

<style></style>
