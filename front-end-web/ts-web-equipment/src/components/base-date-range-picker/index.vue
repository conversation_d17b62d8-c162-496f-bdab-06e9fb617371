<template>
  <div class="base-date-range-picker">
    <el-date-picker
      class="date-picker"
      :readonly="readonly"
      :disabled="pickerDisabled"
      v-model="startValue"
      :type="pickerType"
      :format="dataFormat"
      :value-format="dataFormat"
      :picker-options="pickerOptions"
      placeholder="开始日期"
      @change="handleChange"
      @blur="handleBlur"
    ></el-date-picker>
    <span class="range-separator">至</span>
    <el-date-picker
      class="date-picker"
      :readonly="readonly"
      :disabled="pickerDisabled"
      v-model="endValue"
      :type="pickerType"
      :format="dataFormat"
      :value-format="dataFormat"
      placeholder="结束日期"
      :picker-options="pickerOptions1"
      @change="handleChange"
      @blur="handleBlur"
    ></el-date-picker>
  </div>
</template>

<script>
import index from './index';
export default {
  mixins: [index],
  inject: {
    elForm: {
      default: ''
    },
    elFormItem: {
      default: ''
    }
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {},
    type: {
      type: String,
      default: 'daterange'
    },
    disabled: Boolean,
    format: String,
    valueFormat: String,
    readonly: Boolean,
    getCalendarContainer: {
      type: Function,
      default: triggerNode => triggerNode.parentNode
    },
    validateEvent: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.$on('fieldReset', this.handleFieldReset);
  },
  methods: {
    handleFieldReset(initialValue) {
      this.userInput = initialValue === '' ? null : initialValue;
    }
  }
};
</script>

<style lang="scss">
.base-date-range-picker {
  display: inline-flex;
  align-items: center;
  .range-separator {
    margin: 0 4px;
  }
}
.el-date-picker.has-sidebar {
  width: 322px !important;
}
.el-picker-panel__sidebar {
  width: 100% !important;
  height: 40px !important;
  line-height: 40px !important;
  top: unset !important;
  text-align: right !important;
  padding: 0 15px !important;
}
.el-picker-panel__shortcut {
  display: inline-block !important;
  width: auto !important;
  padding: 0 8px !important;
  border: 1px solid #eee !important;
  border-radius: 4px !important;
  margin-left: 8px !important;
}
.el-picker-panel__sidebar + .el-picker-panel__body {
  margin: 0 !important;
}
.el-date-picker.has-sidebar .el-picker-panel__content {
  margin-bottom: 40px !important;
}
</style>
