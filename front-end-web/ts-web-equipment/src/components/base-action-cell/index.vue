<template>
  <div ref="content" class="base-action-cell-content">
    <span
      class="visible-action-item"
      :class="item.className"
      v-for="(item, index) of showActionList"
      :key="index"
      @click="handleActionClick(item)"
      >{{ item.label }}</span
    >
    <template v-if="hiddenActionList.length">
      <el-popover
        ref="popover"
        placement="bottom-end"
        trigger="hover"
        :close-delay="0"
        popper-class="base-table-action-cell-popover"
        :popper-options="{
          boundariesElement: 'viewport',
          removeOnDestroy: true
        }"
      >
        <span slot="reference">
          <i class="more-action-icon el-icon-more"></i>
        </span>
        <div
          class="hidden-action-item"
          v-for="(item, index) of hiddenActionList"
          :key="index"
          @click="handleActionClick(item)"
        >
          <div class="action-icon">
            <i :class="item.icon"></i>
          </div>
          {{ item.label }}
        </div>
      </el-popover>
    </template>
  </div>
</template>

<script>
import styleOptions from '@/styles/variables.scss';

export default {
  name: 'BaseActionCell',
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      lock: null, //是否在计算宽度中
      showActionList: [],
      hiddenActionList: [],
      resizeTimer: null
    };
  },
  mounted() {
    window.addEventListener('resize', this.handleWindowResize);
  },
  watch: {
    actions: {
      handler() {
        this.refresh();
      },
      immediate: true
    }
  },
  methods: {
    handleActionClick(action) {
      this.$emit('action-select', action.event);
    },
    handleWindowResize() {
      this.resizeTimer && clearTimeout(this.resizeTimer);
      this.resizeTimer = setTimeout(() => {
        this.refresh();
      }, 200);
    },
    refresh(key) {
      if (this.lock && key != this.lock) {
        return;
      }
      this.lock = Math.random()
        .toString(36)
        .substr(2);

      let content = this.$refs.content;
      if (!content) {
        setTimeout(() => {
          this.refresh(this.lock);
        }, 50);
        return;
      }

      let interval = Number(
          styleOptions['ts-primary-spacing'].replace('px', '')
        ),
        width = content.clientWidth + interval,
        newShowActionList = [],
        newHiddenActionList = [];

      for (let index = 0; index < this.actions.length; index++) {
        let action = this.actions[index],
          actionWidth = action.label.length * 14;

        if (index < this.actions.length - 1) {
          //如果 index 小于操作长度，即后面还有操作的时候， 默认存在更多按钮，如若都放的下则继续轮询
          if (width - interval * 2 - actionWidth - 16 >= 0) {
            width = width - interval - actionWidth;
          } else {
            newShowActionList = this.actions.slice(0, index);
            newHiddenActionList = this.actions.slice(index);
            break;
          }
        } else {
          //如果 能容纳下最后一个按钮
          if (width - interval - actionWidth >= 0) {
            newShowActionList = this.actions;
          } else {
            newShowActionList = this.actions.slice(0, index);
            newHiddenActionList = this.actions.slice(index);
          }
        }
      }
      this.hiddenActionList = newHiddenActionList;
      this.showActionList = newShowActionList;

      this.lock = null;
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.handleWindowResize);
  }
};
</script>

<style lang="scss" scoped>
.base-action-cell-content {
  text-align: center;
}
.base-action-cell-content span + span {
  margin-left: $primary-spacing;
}
.more-action-icon {
  cursor: pointer;
  transform: rotate(90deg);
  &:hover {
    color: $primary-blue;
  }
}
.visible-action-item {
  cursor: pointer;
  color: $primary-blue;
  &:hover {
    text-decoration: underline;
    opacity: 0.8;
  }
  &.actionDel {
    color: red;
  }
  &.actionWarn {
    color: #f2711c;
  }
  &.actionEdit {
    color: #5caa90;
  }
}
</style>

<style lang="scss">
.base-table-action-cell-popover {
  margin-top: 2px !important;
  padding: 0 !important;
  min-width: unset;
  .popper__arrow {
    display: none;
  }
  .hidden-action-item {
    display: flex;
    align-items: center;
    padding-left: $primary-spacing;
    padding-right: 20px;
    height: 27px;
    cursor: pointer;
    &:hover {
      background-color: mix(#fff, $primary-blue, 92%);
      color: $primary-blue;
    }
  }
  .action-icon {
    height: 27px;
    width: 27px;
    line-height: 27px;
    text-align: center;
    display: inline-block;
    margin-right: 5px;
    i {
      font-size: 16px;
    }
  }
}
</style>
