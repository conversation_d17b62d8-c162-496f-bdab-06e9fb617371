<template>
  <div class="flex-grow">
    <ts-upload
      v-if="!onlyRead"
      ref="tsUpload"
      action="/ts-basics-bottom/fileAttachment/upload?moduleName=equipment"
      :fileList.sync="fileList"
      :show-file-list="false"
      :http-request="handleUploadFile"
    >
      <slot>
        <ts-button>文件上传</ts-button>
      </slot>
      <div slot="tip" class="el-upload__tip">
        {{ tips }}
      </div>
    </ts-upload>
    <slot name="uploadFiles" :fileList="fileList">
      <ts-upload-file-list
        :fileList.sync="fileList"
        :on-remove="onRemove"
        :on-abort="onAbort"
        :on-upload="onUpload"
        :onPreview="onPreview"
        :showPreview="showPreview"
        :showUpload="showUpload"
        :showRemove="showRemove"
        type="mixture"
      ></ts-upload-file-list>
    </slot>

    <el-image
      ref="preview"
      style="display: none;"
      :src="previewFile"
      :preview-src-list="previewFileList"
      :z-index="3000"
    >
    </el-image>
  </div>
</template>

<script>
import commonUtils from '@/util/file.js';

export default {
  model: {
    prop: 'businessId',
    event: 'input'
  },
  props: {
    businessId: {
      type: String,
      default: () => ''
    },
    tips: {
      type: String,
      default: () => ''
    },
    onlyRead: {
      type: Boolean,
      default: () => false
    },
    actions: {
      type: [Array, String],
      default: () => ['preview', 'downLoad', 'remove']
    }
  },
  data() {
    return {
      fileList: [],
      innerBusinessId: '',
      previewFile: '',
      previewFileList: []
    };
  },
  computed: {
    showPreview() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('preview');
    },
    showUpload() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('downLoad');
    },
    showRemove() {
      let actions = this.actions;
      if (this.actions instanceof String) {
        actions = [this.actions];
      }
      return actions.includes('remove') && !this.onlyRead;
    }
  },
  watch: {
    businessId: {
      handler(val) {
        this.innerBusinessId = val;
        this.fileList = [];
        if (val) {
          this.getFileList();
        }
      },
      immediate: true
    }
  },
  methods: {
    /**@desc 获取文件列表 */
    getFileList() {
      this.fileList = [];
      this.ajax
        .getFileAttachmentByBusinessId({ businessId: this.innerBusinessId })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.fileList = res.object.map(item => ({
            ...item,
            fileName: `${item.id}.${item.fileExtension}`,
            fileRealName: item.originalName,
            fileId: item.id,
            uid: item.id,
            url: item.realPath,
            name: item.originalName,
            status: 'success'
          }));

          if (!this.fileList.length) {
            this.$emit('input', null);
            this.$emit('change', null);
          }
        });
    },
    /**@desc 替换原生上传 */
    handleUploadFile(params) {
      let businessId = this.innerBusinessId;
      if (!this.innerBusinessId) {
        businessId = commonUtils.guid();
      }
      let data = new FormData();
      data.append('file', params.file);
      data.append('businessId', businessId);
      this.ajax.uploadFile(data).then(res => {
        if (res.success) {
          this.fileList.push({
            ...res.object[0],
            realPath: res.object[0].filePath,
            url: res.object[0].filePath,
            uid: res.object[0].fileId,
            name: res.object[0].fileRealName
          });

          !this.innerBusinessId &&
            this.$emit('input', businessId) &&
            this.$emit('change', businessId);
        } else {
          this.$message.error(res.message || '上传失败');
        }
      });
    },
    /**@desc 删除 */
    onRemove(file) {
      let idx = this.fileList.findIndex(e => {
          return e.uid === file.uid;
        }),
        deleteFile = this.fileList[idx] || {};
      this.ajax.deleteFile(deleteFile.uid).then(res => {
        if (!res.success) {
          this.$message.error(res.message || '删除失败');
          return;
        }
        this.$message.success('删除成功');
        this.fileList.splice(idx, 1);

        if (!this.fileList.length) {
          this.$emit('input', null);
          this.$emit('change', null);
        }
      });
    },
    onAbort(file) {
      this.$refs.tsUpload.abort(file);
      let idx = this.fileList.findIndex(e => {
        return e.uid === file.uid;
      });
      this.fileList.splice(idx, 1);
    },
    /**@desc 图片点击下载 */
    onUpload(file) {
      let a = document.createElement('a');
      a.href = file.url;
      a.click();
    },
    /**@desc 图片点击预览 */
    onPreview(file) {
      if (commonUtils.isDoc(file.fileName)) {
        commonUtils.viewerDocBase(file.realPath, file.fileName);
      } else {
        this.previewFile = location.origin + file.url;
        this.previewFileList = this.fileList
          .filter(item => commonUtils.isImg(item.name))
          .map(item => location.origin + item.url);
        this.$nextTick(() => {
          this.$refs.preview.clickHandler();
        });
      }
    }
  }
};
</script>

<style></style>
