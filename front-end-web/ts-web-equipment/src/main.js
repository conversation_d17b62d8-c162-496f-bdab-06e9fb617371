import './public-path';

import Vue from 'vue';
import clipboard from 'clipboard';
Vue.prototype.clipboard = clipboard;

import App from './App.vue';
import '@/assets/iconfont/iconfont.css';

import { getRouter } from './router';

import component from '@/components/index.js';
import store from './store';

Vue.prototype.$store = store;

import unit from './unit/index';

import '@/iconfont/font/iconfont.css';
import '@/iconfont/awesome/font-awesome.min.css';
import '@/iconfont/oa-pc/iconfont.css';
import '@/iconfont/layui-font/iconfont.css';

import axios from 'axios';

import * as echarts from 'echarts';

import dayjs from 'dayjs';

import '@/unit/directive.js';

import Print from '@/util/print.js';
Vue.use(Print);

import VXETable from 'vxe-table';
import 'vxe-table/lib/style.css';
import '@/assets/css/var.scss';
VXETable.config({
  tooltip: {
    position(params, event) {
      return {
        top: event.clientY + 10 + 'px', // 10px 是偏移量，根据需要调整
        left: event.clientX + 'px'
      };
    }
  }
});
Vue.use(VXETable);

Vue.prototype.$EventBus = new Vue();
import event from '@/unit/event.js';
Vue.prototype.$event = event;

// axios.defaults.baseUrl = '';
Vue.use(unit); //初始化要使用的各种工具
Vue.use(component); //初始化要使用的各种组件
Vue.prototype.$axios = axios;
Vue.prototype.$echarts = echarts;
Vue.prototype.$dayjs = dayjs;

import moment from 'moment';

Vue.prototype.$moment = moment;

Vue.config.productionTip = false;
let instance = null;

import WebBusiComponent from '@trasen-oa/web_busi_component';
import '@trasen-oa/web_busi_component/lib/web_busi_component.css';
Vue.use(WebBusiComponent);

import tsElement from '@trasen-oa/trasen-ui-web';
import '@trasen-oa/trasen-ui-web/lib/tsElement.css';
Vue.use(tsElement, { size: 'medium' });

//路由切换刷新
Vue.mixin({
  beforeRouteEnter(to, from, next) {
    next(vm => {
      vm.refresh && typeof vm.refresh == 'function' && vm.refresh();
    });
  }
});

function render(props = {}) {
  const router = getRouter(props);

  instance = new Vue({
    router,
    store,
    render: h => h(App)
  }).$mount(document.getElementById(process.env.VUE_APP_CONTAINER));
}

if (!window.__POWERED_BY_QIANKUN__) {
  render();
}

export async function bootstrap(props) {}

export async function mount(props) {
  Vue.prototype.qiankunParentNode = document.getElementById(
    process.env.VUE_APP_CONTAINER
  ).parentNode;
  // 设置主应用下发的方法
  Object.keys(props.fn).forEach(method => {
    Vue.prototype[`$${method}`] = props.fn[method];
  });
  //监听主应用下发用户信息
  props.onGlobalStateChange((state, prevState) => {});
  // 设置通讯
  store.state.common.userInfo = props.fn.getUserInfo(); //主动获取用户信息
  store.commit('common/setData', { label: 'token', value: props.data.token });
  store.commit('common/setData', {
    label: 'hospitalCode',
    value: props.data.hospitalCode
  });
  store.commit('common/setData', {
    label: 'systemCustomCode',
    value: props.data.systemCustomCode
  });

  store.commit('common/setData', {
    label: 'personalSortData',
    value: props.data.personalSortData
  });
  Vue.prototype.$config = props.config; //配置信息
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  Vue.prototype.$setGlobalState = props.setGlobalState;
  render(props);
}

export async function update(props) {
  instance.$emit('updateDataQianKun', props);
}

export async function unmount() {
  instance.$destroy();
}
