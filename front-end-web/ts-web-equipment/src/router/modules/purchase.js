export default [
  {
    path: '/purchase/apply',
    component: resolve =>
      require([
        `@/views/purchaseManagement/myPurchaseApplication/index.vue`
      ], resolve),
    styleName: '',
    name: '采购申请'
  },
  {
    path: '/purchase/approval',
    component: resolve =>
      require([
        `@/views/purchaseManagement/purchaseApproval/index.vue`
      ], resolve),
    styleName: '',
    name: '采购审批'
  },
  {
    path: '/purchase/view',
    component: resolve =>
      require([`@/views/purchaseManagement/purchaseView/index.vue`], resolve),
    styleName: '',
    name: '采购办理查阅'
  },
  {
    path: '/purchase/discussion-table',
    component: resolve =>
      require([
        `@/views/purchaseManagement/purchaseApprovalTable/discussionTable/index.vue`
      ], resolve),
    styleName: '',
    name: '上会事项表'
  },
  {
    path: '/purchase/evaluation-table',
    component: resolve =>
      require([
        `@/views/purchaseManagement/purchaseApprovalTable/evaluationTable/index.vue`
      ], resolve),
    styleName: '',
    name: '会议评审表'
  },
  {
    path: '/purchase/purchase-result',
    component: resolve =>
      require([`@/views/purchaseManagement/purchaseResult/index.vue`], resolve),
    styleName: '',
    name: '采购结果'
  }
];
