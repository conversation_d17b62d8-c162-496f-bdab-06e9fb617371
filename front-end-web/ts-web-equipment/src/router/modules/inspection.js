export default [
  {
    path: '/inspection/equipment',
    component: resolve =>
      require([
        `@/views/inspectionManagement/inspectionEquipment/index.vue`
      ], resolve),
    styleName: '',
    name: '巡检设备'
  },
  {
    path: '/inspection/plan',
    component: resolve =>
      require([
        `@/views/inspectionManagement/inspectionPlan/index.vue`
      ], resolve),
    styleName: '',
    name: '巡检计划'
  },
  {
    path: '/inspection/template',
    component: resolve =>
      require([
        `@/views/inspectionManagement/inspectionTemplate/index.vue`
      ], resolve),
    styleName: '',
    name: '巡检模板'
  },
  {
    path: '/inspection/abnormal',
    component: resolve =>
      require([
        `@/views/inspectionManagement/inspectionAbnormal/index.vue`
      ], resolve),
    styleName: '',
    name: '巡检异常'
  }
];
