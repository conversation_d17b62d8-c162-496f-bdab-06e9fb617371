function routerResolve(resolve) {
  let _this = this;
  return function(module) {
    let component = module.default;
    !component.mixins && (component.mixins = []);
    component.mixins.push({
      data() {
        return {
          menuLimits: [] // 路由按钮权限按钮
        };
      },
      mounted() {
        this.getMenuLimitList();
      },
      methods: {
        async getMenuLimitList() {
          this.menuLimits = await this.$getMenuSource();
        }
      }
    });
    return resolve.call(_this, module);
  };
}
export default [
  {
    path: '/inventory-management/inventory-device',
    component: resolve =>
      require([
        `@/views/inventoryManagement/inventoryDevice/index.vue`
      ], routerResolve(resolve)),
    name: '资产台账'
  },
  {
    path: '/inventory-management/storage-record',
    component: resolve =>
      require([
        `@/views/inventoryManagement/storageRecord/index.vue`
      ], routerResolve(resolve)),
    name: '入库设备'
  },
  {
    path: '/inventory-management/outbound-record',
    component: resolve =>
      require([
        `@/views/inventoryManagement/outboundRecord/index.vue`
      ], routerResolve(resolve)),
    name: '出库设备'
  }
];
