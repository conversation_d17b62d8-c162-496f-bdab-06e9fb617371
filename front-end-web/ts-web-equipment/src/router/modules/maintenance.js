export default [
  {
    path: '/maintenance/equipment',
    component: resolve =>
      require([
        `@/views/maintenanceManagement/maintenanceEquipment/index.vue`
      ], resolve),
    styleName: '',
    name: '保养设备'
  },
  {
    path: '/maintenance/plan',
    component: resolve =>
      require([
        `@/views/maintenanceManagement/maintenancePlan/index.vue`
      ], resolve),
    styleName: '',
    name: '保养计划'
  },
  {
    path: '/maintenance/work-order',
    component: resolve =>
      require([
        `@/views/maintenanceManagement/maintenanceWorkOrder/index.vue`
      ], resolve),
    styleName: '',
    name: '保养工单'
  },
  {
    path: '/maintenance/template',
    component: resolve =>
      require([
        `@/views/maintenanceManagement/maintenanceTemplate/index.vue`
      ], resolve),
    styleName: '',
    name: '保养模板'
  },
  {
    path: '/maintenance/abnormal',
    component: resolve =>
      require([
        `@/views/maintenanceManagement/maintenanceAbnormal/index.vue`
      ], resolve),
    styleName: '',
    name: '保养异常'
  }
];
