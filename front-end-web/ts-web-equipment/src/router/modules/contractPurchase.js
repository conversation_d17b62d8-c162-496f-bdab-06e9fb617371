export default [
  {
    path: '/purchase-management/apply',
    component: resolve =>
      require([
        `@/views/contractPurchase/purchaseManagement/apply/index.vue`
      ], resolve),
    name: '采购申请'
  },
  {
    path: '/purchase-management/approve',
    component: resolve =>
      require([
        `@/views/contractPurchase/purchaseManagement/approve/index.vue`
      ], resolve),
    name: '采购审批'
  },
  {
    path: '/purchase-management/view',
    component: resolve =>
      require([
        `@/views/contractPurchase/purchaseManagement/view/index.vue`
      ], resolve),
    name: '办理查阅'
  },
  {
    path: '/purchase-management/order-management',
    component: resolve =>
      require([`@/views/contractPurchase/orderManagement/index.vue`], resolve),
    name: '采购订单管理'
  }
];
