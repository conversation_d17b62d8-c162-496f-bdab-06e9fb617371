export default [
  {
    path: '/measurement/equipment',
    component: resolve =>
      require([
        `@/views/measurementManagement/measurementEquipment/index.vue`
      ], resolve),
    styleName: '',
    name: '设备计量'
  },
  {
    path: '/measurement/record',
    component: resolve =>
      require([
        `@/views/measurementManagement/measurementRecord/index.vue`
      ], resolve),
    styleName: '',
    name: '计量记录'
  },
  {
    path: '/measurement/abnormal',
    component: resolve =>
      require([
        `@/views/measurementManagement/measurementAbnormal/index.vue`
      ], resolve),
    styleName: '',
    name: '计量异常'
  }
];
