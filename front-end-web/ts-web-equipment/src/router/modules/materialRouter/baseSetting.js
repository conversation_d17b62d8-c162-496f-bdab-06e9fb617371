export default [
  {
    path: '/material/base-setting/material-warehouse',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/materialWarehouse/index.vue`
      ], resolve),
    name: '物资库房'
  },
  {
    path: '/material/base-setting/accounting-period',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/accountingPeriod/index.vue`
      ], resolve),
    name: '会计期间'
  },
  {
    path: '/material/base-setting/material-classification',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/materialClassification/index.vue`
      ], resolve),
    name: '物资分类'
  },
  {
    path: '/material/base-setting/warehouse-be-material-classsification',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/warehouseBeMaterialClassification/index.vue`
      ], resolve),
    name: '库房对应物资分类'
  },
  {
    path: '/material/base-setting/warehouse-be-person',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/warehouseBePerson/index.vue`
      ], resolve),
    name: '库房对应用户'
  },
  {
    path: '/material/base-setting/material-dictionary',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/materialDictionary/index.vue`
      ], resolve),
    name: '物资字典'
  },
  {
    path: '/material/base-setting/method-code-maintenance',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/methodCodeMaintenance/index.vue`
      ], resolve),
    name: '方式代码维护'
  },
  {
    path: '/material/base-setting/material-manufacturer',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/materialManufacturer/index.vue`
      ], resolve),
    name: '生产厂家'
  },
  {
    path: '/material/base-setting/material-supplier',
    component: resolve =>
      require([
        `@/views/moduleMaterial/basicSetting/materialSupplier/index.vue`
      ], resolve),
    name: '供应商'
  }
];
