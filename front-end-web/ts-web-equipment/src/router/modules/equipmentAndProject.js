export default [
  {
    path: '/management/equipment-parameter',
    component: resolve =>
      require([`@/views/equipmentManagement/equipment-parameter.vue`], resolve),
    styleName: '',
    name: '设备参数库'
  },
  {
    path: '/management/equipment-management',
    component: resolve =>
      require([`@/views/equipmentManagement/index.vue`], resolve),
    styleName: '',
    name: '设备管理'
  },
  {
    path: '/management/equipment-classification',
    component: resolve =>
      require([
        `@/views/equipmentManagement/classification-management.vue`
      ], resolve),
    styleName: '',
    name: '设备分类'
  },
  {
    path: '/management/construction-management',
    component: resolve =>
      require([`@/views/constructionManagement/index.vue`], resolve),
    styleName: '',
    name: '工程项目管理'
  },
  {
    path: '/management/service-management',
    component: resolve =>
      require([`@/views/serviceManagement/index.vue`], resolve),
    styleName: '',
    name: '服务项目管理'
  },
  {
    path: '/management/equipment-ledger',
    component: resolve =>
      require([`@/views/equipmentLedger/index.vue`], resolve),
    styleName: '',
    name: '设备台账'
  },
  {
    path: '/management/install-acceptance',
    component: resolve =>
      require([`@/views/installAcceptance/index.vue`], resolve),
    styleName: '',
    name: '安装验收'
  }
];
