(window._iconfont_svg_string_3698034 =
  '<svg><symbol id="icon-jian" viewBox="0 0 1024 1024"><path d="M507.904 52.224q95.232 0 179.2 36.352t145.92 98.304 98.304 145.408 36.352 178.688-36.352 179.2-98.304 145.92-145.92 98.304-179.2 36.352-178.688-36.352-145.408-98.304-98.304-145.92-36.352-179.2 36.352-178.688 98.304-145.408 145.408-98.304 178.688-36.352zM736.256 573.44q30.72 0 55.296-15.872t24.576-47.616q0-30.72-24.576-45.568t-55.296-14.848l-452.608 0q-30.72 0-56.32 14.848t-25.6 45.568q0 31.744 25.6 47.616t56.32 15.872l452.608 0z"  ></path></symbol><symbol id="icon-xinzeng" viewBox="0 0 1024 1024"><path d="M512.11776 47.2576C255.40096 47.2576 47.14496 255.27808 47.14496 512c0 256.60416 208.************ 464.9728 464.7424 256.48128 0 464.7424-208.13824 464.7424-464.7424 0-256.72192-208.26112-464.7424-464.7424-464.7424z m232.3712 522.77248h-174.44864v174.34112h-116.0704v-174.34112H279.75168V453.84704h174.21824V279.6288h116.0704v174.21824h174.44864v116.18304z" fill="" ></path></symbol></svg>'),
  (function(n) {
    var t = (t = document.getElementsByTagName('script'))[t.length - 1],
      e = t.getAttribute('data-injectcss'),
      t = t.getAttribute('data-disable-injectsvg');
    if (!t) {
      var i,
        o,
        d,
        c,
        s,
        a = function(t, e) {
          e.parentNode.insertBefore(t, e);
        };
      if (e && !n.__iconfont__svg__cssinject__) {
        n.__iconfont__svg__cssinject__ = !0;
        try {
          document.write(
            '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>'
          );
        } catch (t) {
          console && console.log(t);
        }
      }
      (i = function() {
        var t,
          e = document.createElement('div');
        (e.innerHTML = n._iconfont_svg_string_3698034),
          (e = e.getElementsByTagName('svg')[0]) &&
            (e.setAttribute('aria-hidden', 'true'),
            (e.style.position = 'absolute'),
            (e.style.width = 0),
            (e.style.height = 0),
            (e.style.overflow = 'hidden'),
            (e = e),
            (t = document.body).firstChild
              ? a(e, t.firstChild)
              : t.appendChild(e));
      }),
        document.addEventListener
          ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
            ? setTimeout(i, 0)
            : ((o = function() {
                document.removeEventListener('DOMContentLoaded', o, !1), i();
              }),
              document.addEventListener('DOMContentLoaded', o, !1))
          : document.attachEvent &&
            ((d = i),
            (c = n.document),
            (s = !1),
            r(),
            (c.onreadystatechange = function() {
              'complete' == c.readyState &&
                ((c.onreadystatechange = null), l());
            }));
    }
    function l() {
      s || ((s = !0), d());
    }
    function r() {
      try {
        c.documentElement.doScroll('left');
      } catch (t) {
        return void setTimeout(r, 50);
      }
      l();
    }
  })(window);
