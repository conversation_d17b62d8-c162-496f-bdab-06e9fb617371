<template>
  <div
    class="el-form-item"
    :class="[
      {
        'el-form-item--feedback': elForm && elForm.statusIcon,
        'is-error': validateState === 'error',
        'is-validating': validateState === 'validating',
        'is-success': validateState === 'success',
        'is-required': isRequired || required,
        'is-no-asterisk': elForm && elForm.hideRequiredAsterisk
      },
      sizeClass ? 'el-form-item--' + sizeClass : ''
    ]"
  >
    <label-wrap
      :is-auto-width="labelStyle && labelStyle.width === 'auto'"
      :update-all="form.labelWidth === 'auto'"
    >
      <label
        :for="labelFor"
        class="el-form-item__label"
        :style="labelStyle"
        v-if="label || $slots.label"
      >
        <slot name="label">{{ label + form.labelSuffix }}</slot>
      </label>
    </label-wrap>
    <div class="el-form-item__content" :style="contentStyle">
      <slot></slot>
      <transition name="el-zoom-in-top">
        <slot
          v-if="
            validateState === 'error' &&
              showMessage &&
              form.showMessage &&
              showErrorMessageBox
          "
          name="error"
          :error="validateMessage"
        >
          <div
            class="el-form-item__error"
            :class="{
              'el-form-item__error--inline':
                typeof inlineMessage === 'boolean'
                  ? inlineMessage
                  : (elForm && elForm.inlineMessage) || false
            }"
          >
            {{ validateMessage }}
          </div>
        </slot>
      </transition>
    </div>
  </div>
</template>
<script>
import AsyncValidator from 'async-validator';
import ElFormItem from '@trasen/trasen-element-ui/packages/form-item/index';
import { noop, getPropByPath } from 'element-ui/src/utils/util';

export default {
  extends: ElFormItem,
  data() {
    return {
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 Start-1 */
      showErrorMessageBox: false, //是否显示错误提示
      errorMessageTimer: null //错误提示框的定时器
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 End-1 */
    };
  },
  methods: {
    validate(trigger, callback = noop) {
      this.validateDisabled = false;
      const rules = this.getFilteredRule(trigger);
      if ((!rules || rules.length === 0) && this.required === undefined) {
        callback();
        return true;
      }
      this.validateState = 'validating';
      const descriptor = {};
      if (rules && rules.length > 0) {
        rules.forEach(rule => {
          delete rule.trigger;
        });
      }
      descriptor[this.prop] = rules;
      const validator = new AsyncValidator(descriptor);
      const model = {};
      model[this.prop] = this.fieldValue;
      validator.validate(
        model,
        { firstFields: true },
        (errors, invalidFields) => {
          this.validateState = !errors ? 'success' : 'error';
          this.validateMessage = errors ? errors[0].message : '';
          /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 Start-2 */
          if (errors) {
            this.showErrorMessageBox = true;
            this.errorMessageTimer && clearTimeout(this.errorMessageTimer);
            this.$nextTick(() => {
              this.errorMessageTimer = setTimeout(() => {
                this.showErrorMessageBox = false;
              }, 5000);
            });
          }
          /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 End-2 */
          callback(this.validateMessage, invalidFields);
          this.elForm &&
            this.elForm.$emit(
              'validate',
              this.prop,
              !errors,
              this.validateMessage || null
            );
        }
      );
    },
    clearValidate() {
      this.validateState = '';
      this.validateMessage = '';
      this.validateDisabled = false;
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 Start-3 */
      this.showErrorMessageBox = false;
      this.errorMessageTimer && clearTimeout(this.errorMessageTimer);
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 End-3 */
    },
    resetField() {
      this.validateState = '';
      this.validateMessage = '';
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 Start-4 */
      this.showErrorMessageBox = false;
      this.errorMessageTimer && clearTimeout(this.errorMessageTimer);
      /**@desc 何锴 2021-10-27 添加属性，控制一定时间后错误提示消失 End-4 */
      let model = this.form.model;
      let value = this.fieldValue;
      let path = this.prop;
      if (path.indexOf(':') !== -1) {
        path = path.replace(/:/, '.');
      }
      let prop = getPropByPath(model, path, true);
      this.validateDisabled = true;
      if (Array.isArray(value)) {
        prop.o[prop.k] = [].concat(this.initialValue);
      } else {
        prop.o[prop.k] = this.initialValue;
      }
      // reset validateDisabled after onFieldChange triggered
      this.$nextTick(() => {
        this.validateDisabled = false;
      });
      this.broadcast('ElTimeSelect', 'fieldReset', this.initialValue);
    }
  }
};
</script>

<style lang="scss" scoped>
.el-form-item {
  margin-bottom: 8px !important;
  .el-form-item__label {
    padding-right: 8px;
  }
  .el-form-item__error {
    position: absolute;
    z-index: 10;
    padding: 2px 8px;
    top: calc(100% + 4px);
    background-color: #fbe3e3;
    border-radius: 4px;
    height: 24px;
    display: flex;
    align-items: center;
  }
}
</style>
