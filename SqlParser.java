package cn.trasen.ams.common.interceptor;

import java.util.Arrays;

/**
 * SQL解析器 - 更准确地解析SQL结构
 * 解决原版本在复杂SQL场景下的解析问题
 */
public class SqlParser {
    private final String sql;
    private final String upperSql;

    public SqlParser(String sql) {
        this.sql = sql;
        this.upperSql = sql.toUpperCase();
    }

    /**
     * 获取主表名
     */
    public String getMainTable() {
        int fromIndex = findMainFromPosition();
        if (fromIndex == -1) {
            return null;
        }

        String afterFrom = sql.substring(fromIndex + 4).trim();
        String[] parts = afterFrom.split("\\s+", 3);
        
        return parts.length > 0 ? cleanTableName(parts[0]) : null;
    }

    /**
     * 获取表别名
     */
    public String getTableAlias() {
        int fromIndex = findMainFromPosition();
        if (fromIndex == -1) {
            return null;
        }

        String afterFrom = sql.substring(fromIndex + 4).trim();
        String[] parts = afterFrom.split("\\s+", 3);
        
        if (parts.length >= 2) {
            String secondPart = parts[1];
            if ("AS".equalsIgnoreCase(secondPart) && parts.length >= 3) {
                return cleanTableName(parts[2]);
            } else if (!isSqlKeyword(secondPart)) {
                return cleanTableName(secondPart);
            }
        }
        
        return null;
    }

    /**
     * 查找主查询的WHERE位置
     */
    public int findMainWherePosition() {
        int position = 0;
        while (true) {
            int whereIndex = upperSql.indexOf(" WHERE ", position);
            if (whereIndex == -1) {
                break;
            }
            
            if (!isInSubquery(whereIndex)) {
                return whereIndex;
            }
            
            position = whereIndex + 7;
        }
        return -1;
    }

    /**
     * 查找WHERE子句插入位置
     */
    public int findWhereInsertPosition() {
        // 找到FROM子句结束位置
        int fromIndex = findMainFromPosition();
        if (fromIndex == -1) {
            return sql.length();
        }

        // 查找FROM子句后的第一个关键字
        String afterFrom = sql.substring(fromIndex);
        String[] keywords = {" JOIN ", " LEFT ", " RIGHT ", " INNER ", " OUTER ", 
                           " WHERE ", " GROUP BY ", " ORDER BY ", " HAVING ", " LIMIT ", " UNION "};
        
        int minIndex = Integer.MAX_VALUE;
        for (String keyword : keywords) {
            int index = afterFrom.toUpperCase().indexOf(keyword);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }
        
        if (minIndex == Integer.MAX_VALUE) {
            return sql.length();
        }
        
        // 如果找到的是JOIN相关关键字，需要跳过整个JOIN子句
        String foundKeyword = getKeywordAt(afterFrom, minIndex);
        if (isJoinKeyword(foundKeyword)) {
            return findPositionAfterJoins(fromIndex + minIndex);
        }
        
        return fromIndex + minIndex;
    }

    /**
     * 查找主FROM位置
     */
    private int findMainFromPosition() {
        int position = 0;
        int lastFromIndex = -1;
        
        while (true) {
            int fromIndex = upperSql.indexOf(" FROM ", position);
            if (fromIndex == -1) {
                fromIndex = upperSql.indexOf("FROM ", position);
                if (fromIndex != 0) fromIndex = -1; // 确保FROM在开头
            }
            
            if (fromIndex == -1) {
                break;
            }
            
            if (!isInSubquery(fromIndex)) {
                lastFromIndex = fromIndex;
            }
            
            position = fromIndex + 5;
        }
        
        return lastFromIndex;
    }

    /**
     * 判断位置是否在子查询中
     */
    private boolean isInSubquery(int position) {
        int leftParens = 0;
        int rightParens = 0;
        boolean inString = false;
        char stringChar = 0;
        boolean inComment = false;
        
        for (int i = 0; i < position; i++) {
            char c = sql.charAt(i);
            
            // 处理注释
            if (!inString && !inComment) {
                if (c == '/' && i + 1 < sql.length() && sql.charAt(i + 1) == '*') {
                    inComment = true;
                    i++; // 跳过下一个字符
                    continue;
                }
            }
            
            if (inComment) {
                if (c == '*' && i + 1 < sql.length() && sql.charAt(i + 1) == '/') {
                    inComment = false;
                    i++; // 跳过下一个字符
                }
                continue;
            }
            
            // 处理字符串
            if (!inString) {
                if (c == '\'' || c == '"') {
                    inString = true;
                    stringChar = c;
                } else if (c == '(') {
                    leftParens++;
                } else if (c == ')') {
                    rightParens++;
                }
            } else {
                if (c == stringChar && (i == 0 || sql.charAt(i - 1) != '\\')) {
                    inString = false;
                }
            }
        }
        
        return leftParens > rightParens;
    }

    /**
     * 清理表名（移除引号、schema前缀等）
     */
    private String cleanTableName(String tableName) {
        if (tableName == null) return null;
        
        // 移除反引号
        if (tableName.startsWith("`") && tableName.endsWith("`")) {
            tableName = tableName.substring(1, tableName.length() - 1);
        }
        
        // 移除schema前缀
        if (tableName.contains(".")) {
            String[] parts = tableName.split("\\.");
            tableName = parts[parts.length - 1];
        }
        
        return tableName;
    }

    /**
     * 判断是否为SQL关键字
     */
    private boolean isSqlKeyword(String word) {
        String[] keywords = {"WHERE", "GROUP", "ORDER", "HAVING", "LIMIT", "UNION", 
                           "JOIN", "LEFT", "RIGHT", "INNER", "OUTER", "ON", "AS"};
        return Arrays.stream(keywords).anyMatch(keyword -> keyword.equalsIgnoreCase(word));
    }

    /**
     * 判断是否为JOIN相关关键字
     */
    private boolean isJoinKeyword(String keyword) {
        String[] joinKeywords = {"JOIN", "LEFT", "RIGHT", "INNER", "OUTER"};
        return Arrays.stream(joinKeywords).anyMatch(kw -> kw.equalsIgnoreCase(keyword));
    }

    /**
     * 获取指定位置的关键字
     */
    private String getKeywordAt(String text, int position) {
        String upperText = text.toUpperCase();
        String[] keywords = {" JOIN ", " LEFT ", " RIGHT ", " INNER ", " OUTER "};
        
        for (String keyword : keywords) {
            if (upperText.substring(position).startsWith(keyword)) {
                return keyword.trim();
            }
        }
        return "";
    }

    /**
     * 查找JOIN子句结束后的位置
     */
    private int findPositionAfterJoins(int startPosition) {
        String remaining = sql.substring(startPosition);
        String upperRemaining = remaining.toUpperCase();
        
        String[] endKeywords = {" WHERE ", " GROUP BY ", " ORDER BY ", " HAVING ", " LIMIT ", " UNION "};
        
        int minIndex = Integer.MAX_VALUE;
        for (String keyword : endKeywords) {
            int index = upperRemaining.indexOf(keyword);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }
        
        return minIndex == Integer.MAX_VALUE ? sql.length() : startPosition + minIndex;
    }

    /**
     * 检查SQL是否包含UNION
     */
    public boolean containsUnion() {
        return upperSql.contains(" UNION ");
    }

    /**
     * 分割UNION查询
     */
    public String[] splitUnionQueries() {
        if (!containsUnion()) {
            return new String[]{sql};
        }
        
        // 简单分割，实际应用中可能需要更复杂的逻辑来处理嵌套的UNION
        return sql.split("(?i)\\s+UNION\\s+(ALL\\s+)?");
    }
}
