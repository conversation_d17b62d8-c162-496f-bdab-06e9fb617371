# SsoOrgCodeInterceptor 深度分析报告

## 📋 概述

SsoOrgCodeInterceptor 是一个 MyBatis 拦截器，用于自动为 SELECT 语句添加机构代码条件 (`sso_org_code`)，实现多租户数据隔离。

## 🔍 核心逻辑分析

### 工作流程
1. 拦截所有 SQL 执行
2. 解析 SELECT 语句，识别主表
3. 检查表是否需要添加机构条件
4. 检查 SQL 中是否已存在机构条件
5. 自动添加 `主表.sso_org_code = '用户机构代码'` 条件

## ⚠️ 发现的主要问题

### 1. 子查询处理缺陷 (🔴 高风险)

**问题代码：**
```java
private boolean isInSubquery(String sql, int position) {
    int leftParens = 0;
    int rightParens = 0;
    
    for (int i = 0; i < position; i++) {
        char c = sql.charAt(i);
        if (c == '(') leftParens++;
        else if (c == ')') rightParens++;
    }
    
    return leftParens > rightParens;
}
```

**风险场景：**
```sql
-- 字符串中的括号会被误判
SELECT * FROM user_table WHERE name = 'test(abc)' AND status = 1

-- 函数调用会被误判为子查询
SELECT CONCAT('(', name, ')') FROM user_table WHERE active = 1

-- 注释中的括号
SELECT * FROM user_table /* (comment) */ WHERE id = 1
```

**影响：** 可能导致主查询被误判为子查询，跳过机构条件添加，造成数据泄露。

### 2. 复杂 JOIN 查询处理不当 (🟡 中风险)

**问题描述：**
当前实现只给主表添加机构条件，但在复杂 JOIN 场景下可能不够：

```sql
-- 原始SQL
SELECT u.*, d.name as dept_name 
FROM user_table u 
LEFT JOIN dept_table d ON u.dept_id = d.id 
WHERE u.status = 1

-- 拦截器处理后
SELECT u.*, d.name as dept_name 
FROM user_table u 
LEFT JOIN dept_table d ON u.dept_id = d.id 
WHERE user_table.sso_org_code = 'ORG001' AND u.status = 1
```

**风险：** 如果 `dept_table` 也需要机构隔离，可能会查询到其他机构的部门数据。

### 3. UNION 查询支持不完整 (🟡 中风险)

**问题描述：**
```sql
-- UNION查询只会给第一个SELECT添加条件
SELECT * FROM user_table WHERE status = 1
UNION
SELECT * FROM user_archive WHERE status = 1

-- 处理后可能变成
SELECT * FROM user_table WHERE user_table.sso_org_code = 'ORG001' AND status = 1
UNION
SELECT * FROM user_archive WHERE status = 1  -- 缺少机构条件！
```

### 4. 正则表达式性能问题 (🟢 低风险)

**问题代码：**
```java
private static final Pattern ORG_CODE_PATTERN = Pattern.compile(
    "\\b(\\w+\\.)?`?sso_org_code`?\\s*[=<>!]|\\b(\\w+\\.)?`?sso_org_code`?\\s+LIKE|\\b(\\w+\\.)?`?sso_org_code`?\\s+IS\\s+(?:NOT\\s+)?NULL",
    Pattern.CASE_INSENSITIVE
);
```

**影响：** 复杂正则表达式在大型 SQL 上性能较差，且可能误匹配注释或字符串中的内容。

### 5. 反射修改 SQL 的兼容性问题 (🟡 中风险)

**问题代码：**
```java
java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
sqlField.setAccessible(true);
sqlField.set(boundSql, modifiedSql);
```

**风险：** 不同版本的 MyBatis 可能有不同的内部结构，反射操作可能失败。

## 🧪 测试用例覆盖

### 当前缺失的测试场景：

1. **复杂子查询**
   ```sql
   SELECT * FROM user WHERE id IN (
       SELECT user_id FROM role_user WHERE role_id IN (
           SELECT id FROM role WHERE name = 'admin'
       )
   )
   ```

2. **多层 JOIN**
   ```sql
   SELECT u.name, d.name, o.name 
   FROM user u 
   JOIN dept d ON u.dept_id = d.id 
   JOIN org o ON d.org_id = o.id
   ```

3. **UNION 查询**
   ```sql
   SELECT * FROM current_user WHERE active = 1
   UNION ALL
   SELECT * FROM archived_user WHERE active = 1
   ```

4. **存在性检查边界情况**
   ```sql
   -- 注释中的sso_org_code
   SELECT * FROM user /* sso_org_code test */ WHERE status = 1
   
   -- 字符串中的sso_org_code
   SELECT * FROM user WHERE description = 'field sso_org_code info'
   ```

## 🚀 性能优化建议

### 1. 缓存机制
```java
// 添加SQL解析结果缓存
private static final Map<String, SqlParseResult> PARSE_CACHE = new ConcurrentHashMap<>();
```

### 2. 避免正则表达式
使用词法分析器替代正则表达式：
```java
// 使用SqlTokenizer进行更精确的解析
SqlTokenizer tokenizer = new SqlTokenizer(sql);
while (tokenizer.hasNext()) {
    String token = tokenizer.next();
    // 处理token
}
```

### 3. 预编译优化
对于频繁执行的SQL模式，可以预编译处理逻辑。

## 🔧 改进建议

### 1. 立即修复 (高优先级)

1. **修复子查询判断逻辑**
   - 正确处理字符串和注释中的括号
   - 实现状态机解析器

2. **完善UNION查询支持**
   - 为每个UNION分支都添加机构条件
   - 处理UNION ALL场景

### 2. 中期改进 (中优先级)

1. **增强JOIN查询处理**
   - 分析所有涉及的表
   - 为需要隔离的表都添加条件

2. **添加配置化支持**
   ```properties
   # 配置需要隔离的表
   sso.org.interceptor.include-tables=user_table,dept_table,role_table
   # 配置排除的表
   sso.org.interceptor.exclude-tables=system_config,dictionary
   ```

### 3. 长期优化 (低优先级)

1. **SQL解析器重构**
   - 使用专业的SQL解析库（如JSqlParser）
   - 提供更准确的语法分析

2. **监控和告警**
   - 添加SQL修改日志
   - 监控拦截器性能指标

## 📊 风险评估

| 风险类型 | 风险等级 | 影响范围 | 建议处理时间 |
|---------|---------|---------|-------------|
| 数据泄露 | 高 | 多租户隔离失效 | 立即修复 |
| 性能问题 | 中 | 查询响应变慢 | 1-2周内 |
| 兼容性问题 | 中 | 升级MyBatis失败 | 1个月内 |
| 功能缺失 | 低 | 部分SQL类型不支持 | 2-3个月内 |

## 🎯 推荐方案

### 方案一：渐进式改进 (推荐)
1. 立即修复高风险问题
2. 逐步完善功能
3. 保持向后兼容

### 方案二：重构替换
1. 使用专业SQL解析库
2. 重新设计架构
3. 全面测试验证

## 📝 结论

当前的 SsoOrgCodeInterceptor 在基本场景下能够工作，但在复杂SQL场景下存在健壮性和安全性问题。建议：

1. **立即修复**子查询判断和UNION查询处理问题
2. **增加全面的测试用例**覆盖各种复杂场景
3. **考虑引入专业的SQL解析库**提升解析准确性
4. **建立监控机制**确保拦截器正常工作

通过这些改进，可以显著提升拦截器的健壮性和安全性，确保多租户数据隔离的可靠性。
