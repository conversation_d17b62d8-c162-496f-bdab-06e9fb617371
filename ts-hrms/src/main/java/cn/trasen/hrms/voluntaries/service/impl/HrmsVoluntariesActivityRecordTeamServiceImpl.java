package cn.trasen.hrms.voluntaries.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.voluntaries.dao.HrmsVoluntariesActivityRecordTeamMapper;
import cn.trasen.hrms.voluntaries.model.HrmsVoluntariesActivityRecord;
import cn.trasen.hrms.voluntaries.model.HrmsVoluntariesActivityRecordTeam;
import cn.trasen.hrms.voluntaries.service.HrmsVoluntariesActivityRecordService;
import cn.trasen.hrms.voluntaries.service.HrmsVoluntariesActivityRecordTeamService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName HrmsVoluntariesActivityRecordTeamServiceImpl
 * @Description TODO
 * @date 2023��9��20�� ����2:36:59
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsVoluntariesActivityRecordTeamServiceImpl implements HrmsVoluntariesActivityRecordTeamService {

	@Autowired
	private HrmsVoluntariesActivityRecordTeamMapper mapper;
	@Autowired
	private HrmsVoluntariesActivityRecordService hrmsVoluntariesActivityRecordService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsVoluntariesActivityRecordTeam record) {
		record.setId(String.valueOf(IdWork.id.nextId()));
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//拿到所有人员 给人家报名
		if(record.getTeamMemberName() != null && record.getTeamMemberIds() != null ) {
			List<HrmsVoluntariesActivityRecord> list =new ArrayList<>();

			List<String> _ids = Arrays.asList(record.getTeamMemberIds().split(","));
			for(int i=0;i<_ids.size();i++) {
				HrmsVoluntariesActivityRecord  saveBean = new HrmsVoluntariesActivityRecord();
				saveBean.setTeamId(record.getId());
				saveBean.setTeamName(record.getTeamName());
				saveBean.setRecordIdcard(_ids.get(i));
				saveBean.setActivityDuration(record.getActivityDuration());
				saveBean.setActivityId(record.getActivityId());
				saveBean.setRecordType("2");
				saveBean.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				list.add(saveBean);
			}
			hrmsVoluntariesActivityRecordService.laterSave(list);
		}else {
			throw new BusinessException("团体没有选择成员");
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsVoluntariesActivityRecordTeam record) {
		//拿到所有人员 给人家报名
		if(record.getTeamMemberName() != null && record.getTeamMemberIds() != null ) {
			deletePersonById(record.getId());	//先删除团体成员重新添加
			List<HrmsVoluntariesActivityRecord> list =new ArrayList<>();
			List<String> _ids = Arrays.asList(record.getTeamMemberIds().split(","));
			for(int i=0;i<_ids.size();i++) {
				HrmsVoluntariesActivityRecord  saveBean = new HrmsVoluntariesActivityRecord();
				saveBean.setTeamId(record.getId());
				saveBean.setTeamName(record.getTeamName());
				saveBean.setRecordIdcard(_ids.get(i));
				saveBean.setActivityDuration(record.getActivityDuration());
				saveBean.setActivityId(record.getActivityId());
				list.add(saveBean);
			}
			hrmsVoluntariesActivityRecordService.laterSave(list);
		}else {
			throw new BusinessException("团体没有选择成员");
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsVoluntariesActivityRecordTeam record = new HrmsVoluntariesActivityRecordTeam();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		deletePersonById(id);
		return mapper.updateByPrimaryKeySelective(record);
	}
	//根据团体id删除人员
	private Integer deletePersonById(String id){
		return mapper.deletePersonById(id);
	}

	@Override
	public HrmsVoluntariesActivityRecordTeam selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<HrmsVoluntariesActivityRecordTeam> getDataSetList(Page page, HrmsVoluntariesActivityRecordTeam record) {
		Example example = new Example(HrmsVoluntariesActivityRecordTeam.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		if(!StringUtil.isEmpty(record.getActivityId())){
			criteria.andEqualTo("activityId", record.getActivityId());
		}
		List<HrmsVoluntariesActivityRecordTeam> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
}
