package cn.trasen.hrms.service.impl;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.hrms.dao.CommitteeEmployeeMapper;
import cn.trasen.hrms.dao.CommitteeMapper;
import cn.trasen.hrms.model.Committee;
import cn.trasen.hrms.model.CommitteeEmployee;
import cn.trasen.hrms.model.HrmsPapersBooks;
import cn.trasen.hrms.service.CommitteeService;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;

@Service
public class CommitteeServiceImpl implements CommitteeService {

	@Autowired
	private CommitteeMapper committeeMapper;
	
	@Autowired
	CommitteeEmployeeMapper committeeEmployeeMapper;
	

	@Override
	@Transactional(readOnly = false)
	public int insert(Committee entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		int insert = committeeMapper.insertSelective(entity);
		return insert;
	}

	@Override
	@Transactional(readOnly = false)
	public int update(Committee entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return committeeMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	@Transactional(readOnly = false)
	public int deleted(String id) {
		Committee committee = committeeMapper.selectByPrimaryKey(id);
		if (committee != null) {
			committee.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return committeeMapper.updateByPrimaryKeySelective(committee);
	}

	@Override
	public List<Committee> getDataList(Page page, Committee entity) {
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return committeeMapper.getDataList(entity, page);
	}

	@Override
	public List<CommitteeEmployee> getCommitteeDataList(Page page, CommitteeEmployee entity) {
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return committeeMapper.getCommitteeDataList(entity, page);
	}
	
	
	
	@Override
	public List<Committee> getList(Committee entity) {
		Example example = new Example(HrmsPapersBooks.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		return committeeMapper.selectByExample(example);
	}

	@Override
	@Transactional(readOnly = false)
	public int insertCommitteeEmployee(CommitteeEmployee entity) {
		entity.setId(String.valueOf(IdWork.id.nextId()));
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		if (UserInfoHolder.getCurrentUserInfo() != null) {
			entity.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			entity.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
		}
		//判断是否已存在党委书记
		if("1".equals(entity.getPostId())) {
			Example example = new Example(CommitteeEmployee.class);
			Criteria c1 = example.createCriteria();
			c1.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE);
			c1.andEqualTo("postId", "1");
			c1.andEqualTo("committeeId", entity.getCommitteeId());
			Criteria c2 = example.createCriteria();
			
			c2.andIsNull("outputDate");
			c2.orEqualTo("outputDate", "");
			example.and(c2);
			List<CommitteeEmployee> result = committeeEmployeeMapper.selectByExample(example);
			if(result != null && result.size() > 0) {
				Assert.isTrue(false, "不允许有多个党委书记");
			}
		}
		int insert = committeeEmployeeMapper.insertSelective(entity);
		return insert;
	}

	@Override
	@Transactional(readOnly = false)
	public int updateCommitteeEmployee(CommitteeEmployee entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return committeeEmployeeMapper.updateByPrimaryKeySelective(entity);
	}

	@Override
	public int deleteCommitteeEmployee(String id) {
		CommitteeEmployee committeeEmp = committeeEmployeeMapper.selectByPrimaryKey(id);
		if (committeeEmp != null) {
			committeeEmp.setIsDeleted(Contants.IS_DELETED_TURE);
		}
		return committeeEmployeeMapper.updateByPrimaryKeySelective(committeeEmp);
	}

}
