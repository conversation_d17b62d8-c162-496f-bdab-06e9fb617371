package cn.trasen.hrms.service.impl;

import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.trasen.hrms.model.*;
import cn.trasen.hrms.salary.DTO.CheckPersonnel;
import cn.trasen.hrms.salary.DTO.SearchListTable;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicColumnMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;

import cn.trasen.BootComm.utils.PasswordHash;
import cn.trasen.BootComm.utils.PinYinUtil;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.bean.ThpsUserReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.entity.ExcelException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.model.PageDataReq;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.sso.RightFeignService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import cn.trasen.hrms.bean.EmployeeListReq;
import cn.trasen.hrms.common.LifeCycleList;
import cn.trasen.hrms.contants.CommonContants;
import cn.trasen.hrms.contants.DictContants;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.enums.DateFormatEnum;
import cn.trasen.hrms.enums.EmployeeStatusEnum;
import cn.trasen.hrms.enums.GenderTypeEnum;
import cn.trasen.hrms.enums.WhetherEnum;
import cn.trasen.hrms.service.CustEmpBaseService;
import cn.trasen.hrms.service.CustEmpInfoService;
import cn.trasen.hrms.service.HrmsBirthdayManagementService;
import cn.trasen.hrms.service.HrmsDictInfoService;
import cn.trasen.hrms.service.HrmsEducationInfoService;
import cn.trasen.hrms.service.HrmsEmployeeExtendService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.service.HrmsJobtitleInfoService;
import cn.trasen.hrms.service.HrmsOutRecordService;
import cn.trasen.hrms.service.HrmsPapersBooksService;
import cn.trasen.hrms.service.HrmsPersonnelIncidentService;
import cn.trasen.hrms.service.HrmsPersonnelTransactionService;
import cn.trasen.hrms.service.HrmsRewardPenaltyService;
import cn.trasen.hrms.service.HrmsStandardizedTrainingService;
import cn.trasen.hrms.service.PostInformationService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.MapUtil;
import cn.trasen.hrms.utils.TreeItem;
import cn.trasen.hrms.utils.TreeUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @Title: HrmsEmployeeServiceImpl.java
 * @Package cn.trasen.hrms.service.impl
 * @Description: 员工基本信息 业务层接口实现
 * <AUTHOR>
 * @Company: 湖南创星科技股份有限公司
 * @date 2020年3月17日 下午4:56:08
 * @version V1.0
 */
@Slf4j
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsEmployeeServiceImpl implements HrmsEmployeeService {

	//新增员工信息 添加角色
	@Value("${roleCode}")
	String roleCode;

	@Autowired
	private HrmsEmployeeMapper hrmsEmployeeMapper;
	@Autowired
	private HrmsEmployeeExtendService hrmsEmployeeExtendService;
	@Autowired
	private HrmsDictInfoService hrmsDictInfoService;
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationService;
	@Autowired
	private HrmsBirthdayManagementService hrmsBirthdayManagementService;
	
	@Autowired
	private HrmsPersonnelTransactionService hrmsPersonnelTransactionService;

	@Autowired
	private HrmsEducationInfoService hrmsEducationInfoService;

	@Autowired
	HrmsJobtitleInfoService hrmsJobtitleInfoService;

	@Autowired
	HrmsPapersBooksService hrmsPapersBooksService;

	@Autowired
	HrmsRewardPenaltyService hrmsRewardPenaltyService;

	@Autowired
	private HrmsStandardizedTrainingService hrmsStandardizedTrainingService;

	@Autowired
	PostInformationService postInformationService;

	@Autowired
	RightFeignService rightService;

	@Autowired
	HrmsOutRecordService hrmsOutRecordService;

	@Autowired
	HrmsPersonnelIncidentService hrmsPersonnelIncidentService;
	
	@Autowired
	CustEmpBaseService custEmpBaseService;
	
	@Autowired
	CustEmpInfoService custEmpInfoService;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;

	@Resource
	private HrmsNewsalaryBasicColumnMapper basicMapper;

	/**
	 * @throws ExcelException
	 * @Title: insert
	 * @Description: 新增员工信息
	 * @Param: entity
	 * <AUTHOR>
	 * @date 2020年3月25日 下午3:30:31
	 */
	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public PlatformResult<HrmsEmployee> insert(HrmsEmployee entity) throws Exception {

		Assert.hasText(entity.getIdentityNumber(), "身份证不能为空");
		Assert.hasText(entity.getPhoneNumber(), "电话号码不能为空");
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		//验证身份证或者手机号是否占用
		List<HrmsEmployee> bean = hrmsEmployeeMapper.getEmployeeIdentityAndPhone(entity,ssoOrgCode);
		if (bean != null && bean.size() > 0) {
			throw new Exception("身份证或电话号码已存在");
		}

		entity.setEmployeeId(String.valueOf(IdWork.id.nextId()));
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		entity.setIsEnable(CommonContants.IS_ENABLE_TRUE);
		entity.setIsDeleted(Contants.IS_DELETED_FALSE);
		entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
		entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
		entity.setCreateDate(new Date());
		entity.setNameSpell(PinYinUtil.converterToFirstSpell(entity.getEmployeeName()));
		// 生成员工工号
		//浏阳改为使用身份证号
		entity.setEmployeeNo(entity.getIdentityNumber());
		if (StringUtils.isBlank(entity.getOldEmployeeNo())) {
			entity.setOldEmployeeNo(entity.getIdentityNumber());
		}
//		entity.setEmployeeNo(hrmsNumberConfigService.generatorEmployeeNumber());
//		if (StringUtils.isBlank(entity.getOldEmployeeNo())) {
//			entity.setOldEmployeeNo(entity.getEmployeeNo());
//		}
		if (hrmsEmployeeMapper.insert(entity) > 0) {
			// 保存员工扩展信息
			hrmsEmployeeExtendService.saveOrUpdate(entity);

			HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction(entity.getEmployeeNo(),
					entity.getEmployeeName(), entity.getEmployeeId(), null, null, entity.getOrgId(),
					entity.getOrgName(), DateUtils.getStringDateShort(new Date()), "新员工入职",
					"是", hrmsPersonnelTransactionService.getBatchNumber(), null, null, null);
			hrmsPersonnelTransactionService.insert(hpt);  //添加人事事件

			//添加生日记录
			HrmsBirthdayManagement birthday = new HrmsBirthdayManagement();
			birthday.setEmployeeNo(entity.getEmployeeNo());
			birthday.setEmployeeName(entity.getEmployeeName());
			birthday.setGender(entity.getGender());
			birthday.setBirthday(entity.getBirthday());
			birthday.setDateOfBirth(entity.getBirthday() == null ? null : new DateTime(entity.getBirthday()).toString(DateFormatEnum.MM_DD.getValue()));
			birthday.setReceiveStatus(CommonContants.BIRTH_RECEIVE_STATUS_2);
			birthday.setOrgId(entity.getOrgId());
			birthday.setOrgName(entity.getOrgName());
			birthday.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			hrmsBirthdayManagementService.insert(birthday);
			try {
				doSynOtherSystem(entity);//后台数据同步
			} catch (Exception e) {
				log.error("新增用户同步后台用户失败" + e.getMessage(), e);
			}

			return PlatformResult.success(entity);
		}


		return PlatformResult.failure();
	}

	/**
	 * @Title: update
	 * @Description: 修改员工信息
	 * @Param: entity
	 * <AUTHOR>
	 * @date 2020年3月25日 下午4:07:51
	 */
	@Override
	@Transactional(readOnly = false)
	public int update(HrmsEmployee entity) {
		
		CustEmpBase custEmpBase = new CustEmpBase();
		custEmpBase.setEmployeeId(entity.getEmployeeId());
		custEmpBase.setOrgId(entity.getOrgId());
		custEmpBase.setPositionId(entity.getPositionId());
		custEmpBase.setPersonalIdentity(entity.getPersonalIdentity());
		custEmpBase.setEmployeeStatus(entity.getEmployeeStatus());		
		custEmpBase.setIsEnable(entity.getIsEnable());
		custEmpBase.setJobAttributes(entity.getJobAttributes());
		custEmpBase.setOrgAttributes(entity.getOrgAttributes());
		custEmpBase.setPostType(entity.getPostType());
		custEmpBase.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		custEmpBase.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		custEmpBase.setUpdateDate(new Date());
		custEmpBaseService.update(custEmpBase);
		
		
		if(StringUtils.isNotBlank(entity.getIsRetire()) || StringUtils.isNotBlank(entity.getShifouzhongcengganbu()) 
				|| StringUtils.isNotBlank(entity.getEstablishmentType())) {
			CustEmpInfo custEmpInfo = new CustEmpInfo();
			custEmpInfo.setInfoId(entity.getEmployeeId());	
			custEmpInfo.setIsRetire(entity.getIsRetire());
			custEmpInfo.setShifouzhongcengganbu(entity.getShifouzhongcengganbu());
			custEmpInfo.setEstablishmentType(entity.getEstablishmentType());
			custEmpInfoService.update(custEmpInfo);
		}
		
		if ("1".equals(entity.getPostUpdate())) {  //保存岗位信息到岗位历史表
			PostInformation postInformationBean = new PostInformation();
			postInformationBean.setEmployeeId(entity.getEmployeeId());
			postInformationBean.setPostCategory(entity.getPostCategory());
			postInformationBean.setPostId(entity.getPostId());
			postInformationBean.setEmployDutyEquallyDate(DateUtils.getStringDateShort(entity.getEmployDutyEquallyDate()));
			postInformationBean.setEmployDutyDuration(entity.getEmployDutyDuration());
			postInformationService.insert(postInformationBean);
		}
		
		try {
			doSyncUpdateUser(entity);
		} catch (Exception e) {
			e.printStackTrace();
			log.error("用户同步后台用户失败" + e.getMessage(), e);
		}
		return 1;
	}

	private void doSyncUpdateUser(HrmsEmployee entity) {
		
		ThpsUserReq thpsUser = new ThpsUserReq();
		
		//离职 死亡 退休 停用账号
		if("4".equals(entity.getEmployeeStatus()) || "7".equals(entity.getEmployeeStatus()) || "8".equals(entity.getEmployeeStatus())) {  
			thpsUser.setStatus(0);  //停用账号
			ThpsUserReq user = new ThpsUserReq();
        	user.setId(entity.getEmployeeId());
        	user.setStatus(0);
        	systemUserFeignService.newDisable(user);
		}
		
		thpsUser.setId(entity.getEmployeeId());
		thpsUser.setUsercode(entity.getEmployeeNo());
		thpsUser.setOldusercode(entity.getEmployeeNo());
		thpsUser.setUsername(entity.getEmployeeName());
		thpsUser.setMobileNo(entity.getPhoneNumber());
		thpsUser.setSex(entity.getGender());
		thpsUser.setDeptcode(entity.getOrgId());
		thpsUser.setCorpcode(UserInfoHolder.getCurrentUserCorpCode());
		
		log.info("同步后台数据：" + JSON.toJSONString(thpsUser));

		// 权限系统同步
		systemUserFeignService.saveOrUpdate(thpsUser);
	}


	/**
	 * @param entity
	 * @Title: updateByPrimaryKeySelective
	 * @Description: 更新员工信息(空值不更新)
	 * @Return int
	 * <AUTHOR>
	 * @date 2020年6月29日 下午5:21:02
	 */
	@Override
	@Deprecated
	public int updateByPrimaryKeySelective(HrmsEmployee entity) {
		entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		entity.setUpdateDate(new Date());
		return hrmsEmployeeMapper.updateByPrimaryKeySelective(entity);
	}


	/**
	 * @description: 获取员工列表
	 * @param: page
	 * @param: employeeListReq
	 * @return: java.util.List<cn.trasen.hrms.model.HrmsEmployee>
	 * @author: liyuan
	 * @createTime: 2021/5/27 10:42
	 */
	@Override
	@Deprecated
	public DataSet<HrmsEmployee> getEmployeePageList(PageDataReq<EmployeeListReq> pageDataReq) {

		List<HrmsEmployee> list = new ArrayList<>();
		HrmsEmployee entity = new HrmsEmployee();
		if (!StringUtils.isBlank(pageDataReq.getData().getOrgId())) {
			List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(pageDataReq.getData().getOrgId()).getObject();
			entity.setOrgIdList(orgIdNewList);
		}

		entity.setEmployeeNo(pageDataReq.getData().getEmployeeNo());
		if (!StringUtils.isBlank(pageDataReq.getData().getEmployeeName())) {
			entity.setEmployeeNo(pageDataReq.getData().getEmployeeName());
		}

		Page page = new Page();
		page.setPageNo(pageDataReq.getPageNum());
		page.setPageSize(pageDataReq.getPageSize());
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		list = hrmsEmployeeMapper.getPageList(page, entity);
		fullEmployeeData(list);
		DataSet dataSet = new DataSet();
		dataSet.setPageNo(page.getPageNo());
		dataSet.setPageSize(page.getPageSize());
		dataSet.setTotalCount(page.getTotalCount());
		dataSet.setPageCount(page.getTotalPages());
		dataSet.setRows(list);
		return dataSet;
	}

	/**
	 * @param entity
	 * @Title: getDataList
	 * @Description: 查询员工列表(分页)
	 * @Param: page
	 * <AUTHOR>
	 * @date 2020年3月25日 下午5:42:18
	 */
	@Override
	@Deprecated
	public List<HrmsEmployee> getDataList(Page page, HrmsEmployee entity) {

		List<String> orgIdList = new ArrayList<>();

		if (StringUtils.isNotBlank(entity.getOrgIds())) {
			String[] orgIds = entity.getOrgIds().split(",");
			if (orgIds != null) {
				orgIdList = Arrays.stream(orgIds).filter(s -> s != null && s != "").collect(Collectors.toList());
				entity.setOrgIdList(orgIdList);
			}
		}
		List<HrmsEmployee> list = new ArrayList<>();

		//数据权限
		ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
		String orgRang = thpsUser.getOrgRang();
		if (!UserInfoHolder.ISADMIN()) {    // 是否管理员
			if (!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
				entity.setHtOrgIdList(orgRang);
			}
		}
		//根据当前登录账号机构编码过滤查询数据
		entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		list = hrmsEmployeeMapper.getPageList(page, entity);

		fullEmployeeData(list);
		return list;
	}


	/**
	 * @param list
	 * @description: 初始化数据
	 * @return: void
	 * @author: liyuan
	 * @createTime: 2021/5/27 10:30
	 */
	@Deprecated
	private void fullEmployeeData(List<HrmsEmployee> list) {
		if (CollectionUtils.isNotEmpty(list)) {
			Date nowDate = new Date(); // 当前的日期
			Map<String, String> employeeStatusMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_STATUS); // 员工状态数据字典
			Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE); //编制类型
			Map<String, String> employeeCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_CATEGORY);  //编制类别
			Map<String, String> nationalityMap = hrmsDictInfoService.convertDictMap(DictContants.NATIONALITY_NAME);
			Map<String, String> politicalStatusMap = hrmsDictInfoService.convertDictMap(DictContants.POLITICAL_STATUS);
			Map<String, String> bloodGroupMap = hrmsDictInfoService.convertDictMap(DictContants.BLOOD_GROUP);
			Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型字典
			Map<String, String> firstEducationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.FIRST_EDUCATION_TYPE); // 第一学历
			Map<String, String> postCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.POST_CATEGORY); //
			Map<String, String> jobDescriptionTypeMap = hrmsDictInfoService.convertDictMap(DictContants.JOB_DESCRIPTION_TYPE); // 岗位描述
			Map<String, String> personalIdentityMap = hrmsDictInfoService.convertDictMap(DictContants.PERSONAL_IDENTITY); // 岗位类别
			Map<String, String> postTypeMap = hrmsDictInfoService.convertDictMap(DictContants.POST_TYPE); // 岗位类别
			Map<String, String> authorizedOrgMap = hrmsDictInfoService.convertDictMap(DictContants.AUTHORIZED_ORG); // 编制所属机构
			Map<String, String> marriageStatusMap = hrmsDictInfoService.convertDictMap(DictContants.MARRIAGE_STATUS); // 婚姻


			list.stream().forEach(item -> {
				try {  // 年龄
					if (item.getBirthday() != null) {
						int differYears = DateUtils.getDifferYears(item.getBirthday(), nowDate);
						item.setAge(String.valueOf(differYears));
					}
					if (StringUtil.isEmpty(item.getAge())) {
						String age = getAgeByCertId(item.getIdentityNumber());
						item.setAge(age);
					}
				} catch (Exception e) {
					log.error(item.getEmployeeName() + "年龄计算错误" + e.getMessage(), e);
				}
				//工龄
				if (item.getWorkStartDate() != null) {
					try {
						int differYears;
						differYears = DateUtils.getDifferYears(item.getWorkStartDate(), nowDate);
						if (differYears > 0) {
							item.setWorkYears(String.valueOf(differYears));
						}
					} catch (Exception e) {
						log.error(item.getEmployeeName() + "工龄计算错误" + e.getMessage(), e);
					}
				}


				item.setGenderText(GenderTypeEnum.getValByKey(item.getGender())); // 性别文本值
				item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus())); // 员工状态文本值
				item.setEstablishmentTypeText(establishmentTypeMap.get(item.getEstablishmentType())); // 编制类型文本值
				item.setEmployeeCategoryText(employeeCategoryMap.get(item.getEmployeeCategory())); // 员工类别文本值
				item.setNationalityName(nationalityMap.get(item.getNationality())); // 民族
				item.setPoliticalStatusText(politicalStatusMap.get(item.getPoliticalStatus())); // 政治面貌
				item.setBloodGroupText(bloodGroupMap.get(item.getBloodGroup())); // 血型
				item.setEducationType(educationTypeMap.get(item.getEducationType()));//学历
				item.setFirstEducationTypeText(firstEducationTypeMap.get(item.getFirstEducationType()));
				item.setAuthorizedOrg(authorizedOrgMap.get(item.getAuthorizedOrg()));
				item.setMarriageStatus(marriageStatusMap.get(item.getMarriageStatus()));

				item.setPostCategory(postCategoryMap.get(item.getPostCategory()));
				item.setJobDescriptionType(jobDescriptionTypeMap.get(item.getJobDescriptionType()));
				item.setPersonalIdentity(personalIdentityMap.get(item.getPersonalIdentity()));
				item.setPostType(postTypeMap.get(item.getPostType()));

				//是否字段的值
				item.setWorkNature(WhetherEnum.getValByKey(item.getWorkNature()));
				item.setIsLeader(WhetherEnum.getValByKey(item.getIsLeader()));
				item.setComplianceTraining(WhetherEnum.getValByKey(item.getComplianceTraining()));
				item.setDoctorQualificationCertificate(WhetherEnum.getValByKey(item.getDoctorQualificationCertificate()));
				item.setMidwife(WhetherEnum.getValByKey(item.getMidwife()));
				item.setIsVeteran(WhetherEnum.getValByKey(item.getIsVeteran()));


				//三个学历
				item.setEducationTypeFull(educationTypeMap.get(item.getEducationTypeFull()));
				item.setEducationTypeFirst(educationTypeMap.get(item.getEducationTypeFirst()));
				item.setEducationTypeSecond(educationTypeMap.get(item.getEducationTypeSecond()));
			});
		}
	}

	/**
	 * <p> @Title: getAgeByCertId</p>
	 * <p> @Description: 根据身份证获取年龄</p>
	 * <p> @Param: </p>
	 * <p> @Return: String</p>
	 * <P> @Date: 2021年3月8日  上午9:53:02 </p>
	 * <p> <AUTHOR>
	 */
	public static String getAgeByCertId(String certId) {
		String birthday = "";
		if (certId.length() == 18) {
			birthday = certId.substring(6, 10) + "/"
					+ certId.substring(10, 12) + "/"
					+ certId.substring(12, 14);
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
		Date now = new Date();
		Date birth = new Date();
		try {
			birth = sdf.parse(birthday);
		} catch (ParseException e) {
		}
		long intervalMilli = now.getTime() - birth.getTime();
		int age = (int) (intervalMilli / (24 * 60 * 60 * 1000)) / 365;
		if (age > 0) {
			return age + "";
		}
		return "";
	}


	/**
	 * @param entity
	 * @Title: getList
	 * @Description: 查询员工列表(不分页)
	 * @Return List<HrmsEmployee>
	 * <AUTHOR>
	 * @date 2020年5月9日 上午10:37:31
	 */
	@Override
	public List<HrmsEmployee> getList(HrmsEmployee entity) {
		return hrmsEmployeeMapper.getEmployeeList(entity);
	}


	/**
	 * @param employeeId
	 * @Title: findDetailById
	 * @Description: 根据ID查询员工详情信息
	 * @Return HrmsEmployee
	 * <AUTHOR>
	 * @date 2020年4月15日 下午3:07:12
	 */
	@Override
	public HrmsEmployee findDetailById(String employeeId) {
		HrmsEmployee employee = hrmsEmployeeMapper.findDetailById(employeeId);
		if (employee != null) {
			employee.setEmployeeStatusText(EmployeeStatusEnum.getValByKey(employee.getEmployeeStatus())); // 员工状态
		}
		return employee;
	}


	/**
	 * @description:
	 * @param: employeeIds
	 * @return: java.util.List<cn.trasen.hrms.model.HrmsEmployee>
	 * @author: liyuan
	 * @createTime: 2021/6/4 18:04
	 */
	@Override
	public List<HrmsEmployee> findDetailByIds(List<String> employeeIds) {
		List<HrmsEmployee> employeeList = hrmsEmployeeMapper.findDetailByIds(employeeIds);
		for (HrmsEmployee hrmsEmployee : employeeList) {
			fullEmployeeData(hrmsEmployee);
		}
		return employeeList;
	}

	/**
	 * @param employeeNo
	 * @Title: findByEmployeeNo
	 * @Description: 根据员工工号查询员工信息
	 * @Return HrmsEmployee
	 * <AUTHOR>
	 * @date 2020年5月20日 上午12:45:00
	 */
	@Override
	public HrmsEmployee findByEmployeeNo(String employeeNo) {
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		HrmsEmployee hrmsEmployee = hrmsEmployeeMapper.findByEmployeeNo(employeeNo,ssoOrgCode);
		fullEmployeeData(hrmsEmployee);
		return hrmsEmployee;
	}

	/**
	 * 分页获取所有员工信息（包括档案员工和临时员工）
	 * @param page
	 * @param entity
	 * @return
	 */
	@Override
	public List<HrmsEmployee> pagesAllEmployeeByParam(Page page, HrmsEmployee entity){
		List<HrmsEmployee> list = hrmsEmployeeMapper.pagesAllEmployeeByParam(page, entity);
		if(CollUtil.isNotEmpty(list)){
            Map<String, String> employeeCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_CATEGORY);
            Map<String, String> nationalityMap = hrmsDictInfoService.convertDictMap(DictContants.NATIONALITY_NAME);
            Map<String, String> politicalStatusMap = hrmsDictInfoService.convertDictMap(DictContants.POLITICAL_STATUS);
            Map<String, String> marriageStatusMap = hrmsDictInfoService.convertDictMap(DictContants.MARRIAGE_STATUS);
            Map<String, String> healthStatusMap = hrmsDictInfoService.convertDictMap(DictContants.HEALTH_STATUS);
            Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE);
            list.stream().forEach(employee -> {
                employee.setEmployeeCategoryText(employeeCategoryMap.get(employee.getEmployeeCategory()));
                employee.setNationalityName(nationalityMap.get(employee.getNationality()));
                employee.setPoliticalStatusText(politicalStatusMap.get(employee.getPoliticalStatus()));
                employee.setMarriageStatus(marriageStatusMap.get(employee.getMarriageStatus()));
                employee.setHealthStatus(healthStatusMap.get(employee.getHealthStatus()));
                employee.setEstablishmentTypeText(establishmentTypeMap.get(employee.getEstablishmentType()));
                employee.setGenderText(GenderTypeEnum.getValByKey(employee.getGender()));
            });
        }
		return list;
	}

	@Override
	public List<HrmsEmployee> findByEmployeeNos(String employeeNos) {
		Assert.hasText(employeeNos, "employeeNos不能为空.");
		String[] employeeNoArray = employeeNos.split(",");
		List<String> employeeNoList = Arrays.stream(employeeNoArray).filter(s -> s != null && s != "").collect(Collectors.toList());
		return findByEmployeeNos(employeeNoList);
	}

	/**
	 * @description: 根据员工编码批量获取员工信息
	 * @param: employeeNoList
	 * @return: java.util.List<cn.trasen.hrms.model.HrmsEmployee>
	 * @author: liyuan
	 * @createTime: 2021/6/1 15:41
	 */
	@Override
	public List<HrmsEmployee> findByEmployeeNos(List<String> employeeNoList) {
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<HrmsEmployee> hrmsEmployeeList = hrmsEmployeeMapper.findByEmployeeNos(employeeNoList,ssoOrgCode);
		for (HrmsEmployee hrmsEmployee : hrmsEmployeeList) {
			fullEmployeeData(hrmsEmployee);
		}
		return hrmsEmployeeList;
	}

	private void fullEmployeeData(HrmsEmployee hrmsEmployee) {
		if(hrmsEmployee == null){
			return ;
		}
		Map<String, String> employeeCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_CATEGORY);
		Map<String, String> nationalityMap = hrmsDictInfoService.convertDictMap(DictContants.NATIONALITY_NAME);
		Map<String, String> politicalStatusMap = hrmsDictInfoService.convertDictMap(DictContants.POLITICAL_STATUS);
		Map<String, String> marriageStatusMap = hrmsDictInfoService.convertDictMap(DictContants.MARRIAGE_STATUS);
		Map<String, String> healthStatusMap = hrmsDictInfoService.convertDictMap(DictContants.HEALTH_STATUS);
		Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE);

		if(StringUtils.isNotBlank(hrmsEmployee.getEmployeeCategory())  && employeeCategoryMap.containsKey(hrmsEmployee.getEmployeeCategory())) {
			hrmsEmployee.setEmployeeCategoryText(employeeCategoryMap.get(hrmsEmployee.getEmployeeCategory()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getNationality())  && nationalityMap.containsKey(hrmsEmployee.getNationality())) {
			hrmsEmployee.setNationalityName(nationalityMap.get(hrmsEmployee.getNationality()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getPoliticalStatus())  && politicalStatusMap.containsKey(hrmsEmployee.getPoliticalStatus())) {
			hrmsEmployee.setPoliticalStatusText(politicalStatusMap.get(hrmsEmployee.getPoliticalStatus()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getMarriageStatus())  && marriageStatusMap.containsKey(hrmsEmployee.getMarriageStatus())) {
			hrmsEmployee.setMarriageStatus(marriageStatusMap.get(hrmsEmployee.getMarriageStatus()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getHealthStatus())  && healthStatusMap.containsKey(hrmsEmployee.getHealthStatus())) {
			hrmsEmployee.setHealthStatus(healthStatusMap.get(hrmsEmployee.getHealthStatus()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getEstablishmentType())  && establishmentTypeMap.containsKey(hrmsEmployee.getEstablishmentType())) {
			hrmsEmployee.setEstablishmentTypeText(establishmentTypeMap.get(hrmsEmployee.getEstablishmentType()));
		}
		if(StringUtils.isNotBlank(hrmsEmployee.getGender()) ) {
			hrmsEmployee.setGenderText(GenderTypeEnum.getValByKey(hrmsEmployee.getGender()));
		}
	}

	/**
	 * 1. 保存员工信息
	 * 2. 保存员工扩展信息
	 * 3. 保存员工生日信息
	 *
	 * @param list
	 * @Title: excelImportEmployee
	 * @Description: excel导入员工信息
	 * @Return PlatformResult<String>
	 * <AUTHOR>
	 * @date 2020年6月17日 下午4:00:56
	 */
	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public PlatformResult<String> excelImportEmployee(List<HrmsEmployee> list) {
		if (CollectionUtils.isEmpty(list)) {
			return PlatformResult.failure("导入内容为空");
		}

		List<HrmsEmployee> insertEmployeeList = Lists.newArrayList(); // 插入的员工数据
		List<HrmsEmployeeExtend> insertEmployeeExtendList = Lists.newArrayList(); // 插入的员工扩展信息数据
		List<HrmsBirthdayManagement> insertBirthdayList = Lists.newArrayList(); // 插入的生日管理数据

		list.stream().forEach(employee -> {
			Map<String, String> employeeStatusMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.EMPLOYEE_STATUS); // 员工状态
			Map<String, String> establishmentTypeMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.ESTABLISHMENT_TYPE); // 编制类型数据字典
			Map<String, String> nationalityMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.NATIONALITY_NAME); // 民族数据字典
			Map<String, String> politicalStatusMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.POLITICAL_STATUS); // 政治面貌数据字典
			Map<String, String> personalIdentityMaps = hrmsDictInfoService.convertDictValueKeyMap(DictContants.PERSONAL_IDENTITY); // 个人身份数据字典

			// 查询所有的组织机构
			List<HrmsOrganizationResp> allOrgs = hrmsOrganizationService.getOrgAllList().getObject();
			Map<String, String> orgMaps = Maps.newHashMap();
			if (CollectionUtils.isNotEmpty(allOrgs)) {
				for (HrmsOrganizationResp org : allOrgs) {
					orgMaps.put(org.getName(), org.getOrganizationId());
				}
			}

			employee.setEmployeeId(String.valueOf(IdWork.id.nextId()));
			employee.setOrgId(orgMaps.get(employee.getOrgName().trim()));
//			employee.setBirthday(new DateTime(employee.getBirthdayImport()).toDate()); // 出生日期
			//改为从身份证号码获取，以免出错
			String identityNumber = employee.getIdentityNumber();
			if (!StringUtil.isEmpty(identityNumber)) {
				try {
					employee.setBirthday(DateUtils.getStringToDatePlain(identityNumber.substring(6, 14))); // 出生日期
				} catch (Exception e) {
					log.error("出生日期转换失败" + e.getMessage(), e);
					employee.setBirthday(null); // 出生日期
				}
			} else {
				employee.setBirthday(null); // 出生日期
			}
			employee.setEmployeeStatus(employeeStatusMaps.get(employee.getEmployeeStatus())); // 员工状态
			employee.setCheckWorkDepartId(orgMaps.get(employee.getCheckWorkDepart())); // 考勤科室
			employee.setReviewDepartId(orgMaps.get(employee.getReviewDepart())); // 审核科室
			employee.setGender(GenderTypeEnum.getKeyByVal(employee.getGender())); // 性别
			employee.setEstablishmentType(establishmentTypeMaps.get(employee.getEstablishmentType())); // 编制类型
			employee.setNationality(nationalityMaps.get(employee.getNationality())); // 民族
			employee.setPoliticalStatus(politicalStatusMaps.get(employee.getPoliticalStatus())); // 政治面貌
			employee.setPersonalIdentity(personalIdentityMaps.get(employee.getPersonalIdentity())); // 个人身份
			employee.setIsDeleted(Contants.IS_DELETED_FALSE);
			employee.setIsEnable(CommonContants.IS_ENABLE_TRUE);
			employee.setCreateUser(UserInfoHolder.getCurrentUserCode());
			employee.setCreateUserName(UserInfoHolder.getCurrentUserName());
			employee.setCreateDate(new Date());
			insertEmployeeList.add(employee);

			// 员工扩展信息
			HrmsEmployeeExtend extend = returnEmployeeExtend(employee);
			insertEmployeeExtendList.add(extend);

			// 员工生日管理
			HrmsBirthdayManagement birthday = returnHrmsBirthdayManagement(employee);
			insertBirthdayList.add(birthday);
		});

		// 批量新增数据
		if (CollectionUtils.isNotEmpty(insertEmployeeList)) {
			hrmsEmployeeMapper.batchInsert(insertEmployeeList);
			hrmsEmployeeExtendService.batchInsert(insertEmployeeExtendList);
			hrmsBirthdayManagementService.batchInsert(insertBirthdayList);
			for (HrmsEmployee e : insertEmployeeList) {
				doSynOtherSystem(e);
			}
		}
		return PlatformResult.success(insertEmployeeList.size() == 0 ? "0" : String.valueOf(insertEmployeeList.size()));
	}

	/**
	 * @param employee
	 * @Title: returnEmployeeExtend
	 * @Description: 赋值并返回员工扩展信息
	 * @Return HrmsEmployeeExtend
	 * <AUTHOR>
	 * @date 2020年6月17日 下午4:57:02
	 */
	@Deprecated
	private HrmsEmployeeExtend returnEmployeeExtend(HrmsEmployee employee) {
		HrmsEmployeeExtend extend = new HrmsEmployeeExtend();
		extend.setEmployeeExtendId(String.valueOf(IdWork.id.nextId()));
		extend.setEmployeeId(employee.getEmployeeId());
		extend.setOldEmployeeNo(StringUtils.isBlank(employee.getOldEmployeeNo()) ? employee.getEmployeeNo() : employee.getOldEmployeeNo());
		extend.setEntryDate(employee.getEntryDate());
		extend.setRetireDate(employee.getRetireDate());
		extend.setQuitDate(employee.getQuitDate());
		extend.setReemploymentDate(employee.getReemploymentDate());
		extend.setPartyDate(employee.getPartyDate());
		extend.setWorkStartDate(employee.getWorkStartDate());
		extend.setUnitStartDate(employee.getUnitStartDate());
		extend.setPersonalIdentity(employee.getPersonalIdentity());  //个人身份
		extend.setWorkNature(employee.getWorkNature());
		extend.setGoodAt(employee.getGoodAt());
		extend.setCheckWorkDepart(employee.getCheckWorkDepartId());
		extend.setReviewDepart(employee.getReviewDepartId());
		extend.setIsDeleted(Contants.IS_DELETED_FALSE);
		extend.setCreateUser(UserInfoHolder.getCurrentUserCode());
		extend.setCreateUserName(UserInfoHolder.getCurrentUserName());
		extend.setCreateDate(new Date());
		return extend;
	}

	/**
	 * @param employee
	 * @Title: returnHrmsBirthdayManagement
	 * @Description: 赋值并返回生日信息
	 * @Return HrmsBirthdayManagement
	 * <AUTHOR>
	 * @date 2020年6月17日 下午5:08:57
	 */
	@Deprecated
	private HrmsBirthdayManagement returnHrmsBirthdayManagement(HrmsEmployee employee) {
		HrmsBirthdayManagement birthday = new HrmsBirthdayManagement();
		birthday.setBirthdayManagementId(String.valueOf(IdWork.id.nextId()));
		birthday.setEmployeeNo(employee.getEmployeeNo());
		birthday.setEmployeeName(employee.getEmployeeName());
		birthday.setGender(employee.getGender());
		birthday.setBirthday(employee.getBirthday());
		birthday.setDateOfBirth(employee.getBirthday() == null ? null : new DateTime(employee.getBirthday()).toString(DateFormatEnum.MM_DD.getValue()));
		birthday.setOrgId(employee.getOrgId());
		birthday.setOrgName(employee.getOrgName());
		birthday.setCreateUser(UserInfoHolder.getCurrentUserCode());
		birthday.setCreateUserName(UserInfoHolder.getCurrentUserName());
		birthday.setReceiveStatus(CommonContants.BIRTH_RECEIVE_STATUS_2);
		birthday.setIsDeleted(Contants.IS_DELETED_FALSE);
		return birthday;
	}

	/**
	 * @param entity
	 * @Title: doSynOtherSystem
	 * @Description: 同步系统用户
	 * @Return void
	 * <AUTHOR>
	 * @date 2020年6月19日 上午10:28:34
	 */
	@Deprecated
	private void doSynOtherSystem(HrmsEmployee entity) {
		ThpsUserReq thpsUser = new ThpsUserReq();
		thpsUser.setId(entity.getEmployeeId());
		thpsUser.setUsercode(entity.getEmployeeNo());
		thpsUser.setOldusercode(entity.getEmployeeNo());
		thpsUser.setUsername(entity.getEmployeeName());
		thpsUser.setMobileNo(entity.getPhoneNumber());
		thpsUser.setSex(entity.getGender());
		thpsUser.setDeptcode(entity.getOrgId());
		String corpCode = UserInfoHolder.getCurrentUserCorpCode();
		if (StringUtils.isNoneBlank(corpCode)) {
			thpsUser.setCorpcode(corpCode);
		} else {
			thpsUser.setCorpcode("ZZSFYBJY");
		}

		String idNum = entity.getIdentityNumber();
		//处理密码
		try {
			if (StringUtils.isNotBlank(idNum)) {
				thpsUser.setPassword(PasswordHash.createHash(idNum.substring(idNum.length() - 6)));
			} else {
				thpsUser.setPassword(PasswordHash.createHash("123456"));
			}
		} catch (NoSuchAlgorithmException | InvalidKeySpecException e) {

		}
		// 权限系统同步
		systemUserFeignService.saveOrUpdate(thpsUser);
		//新增员工信息 添加角色
		rightService.roleUserSave(entity.getEmployeeId(), roleCode, "", "");
	}

	//批量修改人员科室
	@Transactional(readOnly = false)
	@Override
	@Deprecated
	public int updateBatchDept(List<HrmsEmployee> entity) {

		List<String> employeeNos = new ArrayList<>();
		for (HrmsEmployee employee : entity) {
			employeeNos.add(employee.getEmployeeNo());
		}

		//获取所有机构
		List<HrmsOrganizationResp> orgAllList = hrmsOrganizationService.getOrgAllList().getObject();
		Map<String, String> orgMap = orgAllList.stream().collect(Collectors.toMap(HrmsOrganizationResp::getOrganizationId, HrmsOrganizationResp::getName));
		Map<String, String> empMap = entity.stream().collect(Collectors.toMap(HrmsEmployee::getEmployeeId, HrmsEmployee::getOrgId));
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<HrmsEmployee> oldEmp = hrmsEmployeeMapper.findByEmployeeNos(employeeNos,ssoOrgCode);

		int k = 0;
		for (int i = 0; i < entity.size(); i++) {
			k += hrmsEmployeeMapper.updateByPrimaryKeySelective(entity.get(i));
		}

		List<HrmsPersonnelTransaction> list = new ArrayList<>();

		if (oldEmp != null && oldEmp.size() > 0) {
			for (HrmsEmployee bean : oldEmp) {
				HrmsPersonnelTransaction hpt = new HrmsPersonnelTransaction();
				BeanUtils.copyProperties(bean, hpt);
				hpt.setCause("科室拆分移动人员");
				hpt.setExecute("是");
				hpt.setBatchNumber(hrmsPersonnelTransactionService.getBatchNumber());
				hpt.setEffectiveDate(DateUtils.getStringDateShort(new Date()));
				if (bean.getOrgId() != null) {
					hpt.setOldOrgId(bean.getOrgId());
					hpt.setOldOrgName(orgMap.get(bean.getOrgId()));
				}
				hpt.setNewOrgId(empMap.get(bean.getEmployeeId()));
				hpt.setNewOrgName(orgMap.get(empMap.get(bean.getEmployeeId())));
				hpt.setCreateDate(new Date());
				hpt.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				list.add(hpt);
			}
		}

		hrmsPersonnelTransactionService.batchInsert(list); //批量添加人员异动记录


		//批量修改后台用户所属机构
		for (HrmsEmployee bean : entity) {
			ThpsUserReq thpsUser = new ThpsUserReq();
			thpsUser.setId(bean.getEmployeeId());
			thpsUser.setUsercode(bean.getEmployeeNo());
			thpsUser.setOldusercode(bean.getEmployeeNo());
			thpsUser.setUsername(bean.getEmployeeName());
			thpsUser.setDeptcode(bean.getOrgId());
			String corpCode = UserInfoHolder.getCurrentUserCorpCode();
			if (StringUtils.isNoneBlank(corpCode)) {
				thpsUser.setCorpcode(corpCode);
			} else {
				thpsUser.setCorpcode("ZZSFYBJY");
			}
			// 权限系统同步
			systemUserFeignService.saveOrUpdate(thpsUser);
		}
		return k;
	}

	@Override
	@Deprecated
	public String getTreeOrgAndEmp() {
		Example example = new Example(HrmsEmployee.class);
		example.createCriteria().andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.and().andEqualTo("isEnable", CommonContants.IS_ENABLE_TRUE);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		//加上条件 离职退休  ，
		example.and().andNotEqualTo("employeeStatus", '3');
		example.and().andNotEqualTo("employeeStatus", '4');
		example.and().andNotEqualTo("employeeStatus", '7');
		example.and().andNotEqualTo("employeeStatus", '8');
		example.and().andIsNotNull("orgId");
		example.and().andNotEqualTo("orgId", "");
		List<TreeItem> treeItemList = new ArrayList<>();

		List<HrmsEmployee> empAllList = hrmsEmployeeMapper.selectByExample(example);  //获取所有人员
		List<HrmsOrganizationResp> orgAllList = hrmsOrganizationService.getOrgAllList().getObject();  //获取机构


		orgAllList.forEach(item -> {  //机构信息
			TreeItem treeItem = new TreeItem(item.getOrganizationId(), item.getParentId(), item.getName());
			treeItem.setNocheck(true);
			treeItem.setOpen(true);
			treeItemList.add(treeItem);
		});

		empAllList.forEach(item -> {  //人员信息
			treeItemList.add(new TreeItem(item.getEmployeeId(), item.getOrgId(), item.getEmployeeName()));
		});


		TreeItem treeItem = TreeUtil.getInstance().enquireTree(treeItemList);
		return JSONObject.toJSONString(treeItem);
	}

	/**
	 * <p>Title: exportZytj</p>
	 * <p>Description: 导出人力资源分布</p>
	 *
	 * @return
	 */
	@Override
	@Deprecated
	public Map<String, Object> exportZytj(String orgIds) {

		//1.医疗人员 2医技人员 3：药剂人员 4护理人员 5管理人员 6后勤人员7财务人员 8信息人员 9 其他人员
		Map<String, Object> resultMap = new HashMap<String, Object>();


		List<String> orgIdNewList = hrmsOrganizationService.getHrmsOrganizationAndNextList(orgIds).getObject();

		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		List<MapUtil> zytjNumber = hrmsEmployeeMapper.zytjNumber(orgIdNewList,ssoOrgCode);//人数
		setCount(resultMap, zytjNumber, "zytjNumber");
		List<MapUtil> zytjMale = hrmsEmployeeMapper.male(orgIdNewList,ssoOrgCode);//男
		setCount(resultMap, zytjMale, "zytjMale");
		List<MapUtil> zytjWoman = hrmsEmployeeMapper.woman(orgIdNewList,ssoOrgCode);//女
		setCount(resultMap, zytjWoman, "zytjWoman");
		List<MapUtil> zytjTwenty20 = hrmsEmployeeMapper.twenty("0", "19", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty20, "zytjTwenty20");
		List<MapUtil> zytjTwenty2130 = hrmsEmployeeMapper.twenty("20", "29", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty2130, "zytjTwenty2130");
		List<MapUtil> zytjTwenty3140 = hrmsEmployeeMapper.twenty("30", "39", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty3140, "zytjTwenty3140");
		List<MapUtil> zytjTwenty4150 = hrmsEmployeeMapper.twenty("40", "49", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty4150, "zytjTwenty4150");
		List<MapUtil> zytjTwenty5160 = hrmsEmployeeMapper.twenty("50", "59", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty5160, "zytjTwenty5160");
		List<MapUtil> zytjTwenty61 = hrmsEmployeeMapper.twenty("60", "120", orgIdNewList,ssoOrgCode);//查询年龄段分布
		setCount(resultMap, zytjTwenty61, "zytjTwenty61");
		//博士
		List<MapUtil> zytjDoctor = hrmsEmployeeMapper.education("t2.education_type = 7", orgIdNewList,ssoOrgCode);
		setCount(resultMap, zytjDoctor, "zytjDoctor");
		//硕士研究生
		List<MapUtil> zytjMaster = hrmsEmployeeMapper.education("t2.education_type = 8", orgIdNewList,ssoOrgCode);
		setCount(resultMap, zytjMaster, "zytjMaster");

		List<MapUtil> zytjBk = hrmsEmployeeMapper.education("t2.education_type=2", orgIdNewList,ssoOrgCode);  //本科
		setCount(resultMap, zytjBk, "zytjBk");

//		List<MapUtil> zytjZk = hrmsEmployeeMapper.education("t2.education_type=3", orgIdNewList);  //专科
//		setCount(resultMap, zytjZk, "zytjZk");

		List<MapUtil> zytjGz = hrmsEmployeeMapper.education("(t2.education_type!=2  and t2.education_type!=7 and t2.education_type!=8 and t2.education_type_name IS not NULL)", orgIdNewList,ssoOrgCode);  //高中及以下
		setCount(resultMap, zytjGz, "zytjGz");

		List<MapUtil> zytjWxl = hrmsEmployeeMapper.education(" t2.education_type_name IS NULL", orgIdNewList,ssoOrgCode);   //无学历
		setCount(resultMap, zytjWxl, "zytjWxl");

		List<MapUtil> zytjZGj = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName = '正高级' AND t2.jobtitleBasicPid !='254491496962453504'  ", orgIdNewList,ssoOrgCode);  //正高级
		setCount(resultMap, zytjZGj, "zytjZGj");

		List<MapUtil> zytjFGj = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName = '副高级' AND t2.jobtitleBasicPid !='254491496962453504'  ", orgIdNewList,ssoOrgCode);  //职称
		setCount(resultMap, zytjFGj, "zytjFGj");

		List<MapUtil> zytjZj = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName = '中级'  AND t2.jobtitleBasicPid !='254491496962453504' ", orgIdNewList,ssoOrgCode);  //职称
		setCount(resultMap, zytjZj, "zytjZj");

		List<MapUtil> zytjCj = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName = '初级' AND t2.jobtitleBasicPid !='254491496962453504' ", orgIdNewList,ssoOrgCode);  //职称
		setCount(resultMap, zytjCj, "zytjCj");

		List<MapUtil> zytjQt = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName != '初级' and t2.jobtitleLevelName != '中级'  and t2.jobtitleLevelName != '副高级' and t2.jobtitleLevelName != '正高级' ", orgIdNewList,ssoOrgCode);  //职称
		setCount(resultMap, zytjQt, "zytjQt");

		List<MapUtil> zytjWzc = hrmsEmployeeMapper.technical(" t2.jobtitleLevelName IS NULL ", orgIdNewList,ssoOrgCode);  //无职称
		setCount(resultMap, zytjWzc, "zytjWzc");

//		List<MapUtil> zytjQt = hrmsEmployeeMapper.noTechnical("其他");  //职称
//		setCount(resultMap,zytjQt,"zytjQt");

//		otherStaffs(resultMap);    //其他人员通过计算得出


		return resultMap;
	}
	
	@Deprecated
	private Map<String, Object> otherStaffs(Map<String, Object> resultMap) {
		List<MapUtil> zytjQt = new ArrayList<MapUtil>();
		Map<String, Integer> zytjNumber = (Map) resultMap.get("zytjNumber"); //总人数
		Map<String, Integer> zytjZGj = (Map) resultMap.get("zytjZGj"); //正高级
		Map<String, Integer> zytjFGj = (Map) resultMap.get("zytjFGj"); //副高级
		Map<String, Integer> zytjZj = (Map) resultMap.get("zytjZj"); //中级
		Map<String, Integer> zytjCj = (Map) resultMap.get("zytjCj"); //初级

		for (int i = 1; i < 11; i++) {
			if (zytjNumber.get(String.valueOf(i)) != null) {
				Integer v1 = zytjZGj.get(String.valueOf(i)) == null ? 0 : zytjZGj.get(String.valueOf(i));
				Integer v2 = zytjFGj.get(String.valueOf(i)) == null ? 0 : zytjFGj.get(String.valueOf(i));
				Integer v3 = zytjZj.get(String.valueOf(i)) == null ? 0 : zytjZj.get(String.valueOf(i));
				Integer v4 = zytjCj.get(String.valueOf(i)) == null ? 0 : zytjCj.get(String.valueOf(i));
				Integer _vInteger = zytjNumber.get(String.valueOf(i)) - v1 - v2 - v3 - v4;
				MapUtil obj = new MapUtil();
				obj.setK(String.valueOf(i));
				obj.setV(_vInteger);
				zytjQt.add(obj);
			}

		}
		setCount(resultMap, zytjQt, "zytjQt");
		return resultMap;
	}

	//数据封装
	@Deprecated
	private Map<String, Object> setCount(Map<String, Object> resultMap, List<MapUtil> list, String mapName) {
		Map<String, Integer> map = list.stream().collect(Collectors.toMap(MapUtil::getK, MapUtil::getV));
		Integer sum = list.stream().mapToInt(MapUtil::getV).sum();
		map.put("sum", sum);
		resultMap.put(mapName, map);
		return resultMap;
	}

	/**
	 * <p>Title: getRosterDataList</p>
	 * <p>Description:员工花名册列表 </p>
	 *
	 * @param page
	 * @param entity
	 * @return
	 */
	@Override
	public List<HrmsEmployee> getRosterDataList(Page page, HrmsEmployee entity) {
		List<String> orgIdList = new ArrayList<>();

        if (StringUtils.isNotBlank(entity.getOrgIds())) {
            String[] orgIds = entity.getOrgIds().split(",");
			orgIdList = Arrays.stream(orgIds).filter(s -> s != null && !s.equals("")).collect(Collectors.toList());
			entity.setOrgIdList(orgIdList);
		}
        List<HrmsEmployee> list = new ArrayList<>();

        //数据权限
        ThpsUser thpsUser = UserInfoHolder.getCurrentUserInfo();
        String orgRang = thpsUser.getOrgRang();
        if (!UserInfoHolder.ISADMIN()) {    // 是否管理员
            if (!StringUtil.isEmpty(orgRang)) {//查询组织范围数据
                entity.setHtOrgIdList(orgRang);
            }
        }

        //切换状态类型就用啥值,过滤其他和全部状态
        if (!entity.getQueryStatus().equals(DictContants.ALL_STATUS)){
            entity.setEmployeeStatus(entity.getQueryStatus());
        }
        String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
        entity.setSsoOrgCode(ssoOrgCode);
		//宁乡妇幼 全部只需要显示在职、返聘、退休人员
		if(StringUtils.isNotBlank(ssoOrgCode) && "nxsfybjy".equals(ssoOrgCode) && entity.getQueryStatus().equals(DictContants.ALL_STATUS)) {
			entity.setMultiEmployeeStatus("'1','6','8'");
		}
        list = hrmsEmployeeMapper.getRosterDataList(page, entity);

        if (CollectionUtils.isNotEmpty(list)) {
            Date nowDate = new Date(); // 当前的日期
            Map<String, String> employeeStatusMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_STATUS); // 员工状态数据字典
            Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE); //编制类型
            Map<String, String> employeeCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_CATEGORY);  //编制类别
            Map<String, String> nationalityMap = hrmsDictInfoService.convertDictMap(DictContants.NATIONALITY_NAME);
            Map<String, String> politicalStatusMap = hrmsDictInfoService.convertDictMap(DictContants.POLITICAL_STATUS);
            Map<String, String> bloodGroupMap = hrmsDictInfoService.convertDictMap(DictContants.BLOOD_GROUP);
            Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型字典
            Map<String, String> firstEducationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.FIRST_EDUCATION_TYPE); // 第一学历
            Map<String, String> postCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.POST_CATEGORY); //
            //Map<String, String> jobDescriptionTypeMap = hrmsDictInfoService.convertDictMap(DictContants.JOB_DESCRIPTION_TYPE); // 岗位描述
            Map<String, String> personalIdentityMap = hrmsDictInfoService.convertDictMap(DictContants.PERSONAL_IDENTITY); // 岗位名称
            Map<String, String> postTypeMap = hrmsDictInfoService.convertDictMap(DictContants.POST_TYPE); // 岗位类别
            Map<String, String> authorizedOrgMap = hrmsDictInfoService.convertDictMap(DictContants.AUTHORIZED_ORG); // 编制所属机构
            Map<String, String> marriageStatusMap = hrmsDictInfoService.convertDictMap(DictContants.MARRIAGE_STATUS); // 婚姻
            Map<String, String> operationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.OPERATION_TYPE); // 执业类别
			List<Map<String,String>> allCityDataList = hrmsEmployeeMapper.getAllCityData(); //获取所有地区数据;

            list.stream().forEach(item -> {
                try {  // 年龄
                    if (item.getBirthday() != null) {
                        int differYears = DateUtils.getDifferYears(item.getBirthday(), nowDate);
                        item.setAge(String.valueOf(differYears));
                    }
                    if (StringUtil.isEmpty(item.getAge()) && StringUtils.isNotBlank(item.getIdentityNumber())) {
                        String age = getAgeByCertId(item.getIdentityNumber());
                        item.setAge(age);
                    }
                } catch (Exception e) {
                    log.error(item.getEmployeeName() + "年龄计算错误" + e.getMessage(), e);
                }
                //工龄
                if (item.getWorkStartDate() != null) {
                    try {
                        int differYears = DateUtils.getDifferYears(item.getWorkStartDate(), nowDate);
                        if (differYears > 0) {
                            item.setWorkYears(String.valueOf(differYears));
                        }
                    } catch (Exception e) {
                        log.error(item.getEmployeeName() + "工龄计算错误" + e.getMessage(), e);
                    }
                }
                //退休时间格式转换，只保留年月日
                if(StringUtils.isNotBlank(item.getRetireDateExport())){
                	try {
						item.setRetireDateExport(DateUtils.getStringDateShort(DateUtils.getStringToDate(item.getRetireDateExport())));
					}catch (Exception e){
                		e.printStackTrace();
					}
				}
                item.setGenderText(GenderTypeEnum.getValByKey(item.getGender())); // 性别文本值
                item.setEmployeeStatusText(employeeStatusMap.get(item.getEmployeeStatus())); // 员工状态文本值
                item.setEstablishmentTypeText(establishmentTypeMap.get(item.getEstablishmentType())); // 编制类型文本值
                item.setEmployeeCategoryText(employeeCategoryMap.get(item.getEmployeeCategory())); // 员工类别文本值
                item.setNationalityName(nationalityMap.get(item.getNationality())); // 民族
                item.setPoliticalStatusText(politicalStatusMap.get(item.getPoliticalStatus())); // 政治面貌
                item.setBloodGroupText(bloodGroupMap.get(item.getBloodGroup())); // 血型
                item.setEducationType(educationTypeMap.get(item.getEducationType()));//学历
                item.setFirstEducationTypeText(firstEducationTypeMap.get(item.getFirstEducationType()));
                item.setAuthorizedOrg(authorizedOrgMap.get(item.getAuthorizedOrg()));
                item.setMarriageStatus(marriageStatusMap.get(item.getMarriageStatus()));

                item.setPostCategory(postCategoryMap.get(item.getPostCategory()));
                //item.setJobDescriptionType(jobDescriptionTypeMap.get(item.getJobDescriptionType()));
                item.setPersonalIdentity(personalIdentityMap.get(item.getPersonalIdentity()));
                item.setPostType(postTypeMap.get(item.getPostType()));
                item.setOperationType(operationTypeMap.get(item.getOperationType()));            //执业类别
                //是否字段的值
                item.setWorkNature(WhetherEnum.getValByKey(item.getWorkNature()));
                item.setIsLeader(WhetherEnum.getValByKey(item.getIsLeader()));
                item.setComplianceTraining(WhetherEnum.getValByKey(item.getComplianceTraining()));
                item.setDoctorQualificationCertificate(WhetherEnum.getValByKey(item.getDoctorQualificationCertificate()));
                item.setMidwife(WhetherEnum.getValByKey(item.getMidwife()));
                item.setIsVeteran(WhetherEnum.getValByKey(item.getIsVeteran()));


				//三个学历
				item.setEducationTypeFull(educationTypeMap.get(item.getEducationTypeFull()));
				item.setEducationTypeFirst(educationTypeMap.get(item.getEducationTypeFirst()));
				item.setEducationTypeSecond(educationTypeMap.get(item.getEducationTypeSecond()));

				//设置籍贯
				if(StringUtils.isNotBlank(item.getBirthplace())){
					String[] codes = item.getBirthplace().split("-");
					StringBuffer  sb = new StringBuffer();
					 for(String code : codes){
					     List<Map<String,String>> cityResult  = allCityDataList.stream().filter(city-> Convert.toStr(city.get("id")).equals(code.trim())).collect(Collectors.toList());
						 if(CollUtil.isNotEmpty(cityResult)) {
                             sb.append(Convert.toStr(cityResult.get(0).get("name"))).append('-');
                         }
					 }
					 if(sb.length()>0) {
						 item.setBirthplace(sb.substring(0, sb.length() - 1));
					 }
				}
			});
		}
		return list;
	}

	/**
	 * <p>Title: getLifeCycle</p>
	 * <p>Description:查询个人生命周期 </p>
	 *
	 * @return
	 */
	@Override
	public List<LifeCycleList> getLifeCycle(String employeeId) {

		List<LifeCycleList> lifeCycleList = new ArrayList<>();

		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		//入院
		HrmsEmployee empHosp = hrmsEmployeeMapper.getEntranceHospital(employeeId,ssoOrgCode);
		String empHospstringDateShort = DateUtils.getStringDateShort(empHosp.getEntryDate());
		lifeCycleList.add(new LifeCycleList(empHosp, empHospstringDateShort, "8", "入院"));


		//学历   type 1
		HrmsEducationInfo educationInfo = new HrmsEducationInfo();
		educationInfo.setEmployeeId(employeeId);
		List<HrmsEducationInfo> listEducationInfo = hrmsEducationInfoService.getList(educationInfo);
		if (listEducationInfo != null && listEducationInfo.size() > 0) {
			Map<String, HrmsEducationInfo> educationInfoMap = listEducationInfo.stream().collect(Collectors.toMap(HrmsEducationInfo::getId, o -> o, (o, o1) -> o));
			for (HrmsEducationInfo bean : educationInfoMap.values()) {
				if (bean.getEndTime() != null) {
					String stringDateShort = DateUtils.getStringDateShort(bean.getEndTime());
					lifeCycleList.add(new LifeCycleList(bean, stringDateShort, "1", "学历"));
				}
			}
		}
		//职称
		HrmsJobtitleInfo jobtitleInfoBean = new HrmsJobtitleInfo();
		jobtitleInfoBean.setEmployeeId(employeeId);
		jobtitleInfoBean.setIsDeleted("N");
		List<HrmsJobtitleInfo> jobtitleInfoList = hrmsJobtitleInfoService.getList(jobtitleInfoBean);
		if (jobtitleInfoList != null && jobtitleInfoList.size() > 0) {
			Map<String, HrmsJobtitleInfo> jobtitleInfoMap = jobtitleInfoList.stream().collect(Collectors.toMap(HrmsJobtitleInfo::getId, o -> o, (o, o1) -> o));
			for (HrmsJobtitleInfo bean : jobtitleInfoMap.values()) {
				if (bean.getAcquisitionDate() != null) {
					String stringDateShort = DateUtils.getStringDateShort(bean.getAcquisitionDate());
					lifeCycleList.add(new LifeCycleList(bean, stringDateShort, "2", "职称"));
				}
			}
		}
		// 科研论文
		HrmsPapersBooks papersBooksBean = new HrmsPapersBooks();
		papersBooksBean.setEmployeeId(employeeId);
		List<HrmsPapersBooks> papersBooksList = hrmsPapersBooksService.getList(papersBooksBean);
		if (papersBooksList != null && papersBooksList.size() > 0) {
			Map<String, HrmsPapersBooks> papersBooksMap = papersBooksList.stream().collect(Collectors.toMap(HrmsPapersBooks::getId, o -> o, (o, o1) -> o));
			for (HrmsPapersBooks bean : papersBooksMap.values()) {
				if (bean.getPublishDate() != null) {
//					String stringDateShort = DateUtils.getStringDateShort(bean.getPublishDate());
					String stringDateShort = bean.getPublishDate();
					lifeCycleList.add(new LifeCycleList(bean, stringDateShort, "3", "科研论文"));
				}
			}
		}

		//奖惩记录
		HrmsRewardPenalty rewardPenaltyBean = new HrmsRewardPenalty();
		rewardPenaltyBean.setEmployeeId(employeeId);
		List<HrmsRewardPenalty> rewardPenaltyList = hrmsRewardPenaltyService.getList(rewardPenaltyBean);
		if (papersBooksList != null && papersBooksList.size() > 0) {
			Map<String, HrmsRewardPenalty> rewardPenaltyMap = rewardPenaltyList.stream().collect(Collectors.toMap(HrmsRewardPenalty::getId, o -> o, (o, o1) -> o));
			for (HrmsRewardPenalty bean : rewardPenaltyMap.values()) {
				if (bean.getRewardPenaltyDate() != null) {
					if(bean.getRewardPenaltyDate().length() < 8) {
						lifeCycleList.add(new LifeCycleList(bean, bean.getRewardPenaltyDate() + "-01", "4", "奖惩记录"));
					}else {
						lifeCycleList.add(new LifeCycleList(bean, bean.getRewardPenaltyDate(), "4", "奖惩记录"));
					}

				}
			}
		}
		//  培训
		HrmsStandardizedTraining standardizedTrainingBean = new HrmsStandardizedTraining();
		standardizedTrainingBean.setEmployeeId(employeeId);
		List<HrmsStandardizedTraining> standardizedTrainingList = hrmsStandardizedTrainingService.getList(standardizedTrainingBean);
		if (standardizedTrainingList != null && standardizedTrainingList.size() > 0) {
			Map<String, HrmsStandardizedTraining> rewardPenaltyMap = standardizedTrainingList.stream().collect(Collectors.toMap(HrmsStandardizedTraining::getId, o -> o, (o, o1) -> o));
			for (HrmsStandardizedTraining bean : rewardPenaltyMap.values()) {
				if (bean.getAcquisitionDate() != null) {
					if(bean.getAcquisitionDate().length() < 8) {
						lifeCycleList.add(new LifeCycleList(bean,bean.getAcquisitionDate() + "-01" , "5", "培训"));
					}else {
						lifeCycleList.add(new LifeCycleList(bean,bean.getAcquisitionDate() , "5", "培训"));
					}

				}
			}
		}
		//岗位/ 延聘/  返聘/ 调动
		// 退休/ 离职/ 死亡
		HrmsPersonnelTransaction personnelTransaction = new HrmsPersonnelTransaction();
		personnelTransaction.setEmployeeId(employeeId);
		List<HrmsPersonnelTransaction> personnelTransactionList = hrmsPersonnelTransactionService.getCycleList(personnelTransaction);
		if (personnelTransactionList != null && personnelTransactionList.size() > 0) {
			Map<Long, HrmsPersonnelTransaction> personnelTransactionMap = personnelTransactionList.stream().collect(Collectors.toMap(HrmsPersonnelTransaction::getId, o -> o, (o, o1) -> o));
			for (HrmsPersonnelTransaction bean : personnelTransactionMap.values()) {
				if (bean.getEffectiveDate() != null) {
					String stringDateShort = bean.getEffectiveDate();
					lifeCycleList.add(new LifeCycleList(bean, stringDateShort, "6", bean.getCause()));
				}
			}
		}

		//进修 规培
	/*	HrmsOutRecord entity = new HrmsOutRecord();
		Page page = new Page();
		page.setPageSize(Integer.MAX_VALUE);
		entity.setEmployeeId(employeeId);
		entity.getOutType("进修");
		List<HrmsOutRecord> dataSetList = hrmsOutRecordService.getDataSetList(page, entity);
		if(dataSetList != null && dataSetList.size() >0) {
			
		}*/


		//岗位信息

		List<PostInformation> postInformationList = postInformationService.getList(employeeId);
		if (postInformationList != null && postInformationList.size() > 0) {
			Map<String, PostInformation> postInformationMap = postInformationList.stream().collect(Collectors.toMap(PostInformation::getId, o -> o, (o, o1) -> o));
			for (PostInformation bean : postInformationMap.values()) {
				if (bean.getEmployDutyEquallyDate() != null) {
					String stringDateShort = bean.getEmployDutyEquallyDate();
					lifeCycleList.add(new LifeCycleList(bean, stringDateShort, "7", "岗位信息"));
				}
			}
		}


		//排序
		ComparatorLifeCycle comparator = new ComparatorLifeCycle();
		Collections.sort(lifeCycleList, comparator);


		return lifeCycleList;
	}


	/**
	 * <P> @Description: 排序类</p>
	 * <P> @Date: 2021年4月20日  下午3:47:21 </p>
	 * <P> @Author: wangzhihua </p>
	 * <P> @Company: 湖南创星 </p>
	 * <P> @version V1.0    </p>
	 */

	public class ComparatorLifeCycle implements Comparator {
		final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

		@Override
		public int compare(Object arg0, Object arg1) {
			LifeCycleList bean1 = (LifeCycleList) arg0;
			LifeCycleList bean2 = (LifeCycleList) arg1;
			int mark = 1;
			try {
				Date date0 = sdf.parse(bean1.getSort().replaceAll("/", "-"));
				Date date1 = sdf.parse(bean2.getSort().replaceAll("/", "-"));
				if (date0.getTime() < date1.getTime()) {
					mark = -1;
				}
				if (bean1.getSort().equals(bean2.getSort())) {
					mark = 0;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			return mark;
		}

	}


	/**
	 * <p>Title: getPeriodEmployeeDetail</p>
	 * <p>Description: 生命周期查询人员信息</p>
	 *
	 * @param employeeId
	 * @return
	 */
	@Override
	public HrmsEmployee getPeriodEmployeeDetail(String employeeId) {
		HrmsEmployee employee = hrmsEmployeeMapper.getPeriodEmployeeDetail(employeeId, UserInfoHolder.getCurrentUserCorpCode());
		Map<String, String> postCategoryMap = hrmsDictInfoService.convertDictMap(DictContants.POST_CATEGORY); //岗位类型
		Map<String, String> educationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.EDUCATION_TYPE); // 学历类型字典
		Map<String, String> operationTypeMap = hrmsDictInfoService.convertDictMap(DictContants.OPERATION_TYPE); // 执业范围


		if (employee != null) {
			employee.setEmployeeStatusText(EmployeeStatusEnum.getValByKey(employee.getEmployeeStatus())); // 员工状态
			employee.setGenderText(GenderTypeEnum.getValByKey(employee.getGender()));  //性别
			employee.setPostCategory(postCategoryMap.get(employee.getPostCategory()));
			employee.setEducationType(educationTypeMap.get(employee.getEducationType()));
			employee.setOperationType(operationTypeMap.get(employee.getOperationType()));
		}
		return employee;
	}

	/**
	 * @Title: findEmployeeByCode
	 * @Description: 根据员工工号查询员工信息
	 * @Params: @param code
	 * @Params: @return
	 * @Return: HrmsEmployee
	 * <AUTHOR>
	 * @date:2021年9月14日
	 * @Throws
	 */
	@Override
	public HrmsEmployee findEmployeeByCode(String code) {
		
		return hrmsEmployeeMapper.findByEmployeeNo(code,null);

	}

	/**
	 * @Title: findEmployeeByCode
	 * @Description: 根据员工工号查询员工信息
	 * @Params: @param code
	 * @Params: @return
	 * @Return: HrmsEmployee
	 * <AUTHOR>
	 * @date:2021年9月14日
	 * @Throws
	 */
	@Override
	public boolean isExistEmployeeByCode(String code) {
		boolean isExistFlog = true;
	   Integer count =	hrmsEmployeeMapper.isExistEmployeeByCode(code);
	   if(count == null || count < 1){
		   isExistFlog = false;
	   }
		return isExistFlog;
	}


/**
* 获取员工信息
* @param name
* @return cn.trasen.hrms.model.HrmsEmployee
* <AUTHOR>
* @date 2021/10/29 16:46
*/
	/**
	* 获取员工信息
	* @param name
	* @return cn.trasen.hrms.model.HrmsEmployee
	* <AUTHOR>
	* @date 2021/10/29 16:46
	*/
	@Deprecated
	public HrmsEmployee findEmployeeByName(String name) {
		Example example = new Example(HrmsEmployee.class);
		example.and().andEqualTo("employeeName", name);
		example.and().andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		example.setOrderByClause(" employee_id desc LIMIT 1 ");

		return hrmsEmployeeMapper.selectOneByExample(example);
	}

	@Override
	public HrmsEducationInfo getEducationByEmpId(String employeeId) {
		return hrmsEmployeeMapper.getEducationByEmpId(employeeId, UserInfoHolder.getCurrentUserCorpCode());
	}

	//修改员工绩效系数
	@Override
	@Transactional(readOnly = false)
	public int updateCoefficient(Map<String, String> entity) {
		return hrmsEmployeeMapper.updateCoefficient(entity);
	}

	@Override

	public List<EmployeeResp> getEmployeeDetailByIdentityNumber(List<String> identityNumberList) {
		// TODO Auto-generated method stub
		return hrmsEmployeeMapper.getEmployeeDetailByIdentityNumber(identityNumberList);
	}

	@Override
	public List<EmployeeResp> getRy7day() {
		return hrmsEmployeeMapper.getRy7day();
	}

	@Override
	public List<EmployeeResp> getRy3day() {
		return hrmsEmployeeMapper.getRy3day();
	}

	@Override
	public List<EmployeeResp> getRy27day() {
		return hrmsEmployeeMapper.getRy27day();
	}

	@Override
	public List<EmployeeResp> getRy57day() {
		return hrmsEmployeeMapper.getRy57day();
	}

	@Override
	public List<EmployeeResp> getRysyqday() {
		return hrmsEmployeeMapper.getRysyqday();
	}

	@Override
	@Transactional(readOnly = false)
	public int updateDisable(String id, String status) {
		return hrmsEmployeeMapper.updateDisable(id,status);
	}

	@Override
	public Map<String, String> getEmployeeByEmployeeNo(String employeeNo) {

		return hrmsEmployeeMapper.getEmployeeByEmployeeNo(employeeNo);
	}

	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public Integer updateAge() {
		//更新年龄
		return hrmsEmployeeMapper.updateAge();
	}

	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public Integer updateAgeSection() {
		//设置年龄区间
		return hrmsEmployeeMapper.updateAgeSection();
	}

	@Override
	@Transactional(readOnly = false)
	@Deprecated
	public Integer updateSseniority() {
		// TODO Auto-generated method stub
		return hrmsEmployeeMapper.updateSseniority();
	}

	@Override
	public List<String> getEmpnoByEmpId(List empIdList) {
//		Example example = new Example(HrmsEmployee.class);
//		Example.Criteria criteria = example.createCriteria();
//		criteria.andIn("employeeId",empIdList);
//		criteria.andEqualTo("isDeleted","N");
//		List<HrmsEmployee> list = hrmsEmployeeMapper.selectByExample(example);
		
		List<HrmsEmployee> list = hrmsEmployeeMapper.findDetailByIds(empIdList);
		
		List<String> empList = new ArrayList<>();
		if(CollUtil.isNotEmpty(list)){
			empList = list.stream().map(HrmsEmployee::getEmployeeNo).collect(Collectors.toList());
		}
		return empList;
	}

    @Override
    public List<HrmsStatusTypeVo> employeeListStatus(String archivesType) {
        //查询人员花名册类型和数量
        List<HrmsStatusTypeVo> listStatus = hrmsEmployeeMapper.employeeListStatus(UserInfoHolder.getCurrentUserCorpCode(),archivesType);
        Map<String, String> employeeStatusMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_STATUS);
        //取员工总数
        long sum = listStatus.stream().mapToLong(HrmsStatusTypeVo::getEmployeeCount).sum();

        //获取员工状态字典
        listStatus.forEach(hrmsStatusTypeVo -> {
            if (Objects.isNull(employeeStatusMap.get(hrmsStatusTypeVo.getEmployeeStatus()))) {
                hrmsStatusTypeVo.setEmployeeType(DictContants.OTHER);
                hrmsStatusTypeVo.setEmployeeStatus(DictContants.OTHER_STATUS);
            } else {
                hrmsStatusTypeVo.setEmployeeType(employeeStatusMap.get(hrmsStatusTypeVo.getEmployeeStatus()));
            }
        });
        //构建全部员工数据
        HrmsStatusTypeVo hrmsStatusType = new HrmsStatusTypeVo();
        hrmsStatusType.setEmployeeType(DictContants.ALL);

		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		//宁乡妇幼 全部只需要显示在职、返聘、退休人员
		if(StringUtils.isNotBlank(ssoOrgCode) && "nxsfybjy".equals(ssoOrgCode)){
			sum = listStatus.stream().filter(vo-> "1".equals(vo.getEmployeeStatus()) || "6".equals(vo.getEmployeeStatus()) || "8".equals(vo.getEmployeeStatus()))
					.mapToLong(HrmsStatusTypeVo::getEmployeeCount).sum();
			hrmsStatusType.setEmployeeCount(sum);
		}else{
			hrmsStatusType.setEmployeeCount(sum);
		}
        hrmsStatusType.setEmployeeStatus(DictContants.ALL_STATUS);
        listStatus.add(hrmsStatusType);
        listStatus.sort((v1,v2)->{
        	return Convert.toInt(v1.getEmployeeStatus()).compareTo(Convert.toInt(v2.getEmployeeStatus()));
		});
        return listStatus;
    }

	/**
	 * 获取薪酬方案可选员工列表
	 * @param searchListTable
	 * @return
	 */
	@Override
	public List<CheckPersonnel> searchEmployee2OptionDatas(Page page,SearchListTable searchListTable){
		List<CheckPersonnel> list = hrmsEmployeeMapper.searchEmployee2OptionDatas(page,searchListTable);
		if(CollUtil.isNotEmpty(list)){
			Map<String, String> employeeStatusMap = hrmsDictInfoService.convertDictMap(DictContants.EMPLOYEE_STATUS); // 员工状态数据字典
			Map<String, String> establishmentTypeMap = hrmsDictInfoService.convertDictMap(DictContants.ESTABLISHMENT_TYPE); //编制类型
			Map<String, String> personalIdentityMap = hrmsDictInfoService.convertDictMap(DictContants.PERSONAL_IDENTITY); // 岗位类别

			list.forEach(o ->{
				if(StringUtils.isNotBlank(o.getEmployeeStatus())){
					o.setEmployeeStatus(employeeStatusMap.get(o.getEmployeeStatus()));
				}
				if(StringUtils.isNotBlank(o.getEstablishmentType())){
					o.setEstablishmentType(establishmentTypeMap.get(o.getEstablishmentType()));
				}
				if(StringUtils.isNotBlank(o.getPersonalIdentity())){
					o.setPersonalIdentity(personalIdentityMap.get(o.getPersonalIdentity()));
				}
				if(StringUtils.isNotBlank(o.getPlgw())){
					o.setPlgw(basicMapper.getPostCategoryType(o.getPlgw(), UserInfoHolder.getCurrentUserCorpCode()));
				}
				if(StringUtils.isNotBlank(o.getGwdj())){
					o.setGwdj(basicMapper.getPostCategoryLevel(o.getGwdj()));
				}
				if(StringUtils.isNotBlank(o.getSalaryLevel())){
					o.setSalaryLevel(basicMapper.getSalaryLevelCategoryLevel(o.getSalaryLevel()));
				}
			});
		}
		return list;
	}

	@Override
	public List<Map<String, Object>> getExportPhoto(HrmsEmployee record) {
		return hrmsEmployeeMapper.getExportPhoto(record);
	}
    
    
}