package cn.trasen.hrms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.HrmsAdvancementIncidentReq;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.UserDataPermissionVo;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.hrms.bean.AdvancementIncidentExport;
import cn.trasen.hrms.dao.HrmsAdvancementIncidentMapper;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.enums.SalarySoureEnum;
import cn.trasen.hrms.model.HrmsAdvancementIncidentEo;
import cn.trasen.hrms.model.HrmsEmployee;
import cn.trasen.hrms.model.HrmsPostWageVo;
import cn.trasen.hrms.model.HrmsSalaryLevelWage;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmpHistory;
import cn.trasen.hrms.salary.model.HrmsNewsalaryItemLibrary;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpHistoryService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.service.HrmsAdvancementIncidentService;
import cn.trasen.hrms.service.HrmsEmployeeService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import cn.trasen.hrms.utils.UserPermissionManager;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsAdvancementIncidentServiceImpl implements HrmsAdvancementIncidentService {

    private static final Logger logger = LoggerFactory.getLogger(HrmsAdvancementIncidentServiceImpl.class);
    @javax.annotation.Resource
    private HrmsAdvancementIncidentMapper mapper;
    @Autowired
    DictItemFeignService dictItemFeignService;
    @javax.annotation.Resource
    private HrmsNewsalaryBasicitemEmpMapper empMapper;
    @Autowired
    HrmsNewsalaryBasicitemEmpHistoryService hrmsNewsalaryBasicitemEmpHistoryService;
    @Autowired
    HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;
    @javax.annotation.Resource
    HrmsNewsalaryBasicitemEmpMapper hrmsNewsalaryBasicitemEmpMapper;
    @Autowired
    HrmsEmployeeService hrmsEmployeeService;

    @Override
    public List<HrmsAdvancementIncidentEo> getDataList(Page page, HrmsAdvancementIncidentEo entity) {
        UserDataPermissionVo userDataPermissionVo = UserPermissionManager.getInstance().getHrmsUserDataPermission();

        if (null != userDataPermissionVo) {

            if (StringUtils.isNotBlank(userDataPermissionVo.getUserCode())) {

                entity.setCreateUser(userDataPermissionVo.getUserCode());
            }

        }
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        // 处理四个字段
        Map<String, String> postCategoryDictMap = convertDictMap("post_category"); // 岗位类别
        Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category"); // 薪级类别
        List<HrmsAdvancementIncidentEo> list = mapper.getDataSetList(page, entity);

        for (HrmsAdvancementIncidentEo item : list) {
            if (StringUtils.isNotEmpty(item.getNewPlgw())) { // 岗位类别
                item.setNewPlgwText(postCategoryDictMap.get(item.getNewPlgw()));
                item.setOldPlgwText(postCategoryDictMap.get(item.getOldPlgw()));
            }
            if (StringUtils.isNotEmpty(item.getNewSalaryLevelType())) { // 薪级类别
                item.setNewSalaryLevelTypeText(salaryLevelCategoryDictMap.get(item.getNewSalaryLevelType()));
                item.setOldSalaryLevelTypeText(salaryLevelCategoryDictMap.get(item.getOldSalaryLevelType()));
            }
            // 处理岗位等级
            if (StringUtils.isNotEmpty(item.getNewGwdj())) { // 岗位等级
                String _text = empMapper.getGwdjText(item.getNewGwdj());
                String oldText = empMapper.getGwdjText(item.getOldGwdj());
                item.setNewGwdjText(_text);
                item.setOldGwdjText(oldText);
            }
            // 处理薪级等级
            if (StringUtils.isNotEmpty(item.getNewSalaryLevelId())) { // 薪级等级
                String _text = empMapper.getSalaryLeavelText(item.getNewSalaryLevelId());
                String oldText = empMapper.getSalaryLeavelText(item.getOldSalaryLevelId());
                item.setNewSalaryLevelIdText(_text);
                item.setOldSalaryLevelIdText(oldText);
            }
            item.setApprovalStatusText(IncidentAuditStatusEnum.getValByKey(item.getApprovalStatus().toString()));
            item.setSourceTypeText(SalarySoureEnum.getValByKey(item.getSourceType()));
        }

        return list;
    }

    @Override
    public List<HrmsAdvancementIncidentEo> getStatusByEmployeeId(String employeeId) {
        HrmsAdvancementIncidentEo record = new HrmsAdvancementIncidentEo();
        record.setIsDeleted(Contants.IS_DELETED_FALSE);
        record.setApprovalStatus(1);
        record.setEmployeeId(employeeId);
        record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        List<HrmsAdvancementIncidentEo> beanList = mapper.select(record);
        return beanList;
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> insert(HrmsAdvancementIncidentEo entity) {

        List<HrmsAdvancementIncidentEo> list = getStatusByEmployeeId(entity.getEmployeeId());
        if (list != null && list.size() > 0) {
            return PlatformResult.failure("存在未审批的数据");
        }
        
        HrmsEmployee employee = hrmsEmployeeService.findDetailById(entity.getEmployeeId());
        
        if (Objects.nonNull(employee) && !Objects.equals(employee.getSalaryAppoint(),"1")){
            return PlatformResult.failure("未定薪不能晋升岗位");
        }
        entity.setApprovalStatus(Integer.valueOf(IncidentAuditStatusEnum.AUDIT_STATUS_1.getKey()));
        entity.setIsDeleted(Contants.IS_DELETED_FALSE);
        entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
        entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
        entity.setCreateDate(new Date());
        entity.setId(IdUtil.getId());
        entity.setSourceType(1);
        entity.setJobDeionTypeTime(DateUtils.getStringDateShortYM(new Date()));
        entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        mapper.insert(entity);

        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> update(HrmsAdvancementIncidentEo entity) {
        HrmsAdvancementIncidentEo incidentEo = mapper.selectByPrimaryKey(entity.getId());
        if (Objects.equals(incidentEo.getApprovalStatus(), 4)) {
            return PlatformResult.failure("已审核状态不能修改！");
        }
        entity.setUpdateUser(UserInfoHolder.getCurrentUserCode());
        entity.setUpdateUserName(UserInfoHolder.getCurrentUserName());
        entity.setUpdateDate(new Date());
        mapper.updateByPrimaryKeySelective(entity);
        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> incidentAudit(List<String> idList) {
        idList.forEach(id -> {
            dealWith(id);
        });
        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> cancel(List<String> idList) {
        for (String id : idList) {
            HrmsAdvancementIncidentEo hrmsPersonnelIncident = mapper.selectByPrimaryKey(id);
            if (hrmsPersonnelIncident != null) {
                if (!Objects.equals(hrmsPersonnelIncident.getApprovalStatus(), 4)) {
                    return PlatformResult.failure("非已审核状态不能退回！");
                }
                hrmsPersonnelIncident.setApprovalStatus(Integer.valueOf(IncidentAuditStatusEnum.AUDIT_STATUS_3.getKey()));
                hrmsPersonnelIncident.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                hrmsPersonnelIncident.setUpdateUserName(UserInfoHolder.getCurrentUserName());
                hrmsPersonnelIncident.setUpdateDate(new Date());

                mapper.updateByPrimaryKeySelective(hrmsPersonnelIncident);

                //保存数据到定薪表
                commonDataInsert(hrmsPersonnelIncident.getEmployeeId(), hrmsPersonnelIncident.getOldPlgw(), hrmsPersonnelIncident.getOldGwdj(),
                        hrmsPersonnelIncident.getOldSalaryLevelId(), hrmsPersonnelIncident.getOldSalaryLevelType(), hrmsPersonnelIncident.getEffectiveDate(), 0);
            }

        }
        return PlatformResult.success();
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> deleted(String id) {
        HrmsAdvancementIncidentEo incidentEo = mapper.selectByPrimaryKey(id);
        if (incidentEo != null) {
            if (Objects.equals(incidentEo.getApprovalStatus(), 4)) {
                return PlatformResult.failure("已审核状态不能删除！");
            }
            incidentEo.setIsDeleted(Contants.IS_DELETED_TURE);
        }
        mapper.updateByPrimaryKeySelective(incidentEo);
        return PlatformResult.success();
    }

    @Override
    public HrmsAdvancementIncidentEo getDataByEmployeeId(String employeeId) {
        Assert.hasText(employeeId, "员工id不能为空.");
        Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
        criteria.andEqualTo("employeeId", employeeId);

        List<HrmsNewsalaryBasicitemEmp> list = empMapper.getNewsalaryDataByEmployeeId(employeeId);

        HrmsAdvancementIncidentEo incidentEo = new HrmsAdvancementIncidentEo();
        incidentEo.setEmployeeId(employeeId);

        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                if ("plgw".equals(list.get(i).getEmpField())) { // 岗位类别
                    incidentEo.setOldPlgw(list.get(i).getEmpFieldValue());
                    incidentEo.setOldPlgwText(list.get(i).getEmpFieldValueText());
                }
                if ("salary_level_type".equals(list.get(i).getEmpField())) { // 薪级类别
                    incidentEo.setOldSalaryLevelType(list.get(i).getEmpFieldValue());
                    incidentEo.setOldSalaryLevelTypeText(list.get(i).getEmpFieldValueText());
                }
                // 处理岗位等级
                if ("gwdj".equals(list.get(i).getEmpField())) {
                    incidentEo.setOldGwdj(list.get(i).getEmpFieldValue());
                    incidentEo.setOldGwdjText(list.get(i).getEmpFieldValueText());
                }
                // 处理薪级等级
                if ("salary_level_id".equals(list.get(i).getEmpField())) {
                    incidentEo.setOldSalaryLevelId(list.get(i).getEmpFieldValue());
                    incidentEo.setOldSalaryLevelIdText(list.get(i).getEmpFieldValueText());
                }
            }
        }
        return incidentEo;
    }

    @Override
    public List<AdvancementIncidentExport> getList(Page page, HrmsAdvancementIncidentEo entity) {

        List<AdvancementIncidentExport> list = mapper.getList(page, entity);

        for (AdvancementIncidentExport advancementIncidentExport : list) {
            List<AdvancementIncidentExport> detailList = mapper.getDetail(advancementIncidentExport.getEmployeeId(),advancementIncidentExport.getEffectiveDate());
            //岗位类别
            String oldPlgw = empMapper.getGwdjText(advancementIncidentExport.getOldGwdj());
            String newPlgw = empMapper.getGwdjText(advancementIncidentExport.getNewGwdj());
            advancementIncidentExport.setNewPlgw(newPlgw);
            advancementIncidentExport.setOldPlgw(oldPlgw);

            //获取员工薪酬档案的政策标准，如果没有政策标准则默认取最新政策标准
            List<HrmsNewsalaryBasicitemEmpHistory> basicitemEmpHistoryList = hrmsNewsalaryBasicitemEmpHistoryService.getEmployeeIdAndEffectiveDate(advancementIncidentExport.getEmployeeId(),advancementIncidentExport.getEffectiveDate());
            String policyStandardId = null;
            if(CollUtil.isNotEmpty(basicitemEmpHistoryList)){
                basicitemEmpHistoryList = basicitemEmpHistoryList.stream().filter(vo-> "policy_standard_id".equals(vo.getEmpField())).collect(Collectors.toList());
                if(CollUtil.isNotEmpty(basicitemEmpHistoryList)){
                    policyStandardId = basicitemEmpHistoryList.get(0).getEmpFieldValue();
                }
            }
            advancementIncidentExport.setEffectiveDate(entity.getEffectiveDate());
            HrmsPostWageVo oldPostWageVo = mapper.getGwgz(advancementIncidentExport.getOldGwdj(),policyStandardId);
            HrmsPostWageVo newPostWageVo = mapper.getGwgz(advancementIncidentExport.getNewGwdj(),policyStandardId);
            HrmsSalaryLevelWage oldLevelWage = mapper.getXjgz(advancementIncidentExport.getOldSalaryLevelId(),policyStandardId);
            HrmsSalaryLevelWage newLevelWage = mapper.getXjgz(advancementIncidentExport.getNewSalaryLevelId(),policyStandardId);

            //之前的岗位薪资
            if (Objects.nonNull(oldPostWageVo)){
                advancementIncidentExport.setOldAwardWage(Objects.isNull(oldPostWageVo.getAwardWage()) ? new BigDecimal(0):oldPostWageVo.getAwardWage());
                advancementIncidentExport.setOldPostWage(oldPostWageVo.getPostWage());
                advancementIncidentExport.setOldPerformanceWage(oldPostWageVo.getPerformanceWage());
            }
            //提高工资部分
            if (CollectionUtils.isNotEmpty(detailList)){
                advancementIncidentExport.setNewTggz(Objects.equals(detailList.get(0).getText(),"提高工资部分") ? detailList.get(0).getNewTggz():detailList.get(1).getNewTggz());
                advancementIncidentExport.setOldTggz(Objects.equals(detailList.get(0).getText(),"提高工资部分") ? detailList.get(0).getOldTggz():detailList.get(1).getOldTggz());
                advancementIncidentExport.setNewHljt(Objects.equals(detailList.get(0).getText(),"教护龄津贴") ? detailList.get(0).getNewTggz():detailList.get(1).getNewTggz());
                advancementIncidentExport.setOldHljt(Objects.equals(detailList.get(0).getText(),"教护龄津贴") ? detailList.get(0).getOldTggz():detailList.get(1).getOldTggz());
            }

            //之前的薪级薪资
            if (Objects.nonNull(oldLevelWage)) {
                advancementIncidentExport.setOldSalaryLevelWage(Objects.isNull(oldLevelWage.getSalaryLevelWage()) ? new BigDecimal(0) : oldLevelWage.getSalaryLevelWage());
            }
            //现在的薪级薪资
            if (Objects.nonNull(newLevelWage)) {
                advancementIncidentExport.setNewSalaryLevelWage(newLevelWage.getSalaryLevelWage());
            }
            //现在的岗位薪资
            if (Objects.nonNull(newPostWageVo)) {
                advancementIncidentExport.setNewAwardWage(newPostWageVo.getAwardWage());
                advancementIncidentExport.setNewPostWage(newPostWageVo.getPostWage());
                advancementIncidentExport.setNewPerformanceWage(newPostWageVo.getPerformanceWage());
                BigDecimal subtract = newPostWageVo.getAwardWage().subtract(Objects.isNull(oldPostWageVo) ? new BigDecimal(0) :oldPostWageVo.getAwardWage());
                BigDecimal subtract1 = newPostWageVo.getPostWage().subtract(Objects.isNull(oldPostWageVo) ? new BigDecimal(0):oldPostWageVo.getPostWage());
                BigDecimal subtract2 = newPostWageVo.getPerformanceWage().subtract(Objects.isNull(oldPostWageVo) ? new BigDecimal(0): oldPostWageVo.getPerformanceWage());
                BigDecimal  subtract3 = Objects.isNull(newLevelWage) ? new BigDecimal(0) :newLevelWage.getSalaryLevelWage().subtract(Objects.isNull(oldLevelWage) ?new BigDecimal(0):oldLevelWage.getSalaryLevelWage());
                BigDecimal  subtract4 = Objects.isNull(advancementIncidentExport.getNewTggz()) ? new BigDecimal(0) :advancementIncidentExport.getNewTggz().subtract(Objects.isNull(advancementIncidentExport.getOldTggz()) ?new BigDecimal(0):advancementIncidentExport.getOldTggz());
                BigDecimal  subtract5 =Objects.isNull(advancementIncidentExport.getNewHljt()) ? new BigDecimal(0) :advancementIncidentExport.getNewHljt().subtract(Objects.isNull(advancementIncidentExport.getOldHljt()) ?new BigDecimal(0):advancementIncidentExport.getOldHljt());

                //总增值
                BigDecimal sum = subtract.add(subtract1).add(subtract2).add(subtract3).add(subtract4).add(subtract5);
                advancementIncidentExport.setAddSum(sum);
            }

        }
        return list;
    }

    @Override
    public void exportWord(HttpServletResponse response, HrmsAdvancementIncidentEo hrmsAdvancementIncidentEo) {
        // 导出文件名称
        String filename = "岗位异动单.docx";
        // 模板位置
        Resource resource = new ClassPathResource("template/ydd.xlsx");
        XSSFWorkbook workbook = null;
        // 导出数据列表
        Page page = new Page();
        page.setPageNo(1);
        page.setPageSize(20000);
        List<AdvancementIncidentExport> list = getList(page, hrmsAdvancementIncidentEo);
        try {
            workbook = new XSSFWorkbook(resource.getInputStream());
        } catch (IOException e) {
            logger.error("获取导出模板错误" + e.getMessage(), e);
        }
        XSSFWorkbook finalWorkbook = workbook;
        XSSFSheet sheet = finalWorkbook.getSheetAt(0);
        Row row0 = sheet.getRow(1);
        Cell cell = row0.getCell(0);
        cell.setCellValue("填报单位：宁乡市妇幼保健计划生育服务中心");

        if (CollectionUtils.isNotEmpty(list)) {
            XSSFCellStyle colCellStyle = finalWorkbook.createCellStyle();
            colCellStyle.setWrapText(true);
            colCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            colCellStyle.setBorderLeft(BorderStyle.THIN);
            colCellStyle.setBorderTop(BorderStyle.THIN);
            colCellStyle.setBorderRight(BorderStyle.THIN);
            colCellStyle.setBorderBottom(BorderStyle.THIN);
            XSSFFont font = finalWorkbook.createFont();//生产一个字体
            font.setColor(HSSFColor.BLACK.index);    //字体颜色
            font.setFontHeightInPoints((short) 14);
            colCellStyle.setFont(font);

                try {
                    List<BigDecimal> totalSum = new ArrayList<>();
                    for (int i = 0; i < list.size(); i++) {
                        Row newRow = sheet.getRow(i + 4 );
                        BigDecimal oldPostWage = list.get(i).getOldPostWage();
                        BigDecimal oldSalaryLevelWage = list.get(i).getOldSalaryLevelWage();
                        BigDecimal oldPerformanceWage = list.get(i).getOldPerformanceWage();
                        BigDecimal oldAwardWage = list.get(i).getOldAwardWage();

                        BigDecimal newPostWage = list.get(i).getNewPostWage();
                        BigDecimal newSalaryLevelWage = list.get(i).getNewSalaryLevelWage();
                        BigDecimal newPerformanceWage = list.get(i).getNewPerformanceWage();
                        BigDecimal newAwardWage = list.get(i).getNewAwardWage();

                        newRow.getCell(0).setCellValue(i+1);
                        newRow.getCell(0).setCellStyle(colCellStyle);
                        newRow.getCell(1).setCellValue(list.get(i).getEmployeeName());
                        newRow.getCell(1).setCellStyle(colCellStyle);
                        newRow.getCell(2).setCellValue(list.get(i).getIdentityNumber());
                        newRow.getCell(2).setCellStyle(colCellStyle);
                        newRow.getCell(3).setCellValue(list.get(i).getOldPlgw());
                        newRow.getCell(3).setCellStyle(colCellStyle);
                        newRow.getCell(4).setCellValue(list.get(i).getNewPlgw());
                        newRow.getCell(4).setCellStyle(colCellStyle);
                        newRow.getCell(5).setCellValue(list.get(i).getJobDeionTypeTime());
                        newRow.getCell(5).setCellStyle(colCellStyle);
                        newRow.getCell(6).setCellValue(Objects.nonNull(oldPostWage) ? oldPostWage.toString():"0");
                        newRow.getCell(6).setCellStyle(colCellStyle);
                        newRow.getCell(7).setCellValue(Objects.nonNull(oldSalaryLevelWage) ? oldSalaryLevelWage.toString():"0");
                        newRow.getCell(7).setCellStyle(colCellStyle);
                        newRow.getCell(8).setCellValue(Objects.nonNull(list.get(i).getOldTggz()) ? list.get(i).getOldTggz().toString():"0");
                        newRow.getCell(8).setCellStyle(colCellStyle);
                        newRow.getCell(9).setCellValue(Objects.nonNull(list.get(i).getOldHljt()) ? list.get(i).getOldHljt().toString():"0");
                        newRow.getCell(9).setCellStyle(colCellStyle);
                        newRow.getCell(10).setCellValue(Objects.nonNull(oldPerformanceWage) ? oldPerformanceWage.toString():"0");
                        newRow.getCell(10).setCellStyle(colCellStyle);
                        newRow.getCell(11).setCellValue(Objects.nonNull(oldAwardWage) ? oldAwardWage.toString():"0");
                        newRow.getCell(11).setCellStyle(colCellStyle);
                        newRow.getCell(12).setCellValue(Objects.nonNull(newPostWage) ? newPostWage.toString():"0");
                        newRow.getCell(12).setCellStyle(colCellStyle);
                        newRow.getCell(13).setCellValue(Objects.nonNull(newSalaryLevelWage) ? newSalaryLevelWage.toString():"0");
                        newRow.getCell(13).setCellStyle(colCellStyle);
                        newRow.getCell(14).setCellValue(Objects.nonNull(list.get(i).getNewTggz()) ? list.get(i).getNewTggz().toString():"0");
                        newRow.getCell(14).setCellStyle(colCellStyle);
                        newRow.getCell(15).setCellValue(Objects.nonNull(list.get(i).getNewHljt()) ? list.get(i).getNewHljt().toString():"0");
                        newRow.getCell(15).setCellStyle(colCellStyle);
                        newRow.getCell(16).setCellValue(Objects.nonNull(newPerformanceWage) ? newPerformanceWage.toString():"0");
                        newRow.getCell(16).setCellStyle(colCellStyle);
                        newRow.getCell(17).setCellValue(Objects.nonNull(newAwardWage) ? newAwardWage.toString():"0");
                        newRow.getCell(17).setCellStyle(colCellStyle);

                        newRow.getCell(18).setCellValue(Objects.nonNull(list.get(i).getAddSum()) ? list.get(i).getAddSum().toString() :"0");
                        newRow.getCell(18).setCellStyle(colCellStyle);
                        newRow.getCell(19).setCellValue(list.get(i).getEffectiveDate());
                        newRow.getCell(19).setCellStyle(colCellStyle);
                        totalSum.add(Objects.nonNull(list.get(i).getAddSum()) ? list.get(i).getAddSum() :new BigDecimal(0));
                    }
                    Row newRow = sheet.getRow(4 +  list.size() );
                    newRow.getCell(0).setCellValue("合计：");
                    newRow.getCell(18).setCellValue(totalSum.stream()
                            .reduce(BigDecimal.ZERO, BigDecimal::add).toString());
                    commonStyle(newRow,colCellStyle);

                    Row newRow6 = sheet.getRow(5 +  list.size() );
                    // 合并单元格
                    sheet.addMergedRegion(new CellRangeAddress(5 +  list.size(),5 +  list.size() + 3,0 ,3)); // 合并最后一行的第一列到第三列
                    sheet.addMergedRegion(new CellRangeAddress(5 +  list.size(),5 +  list.size() + 3,4 ,7));
                    sheet.addMergedRegion(new CellRangeAddress(5 +  list.size(),5 +  list.size() + 3,8 ,11));
                    sheet.addMergedRegion(new CellRangeAddress(5 +  list.size(),5 +  list.size() + 3,12 ,15));
                    sheet.addMergedRegion(new CellRangeAddress(5 +  list.size(),5 +  list.size() + 3,16 ,19));

                    Cell cell6 = newRow6.createCell(0);
                    cell6.setCellValue("填报人签名：");
                    Cell cell7 = newRow6.createCell(4);
                    cell7.setCellValue("财务科意见：");
                    Cell cell8 = newRow6.createCell(8);
                    cell8.setCellValue("人事分管领导意见：");
                    cell8.setCellStyle(colCellStyle);
                    Cell cell9 = newRow6.createCell(12);
                    cell9.setCellValue("财务分管领导意见：");
                    Cell cell10 = newRow6.createCell(16);
                    cell10.setCellValue("院长意见：");
                    commonStyle(newRow6,colCellStyle);
                    Row newRow7 = sheet.getRow(6 +  list.size() );
                    commonStyle(newRow7,colCellStyle);
                    Row newRow8 = sheet.getRow(7 +  list.size() );
                    commonStyle(newRow8,colCellStyle);
                    Row newRow9 = sheet.getRow(8 +  list.size() );
                    commonStyle(newRow9,colCellStyle);

                } catch (Exception e) {
                    logger.error("导出数据异常" + e.getMessage(), e);
                    PlatformResult.failure("导出数据异常" + e.getMessage());
                }

                try {
                    response.setContentType("application/vnd.ms-excel");
                    response.setCharacterEncoding("UTF-8");
                    response.setHeader("Content-disposition", "attachment; filename="
                            + new String(filename.getBytes("gbk"), "iso8859-1") + ".xlsx");
                    OutputStream fos = response.getOutputStream();
                    finalWorkbook.write(fos);
                    fos.close();
                } catch (Exception e) {
                    logger.error("导出数据异常" + e.getMessage(), e);
                    PlatformResult.failure("导出数据异常" + e.getMessage());
                }
            }
    }

    private void commonStyle(Row newRow,XSSFCellStyle colCellStyle) {
        newRow.getCell(0).setCellStyle(colCellStyle);
        newRow.getCell(1).setCellStyle(colCellStyle);
        newRow.getCell(2).setCellStyle(colCellStyle);
        newRow.getCell(3).setCellStyle(colCellStyle);
        newRow.getCell(4).setCellStyle(colCellStyle);
        newRow.getCell(5).setCellStyle(colCellStyle);
        newRow.getCell(6).setCellStyle(colCellStyle);
        newRow.getCell(7).setCellStyle(colCellStyle);
        newRow.getCell(8).setCellStyle(colCellStyle);
        newRow.getCell(9).setCellStyle(colCellStyle);
        newRow.getCell(10).setCellStyle(colCellStyle);
        newRow.getCell(11).setCellStyle(colCellStyle);
        newRow.getCell(12).setCellStyle(colCellStyle);
        newRow.getCell(13).setCellStyle(colCellStyle);
        newRow.getCell(14).setCellStyle(colCellStyle);
        newRow.getCell(15).setCellStyle(colCellStyle);
        newRow.getCell(16).setCellStyle(colCellStyle);
        newRow.getCell(17).setCellStyle(colCellStyle);
        newRow.getCell(18).setCellStyle(colCellStyle);
        newRow.getCell(19).setCellStyle(colCellStyle);
    }

    @Override
    @Transactional(readOnly = false)
    public PlatformResult<String> empSave(HrmsAdvancementIncidentReq entity) {
        HrmsAdvancementIncidentEo incidentEo = new HrmsAdvancementIncidentEo();
        BeanUtils.copyProperties(entity,incidentEo);
        incidentEo.setSourceType(0);
        incidentEo.setId(IdUtil.getId());
        incidentEo.setJobDeionTypeTime(DateUtils.getStringDateShortYM(new Date()));
        incidentEo.setEffectiveDate(DateUtils.getStringDateShort(new Date()));
        //有修改才保存记录
        if (Objects.equals(1,incidentEo.getType())){
            mapper.insert(incidentEo);
        }

        return PlatformResult.success();
    }

    private int dealWith(String id) {
        HrmsAdvancementIncidentEo hrmsPersonnelIncident = mapper.selectByPrimaryKey(id);
        if (hrmsPersonnelIncident != null) {
            hrmsPersonnelIncident.setApprovalStatus(Integer.valueOf(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey()));

            //保存数据到定薪表
            commonDataInsert(hrmsPersonnelIncident.getEmployeeId(), hrmsPersonnelIncident.getNewPlgw(), hrmsPersonnelIncident.getNewGwdj(),
                    hrmsPersonnelIncident.getNewSalaryLevelId(), hrmsPersonnelIncident.getNewSalaryLevelType(), hrmsPersonnelIncident.getEffectiveDate(), 1);
        }

        return mapper.updateByPrimaryKeySelective(hrmsPersonnelIncident);

    }

    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

    private void commonDataInsert(String employeeId, String plgw, String gwdj, String salaryLevelId, String salaryLevelType, String effectiveDate, int status) {
        Date date1 = DateUtil.parse(DateUtils.getStringDateShort(new Date())); // 当前日期
        Date date2 = DateUtil.parse(effectiveDate); // 生效日期
        int compare = DateUtil.compare(date1, date2, "yyyy-MM-dd");

        // 根据人员id 和定薪时间删除本月的数据
        hrmsNewsalaryBasicitemEmpHistoryService.deleteByEmployeeIdAndDate(employeeId,effectiveDate);

        List<HrmsNewsalaryBasicitemEmpHistory> todoList = new ArrayList();

        //status 0表示撤回
        if (compare >= 0 || status == 0) {
            // 历史表中的生效数据改为 已过期
            hrmsNewsalaryBasicitemEmpHistoryService.updateExpired(employeeId,null);
            //更新员工档案岗位类别岗位等级 薪级类别 薪级等级
            empMapper.updateEmployeeSalary(employeeId, plgw, gwdj, salaryLevelType, salaryLevelId);
        }
        //获取员工薪酬档案的政策标准，如果没有政策标准则默认取最新政策标准
        List<HrmsNewsalaryBasicitemEmp>  basicitemEmpData = hrmsNewsalaryBasicitemEmpMapper.getSalaryOptionEmpEffDate("", employeeId, effectiveDate);
        String policyStandardId = null;
        if(CollUtil.isNotEmpty(basicitemEmpData)){
            basicitemEmpData = basicitemEmpData.stream().filter(vo-> "policy_standard_id".equals(vo.getEmpField())).collect(Collectors.toList());
            if(CollUtil.isNotEmpty(basicitemEmpData)){
                policyStandardId = basicitemEmpData.get(0).getEmpFieldValue();
            }
        }
        //审核通过保存历史记录
        if (status == 1) {
            //处理岗位数据
            List<HrmsNewsalaryBasicitemEmp> records = empMapper.getNewsalaryDataByEmployeeId(employeeId);
            copyList(records, todoList, compare);
            hrmsNewsalaryBasicitemEmpHistoryService.save(todoList);

            //处理薪资
            HrmsPostWageVo newPostWageVo = mapper.getGwgz(gwdj,policyStandardId);
            HrmsSalaryLevelWage newLevelWage = mapper.getXjgz(salaryLevelId,policyStandardId);
            List<HrmsNewsalaryItemLibrary> itemList = mapper.queryNewsalaryInfo();
            List<HrmsNewsalaryBasicitemEmpHistory> historyList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(itemList)){
                itemList.forEach(itemEo->{
                    Example example4 = new Example(HrmsNewsalaryBasicitemEmp.class);
                    Example.Criteria criteria4 = example4.createCriteria();
                    criteria4.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                    criteria4.andEqualTo("empField",itemEo.getId());
                    criteria4.andEqualTo("employeeId", employeeId);
                    HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitem = empMapper.selectOneByExample(example4);
                    if (null != hrmsNewsalaryBasicitem){
                        if ("岗位工资".equals(itemEo.getItemName()) && Objects.nonNull(newPostWageVo.getPostWage())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getPostWage());
                        }else if ("薪级工资".equals(itemEo.getItemName()) && Objects.nonNull(newLevelWage.getSalaryLevelWage())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newLevelWage.getSalaryLevelWage());
                        }else if ("奖励性绩效（人事）".equals(itemEo.getItemName()) && Objects.nonNull(newPostWageVo.getAwardWage())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getAwardWage());
                        }else if ("基础性绩效（人事）".equals(itemEo.getItemName()) && Objects.nonNull(newPostWageVo.getPerformanceWage())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getPerformanceWage());
                        }
                        hrmsNewsalaryBasicitem.setEffectiveDate(effectiveDate);
                        HrmsNewsalaryBasicitemEmpHistory todoBean = new HrmsNewsalaryBasicitemEmpHistory();
                        BeanUtils.copyProperties(hrmsNewsalaryBasicitem, todoBean);
                        historyList.add(todoBean);
                    }
                });
            }
            if (CollectionUtils.isNotEmpty(historyList)){
                hrmsNewsalaryBasicitemEmpHistoryService.save(historyList);
            }
        }

        //退回或者需要生效的立马插入表及时表中
        if (compare >= 0 || status == 0) {
            Map<String, String> postCategoryDictMap = convertDictMap("post_category"); // 岗位类别
            Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category"); // 薪级类别
            //岗位类别
            Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria.andEqualTo("employeeId", employeeId);
            criteria.andEqualTo("empField", "plgw");

            HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitemEmp = new HrmsNewsalaryBasicitemEmp();

            hrmsNewsalaryBasicitemEmp.setEmpFieldValue(plgw);
            hrmsNewsalaryBasicitemEmp.setEmpFieldValueText(postCategoryDictMap.get(plgw));
            hrmsNewsalaryBasicitemEmp.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            hrmsNewsalaryBasicitemEmp.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            hrmsNewsalaryBasicitemEmp.setUpdateDate(new Date());
            hrmsNewsalaryBasicitemEmp.setEffectiveDate(effectiveDate);
            empMapper.updateByExampleSelective(hrmsNewsalaryBasicitemEmp, example);

            //岗位等级
            Example example1 = new Example(HrmsNewsalaryBasicitemEmp.class);
            Example.Criteria criteria1 = example1.createCriteria();
            criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria1.andEqualTo("employeeId", employeeId);
            criteria1.andEqualTo("empField", "gwdj");

            HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitemEmp1 = new HrmsNewsalaryBasicitemEmp();
            hrmsNewsalaryBasicitemEmp1.setEmpFieldValue(gwdj);
            hrmsNewsalaryBasicitemEmp1.setEmpFieldValueText(empMapper.getGwdjText(gwdj));
            hrmsNewsalaryBasicitemEmp1.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            hrmsNewsalaryBasicitemEmp1.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            hrmsNewsalaryBasicitemEmp1.setUpdateDate(new Date());
            hrmsNewsalaryBasicitemEmp1.setEffectiveDate(effectiveDate);
            empMapper.updateByExampleSelective(hrmsNewsalaryBasicitemEmp1, example1);

            //薪级等级
            Example example2 = new Example(HrmsNewsalaryBasicitemEmp.class);
            Example.Criteria criteria2 = example2.createCriteria();
            criteria2.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria2.andEqualTo("employeeId", employeeId);
            criteria2.andEqualTo("empField", "salary_level_id");

            HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitemEmp2 = new HrmsNewsalaryBasicitemEmp();
            hrmsNewsalaryBasicitemEmp2.setEmpFieldValue(salaryLevelId);
            String salaryLeavelText = empMapper.getSalaryLeavelText(salaryLevelId);
            hrmsNewsalaryBasicitemEmp2.setEmpFieldValueText(salaryLeavelText);
            hrmsNewsalaryBasicitemEmp2.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            hrmsNewsalaryBasicitemEmp2.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            hrmsNewsalaryBasicitemEmp2.setUpdateDate(new Date());
            hrmsNewsalaryBasicitemEmp2.setEffectiveDate(effectiveDate);
            empMapper.updateByExampleSelective(hrmsNewsalaryBasicitemEmp2, example2);

            //薪级类别
            Example example3 = new Example(HrmsNewsalaryBasicitemEmp.class);
            Example.Criteria criteria3 = example3.createCriteria();
            criteria3.andEqualTo(Contants.IS_DELETED_FIELD, "N");
            criteria3.andEqualTo("employeeId", employeeId);
            criteria3.andEqualTo("empField", "salary_level_type");

            HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitemEmp3 = new HrmsNewsalaryBasicitemEmp();
            hrmsNewsalaryBasicitemEmp3.setEmpFieldValue(salaryLevelType);
            hrmsNewsalaryBasicitemEmp3.setEmpFieldValueText(salaryLevelCategoryDictMap.get(salaryLevelType));
            hrmsNewsalaryBasicitemEmp3.setUpdateUser(UserInfoHolder.getCurrentUserCode());
            hrmsNewsalaryBasicitemEmp3.setUpdateUserName(UserInfoHolder.getCurrentUserName());
            hrmsNewsalaryBasicitemEmp3.setUpdateDate(new Date());
            hrmsNewsalaryBasicitemEmp3.setEffectiveDate(effectiveDate);
            empMapper.updateByExampleSelective(hrmsNewsalaryBasicitemEmp3, example3);

            HrmsPostWageVo newPostWageVo = mapper.getGwgz(gwdj,policyStandardId);
            HrmsSalaryLevelWage newLevelWage = mapper.getXjgz(salaryLevelId,policyStandardId);
            List<HrmsNewsalaryItemLibrary> itemList = mapper.queryNewsalaryInfo();
            if (CollectionUtils.isNotEmpty(itemList)){
                itemList.forEach(itemEo->{
                    Example example4 = new Example(HrmsNewsalaryBasicitemEmp.class);
                    Example.Criteria criteria4 = example4.createCriteria();
                    criteria4.andEqualTo(Contants.IS_DELETED_FIELD, "N");
                    criteria4.andEqualTo("empField",itemEo.getId());
                    criteria4.andEqualTo("employeeId", employeeId);
                    HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitem = empMapper.selectOneByExample(example4);
                    if (Objects.nonNull(hrmsNewsalaryBasicitem)){
                        if ("岗位工资".equals(itemEo.getItemName())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getPostWage());
                        }else if ("薪级工资".equals(itemEo.getItemName())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newLevelWage.getSalaryLevelWage());
                        }else if ("奖励性绩效（人事）".equals(itemEo.getItemName())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getAwardWage());
                        }else if ("基础性绩效（人事）".equals(itemEo.getItemName())){
                            hrmsNewsalaryBasicitem.setSalaryAmount(newPostWageVo.getPerformanceWage());
                        }
                        hrmsNewsalaryBasicitem.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                        hrmsNewsalaryBasicitem.setUpdateUserName(UserInfoHolder.getCurrentUserName());
                        hrmsNewsalaryBasicitem.setUpdateDate(new Date());
                        empMapper.updateByPrimaryKeySelective(hrmsNewsalaryBasicitem);
                    }
                });
            }
        }
        //计算员工的定薪调薪数据 add ni.jiang
        hrmsNewsalaryBasicitemEmpService.CalculatSingle(employeeId);
    }

    // 复制ListBean
    private void copyList(List<HrmsNewsalaryBasicitemEmp> oldList, List<HrmsNewsalaryBasicitemEmpHistory> toDoList, int compare) {
        oldList.forEach(stu -> {
            HrmsNewsalaryBasicitemEmpHistory todoBean = new HrmsNewsalaryBasicitemEmpHistory();
            BeanUtils.copyProperties(stu, todoBean);
            if (compare < 0) { // 当前日期小于生效日期
                todoBean.setDataStatus("0"); // 未生效
            } else if (compare == 0) {// 当前日期等于生效日期
                todoBean.setDataStatus("1"); // 已生效
            } else if (compare > 0) {// 当前日期大于生效日期
                todoBean.setDataStatus("1"); // 立马生效
            }
            toDoList.add(todoBean);
        });
    }

}
