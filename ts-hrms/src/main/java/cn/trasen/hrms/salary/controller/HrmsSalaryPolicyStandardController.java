package cn.trasen.hrms.salary.controller;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import cn.trasen.hrms.salary.model.HrmsSalaryPolicyStandard;
import cn.trasen.hrms.salary.service.HrmsSalaryPolicyStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.List;

/**
 * @ClassName HrmsSalaryPolicyStandardController
 * @Description TODO
 * @date 2025��2��24�� ����11:15:17
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsSalaryPolicyStandardController")
public class HrmsSalaryPolicyStandardController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsSalaryPolicyStandardController.class);

	@Autowired
	private HrmsSalaryPolicyStandardService hrmsSalaryPolicyStandardService;

	/**
	 * @Title saveHrmsSalaryPolicyStandard
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/salaryPolicyStandard/save")
	public PlatformResult<String> saveHrmsSalaryPolicyStandard(@RequestBody HrmsSalaryPolicyStandard record) {
		try {
			if (hrmsSalaryPolicyStandardService.save(record) > 0) {
				return PlatformResult.success();
			}
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsSalaryPolicyStandard
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/salaryPolicyStandard/update")
	public PlatformResult<String> updateHrmsSalaryPolicyStandard(@RequestBody HrmsSalaryPolicyStandard record) {
		try {
			hrmsSalaryPolicyStandardService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsSalaryPolicyStandardById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsSalaryPolicyStandard>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/salaryPolicyStandard/{id}")
	public PlatformResult<HrmsSalaryPolicyStandard> selectHrmsSalaryPolicyStandardById(@PathVariable String id) {
		try {
			HrmsSalaryPolicyStandard record = hrmsSalaryPolicyStandardService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsSalaryPolicyStandardById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/salaryPolicyStandard/delete/{id}")
	public PlatformResult<String> deleteHrmsSalaryPolicyStandardById(@PathVariable String id) {
		try {
			hrmsSalaryPolicyStandardService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsSalaryPolicyStandardList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsSalaryPolicyStandard>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "分页列表", notes = "分页列表")
	@PostMapping("/api/salaryPolicyStandard/list")
	public DataSet<HrmsSalaryPolicyStandard> selectHrmsSalaryPolicyStandardList(Page page, HrmsSalaryPolicyStandard record) {
		return hrmsSalaryPolicyStandardService.getDataSetList(page, record);
	}

	/**
	 * @Title selectHrmsSalaryPolicyStandardList
	 * @Description 查询列表
	 * @param record
	 * @return DataSet<HrmsSalaryPolicyStandard>
	 * @date 2025��2��24�� ����11:15:17
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@PostMapping("/api/salaryPolicyStandard/data")
	public PlatformResult<List<HrmsSalaryPolicyStandard>> selectHrmsSalaryPolicyStandardData(HrmsSalaryPolicyStandard record) {
		List<HrmsSalaryPolicyStandard> list =hrmsSalaryPolicyStandardService.getList(record);
		return PlatformResult.success(list);
	}

	@ApiOperation(value = "批量启用禁用", notes = "批量启用禁用")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "policyStandardId", value = "多个用逗号分开", required = true, dataType = "String")
			, @ApiImplicitParam(name = "enable", value = "1启用0禁用", required = true, dataType = "String")
	})
	@RequestMapping(value = "/api/salaryPolicyStandard/batchEnable", method = {RequestMethod.POST, RequestMethod.GET})
	public PlatformResult batchEnable(@RequestParam("policyStandardId") String policyStandardId, @RequestParam("enable") String enable) {
		try {
			// 把逗号分隔的postId 转成数组 这里数据量不到 直接loop 另外一个问题是因为要增加操作日志 这样比较方便
			// 如果性能差 则再优化 先不过度设计
			String[] policyStandardIds = policyStandardId.split(",");
			for (String id : policyStandardIds) {
				HrmsSalaryPolicyStandard entity = new HrmsSalaryPolicyStandard();
				entity.setPolicyStandardId(id);
				entity.setIsEnable(enable);
				hrmsSalaryPolicyStandardService.update(entity);
			}
			return PlatformResult.success();
		} catch (Exception e) {
			return PlatformResult.failure(e.getMessage());
		}
	}
}
