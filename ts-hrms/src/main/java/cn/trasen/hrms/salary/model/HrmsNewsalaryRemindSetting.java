package cn.trasen.hrms.salary.model;

import io.swagger.annotations.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.*;

/**
 * 薪酬提醒表
 *
 */
@Table(name = "hrms_newsalary_remind_setting")
@Setter
@Getter
public class HrmsNewsalaryRemindSetting {
    /**
     * 主键ID
     */
    @Id
    @ApiModelProperty(value = "主键ID")
    private String id;

    /**
     * 提醒类型: 1-方案; 2-薪酬薪资类;3-薪级工资自动调整;
     */
    @Column(name = "remind_type")
    @ApiModelProperty(value = "提醒类型: 1-方案; 2-薪酬薪资类;3-薪级工资自动调整")
    private String remindType;

    /**
     * 提前预警天数
     */
    @Column(name = "advance_days")
    @ApiModelProperty(value = "提前预警天数")
    private Integer advanceDays;

    /**
     * 预警周期 1-单次 2-每天 3-每周 4-每月 5-每年
     */
    @Column(name = "remind_cycle")
    @ApiModelProperty(value = "预警周期 1-单次 2-每天 3-每周 4-每月 5-每年")
    private String remindCycle;

    /**
     * 提醒日期
     */
    @Column(name = "remind_date")
    @ApiModelProperty(value = "提醒日期")
    private String remindDate;

    /**
     * 提醒时间
     */
    @Column(name = "remind_time")
    @ApiModelProperty(value = "提醒时间")
    private String remindTime;

    /**
     * 通知人
     */
    @Column(name = "notice_user")
    @ApiModelProperty(value = "通知人")
    private String noticeUser;

    /**
     * 通知人名称
     */
    @Column(name = "notice_user_name")
    @ApiModelProperty(value = "通知人名称")
    private String noticeUserName;

    /**
     * 通知方式  1站内  2微信 3-短信
     */
    @Column(name = "notice_type")
    @ApiModelProperty(value = "通知方式  1站内  2微信 3-短信")
    private String noticeType;

    /**
     * 通知模板
     */
    @Column(name = "notice_template")
    @ApiModelProperty(value = "通知模板")
    private String noticeTemplate;

    /**
     * 是否启用  1正常  0停用
     */
    @Column(name = "is_enabled")
    @ApiModelProperty(value = "是否启用  1正常  0停用")
    private String isEnabled;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建者ID
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建者ID")
    private String createUser;

    /**
     * 创建者姓名
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建者姓名")
    private String createUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 更新者ID
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新者ID")
    private String updateUser;

    /**
     * 更新者姓名
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新者姓名")
    private String updateUserName;

    /**
     * 删除标识: Y=是; N=否;
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标识: Y=是; N=否;")
    private String isDeleted;

    @Column(name = "sso_org_code")
    private String ssoOrgCode;

    @Column(name = "sso_org_name")
    private String ssoOrgName;

    @Transient
    @ApiModelProperty(value = "薪酬项目提醒设置列表")
    private List<HrmsNewsalaryItemRemindSetting> itemRemindSettingList = new ArrayList<>();

    @Transient
    @ApiModelProperty(value = "薪级等级提醒设置列表")
    private List<HrmsNewsalaryLevelRemindSetting> levelRemindSettingList = new ArrayList<>();
}