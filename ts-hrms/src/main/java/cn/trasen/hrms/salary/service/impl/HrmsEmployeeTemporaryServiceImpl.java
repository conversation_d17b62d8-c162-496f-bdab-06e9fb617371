package cn.trasen.hrms.salary.service.impl;

import java.math.BigDecimal;
import java.util.*;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.HrmsOrganizationResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.utils.DateUtil;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.dao.HrmsPostMapper;
import cn.trasen.hrms.model.*;
import cn.trasen.hrms.salary.enums.EmployeeTemporaryOpTypeEnum;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicColumn;
import cn.trasen.hrms.salary.model.HrmsNewsalaryBasicitemEmp;
import cn.trasen.hrms.salary.model.HrmsSalaryPolicyStandard;
import cn.trasen.hrms.salary.service.HrmsEmployeeTemporaryChangeService;
import cn.trasen.hrms.salary.service.HrmsNewsalaryBasicitemEmpService;
import cn.trasen.hrms.salary.service.HrmsSalaryPolicyStandardService;
import cn.trasen.hrms.service.HrmsSalaryLevelService;
import cn.trasen.hrms.utils.DateUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.hrms.salary.dao.HrmsEmployeeTemporaryMapper;
import cn.trasen.hrms.salary.model.HrmsEmployeeTemporary;
import cn.trasen.hrms.salary.service.HrmsEmployeeTemporaryService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;

/**
 * @ClassName HrmsEmployeeTemporaryServiceImpl
 * @Description TODO
 * @date 2024��10��8�� ����3:11:08
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsEmployeeTemporaryServiceImpl implements HrmsEmployeeTemporaryService {

	@Resource
	private HrmsEmployeeTemporaryMapper mapper;

	@Resource
	private HrmsEmployeeMapper hrmsEmployeeMapper;

	@Resource
	private HrmsPostMapper hrmsPostMapper;

	@Resource
	private DictItemFeignService dictItemFeignService;

	@Resource
	private HrmsOrganizationFeignService hrmsOrganizationService;

	@Resource
	private HrmsEmployeeTemporaryChangeService employeeTemporaryChangeService;

	@Autowired
	private HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;

	@Autowired
	private HrmsSalaryLevelService hrmsSalaryLevelService;

	@Autowired
	private HrmsSalaryPolicyStandardService salaryPolicyStandardService;


	@Transactional(readOnly = false)
	@Override
	public Integer save(HrmsEmployeeTemporary record,EmployeeTemporaryOpTypeEnum opTypeEnum) {
		//根据工号+身份证号确定一条数据
		Assert.hasText(record.getEmployeeNo(), "工号不能为空.");
		Assert.hasText(record.getIdentityNumber(), "身份证号不能为空.");
		Assert.hasText(record.getEmployeeName(), "姓名不能为空.");
		Example example = new Example(HrmsEmployeeTemporary.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeNo", record.getEmployeeNo())
				.andEqualTo("identityNumber",record.getIdentityNumber())
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsEmployeeTemporary> records = mapper.selectByExample(example);
		if(CollectionUtil.isNotEmpty(records)){
			throw new BusinessException("当前工号已存在，请勿重复创建");
		}
		//判断当前员工是否在员工档案存在
		HrmsEmployee employee = new HrmsEmployee();
		employee.setEmployeeNo(record.getEmployeeNo());
		employee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsEmployee> empRecords = hrmsEmployeeMapper.getEmployeeList(employee);
		if(CollectionUtil.isNotEmpty(empRecords)){
			throw new BusinessException("当前工号已在员工档案存在，请勿重复使用");
		}

		if (!StringUtil.isEmpty(record.getIdentityNumber())) {
			try {
				record.setBirthday(DateUtils.getStringToDatePlain(record.getIdentityNumber().substring(6, 14))); // 出生日期
			} catch (Exception e) {
				log.error("出生日期转换失败" + e.getMessage(), e);
				record.setBirthday(null); // 出生日期
			}
		} else {
			record.setBirthday(null); // 出生日期
		}

		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setTmpEmployeeStatus("1");
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		//保存操作记录
		employeeTemporaryChangeService.saveChanges(null,record,opTypeEnum);
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(HrmsEmployeeTemporary record,EmployeeTemporaryOpTypeEnum opTypeEnum) {
		//根据工号+身份证号确定一条数据
		Assert.hasText(record.getId(), "ID不能为空.");
		Assert.hasText(record.getEmployeeNo(), "工号不能为空.");
		Assert.hasText(record.getIdentityNumber(), "身份证号不能为空.");
		Assert.hasText(record.getEmployeeName(), "姓名不能为空.");
		//判断当员工是否已离职，离职以后得数据不能进行编辑操作
		HrmsEmployeeTemporary entity =mapper.selectByPrimaryKey(record.getId());
		if(entity == null || "Y".equals(entity.getIsDeleted())){
			throw new BusinessException("未找到可以编辑的数据");
		}else if("2".equals(entity.getTmpEmployeeStatus())){
			throw new BusinessException("已离职的员工信息不允许进行编辑操作");
		}

		Example example = new Example(HrmsEmployeeTemporary.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeNo", record.getEmployeeNo())
				.andEqualTo("identityNumber",record.getIdentityNumber())
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsEmployeeTemporary> records = mapper.selectByExample(example);
		if(CollectionUtil.isNotEmpty(records)){
			if(records.size()>1) {
				throw new BusinessException("当前工号或身份证号已存在，请重新操作");
			}else if(records.size()==1 && !record.getId().equals(records.get(0).getId())){
				throw new BusinessException("当前工号或身份证号已存在，请重新操作");
			}
		}
		//判断当前员工是否在员工档案存在
		HrmsEmployee employee = new HrmsEmployee();
		employee.setEmployeeNo(record.getEmployeeNo());
		employee.setIdentityNumber(record.getIdentityNumber());
		employee.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsEmployee> empRecords = hrmsEmployeeMapper.getEmployeeList(employee);
		if(CollectionUtil.isNotEmpty(empRecords)){
			throw new BusinessException("当前员工已在员工档案存在，请勿重复操作");
		}
		if (!StringUtil.isEmpty(record.getIdentityNumber())) {
			try {
				record.setBirthday(DateUtils.getStringToDatePlain(record.getIdentityNumber().substring(6, 14))); // 出生日期
			} catch (Exception e) {
				log.error("出生日期转换失败" + e.getMessage(), e);
				record.setBirthday(null); // 出生日期
			}
		} else {
			record.setBirthday(null); // 出生日期
		}
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//保存操作记录
		employeeTemporaryChangeService.saveChanges(entity,record,opTypeEnum);
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsEmployeeTemporary record = new HrmsEmployeeTemporary();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsEmployeeTemporary selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<HrmsEmployeeTemporary> getDataSetList(Page page, HrmsEmployeeTemporary record) {
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsEmployeeTemporary> records = mapper.getPageList(page,record);
		return records;
	}

    @Transactional(readOnly = false)
    @Override
    public Integer sync2Salary() {
        Example example = new Example(HrmsEmployeeTemporary.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("isSyncSalary", "N")
				.andEqualTo(Contants.IS_DELETED_FIELD, "N")
			    .andEqualTo("tmpEmployeeStatus","1");
        List<HrmsEmployeeTemporary> list =  mapper.selectByExample(example);

        HrmsEmployeeTemporary employeeTemporary = new HrmsEmployeeTemporary();
        employeeTemporary.setIsSyncSalary("Y");
        employeeTemporary.setUpdateDate(new Date());
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        if (user != null) {
            employeeTemporary.setUpdateUser(user.getUsercode());
            employeeTemporary.setUpdateUserName(user.getUsername());
        }
		Integer count =  mapper.updateByExampleSelective(employeeTemporary,example);

        //获取岗位等级、薪级等级数据
		Map<String, String> salaryLevelCategoryDictMap = convertReversalDictMap("salary_level_category"); // 获取薪级类别 通过字典
		Map<String, String> postCategoryDictMap = convertReversalDictMap("post_category"); // 获取岗位类别
		String plgwCode = postCategoryDictMap.get("无");
		String salaryLevelTypeCode = salaryLevelCategoryDictMap.get("无");
		// 导入前对基础资料进行缓存
		Example postExample = new Example(HrmsPost.class);
		Example.Criteria postCriteria = postExample.createCriteria();
		postCriteria.andEqualTo("postName", "无")
				.andEqualTo("postCategory", plgwCode)
				.andEqualTo("isEnable","1")
				.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsPost> postList = hrmsPostMapper.selectByExample(postExample); // 岗位及工资缓存
		String gwdjCode = CollUtil.isNotEmpty(postList) ? postList.get(0).getPostId() : null;

		// 薪资等级及工资缓存
		HrmsSalaryLevel salaryLevel = new HrmsSalaryLevel();
		salaryLevel.setSalaryLevelCategory(salaryLevelTypeCode);
		salaryLevel.setSalaryLevelName("无");
		List<HrmsSalaryLevel> hslw = hrmsSalaryLevelService.getList(salaryLevel);
		String salaryLevelId = CollectionUtil.isNotEmpty(hslw) ? hslw.get(0).getSalaryLevelId() : null;

		//获取最新的政策标准
		HrmsSalaryPolicyStandard policyStandard = new HrmsSalaryPolicyStandard();
		policyStandard.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		policyStandard.setIsEnable("1");
		List<HrmsSalaryPolicyStandard> policyStandardList = salaryPolicyStandardService.getList(policyStandard);
		if(CollUtil.isEmpty(policyStandardList)){
			throw new BusinessException("请先前往薪酬管理-基础配置进行政策标准的设置");
		}
		policyStandard = policyStandardList.get(0);

		HrmsEmployeeTemporary newEntity = null;
		HrmsNewsalaryBasicColumn newsalaryBasicColumns = null;
		List<HrmsNewsalaryBasicColumn> columnsList = null;
		List<HrmsNewsalaryBasicitemEmp> basicitemEmpList = new ArrayList<>();
		HrmsNewsalaryBasicitemEmp basicitemEmp = null;
        for(HrmsEmployeeTemporary entity : list){
        	try {
				//保存操作记录
				newEntity = new HrmsEmployeeTemporary();
				BeanUtils.copyProperties(newEntity,entity);
				newEntity.setIsSyncSalary("Y");
				newEntity.setUpdateDate(employeeTemporary.getUpdateDate());
				newEntity.setUpdateUser(user.getUsercode());
				newEntity.setUpdateUserName(user.getUsername());
				employeeTemporaryChangeService.saveChanges(entity,newEntity,EmployeeTemporaryOpTypeEnum.SYNC);

				//创建薪酬档案定薪数据
				newsalaryBasicColumns = new HrmsNewsalaryBasicColumn();
				newsalaryBasicColumns.setEmployeeId(newEntity.getId());
				columnsList = hrmsNewsalaryBasicitemEmpService.getAllData(newsalaryBasicColumns);
				for(HrmsNewsalaryBasicColumn column : columnsList){
					basicitemEmp = new HrmsNewsalaryBasicitemEmp();
					BeanUtils.copyProperties(basicitemEmp,column);
					basicitemEmp.setEmployeeId(newEntity.getId());
					basicitemEmp.setBasicItemId(column.getId());
					basicitemEmp.setEffectiveDate(DateUtils.getStringDateShort(DateUtil.getMonthBegin(DateUtils.getStringToDate(newEntity.getEffectiveDate()))));
					basicitemEmp.setReason("临时员工同步至薪酬档案");
					// 基础信息
					if ("1".equals(column.getBasicItemType())) {
						// 处理岗位类别 岗位等级 薪级类别 薪级等级
						if ("plgw".equals(column.getEmpField())) {
							if (StringUtils.isNoneBlank(plgwCode)) {
								basicitemEmp.setEmpFieldValue(plgwCode);
								basicitemEmp.setEmpFieldValueText("无");
							} else {
								basicitemEmp.setEmpFieldValueText("无");
							}
						} else if ("gwdj".equals(column.getEmpField())) {
							if (StringUtils.isNoneBlank(gwdjCode)) {
								basicitemEmp.setEmpFieldValue(gwdjCode);
								basicitemEmp.setEmpFieldValueText("无");
							} else {
								basicitemEmp.setEmpFieldValueText("无");
							}
						} else if ("salary_level_type".equals(column.getEmpField())) {
							if (StringUtils.isNoneBlank(salaryLevelTypeCode)) {
								basicitemEmp.setEmpFieldValue(salaryLevelTypeCode);
								basicitemEmp.setEmpFieldValueText("无");
							} else {
								basicitemEmp.setEmpFieldValueText("无");
							}
						} else if ("salary_level_id".equals(column.getEmpField())) {
							if (StringUtils.isNoneBlank(salaryLevelId)) {
								basicitemEmp.setEmpFieldValue(salaryLevelId);
								basicitemEmp.setEmpFieldValueText("无");
							} else {
								basicitemEmp.setEmpFieldValueText("无");
							}
						}else if ("policy_standard_id".equals(column.getEmpField())) {
							basicitemEmp.setEmpFieldValue(policyStandard.getPolicyStandardId());
							basicitemEmp.setEmpFieldValueText(policyStandard.getPolicyStandardName());
						}
					} else if ("2".equals(column.getBasicItemType())) { // 工资项
						if ("2".equals(column.getBasicItemValue())) {
							basicitemEmp.setEmpFieldValue("100");
						}
						if ("A01".equals(column.getCustomRule())) {
							basicitemEmp.setSalaryAmount(newEntity.getTmpPositionSalary());
						}
					}
					basicitemEmp.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
					basicitemEmpList.add(basicitemEmp);
				}
				hrmsNewsalaryBasicitemEmpService.save(basicitemEmpList);
			}catch (Exception e){
        		e.printStackTrace();
			}
		}
		return count;
    }

	private Map<String, String> convertReversalDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemName(), d.getItemNameValue());
			}
		}
		return map;
	}

	@Transactional(readOnly = false)
	@Override
	public Map<String,Integer> importEmployeeTemporary(List<HrmsEmployeeTemporary> tmpEmpList){
		log.info("导入的数据EmployeeTemporaryList:" + tmpEmpList);
		if(CollectionUtil.isEmpty(tmpEmpList)){
			throw new BusinessException("请至少添加一条数据");
		}
		//处理工作年限  性别 学历
		Map<String, String> sexConvertDictMap = convertDictMap("SEX");  //性别
		Map<String, String> educationConvertDictMap = convertDictMap("education_type");  //学历
		Map<String, String> positionConvertDictMap = convertDictMap("personal_identity");  //岗位
		Map<String, String> establishmentConvertDictMap = convertDictMap("establishment_type");  //编制
		int sucessNum = 0;
        Example example = null;
        Example.Criteria criteria = null;
        List<HrmsEmployeeTemporary> datas = null;
        HrmsEmployeeTemporary vo = null;

		// 查询所有的组织机构
		List<HrmsOrganizationResp> allOrgs = hrmsOrganizationService.getOrgAllList().getObject();
		Map<String, String> orgMaps = Maps.newHashMap();
		//获取第一级组织机构id
		String firstOrgId = allOrgs.get(0).getOrganizationId();
		if (CollectionUtils.isNotEmpty(allOrgs)) {
			for (HrmsOrganizationResp org : allOrgs) {
				orgMaps.put(org.getName(), org.getOrganizationId());
				if(StringUtils.isEmpty(org.getParentId())){
					firstOrgId = org.getOrganizationId();
				}
			}
		}

		for (HrmsEmployeeTemporary employeeTemporary : tmpEmpList) {
			if(StringUtils.isNotBlank(employeeTemporary.getOrgName())){
				employeeTemporary.setOrgId(orgMaps.get(employeeTemporary.getOrgName().trim()));
			}else{
				employeeTemporary.setOrgId(firstOrgId);
			}
			if (StringUtils.isBlank(employeeTemporary.getGender()) == false) { //性别
				employeeTemporary.setGender(sexConvertDictMap.get(employeeTemporary.getGenderName().trim()));
			}
			if (StringUtils.isBlank(employeeTemporary.getTmpEducationName()) == false) { //学历
				employeeTemporary.setTmpEducation(educationConvertDictMap.get(employeeTemporary.getTmpEducationName().trim()));
			}
			if (StringUtils.isBlank(employeeTemporary.getTmpPositionName()) == false) {//岗位
				employeeTemporary.setTmpPosition(positionConvertDictMap.get(employeeTemporary.getTmpPositionName().trim()));
			}
			if (StringUtils.isBlank(employeeTemporary.getTmpEstablishmentName()) == false) {//编制
				employeeTemporary.setTmpEstablishment(establishmentConvertDictMap.get(employeeTemporary.getTmpEstablishmentName().trim()));
			}
			if(employeeTemporary.getTmpPositionSalary() == null){
				employeeTemporary.setTmpPositionSalary(BigDecimal.ZERO);
			}
			if(StringUtils.isNotBlank(employeeTemporary.getJoinDate())){
				employeeTemporary.setEffectiveDate(employeeTemporary.getJoinDate());
			}else{
				employeeTemporary.setJoinDate(DateUtils.getStringDateShort(new Date()));
				employeeTemporary.setEffectiveDate(employeeTemporary.getJoinDate());
			}
			if(employeeTemporary.getBirthday()==null) {
				//改为从身份证号码获取，以免出错
				String identityNumber = employeeTemporary.getIdentityNumber();
				if (!StringUtil.isEmpty(identityNumber)) {
					try {
						employeeTemporary.setBirthday(DateUtils.getStringToDatePlain(identityNumber.substring(6, 14))); // 出生日期
					} catch (Exception e) {
						log.error("出生日期转换失败" + e.getMessage(), e);
						employeeTemporary.setBirthday(null); // 出生日期
					}
				} else {
					employeeTemporary.setBirthday(null); // 出生日期
				}
			}
			employeeTemporary.setIsSyncSalary("N");
			employeeTemporary.setTmpEmployeeStatus("1");

			example = new Example(HrmsEmployeeTemporary.class);
			criteria = example.createCriteria();
            criteria.andEqualTo("employeeNo", employeeTemporary.getEmployeeNo()).andEqualTo("identityNumber",employeeTemporary.getIdentityNumber()).andEqualTo(Contants.IS_DELETED_FIELD, "N");
            datas = mapper.selectByExample(example);
            if(datas!=null &&datas.size()>0){
                vo = datas.get(0);
                if("N".equals(vo.getIsSyncSalary())){ //未同步则可以进行修改
                    employeeTemporary.setId(vo.getId());
                    sucessNum += update(employeeTemporary, EmployeeTemporaryOpTypeEnum.BATCHENTRY);
                }
            }else {
                sucessNum += save(employeeTemporary, EmployeeTemporaryOpTypeEnum.BATCHENTRY);
            }
		}
		Map<String,Integer> map = new HashMap<>();
		map.put("successNum",sucessNum);
		map.put("errorNum",tmpEmpList.size() - sucessNum);
		return map;
	}

	/**
	 *
	 * @Title batchAdjustSalary
	 * @Description 批量调薪
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Map<String,Integer> batchAdjustSalary(List<HrmsEmployeeTemporary> tmpEmpList){
		log.info("批量调薪的数据EmployeeTemporaryList:" + tmpEmpList);
		if(CollectionUtil.isEmpty(tmpEmpList)){
			throw new BusinessException("请至少添加一条数据");
		}
		Example example = new Example(HrmsEmployeeTemporary.class);
		Example.Criteria criteria = null;
		int successNum = 0;
		HrmsEmployeeTemporary oldEntity = null;
		for(HrmsEmployeeTemporary employeeTemporary: tmpEmpList){
			if(employeeTemporary==null
					|| StringUtil.isEmpty(employeeTemporary.getEmployeeNo())
					|| StringUtil.isEmpty(employeeTemporary.getIdentityNumber())){
				continue;
			}
            if(employeeTemporary.getTmpPositionSalary() == null){
				employeeTemporary.setTmpPositionSalary(BigDecimal.ZERO);
			}
			if(StringUtils.isBlank(employeeTemporary.getEffectiveDate())){
				employeeTemporary.setEffectiveDate(DateUtils.getStringDateShort(new Date()));
			}
			criteria = example.createCriteria();
			criteria.andEqualTo("employeeNo",employeeTemporary.getEmployeeNo())
					.andEqualTo("identityNumber",employeeTemporary.getIdentityNumber())
					.andEqualTo(Contants.IS_DELETED_FIELD, "N")
					.andEqualTo("tmpEmployeeStatus","1");
			oldEntity = mapper.selectOneByExample(example);
			if(oldEntity!=null) {
				oldEntity.setTmpPositionSalary(employeeTemporary.getTmpPositionSalary());
				oldEntity.setEffectiveDate(employeeTemporary.getEffectiveDate());
				successNum += update(oldEntity,EmployeeTemporaryOpTypeEnum.SALARYADJUSTMENT);
			}
		}
		Map<String,Integer> map = new HashMap<>();
		map.put("successNum",successNum);
		map.put("errorNum",tmpEmpList.size() - successNum);
		return map;
	}

	/**
	 *
	 * @Title batchdimission
	 * @Description 批量离职
	 * @return Map
	 * @date 2024��10��8�� ����3:11:08
	 * <AUTHOR>
	 */
	@Transactional(readOnly = false)
	@Override
	public Integer batchdimission(List<String> ids,String dimissionDate,String effectiveDate,String reason){
		Example example = new Example(HrmsEmployeeTemporary.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andIn("id",ids)
				.andEqualTo(Contants.IS_DELETED_FIELD, "N")
				.andEqualTo("tmpEmployeeStatus","1");
		List<HrmsEmployeeTemporary> list = mapper.selectByExample(example);
		HrmsEmployeeTemporary employeeTemporary = new HrmsEmployeeTemporary();
		employeeTemporary.setTmpEmployeeStatus("2");
		employeeTemporary.setDimissionDate(dimissionDate);
		employeeTemporary.setEffectiveDate(effectiveDate);
		employeeTemporary.setDimissionReason(reason);
		employeeTemporary.setUpdateDate(new  Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			employeeTemporary.setUpdateUser(user.getUsercode());
			employeeTemporary.setUpdateUserName(user.getUsername());
		}
		//保存操作记录
		HrmsEmployeeTemporary newEntity = null;
		for(HrmsEmployeeTemporary entity : list){
			try {
				newEntity = new HrmsEmployeeTemporary();
				BeanUtils.copyProperties(newEntity,entity);
				newEntity.setTmpEmployeeStatus("2");
				newEntity.setDimissionDate(dimissionDate);
				newEntity.setDimissionReason(reason);
				newEntity.setEffectiveDate(effectiveDate);
				newEntity.setUpdateDate(employeeTemporary.getUpdateDate());
				newEntity.setUpdateUser(user.getUsercode());
				newEntity.setUpdateUserName(user.getUsername());
				employeeTemporaryChangeService.saveChanges(entity,newEntity,EmployeeTemporaryOpTypeEnum.BATCHDIMISSION);
			}catch (Exception e){}
		}
		return mapper.updateByExampleSelective(employeeTemporary,example);
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemName(),d.getItemNameValue());
			}
		}
		return map;
	}
}
