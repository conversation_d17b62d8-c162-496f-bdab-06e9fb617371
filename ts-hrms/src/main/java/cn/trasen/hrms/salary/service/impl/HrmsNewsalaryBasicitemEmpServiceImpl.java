package cn.trasen.hrms.salary.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.dao.HrmsAdvancementIncidentMapper;
import cn.trasen.hrms.dao.HrmsEmployeeMapper;
import cn.trasen.hrms.enums.IncidentAuditStatusEnum;
import cn.trasen.hrms.model.*;
import cn.trasen.hrms.salary.DTO.NewSalaryBasicItemBatchAdjustReq;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicColumnMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryBasicitemEmpMapper;
import cn.trasen.hrms.salary.dao.HrmsNewsalaryItemLibraryMapper;
import cn.trasen.hrms.salary.enums.CarryRuleEnum;
import cn.trasen.hrms.salary.model.*;
import cn.trasen.hrms.salary.service.*;
import cn.trasen.hrms.salary.utils.FormulaParse;
import cn.trasen.hrms.service.HrmsSalaryLevelWageService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.utils.IdUtil;
import com.google.common.collect.Maps;
import com.udojava.evalex.Expression;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @ClassName HrmsNewsalaryBasicitemEmpServiceImpl
 * @Description TODO
 * @date 2023��11��11�� ����4:32:36
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
//@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class HrmsNewsalaryBasicitemEmpServiceImpl implements HrmsNewsalaryBasicitemEmpService {

	@Resource
	private HrmsNewsalaryBasicitemEmpMapper mapper;

	@Resource
	private HrmsNewsalaryBasicColumnMapper basicMapper;

	@Autowired
	HrmsNewsalaryBasicitemEmpHistoryService hrmsNewsalaryBasicitemEmpHistoryService; // 定薪历史表

	@Resource
	DictItemFeignService dictItemFeignService;

	@Resource
	HrmsNewsalaryItemLibraryMapper itemLibraryMapper;

	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;
	@Autowired
	HrmsNewsalaryOptionEmpService hrmsNewsalaryOptionEmpService;
	@Autowired
	HrmsNewsalaryBasicitemEmpService hrmsNewsalaryBasicitemEmpService;
	@Autowired
	HrmsSalaryLevelWageService hrmsSalaryLevelWageService;

	@Autowired
	IHrmsNewSalaryItemLibraryService hrmsNewSalaryItemLibraryService;

	@Autowired
	HrmsNewsalaryChangesDetailedService detailedService;
	@Resource
	private HrmsAdvancementIncidentMapper incidentMapper;
	@Resource
	private HrmsEmployeeMapper employeeMapper;

	@Autowired
	private HrmsSalaryPolicyStandardService hrmsSalaryPolicyStandardService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(List<HrmsNewsalaryBasicitemEmp> records) {
		String employeeId = "";
		if (records != null && records.size() > 0) {
			employeeId = records.get(0).getEmployeeId();
			if (StringUtil.isEmpty(employeeId)) {
				throw new BusinessException("员工ID不能为空");
			}

			String effectiveDate = records.get(0).getEffectiveDate();
			if (StringUtil.isEmpty(effectiveDate)) {
				throw new BusinessException("生效日期为空");
			}
			Date date1 = DateUtil.parse(DateUtils.getStringDateShort(new Date())); // 当前日期
			Date date2 = DateUtil.parse(effectiveDate); // 生效日期
			int compare = DateUtil.compare(date1, date2, "yyyy-MM-dd");
			String plgw = "";
			String gwdj = "";
			String salary_level_type = "";
			String salary_level_id = "";
			boolean UpdateFlag = false; //因多批次定薪导入时，有些模板不带岗位与薪类型，所以找不到就会被重置为空；
			for (int i = 0; i < records.size(); i++) {
				// 处理 中文
				if ("plgw".equals(records.get(i).getEmpField())) {
					plgw = records.get(i).getEmpFieldValue();
					records.get(i)
							.setEmpFieldValueText(basicMapper.getPostCategoryType(records.get(i).getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
					UpdateFlag = true;
				} else if ("gwdj".equals(records.get(i).getEmpField())) {
					gwdj = records.get(i).getEmpFieldValue();
					records.get(i)
							.setEmpFieldValueText(basicMapper.getPostCategoryLevel(records.get(i).getEmpFieldValue()));
					UpdateFlag = true;
				} else if ("salary_level_type".equals(records.get(i).getEmpField())) {
					salary_level_type = records.get(i).getEmpFieldValue();
					UpdateFlag = true;
					records.get(i).setEmpFieldValueText(
							basicMapper.getSalaryLevelCategoryType(records.get(i).getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
				} else if ("salary_level_id".equals(records.get(i).getEmpField())) {
					salary_level_id = records.get(i).getEmpFieldValue();
					UpdateFlag = true;
					records.get(i).setEmpFieldValueText(
							basicMapper.getSalaryLevelCategoryLevel(records.get(i).getEmpFieldValue()));
				}else if ("policy_standard_id".equals(records.get(i).getEmpField())) {
                    String policyStandardId = records.get(i).getEmpFieldValue();
                    HrmsSalaryPolicyStandard policyStandard = hrmsSalaryPolicyStandardService.selectById(policyStandardId);
                    if(policyStandard!=null) {
                        records.get(i).setEmpFieldValueText(policyStandard.getPolicyStandardName());
                    }
                }
				records.get(i).setId(IdUtil.getId());
				records.get(i).setCreateDate(new Date());
				records.get(i).setUpdateDate(new Date());
				records.get(i).setIsDeleted("N");
				if (StrUtil.isBlank(records.get(i).getReason())) {
					records.get(i).setReason("入职定薪");
				}
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					records.get(i).setCreateUser(user.getUsercode());
					records.get(i).setCreateUserName(user.getUsername());
					records.get(i).setUpdateUser(user.getUsercode());
					records.get(i).setUpdateUserName(user.getUsername());
				}
				// 可以在此直接计算出金额，这样在计算时就不再需要进行规则判断，可以在调薪或者定薪时进行批量处理；
				if (!StringUtils.isAllBlank(records.get(i).getCustomRule())) {
					if ("1".equals(records.get(i).getEmpFieldValue())) { // 启用
						records.get(i).setSalaryAmount(FormulaParse.custRuleParse(records.get(i).getCustomRule()));
					}
					if ("2".equals(records.get(i).getEmpFieldValue())) { //禁用
						records.get(i).setSalaryAmount(new BigDecimal(0));
					}
				}
				if (compare < 0) { // 当前日期小于生效日期
					records.get(i).setDataStatus("0"); // 未生效
				} else if (compare == 0) {// 当前日期等于生效日期
					records.get(i).setDataStatus("1"); // 已生效
				} else if (compare > 0) {// 当前日期大于生效日期
					records.get(i).setDataStatus("1"); // 立马生效
				}
				if (compare >= 0) { // 需要生效的立马插入表及时表中
					// 先删除原来的定薪数据 重新保存
					Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
					Example.Criteria criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andEqualTo("employeeId", records.get(i).getEmployeeId());
					criteria.andEqualTo("basicItemId", records.get(i).getBasicItemId());
					mapper.deleteByExample(example); // 先删除原来的定薪数据
					mapper.insertSelective(records.get(i));
//					mapper.upsertSalaryBasicEmp(records.get(i));
					//将历史表的数据设置为已过期
					hrmsNewsalaryBasicitemEmpHistoryService.updateExpired(records.get(i).getEmployeeId(), records.get(i).getBasicItemId());
				}
			}
			if (compare >= 0) { // 需要生效的立马插入表及时表中
				mapper.updateSalaryAppoint(records.get(0).getEmployeeId()); // 设置为已定薪
				if (UpdateFlag && (StringUtils.isNotBlank(plgw) || StringUtils.isNotBlank(gwdj) || StringUtils.isNotBlank(salary_level_type) || StringUtils.isNotBlank(salary_level_id))) {
					mapper.updateEmployeeSalary(employeeId, plgw, gwdj, salary_level_type, salary_level_id); // 同步到人员档案
				}
			}
		}
		List<HrmsNewsalaryBasicitemEmpHistory> todoList = new ArrayList();
		copyList(records, todoList);
		// 将最新调薪数据放入历史表
		hrmsNewsalaryBasicitemEmpHistoryService.save(todoList);
		if (!StringUtil.isEmpty(employeeId)) {
			this.CalculatSingle(employeeId);
			//计算未生效的定薪数据
			this.CalculatSingle2Ineffective(employeeId,records.get(0).getEffectiveDate());
		}
		return records.size();
	}

	/**
	 * 调薪保存
	 *
	 * @param records
	 * @return
	 */
    @Transactional(readOnly = false)
	@Override
	public Integer adjustSave(List<HrmsNewsalaryBasicitemEmp> records) {
		if (records != null && records.size() > 0) {
			String employeeId = records.get(0).getEmployeeId();
			if (StringUtil.isEmpty(employeeId)) {
				throw new BusinessException("员工Id不能为空");
			}

			String effectiveDate = records.get(0).getEffectiveDate();
			if (StringUtil.isEmpty(effectiveDate)) {
				throw new BusinessException("生效日期为空");
			}
//			// 根据人员id 和定薪时间删除本月的数据
//			hrmsNewsalaryBasicitemEmpHistoryService.deleteByEmployeeIdAndDate(records.get(0).getEmployeeId(),
//					effectiveDate);
			Date date1 = DateUtil.parse(DateUtils.getStringDateShort(new Date())); // 当前日期
			Date date2 = DateUtil.parse(effectiveDate); // 生效日期
			int compare = DateUtil.compare(date1, date2, "yyyy-MM-dd");
			String plgw = "";
			String gwdj = "";
			String salary_level_type = "";
			String salary_level_id = "";

			boolean UpdateFlag = false;
			HrmsAdvancementIncidentEo entity = new HrmsAdvancementIncidentEo();
			for (int i = 0; i < records.size(); i++) {
				// 处理 中文
				if ("plgw".equals(records.get(i).getEmpField())) {
					plgw = records.get(i).getEmpFieldValue();
					records.get(i)
							.setEmpFieldValueText(basicMapper.getPostCategoryType(records.get(i).getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
					UpdateFlag = true;
				} else if ("gwdj".equals(records.get(i).getEmpField())) {
					gwdj = records.get(i).getEmpFieldValue();
					UpdateFlag = true;
					records.get(i)
							.setEmpFieldValueText(basicMapper.getPostCategoryLevel(records.get(i).getEmpFieldValue()));
				} else if ("salary_level_type".equals(records.get(i).getEmpField())) {
					salary_level_type = records.get(i).getEmpFieldValue();
					UpdateFlag = true;
					records.get(i).setEmpFieldValueText(
							basicMapper.getSalaryLevelCategoryType(records.get(i).getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
				} else if ("salary_level_id".equals(records.get(i).getEmpField())) {
					salary_level_id = records.get(i).getEmpFieldValue();
					UpdateFlag = true;
					records.get(i).setEmpFieldValueText(
							basicMapper.getSalaryLevelCategoryLevel(records.get(i).getEmpFieldValue()));
				}else if ("policy_standard_id".equals(records.get(i).getEmpField())) {
					String  policyStandardId = records.get(i).getEmpFieldValue();
                    HrmsSalaryPolicyStandard policyStandard = hrmsSalaryPolicyStandardService.selectById(policyStandardId);
                    if(policyStandard!=null) {
                        records.get(i).setEmpFieldValueText(policyStandard.getPolicyStandardName());
                    }
				}
				records.get(i).setId(IdUtil.getId());
				records.get(i).setCreateDate(new Date());
				records.get(i).setUpdateDate(new Date());
				records.get(i).setIsDeleted("N");
				ThpsUser user = UserInfoHolder.getCurrentUserInfo();
				if (user != null) {
					records.get(i).setCreateUser(user.getUsercode());
					records.get(i).setCreateUserName(user.getUsername());
					records.get(i).setUpdateUser(user.getUsercode());
					records.get(i).setUpdateUserName(user.getUsername());
				}
				// 可以在此直接计算出金额，这样在计算时就不再需要进行规则判断，可以在调薪或者定薪时进行批量处理；
				if (!StringUtils.isAllBlank(records.get(i).getCustomRule())) {
					if ("1".equals(records.get(i).getEmpFieldValue())) { //启用
						records.get(i).setSalaryAmount(FormulaParse.custRuleParse(records.get(i).getCustomRule()));
					}
					if ("2".equals(records.get(i).getEmpFieldValue())) { //禁用
						records.get(i).setSalaryAmount(new BigDecimal(0));
					}
				}
				if (compare < 0) { // 当前日期小于生效日期
					records.get(i).setDataStatus("0"); // 未生效
				} else if (compare == 0) {// 当前日期等于生效日期
					records.get(i).setDataStatus("1"); // 已生效
				} else if (compare > 0) {// 当前日期大于生效日期
					records.get(i).setDataStatus("1"); // 立马生效
				}
				HrmsNewsalaryBasicitemEmp hrmsNewsalaryBasicitemEmp = null;
				if (Objects.nonNull(records.get(i).getBasicItemId())){
					Example example1 = new Example(HrmsNewsalaryBasicitemEmp.class);
					Example.Criteria criteria1 = example1.createCriteria();
					criteria1.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria1.andEqualTo("employeeId", records.get(i).getEmployeeId());
					criteria1.andEqualTo("basicItemId", records.get(i).getBasicItemId());
					hrmsNewsalaryBasicitemEmp = mapper.selectOneByExample(example1);
				}
				if (compare >= 0) { // 需要生效的立马插入表及时表中
					// 先删除原来的定薪数据 重新保存
					Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
					Example.Criteria criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andEqualTo("employeeId", records.get(i).getEmployeeId());
					criteria.andEqualTo("basicItemId", records.get(i).getBasicItemId());
					mapper.deleteByExample(example); // 先删除原来的定薪数据
					mapper.insertSelective(records.get(i));
					//将历史表的数据设置为已过期
					hrmsNewsalaryBasicitemEmpHistoryService.updateExpired(records.get(i).getEmployeeId(), records.get(i).getBasicItemId());
				}
				if (Objects.nonNull(hrmsNewsalaryBasicitemEmp)){
					//调薪的薪酬项目保存到异动表
					boolean b = Objects.nonNull(hrmsNewsalaryBasicitemEmp.getBasicItemId()) && Objects.equals(hrmsNewsalaryBasicitemEmp.getBasicItemId(), records.get(i).getBasicItemId());
					//不包含岗位等级薪级类别
					String context = "plgw,salary_level_type,gwdj,salary_level_id";
					boolean contains = context.contains(hrmsNewsalaryBasicitemEmp.getEmpField());
					HrmsEmployee detailById = employeeMapper.findDetailById(hrmsNewsalaryBasicitemEmp.getEmployeeId());
					Example example = new Example(HrmsNewsalaryBasicColumn.class);
					Example.Criteria criteria = example.createCriteria();
					criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
					criteria.andNotEqualTo("basicItemType", 1);
					example.setOrderByClause(" number_sort ");
					List<HrmsNewsalaryBasicColumn> hrmsNewsalaryBasicColumns = basicMapper.selectByExample(example);
					List<String> collect = hrmsNewsalaryBasicColumns.stream().map(HrmsNewsalaryBasicColumn::getEmpField).collect(Collectors.toList());
					BigDecimal bigDecimal = Objects.nonNull(hrmsNewsalaryBasicitemEmp.getSalaryAmount()) ? hrmsNewsalaryBasicitemEmp.getSalaryAmount() : new BigDecimal(0);
					BigDecimal salaryAmount = Objects.nonNull(records.get(i).getSalaryAmount()) ? records.get(i).getSalaryAmount() : new BigDecimal(0);
//					if (b && collect.contains(records.get(i).getEmpField())
//							&& bigDecimal.compareTo(salaryAmount) != 0 && !contains) {
					//所有调整项都添加到薪酬异动记录 去除&& !contains的限制
					if (b && collect.contains(records.get(i).getEmpField())
							&& bigDecimal.compareTo(salaryAmount) != 0) {
						HrmsNewsalaryChangesDetailedEo hrmsNewsalaryChangesDetailedEo = new HrmsNewsalaryChangesDetailedEo();
						hrmsNewsalaryChangesDetailedEo.setEmployeeId(hrmsNewsalaryBasicitemEmp.getEmployeeId());
						if(null == hrmsNewsalaryBasicitemEmp.getSalaryAmount()){
							hrmsNewsalaryChangesDetailedEo.setAdjustValue("0");
						}else {
							hrmsNewsalaryChangesDetailedEo.setAdjustValue(hrmsNewsalaryBasicitemEmp.getSalaryAmount().toString());
						}
						if (null == records.get(i).getSalaryAmount()) {
							hrmsNewsalaryChangesDetailedEo.setNowValue("0");
						}else {
							hrmsNewsalaryChangesDetailedEo.setNowValue(records.get(i).getSalaryAmount().toString());
						}
						hrmsNewsalaryChangesDetailedEo.setAbnormalItems(records.get(i).getBasicItemId());
						hrmsNewsalaryChangesDetailedEo.setSalaryCategory(3);
						hrmsNewsalaryChangesDetailedEo.setId(IdUtil.getId());
						hrmsNewsalaryChangesDetailedEo.setCreateDate(new Date());
						if(user!= null) {
							hrmsNewsalaryChangesDetailedEo.setCreateUser(user.getUsercode());
							hrmsNewsalaryChangesDetailedEo.setCreateUserName(user.getUsername());
							hrmsNewsalaryChangesDetailedEo.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						}else{
							hrmsNewsalaryChangesDetailedEo.setCreateUser(records.get(i).getCreateUser());
							hrmsNewsalaryChangesDetailedEo.setCreateUserName(records.get(i).getCreateUserName());
							hrmsNewsalaryChangesDetailedEo.setSsoOrgCode(records.get(i).getSsoOrgCode());
						}
						hrmsNewsalaryChangesDetailedEo.setOrgName(detailedService.queryOrgName(hrmsNewsalaryBasicitemEmp.getEmployeeId()));
						hrmsNewsalaryChangesDetailedEo.setEffectiveDate(records.get(i).getEffectiveDate());
						hrmsNewsalaryChangesDetailedEo.setReason(records.get(i).getReason());
						hrmsNewsalaryChangesDetailedEo.setEmployeeName(detailById.getEmployeeName());
						hrmsNewsalaryChangesDetailedEo.setEmployeeNo(detailById.getEmployeeNo());
						hrmsNewsalaryChangesDetailedEo.setSourceType(2);
						hrmsNewsalaryChangesDetailedEo.setRemark(records.get(i).getRemark());
						hrmsNewsalaryChangesDetailedEo.setIsDeleted(Contants.IS_DELETED_FALSE);
						detailedService.save(hrmsNewsalaryChangesDetailedEo);
					}
					//岗位工资岗位等级薪级等级保存到异动表
					if (contains) {
						entity.setEffectiveDate(records.get(i).getEffectiveDate());
						entity.setEmployeeId(hrmsNewsalaryBasicitemEmp.getEmployeeId());
						entity.setEmployeeName(detailById.getEmployeeName());
						entity.setEmployeeNo(detailById.getEmployeeNo());
						entity.setJobDeionTypeTime(Objects.isNull(records.get(i).getJobDeionTypeTime()) ? DateUtils.getStringDateShortYM(new Date()):records.get(i).getJobDeionTypeTime());
						if (Objects.nonNull(hrmsNewsalaryBasicitemEmp)
								&& Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpField(), "plgw")
								&& !Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpFieldValue(), records.get(i).getEmpFieldValue())) {
							entity.setOldPlgw(hrmsNewsalaryBasicitemEmp.getEmpFieldValue());
							entity.setNewPlgw(records.get(i).getEmpFieldValue());
							entity.setType(1);
						} else if (Objects.nonNull(hrmsNewsalaryBasicitemEmp)
								&& Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpField(), "salary_level_type")
								&& !Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpFieldValue(), records.get(i).getEmpFieldValue())) {
							entity.setOldSalaryLevelType(hrmsNewsalaryBasicitemEmp.getEmpFieldValue());
							entity.setNewSalaryLevelType(records.get(i).getEmpFieldValue());
							entity.setType(1);
						} else if (Objects.nonNull(hrmsNewsalaryBasicitemEmp)
								&& Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpField(), "gwdj")
								&& !Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpFieldValue(), records.get(i).getEmpFieldValue())) {
							entity.setOldGwdj(hrmsNewsalaryBasicitemEmp.getEmpFieldValue());
							entity.setNewGwdj(records.get(i).getEmpFieldValue());
							entity.setType(1);
						} else if (Objects.nonNull(hrmsNewsalaryBasicitemEmp)
								&& Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpField(), "salary_level_id")
								&& !Objects.equals(hrmsNewsalaryBasicitemEmp.getEmpFieldValue(), records.get(i).getEmpFieldValue())) {
							entity.setOldSalaryLevelId(hrmsNewsalaryBasicitemEmp.getEmpFieldValue());
							entity.setNewSalaryLevelId(records.get(i).getEmpFieldValue());
							entity.setType(1);
						}

						entity.setApprovalStatus(Integer.valueOf(IncidentAuditStatusEnum.AUDIT_STATUS_4.getKey()));
						entity.setIsDeleted(Contants.IS_DELETED_FALSE);
						entity.setCreateDate(new Date());
						entity.setId(IdUtil.getId());
						entity.setSourceType(2);
						entity.setReason(records.get(i).getReason());
						if(user!= null) {
							entity.setCreateUser(UserInfoHolder.getCurrentUserCode());
							entity.setCreateUserName(UserInfoHolder.getCurrentUserName());
							entity.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
						}else{
							entity.setCreateUser(records.get(i).getCreateUser());
							entity.setCreateUserName(records.get(i).getCreateUserName());
							entity.setSsoOrgCode(records.get(i).getSsoOrgCode());
						}
					}
				}
			}
			// 老数据放到历史表
			if (compare >= 0) { // 需要生效的立马插入表及时表中
				// TODO 更新员工档案岗位类别岗位等级 薪级类别 薪级等级
				if (UpdateFlag && (StringUtils.isNotBlank(plgw) || StringUtils.isNotBlank(gwdj) || StringUtils.isNotBlank(salary_level_type) || StringUtils.isNotBlank(salary_level_id))) {
					mapper.updateEmployeeSalary(employeeId, plgw, gwdj, salary_level_type, salary_level_id); // 同步到人员档案
				}
			}
			if (Objects.nonNull(entity) && Objects.nonNull(entity.getId()) && Objects.equals(entity.getType(), 1)) {
				incidentMapper.insert(entity);
			}

			List<HrmsNewsalaryBasicitemEmpHistory> todoList = new ArrayList();
			copyList(records, todoList);
			hrmsNewsalaryBasicitemEmpHistoryService.save(todoList);
			this.CalculatSingle(employeeId);
			//计算未生效的定薪数据
			this.CalculatSingle2Ineffective(employeeId,effectiveDate);
			return records.size();
		}
		return 1;
	}

    /**
     * 批量调薪
     * @param record
     */
    @Transactional(readOnly = false)
    @Override
    public String batchAdjustSave(NewSalaryBasicItemBatchAdjustReq record){
        Assert.notNull(record,"调薪内容不能为空");
        Assert.hasText(record.getEffectiveDate(),"生效时间不能为空");
        Assert.notNull(record.getEmployeeIds(),"批量调薪的员工不能为空");
        if(StringUtils.isEmpty(record.getPolicyStandardId()) && CollUtil.isEmpty(record.getBasicColumnsList())){
            throw new BusinessException("批量调薪项不能为空");
        }
        ThpsUser user = UserInfoHolder.getCurrentUserInfo();
        StringBuffer errorMsg = new StringBuffer();
        //循环员工id
        List<HrmsNewsalaryBasicitemEmp> itemList = null;
        List<String> adjustItem = Arrays.asList(new String[]{"plgw","gwdj","salary_level_type","salary_level_id"});
        List<String> customRule = Arrays.asList(new String[]{"a01","a02","a03","a04"});
        List<Map<String,String>> empBasicItemList = new ArrayList<>();
		HrmsSalaryPolicyStandard policyStandard = null;
        if(StringUtils.isNotBlank(record.getPolicyStandardId())) {
        	policyStandard = hrmsSalaryPolicyStandardService.selectById(record.getPolicyStandardId());
		}
		List<HrmsEmployee> employees = employeeMapper.findDetailByIds(record.getEmployeeIds());
		List<HrmsEmployee> tempEmps = null;
        for(String empId : record.getEmployeeIds()){
			tempEmps =  employees.stream().filter(entity-> empId.equals(entity.getEmployeeId())).collect(Collectors.toList());
            if(CollUtil.isEmpty(tempEmps)){
                throw new BusinessException("未找到员工信息");
            }
			HrmsEmployee employee = tempEmps.get(0);
            // 取出人员所有定薪项数据
            List<HrmsNewsalaryBasicitemEmp> emp = mapper.getDataByEmployeeId(empId);
            if (emp.size() > 0) { // 表示已经定过薪
                //政策标准调整
               if(StringUtils.isNotEmpty(record.getPolicyStandardId())){
                   if(policyStandard == null){
                       throw new BusinessException("未找到政策标准数据");
                   }
                   Map<String,String> empBasicItem = new HashMap<>();
                   emp.stream().forEach(vo->{
                       if(adjustItem.contains(vo.getEmpField())){
                           empBasicItem.put(vo.getBasicItemName(),vo.getEmpFieldValueText());
                       }
                       if("2".equals(vo.getBasicItemType())  && StringUtils.isNotBlank(vo.getCustomRule()) && customRule.contains(vo.getCustomRule().toLowerCase())){
                           empBasicItem.put(vo.getBasicItemName(),null);
                           empBasicItem.put(vo.getBasicItemName()+"-百分比",vo.getEmpFieldValue());
                       }
                   });
				   empBasicItem.put("工号",employee.getEmployeeNo());
				   empBasicItem.put("姓名",employee.getEmployeeName());
                   empBasicItem.put("生效日期",record.getEffectiveDate());
                   empBasicItem.put("定调薪原因",record.getReason());
                   empBasicItem.put("政策标准",policyStandard.getPolicyStandardName());
                   if (user != null) {
                       empBasicItem.put("操作人",user.getUsername());
                       empBasicItem.put("医院编码",UserInfoHolder.getCurrentUserCorpCode());
                   }
                   if(CollUtil.isEmpty(empBasicItemList) || empBasicItemList.size() < 1){
					   Map<String,String> basicItemNameParam = new HashMap<>();
					   empBasicItem.keySet().forEach(key->{
						   basicItemNameParam.put(key,key);
					   });
					   empBasicItemList.add(basicItemNameParam);
				   }
                   empBasicItemList.add(empBasicItem);
               }else if(CollUtil.isNotEmpty(record.getBasicColumnsList())){ //薪酬项普调
                   Map<String,String> empBasicItem = new HashMap<>();
                   record.getBasicColumnsList().forEach(item->{
                      List<HrmsNewsalaryBasicitemEmp> list = emp.stream().filter(vo-> vo.getEmpField().equals(item.getEmpField())).collect(Collectors.toList());
                      if(CollUtil.isNotEmpty(list)) {
                          HrmsNewsalaryBasicitemEmp empItem = list.get(0);
                          if (empItem != null) {
                              empBasicItem.put(empItem.getBasicItemName(), empItem.getSalaryAmount().add(Convert.toBigDecimal(item.getSalaryAmount())).toString());
                              empBasicItem.put(empItem.getBasicItemName() + "-百分比", empItem.getEmpFieldValue());
                          }
                      }
                   });
				   empBasicItem.put("工号",employee.getEmployeeNo());
				   empBasicItem.put("姓名",employee.getEmployeeName());
                   empBasicItem.put("生效日期",record.getEffectiveDate());
                   empBasicItem.put("定调薪原因",record.getReason());
                   if (user != null) {
                       empBasicItem.put("操作人编码",user.getUsercode());
                       empBasicItem.put("操作人",user.getUsername());
                       empBasicItem.put("医院编码",UserInfoHolder.getCurrentUserCorpCode());
                   }
                   //使用现有政策标准
				   List<HrmsNewsalaryBasicitemEmp> list = emp.stream().filter(vo-> "policy_standard_id".equals(vo.getEmpField())).collect(Collectors.toList());
				   if(CollUtil.isNotEmpty(list)) {
					   HrmsNewsalaryBasicitemEmp empItem = list.get(0);
					   empBasicItem.put("政策标准",empItem.getEmpFieldValueText());
				   }else{
					   errorMsg.append(employee.getEmployeeName()+"["+employee.getEmployeeNo()+"]未设置政策标准，无法调薪;");
					   continue;
				   }
				   if(CollUtil.isEmpty(empBasicItemList) || empBasicItemList.size() < 1){
					   Map<String,String> basicItemNameParam = new HashMap<>();
					   empBasicItem.keySet().forEach(key->{
						   basicItemNameParam.put(key,key);
					   });
					   empBasicItemList.add(basicItemNameParam);
				   }
                   empBasicItemList.add(empBasicItem);
               }
            } else {
                errorMsg.append(employee.getEmployeeName()+"["+employee.getEmployeeNo()+"]未定薪;");
            }
        }
        if(StringUtils.isBlank(errorMsg)) {
        	if(CollUtil.isNotEmpty(empBasicItemList)) {
				new Thread(() -> {
					importEntryTemplateData(empBasicItemList,false);
				}).start();
//        		if(empBasicItemList.size()>100){
//        			Map<String,String> titileMap = empBasicItemList.get(0);
//					List<Map<String,String>> newBasicItemList = new ArrayList<>();
//					newBasicItemList.addAll(empBasicItemList.subList(0,empBasicItemList.size()/2));
//					List<Map<String,String>> newBasicItemList1 = new ArrayList<>();
//					newBasicItemList1.add(0,titileMap);
//					newBasicItemList1.addAll(empBasicItemList.subList(empBasicItemList.size()/2,empBasicItemList.size()));
//					ExecutorService executor = Executors.newFixedThreadPool(2); // 创建固定大小的线程池
//					executor.execute(() -> {
//						hrmsNewsalaryBasicitemEmpService.importEntryTemplateData(newBasicItemList);
//					});
//					executor.execute(() -> {
//						hrmsNewsalaryBasicitemEmpService.importEntryTemplateData(newBasicItemList1);
//					});
//					try {
//						Thread.sleep(60 * 1000L);
//					}catch (Exception e){}
//				}else{
//					new Thread(() -> {
//						hrmsNewsalaryBasicitemEmpService.importEntryTemplateData(empBasicItemList);
//					}).start();
//				}
			}
		}
        return errorMsg.toString();
    }

	/**
	 * 定时任务定薪调薪生效
	 */
	@Transactional(readOnly = false)
	@Override
	public void taskAdjustSalary() {

		// String takeEffect = "2024-03-01"; //生效日期
		String takeEffect = DateUtils.getStringDateShortYM(new Date()) + "-01";

		try {
			// 先查询出 当前日期要生效的人员id
			List<HrmsNewsalaryBasicitemEmpHistory> empHistoryList = hrmsNewsalaryBasicitemEmpHistoryService
					.getEmpTakeEffect(takeEffect);

			Map<String, List<HrmsNewsalaryBasicitemEmpHistory>> empHistoryMap = empHistoryList.stream()
					.collect(Collectors.groupingBy(HrmsNewsalaryBasicitemEmpHistory::getEmployeeId));

			empHistoryMap.forEach((employeeId, historyList) -> {
				List<String> basicItemIds = historyList.stream().map(HrmsNewsalaryBasicitemEmpHistory::getBasicItemId).distinct().collect(Collectors.toList());
				// 根据人员id把实时表中的数据干掉
				deleteByEmployeeId(employeeId,basicItemIds);

				String plgw = "";
				String gwdj = "";
				String salary_level_type = "";
				String salary_level_id = "";
				boolean UpdateFlag = false;
				// 实时表中插入新的生效数据
				for (int i = 0; i < historyList.size(); i++) {
					HrmsNewsalaryBasicitemEmp newBean = new HrmsNewsalaryBasicitemEmp();
					BeanUtils.copyProperties(historyList.get(i), newBean);
					if ("plgw".equals(newBean.getEmpField())) {
						plgw = newBean.getEmpFieldValue();
						newBean.setEmpFieldValueText(basicMapper.getPostCategoryType(newBean.getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
						UpdateFlag = true;
					} else if ("gwdj".equals(newBean.getEmpField())) {
						gwdj = newBean.getEmpFieldValue();
						newBean.setEmpFieldValueText(basicMapper.getPostCategoryLevel(newBean.getEmpFieldValue()));
						UpdateFlag = true;
					} else if ("salary_level_type".equals(newBean.getEmpField())) {
						salary_level_type = newBean.getEmpFieldValue();
						UpdateFlag = true;
						newBean.setEmpFieldValueText(
								basicMapper.getSalaryLevelCategoryType(newBean.getEmpFieldValue(), UserInfoHolder.getCurrentUserCorpCode()));
					} else if ("salary_level_id".equals(newBean.getEmpField())) {
						salary_level_id = newBean.getEmpFieldValue();
						UpdateFlag = true;
						newBean.setEmpFieldValueText(
								basicMapper.getSalaryLevelCategoryLevel(newBean.getEmpFieldValue()));
					}
					mapper.insertSelective(newBean);
					// 把历史表中生效中的数据改为 已过期
					hrmsNewsalaryBasicitemEmpHistoryService.updateExpired(employeeId,historyList.get(i).getBasicItemId());
				}
				if (UpdateFlag) {
					mapper.updateEmployeeSalary(employeeId, plgw, gwdj, salary_level_type, salary_level_id); // 同步到人员档案
				}

				// 把当月要生效的数据改为生效中
				hrmsNewsalaryBasicitemEmpHistoryService.updateSetTakeEffect(historyList);
				//重新计算员工的定薪调薪数据 add ni.jiang
				this.CalculatSingle(employeeId);
			});

		} catch (Exception e) {
			throw new RuntimeException("自动生效定薪失败" + e.getMessage(), e);
		}
	}

	private Integer deleteByEmployeeId(String employeeId,List<String> basicItemIds) {
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);
		criteria.andIn("basicItemId",basicItemIds);
		return mapper.deleteByExample(example);
	}

    @Transactional(readOnly = false)
	@Override
	public Integer update(List<HrmsNewsalaryBasicitemEmp> records) {

		// 根据人员id查询记录
		List<HrmsNewsalaryBasicitemEmp> dataList = getDataList(records.get(0));
		List<HrmsNewsalaryBasicitemEmpHistory> todoList = new ArrayList();
		copyList(dataList, todoList);
		// 老数据放到历史表
		hrmsNewsalaryBasicitemEmpHistoryService.save(todoList);

		// 删除之后重新保存
		HrmsNewsalaryBasicitemEmp del = new HrmsNewsalaryBasicitemEmp();
		del.setEmployeeId(records.get(0).getEmployeeId());
		mapper.delete(del);
		save(records);
		return records.size();
	}

	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		HrmsNewsalaryBasicitemEmp record = new HrmsNewsalaryBasicitemEmp();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public HrmsNewsalaryBasicitemEmp selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<HrmsNewsalaryBasicitemEmp> getDataByEmployeeId(String employeeId) {
		Assert.hasText(employeeId, "员工id不能为空.");
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andEqualTo("employeeId", employeeId);

		List<HrmsNewsalaryBasicitemEmp> list = mapper.getDataByEmployeeId(employeeId);
		// 处理四个字段
		Map<String, String> postCategoryDictMap = convertDictMap("post_category"); // 岗位类别
		Map<String, String> salaryLevelCategoryDictMap = convertDictMap("salary_level_category"); // 薪级类别

		if (list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				if ("plgw".equals(list.get(i).getEmpField())) { // 岗位类别
					list.get(i).setEmpFieldText(postCategoryDictMap.get(list.get(i).getEmpFieldValue()));
				}
				if ("salary_level_type".equals(list.get(i).getEmpField())) { // 薪级类别
					list.get(i).setEmpFieldText(salaryLevelCategoryDictMap.get(list.get(i).getEmpFieldValue()));
				}
				// 处理岗位等级
				if ("gwdj".equals(list.get(i).getEmpField())) { // 薪级类别
					String _text = mapper.getGwdjText(list.get(i).getEmpFieldValue());
					list.get(i).setEmpFieldText(_text);
				}
				// 处理薪级等级
				if ("salary_level_id".equals(list.get(i).getEmpField())) { // 薪级类别
					String _text = mapper.getSalaryLeavelText(list.get(i).getEmpFieldValue());
					list.get(i).setEmpFieldText(_text);
				}
			}
		}
		return list;
	}

	@Override
	public DataSet<HrmsNewsalaryBasicitemEmp> getDataSetList(Page page, HrmsNewsalaryBasicitemEmp record) {
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<HrmsNewsalaryBasicitemEmp> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	private List<HrmsNewsalaryBasicitemEmp> getDataList(HrmsNewsalaryBasicitemEmp record) {
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if (!StringUtil.isEmpty(record.getEmployeeId())) {
			criteria.andEqualTo("employeeId", record.getEmployeeId());
		}
		List<HrmsNewsalaryBasicitemEmp> records = mapper.selectByExample(example);
		//

		return records;
	}

	// 复制ListBean
	private void copyList(List<HrmsNewsalaryBasicitemEmp> oldList, List<HrmsNewsalaryBasicitemEmpHistory> toDoList) {
		oldList.forEach(stu -> {
			HrmsNewsalaryBasicitemEmpHistory todoBean = new HrmsNewsalaryBasicitemEmpHistory();
			BeanUtils.copyProperties(stu, todoBean);
			toDoList.add(todoBean);
		});
	}

	@Override
	public Map<String, Object> getBaseData(String id) {
		Map<String, String> baseData = mapper.getBaseData(id, UserInfoHolder.getCurrentUserCorpCode());
		// 查询最近调薪日期
		HrmsNewsalaryBasicitemEmpHistory _bean = hrmsNewsalaryBasicitemEmpHistoryService.getFinallYByEmployeeId(id);
		// 查询人员所属薪酬组
		HrmsNewsalaryOptionEmp option = hrmsNewsalaryOptionEmpService.getEmpByOption(id);

		Map<String, Object> retMap = new LinkedHashMap<>();
		retMap.put("姓名", baseData.get("employee_name"));
		retMap.put("工号", baseData.get("employee_no"));
		retMap.put("性别", baseData.get("gender"));
		retMap.put("年龄", baseData.get("emp_age"));
		retMap.put("学历", baseData.get("educationTypeName"));
		retMap.put("身份证号", baseData.get("identity_number"));
		retMap.put("部门", baseData.get("orgName"));
		retMap.put("岗位", baseData.get("personalIdentityName"));
		retMap.put("职务", baseData.get("positionName"));
		retMap.put("编制类型", baseData.get("establishmentTypeName"));
		retMap.put("员工状态", baseData.get("employeeStatusName"));
		retMap.put("工龄", baseData.get("year_work"));
		retMap.put("入职日期", baseData.get("entry_date"));
		retMap.put("转正日期", baseData.get("positive_time"));
		retMap.put("离退休日期", baseData.get("retirement_time"));
		retMap.put("最近调薪日期", _bean.getCreateDate());
		if (ObjectUtil.isNotEmpty(option)) {
			retMap.put("薪酬组", option.getOptionName());
		}
		retMap.put("开户行", baseData.get("bankcardname"));
		retMap.put("银行卡号", baseData.get("bankcardno"));
		return retMap;
	}

	@Override
	public List<HrmsNewsalaryBasicColumn> getAllData(HrmsNewsalaryBasicColumn record) {

		Map<String, String> employeeStatusDictMap = convertDictMap("employee_status"); // 员工状态
		Map<String, String> personalIdentityDictMap = convertDictMap("personal_identity"); // 岗位名称
		Map<String, String> establishmentTypeDictMap = convertDictMap("establishment_type"); // 编制类型

//		Example example = new Example(HrmsNewsalaryBasicColumn.class);
//		Example.Criteria criteria = example.createCriteria();
//		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
//		example.setOrderByClause(" number_sort asc");
//		List<HrmsNewsalaryBasicColumn> records = basicMapper.selectByExample(example);
		List<HrmsNewsalaryBasicColumn> records = basicMapper.selectBasColumn();
		// 查询人员的基本信息
		// Map<String,String> employeeMap =
		// basicMapper.getEmployeeBase(record.getEmployeeId());
		Map<String, String> employeeMap = mapper.getBaseData(record.getEmployeeId(), UserInfoHolder.getCurrentUserCorpCode());

		HrmsNewsalaryOptionEmp option = hrmsNewsalaryOptionEmpService.getEmpByOption(record.getEmployeeId());

		if (records != null && records.size() > 0) {
			for (int i = 0; i < records.size(); i++) {
				if ("1".equals(records.get(i).getBasicItemType())) {
					if ("employee_status".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(
								employeeStatusDictMap.get(employeeMap.get(records.get(i).getEmpField())));
					}
					if ("personal_identity".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(
								personalIdentityDictMap.get(employeeMap.get(records.get(i).getEmpField())));
					}
					if ("establishment_type".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(
								establishmentTypeDictMap.get(employeeMap.get(records.get(i).getEmpField())));
					}
					if ("employee_name".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("employee_no".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("org_name".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("option_name".equals(records.get(i).getEmpField())) {
						if (null != option) {
							records.get(i).setBasicItemValue(option.getOptionName());
						}
					}
					if ("entry_date".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("positive_time".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("retirement_time".equals(records.get(i).getEmpField())) {
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
					if ("identity_number".equals(records.get(i).getEmpField())) { // 身份证号码
						records.get(i).setBasicItemValue(employeeMap.get(records.get(i).getEmpField()));
					}
				}
				if(StringUtils.isEmpty(records.get(i).getSalaryAmount())){
					records.get(i).setSalaryAmount("0.00");
				}
			}
		}
		return records;
	}

	@Override
	public List<HrmsNewsalaryBasicitemEmp> getBasicSalaryByEmpIds(List<String> empIds) {
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		criteria.andIn("employeeId", empIds);
		return mapper.selectByExample(example);
	}

	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}

	private Map<String, String> convertReversalDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemName(), d.getItemNameValue());
			}
		}
		return map;
	}

	// 批量导入员工薪酬项数据的方法
    @Transactional(readOnly = false)
	@Override
	public Integer importEntryTemplateData(List<Map<String, String>> data,boolean isSalaryAppoint) {
		Integer saveSize = 0;
		Map<String, String> salaryLevelCategoryDictMap = convertReversalDictMap("salary_level_category"); // 获取薪级类别 通过字典
		Map<String, String> postCategoryDictMap = convertReversalDictMap("post_category"); // 获取岗位类别
		// 导入前对基础资料进行缓存
		List<HrmsPostWageVo> hpw = mapper.selectPostWage(); // 岗位及工资缓存
		List<HrmsSalaryLevelWageVo> hslw = mapper.selectSalaryLevelWage(); // 薪资等级及工资缓存
		Map<String, String> policyStandardMap = convertPolicyStandardMap(); //获取政策标准列表
		List<String> empIds = new ArrayList<>();
		//先确定本次定调薪导入的项次有哪些内容，如果不在此批导入的项目中，则不做处理
		List<String> items = new ArrayList<>(data.get(0).values());
		for (int i = 1; i < data.size(); i++) { // 遍历每一个人
			boolean isSalary = false;
			String employeeNo = data.get(i).get("工号").trim();
			// 拿到人员的基本信息 根据工号
			Map<String, String> baseData = mapper.getBaseDataByEmpNo(employeeNo, UserInfoHolder.getCurrentUserCorpCode());
			if (MapUtil.isEmpty(baseData)) {
				throw new BusinessException(employeeNo + "员工信息不存在!");
			}
			if (!StrUtil.equals(MapUtil.getStr(baseData, "employee_name"), data.get(i).get("姓名"))) {
				throw new BusinessException(employeeNo + "姓名不正确!");
			}
			HrmsNewsalaryBasicColumn record = new HrmsNewsalaryBasicColumn();
			record.setEmployeeId(baseData.get("employee_id"));
			List<HrmsNewsalaryBasicitemEmp> saveList = new ArrayList<>();
			// 定调薪原因
			String reason = data.get(i).get("定调薪原因");
			if (StrUtil.isBlank(reason)) {
				throw new BusinessException(employeeNo + "定调薪原因为空!");
			}
			// 生效日期
			String effectiveDate = data.get(i).get("生效日期");
			if (StrUtil.isBlank(effectiveDate)) {
				throw new BusinessException(employeeNo + "生效日期为空!");
			}
			// 政策标准
            String policyStandardName = data.get(i).get("政策标准");
			if (StrUtil.isBlank(policyStandardName)) {
				throw new BusinessException(employeeNo + "政策标准不能为空!");
			}
			if(StrUtil.isNotBlank(policyStandardName) && !policyStandardMap.containsKey(policyStandardName.trim())){
				throw new BusinessException(employeeNo + "政策标准不能为空或错误!");
			}
			String  policyStandar = policyStandardName.trim();
			String dateFormat = "yyyy-MM-dd";
			Boolean checkDateFormat = this.checkDateFormat(effectiveDate, dateFormat);
			if (checkDateFormat) {
				throw new BusinessException(employeeNo + "生效日期格式错误!");
			}
			int idx = i;
			// 拿到 岗位类别名称，然后通过岗位类别名称去取对应岗位类别对应工资；
			String plgwText = data.get(idx).get("岗位类别");
			String gwdjText = data.get(idx).get("岗位等级");
			// 拿到薪级类别
			String salaryLevelTypeText = data.get(idx).get("薪级类别");
			String salaryLevelIdText = data.get(idx).get("薪级等级");
			String gwdjCode = "";
			String plgwCode = postCategoryDictMap.get(plgwText);
			String salaryLevelTypeCode = salaryLevelCategoryDictMap.get(salaryLevelTypeText);
			String salaryLevelId = "";
			String gwSalary = "0"; // 岗位工资
			String jxSalary = "0"; // 绩效工资
			String jljxSalary = "0"; // 激励性绩效
			String xjSalary = "0"; // 薪等工资
			if ((StringUtils.isNoneBlank(plgwCode)) && (StringUtils.isNoneBlank(gwdjText))) {
				Optional<HrmsPostWageVo> hpwv = hpw.stream()
						.filter(vo -> vo.getPostCategory().equals(plgwCode) && vo.getPostName().equals(gwdjText) && vo.getPolicyStandardId().equals(Convert.toStr(policyStandardMap.get(policyStandar))))
						.findFirst();
				if (hpwv.isPresent()) {
					// 取对应的岗位工资，绩效工资，奖励性绩效工资
					gwdjCode = hpwv.get().getPostId();
					if (null != hpwv.get().getPostWage()) {
						gwSalary = hpwv.get().getPostWage().toString(); //
					}
					if (null != hpwv.get().getPerformanceWage()) {
						jxSalary = hpwv.get().getPerformanceWage().toString();
					}
					if (null != hpwv.get().getAwardWage()) {
						jljxSalary = hpwv.get().getAwardWage().toString();
					}
				}
			}
			if ((StringUtils.isNoneBlank(salaryLevelTypeCode)) && StringUtils.isNoneBlank(salaryLevelIdText)) {
				Optional<HrmsSalaryLevelWageVo> hslwv = hslw.stream()
						.filter(vo -> vo.getSalaryLevelCategory().equals(salaryLevelTypeCode)
								&& vo.getSalaryLevelName().equals(salaryLevelIdText)
								&& vo.getPolicyStandardId().equals(Convert.toStr(policyStandardMap.get(policyStandar))))
						.findFirst();
				if (hslwv.isPresent()) {
					if (null != hslwv.get().getSalaryLevelWage()) {
						xjSalary = hslwv.get().getSalaryLevelWage().toString();
					}
					salaryLevelId = hslwv.get().getSalaryLevelId();
				}
			}
			List<HrmsNewsalaryBasicColumn> list = hrmsNewsalaryBasicitemEmpService.getAllData(record);
			empIds.add(baseData.get("employee_id"));
			// 遍历list
			try {
				for (int j = 0; j < list.size(); j++) {
					//如果此次导入项次不存在，则不进行下一步处理；
					if (items.indexOf(list.get(j).getBasicItemName())==-1) {
						continue;
					}
					HrmsNewsalaryBasicitemEmp _bean = new HrmsNewsalaryBasicitemEmp();
					_bean.setBasicItemName(list.get(j).getBasicItemName());
					_bean.setBasicItemType(list.get(j).getBasicItemType());
					_bean.setEmpField(list.get(j).getEmpField());
					_bean.setBasicItemId(list.get(j).getId());
					_bean.setEmployeeId(baseData.get("employee_id"));
					_bean.setEffectiveDate(effectiveDate);
					_bean.setCustomRule(list.get(j).getCustomRule());
					_bean.setReason(reason);
                    if(data.get(i).containsKey("操作人")){
                        _bean.setCreateUserName(data.get(i).get("操作人"));
                        _bean.setUpdateUserName(data.get(i).get("操作人"));
                        _bean.setSsoOrgCode(data.get(i).get("医院编码"));
                    }
                    if(data.get(i).containsKey("操作人编码")){
                        _bean.setCreateUser(data.get(i).get("操作人编码"));
                        _bean.setUpdateUser(data.get(i).get("操作人编码"));
                    }
					 // 基础信息
					if ("1".equals(list.get(j).getBasicItemType())) {
						// 处理岗位类别 岗位等级 薪级类别 薪级等级
						if ("plgw".equals(list.get(j).getEmpField())) {
							if (StringUtils.isNoneBlank(plgwCode)) {
								_bean.setEmpFieldValue(plgwCode);
								_bean.setEmpFieldValueText(plgwText);
							} else {
								_bean.setEmpFieldValueText(plgwText);
							}
						} else if ("gwdj".equals(list.get(j).getEmpField())) {
							if (StringUtils.isNoneBlank(gwdjCode)) {
								_bean.setEmpFieldValue(gwdjCode);
								_bean.setEmpFieldValueText(gwdjText);
							} else {
								_bean.setEmpFieldValueText(gwdjText);
							}
						} else if ("salary_level_type".equals(list.get(j).getEmpField())) {
							if (StringUtils.isNoneBlank(salaryLevelTypeCode)) {
								_bean.setEmpFieldValue(salaryLevelTypeCode);
								_bean.setEmpFieldValueText(salaryLevelTypeText);
							} else {
								_bean.setEmpFieldValueText(salaryLevelTypeText);
							}
						} else if ("salary_level_id".equals(list.get(j).getEmpField())) {
							if (StringUtils.isNoneBlank(salaryLevelId)) {
								_bean.setEmpFieldValue(salaryLevelId);
								_bean.setEmpFieldValueText(salaryLevelIdText);
							} else {
								_bean.setEmpFieldValueText(salaryLevelIdText);
							}
						}else if ("policy_standard_id".equals(list.get(j).getEmpField())){ //政策标准
							if (StringUtils.isNoneBlank(policyStandardName)) {
								_bean.setEmpFieldValue(policyStandardMap.get(policyStandardName));
								_bean.setEmpFieldValueText(policyStandardName);
							} else {
								_bean.setEmpFieldValueText(policyStandardName);
							}
						}
					} else if ("2".equals(list.get(j).getBasicItemType())) { // 工资项
						//判断是否有传薪酬项的百分比
						if(items.contains(list.get(j).getBasicItemName()+"-百分比")){
							String percent = data.get(idx).get(list.get(j).getBasicItemName()+"-百分比");
							if(StringUtils.isNotBlank(percent) && percent.endsWith("%")){
								percent = percent.substring(0,percent.length()-1);
							}
							_bean.setEmpFieldValue(percent);
						}
						if ("a01".equalsIgnoreCase(list.get(j).getCustomRule())) {
//						if ("岗位工资".equals(list.get(j).getBasicItemName())) {
							// 如果Excel中有，则以本次设置值为准，反之则从等级中取值,Excel中的值优先
							if (null != data.get(idx).get(list.get(j).getBasicItemName())) {
								_bean.setSalaryAmount(new BigDecimal(data.get(idx).get(list.get(j).getBasicItemName())));
							} else { // 从系统设置中取值，如果第一次导入时有值，第后面导入无值，则保留之前值
								_bean.setSalaryAmount(new BigDecimal(gwSalary));
							}
						} else if ("a02".equalsIgnoreCase(list.get(j).getCustomRule())) { //基础性绩效
//						} else if ("绩效工资".equals(list.get(j).getBasicItemName())) {
							if (null != data.get(idx).get(list.get(j).getBasicItemName())) {
								_bean.setSalaryAmount(new BigDecimal(data.get(idx).get(list.get(j).getBasicItemName())));
							} else { // 从系统设置中取值
								_bean.setSalaryAmount(new BigDecimal(jxSalary));
							}
						} else if ("a03".equalsIgnoreCase(list.get(j).getCustomRule())) { //薪级等级工资
//						} else if ("薪级工资".equals(list.get(j).getBasicItemName())) {
							if (null != data.get(idx).get(list.get(j).getBasicItemName())) {
								_bean.setSalaryAmount(new BigDecimal(data.get(idx).get(list.get(j).getBasicItemName())));
							}else {
								_bean.setSalaryAmount(new BigDecimal(xjSalary));
//								_bean.setSalaryAmount(xjData.getSalaryLevelWage());
							}
//							if(null != xjData){
//								_bean.setSalaryAmount(xjData.getSalaryLevelWage());
//							}
						}else if ("a04".equalsIgnoreCase(list.get(j).getCustomRule())){ //奖励性绩效
							if (null != data.get(idx).get(list.get(j).getBasicItemName())) {
								_bean.setSalaryAmount(new BigDecimal(data.get(idx).get(list.get(j).getBasicItemName())));
							}else {
								_bean.setSalaryAmount(new BigDecimal(jljxSalary));
							}
						} else {
							String salaryAmount = data.get(idx).get(list.get(j).getBasicItemName());
							if (StringUtils.isNotBlank(salaryAmount)) {
								_bean.setSalaryAmount(new BigDecimal(salaryAmount));
							}else{
								continue;
							}
						}
					} else if ("3".equals(list.get(j).getBasicItemType())) { // 3其它项目为开关启停项
						if ("是".equals(data.get(idx).get(list.get(j).getBasicItemName()))) {
							_bean.setEmpFieldValue("1");
						} else {
							_bean.setEmpFieldValue("2");
						}
					}
					saveList.add(_bean);
				}
			} catch (Exception e) {
				e.printStackTrace();
				throw new BusinessException("导入发生错误的员工工号：" + employeeNo);
			}
			if(isSalaryAppoint) {
				hrmsNewsalaryBasicitemEmpService.save(saveList);
			}else{
				hrmsNewsalaryBasicitemEmpService.adjustSave(saveList);
			}
		}
		// 批量定薪完成后计算一次
		//已经在保存的时候计算过，不需要重复计算
//		for (int k = 0; k < empIds.size(); k++) {
//			this.CalculatSingle(empIds.get(k));
//		}
		saveSize = data.size() - 1;
		return saveSize;
	}

	// 批量调薪
	@Override
	public Integer importUpdateEntryTemplateData(List<Map<String, String>> data) {
		Integer saveSize = 0;
		Map<String, String> salaryLevelCategoryDictMap = convertReversalDictMap("salary_level_category"); // 获取薪级类别 通过字典
		Map<String, String> postCategoryDictMap = convertReversalDictMap("post_category"); // 获取岗位类别
		List<String> empIds = new ArrayList<>();
		for (int k = 1; k < data.size(); k++) { // 遍历每一个人
			String employeeNo = data.get(k).get("工号").trim();
			// 拿到人员的基本信息 根据工号
			Map<String, String> baseData = mapper.getBaseDataByEmpNo(employeeNo, UserInfoHolder.getCurrentUserCorpCode());
			if (MapUtil.isEmpty(baseData)) {
				throw new BusinessException(employeeNo + "员工信息不存在!");
			}
			if (!StrUtil.equals(MapUtil.getStr(baseData, "employee_name"), data.get(k).get("姓名"))) {
				throw new BusinessException(employeeNo + "姓名不正确!");
			}
			/*
			 * if("1".equals(baseData.get("salary_appoint"))){ log.error("已定薪"+ employeeNo +
			 * "======" +baseData.get("salary_appoint")); continue; }
			 */
			HrmsNewsalaryBasicColumn record = new HrmsNewsalaryBasicColumn();
			record.setEmployeeId(baseData.get("employee_id"));
			empIds.add(record.getEmployeeId());
			List<HrmsNewsalaryBasicColumn> list = hrmsNewsalaryBasicitemEmpService.getAllData(record);
			List<HrmsNewsalaryBasicitemEmp> saveList = new ArrayList<>();
			// 处理 类型为三的
			String xjndzcjj = data.get(k).get("薪级年度正常晋升");
			String ndtgbs = data.get(k).get("年度提高标识");
			String tggz = data.get(k).get("提高10%");
			String rsyfhj = data.get(k).get("人事应发合计");
			// 定调薪原因
			String reason = data.get(k).get("定调薪原因");
			if (StrUtil.isBlank(reason)) {
				throw new BusinessException(employeeNo + "定调薪原因为空!");
			}
			// 生效日期
			String effectiveDate = data.get(k).get("生效日期");
			if (StrUtil.isBlank(effectiveDate)) {
				throw new BusinessException(employeeNo + "生效日期为空!");
			}
			String dateFormat = "yyyy-MM-dd";
			Boolean checkDateFormat = this.checkDateFormat(effectiveDate, dateFormat);
			if (checkDateFormat) {
				throw new BusinessException(employeeNo + "生效日期格式错误!");
			}
			// 如果有岗位工资 直接取模版的岗位工资
			String tempGwgz = data.get(k).get("岗位工资");
			String tempXjgz = data.get(k).get("薪级工资");
			// 拿到 岗位类别
			String plgwText = data.get(k).get("岗位类别");
			String gwdjText = data.get(k).get("岗位等级");
			if (StrUtil.isBlank(plgwText)) {
				throw new BusinessException(employeeNo + "岗位类别为空!");
			}
			if (StrUtil.isBlank(gwdjText)) {
				throw new BusinessException(employeeNo + "岗位等级为空!");
			}
			String gwdjCode = "";
			String plgwCode = postCategoryDictMap.get(plgwText);
			if (StrUtil.isBlank(plgwCode)) {
				throw new BusinessException(employeeNo + "岗位类别不存在!");
			}
			// 拿到岗位等级
			List<String> gwdjCodeList = mapper.getGwdjCode(plgwCode, gwdjText);
			if (CollUtil.isEmpty(gwdjCodeList)) {
				throw new BusinessException(employeeNo + "岗位等级不存在或未启用!");
			}
			if (gwdjCodeList != null && gwdjCodeList.size() > 0) {
				gwdjCode = gwdjCodeList.get(0);
			}
			// 拿到薪级类别
			String salaryLevelTypeText = data.get(k).get("薪级类别");
			String salaryLevelIdText = data.get(k).get("薪级等级");
			if (StrUtil.isBlank(salaryLevelTypeText)) {
				throw new BusinessException(employeeNo + "薪级类别为空!");
			}
			if (StrUtil.isBlank(salaryLevelIdText)) {
				throw new BusinessException(employeeNo + "薪级等级为空!");
			}
			String salaryLevelTypeCode = salaryLevelCategoryDictMap.get(salaryLevelTypeText);
			if (StrUtil.isBlank(salaryLevelTypeCode)) {
				throw new BusinessException(employeeNo + "薪级类别不存在!");
			}
			String salaryLevelId = "";
			// 拿到薪级等级
			List<String> salaryLevelIdList = mapper.getSalaryLevelId(salaryLevelTypeCode, salaryLevelIdText);
			if (CollUtil.isEmpty(salaryLevelIdList)) {
				throw new BusinessException(employeeNo + "薪级等级不存在或未启用!");
			}
			if (salaryLevelIdList != null && salaryLevelIdList.size() > 0) {
				salaryLevelId = salaryLevelIdList.get(0);
			}
			// 拿到岗位工资
			String gwSalary = "";
			List<String> gwSalaryLsit = mapper.getGwSalary(plgwCode, gwdjCode);
			if (gwSalaryLsit != null && gwSalaryLsit.size() > 0) {
				gwSalary = gwSalaryLsit.get(0);
			}
			// 拿到薪级工资
			HrmsSalaryLevelWage xjEntity = new HrmsSalaryLevelWage();
			xjEntity.setSalaryLevelId(salaryLevelId);
			HrmsSalaryLevelWage xjData = hrmsSalaryLevelWageService.getDataByLevelId(xjEntity); // 薪级工资对象
			// 遍历list
			for (int i = 0; i < list.size(); i++) {
				HrmsNewsalaryBasicitemEmp _bean = new HrmsNewsalaryBasicitemEmp();
				_bean.setBasicItemName(list.get(i).getBasicItemName());
				_bean.setBasicItemType(list.get(i).getBasicItemType());
				_bean.setEmpField(list.get(i).getEmpField());
				_bean.setBasicItemId(list.get(i).getId());
				_bean.setEmployeeId(baseData.get("employee_id"));
				_bean.setEffectiveDate(effectiveDate);
				_bean.setReason(reason);
				// 基础信息
				if ("1".equals(list.get(i).getBasicItemType())) {
					// 处理岗位类别 岗位等级 薪级类别 薪级等级
					if ("plgw".equals(list.get(i).getEmpField())) {
						_bean.setEmpFieldValue(plgwCode);
					} else if ("gwdj".equals(list.get(i).getEmpField())) {
						_bean.setEmpFieldValue(gwdjCode);
					} else if ("salary_level_type".equals(list.get(i).getEmpField())) {
						_bean.setEmpFieldValue(salaryLevelTypeCode);
					} else if ("salary_level_id".equals(list.get(i).getEmpField())) {
						_bean.setEmpFieldValue(salaryLevelId);
					}
				} else if ("2".equals(list.get(i).getBasicItemType())) { // 工资项
					if ("岗位工资".equals(list.get(i).getBasicItemName())) {
						if (!StringUtil.isEmpty(tempGwgz)) {
							_bean.setSalaryAmount(new BigDecimal(tempGwgz));
						} else {
							if (!StringUtil.isEmpty(gwSalary)) {
								_bean.setSalaryAmount(new BigDecimal(gwSalary));
							}
						}

					} else if ("薪级工资".equals(list.get(i).getBasicItemName())) {
						if (!StringUtil.isEmpty(tempXjgz)) {
							_bean.setSalaryAmount(new BigDecimal(tempXjgz));
						} else {
							if (null != xjData) {
								_bean.setSalaryAmount(xjData.getSalaryLevelWage());
							}
						}
					} else {
						String salaryAmount = data.get(k).get(list.get(i).getBasicItemName());
						if (salaryAmount != null) {
							_bean.setSalaryAmount(new BigDecimal(salaryAmount));
						}
					}

				} else if ("3".equals(list.get(i).getBasicItemType())) { // 其它项目
					if ("薪级年度正常晋升".equals(list.get(i).getBasicItemName())) {
						if ("是".equals(xjndzcjj)) {
							_bean.setEmpFieldValue("1");
						}
					}
					if ("年度提高标识".equals(list.get(i).getBasicItemName())) {
						if ("是".equals(ndtgbs)) {
							_bean.setEmpFieldValue("1");
						}
					}
					if ("提高10%".equals(list.get(i).getBasicItemName())) {
						if ("是".equals(tggz)) {
							_bean.setEmpFieldValue("1");
						}
					}
					if ("人事应发合计".equals(list.get(i).getBasicItemName())) {
						if ("是".equals(rsyfhj)) {
							_bean.setEmpFieldValue("1");
						}
					}
				}
				saveList.add(_bean);
			}
			hrmsNewsalaryBasicitemEmpService.adjustSave(saveList);
		}
		for (int k = 0; k < empIds.size(); k++) {
			this.CalculatSingle(empIds.get(k));
		}
		saveSize = data.size() - 1;
		return saveSize;
	}

	// 判断日期是否为指定格式
	public Boolean checkDateFormat(String date, String dateFormat) {
		SimpleDateFormat sdf = new SimpleDateFormat(dateFormat);
		sdf.setLenient(false);
		try {
			sdf.parse(date);
			return false;
		} catch (ParseException e) {
			return true;
		}
	}

    @Transactional(readOnly = false)
	public void CalculatSingle(String empId) {
		Assert.hasText(empId, "员工id不能为空.");
		//查询员工信息
		HrmsEmployee hrmsEmployee = employeeMapper.findDetailById(empId);
		List<HrmsNewsalaryItem> salaryItem = mapper.getSalaryItemLibrary();
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("employeeId", empId);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		example.setOrderByClause(" create_date desc ");
		List<HrmsNewsalaryBasicitemEmp> list = mapper.selectByExample(example);
		log.info("员工id:"+empId);
		Map<String, BigDecimal> empBasicSalaryMap = list.stream().filter(emp->!"1".equals(emp.getBasicItemType()))
				.collect(Collectors.toMap(HrmsNewsalaryBasicitemEmp::getEmpField,
						emp -> {
							BigDecimal salaryAmount = Optional.ofNullable(emp.getSalaryAmount()).orElse(BigDecimal.ZERO);
							if ("1".equals(emp.getEmpFieldValue())) {
								return salaryAmount;
							}
		                    BigDecimal empFieldValue = Optional.ofNullable(emp.getEmpFieldValue()).map(BigDecimal::new).orElse(new BigDecimal(100));
		                    return salaryAmount.multiply(empFieldValue).divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP);
						},
						(oldValue, newValue) -> newValue, LinkedHashMap::new));

		BigDecimal oldValue = BigDecimal.ZERO; //薪酬项原值
		String effectiveDate = list.get(0).getEffectiveDate(); //获取最新的生效日期
		// 计算自定义其他项目
		for (HrmsNewsalaryBasicitemEmp record : list) {
			oldValue = record.getSalaryAmount();
			if (StrUtil.equals("3", record.getBasicItemType())) {
				HrmsNewsalaryItemLibrary library = itemLibraryMapper.selectByPrimaryKey(record.getEmpField());
				if (StrUtil.equals("2", library.getItemRule())) { //固定项
					if (StrUtil.equals("1", record.getEmpFieldValue())) {
						int scale = Integer.parseInt(library.getItemDigit());
	                    int roundingMode = CarryRuleEnum.getValByKey(library.getCarryRule());
						empBasicSalaryMap.put(record.getEmpField(), library.getSalaryItemAmount());
						record.setSalaryAmount(library.getSalaryItemAmount().setScale(scale, roundingMode));
						record.setEffectiveDate(effectiveDate);
						mapper.updateByPrimaryKeySelective(record);
						hrmsNewsalaryBasicitemEmpHistoryService.updateEmpHisttorySalaryItemByEmpIdAndEffect(empId,record.getBasicItemId(),record.getEffectiveDate(),record.getSalaryAmount());
						//根据计算结果更新异动记录
						detailedService.updateNowValueByEffective(hrmsEmployee,record.getBasicItemId(),record.getEffectiveDate(),oldValue,record.getSalaryAmount(),record.getReason());
					}
				}
			}
		}
		boolean flag=true;
		// 计算自定义公式
		for (HrmsNewsalaryBasicitemEmp record : list) {
			oldValue = record.getSalaryAmount();
			if (StrUtil.equals("3", record.getBasicItemType())) {
				HrmsNewsalaryItemLibrary library = itemLibraryMapper.selectByPrimaryKey(record.getEmpField());
				if (StrUtil.equals("4", library.getItemRule())) {
					String formulaText = library.getCountFormula();
					String countFormula = "";
					countFormula = formulaText;
//					if (StrUtil.equals("2", record.getEmpFieldValue())) { //如果禁用则不进一步公式解析，如果1启用则可能需要进一步解析公式；
//					}else {
//						countFormula = FormulaParse.getCountFormulaListLoop(formulaText, salaryItem);
//					}
					List<String> item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目
					int scale = Integer.parseInt(library.getItemDigit());
                    int roundingMode = CarryRuleEnum.getValByKey(library.getCarryRule());
					boolean resume = true; // 继续标识
					for (int k = 0; k < item.size(); k++) {
						Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item.get(k));
						if (null != countFormulaCode) {
							if ("2".equals(countFormulaCode.get("type"))) {
								if (null == empBasicSalaryMap.get(countFormulaCode.get("code"))) {
									resume = false;
									break;
								}
								countFormula = countFormula.replace(item.get(k),
										empBasicSalaryMap.get(countFormulaCode.get("code")).setScale(scale, roundingMode).toString());
							} else if ("4".equals(countFormulaCode.get("type"))) { // 常数
								String formula = item.get(k);
								int index = formula.indexOf("[");
								countFormula = countFormula.replace(item.get(k), formula.substring(0, index));
							}
						}
					}
					if (resume) {
						// 用函数库拿到结果
						countFormula = countFormula.replace("\\[\\d+\\]", "");
						// 匹配薪酬项目
						log.error("匹配替换前===========" + countFormula);
						for (Map.Entry<String, BigDecimal> entry : empBasicSalaryMap.entrySet()) {
							if (null != entry.getValue()) {
								countFormula = countFormula.replace("{" + entry.getKey() + "}",
										entry.getValue().toString());
							} else {
								countFormula = countFormula.replace("{" + entry.getKey() + "}", "0");
							}
						}
						// 去掉大括号
						countFormula = countFormula.replaceAll("[{}]", "");
						log.error("匹配替换后 计算算式===========" + countFormula);
						Expression expression = new Expression(countFormula);
						// 设置小数位及取整规则
						BigDecimal itemVal = expression.eval().setScale(Integer.parseInt(library.getItemDigit()),
								CarryRuleEnum.getValByKey(library.getCarryRule()));
						if (StrUtil.isNotBlank(record.getEmpFieldValue())) {
							if (StrUtil.equals("1", record.getEmpFieldValue())) {
								empBasicSalaryMap.put(record.getEmpField(), itemVal);
								record.setSalaryAmount(itemVal);
								record.setEffectiveDate(effectiveDate);
								mapper.updateByPrimaryKeySelective(record);
								hrmsNewsalaryBasicitemEmpHistoryService.updateEmpHisttorySalaryItemByEmpIdAndEffect(empId,record.getBasicItemId(),record.getEffectiveDate(),record.getSalaryAmount());
								//根据计算结果更新异动记录
								detailedService.updateNowValueByEffective(hrmsEmployee,record.getBasicItemId(),record.getEffectiveDate(),oldValue,record.getSalaryAmount(),record.getReason());
							}
						}
					}
				}
			}
		}
	}


    @Override
    public void CalculatSingle2Ineffective(String empId,String effectiveDate) {
        Assert.hasText(empId, "员工id不能为空.");
        //查询员工信息
        HrmsEmployee hrmsEmployee = employeeMapper.findDetailById(empId);
        Assert.hasText(effectiveDate, "生效日期不能为空.");
        List<HrmsNewsalaryItem> salaryItem = mapper.getSalaryItemLibrary();
        List<HrmsNewsalaryBasicitemEmpHistory> list = hrmsNewsalaryBasicitemEmpHistoryService.getEmpIneffectiveItemByEmpIdAndEffect(empId,effectiveDate);
        if(CollectionUtils.isEmpty(list)){
            return ;
        }
        log.info("员工id:"+empId);
        Map<String, BigDecimal> empBasicSalaryMap = list.stream().filter(emp->!"1".equals(emp.getBasicItemType()))
                .collect(Collectors.toMap(HrmsNewsalaryBasicitemEmpHistory::getEmpField,
                        emp -> {
                            BigDecimal salaryAmount = Optional.ofNullable(emp.getSalaryAmount()).orElse(BigDecimal.ZERO);
                            if ("1".equals(emp.getEmpFieldValue())) {
                                return salaryAmount;
                            }
                            BigDecimal empFieldValue = Optional.ofNullable(emp.getEmpFieldValue()).map(BigDecimal::new).orElse(new BigDecimal(100));
                            return salaryAmount.multiply(empFieldValue).divide(BigDecimal.valueOf(100), BigDecimal.ROUND_HALF_UP);
                        },
                        (oldValue, newValue) -> newValue, LinkedHashMap::new));

		BigDecimal oldValue = BigDecimal.ZERO; //薪酬项原值
        // 计算自定义其他项目
        for (HrmsNewsalaryBasicitemEmpHistory record : list) {
			oldValue = record.getSalaryAmount();
            if (StrUtil.equals("3", record.getBasicItemType())) {
                HrmsNewsalaryItemLibrary library = itemLibraryMapper.selectByPrimaryKey(record.getEmpField());
                if (StrUtil.equals("2", library.getItemRule())) { //固定项
                    if (StrUtil.equals("1", record.getEmpFieldValue())) {
                        int scale = Integer.parseInt(library.getItemDigit());
                        int roundingMode = CarryRuleEnum.getValByKey(library.getCarryRule());
                        empBasicSalaryMap.put(record.getEmpField(), library.getSalaryItemAmount());
                        record.setSalaryAmount(library.getSalaryItemAmount().setScale(scale, roundingMode));
						record.setEffectiveDate(effectiveDate);
                        hrmsNewsalaryBasicitemEmpHistoryService.update(record);
                        //根据计算结果更新异动记录
                        detailedService.updateNowValueByEffective(hrmsEmployee,record.getBasicItemId(),record.getEffectiveDate(),oldValue,record.getSalaryAmount(),record.getReason());
                    }
                }
            }
        }
        boolean flag=true;
        // 计算自定义公式
        for (HrmsNewsalaryBasicitemEmpHistory record : list) {
			oldValue = record.getSalaryAmount();
            if (StrUtil.equals("3", record.getBasicItemType())) {
                HrmsNewsalaryItemLibrary library = itemLibraryMapper.selectByPrimaryKey(record.getEmpField());
                if (StrUtil.equals("4", library.getItemRule())) {
                    String formulaText = library.getCountFormula();
                    String countFormula = "";
                    countFormula = formulaText;
                    List<String> item = FormulaParse.getCountFormulaList(countFormula); // 单独是项目
                    int scale = Integer.parseInt(library.getItemDigit());
                    int roundingMode = CarryRuleEnum.getValByKey(library.getCarryRule());
                    boolean resume = true; // 继续标识
                    for (int k = 0; k < item.size(); k++) {
                        Map<String, String> countFormulaCode = FormulaParse.getCountFormulaCode(item.get(k));
                        if (null != countFormulaCode) {
                            if ("2".equals(countFormulaCode.get("type"))) {
                                if (null == empBasicSalaryMap.get(countFormulaCode.get("code"))) {
                                    resume = false;
                                    break;
                                }
                                countFormula = countFormula.replace(item.get(k),
                                        empBasicSalaryMap.get(countFormulaCode.get("code")).setScale(scale, roundingMode).toString());
                            } else if ("4".equals(countFormulaCode.get("type"))) { // 常数
                                String formula = item.get(k);
                                int index = formula.indexOf("[");
                                countFormula = countFormula.replace(item.get(k), formula.substring(0, index));
                            }
                        }
                    }
                    if (resume) {
                        // 用函数库拿到结果
                        countFormula = countFormula.replace("\\[\\d+\\]", "");
                        // 匹配薪酬项目
                        log.error("匹配替换前===========" + countFormula);
                        for (Map.Entry<String, BigDecimal> entry : empBasicSalaryMap.entrySet()) {
                            if (null != entry.getValue()) {
                                countFormula = countFormula.replace("{" + entry.getKey() + "}",
                                        entry.getValue().toString());
                            } else {
                                countFormula = countFormula.replace("{" + entry.getKey() + "}", "0");
                            }
                        }
                        // 去掉大括号
                        countFormula = countFormula.replaceAll("[{}]", "");
                        log.error("匹配替换后 计算算式===========" + countFormula);
                        Expression expression = new Expression(countFormula);
                        // 设置小数位及取整规则
                        BigDecimal itemVal = expression.eval().setScale(Integer.parseInt(library.getItemDigit()),
                                CarryRuleEnum.getValByKey(library.getCarryRule()));
                        if (StrUtil.isNotBlank(record.getEmpFieldValue())) {
                            if (StrUtil.equals("1", record.getEmpFieldValue())) {
                                empBasicSalaryMap.put(record.getEmpField(), itemVal);
                                record.setSalaryAmount(itemVal);
								record.setEffectiveDate(effectiveDate);
                                hrmsNewsalaryBasicitemEmpHistoryService.update(record);
                                //根据计算结果更新异动记录
                                detailedService.updateNowValueByEffective(hrmsEmployee,record.getBasicItemId(),record.getEffectiveDate(),oldValue,record.getSalaryAmount(),record.getReason());
                            }
                        }
                    }
                }
            }
        }
    }

	@Override
	public List<HrmsNewsalaryBasicitemEmp> getEmpByTypeAndIds(String type, List empIds) {
		Example example = new Example(HrmsNewsalaryBasicitemEmp.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo("basicItemType", type);
		criteria.andIn("employeeId", empIds);
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		List<HrmsNewsalaryBasicitemEmp> list = mapper.selectByExample(example);
		return list;
	}

	//政策标准
	private Map<String, String> convertPolicyStandardMap() {
		Map<String, String> map = Maps.newHashMap();
		List<HrmsSalaryPolicyStandard> policyStandardList = hrmsSalaryPolicyStandardService.getList(new HrmsSalaryPolicyStandard());
		if (CollUtil.isNotEmpty(policyStandardList)) {
			for (HrmsSalaryPolicyStandard d : policyStandardList) {
				map.put(d.getPolicyStandardName(),d.getPolicyStandardId());
			}
		}
		return map;
	}
}
