package cn.trasen.hrms.train.service.impl;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.collect.Maps;

import cn.hutool.core.lang.Assert;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.hrms.train.dao.HrmsTrainPlanMapper;
import cn.trasen.hrms.train.dao.HrmsTrainRecordMapper;
import cn.trasen.hrms.train.model.HrmsTrainPlan;
import cn.trasen.hrms.train.model.HrmsTrainRecord;
import cn.trasen.hrms.train.service.HrmsTrainRecordService;
import cn.trasen.hrms.utils.DateUtils;
import tk.mybatis.mapper.entity.Example;
import tk.mybatis.mapper.entity.Example.Criteria;


/**
 * @ClassName: HrmsTrainRecordServiceImpl  
 * @Description: 培训记录
 * <AUTHOR>
 * @date 2020年5月23日 上午8:56:55
 */
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Service
public class HrmsTrainRecordServiceImpl implements HrmsTrainRecordService {

	@Autowired
	private HrmsTrainRecordMapper hrmsTrainRecordMapper;
	
	@Autowired
	private HrmsTrainPlanMapper hrmsTrainPlanMapper;
	
    @Autowired
    DictItemFeignService dictItemFeignService;

	@Autowired
	HrmsEmployeeFeignService hrmsEmployeeFeignService;
	
	
	
	@Override
	@Transactional(readOnly = false)
	public String signin(String signinType, String trainPlanId) {
		
		String retText = "";
		Example example = new Example(HrmsTrainRecord.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("trainPlanId", trainPlanId);
		criteria.andEqualTo("employeeId", UserInfoHolder.getCurrentUserCode());
		
		List<HrmsTrainRecord> hrmsTrainRecordList = hrmsTrainRecordMapper.selectByExample(example);
		
		HrmsTrainPlan hrmsTrainPlan = hrmsTrainPlanMapper.selectByPrimaryKey(trainPlanId);

		//判断是不是有以前有过这个培训主题
		HrmsTrainPlan selectZhuti = new HrmsTrainPlan();
		selectZhuti.setTrainTopic(hrmsTrainPlan.getTrainTopic());
		selectZhuti.setEmployeeId(UserInfoHolder.getCurrentUserCode());
		List<HrmsTrainPlan> zhuti = hrmsTrainPlanMapper.selectByUserCode(selectZhuti);  // 根源人员查询是否有参与过
		boolean is_compute = false;
		if("1".equals(hrmsTrainPlan.getRepeatCompute()) && zhuti != null && zhuti.size() > 0){
			is_compute = true;  //不计算
		}


		 String sex = UserInfoHolder.getCurrentUserInfo().getSex();  //性别  0男 1女
		if("0".equals(sex)) {
			sex ="男";
		}else if("1".equals(sex)) {
			sex ="女";
		}else {
			sex ="";
		}
		
		if("1".equals(signinType)){
			if(CollectionUtils.isNotEmpty(hrmsTrainRecordList)){
				HrmsTrainRecord hrmsTrainRecord = hrmsTrainRecordList.get(0);
				if(0 == hrmsTrainRecord.getSignStatus()) {
					//判断是否迟到
//					if(DateUtil.compare(new Date(),hrmsTrainPlan.getTrainStartTime()) >  0) {
//						hrmsTrainRecord.setSignStatus(1);
//					}else {
//						hrmsTrainRecord.setSignStatus(2);
//					}
					hrmsTrainRecord.setSignStatus(1);
					hrmsTrainRecord.setSex(sex);
					hrmsTrainRecord.setSignTime(new Date());
					if(is_compute){
						hrmsTrainRecord.setRecordCredit(null);
					}else{
						if(!StringUtil.isEmpty(hrmsTrainPlan.getCredit()) && Integer.valueOf(hrmsTrainPlan.getCredit()) > 0) {
							hrmsTrainRecord.setRecordCredit(String.valueOf(Double.valueOf(hrmsTrainPlan.getCredit())/2));
						}else {
							hrmsTrainRecord.setRecordCredit(null);
						}
					}


					hrmsTrainRecord.setRecordCreditHour(hrmsTrainPlan.getCreditHour());
					hrmsTrainRecord.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
					hrmsTrainRecord.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
					hrmsTrainRecordMapper.updateByPrimaryKeySelective(hrmsTrainRecord);
					retText = "签到成功";
				}else {
					retText = "已签到";
				}
			}else{
				HrmsTrainRecord hrmsTrainRecord = new HrmsTrainRecord();
				hrmsTrainRecord.setTrainRecordId(String.valueOf(IdWork.id.nextId()));
				hrmsTrainRecord.setTrainPlanId(hrmsTrainPlan.getTrainPlanId());
				hrmsTrainRecord.setEmployeeId(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setEmployeeName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setTrainTopic(hrmsTrainPlan.getTrainTopic());
				hrmsTrainRecord.setSignStatus(1);
				hrmsTrainRecord.setSex(sex);
				hrmsTrainRecord.setSignTime(new Date());
//				hrmsTrainRecord.setRecordCredit(hrmsTrainPlan.getCredit());
				if(is_compute){
					hrmsTrainRecord.setRecordCredit(null);
				}else{
					if(!StringUtil.isEmpty(hrmsTrainPlan.getCredit()) && Integer.valueOf(hrmsTrainPlan.getCredit()) > 0) {
						hrmsTrainRecord.setRecordCredit(String.valueOf(Double.valueOf(hrmsTrainPlan.getCredit())/2));
					}else {
						hrmsTrainRecord.setRecordCredit(null);
					}
				}

				hrmsTrainRecord.setRecordCreditHour(hrmsTrainPlan.getCreditHour());
				hrmsTrainRecord.setSignOutStatus(0);
				hrmsTrainRecord.setCreateDate(new Date());
				hrmsTrainRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setUpdateDate(new Date());
				hrmsTrainRecord.setIsDeleted("N");
				hrmsTrainRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				hrmsTrainRecord.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
				hrmsTrainRecord.setInvited(0);
				hrmsTrainRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsTrainRecordMapper.insertSelective(hrmsTrainRecord);
				retText = "签到成功";
			}
		}else{
			if(CollectionUtils.isNotEmpty(hrmsTrainRecordList)){
				HrmsTrainRecord hrmsTrainRecord = hrmsTrainRecordList.get(0);
				hrmsTrainRecord.setSignOutStatus(1);
				hrmsTrainRecord.setSex(sex);
				hrmsTrainRecord.setSignOutTime(new Date());
				hrmsTrainRecord.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				hrmsTrainRecord.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
				
				String recordCredit = hrmsTrainRecord.getRecordCredit();
				if(is_compute){
					hrmsTrainRecord.setRecordCredit(null);
				}else{
					if(1 == hrmsTrainRecord.getInvited()) {
						hrmsTrainRecord.setRecordCredit(String.valueOf((Double.valueOf(hrmsTrainPlan.getCredit())/2)+Double.valueOf(recordCredit)));
					}else {
						hrmsTrainRecord.setRecordCredit(null);
					}
				}

				
				hrmsTrainRecordMapper.updateByPrimaryKeySelective(hrmsTrainRecord);
			}else{
				HrmsTrainRecord hrmsTrainRecord = new HrmsTrainRecord();
				hrmsTrainRecord.setTrainRecordId(String.valueOf(IdWork.id.nextId()));
				hrmsTrainRecord.setTrainPlanId(hrmsTrainPlan.getTrainPlanId());
				hrmsTrainRecord.setEmployeeId(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setEmployeeName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setTrainTopic(hrmsTrainPlan.getTrainTopic());
				hrmsTrainRecord.setSignStatus(0);
				hrmsTrainRecord.setSex(sex);
				String recordCredit = hrmsTrainRecord.getRecordCredit();
				if(is_compute){
					hrmsTrainRecord.setRecordCredit(null);
				}else{
					if(1 == hrmsTrainRecord.getInvited()) {
						hrmsTrainRecord.setRecordCredit(String.valueOf((Double.valueOf(hrmsTrainPlan.getCredit())/2)+Double.valueOf(recordCredit)));
					}else {
						hrmsTrainRecord.setRecordCredit(null);
					}
				}
//				hrmsTrainRecord.setRecordCredit(hrmsTrainPlan.getCredit());
				hrmsTrainRecord.setRecordCreditHour(hrmsTrainPlan.getCreditHour());
				hrmsTrainRecord.setSignOutStatus(1);
				hrmsTrainRecord.setSignOutTime(new Date());
				hrmsTrainRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setUpdateDate(new Date());
				hrmsTrainRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
				hrmsTrainRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
				hrmsTrainRecord.setOrgId(UserInfoHolder.getCurrentUserInfo().getDeptcode());
				hrmsTrainRecord.setOrgName(UserInfoHolder.getCurrentUserInfo().getDeptname());
				hrmsTrainRecord.setInvited(0);
				hrmsTrainRecord.setIsDeleted("N");
				hrmsTrainRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
				hrmsTrainRecordMapper.insertSelective(hrmsTrainRecord);
			}
			retText = "签退成功";
		}
		 return retText;
	}

	@Override
	public List<HrmsTrainRecord> getDataList(Page page,HrmsTrainRecord hrmsTrainRecord) {
		
		if(StringUtil.isEmpty(page.getSidx())) {
			page.setSidx("p.train_start_time");
			page.setSord(" desc");
		}
		
		if("Y".equals(hrmsTrainRecord.getMyPlan())){
			hrmsTrainRecord.setEmployeeId(UserInfoHolder.getCurrentUserCode());
		}
		Map<String, String> trainingTypeConvertDictMap = convertDictMap("TRAINING_TYPE");  //学历
		//获取数据字典
		if(!StringUtil.isEmpty(hrmsTrainRecord.getStartDate()) && !StringUtil.isEmpty(hrmsTrainRecord.getEndDate()) ) {
			hrmsTrainRecord.setStartDate(hrmsTrainRecord.getStartDate()+" 00:00:00");
			hrmsTrainRecord.setEndDate(hrmsTrainRecord.getEndDate()+" 23:59:59");
		}
	
		
		hrmsTrainRecord.setCreateUser(UserInfoHolder.getCurrentUserInfo().getUsercode());
		hrmsTrainRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainRecord> dataList = hrmsTrainRecordMapper.getDataList(page, hrmsTrainRecord);
		for (int i = 0; i < dataList.size(); i++) {
			dataList.get(i).setTrainTypeText(trainingTypeConvertDictMap.get(dataList.get(i).getTrainType()));  //培训类型字典
			if(0 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("草稿");
			}
			if(1 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("待审批");
			}
			if(2 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("待开始");		
			}
			if(3 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("审批驳回");
			}
			if(4 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("取消计划");
			}
			if(5 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("已开始");
			}
			if(6 == dataList.get(i).getPlanState()){
				dataList.get(i).setPlanStateText("已结束");
			}
		}
		
		
		return dataList;
	}
	
	//我的培训明细时间
	@Override
	@Transactional(readOnly = false)
	public Map<String, Object> myDetails() {
		
		Map<String,Object> returnMap = new HashMap<>();
		
		String userCode = UserInfoHolder.getCurrentUserCode();
		//机构编码
		String ssoOrgCode = UserInfoHolder.getCurrentUserCorpCode();
		//培训次数
		Integer pxcs = hrmsTrainRecordMapper.pxcs(userCode, ssoOrgCode);
		//培训时长
		Double pxsc = hrmsTrainRecordMapper.pxsc(userCode, ssoOrgCode);
		//培训学时
		Double pxxs = hrmsTrainRecordMapper.pxxs(userCode, ssoOrgCode);
		//当前积分
		Double dqjf = hrmsTrainRecordMapper.dqjf(userCode, ssoOrgCode);
		//未参加次数
		Integer cj = hrmsTrainRecordMapper.cj(userCode, ssoOrgCode);
		if(null ==cj) {
			cj = 0;
		}
		
		returnMap.put("pxcs", null != pxcs && pxcs  > 0 ? pxcs :0);
		returnMap.put("pxsc",null != pxsc && pxsc > 0 ? pxsc :0);
		returnMap.put("pxxs",null != pxxs && pxxs > 0 ? pxxs :0);
		returnMap.put("dqjf",null != dqjf && dqjf > 0 ? dqjf :0);
		returnMap.put("wcj",pxcs - cj  );
		return returnMap;
	}
	
	
//	private String getHM(Integer min) {
//		if(min > 0) {
//			int floor = (int) Math.floor(min / 60);//分钟计算小时
//			int fen = min % 60;//分钟计算
//			return floor + "小时" + fen + "分钟";
//		}
//		return " 0小时0分钟";
//	}

	@Override
	public List<HrmsTrainRecord> getSigninDataList(Page page, HrmsTrainRecord hrmsTrainRecord) {
		List<HrmsTrainRecord> list = hrmsTrainRecordMapper.getSigninDataList(page,hrmsTrainRecord);
		if (list != null && list.size() > 0) {
			for (int i = 0; i < list.size(); i++) {
				list.get(i).setNo(i+1);  //设置序号

				//设置迟到数据
				if(1 == list.get(i).getSignStatus()) {
					list.get(i).setSignStatusText("已签到");
				}else if(2 == list.get(i).getSignStatus()) {
					list.get(i).setSignStatusText("迟到");
				}else {
					list.get(i).setSignStatusText("未签到");
				}

				//签退
				if( 0== list.get(i).getSignOutStatus()) {
					list.get(i).setSignOutStatusText("未签退");
				}else {
					list.get(i).setSignOutStatusText("已签退");
				}

				//处理签到时间签退时间

				if(null != list.get(i).getSignTime()) {
					list.get(i).setSignTimeText(DateUtils.getPresentTimeStr(list.get(i).getSignTime()));
				}

				if(null != list.get(i).getSignOutTime()) {
					list.get(i).setSignOutTimeText(DateUtils.getPresentTimeStr(list.get(i).getSignOutTime()));
				}
			}
		}
		
		return list;
	}


	@Override
	@Transactional(readOnly = false)
	public Map<String, Object> signInDetailsById(HrmsTrainRecord hrmsTrainRecord) {
		
		Map<String, Object> returnMap = new HashMap<>();
		
		Double yindao = hrmsTrainRecordMapper.yindao(hrmsTrainRecord);
		Double yidao = hrmsTrainRecordMapper.yidao(hrmsTrainRecord);
		Double weidao = hrmsTrainRecordMapper.weidao(hrmsTrainRecord);
		Double chidao = hrmsTrainRecordMapper.chidao(hrmsTrainRecord);
		Double qiantui = hrmsTrainRecordMapper.qiantui(hrmsTrainRecord);
		Double weiqiantui = hrmsTrainRecordMapper.weiqiantui(hrmsTrainRecord);
		
		returnMap.put("yindao", yindao);
		returnMap.put("yidao", yidao);
		returnMap.put("weidao", weidao);
		returnMap.put("chidao", chidao);
		returnMap.put("qiantui", qiantui);
		returnMap.put("weiqiantui", weiqiantui);
		return returnMap;
	}
	
	//签到签退导出数据
	@Override
	public List<HrmsTrainRecord> getSignInCountExport( HrmsTrainRecord hrmsTrainRecord) {
		// TODO Auto-generated method stub
		return  hrmsTrainRecordMapper.getSignInCountExport(hrmsTrainRecord);
	}
	
	//我的培训三个统计
	@Override
	public Map<String, Object> myParticipate() {
		Map<String, Object> returnMap = new HashMap<>();
		String userCode = UserInfoHolder.getCurrentUserCode();
//		Integer pxcs = hrmsTrainRecordMapper.pxall(userCode);  //培训次数
		
		Integer cj = hrmsTrainRecordMapper.cj(userCode, UserInfoHolder.getCurrentUserCorpCode());
		
		//待培训
		Integer dpx = hrmsTrainRecordMapper.dpx(userCode, UserInfoHolder.getCurrentUserCorpCode());
//		if(null ==pxcs) {
//			pxcs = 0;
//		}
		if(null ==cj) {
			cj = 0;
		}
		if(null ==dpx) {
			dpx = 0;
		}
		returnMap.put("dpx", dpx);
		returnMap.put("ypx", cj);
//		returnMap.put("all", pxcs);
		return returnMap;
	}
	
    private Map<String, String> convertDictMap(String dictType) {
        Assert.notNull(dictType, "dictType must not be null.");
        Map<String, String> map = Maps.newHashMap();
        List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
        if (CollectionUtils.isNotEmpty(dictItemList)) {
            for (DictItemResp d : dictItemList) {
                map.put(d.getItemNameValue(), d.getItemName());
            }
        }
        return map;
    }

	@Override
	public List<HrmsTrainRecord> getSignInAllCountExport(HrmsTrainRecord hrmsTrainRecord) {
		hrmsTrainRecord.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		return  hrmsTrainRecordMapper.getSignInAllCountExport(hrmsTrainRecord);
	}

	@Override
	public List<HrmsTrainRecord> notInvited(HrmsTrainRecord hrmsTrainRecord) {
		Example example = new Example(HrmsTrainRecord.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
		criteria.andEqualTo("trainPlanId", hrmsTrainRecord.getTrainPlanId());
		//criteria.andEqualTo("invited", 0);
		if(!StringUtil.isEmpty(hrmsTrainRecord.getEmployeeName())) {
			criteria.andLike("employeeName", "%" + hrmsTrainRecord.getEmployeeName() + "%");
		}
		
		if(!StringUtil.isEmpty(hrmsTrainRecord.getSignType())) {
			if("0".equals(hrmsTrainRecord.getSignType())) {  //全部
				
			}else if("1".equals(hrmsTrainRecord.getSignType())) {  //未签到
				criteria.andEqualTo("signStatus", 0);
			}else if("2".equals(hrmsTrainRecord.getSignType())) {  //未签退
				criteria.andEqualTo("signOutStatus", 0);
			}else if("3".equals(hrmsTrainRecord.getSignType())) {  //签到也签退的人员
				criteria.andEqualTo("signOutStatus", 1);
				criteria.andEqualTo("signStatus", 1);
			}
			
		}
		//criteria.andIsNull("recordCredit");
		List<HrmsTrainRecord> hrmsTrainRecordList = hrmsTrainRecordMapper.selectByExample(example);
		return hrmsTrainRecordList;
	}
	
	@Transactional(readOnly = false)
	@Override
	public int ascribeIntegral(List<HrmsTrainRecord> hrmsTrainRecords) {
		
		if (hrmsTrainRecords != null && hrmsTrainRecords.size() > 0) {
			for (int i = 0; i < hrmsTrainRecords.size(); i++) {
				HrmsTrainRecord bean = new HrmsTrainRecord();
				bean.setRecordCredit(hrmsTrainRecords.get(i).getRecordCredit());
				bean.setTrainRecordId(hrmsTrainRecords.get(i).getTrainRecordId());;
				hrmsTrainRecordMapper.updateByPrimaryKeySelective(bean);
			}
		}
		return hrmsTrainRecords.size();
	}

	@Override
	public DataSet<HrmsTrainRecord> ranking(Page page, HrmsTrainRecord record) {
	
		String userCode = UserInfoHolder.getCurrentUserCode();
		if ("1".equals(record.getRankingType())) { // 等于1 查询前10
			page.setPageSize(10);

		}
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<HrmsTrainRecord> records = hrmsTrainRecordMapper.ranking(page,record);
		if("1".equals(record.getRankingType())) {
			boolean isNo = false;
			if (records != null && records.size() > 0) {
				for (int i = 0; i < records.size(); i++) {
					records.get(i).setNo(i+1);
					if(userCode.equals(records.get(i).getEmployeeId())) {
						isNo = true;
						records.get(i).setEmployeeName(records.get(i).getEmployeeName() + "（本人）");
						
					}
				}
			}
			if(!isNo) {
				Page page2 = new Page();
				try {
					BeanUtils.copyProperties(page, page2);
				} catch (Exception e) {
				}
				page2.setPageSize(Integer.MAX_VALUE);
				List<HrmsTrainRecord> benren = hrmsTrainRecordMapper.ranking(page2, record);
				if (benren != null && benren.size() > 0) {
					for (int i = 0; i < benren.size(); i++) {
						if(userCode.equals(benren.get(i).getEmployeeId())) {
							benren.get(i).setEmployeeName(benren.get(i).getEmployeeName() + "（本人）");
							benren.get(i).setNo(i+1);
							records.add(benren.get(i));
							break;
						}
					}
				}
				
			}
		}else {
			for (int f = 0; f < records.size(); f++) {
				records.get(f).setNo(f+1);	
			}
		}
		
		
		if("1".equals(record.getRankingType())) {
			return new DataSet<>(page.getPageNo(), page.getPageSize(), 1, 10, records);
		}else {
			return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
		}
	}

	@Override
	public List<HrmsTrainRecord> findListByEmpId(String id) {
		return hrmsTrainRecordMapper.findListByEmpId(id);
	}

	@Override
	@Transactional(readOnly = false)
	public String addSignin(HrmsTrainRecord record) {
		String retText = "操作成功";
		Example example = new Example(HrmsTrainRecord.class);
		Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, Contants.IS_DELETED_FALSE);
		criteria.andEqualTo("trainPlanId", record.getTrainPlanId());
		criteria.andEqualTo("employeeId", record.getEmployeeId());
		List<HrmsTrainRecord> hrmsTrainRecordList = hrmsTrainRecordMapper.selectByExample(example);
		HrmsTrainPlan hrmsTrainPlan = hrmsTrainPlanMapper.selectByPrimaryKey(record.getTrainPlanId());
		Set<String> empCodeList = new HashSet<>();
		empCodeList.add(record.getEmployeeId());
		List<EmployeeResp> employeeRespList = new ArrayList<>();
		if (empCodeList.size() > 0) {
			employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(empCodeList)).getObject();
		}
		if(employeeRespList.isEmpty()){
			return "未找到人员信息";
		}
		EmployeeResp employeeResp = employeeRespList.get(0);

		if(CollectionUtils.isNotEmpty(hrmsTrainRecordList)){
			HrmsTrainRecord hrmsTrainRecord = hrmsTrainRecordList.get(0);
			hrmsTrainRecord.setSex(employeeResp.getGenderText());
			hrmsTrainRecord.setInvited(1);
			hrmsTrainRecord.setSignStatus(record.getSignStatus());
			if(1 == record.getSignStatus() ){
				hrmsTrainRecord.setSignTime(hrmsTrainPlan.getTrainStartTime());
			}
			if(1 == record.getSignOutStatus()){
				hrmsTrainRecord.setSignOutTime(hrmsTrainPlan.getTrainEndTime());
			}
			hrmsTrainRecord.setSignOutStatus(record.getSignOutStatus());
			hrmsTrainRecord.setRecordCreditHour(hrmsTrainPlan.getCreditHour());
			hrmsTrainRecord.setOrgId(employeeResp.getOrgCode());
			hrmsTrainRecord.setOrgName(employeeResp.getOrgName());
			hrmsTrainRecord.setRecordCredit(record.getRecordCredit());
			hrmsTrainRecordMapper.updateByPrimaryKeySelective(hrmsTrainRecord);
		}else{
			HrmsTrainRecord hrmsTrainRecord = new HrmsTrainRecord();
			hrmsTrainRecord.setTrainRecordId(String.valueOf(IdWork.id.nextId()));
			hrmsTrainRecord.setTrainPlanId(hrmsTrainPlan.getTrainPlanId());
			hrmsTrainRecord.setEmployeeId(record.getEmployeeId());
			hrmsTrainRecord.setEmployeeName(record.getEmployeeName());
			hrmsTrainRecord.setTrainTopic(hrmsTrainPlan.getTrainTopic());
			hrmsTrainRecord.setSex(employeeResp.getGenderText());
			hrmsTrainRecord.setCreateDate(new Date());
			hrmsTrainRecord.setCreateUser(UserInfoHolder.getCurrentUserCode());
			hrmsTrainRecord.setCreateUserName(UserInfoHolder.getCurrentUserName());
			hrmsTrainRecord.setUpdateDate(new Date());
			hrmsTrainRecord.setIsDeleted("N");
			hrmsTrainRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			hrmsTrainRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
			hrmsTrainRecord.setOrgId(employeeResp.getOrgCode());
			hrmsTrainRecord.setOrgName(employeeResp.getOrgName());
			hrmsTrainRecord.setInvited(1);
			hrmsTrainRecord.setSignStatus(record.getSignStatus());
			hrmsTrainRecord.setSignOutStatus(record.getSignOutStatus());
			hrmsTrainRecord.setRecordCreditHour(hrmsTrainPlan.getCreditHour());
			hrmsTrainRecord.setRecordCredit(record.getRecordCredit());

			if(1 == record.getSignStatus()){
				hrmsTrainRecord.setSignTime(hrmsTrainPlan.getTrainStartTime());
			}
			if(1 == record.getSignOutStatus()){
				hrmsTrainRecord.setSignOutTime(hrmsTrainPlan.getTrainEndTime());
			}
			hrmsTrainRecordMapper.insertSelective(hrmsTrainRecord);
		}
		return retText;
	}

	@Override
	@Transactional(readOnly = false)
	public void deleteById(String trainPlanId) {
		org.springframework.util.Assert.hasText(trainPlanId, "ID不能为空.");
		HrmsTrainRecord hrmsTrainRecord = new HrmsTrainRecord();
		hrmsTrainRecord.setTrainRecordId(trainPlanId);
		hrmsTrainRecord.setIsDeleted(Contants.IS_DELETED_TURE);
		hrmsTrainRecord.setUpdateUser(UserInfoHolder.getCurrentUserCode());
		hrmsTrainRecord.setUpdateUserName(UserInfoHolder.getCurrentUserName());
		hrmsTrainRecord.setUpdateDate(new Date());
		hrmsTrainRecordMapper.updateByPrimaryKeySelective(hrmsTrainRecord);
	}
}
