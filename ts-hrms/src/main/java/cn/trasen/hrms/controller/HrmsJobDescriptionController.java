package cn.trasen.hrms.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsJobDescription;
import cn.trasen.hrms.service.HrmsJobDescriptionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName HrmsJobDescriptionController
 * @Description TODO
 * @date 2021��12��22�� ����5:24:49
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "HrmsJobDescriptionController")
public class HrmsJobDescriptionController {

	private transient static final Logger logger = LoggerFactory.getLogger(HrmsJobDescriptionController.class);

	@Autowired
	private HrmsJobDescriptionService hrmsJobDescriptionService;

	/**
	 * @Title saveHrmsJobDescription
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��12��22�� ����5:24:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/jobDescription/save")
	public PlatformResult<String> saveHrmsJobDescription(@RequestBody HrmsJobDescription record) {
		try {
			hrmsJobDescriptionService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateHrmsJobDescription
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��12��22�� ����5:24:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/jobDescription/update")
	public PlatformResult<String> updateHrmsJobDescription(@RequestBody HrmsJobDescription record) {
		try {
			hrmsJobDescriptionService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectHrmsJobDescriptionById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<HrmsJobDescription>
	 * @date 2021��12��22�� ����5:24:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/jobDescription/{id}")
	public PlatformResult<HrmsJobDescription> selectHrmsJobDescriptionById(@PathVariable String id) {
		try {
			HrmsJobDescription record = hrmsJobDescriptionService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteHrmsJobDescriptionById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2021��12��22�� ����5:24:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/jobDescription/delete/{id}")
	public PlatformResult<String> deleteHrmsJobDescriptionById(@PathVariable String id) {
		try {
			hrmsJobDescriptionService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectHrmsJobDescriptionList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<HrmsJobDescription>
	 * @date 2021��12��22�� ����5:24:49
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/jobDescription/list")
	public DataSet<HrmsJobDescription> selectHrmsJobDescriptionList(Page page, HrmsJobDescription record) {
		return hrmsJobDescriptionService.getDataSetList(page, record);
	}
	
	/**
	 * 
	 * @Title: selectHrmsJobDescriptionByEmpId
	 * @Description: TODO(这里用一句话描述这个方法的作用)
	 * @param @param empId
	 * @param @return 参数
	 * @return PlatformResult<HrmsJobDescription> 返回类型
	 * 2021年12月24日
	 * ADMIN
	 * @throws
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/jobDescription/selectHrmsJobDescriptionByEmpId/{empId}")
	public PlatformResult<HrmsJobDescription> selectHrmsJobDescriptionByEmpId(@PathVariable String empId) {
		try {
			HrmsJobDescription record = hrmsJobDescriptionService.selectHrmsJobDescriptionByEmpId(empId);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
