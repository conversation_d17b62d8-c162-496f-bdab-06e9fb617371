package cn.trasen.hrms.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.hrms.model.HrmsCommonMenu;
import cn.trasen.hrms.service.HrmsCommonMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**    
  * <P> @Description: 设置常用快捷菜单</p>
  * <P> @Date: 2020年6月29日  下午2:43:22 </p>
  * <P> @Author: wangzhihua </p>
  * <P> @Company: 湖南创星 </p>
  * <P> @version V1.0    </p> 
  */ 
@Api(tags = "快捷菜单Controller")
@RestController
public class HrmsCommonMenuController {

	private static final Logger logger = LoggerFactory.getLogger(HrmsCommonMenuController.class);

	@Autowired
	private  HrmsCommonMenuService hrmsCommonMenuService;


	/**  
	 * <p> @Title: insert</p>
	 * <p> @Description: 添加快捷菜单</p>
	 * <p> @Param: </p>
	 * <p> @Return: PlatformResult<String></p>
	 * <P> @Date: 2020年6月29日  下午4:43:09 </p>
	 * <p> <AUTHOR>
	 */  
	@ApiOperation(value = "添加快捷菜单", notes = "添加快捷菜单")
	@PostMapping(value = "/hrmscommonmenu/save")
	public PlatformResult<String> insert(@RequestBody HrmsCommonMenu entity) {
		try {
			if (hrmsCommonMenuService.insert(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}

	@ApiOperation(value = "修改快捷菜单", notes = "修改快捷菜单")
	@PostMapping(value = "/hrmscommonmenu/update")
	public PlatformResult<String> update(HrmsCommonMenu entity) {
		try {
			if (hrmsCommonMenuService.update(entity) > 0) {
				return PlatformResult.success();
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		}
		return PlatformResult.failure();
	}



	/**  
	 * <p> @Title: deleteById</p>
	 * <p> @Description: 删除快捷菜单</p>
	 * <p> @Param: </p>
	 * <p> @Return: PlatformResult<String></p>
	 * <P> @Date: 2020年6月29日  下午4:43:54 </p>
	 * <p> <AUTHOR>
	 */  
	@ApiOperation(value = "删除快捷菜单", notes = "删除快捷菜单")
	@PostMapping(value = "/hrmscommonmenu/deletedById/{id}")
	@ResponseBody
	public PlatformResult<String> deleteById(@PathVariable String id) {
		try {
			hrmsCommonMenuService.deleted(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}
	
	@ApiOperation(value = "删除所有快捷菜单", notes = "删除所有快捷菜单")
	@PostMapping(value = "/hrmscommonmenu/deleteAll")
	@ResponseBody
	public PlatformResult<String> deleteAll() {
		try {
			hrmsCommonMenuService.deleteAll();
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

	/**
	 * @Title: getDataList
	 * @Description: 获取所有的快捷菜单
	 * @Param: page
	 * @param entity
	 * @Return: DataSet<HrmsAccountInfo>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取自定义快捷菜单", notes = "获取自定义快捷菜单")
	@PostMapping(value = "/hrmscommonmenu/getAll")
	@ResponseBody
	public DataSet<HrmsCommonMenu> getDataList(Page page, HrmsCommonMenu entity) {
		List<HrmsCommonMenu> list = hrmsCommonMenuService.getDataList(page, entity);
		return new DataSet<HrmsCommonMenu>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	}
	
	@ApiOperation(value = "查询自己的快捷菜单", notes = "查询自己的快捷菜单")
	@GetMapping(value = "/hrmscommonmenu/list")
	@ResponseBody
	public PlatformResult<List<HrmsCommonMenu>> getList() {
		try {
			return PlatformResult.success(hrmsCommonMenuService.getList());
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure();
		}
	}

}
