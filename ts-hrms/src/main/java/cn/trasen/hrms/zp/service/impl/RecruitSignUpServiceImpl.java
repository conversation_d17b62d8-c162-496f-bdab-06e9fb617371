package cn.trasen.hrms.zp.service.impl;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import cn.trasen.BootComm.utils.IDCardUtil;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.HrmsEmployeeSaveReq;
import cn.trasen.homs.bean.oa.MessageInternalReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.BeanUtils;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsEmployeeFeignService;
import cn.trasen.homs.feign.oa.MessageInternalFeignService;
import cn.trasen.hrms.utils.DateUtils;
import cn.trasen.hrms.zp.bean.FastSignUpReq;
import cn.trasen.hrms.zp.bean.RclxReq;
import cn.trasen.hrms.zp.bean.RecruitAuditReq;
import cn.trasen.hrms.zp.bean.RecruitCancelSignUpReq;
import cn.trasen.hrms.zp.bean.RecruitPlanCountReq;
import cn.trasen.hrms.zp.bean.RecruitPlanCountRes;
import cn.trasen.hrms.zp.bean.RecruitPlanPostReq;
import cn.trasen.hrms.zp.bean.RecruitPlanReq;
import cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressReq;
import cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressRes;
import cn.trasen.hrms.zp.bean.RecruitSendSMSReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpCountRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpListReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpListRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpReq;
import cn.trasen.hrms.zp.bean.RecruitSignUpRes;
import cn.trasen.hrms.zp.bean.RecruitSignUpSaveReq;
import cn.trasen.hrms.zp.enums.GraduatesTypeEnum;
import cn.trasen.hrms.zp.enums.RecruitEducationTypeEnum;
import cn.trasen.hrms.zp.enums.RecruitGenderEnum;
import cn.trasen.hrms.zp.enums.RecruitSignUpStatusEnuma;
import cn.trasen.hrms.zp.mapper.RecruitSignUpMapper;
import cn.trasen.hrms.zp.mapper.ZpJurisdictionMapper;
import cn.trasen.hrms.zp.mapper.ZpProcedureMapper;
import cn.trasen.hrms.zp.model.RecruitPlan;
import cn.trasen.hrms.zp.model.RecruitPlanPost;
import cn.trasen.hrms.zp.model.RecruitProcessConfig;
import cn.trasen.hrms.zp.model.RecruitSignUp;
import cn.trasen.hrms.zp.service.RecruitAdminService;
import cn.trasen.hrms.zp.service.RecruitPlanPostService;
import cn.trasen.hrms.zp.service.RecruitPlanService;
import cn.trasen.hrms.zp.service.RecruitProcessConfigService;
import cn.trasen.hrms.zp.service.RecruitSignUpService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2021/12/7 14:35
 */
@Slf4j
@Service
public class RecruitSignUpServiceImpl implements RecruitSignUpService {
    @Autowired
    RecruitSignUpMapper recruitSignUpMapper;


    @Autowired
    RecruitPlanPostService recruitPlanPostService;

    @Autowired
    RecruitPlanService recruitPlanService;


    @Autowired
    RecruitProcessConfigService recruitProcessConfigService;

    @Autowired
    RecruitAdminService recruitAdminService;

    @Autowired
    HrmsEmployeeFeignService hrmsEmployeeFeignService;
    
    @Autowired
    MessageInternalFeignService messageInternalFeignService;
   
    @Autowired
    ZpJurisdictionMapper zpJurisdictionMapper;
    @Autowired
    ZpProcedureMapper zpProcedureMapper;
   
    @Autowired
    DictItemFeignService dictItemFeignService;
   
    @Transactional(rollbackFor = Exception.class)
    @Override
    /**
     * 快速报名
     * @param fastSignUpReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/28 15:31
     */
    public void fastSignUp(FastSignUpReq fastSignUpReq) throws Throwable {
        RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
        recruitSignUpListReq.setIdentityCard(fastSignUpReq.getIdentityCard());
        recruitSignUpListReq.setPlanId(fastSignUpReq.getPlanId());
        recruitSignUpListReq.setName(fastSignUpReq.getName());
        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
        if (CollectionUtils.isEmpty(recruitSignUpList)) {
            throw new BusinessException("未找到对应报名信息，请检查输入信息是否有误");
        }
        RecruitSignUpListRes recruitSignUpListRes = recruitSignUpList.get(0);

        RecruitSignUpSaveReq recruitSignUpSaveReq;
        recruitSignUpSaveReq = JSON.parseObject(recruitSignUpListRes.getOtherData(), RecruitSignUpSaveReq.class);
        recruitSignUpSaveReq.setOtherData(recruitSignUpListRes.getOtherData());
        recruitSignUpSaveReq.setPostId(fastSignUpReq.getPostId());
        signUp(recruitSignUpSaveReq);
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    public void signUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

        checkSignUp(recruitSignUpSaveReq);

        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        if (recruitPlanPost == null) {
            throw new BusinessException("岗位选择不正确！");
        }


        RecruitPlanReq recruitPlanReq = new RecruitPlanReq();
        recruitPlanReq.setId(recruitPlanPost.getPlanId());
        RecruitPlan recruitPlan = recruitPlanService.getBase(recruitPlanReq);

        RecruitSignUpListReq recruitSignUpListReq=new RecruitSignUpListReq();
        recruitSignUpListReq.setPlanId(recruitPlan.getId());
        recruitSignUpListReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList=recruitSignUpMapper.getList(recruitSignUpListReq);
		if(recruitSignUpList.size()>=recruitPlan.getLimitPostNum()) {
		    throw new BusinessException("本次招聘每人限报"+recruitPlan.getLimitPostNum()+"个岗位，您已达上限！");
		
		}


        Date nowTime = new Date();
        RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);
        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpReq.setPostId(recruitSignUpSaveReq.getPostId());
        recruitSignUpReq.setCancelStatus(2);
        RecruitSignUp oldRecruitSignUp = getBase(recruitSignUpReq);
        if (oldRecruitSignUp != null) {
            throw new BusinessException("不可以重复报名！");
        }
        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());

        RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

        recruitSignUp.setGender(sex.getValue());

        BeanUtil.copyProperties(recruitSignUpSaveReq, recruitSignUp, "id");
        recruitSignUp.setSignUpTime(nowTime);
        recruitSignUp.setSignupCode(getMaxCode(recruitPlanPost.getPostCode(), recruitPlanPost.getId()));
        recruitSignUp.setStatus(RecruitSignUpStatusEnuma.UNTREATED.getKey());

        if (recruitProcessConfig.getFirstAudit().equals(1)) {
            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey());
        }
        if (recruitProcessConfig.getReviewAudit().equals(1)) {
            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.REVIEWAUDIT.getKey());
        }
        recruitSignUp.setCancelStatus(2);

        //  recruitSignUp.setOtherData(JSON.toJSONString(recruitSignUpSaveReq));
        recruitSignUp.setGraduatesType(recruitSignUpSaveReq.getIsGraduates().equals("是")?2:1);
        recruitSignUp.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        recruitSignUpMapper.insertSelective(recruitSignUp);
    }
    
    
   
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void newSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

    	newCheckSignUp(recruitSignUpSaveReq);

        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        if (recruitPlanPost == null) {
            throw new BusinessException("岗位选择不正确！");
        }

        RecruitPlanReq recruitPlanReq = new RecruitPlanReq();
        recruitPlanReq.setId(recruitPlanPost.getPlanId());
        RecruitPlan recruitPlan = recruitPlanService.getBase(recruitPlanReq);

        RecruitSignUpListReq recruitSignUpListReq=new RecruitSignUpListReq();
        if(null != recruitPlan) {
        	 recruitSignUpListReq.setPlanId(recruitPlan.getId());
        }
       
        recruitSignUpListReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList=recruitSignUpMapper.getList(recruitSignUpListReq);
        if(null != recruitPlan) {
        	if(recruitSignUpList.size()>=recruitPlan.getLimitPostNum()) {
    		    throw new BusinessException("本次招聘每人限报"+recruitPlan.getLimitPostNum()+"个岗位，您已达上限！");
    		
    		}
        }


        Date nowTime = new Date();
        RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);
        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpReq.setPostId(recruitSignUpSaveReq.getPostId());
        recruitSignUpReq.setCancelStatus(2);
        
        RecruitSignUp oldRecruitSignUp = null;
        if("1".equals(recruitSignUpSaveReq.getTalentPool())) {
        	 oldRecruitSignUp = getBase2(recruitSignUpReq);
        }else {
        	 oldRecruitSignUp = getBase(recruitSignUpReq);
        }
        
        if (oldRecruitSignUp != null) {
            throw new BusinessException("不可以重复报名！");
        }
        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());

//        RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

        recruitSignUp.setGender(sex.getValue());

        BeanUtil.copyProperties(recruitSignUpSaveReq, recruitSignUp, "id");
        recruitSignUp.setSignUpTime(nowTime);
        recruitSignUp.setSignupCode(getMaxCode(recruitPlanPost.getPostCode(), recruitPlanPost.getId()));
        recruitSignUp.setStatus(RecruitSignUpStatusEnuma.UNTREATED.getKey());

     
        recruitSignUp.setCancelStatus(2);

        //  recruitSignUp.setOtherData(JSON.toJSONString(recruitSignUpSaveReq));
        
        // TODO 性别验证
//        recruitSignUp.setGraduatesType(recruitSignUpSaveReq.getIsGraduates().equals("是")?2:1);
        recruitSignUp.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
        recruitSignUpMapper.insertSelective(recruitSignUp);
    }

    @Override
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    public void batchEditSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {
        RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
        recruitSignUpListReq.setName(recruitSignUpSaveReq.getName());
        recruitSignUpListReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpListReq.setPlanId(recruitSignUpSaveReq.getPlanId());
        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpList) {

            JSONObject jsonObject=JSON.parseObject(recruitSignUpSaveReq.getOtherData());
            jsonObject.put("postId",recruitSignUpListRes.getPostId());
            recruitSignUpSaveReq.setOtherData(JSON.toJSONString(jsonObject));
            recruitSignUpSaveReq.setPostId(recruitSignUpListRes.getPostId());
            recruitSignUpSaveReq.setId(recruitSignUpListRes.getId());
            try {
                editSignUp(recruitSignUpSaveReq);
            } catch (BusinessException businessException) {
                throw new BusinessException(recruitSignUpListRes.getPostName() + " " + businessException.getMessage());
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    /**
     * 报名
     *
     * @param recruitSignUpSaveReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/8 14:00
     */
    public void editSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

        if (StringUtils.isBlank(recruitSignUpSaveReq.getId())) {
            throw new BusinessException("请选择报名ID信息！");
        }

        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpReq.setPostId(recruitSignUpSaveReq.getPostId());
        recruitSignUpReq.setCancelStatus(2);
        RecruitSignUp oldRecruitSignUp = getBase(recruitSignUpReq);
        if (oldRecruitSignUp != null) {
            if (!oldRecruitSignUp.getId().equals(recruitSignUpSaveReq.getId())) {
                throw new BusinessException("不可以重复报名！");
            }
        }

        if(oldRecruitSignUp.getStatus().equals(2)||oldRecruitSignUp.getStatus().equals(3))
        {
            throw new BusinessException("已经在审核中不可以进行修改！");
        }


        checkSignUp(recruitSignUpSaveReq);
        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        if (recruitPlanPost == null) {
            throw new BusinessException("岗位选择不正确！");
        }



        Date nowTime = new Date();
        RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);


        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());

        RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

        recruitSignUp.setGender(sex.getValue());

        BeanUtil.copyProperties(recruitSignUpSaveReq, recruitSignUp);
        recruitSignUp.setSignUpTime(nowTime);
        recruitSignUp.setSignupCode(getMaxCode(recruitPlanPost.getPostCode(), recruitPlanPost.getId()));
        recruitSignUp.setStatus(RecruitSignUpStatusEnuma.UNTREATED.getKey());

        if (recruitProcessConfig.getFirstAudit().equals(1)) {
            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey());

        } else if (recruitProcessConfig.getReviewAudit().equals(1)) {
            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.REVIEWAUDIT.getKey());
        }
        //    recruitSignUp.setOtherData(JSON.toJSONString(recruitSignUpSaveReq));
        recruitSignUp.setGraduatesType(recruitSignUpSaveReq.getIsGraduates().equals("是")?2:1);
        recruitSignUpMapper.updateByPrimaryKeySelective(recruitSignUp);
    }
    
    public void newEditSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

        if (StringUtils.isBlank(recruitSignUpSaveReq.getId())) {
            throw new BusinessException("请选择报名ID信息！");
        }

        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
        recruitSignUpReq.setPostId(recruitSignUpSaveReq.getPostId());
        recruitSignUpReq.setCancelStatus(2);
        RecruitSignUp oldRecruitSignUp = getBase(recruitSignUpReq);
        if (oldRecruitSignUp != null) {
            if (!oldRecruitSignUp.getId().equals(recruitSignUpSaveReq.getId())) {
                throw new BusinessException("不可以重复报名！");
            }
        }

        if(oldRecruitSignUp.getStatus().equals(2)||oldRecruitSignUp.getStatus().equals(3))
        {
            throw new BusinessException("已经在审核中不可以进行修改！");
        }


        newCheckSignUp(recruitSignUpSaveReq);
        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        if (recruitPlanPost == null) {
            throw new BusinessException("岗位选择不正确！");
        }



        Date nowTime = new Date();
        RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);


        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());

//        RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

        recruitSignUp.setGender(sex.getValue());

        BeanUtil.copyProperties(recruitSignUpSaveReq, recruitSignUp);
        recruitSignUp.setSignUpTime(nowTime);
        recruitSignUp.setSignupCode(getMaxCode(recruitPlanPost.getPostCode(), recruitPlanPost.getId()));
        recruitSignUp.setStatus(RecruitSignUpStatusEnuma.UNTREATED.getKey());

/*//        if (recruitProcessConfig.getFirstAudit().equals(1)) {
//            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey());
//
//        } else if (recruitProcessConfig.getReviewAudit().equals(1)) {
//            recruitSignUp.setStatus(RecruitSignUpStatusEnuma.REVIEWAUDIT.getKey());
//        }
*/        //    recruitSignUp.setOtherData(JSON.toJSONString(recruitSignUpSaveReq));
//        recruitSignUp.setGraduatesType(recruitSignUpSaveReq.getIsGraduates().equals("是")?2:1);
        recruitSignUpMapper.updateByPrimaryKeySelective(recruitSignUp);
    }
    
    private void newCheckSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

        if (StringUtils.isBlank(recruitSignUpSaveReq.getName())) {
            throw new BusinessException("姓名不能为空！");

        }
        
		if (!"1".equals(recruitSignUpSaveReq.getTalentPool())) {
			if (StringUtils.isBlank(recruitSignUpSaveReq.getPostId())) {
				throw new BusinessException("岗位不能为空！");
			}
		}
     

        if (StringUtils.isBlank(recruitSignUpSaveReq.getIdentityCard())) {
            throw new BusinessException("身份证不能为空！");

        }
        
        if (!IDCardUtil.isIdentity(recruitSignUpSaveReq.getIdentityCard())) {
            throw new BusinessException("身份证号码不正确！");
        }

    /*    if (recruitSignUpSaveReq.getGraduationTime()==null) {
            throw new BusinessException("毕业时间不能为空！");

        }*/
        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        
        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());
        Timestamp birthday = IDCardUtil.getBirthdayFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());
        //出生日期
        String extractYearMonthDayOfIdCard = DateUtils.extractYearMonthDayOfIdCard(recruitSignUpSaveReq.getIdentityCard());
//        Integer year = DateUtil.year(birthday);
//        String currBirthDay = year + "";

        if(!"1".equals(recruitSignUpSaveReq.getTalentPool())) {
          
            if (recruitPlanPost == null) {
                throw new BusinessException("岗位选择不正确！");
            }
            RecruitPlanReq recruitPlanReq = new RecruitPlanReq();
            recruitPlanReq.setId(recruitPlanPost.getPlanId());
            RecruitPlan recruitPlan = recruitPlanService.getBase(recruitPlanReq);
            if (recruitPlan == null) {
                throw new BusinessException("暂无招聘计划！");
            }
            Date nowTime = new Date();
            if (DateUtil.offsetDay(recruitPlan.getEndTime(),1).getTime() < nowTime.getTime()) {
                throw new BusinessException("报名已结束！");
            }

            if (recruitPlan.getBeginTime().getTime() > nowTime.getTime()) {
                throw new BusinessException("报名还未开始！");
            }
            
//     	   RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

    /*       if (recruitProcessConfig.getLimitAgeDay().equals(1)) {
               currBirthDay = currBirthDay + "-01-01";
           } else if (recruitProcessConfig.getLimitAgeDay().equals(2)) {
               currBirthDay = currBirthDay + "-" + DateUtil.format(recruitPlan.getEndTime(), "MM-dd");
           }*/
           int age = DateUtil.ageOfNow(extractYearMonthDayOfIdCard);  //获取年龄

           if (age > recruitPlanPost.getLimitAge()) {
                   throw new BusinessException("岗位年龄上限" + recruitPlanPost.getLimitAge()+"岁");
           }

           if (recruitPlanPost.getGender().equals(0) == false) {
               if (sex.getValue().equals(recruitPlanPost.getGender()) == false) {
                   throw new BusinessException("岗位性别要求必须是" + RecruitGenderEnum.getValByKey(recruitPlanPost.getGender()));
               }
           }
            
        }


    
        
        //	TODO 添加验证学历信息
        
        //	TODO 添加工作经历认证


        /*     if (recruitPlanPost.getGraduate().equals(2)) {

            if(!("是".equals(recruitSignUpSaveReq.getIsGraduates())))
            {
                throw new BusinessException("岗位要求只适合应届毕业生！");
            }
        }*/
        

      /*  if(recruitSignUpSaveReq.getEducationId()==null)
        {
            throw new BusinessException("学历必须填写！");

        }*/
        
        //验证最低学历
  /*      if (recruitSignUpSaveReq.getEducationId() < recruitPlanPost.getMinEducation()) {
            List<DictItemResp> dictItemRespList = recruitPlanService.getEducationList();
            for (DictItemResp dictItemResp : dictItemRespList) {
                if (dictItemResp.getItemNameValue().equals(String.valueOf(recruitPlanPost.getMinEducation()))) {
                    throw new BusinessException("最低学历要求不满足要求" + dictItemResp.getItemName());
                }
            }
        }*/
    }


    private void checkSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {

        if (StringUtils.isBlank(recruitSignUpSaveReq.getName())) {
            throw new BusinessException("姓名不能为空！");

        }
        if (StringUtils.isBlank(recruitSignUpSaveReq.getPostId())) {
            throw new BusinessException("岗位不能为空！");

        }

        if (StringUtils.isBlank(recruitSignUpSaveReq.getIdentityCard())) {
            throw new BusinessException("身份证不能为空！");

        }
        if (recruitSignUpSaveReq.getGraduationTime()==null) {
            throw new BusinessException("毕业时间不能为空！");

        }

        RecruitPlanPostReq recruitPlanPostReq = new RecruitPlanPostReq();
        recruitPlanPostReq.setId(recruitSignUpSaveReq.getPostId());
        RecruitPlanPost recruitPlanPost = recruitPlanPostService.getBase(recruitPlanPostReq);
        if (recruitPlanPost == null) {
            throw new BusinessException("岗位选择不正确！");
        }

        RecruitPlanReq recruitPlanReq = new RecruitPlanReq();
        recruitPlanReq.setId(recruitPlanPost.getPlanId());
        RecruitPlan recruitPlan = recruitPlanService.getBase(recruitPlanReq);
        if (recruitPlan == null) {
            throw new BusinessException("暂无招聘计划！");
        }

        Date nowTime = new Date();


        if (DateUtil.offsetDay(recruitPlan.getEndTime(),1).getTime() < nowTime.getTime()) {
            throw new BusinessException("报名已结束！");
        }

        if (recruitPlan.getBeginTime().getTime() > nowTime.getTime()) {
            throw new BusinessException("报名还未开始！");
        }


        if (!IDCardUtil.isIdentity(recruitSignUpSaveReq.getIdentityCard())) {
            throw new BusinessException("身份证号码不正确！");
        }


        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());
        Timestamp birthday = IDCardUtil.getBirthdayFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());

        Integer year = DateUtil.year(birthday);

        String currBirthDay = year + "";


        RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();

        if (recruitProcessConfig.getLimitAgeDay().equals(1)) {
            currBirthDay = currBirthDay + "-01-01";
        } else if (recruitProcessConfig.getLimitAgeDay().equals(2)) {
            currBirthDay = currBirthDay + "-" + DateUtil.format(recruitPlan.getEndTime(), "MM-dd");
        }

        int age = DateUtil.ageOfNow(currBirthDay);

        if (age > recruitPlanPost.getLimitAge()) {
                throw new BusinessException("岗位年龄上限" + recruitPlanPost.getLimitAge()+"岁");
        }

        if (recruitPlanPost.getGender().equals(0) == false) {
            if (sex.getValue().equals(recruitPlanPost.getGender()) == false) {
                throw new BusinessException("岗位性别要求必须是" + RecruitGenderEnum.getValByKey(recruitPlanPost.getGender()));
            }
        }
        if (recruitPlanPost.getEducationType().equals(RecruitEducationTypeEnum.Y.getKey())) {
            if(recruitSignUpSaveReq.getEducationType()==null)
            {
                throw new BusinessException("岗位学历必须填写！");
            }
            if (recruitSignUpSaveReq.getEducationType().equals(1)) {
                throw new BusinessException("岗位学历要求必须是全日制！");
            }
        }


        if (recruitPlanPost.getGraduate().equals(2)) {

            if(!("是".equals(recruitSignUpSaveReq.getIsGraduates())))
            {
                throw new BusinessException("岗位要求只适合应届毕业生！");
            }

//            if (DateUtil.year(recruitSignUpSaveReq.getGraduationTime()) < DateUtil.year(nowTime)) {
//                throw new BusinessException("岗位要求只适合应届毕业生！");
//            }
        }

        if(recruitSignUpSaveReq.getEducationId()==null)
        {
            throw new BusinessException("学历必须填写！");

        }

        if (recruitSignUpSaveReq.getEducationId() < recruitPlanPost.getMinEducation()) {
            List<DictItemResp> dictItemRespList = recruitPlanService.getEducationList();
            for (DictItemResp dictItemResp : dictItemRespList) {
                if (dictItemResp.getItemNameValue().equals(String.valueOf(recruitPlanPost.getMinEducation()))) {
                    throw new BusinessException("最低学历要求不满足要求" + dictItemResp.getItemName());
                }
            }
        }
    }

    /**
     * @description: 获取最大编码
     * @return: void
     * @author: liyuan
     * @createTime: 2021/7/22 15:32
     */
    public String getMaxCode(String postCode, String postId) {
        Example example = new Example(RecruitSignUp.class);
        example.createCriteria()
                .andEqualTo("postId", postId)
                .andCondition("LENGTH(signup_code)=" + (postCode.length() + 4))
                .andCondition("SUBSTR(signup_code,1," + postCode.length() + ")=" + "'"+postCode+"'");
        example.setOrderByClause(" signup_code desc LIMIT 1 ");

        RecruitSignUp recruitSignUp = recruitSignUpMapper.selectOneByExample(example);

        String atomicCode = "1";
        if (recruitSignUp != null) {
            String v = recruitSignUp.getSignupCode().substring(postCode.length(), postCode.length() + 4);
            if (Validator.isNumber(v)) {
                atomicCode = Integer.parseInt(v) + 1 + "";
            } else {
                atomicCode = String.valueOf(1);
            }
        }
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(postCode);
        for (int i = 0; i < (4 - atomicCode.length()); i++) {
            stringBuilder.append("0");
        }
        stringBuilder.append(atomicCode);
        return stringBuilder.toString();
    }

    @Override
    /**
     * 获取基础数据
     *
     * @param recruitSignUpReq
     * @return cn.trasen.hrms.zp.model.RecruitSignUp
     * <AUTHOR>
     * @date 2021/12/8 18:13
     */
    public RecruitSignUp getBase(RecruitSignUpReq recruitSignUpReq) {
        Example example = new Example(RecruitSignUp.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isBlank(recruitSignUpReq.getIdentityCard()) == false) {
            criteria.andEqualTo("identityCard", recruitSignUpReq.getIdentityCard());
        }
        if (StringUtils.isBlank(recruitSignUpReq.getId()) == false) {
            criteria.andEqualTo("id", recruitSignUpReq.getId());
        }
        if (StringUtils.isBlank(recruitSignUpReq.getPostId()) == false) {
            criteria.andEqualTo("postId", recruitSignUpReq.getPostId());
        }
        if (StringUtils.isBlank(recruitSignUpReq.getName()) == false) {
            criteria.andEqualTo("name", recruitSignUpReq.getName());
        }
        if (recruitSignUpReq.getCancelStatus() != null) {
            criteria.andEqualTo("cancelStatus", recruitSignUpReq.getCancelStatus());
        }
        if (recruitSignUpReq.getStatus() != null) {
            criteria.andEqualTo("status", recruitSignUpReq.getStatus());
        }

        example.setOrderByClause(" signup_code desc LIMIT 1 ");
        return recruitSignUpMapper.selectOneByExample(example);
    }
    
    public RecruitSignUp getBase2(RecruitSignUpReq recruitSignUpReq) {
        Example example = new Example(RecruitSignUp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo(Contants.IS_DELETED_FIELD,Contants.IS_DELETED_FALSE);
        
        if (StringUtils.isBlank(recruitSignUpReq.getIdentityCard()) == false) {
            criteria.andEqualTo("identityCard", recruitSignUpReq.getIdentityCard());
        }
        criteria.andNotEqualTo("talentPool", "1");
        example.setOrderByClause(" signup_code desc LIMIT 1 ");
        return recruitSignUpMapper.selectOneByExample(example);
    }


    @Override
    /**
     * 获取基础列表
     * @param recruitSignUpListReq
     * @return java.util.List<cn.trasen.hrms.zp.model.RecruitSignUp>
     * <AUTHOR>
     * @date 2021/12/13 18:15
     */
    public List<RecruitSignUp> getBaseList(RecruitSignUpListReq recruitSignUpListReq) {
        Example example = new Example(RecruitSignUp.class);
        Example.Criteria criteria = example.createCriteria();

        if (CollectionUtils.isEmpty(recruitSignUpListReq.getIdList()) == false) {
            criteria.andIn("id", recruitSignUpListReq.getIdList());
        }


        if (StringUtils.isBlank(recruitSignUpListReq.getIdentityCard()) == false) {
            criteria.andEqualTo("identityCard", recruitSignUpListReq.getIdentityCard());
        }

        if (StringUtils.isBlank(recruitSignUpListReq.getPostId()) == false) {
            criteria.andEqualTo("postId", recruitSignUpListReq.getPostId());
        }
        if (StringUtils.isBlank(recruitSignUpListReq.getName()) == false) {
            criteria.andEqualTo("name", recruitSignUpListReq.getName());
        }
        example.setOrderByClause(" signup_code desc ");
        return recruitSignUpMapper.selectByExample(example);
    }

@Override
    /**
    * 获取详情
    * @param recruitSignUpReq
    * @return cn.trasen.hrms.zp.bean.RecruitSignUpRes
    * <AUTHOR>
    * @date 2022/1/7 13:51
    */
    public RecruitSignUpRes get(RecruitSignUpReq recruitSignUpReq)
    {
        RecruitSignUpListReq recruitSignUpListReq=new RecruitSignUpListReq();
        recruitSignUpListReq.setId(recruitSignUpReq.getId());
        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
        return BeanUtil.copyProperties(recruitSignUpList.get(0),RecruitSignUpRes.class);
    }

        @Override
    /**
     * 获取基础数据
     *
     * @param recruitSignUpListReq
     * @return cn.trasen.hrms.zp.model.RecruitSignUp
     * <AUTHOR>
     * @date 2021/12/8 18:13
     */
    public DataSet<RecruitSignUpListRes> getList(Page page, RecruitSignUpListReq recruitSignUpListReq) {


//            RecruitAdmin firstAuditRecruitAdmin=null;
//            RecruitAdmin reviewAuditRecruitAdmin=null;
//
//            if(recruitSignUpListReq.getUse()!=null) {
//                if (recruitSignUpListReq.getUse().equals(2)) {
//                    if (recruitSignUpListReq.getSignUpStatus() != null) {
//                        if (recruitSignUpListReq.getSignUpStatus().equals(RecruitSignUpStatusEnuma.UNTREATED.getKey())) {
//                            RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
//                            recruitAdminReq.setManageType(1);
//                            recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
//                            firstAuditRecruitAdmin = recruitAdminService.getBase(recruitAdminReq);
//
//                        } else if (recruitSignUpListReq.getSignUpStatus().equals(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey())) {
//                            RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
//                            recruitAdminReq.setManageType(2);
//                            recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
//                            reviewAuditRecruitAdmin = recruitAdminService.getBase(recruitAdminReq);
//
//                        }
//                    }
//                }
//            }


        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(page, recruitSignUpListReq);
        initData(recruitSignUpList);
        int i = 1;
        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpList) {
//            if(recruitSignUpListReq.getUse()!=null) {
//                if (recruitSignUpListReq.getUse().equals(2)) {
//                    if (recruitSignUpListRes.getStatus().equals(RecruitSignUpStatusEnuma.UNTREATED.getKey())) {
//                        if(firstAuditRecruitAdmin!=null) {
//                            recruitSignUpListRes.setPower(RecruitPowerEnum.FIRSTAUDIT.getKey()+"");
//                        }
//                    } else if (recruitSignUpListRes.getStatus().equals(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey())) {
//                        if(reviewAuditRecruitAdmin!=null) {
//                            recruitSignUpListRes.setPower(RecruitPowerEnum.REVIEW_AUDIT.getKey()+"");
//                        }
//                    }
//                }
//            }
            recruitSignUpListRes.setSequence((page.getPageNo() - 1) * page.getPageSize() + i);
            i++;
        }
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), recruitSignUpList);
    }


    /**
     * 初始化数据
     *
     * @param recruitSignUpListResList
     * @return void
     * <AUTHOR>
     * @date 2021/12/20 14:34
     */
    private void initData(List<RecruitSignUpListRes> recruitSignUpListResList) {
        if (CollectionUtils.isEmpty(recruitSignUpListResList)) {
            return;
        }
        Date nowTime = new Date();
        List<DictItemResp> dictItemRespList = recruitPlanService.getEducationList();


        Set<String> empCodeList = new HashSet<>();

        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpListResList) {
            if (StringUtils.isBlank(recruitSignUpListRes.getFirstAuditEmpCode()) == false) {
                empCodeList.add(recruitSignUpListRes.getFirstAuditEmpCode());
            }
            if (StringUtils.isBlank(recruitSignUpListRes.getReviewAuditEmpCode()) == false) {
                empCodeList.add(recruitSignUpListRes.getReviewAuditEmpCode());
            }
        }

        List<EmployeeResp> employeeRespList = new ArrayList<>();
        if (empCodeList.size() > 0) {
            employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(empCodeList)).getObject();
        }

        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpListResList) {
            for (DictItemResp dictItemResp : dictItemRespList) {
                if (dictItemResp.getItemNameValue().equals(recruitSignUpListRes.getEducationId())) {
                    recruitSignUpListRes.setEducationLable(dictItemResp.getItemName());
                    break;
                }
            }

            Timestamp birthday;
            try {
                birthday = IDCardUtil.getBirthdayFromPersonIDCode(recruitSignUpListRes.getIdentityCard());
                int age = DateUtil.ageOfNow(birthday);
                recruitSignUpListRes.setAge(age);
            } catch (Throwable e) {
                e.printStackTrace();
            }



            recruitSignUpListRes.setGraduateLable(GraduatesTypeEnum.getValByKey(recruitSignUpListRes.getGraduatesType()));
            recruitSignUpListRes.setGraduateLable(recruitSignUpListRes.getFreshGraduate());
//            if (DateUtil.year(recruitSignUpListRes.getGraduationTime()) >= DateUtil.year(nowTime)) {
//                recruitSignUpListRes.setGraduateLable("是");
//            }



            recruitSignUpListRes.setGenderLable(RecruitGenderEnum.getValByKey(recruitSignUpListRes.getGender()));
            recruitSignUpListRes.setEducationTypeLable(RecruitEducationTypeEnum.getValByKey(recruitSignUpListRes.getEducationType()));
            recruitSignUpListRes.setFailStage(1);
            recruitSignUpListRes.setFailStageLable("初审");

            if (recruitSignUpListRes.getReviewAuditTime() != null) {
                recruitSignUpListRes.setFailStage(2);
                recruitSignUpListRes.setFailStageLable("复审");

            }

            if (empCodeList.size() > 0) {
                if (StringUtils.isBlank(recruitSignUpListRes.getFirstAuditEmpCode()) == false) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(recruitSignUpListRes.getFirstAuditEmpCode())) {
                            RecruitSignUpListRes.Emp emp = new RecruitSignUpListRes.Emp();
                            emp.setEmpCode(employeeResp.getEmployeeNo());
                            emp.setEmpId(employeeResp.getEmployeeId());
                            emp.setEmpName(employeeResp.getEmployeeName());
                            emp.setEmpMobile(employeeResp.getPhoneNumber());
                            recruitSignUpListRes.setFirstAuditEmp(emp);
                            break;
                        }
                    }
                }

                if (StringUtils.isBlank(recruitSignUpListRes.getReviewAuditEmpCode()) == false) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(recruitSignUpListRes.getReviewAuditEmpCode())) {
                            RecruitSignUpListRes.Emp emp = new RecruitSignUpListRes.Emp();
                            emp.setEmpCode(employeeResp.getEmployeeNo());
                            emp.setEmpId(employeeResp.getEmployeeId());
                            emp.setEmpName(employeeResp.getEmployeeName());
                            emp.setEmpMobile(employeeResp.getPhoneNumber());
                            recruitSignUpListRes.setReviewAuditEmp(emp);
                            break;
                        }
                    }
                }
            }

        }
    }

    @Override
    /**
     * 报名查询
     *
     * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitReadSignUpProgressRes>
     * <AUTHOR>
     * @date 2021/12/9 17:36
     */
    public RecruitReadSignUpProgressRes readSignUpProgress(RecruitReadSignUpProgressReq recruitReadSignUpProgressReq) {
        RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
        recruitSignUpListReq.setIdentityCard(recruitReadSignUpProgressReq.getIdentityCard());
        recruitSignUpListReq.setPlanId(recruitReadSignUpProgressReq.getPlanId());
        recruitSignUpListReq.setName(recruitReadSignUpProgressReq.getName());
        recruitSignUpListReq.setCancelStatus(2);
        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
        if (CollectionUtils.isEmpty(recruitSignUpList)) {
            throw new BusinessException("未找到对应报名信息，请检查输入信息是否有误");
        }
        RecruitReadSignUpProgressRes recruitReadSignUpProgressRes = new RecruitReadSignUpProgressRes();

        RecruitReadSignUpProgressRes.SignUpInfo signUpInfo = new RecruitReadSignUpProgressRes.SignUpInfo();
        if (StringUtils.isBlank(recruitSignUpList.get(0).getOtherData()) == false) {
            signUpInfo.setSignUpData(JSON.parseObject(recruitSignUpList.get(0).getOtherData()));
        }

        recruitReadSignUpProgressRes.setSignUpInfo(signUpInfo);

        List<RecruitReadSignUpProgressRes.Post> postList = new ArrayList<>();
        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpList) {
            RecruitReadSignUpProgressRes.Post post = new RecruitReadSignUpProgressRes.Post();
            post.setPostId(recruitSignUpListRes.getPostId());
            post.setPostCode(recruitSignUpListRes.getPostCode());
            post.setPostName(recruitSignUpListRes.getPostName());
            
            //不通过
            RecruitReadSignUpProgressRes.Post.SignUpStage signUpStage = new RecruitReadSignUpProgressRes.Post.SignUpStage();
            signUpStage.setStatus(recruitSignUpListRes.getStatus());
            signUpStage.setRemarks(recruitSignUpListRes.getRemarks());
            post.setSignUpStage(signUpStage);
            
           
            if (recruitSignUpListRes.getStatus().equals(3)) {  
            	//笔试
                RecruitReadSignUpProgressRes.Post.ExamStage examStage = new RecruitReadSignUpProgressRes.Post.ExamStage();
                examStage.setStatus(recruitSignUpListRes.getExamStatus());
                examStage.setExamAddr(recruitSignUpListRes.getExamAddr());
                examStage.setExamContent(recruitSignUpListRes.getExamContent());
                examStage.setExamBeginTime(recruitSignUpListRes.getExamBeginTime());
                examStage.setExamEndTime(recruitSignUpListRes.getExamEndTime());
                examStage.setScore(recruitSignUpListRes.getExamScore());
                examStage.setScore2(recruitSignUpListRes.getExamScore2());
                examStage.setSeatNo(recruitSignUpListRes.getExamSeatNo());
                examStage.setExemptionStatus(recruitSignUpListRes.getExamExemptionStatus());
                post.setExamStage(examStage);
                //直接面试的 显示通过
                if (recruitSignUpListRes.getExamExemptionStatus() != null && recruitSignUpListRes.getExamExemptionStatus().equals(2)) {
                    recruitSignUpListRes.setExamStatus(2);
                }
                
                
                //面试
                if (recruitSignUpListRes.getExamStatus() != null && recruitSignUpListRes.getExamStatus().equals(2)) {
                    RecruitReadSignUpProgressRes.Post.InterviewStage interviewStage = new RecruitReadSignUpProgressRes.Post.InterviewStage();
                    interviewStage.setStatus(recruitSignUpListRes.getInterviewStatus());
                    interviewStage.setInterviewAddr(recruitSignUpListRes.getInterviewAddr());
                    interviewStage.setInterviewContent(recruitSignUpListRes.getInterviewContent());
                    interviewStage.setInterviewBeginTime(recruitSignUpListRes.getInterviewBeginTime());
                    interviewStage.setInterviewEndTime(recruitSignUpListRes.getInterviewEndTime());
                    interviewStage.setScore(recruitSignUpListRes.getInterviewScore());
                    interviewStage.setSeatNo(recruitSignUpListRes.getInterviewSeatNo());
                    interviewStage.setExemptionStatus(recruitSignUpListRes.getInterviewExemptionStatus());
                 
                    if (recruitSignUpListRes.getInterviewExemptionStatus() != null && recruitSignUpListRes.getInterviewExemptionStatus().equals(2)) {
                    	recruitSignUpListRes.setInterviewExemptionStatus(2);
                    }
                    post.setInterviewStage(interviewStage);
                    //处理面试的显示通过
                }
               //实操
                if (recruitSignUpListRes.getExamStatus() != null && recruitSignUpListRes.getExamStatus().equals(2)) {
                    RecruitReadSignUpProgressRes.Post.Manipulate manipulate = new RecruitReadSignUpProgressRes.Post.Manipulate();
                    manipulate.setStatus(recruitSignUpListRes.getManipulateStatus());
                    manipulate.setExamAddr(recruitSignUpListRes.getManipulateAddr());
                    manipulate.setExamContent(recruitSignUpListRes.getManipulateContent());
                    manipulate.setExamBeginTime(recruitSignUpListRes.getManipulateBeginTime());
                    manipulate.setExamEndTime(recruitSignUpListRes.getManipulateEndTime());
                    manipulate.setScore(recruitSignUpListRes.getManipulateScore());
                    manipulate.setSeatNo(recruitSignUpListRes.getManipulateSeatNo());
                    manipulate.setExemptionStatus(recruitSignUpListRes.getExamExemptionStatus());
                    
                    if (recruitSignUpListRes.getManipulateExemptionStatus() != null && recruitSignUpListRes.getManipulateExemptionStatus().equals(2)) {
                    	recruitSignUpListRes.setManipulateStatus(2);
                    }
                    
                    post.setManipulate(manipulate);
                }
            }
            postList.add(post);
        }
        recruitReadSignUpProgressRes.setPostList(postList);
        return recruitReadSignUpProgressRes;
    }

    @Override
    /**
     * 统计报名
     * @return cn.trasen.hrms.zp.bean.RecruitSignUpCountRes
     * <AUTHOR>
     * @date 2021/12/10 11:33
     */
    public RecruitSignUpCountRes signUpCount(RecruitSignUpCountReq recruitSignUpCountReq) {
        return recruitSignUpMapper.getSignupCount(recruitSignUpCountReq);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 复通过
     * @param recruitAuditReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 14:11
     */
    public void reviewAuditPass(RecruitAuditReq recruitAuditReq) {
        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setId(recruitAuditReq.getSignUpId());
        RecruitSignUp recruitSignUp = getBase(recruitSignUpReq);
        if (recruitSignUp == null) {
            throw new BusinessException("报名信息不存在！");
        }
        if (!(recruitSignUp.getStatus().equals(2))) {
            throw new BusinessException("该条信息已经被处理！");
        }
        
   //     ZpProcedure procedure = zpProcedureMapper.selectPorBySignupCode(recruitAuditReq.getSignUpId());
        
        //查询权限信息
     //   List<ZpJurisdiction> chushenAdminList = zpJurisdictionMapper.selectChushen();
        
      /*  RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
        recruitAdminReq.setManageType(2);
        recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
        RecruitAdmin recruitAdmin = recruitAdminService.getBase(recruitAdminReq);
        if (recruitAdmin == null) {
            throw new BusinessException("无权限操作！");
        }*/
        
        
        RecruitSignUp updateRecruitSignUp = new RecruitSignUp();
        BeanUtils.updateInitBean(updateRecruitSignUp);
        updateRecruitSignUp.setId(recruitSignUp.getId());
        updateRecruitSignUp.setRemarks(recruitAuditReq.getRemarks());
        updateRecruitSignUp.setReviewAuditTime(new Date());
        updateRecruitSignUp.setReviewAuditEmpCode(UserLoginService.getCurrentUserCode());
        updateRecruitSignUp.setStatus(RecruitSignUpStatusEnuma.REVIEWAUDIT.getKey());
        updateRecruitSignUp.setTalentPool("1");  //设置进入人才库
        recruitSignUpMapper.updateByPrimaryKeySelective(updateRecruitSignUp);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 出审通过
     * @param recruitAuditReq
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 14:11
     */
    public void firstAuditPass(RecruitAuditReq recruitAuditReq) {
        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setId(recruitAuditReq.getSignUpId());
        RecruitSignUp recruitSignUp = getBase(recruitSignUpReq);
        if (recruitSignUp == null) {
            throw new BusinessException("报名信息不存在！");
        }
        if (!(recruitSignUp.getStatus().equals(0))) {
            throw new BusinessException("该条信息已经被处理！");
        }
        
        // 复审的权限
     /*   RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
        recruitAdminReq.setManageType(1);
        recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
        RecruitAdmin recruitAdmin = recruitAdminService.getBase(recruitAdminReq);
        if (recruitAdmin == null) {
            throw new BusinessException("无权限操作！");
        }*/
        RecruitSignUp updateRecruitSignUp = new RecruitSignUp();
        BeanUtils.updateInitBean(updateRecruitSignUp);
        updateRecruitSignUp.setId(recruitSignUp.getId());
        updateRecruitSignUp.setRemarks(recruitAuditReq.getRemarks());
        updateRecruitSignUp.setFirstAuditTime(new Date());
        updateRecruitSignUp.setFirstAuditEmpCode(UserLoginService.getCurrentUserCode());

        updateRecruitSignUp.setStatus(RecruitSignUpStatusEnuma.FIRSTAUDIT.getKey());

      /*  RecruitProcessConfig recruitProcessConfig = recruitProcessConfigService.getBase();
        if (recruitProcessConfig.getReviewAudit().equals(1)) {
            updateRecruitSignUp.setStatus(RecruitSignUpStatusEnuma.REVIEWAUDIT.getKey());
        }*/
        recruitSignUpMapper.updateByPrimaryKeySelective(updateRecruitSignUp);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 取消
     *
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 13:33
     */
    public void cancel(RecruitCancelSignUpReq recruitCancelSignUpReq) {

        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setIdentityCard(recruitCancelSignUpReq.getIdentityCard());
        recruitSignUpReq.setId(recruitCancelSignUpReq.getSignupId());
        RecruitSignUp recruitSignUp = getBase(recruitSignUpReq);
        if (recruitSignUp.getStatus().equals(2) || recruitSignUp.getStatus().equals(3)) {
            throw new BusinessException("已经审核不允许取消");
        }
        RecruitSignUp updateRecruitSignUp = new RecruitSignUp();
        BeanUtils.updateInitBean(updateRecruitSignUp);
        updateRecruitSignUp.setRemarks("");
        updateRecruitSignUp.setCancelStatus(1);

        Example example = new Example(RecruitSignUp.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("identityCard", recruitCancelSignUpReq.getIdentityCard());
        criteria.andEqualTo("name", recruitCancelSignUpReq.getName());
        criteria.andEqualTo("id", recruitCancelSignUpReq.getSignupId());
        recruitSignUpMapper.updateByExampleSelective(updateRecruitSignUp, example);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    /**
     * 不通过
     *
     * @return void
     * <AUTHOR>
     * @date 2021/12/10 13:33
     */
    public void fail(RecruitAuditReq recruitAuditReq) {
        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
        recruitSignUpReq.setId(recruitAuditReq.getSignUpId());
        RecruitSignUp recruitSignUp = getBase(recruitSignUpReq);
        if (recruitSignUp == null) {
            throw new BusinessException("报名信息不存在！");
        }
        if (!(
                recruitSignUp.getStatus().equals(0) ||
                        recruitSignUp.getStatus().equals(2))
        ) {
            throw new BusinessException("该条信息已经被处理！");

        }

        RecruitSignUp updateRecruitSignUp = new RecruitSignUp();

     /*   if (recruitSignUp.getStatus().equals(2)) {
            updateRecruitSignUp.setReviewAuditEmpCode(UserLoginService.getCurrentUserCode());
            RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
            recruitAdminReq.setManageType(2);
            recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
            RecruitAdmin recruitAdmin = recruitAdminService.getBase(recruitAdminReq);
            if (recruitAdmin == null) {
                throw new BusinessException("无权限操作！");
            }
        } else if (recruitSignUp.getStatus().equals(0)) {
            updateRecruitSignUp.setFirstAuditEmpCode(UserLoginService.getCurrentUserCode());
            RecruitAdminReq recruitAdminReq = new RecruitAdminReq();
            recruitAdminReq.setManageType(1);
            recruitAdminReq.setEmpCode(UserLoginService.getCurrentUserCode());
            RecruitAdmin recruitAdmin = recruitAdminService.getBase(recruitAdminReq);
            if (recruitAdmin == null) {
                throw new BusinessException("无权限操作！");
            }
        }*/

        BeanUtils.updateInitBean(updateRecruitSignUp);
        updateRecruitSignUp.setId(recruitSignUp.getId());
        if (recruitSignUp.getStatus().equals(2)) {
            updateRecruitSignUp.setReviewAuditTime(new Date());
            updateRecruitSignUp.setReviewAuditEmpCode(UserLoginService.getCurrentUserCode());

        } else if (recruitSignUp.getStatus().equals(0)) {
            updateRecruitSignUp.setFirstAuditTime(new Date());
            updateRecruitSignUp.setFirstAuditEmpCode(UserLoginService.getCurrentUserCode());
        }
        updateRecruitSignUp.setRemarks(recruitAuditReq.getRemarks());
        updateRecruitSignUp.setStatus(RecruitSignUpStatusEnuma.N.getKey());
        recruitSignUpMapper.updateByPrimaryKeySelective(updateRecruitSignUp);
    }


    @Override
/**
 * 获取统计
 * @param recruitPlanCountReq
 * @return java.util.List<cn.trasen.hrms.zp.bean.RecruitSignUpListRes>
 * <AUTHOR>
 * @date 2021/12/9 17:45
 */
    public RecruitPlanCountRes getPlanCount(RecruitPlanCountReq recruitPlanCountReq) {
        return recruitSignUpMapper.getPlanCount(recruitPlanCountReq);
    }


    /**
     * 发送短信
     *
     * @return java.lang.Void
     * <AUTHOR>
     * @date 2021/12/28 16:43
     */
    @Override
    public void sendSMS(RecruitSendSMSReq recruitSendSMSReq) {

        ///mobilePhone=13650890511,18613953296&content=测试数据&timing=0&isDraft=0

        RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
        recruitSignUpListReq.setIdList(recruitSendSMSReq.getSignupIdList());
        List<RecruitSignUp> recruitSignUpList = getBaseList(recruitSignUpListReq);

        List<String> mobilePhoneList = new ArrayList<>();
        for (RecruitSignUp recruitSignUp : recruitSignUpList) {
            if (StringUtils.isBlank(recruitSignUp.getMobile()) == false) {
                mobilePhoneList.add(recruitSignUp.getMobile());
            }
        }
        MessageInternalReq messageInternalReq = new MessageInternalReq();
        messageInternalReq.setIsDraft("0");
        messageInternalReq.setTiming("0");
        messageInternalReq.setContent(recruitSendSMSReq.getContent());
        messageInternalReq.setMobilePhone(StringUtils.join(mobilePhoneList, ","));
        messageInternalFeignService.sendMessage(messageInternalReq);
    }


	@Override
	public void newFastSignUp(FastSignUpReq fastSignUpReq) throws Throwable {
		   RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
	        recruitSignUpListReq.setIdentityCard(fastSignUpReq.getIdentityCard());
	        recruitSignUpListReq.setPlanId(fastSignUpReq.getPlanId());
	        recruitSignUpListReq.setName(fastSignUpReq.getName());
	        recruitSignUpListReq.setCancelStatus(2);
	        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
	        if (CollectionUtils.isEmpty(recruitSignUpList)) {
	            throw new BusinessException("未找到对应报名信息，请检查输入信息是否有误");
	        }
	        RecruitSignUpListRes recruitSignUpListRes = recruitSignUpList.get(0);

	        RecruitSignUpSaveReq recruitSignUpSaveReq;
	        recruitSignUpSaveReq = JSON.parseObject(recruitSignUpListRes.getOtherData(), RecruitSignUpSaveReq.class);
	        recruitSignUpSaveReq.setOtherData(recruitSignUpListRes.getOtherData());
	        recruitSignUpSaveReq.setPostId(fastSignUpReq.getPostId());
	        newSignUp(recruitSignUpSaveReq);
	}


	@Override
	public void newBatchEditSignUp(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {
		 RecruitSignUpListReq recruitSignUpListReq = new RecruitSignUpListReq();
	        recruitSignUpListReq.setName(recruitSignUpSaveReq.getName());
	        recruitSignUpListReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
	        recruitSignUpListReq.setPlanId(recruitSignUpSaveReq.getPlanId());
	        recruitSignUpListReq.setCancelStatus(2);
	        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getList(recruitSignUpListReq);
	        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpList) {

	            JSONObject jsonObject=JSON.parseObject(recruitSignUpSaveReq.getOtherData());
	            jsonObject.put("postId",recruitSignUpListRes.getPostId());
	            recruitSignUpSaveReq.setOtherData(JSON.toJSONString(jsonObject));
	            recruitSignUpSaveReq.setPostId(recruitSignUpListRes.getPostId());
	            recruitSignUpSaveReq.setId(recruitSignUpListRes.getId());
	            try {
	            	newEditSignUp(recruitSignUpSaveReq);
	            } catch (BusinessException businessException) {
	                throw new BusinessException(recruitSignUpListRes.getPostName() + " " + businessException.getMessage());
	            }
	        }
		
	}
	
    private void initTalentPooData(List<RecruitSignUpListRes> recruitSignUpListResList) {
        if (CollectionUtils.isEmpty(recruitSignUpListResList)) {
            return;
        }
//        Date nowTime = new Date();
        List<DictItemResp> dictItemRespList = recruitPlanService.getEducationList();

        Set<String> empCodeList = new HashSet<>();

        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpListResList) {
            if (StringUtils.isBlank(recruitSignUpListRes.getFirstAuditEmpCode()) == false) {
                empCodeList.add(recruitSignUpListRes.getFirstAuditEmpCode());
            }
            if (StringUtils.isBlank(recruitSignUpListRes.getReviewAuditEmpCode()) == false) {
                empCodeList.add(recruitSignUpListRes.getReviewAuditEmpCode());
            }
        }

        List<EmployeeResp> employeeRespList = new ArrayList<>();
        if (empCodeList.size() > 0) {
            employeeRespList = hrmsEmployeeFeignService.getEmployeeDetailByCodes(new ArrayList<>(empCodeList)).getObject();
        }

        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpListResList) {
            
        	for (DictItemResp dictItemResp : dictItemRespList) {  //处理学历类型
                if (dictItemResp.getItemNameValue().equals(recruitSignUpListRes.getEducationId())) {
                    recruitSignUpListRes.setEducationLable(dictItemResp.getItemName());
                    break;
                }
            }

            Timestamp birthday;
            try {
                birthday = IDCardUtil.getBirthdayFromPersonIDCode(recruitSignUpListRes.getIdentityCard());
                int age = DateUtil.ageOfNow(birthday);
                recruitSignUpListRes.setAge(age);
            } catch (Throwable e) {
                e.printStackTrace();
            }

            recruitSignUpListRes.setGraduateLable(GraduatesTypeEnum.getValByKey(recruitSignUpListRes.getGraduatesType()));

            recruitSignUpListRes.setGenderLable(RecruitGenderEnum.getValByKey(recruitSignUpListRes.getGender()));
            recruitSignUpListRes.setEducationTypeLable(RecruitEducationTypeEnum.getValByKey(recruitSignUpListRes.getEducationType()));
            recruitSignUpListRes.setFailStage(1);
            recruitSignUpListRes.setFailStageLable("初审");

            if (recruitSignUpListRes.getReviewAuditTime() != null) {
                recruitSignUpListRes.setFailStage(2);
                recruitSignUpListRes.setFailStageLable("复审");

            }

            if (empCodeList.size() > 0) {
                if (StringUtils.isBlank(recruitSignUpListRes.getFirstAuditEmpCode()) == false) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(recruitSignUpListRes.getFirstAuditEmpCode())) {
                            RecruitSignUpListRes.Emp emp = new RecruitSignUpListRes.Emp();
                            emp.setEmpCode(employeeResp.getEmployeeNo());
                            emp.setEmpId(employeeResp.getEmployeeId());
                            emp.setEmpName(employeeResp.getEmployeeName());
                            emp.setEmpMobile(employeeResp.getPhoneNumber());
                            recruitSignUpListRes.setFirstAuditEmp(emp);
                            break;
                        }
                    }
                }

                if (StringUtils.isBlank(recruitSignUpListRes.getReviewAuditEmpCode()) == false) {
                    for (EmployeeResp employeeResp : employeeRespList) {
                        if (employeeResp.getEmployeeNo().equals(recruitSignUpListRes.getReviewAuditEmpCode())) {
                            RecruitSignUpListRes.Emp emp = new RecruitSignUpListRes.Emp();
                            emp.setEmpCode(employeeResp.getEmployeeNo());
                            emp.setEmpId(employeeResp.getEmployeeId());
                            emp.setEmpName(employeeResp.getEmployeeName());
                            emp.setEmpMobile(employeeResp.getPhoneNumber());
                            recruitSignUpListRes.setReviewAuditEmp(emp);
                            break;
                        }
                    }
                }
            }

        }
    }


	@Override
	public DataSet<RecruitSignUpListRes> getTalentPoolList(Page page, RecruitSignUpListReq recruitSignUpListReq) {
            //根据当前登录账号机构编码过滤查询数据
            recruitSignUpListReq.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
	        List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getTalentPoolList(page, recruitSignUpListReq);
	        
	        List<DictItemResp> dictItemRespList = dictItemFeignService.getDictItemByTypeCode("HRMS_RCLX").getObject();
	        initTalentPooData(recruitSignUpList);
	        int i = 1;
	        for (RecruitSignUpListRes recruitSignUpListRes : recruitSignUpList) {
	        	
	        	//处理人才类型
	        	for (DictItemResp dictItemResp : dictItemRespList) {  //处理学历类型
	                if (dictItemResp.getItemCode().equals(recruitSignUpListRes.getRclx())) {
	                	recruitSignUpListRes.setRclxLable(dictItemResp.getItemName());
	                    break;
	                }
	            }
	        	//处理笔试平均分
	        	if(recruitSignUpListRes != null && recruitSignUpListRes.getExamScore() != null 
	        			&& recruitSignUpListRes.getExamScore2() != null 
	        			&& recruitSignUpListRes.getExamScore2() >=1 ) {  
	        		double score = (recruitSignUpListRes.getExamScore() + recruitSignUpListRes.getExamScore2()) / 2;
	        		recruitSignUpListRes.setExamScore(score);
	        		
	        	}
	        	
	            recruitSignUpListRes.setSequence((page.getPageNo() - 1) * page.getPageSize() + i);
	            i++;
	        }
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), recruitSignUpList);
	}
	
	
	
	//导出人才库 
	
	
	//修改人才类型
	
	//录入到员工档案
	


	@Override
	public int deleteTalentPool(String id) {
		Assert.hasText(id, "ID不能为空.");
		RecruitSignUp record = new RecruitSignUp();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
		}
		return recruitSignUpMapper.updateByPrimaryKeySelective(record);
		
	}


	@Override
	public int updateRck(RecruitSignUpSaveReq recruitSignUpSaveReq) throws Throwable {
			newCheckSignUp(recruitSignUpSaveReq);
		    RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);
	        RecruitSignUpReq recruitSignUpReq = new RecruitSignUpReq();
	        recruitSignUpReq.setIdentityCard(recruitSignUpSaveReq.getIdentityCard());
	        recruitSignUpReq.setPostId(recruitSignUpSaveReq.getPostId());
	        IDCardUtil.Sex sex = IDCardUtil.getGenderFromPersonIDCode(recruitSignUpSaveReq.getIdentityCard());
	        recruitSignUp.setGender(sex.getValue());
	        BeanUtil.copyProperties(recruitSignUpSaveReq, recruitSignUp);
	        recruitSignUp.setStatus(RecruitSignUpStatusEnuma.UNTREATED.getKey());
	        recruitSignUp.setSignupCode(null); //报名id不改
//	        recruitSignUp.setGraduatesType(recruitSignUpSaveReq.getIsGraduates().equals("是")?2:1);
		return recruitSignUpMapper.updateByPrimaryKeySelective(recruitSignUp);
	}

	
	//批量修改人才类型
	@Override
	public void updateRclx(List<RclxReq> list) {
		list.forEach(item->{
			//先查询出数据
			RecruitSignUp oldBean = recruitSignUpMapper.selectByPrimaryKey(item.getId());
			
			String otherData = oldBean.getOtherData();
			JSONObject parseObject = JSONObject.parseObject(otherData);
			parseObject.put("rclx", item.getRclx());
			RecruitSignUp recruitSignUp = BeanUtils.addInitAnonymousBean(RecruitSignUp.class);
			recruitSignUp.setId(item.getId());
			recruitSignUp.setRclx(item.getRclx());
			recruitSignUp.setOtherData(JSON.toJSONString(parseObject));
			recruitSignUp.setUpdateDate(new Date());
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				recruitSignUp.setUpdateUser(user.getUsercode());
			}
			recruitSignUpMapper.updateByPrimaryKeySelective(recruitSignUp);
			
			
		});
		
	}


	@Override
	public List<RecruitSignUpListRes> getExporttList(Page page,RecruitSignUpListReq recruitSignUpListRes) {
		  List<RecruitSignUpListRes> recruitSignUpList = recruitSignUpMapper.getTalentPoolList(page, recruitSignUpListRes);
	        
	        List<DictItemResp> dictItemRespList = dictItemFeignService.getDictItemByTypeCode("HRMS_RCLX").getObject();
	        initTalentPooData(recruitSignUpList);
	        int i = 1;
	        for (RecruitSignUpListRes res : recruitSignUpList) {
	        	
	        	if(null != res.getExamStatus() && 2 == res.getExamStatus()) {  //笔试
	        		res.setExamStatusLable("是");
	        	}else {
	        		res.setExamStatusLable("否");
	        	}
	        	//面试
	        	if(null != res.getInterviewStatus() && 2 == res.getInterviewStatus()) {  //笔试
	        		res.setInterviewStatusLable("是");
	        	}else {
	        		res.setInterviewStatusLable("否");
	        	}
	        	//实操
	        	if(null != res.getManipulateStatus() &&  2 == res.getManipulateStatus()) {  //笔试
	        		res.setManipulateStatusLable("是");
	        	}else {
	        		res.setManipulateStatusLable("否");
	        	}
	        	
	        	//处理人才类型
	        	for (DictItemResp dictItemResp : dictItemRespList) {  //处理学历类型
	                if (dictItemResp.getItemCode().equals( res.getRclx())) {
	                	 res.setRclxLable(dictItemResp.getItemName());
	                    break;
	                }
	            }
	        	//处理笔试平均分
	        	if( res != null &&  res.getExamScore() != null 
	        			&&  res.getExamScore2() != null 
	        			&&  res.getExamScore2() >=1 ) {  
	        		double score = ( res.getExamScore() +  res.getExamScore2()) / 2;
	        		 res.setExamScore(score);
	        	}
	        	
	        	 res.setSequence((page.getPageNo() - 1) * page.getPageSize() + i);
	            i++;
	            //处理其他数据
	            
	            
	            
	        }
		return recruitSignUpList;
	}

	// 添加到人员档案
	@Transactional
	@Override
	public void addEmployee(String id) {
		RecruitSignUp oldBean = recruitSignUpMapper.selectByPrimaryKey(id);  //先提示
		if(oldBean != null ) {
			if("1".equals(oldBean.getIsAddemp())) {
				throw new BusinessException("不能重复添加");
			}
			String otherData = oldBean.getOtherData();
			JSONObject parseObject = JSONObject.parseObject(otherData);  //需要的信息从这里取
			HrmsEmployeeSaveReq hrmsEmployeeSaveReq = new HrmsEmployeeSaveReq();
			
	        Timestamp birthday = null;
	        IDCardUtil.Sex sex = null;
			try {
				sex = IDCardUtil.getGenderFromPersonIDCode(oldBean.getIdentityCard());
				birthday = IDCardUtil.getBirthdayFromPersonIDCode(oldBean.getIdentityCard());
			} catch (Throwable e) {
				log.error("根据身份证获取信息失败");
				throw new BusinessException("根据身份证获取信息失败");
			}
			//插入要保存的数据
			hrmsEmployeeSaveReq.setEmployeeNo(oldBean.getIdentityCard()); //身份证
			hrmsEmployeeSaveReq.setEmployeeStatus("99");  //试用期
			hrmsEmployeeSaveReq.setEmployeeName(oldBean.getName());
			if("男".equals(sex)) {
				hrmsEmployeeSaveReq.setGender("0");
			}else {
				hrmsEmployeeSaveReq.setGender("1");
			}
			
			hrmsEmployeeSaveReq.setIdentityNumber(oldBean.getIdentityCard());
			hrmsEmployeeSaveReq.setBirthday(birthday);  //出生日期
			//名族 
			
			//手机号
			hrmsEmployeeSaveReq.setPhoneNumber(parseObject.get("iphone").toString());
			PlatformResult<EmployeeResp> addEmployee = hrmsEmployeeFeignService.addEmployee(hrmsEmployeeSaveReq, UserInfoHolder.getToken());
			log.error(addEmployee.toString());
			if(null != addEmployee &&  200 == addEmployee.getStatusCode()) {
				//改为已添加到人员档案的状态
				oldBean.setIsAddemp("1");  //添加到人员档案
				recruitSignUpMapper.updateByPrimaryKeySelective(oldBean);
			}else {
				throw new BusinessException("人员档案添加失败");
			}
		
		}
	
	}
}