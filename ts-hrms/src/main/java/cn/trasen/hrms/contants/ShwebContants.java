package cn.trasen.hrms.contants;

import cn.trasen.homs.core.contants.Contants;

public class ShwebContants extends Contants{

    public static final String YES = "Y";
    public static final String NO = "N";

    public static final Integer ENABLE = 1; //启用
    public static final Integer DISABLE = 2; //禁用

    public static final String MY_PRODUCT_OR_CUSTOMER = "0"; //我的产品/客户

    public static final String ROLE_SERVER_MANAGER = "SERVER_MANAGER"; //客服管理员
    public static final String SYS_DEPT_MANAGER = "SYS_ROLE_DM"; //部门管理人员


    /**
     * 员工类型
     */
    public static final Integer PERSONNEL_TYPE_AFTER_SALE = 1; //售后类
    public static final Integer PERSONNEL_TYPE_IMPLEMENT = 2; //实施类
    public static final Integer PERSONNEL_TYPE_DEMAND_MANAGER = 3; //需求管理类

    /**
     * 流程动态查询类型
     */
    public static final Integer QUERY_TYPE_INTERNAL = 1; //内控
    public static final Integer QUERY_TYPE_CUSTOMER = 2; //客户

    /**
     * 流程申请结果
     */
    public static final Integer APPLY_RESULT_AGREED = 1; //同意
    public static final Integer APPLY_RESULT_REJECTED = 2; //驳回

    /**
     * 操作人类型
     */
    public static final Integer OPERATION_TYPE_CUSTOMER = 1; //客户
    public static final Integer OPERATION_TYPE_AFTER_SALES = 2; //售后
    public static final Integer OPERATION_TYPE_IMPLEMENT = 3; //实施
    public static final Integer OPERATION_TYPE_GENERAL = 4; //总控

    /**
     * 需求状态
     */
    public static final Integer ISSUE_STATUS_TO_SUBMIT = 0; //待提交
    public static final Integer ISSUE_STATUS_OPEN = 1; //开放状态
    public static final Integer ISSUE_STATUS_TO_ACCEPT = 2; //待受理
    public static final Integer ISSUE_STATUS_PROCESSING = 3; //处理中
    public static final Integer ISSUE_STATUS_TO_UPGRADE = 4; //待升级
    public static final Integer ISSUE_STATUS_TO_CONFIRMED = 5; //待确认
    public static final Integer ISSUE_STATUS_FINISHED = 6; //已完结

    public static final String ISSUE_CHILD_STATUS_DEFAULT = "0"; //默认""
    public static final String ISSUE_CHILD_STATUS_SUSPENDED = "0.1"; //已暂停
    public static final String ISSUE_CHILD_STATUS_APPLY_RECALL = "0.2"; //申请撤回
    public static final String ISSUE_CHILD_STATUS_RECALL = "0.3"; //需求已撤回
    public static final String ISSUE_CHILD_STATUS_APPLY_UNDO = "0.4"; //申请撤销
    public static final String ISSUE_CHILD_STATUS_UNDO = "0.5"; //已撤销
    public static final String ISSUE_CHILD_STATUS_TO_REVIEW = "2.1"; //待评审
    public static final String ISSUE_CHILD_STATUS_REVIEW_FAILURE = "2.2"; //评审不通过


    /**
     * 业务类型
     */
    public static final Integer BUSINESS_TYPE_CONVENTIONAL = 1; //常规问题
    public static final Integer BUSINESS_TYPE_DEMAND = 2; //需求
    public static final Integer BUSINESS_TYPE_DEFECTS = 3; //缺陷

    /**
     * 评审结果
     */
    public static final Integer REVIEW_RESULT_THROUGH = 1; //通过 or 缺陷已重现
    public static final Integer REVIEW_RESULT_NOT_THROUGH = 2; //不通过 or 缺陷无法重现

    /**
     * 优先级
     */
    public static final Integer PRIORITY_TYPE_EXTRA_URGENT = 1; //特急
    public static final Integer PRIORITY_TYPE_EMERGENCY = 2; //紧急
    public static final Integer PRIORITY_TYPE_NORMAL = 3; //正常
    public static final Integer PRIORITY_TYPE_GENERAL = 4; //一般

    public static final String CS_CUSTOMER_ROLE = "FC366C3822634029A9EFB9E5A1407139"; //客户角色
    public static final String CS_CUSTOMER_ADMIN = "0DF51F4DD29F47F087CC9B01F9A0E138"; //客户管理员
    
    
    public static final String DEFAULT_CONDITION="无";

}
