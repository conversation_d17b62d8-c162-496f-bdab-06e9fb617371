<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsEmployeeExtendMapper">

	<sql id="Basic_Columns">
		<![CDATA[
			employee_extend_id,
		    employee_id,
		    old_employee_no,
		    his_employee_no,
		    entry_date,
		    retire_date,
		    quit_date,
		    reemployment_date,
		    party_date,
		    work_start_date,
		    unit_start_date,
		    personal_identity,
		    work_nature,
		    good_at,
		    check_work_depart,
		    review_depart,
		    upgrade_flag,
		    improve_flag,
		    is_duplicate_entry,
		    emergency_contact,
		    emergency_tel,
		    probation_salary,
		    regular_salary,
		    buy_social_date,
		    buy_provident_date,
		    salary_remark,
		    remark,
		    enterprise_id,
		    create_date,
		    create_user,
		    create_user_name,
		    update_date,
		    update_user,
		    update_user_name,
		    is_deleted
		]]>
	</sql>

	<resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsEmployeeExtend">
		<!-- WARNING - @mbg.generated -->
		<id column="employee_extend_id" jdbcType="VARCHAR" property="employeeExtendId" />
		<result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
		<result column="old_employee_no" jdbcType="VARCHAR" property="oldEmployeeNo" />
		<result column="his_employee_no" jdbcType="VARCHAR" property="hisEmployeeNo" />
		<result column="entry_date" jdbcType="TIMESTAMP" property="entryDate" />
		<result column="retire_date" jdbcType="TIMESTAMP" property="retireDate" />
		<result column="quit_date" jdbcType="TIMESTAMP" property="quitDate" />
		<result column="reemployment_date" jdbcType="TIMESTAMP" property="reemploymentDate" />
		<result column="party_date" jdbcType="TIMESTAMP" property="partyDate" />
		<result column="work_start_date" jdbcType="TIMESTAMP" property="workStartDate" />
		<result column="unit_start_date" jdbcType="TIMESTAMP" property="unitStartDate" />
		<result column="personal_identity" jdbcType="CHAR" property="personalIdentity" />
		<result column="work_nature" jdbcType="CHAR" property="workNature" />
		<result column="good_at" jdbcType="VARCHAR" property="goodAt" />
		<result column="check_work_depart" jdbcType="VARCHAR" property="checkWorkDepart" />
		<result column="review_depart" jdbcType="VARCHAR" property="reviewDepart" />
		<result column="is_payroll" jdbcType="CHAR" property="isPayroll" />
		<result column="upgrade_flag" jdbcType="CHAR" property="upgradeFlag" />
		<result column="improve_flag" jdbcType="CHAR" property="improveFlag" />
		<result column="is_duplicate_entry" jdbcType="CHAR" property="isDuplicateEntry" />
		<result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact" />
		<result column="emergency_tel" jdbcType="VARCHAR" property="emergencyTel" />
		<result column="probation_salary" jdbcType="DECIMAL" property="probationSalary" />
		<result column="regular_salary" jdbcType="DECIMAL" property="regularSalary" />
		<result column="buy_social_date" jdbcType="TIMESTAMP" property="buySocialDate" />
		<result column="buy_provident_date" jdbcType="TIMESTAMP" property="buyProvidentDate" />
		<result column="salary_remark" jdbcType="VARCHAR" property="salaryRemark" />
		<result column="remark" jdbcType="VARCHAR" property="remark" />
		<result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
		<result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
		<result column="create_user" jdbcType="VARCHAR" property="createUser" />
		<result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
		<result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
		<result column="update_user" jdbcType="VARCHAR" property="updateUser" />
		<result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
		<result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
	</resultMap>

	<select id="findByEmployeeId" resultMap="BaseResultMap">
		SELECT <include refid="Basic_Columns" /> FROM hrms_employee_extend WHERE employee_id = #{employeeId}
	</select>
	
	<insert id="batchInsert">
		<![CDATA[
			INSERT INTO hrms_employee_extend 
			(
				employee_extend_id, 
				employee_id, 
				old_employee_no,
				entry_date,
				retire_date, 
				quit_date,
				reemployment_date,
				party_date,
				work_start_date,
				unit_start_date,
				personal_identity,
				work_nature,
				good_at,
				check_work_depart,
				review_depart,
				remark,
				create_date, 
				create_user, 
				is_deleted,
				
				job_description_type,
				job_description_type_time,
				concurrent_position,
				concurrent_position_time,
				is_leader,	
				post_type,	
				doctor_qualification_certificate,
				midwife,
				start_employ_date,
				end_employ_date,
				is_veteran,
				unit_name,
				business_id,
				born_address,
				born_address_name
				
			) 
			VALUES 
		]]>
		<foreach collection="list" item="item" index="index" separator=",">
			<![CDATA[
			(
				#{item.employeeExtendId}, 
				#{item.employeeId}, 
				#{item.oldEmployeeNo},
				#{item.entryDate}, 
				#{item.retireDate}, 
				#{item.quitDate}, 
				#{item.reemploymentDate}, 
				#{item.partyDate}, 
				#{item.workStartDate}, 
				#{item.unitStartDate}, 
				#{item.personalIdentity}, 
				#{item.workNature}, 
				#{item.goodAt}, 
				#{item.checkWorkDepart}, 
				#{item.reviewDepart}, 
				#{item.remark}, 
				#{item.createDate}, 
				#{item.createUser}, 
				#{item.isDeleted},
				
				#{item.jobDescriptionType},
				#{item.jobDescriptionTypeTime},
				#{item.concurrentPosition},
				#{item.concurrentPositionTime},
				#{item.isLeader},	
				#{item.postType},	
				#{item.doctorQualificationCertificate},
				#{item.midwife},
				#{item.startEmployDate},
				#{item.endEmployDate},
				#{item.isVeteran},
				#{item.unitName},
				#{item.businessId},
				#{item.bornAddress},
				#{item.bornAddressName}
				
				
				
			)
			]]>
		</foreach>
	</insert>
	
</mapper>