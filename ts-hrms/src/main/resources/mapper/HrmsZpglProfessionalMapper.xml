<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.zpgl.dao.HrmsZpglProfessionalMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.zpgl.model.HrmsZpglProfessional">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="zhichengmingcheng" jdbcType="VARCHAR" property="zhichengmingcheng" />
    <result column="zhuenye" jdbcType="VARCHAR" property="zhuenye" />
    <result column="qudeshijian" jdbcType="VARCHAR" property="qudeshijian" />
    <result column="qudedidian" jdbcType="VARCHAR" property="qudedidian" />
    <result column="fujian" jdbcType="VARCHAR" property="fujian" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
  </resultMap>
  
  <select id="selectZhicheng" parameterType="String" resultType="String">
  		select jobtitle_basic_name from comm_jobtitle_basic
  		where jobtitle_basic_id = #{zhichengmingcheng}
  </select>
</mapper>