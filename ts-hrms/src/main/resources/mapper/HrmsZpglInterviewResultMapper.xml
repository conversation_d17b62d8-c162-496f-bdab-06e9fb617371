<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.zpgl.dao.HrmsZpglInterviewResultMapper">
    <resultMap id="BaseResultMap" type="cn.trasen.hrms.zpgl.model.HrmsZpglInterviewResult">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="id" jdbcType="VARCHAR" property="id"/>
        <result column="zpglempid" jdbcType="VARCHAR" property="zpglempid"/>
        <result column="interview_result_status" jdbcType="VARCHAR" property="interviewResultStatus"/>
        <result column="written_result" jdbcType="VARCHAR" property="writtenResult"/>
        <result column="operation_result" jdbcType="VARCHAR" property="operationResult"/>
        <result column="interview_result" jdbcType="VARCHAR" property="interviewResult"/>
        <result column="studyout_dept" jdbcType="VARCHAR" property="studyoutDept"/>
        <result column="studyout_depttext" jdbcType="VARCHAR" property="studyoutDepttext"/>
        <result column="studyout_job" jdbcType="VARCHAR" property="studyoutJob"/>
        <result column="studyout_jobtext" jdbcType="VARCHAR" property="studyoutJobtext"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_user_name" jdbcType="CHAR" property="createUserName"/>
        <result column="update_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName"/>
        <result column="is_deleted" jdbcType="CHAR" property="isDeleted"/>
    </resultMap>
    <select id="getDataSetList" resultType="cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglReportOutListVo">
        SELECT ('收集简历数') as name,
        (
        <include refid="checkResumeTotal"/>
        = 1
        <include refid="checkParam"/>
        ) as january,
        (
        <include refid="checkResumeTotal"/>
        = 2
        <include refid="checkParam"/>
        ) as february,
        (
        <include refid="checkResumeTotal"/>
        = 3
        <include refid="checkParam"/>) as march,
        (
        <include refid="checkResumeTotal"/>
        = 4
        <include refid="checkParam"/>) as april,
        (
        <include refid="checkResumeTotal"/>
        = 5
        <include refid="checkParam"/>) as may,
        (
        <include refid="checkResumeTotal"/>
        = 6
        <include refid="checkParam"/>) as june,
        (
        <include refid="checkResumeTotal"/>
        = 7
        <include refid="checkParam"/>) as july,
        (
        <include refid="checkResumeTotal"/>
        = 8
        <include refid="checkParam"/>) as august,
        (
        <include refid="checkResumeTotal"/>
        = 9
        <include refid="checkParam"/>) as september,
        (
        <include refid="checkResumeTotal"/>
        = 10
        <include refid="checkParam"/>) as october,
        (
        <include refid="checkResumeTotal"/>
        = 11
        <include refid="checkParam"/>) as november,
        (
        <include refid="checkResumeTotal"/>
        = 12
        <include refid="checkParam"/>) as december,
        (
        <include refid="checkResumeTotal"/>
        in(1,2,3)
        <include refid="checkParam"/>) as firstQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(4,5,6)
        <include refid="checkParam"/>) as twoQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(7,8,9)
        <include refid="checkParam"/>) as threeQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(10,11,12)
        <include refid="checkParam"/>) as fourQuarter,
        (
        <include refid="checkResumeTotal"/>
        BETWEEN 1 and 12
        <include refid="checkParam"/>) as year
        union all
        SELECT ('合格简历数') as name,
        (
        <include refid="checkResumeTotal"/>
        = 1 and add_interview =1
        <include refid="checkParam"/>
        ) as january,
        (
        <include refid="checkResumeTotal"/>
        = 2 and add_interview =1
        <include refid="checkParam"/>
        ) as february,
        (
        <include refid="checkResumeTotal"/>
        = 3 and add_interview =1
        <include refid="checkParam"/>) as march,
        (
        <include refid="checkResumeTotal"/>
        = 4 and add_interview =1
        <include refid="checkParam"/>) as april,
        (
        <include refid="checkResumeTotal"/>
        = 5 and add_interview =1
        <include refid="checkParam"/>) as may,
        (
        <include refid="checkResumeTotal"/>
        = 6 and add_interview =1
        <include refid="checkParam"/>) as june,
        (
        <include refid="checkResumeTotal"/>
        = 7 and add_interview =1
        <include refid="checkParam"/>) as july,
        (
        <include refid="checkResumeTotal"/>
        = 8 and add_interview =1
        <include refid="checkParam"/>) as august,
        (
        <include refid="checkResumeTotal"/>
        = 9 and add_interview =1
        <include refid="checkParam"/>) as september,
        (
        <include refid="checkResumeTotal"/>
        = 10 and add_interview =1
        <include refid="checkParam"/>) as october,
        (
        <include refid="checkResumeTotal"/>
        = 11 and add_interview =1
        <include refid="checkParam"/>) as november,
        (
        <include refid="checkResumeTotal"/>
        = 12 and add_interview =1
        <include refid="checkParam"/>) as december,
        (
        <include refid="checkResumeTotal"/>
        in(1,2,3) and add_interview =1
        <include refid="checkParam"/>) as firstQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(4,5,6) and add_interview =1
        <include refid="checkParam"/>) as twoQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(7,8,9) and add_interview =1
        <include refid="checkParam"/>) as threeQuarter,
        (
        <include refid="checkResumeTotal"/>
        in(10,11,12) and add_interview =1
        <include refid="checkParam"/>) as fourQuarter,
        (
        <include refid="checkResumeTotal"/>
        BETWEEN 1 and 12 and add_interview =1
        <include refid="checkParam"/>) as year
        union all
        SELECT('邀约面试数') as name,
        (
        <include refid="checkInvitation"/>
        = 1
        <include refid="checkEmployeeParam"/>
        ) as january,
        (
        <include refid="checkInvitation"/>
        = 2
        <include refid="checkEmployeeParam"/>
        ) as february,
        (
        <include refid="checkInvitation"/>
        = 3
        <include refid="checkEmployeeParam"/>) as march,
        (
        <include refid="checkInvitation"/>
        = 4
        <include refid="checkEmployeeParam"/>) as april,
        (
        <include refid="checkInvitation"/>
        = 5
        <include refid="checkEmployeeParam"/>) as may,
        (
        <include refid="checkInvitation"/>
        = 6
        <include refid="checkEmployeeParam"/>) as june,
        (
        <include refid="checkInvitation"/>
        = 7
        <include refid="checkEmployeeParam"/>) as july,
        (
        <include refid="checkInvitation"/>
        = 8
        <include refid="checkEmployeeParam"/>) as august,
        (
        <include refid="checkInvitation"/>
        = 9
        <include refid="checkEmployeeParam"/>) as september,
        (
        <include refid="checkInvitation"/>
        = 10
        <include refid="checkEmployeeParam"/>) as october,
        (
        <include refid="checkInvitation"/>
        = 11
        <include refid="checkEmployeeParam"/>) as november,
        (
        <include refid="checkInvitation"/>
        = 12
        <include refid="checkEmployeeParam"/>) as december,
        (
        <include refid="checkInvitation"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>) as firstQuarter,
        (
        <include refid="checkInvitation"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>) as twoQuarter,
        (
        <include refid="checkInvitation"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>) as threeQuarter,
        (
        <include refid="checkInvitation"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>) as fourQuarter,
        (
        <include refid="checkInvitation"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>) as year
        union all
        SELECT ('面试人数') as name,
        (
        <include refid="checkNumberInterviewees"/>
        = 1
        <include refid="checkEmployeeParam"/>
        ) as january,
        (
        <include refid="checkNumberInterviewees"/>
        = 2
        <include refid="checkEmployeeParam"/>
        ) as february,
        (
        <include refid="checkNumberInterviewees"/>
        = 3
        <include refid="checkEmployeeParam"/>) as march,
        (
        <include refid="checkNumberInterviewees"/>
        = 4
        <include refid="checkEmployeeParam"/>) as april,
        (
        <include refid="checkNumberInterviewees"/>
        = 5
        <include refid="checkEmployeeParam"/>) as may,
        (
        <include refid="checkNumberInterviewees"/>
        = 6
        <include refid="checkEmployeeParam"/>) as june,
        (
        <include refid="checkNumberInterviewees"/>
        = 7
        <include refid="checkEmployeeParam"/>) as july,
        (
        <include refid="checkNumberInterviewees"/>
        = 8
        <include refid="checkEmployeeParam"/>) as august,
        (
        <include refid="checkNumberInterviewees"/>
        = 9
        <include refid="checkEmployeeParam"/>) as september,
        (
        <include refid="checkNumberInterviewees"/>
        = 10
        <include refid="checkEmployeeParam"/>) as october,
        (
        <include refid="checkNumberInterviewees"/>
        = 11
        <include refid="checkEmployeeParam"/>) as november,
        (
        <include refid="checkNumberInterviewees"/>
        = 12
        <include refid="checkEmployeeParam"/>) as december,
        (
        <include refid="checkNumberInterviewees"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>) as firstQuarter,
        (
        <include refid="checkNumberInterviewees"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>) as twoQuarter,
        (
        <include refid="checkNumberInterviewees"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>) as threeQuarter,
        (
        <include refid="checkNumberInterviewees"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>) as fourQuarter,
        (
        <include refid="checkNumberInterviewees"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>) as year
        union all
        SELECT ('面试通过人数') as name,
        (
        <include refid="checkPass"/>
        = 1
        <include refid="checkEmployeeParam"/>
        ) as january,
        (
        <include refid="checkPass"/>
        = 2
        <include refid="checkEmployeeParam"/>
        ) as february,
        (
        <include refid="checkPass"/>
        = 3
        <include refid="checkEmployeeParam"/>) as march,
        (
        <include refid="checkPass"/>
        = 4
        <include refid="checkEmployeeParam"/>) as april,
        (
        <include refid="checkPass"/>
        = 5
        <include refid="checkEmployeeParam"/>) as may,
        (
        <include refid="checkPass"/>
        = 6
        <include refid="checkEmployeeParam"/>) as june,
        (
        <include refid="checkPass"/>
        = 7
        <include refid="checkEmployeeParam"/>) as july,
        (
        <include refid="checkPass"/>
        = 8
        <include refid="checkEmployeeParam"/>) as august,
        (
        <include refid="checkPass"/>
        = 9
        <include refid="checkEmployeeParam"/>) as september,
        (
        <include refid="checkPass"/>
        = 10
        <include refid="checkEmployeeParam"/>) as october,
        (
        <include refid="checkPass"/>
        = 11
        <include refid="checkEmployeeParam"/>) as november,
        (
        <include refid="checkPass"/>
        = 12
        <include refid="checkEmployeeParam"/>) as december,
        (
        <include refid="checkPass"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>) as firstQuarter,
        (
        <include refid="checkPass"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>) as twoQuarter,
        (
        <include refid="checkPass"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>) as threeQuarter,
        (
        <include refid="checkPass"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>) as fourQuarter,
        (
        <include refid="checkPass"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>) as year
        union all
        SELECT ('入职人数') as name,
        (
        <include refid="checkEmployment"/>
        = 1
        <include refid="checkParam"/>
        ) as january,
        (
        <include refid="checkEmployment"/>
        = 2
        <include refid="checkParam"/>
        ) as february,
        (
        <include refid="checkEmployment"/>
        = 3
        <include refid="checkParam"/>) as march,
        (
        <include refid="checkEmployment"/>
        = 4
        <include refid="checkParam"/>) as april,
        (
        <include refid="checkEmployment"/>
        = 5
        <include refid="checkParam"/>) as may,
        (
        <include refid="checkEmployment"/>
        = 6
        <include refid="checkParam"/>) as june,
        (
        <include refid="checkEmployment"/>
        = 7
        <include refid="checkParam"/>) as july,
        (
        <include refid="checkEmployment"/>
        = 8
        <include refid="checkParam"/>) as august,
        (
        <include refid="checkEmployment"/>
        = 9
        <include refid="checkParam"/>) as september,
        (
        <include refid="checkEmployment"/>
        = 10
        <include refid="checkParam"/>) as october,
        (
        <include refid="checkEmployment"/>
        = 11
        <include refid="checkParam"/>) as november,
        (
        <include refid="checkEmployment"/>
        = 12
        <include refid="checkParam"/>) as december,
        (
        <include refid="checkEmployment"/>
        in(1,2,3)
        <include refid="checkParam"/>) as firstQuarter,
        (
        <include refid="checkEmployment"/>
        in(4,5,6)
        <include refid="checkParam"/>) as twoQuarter,
        (
        <include refid="checkEmployment"/>
        in(7,8,9)
        <include refid="checkParam"/>) as threeQuarter,
        (
        <include refid="checkEmployment"/>
        in(10,11,12)
        <include refid="checkParam"/>) as fourQuarter,
        (
        <include refid="checkEmployment"/>
        BETWEEN 1 and 12
        <include refid="checkParam"/>) as year
    </select>
    <select id="getReportIndexList" resultType="cn.trasen.hrms.zpgl.vo.outVo.HrmsZpglReportOutListVo">
        SELECT ('简历合格率') as name,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 1 and add_interview =1
        <include refid="checkParam"/>) /
        (
        <include refid="checkResumeTotal"/>
        = 1
        <include refid="checkParam"/>),2)
        ) as january,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 2 and add_interview =1
        <include refid="checkParam"/>) /
        (
        <include refid="checkResumeTotal"/>
        = 2
        <include refid="checkParam"/>),2)
        ) as february,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 3 and add_interview =1
        <include refid="checkParam"/>) /
        (
        <include refid="checkResumeTotal"/>
        = 3
        <include refid="checkParam"/>),2)) as march,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 4 and add_interview =1
        <include refid="checkParam"/>) /
        (
        <include refid="checkResumeTotal"/>
        = 4
        <include refid="checkParam"/>),2)
        ) as april,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 5 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 5
        <include refid="checkParam"/>),2)
        ) as may,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 6 and add_interview =1
        <include refid="checkParam"/>) /
        (
        <include refid="checkResumeTotal"/>
        = 6
        <include refid="checkParam"/>),2)) as june,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 7 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 7
        <include refid="checkParam"/>),2)) as july,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 8 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 8
        <include refid="checkParam"/>),2)) as august,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 9 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 9
        <include refid="checkParam"/>),2)) as september,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 10 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 10
        <include refid="checkParam"/>),2)) as october,
        (ROUND(
        (
        <include refid="checkResumeTotal"/>
        = 11 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 11
        <include refid="checkParam"/>),2)) as november,
        (ROUND((
        <include refid="checkResumeTotal"/>
        = 12 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        = 12
        <include refid="checkParam"/>),2)) as december,
        (ROUND((
        <include refid="checkResumeTotal"/>
        in(1,2,3) and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        in(1,2,3)
        <include refid="checkParam"/>),2)) as firstQuarter,
        (ROUND((
        <include refid="checkResumeTotal"/>
        in(4,5,6) and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        in(4,5,6)
        <include refid="checkParam"/>),2)) as twoQuarter,
        (ROUND((
        <include refid="checkResumeTotal"/>
        in(7,8,9) and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        in(7,8,9)
        <include refid="checkParam"/>),2)) as threeQuarter,
        (ROUND((
        <include refid="checkResumeTotal"/>
        in(10,11,12) and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        in(10,11,12)
        <include refid="checkParam"/>),2)) as fourQuarter,
        (ROUND((
        <include refid="checkResumeTotal"/>
        BETWEEN 1 and 12 and add_interview =1
        <include refid="checkParam"/>) / (
        <include refid="checkResumeTotal"/>
        BETWEEN 1 and 12
        <include refid="checkParam"/>),2)) as year
        union all
        SELECT ('面试邀约成功率') as name, (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 1
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 1
        <include refid="checkEmployeeParam"/>),2)
        ) as january,
        (ROUND(
        (
        <include refid="checkNumberInterviewees"/>
        = 2
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 2
        <include refid="checkEmployeeParam"/>),2)
        ) as february,
        (ROUND(
        (
        <include refid="checkNumberInterviewees"/>
        = 3
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 3
        <include refid="checkEmployeeParam"/>),2)) as march,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 4
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 4
        <include refid="checkEmployeeParam"/>),2)) as april,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 5
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 5
        <include refid="checkEmployeeParam"/>),2)) as may,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 6
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 6
        <include refid="checkEmployeeParam"/>),2)) as june,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 7
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 7
        <include refid="checkEmployeeParam"/>),2)) as july,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 8
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 8
        <include refid="checkEmployeeParam"/>),2)) as august,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 9
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 9
        <include refid="checkEmployeeParam"/>),2)) as september,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 10
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 10
        <include refid="checkEmployeeParam"/>),2)) as october,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 11
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 11
        <include refid="checkEmployeeParam"/>),2)) as november,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        = 12
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        = 12
        <include refid="checkEmployeeParam"/>),2)) as december,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>
        ),2)) as firstQuarter,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>),2)) as twoQuarter,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>),2)) as threeQuarter,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>),2)) as fourQuarter,
        (ROUND((
        <include refid="checkNumberInterviewees"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkInvitation"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>),2)) as year
        union all
        SELECT ('面试通过率') as name, (ROUND((
        <include refid="checkPass"/>
        = 1
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 1
        <include refid="checkEmployeeParam"/>),2)
        ) as january,
        (ROUND((
        <include refid="checkPass"/>
        = 2
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 2
        <include refid="checkEmployeeParam"/>),2)
        ) as february,
        (ROUND((
        <include refid="checkPass"/>
        = 3
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 3
        <include refid="checkEmployeeParam"/>),2)) as march,
        (ROUND((
        <include refid="checkPass"/>
        = 4
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 4
        <include refid="checkEmployeeParam"/>),2)) as april,
        (ROUND((
        <include refid="checkPass"/>
        = 5
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 5
        <include refid="checkEmployeeParam"/>),2)) as may,
        (ROUND((
        <include refid="checkPass"/>
        = 6
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 6
        <include refid="checkEmployeeParam"/>),2)) as june,
        (ROUND((
        <include refid="checkPass"/>
        = 7
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 7
        <include refid="checkEmployeeParam"/>),2)) as july,
        (ROUND((
        <include refid="checkPass"/>
        = 8
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 8
        <include refid="checkEmployeeParam"/>),2)) as august,
        (ROUND((
        <include refid="checkPass"/>
        = 9
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 9
        <include refid="checkEmployeeParam"/>),2)) as september,
        (ROUND((
        <include refid="checkPass"/>
        = 10
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 10
        <include refid="checkEmployeeParam"/>),2)) as october,
        (ROUND((
        <include refid="checkPass"/>
        = 11
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 11
        <include refid="checkEmployeeParam"/>),2)) as november,
        (ROUND((
        <include refid="checkPass"/>
        = 12
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 12
        <include refid="checkEmployeeParam"/>),2)) as december,
        (ROUND((
        <include refid="checkPass"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in(1,2,3)
        <include refid="checkEmployeeParam"/>),2)) as firstQuarter,
        (ROUND((
        <include refid="checkPass"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in(4,5,6)
        <include refid="checkEmployeeParam"/>),2)) as twoQuarter,
        (ROUND((
        <include refid="checkPass"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in(7,8,9)
        <include refid="checkEmployeeParam"/>),2)) as threeQuarter,
        (ROUND((
        <include refid="checkPass"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in(10,11,12)
        <include refid="checkEmployeeParam"/>),2)) as fourQuarter,
        (ROUND((
        <include refid="checkPass"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>),2)) as year
        union all
        SELECT ('招聘成功率') as name, (ROUND((
        <include refid="checkEmployment"/>
        = 1
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 1
        <include refid="checkEmployeeParam"/>),2)
        ) as january,
        (ROUND((
        <include refid="checkEmployment"/>
        = 2
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 2
        <include refid="checkEmployeeParam"/>),2)
        ) as february,
        (ROUND((
        <include refid="checkEmployment"/>
        = 3
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 3
        <include refid="checkEmployeeParam"/>),2)) as march,
        (ROUND((
        <include refid="checkEmployment"/>
        = 4
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 4
        <include refid="checkEmployeeParam"/>),2)) as april,
        (ROUND((
        <include refid="checkEmployment"/>
        = 5
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 5
        <include refid="checkEmployeeParam"/>),2)) as may,
        (ROUND((
        <include refid="checkEmployment"/>
        = 6
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 6
        <include refid="checkEmployeeParam"/>),2)) as june,
        (ROUND((
        <include refid="checkEmployment"/>
        = 7
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 7
        <include refid="checkEmployeeParam"/>),2)) as july,
        (ROUND((
        <include refid="checkEmployment"/>
        = 8
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 8
        <include refid="checkEmployeeParam"/>),2)) as august,
        (ROUND((
        <include refid="checkEmployment"/>
        = 9
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 9
        <include refid="checkEmployeeParam"/>),2)) as september,
        (ROUND((
        <include refid="checkEmployment"/>
        = 10
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 10
        <include refid="checkEmployeeParam"/>),2)) as october,
        (ROUND((
        <include refid="checkEmployment"/>
        = 11
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 11
        <include refid="checkEmployeeParam"/>),2)) as november,
        (ROUND((
        <include refid="checkEmployment"/>
        = 12
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        = 12
        <include refid="checkEmployeeParam"/>),2)) as december,
        (ROUND((
        <include refid="checkEmployment"/>
        in(1,2,3)
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in (1,2,3)
        <include refid="checkEmployeeParam"/>),2)) as firstQuarter,
        (ROUND((
        <include refid="checkEmployment"/>
        in(4,5,6)
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in (4,5,6)
        <include refid="checkEmployeeParam"/>),2)) as twoQuarter,
        (ROUND((
        <include refid="checkEmployment"/>
        in(7,8,9)
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in (7,8,9)
        <include refid="checkEmployeeParam"/>),2)) as threeQuarter,
        (ROUND((
        <include refid="checkEmployment"/>
        in(10,11,12)
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        in (10,11,12)
        <include refid="checkEmployeeParam"/>),2)) as fourQuarter,
        (ROUND((
        <include refid="checkEmployment"/>
        BETWEEN 1 and 12
        <include refid="checkParam"/>) / (
        <include refid="checkNumberInterviewees"/>
        BETWEEN 1 and 12
        <include refid="checkEmployeeParam"/>),2)) as year
    </select>

    <sql id="checkNumberInterviewees">
       SELECT count(*)
        FROM hrms_zpgl_employee t1
        LEFT JOIN hrms_zpgl_interview_message tt ON tt.zpglempid = t1.id and tt.is_deleted='N'
        LEFT JOIN hrms_zpgl_interview_result re ON t1.id = re.zpglempid and re.is_deleted = 'N'
        WHERE tt.conform ='1' and t1.zpgl_employee_status !='3' and t1.zpgl_employee_status !='4'
        and YEAR(t1.create_date) = #{year}
        and MONTH(t1.create_date)
        <if test="ssoOrgCode != null and ssoOrgCode != ''">
            and t1.sso_org_code = #{ssoOrgCode}
        </if>
     </sql>
    <sql id="checkResumeTotal">
       SELECT count(*) FROM hrms_zpgl_employee
       where YEAR(create_date) = #{year}
       and MONTH(create_date)
       <if test="ssoOrgCode != null and ssoOrgCode != ''">
           and sso_org_code = #{ssoOrgCode}
       </if>
     </sql>
    <sql id="checkInvitation">
    SELECT count(*)
        FROM hrms_zpgl_employee t1
        LEFT JOIN hrms_zpgl_interview_message tt ON tt.zpglempid = t1.id and tt.is_deleted='N'
        WHERE tt.interview_status ='1' and t1.zpgl_employee_status !='3' and t1.zpgl_employee_status !='4' and YEAR(t1.create_date) = #{year}
        and MONTH(t1.create_date)
        <if test="ssoOrgCode != null and ssoOrgCode != ''">
            and t1.sso_org_code = #{ssoOrgCode}
        </if>
  </sql>
    <sql id="checkPass">
       SELECT count(*)
        FROM hrms_zpgl_employee t1
		LEFT JOIN hrms_zpgl_interview_message tt   ON tt.zpglempid = t1.id and tt.is_deleted='N'
		LEFT JOIN hrms_zpgl_interview_result re ON t1.id = re.zpglempid  and re.is_deleted = 'N'
        WHERE tt.conform ='1'
		and re.interview_result_status ='1'
		and t1.zpgl_employee_status !='3'
		and t1.zpgl_employee_status !='4' and YEAR(t1.create_date) = #{year}
        <if test="ssoOrgCode != null and ssoOrgCode != ''">
            and t1.sso_org_code = #{ssoOrgCode}
        </if>
		and MONTH(t1.create_date)
     </sql>
    <sql id="checkEmployment">
         SELECT count(*)
         FROM hrms_zpgl_employee
         WHERE zpgl_employee_status ='4'
         and YEAR(create_date) = #{year}
         and MONTH(create_date)
         <if test="ssoOrgCode != null and ssoOrgCode != ''">
             and sso_org_code = #{ssoOrgCode}
         </if>
    </sql>
    <sql id="checkParam">
        <if test="personnelCategory != null and personnelCategory.size() > 0">
            AND personnel_category in
            <foreach collection="personnelCategory" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <sql id="checkEmployeeParam">
        <if test="personnelCategory != null and personnelCategory.size() > 0">
            AND t1.personnel_category in
            <foreach collection="personnelCategory" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
</mapper>