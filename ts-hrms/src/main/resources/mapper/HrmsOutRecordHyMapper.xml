<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.hrms.dao.HrmsOutRecordHyMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.hrms.model.HrmsOutRecordHy">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="province_type" jdbcType="VARCHAR" property="provinceType" />
    <result column="apply_time" jdbcType="VARCHAR" property="applyTime" />
    <result column="out_type" jdbcType="VARCHAR" property="outType" />
    <result column="jobs" jdbcType="VARCHAR" property="jobs" />
    <result column="start_time" jdbcType="VARCHAR" property="startTime" />
    <result column="end_time" jdbcType="VARCHAR" property="endTime" />
    <result column="out_days" jdbcType="VARCHAR" property="outDays" />
    <result column="out_address" jdbcType="VARCHAR" property="outAddress" />
    <result column="out_remark" jdbcType="VARCHAR" property="outRemark" />
    <result column="file_id" jdbcType="VARCHAR" property="fileId" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="apply_user" jdbcType="VARCHAR" property="applyUser" />
    <result column="apply_user_name" jdbcType="VARCHAR" property="applyUserName" />
    <result column="apply_dept" jdbcType="VARCHAR" property="applyDept" />
    <result column="apply_dept_name" jdbcType="VARCHAR" property="applyDeptName" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="work_id" jdbcType="VARCHAR" property="workId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
  </resultMap>
  
  
  <select id="getDataSetList" parameterType="cn.trasen.hrms.model.HrmsOutRecord" resultType="cn.trasen.hrms.model.HrmsOutRecord">
  	select r.*,e.identity_number as identityNumber from hrms_out_record_hy r
	left join cust_emp_base e on r.apply_user = e.employee_no
	where r.is_deleted = 'N' 
	<if test="roleCode != '' and roleCode != null">
		and e.org_attributes in ('1','2','3','4','5')
	</if>
	<if test="outType != '' and outType != null and outType == '学习与会议与公务外出'.toString()">
		and r.out_type in ('外出会议','外出学习','公务外出')
	</if>
	<if test="outType != '' and outType != null and outType != '学习与会议与公务外出'.toString()">
		and r.out_type = #{outType}
	</if>
	<if test="searchOutType != '' and searchOutType != null ">
		and r.out_type = #{searchOutType}
	</if>
      <if test="status != '' and status != null and status == '1'.toString()">
          and r.status = '1'
      </if>
      <if test="status != '' and status != null and status != '1'.toString()">
		  and (r.status != '1' or r.status is null or r.status = '')
      </if>
  	<if test="employeeId != '' and employeeId != null">
		and r.employee_id = #{employeeId}
	</if>
  	<if test="applyDeptName != '' and applyDeptName != null">
		and r.apply_dept_name  LIKE CONCAT('%',#{applyDeptName},'%')
	</if>
	<if test="applyUserName != '' and applyUserName != null">
		and r.apply_user_name  LIKE CONCAT('%',#{applyUserName},'%')
	</if>
	<if test="startTime != '' and startTime != null">
		and r.start_time <![CDATA[ >= ]]> #{startTime}
	</if>
  	<if test="endTime != '' and endTime != null">
	  	and r.start_time <![CDATA[ <= ]]> #{endTime}
  	</if>
  	<if test="endStartTime != '' and endStartTime != null">
	  	and r.end_time <![CDATA[ >= ]]> #{endStartTime}
  	</if>
  	<if test="endEndTime != '' and endEndTime != null">
		and r.end_time<![CDATA[ <= ]]> #{endEndTime}
  	</if>
	<if test="outAddress != '' and outAddress != null">
		and r.out_address LIKE CONCAT('%',#{outAddress},'%')
	</if>
    <if test="ssoOrgCode != null and ssoOrgCode != ''">
  	  and e.sso_org_code = #{ssoOrgCode}
    </if>
  </select>
  
  
   <select id="getOutStudyAndMeeting" parameterType="cn.trasen.hrms.model.HrmsOutRecordHy" resultType="Map">
  	select 
  		e.org_id,
  		o.name as orgName,
  		e.employee_name as employeeName,
  		h.jobs,
		h.province_type as provinceType,
		h.out_address as outAddress,
		h.start_time as startTime,
		h.end_time as endTime,
		DATEDIFF(h.end_time,h.start_time)+1 as outDays 
		from comm_organization o 
		LEFT JOIN cust_emp_base e on o.organization_id = e.org_id and o.IS_DELETED = 'N' AND e.IS_DELETED = 'N'
		LEFT JOIN hrms_out_record_hy h on e.employee_id = h.employee_id and h.IS_DELETED = 'N' 
		where 1=1 and h.out_type in ('外出会议','外出学习') and status=1
		<if test="provinceType != '' and provinceType != null">
			and h.province_type=#{provinceType}
		</if>
		<if test="applyUserName != '' and applyUserName != null">
			and e.employee_name  LIKE CONCAT('%',#{applyUserName},'%')
		</if>
		<if test="applyDeptName != '' and applyDeptName != null">
			and o.name  LIKE CONCAT('%',#{applyDeptName},'%')
		</if>
		<if test="startTime != '' and startTime != null and endTime != '' and endTime != null">
			and !(h.start_time>#{endTime} or  h.end_time &lt; #{startTime})
		</if>
		<if test="jobs != '' and jobs != null and jobs =='护理类'">
			and h.jobs in ('护士','护士长')
		</if>
		<if test="jobs != '' and jobs != null and jobs =='非护理类'">
			and h.jobs not in ('护士','护士长')
		</if>
		<if test="outAddress != '' and outAddress != null">
			and h.out_address LIKE CONCAT('%',#{outAddress},'%')
		</if>
		<if test="ssoOrgCode != null and ssoOrgCode != ''">
			and e.sso_org_code = #{ssoOrgCode}
		</if>
		order by o.name,e.employee_name,h.jobs,h.start_time desc
  </select>
  
   <select id="getOutStudyAndMeetingCount" parameterType="cn.trasen.hrms.model.HrmsOutRecordHy" resultType="Map">
  	select sum(case when  h.jobs in ('护士','护士长') then 1 else 0 end) as hl_jobs,sum(case when  h.jobs not in ('护士','护士长') then 1 else 0 end) as fhl_jobs 
		from comm_organization o LEFT JOIN cust_emp_base e 
		on o.organization_id = e.org_id and o.IS_DELETED = 'N' AND e.IS_DELETED = 'N'
		LEFT JOIN hrms_out_record_hy h on e.employee_id = h.employee_id and h.IS_DELETED = 'N' 
		where 1=1 and h.out_type in ('外出会议','外出学习','公务外出') and status=1
		<if test="provinceType != '' and provinceType != null">
			and h.province_type=#{provinceType}
		</if>
		<if test="applyUserName != '' and applyUserName != null">
			and e.employee_name  LIKE CONCAT('%',#{applyUserName},'%')
		</if>
		<if test="applyDeptName != '' and applyDeptName != null">
			and o.name  LIKE CONCAT('%',#{applyDeptName},'%')
		</if>
		<if test="startTime != '' and startTime != null and endTime != '' and endTime != null">
			and !(h.start_time>#{endTime} or  h.end_time &lt; #{startTime})
		</if>
		<!-- <if test="jobs != '' and jobs != null and jobs =='护理类'">
			and h.jobs in ('护士','护士长')
		</if>
		<if test="jobs != '' and jobs != null and jobs =='非护理类'">
			and h.jobs not in ('护士','护士长')
		</if> -->
		<if test="outAddress != '' and outAddress != null">
			and h.out_address LIKE CONCAT('%',#{outAddress},'%')
		</if>
	    <if test="ssoOrgCode != null and ssoOrgCode != ''">
	 	   and e.sso_org_code = #{ssoOrgCode}
	    </if>
  </select>
</mapper>