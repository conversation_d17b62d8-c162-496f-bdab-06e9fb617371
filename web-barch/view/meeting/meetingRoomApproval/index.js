'use strict';
define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            //会议流程
            var meetingRoomApprovalTable = new $.trasenTable('meetingRoomApprovalTable', {
                url: common.url + '/ts-resource/boardroom/apply/workfilw',
                pager: '#meetingRoomApprovalPage',
                mtype: 'post',
                shrinkToFit: true,
                postData: {
                    handStatus: '1',
                },
                colModel: [
                    {
                        label: '标题',
                        sortable: false,
                        name: 'motif',
                        align: 'center',
                        index: 'USER_TYPE',
                        width: 180,
                    },
                    {
                        label: '会议地点',
                        sortable: false,
                        name: 'roomNames',
                        width: 100,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            return cellvalue.replace('#', '');
                        },
                    },
                    {
                        label: '组织人',
                        sortable: false,
                        name: 'applyEmpname',
                        width: 80,
                        align: 'center',
                    },
                    {
                        label: '组织科室',
                        sortable: false,
                        name: 'applyOrgname',
                        width: 80,
                        align: 'center',
                    },
                    {
                        label: '办理状态',
                        sortable: false,
                        name: 'status',
                        width: 60,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            var text = '';
                            if (cellvalue == '0') {
                                //代办
                                text = '<span style="color:#F59A23">待办</span>';
                            } else if (cellvalue == '1') {
                                text = '<span style="color:#70B603">通过</span>';
                            } else if (cellvalue == '-1') {
                                text = '<span style="color:red">不通过</span>';
                            }
                            return text;
                        },
                    },
                    {
                        label: '发起时间',
                        name: 'createDate',
                        index: 'CREATE_DATE',
                        width: 130,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        sortable: false,
                        name: '',
                        index: '',
                        width: 40,
                        editable: false,
                        title: false,
                        align: 'center',
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cellvalue, options, rowObject) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            if (rowObject.status == '0') {
                                btns +=
                                    "<button data-serial='" +
                                    rowObject.workflowNumber +
                                    "' data-status='" +
                                    rowObject.status +
                                    "' data-taskId='" +
                                    rowObject.taskId +
                                    "' data-sfInstId='" +
                                    rowObject.eventid +
                                    "' data-applyId='" +
                                    rowObject.id +
                                    "' data-businessId='" +
                                    rowObject.businessId +
                                    "' stepNo='" +
                                    rowObject.stepNo +
                                    "' class='layui-btn  meetingRoomApprovalShowFlow'><i class='fa fa-upload deal_icon' aria-hidden='true'></i> 流程办理</button>";
                                btns +=
                                    "<button   data-serial='" +
                                    rowObject.workflowNumber +
                                    "' data-status='" +
                                    rowObject.status +
                                    "' data-taskId='" +
                                    rowObject.taskId +
                                    "' data-sfInstId='" +
                                    rowObject.eventid +
                                    "' data-applyId='" +
                                    rowObject.id +
                                    "' data-businessId='" +
                                    rowObject.businessId +
                                    "' stepNo='" +
                                    rowObject.stepNo +
                                    "'  class='layui-btn  meetingRoomApprovalshowDetails'><i class='fa fa-info-circle deal_icon' aria-hidden='true'></i> 会议详情</button>";
                            } else {
                                btns +=
                                    "<button data-serial='" +
                                    rowObject.workflowNumber +
                                    "' data-status='" +
                                    rowObject.status +
                                    "' data-taskId='" +
                                    rowObject.taskId +
                                    "' data-sfInstId='" +
                                    rowObject.eventid +
                                    "' data-applyId='" +
                                    rowObject.id +
                                    "' data-businessId='" +
                                    rowObject.businessId +
                                    "'   class='layui-btn  meetingRoomApprovalShowFlow'><i class='fa fa-cogs deal_icon' aria-hidden='true'></i> 流程查看</button>";
                                btns +=
                                    "<button   data-serial='" +
                                    rowObject.workflowNumber +
                                    "' data-status='" +
                                    rowObject.status +
                                    "' data-taskId='" +
                                    rowObject.taskId +
                                    "' data-sfInstId='" +
                                    rowObject.eventid +
                                    "' data-applyId='" +
                                    rowObject.id +
                                    "' data-businessId='" +
                                    rowObject.businessId +
                                    "'   class='layui-btn  meetingRoomApprovalshowDetails'><i class='fa fa-info-circle deal_icon' aria-hidden='true'></i> 会议详情</button>";
                            }
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: 'eventid',
                        sortable: false,
                        name: 'eventid',
                        width: 10,
                        hidden: true,
                    },
                    //                    {label: 'taskId',  sortable: false, name: 'taskId', width: 10, hidden: true},
                    {
                        label: 'businessId',
                        sortable: false,
                        name: 'taskId',
                        width: 10,
                        hidden: true,
                    },
                    {
                        label: 'workflowNumber',
                        sortable: false,
                        name: 'workflowNumber',
                        width: 10,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evameetingRoomApprovalBaseForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();
            //时间控件
            laydate.render({
                elem: '#searchMeetingRoomApprovalDate',
                range: '~',
                trigger: 'click',
                showBottom: true,
                done: function (value, date, endDate) {
                    if (value != '') {
                        var dateArr = value.split(' ~ ');
                        $("#meetingRoomApproval input[name='serchStartTime']").val(dateArr[0] + ' 00:00:00');
                        $("#meetingRoomApproval input[name='serchEndTime']").val(dateArr[1] + ' 23:59:59');
                    } else {
                        $("#meetingRoomApproval input[name='serchStartTime']").val('');
                        $("#meetingRoomApproval input[name='serchEndTime']").val('');
                    }
                },
            });

            //会议审批列表刷新
            function refresh() {
                meetingRoomApprovalTable.refresh();
            }

            //会议审批列表搜索
            $('#meetingRoomApprovalSearchBtn').funs('click', function () {
                refresh();
            });

            $('#meetingRoomApprovalResetBtn').funs('click', function () {
                $("#meetingRoomApproval input[name='motif']").val('');
                $('#meetingRoomApproval #searchMeetingRoomApprovalDate').val('');
                $("#meetingRoomApproval input[name='serchStartTime']").val('');
                $("#meetingRoomApproval input[name='serchEndTime']").val('');
                refresh();
            });

            $('#meetingRoomApproval .oa-nav .oa-nav_item')
                .off('click')
                .on('click', function () {
                    $(this).addClass('active').siblings().removeClass('active');
                    $("#meetingRoomApproval input[name='handStatus']").val($(this).attr('data-value'));
                    refresh();
                });

            //显示流程详情
            $('#meetingRoomApproval')
                .off('click', '.meetingRoomApprovalShowFlow')
                .on('click', '.meetingRoomApprovalShowFlow', function () {
                    var data = {
                        taskId: $(this).attr('data-taskId'),
                        applyId: $(this).attr('data-applyId'),
                        workId: $(this).attr('data-sfInstId'),
                        boardRoomStatus: $(this).attr('data-status'),
                        stepNo: $(this).attr('stepNo'),
                        //  serial:$(this).attr("data-serial"),
                        id: $(this).attr('data-businessId'),
                    };
                    $.quoteFun('/meeting/meetingRoomApproval/approvalDetail', {
                        title: '会议室流程审批详情',
                        ref: refresh,
                        data: data,
                    });
                });
            //显示会议详情
            $('#meetingRoomApproval')
                .off('click', '.meetingRoomApprovalshowDetails')
                .on('click', '.meetingRoomApprovalshowDetails', function () {
                    var data = {
                        applyId: $(this).attr('data-applyId'),
                        id: $(this).attr('data-businessId'),
                    };
                    $.quoteFun('/meeting/myAppointment/meetingDetails', {
                        title: '会议详情',
                        ref: refresh,
                        data: data,
                    });
                });
        });
    };
});
