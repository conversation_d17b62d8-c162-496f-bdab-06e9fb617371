'use strict';
define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            //假期流程
            var leaveApprovalTable = new $.trasenTable('leaveApprovalTable', {
                url: common.url + '/ts-leave/leaveRecord/apply/workfilw',
                pager: '#leaveApprovalPage',
                mtype: 'post',
                shrinkToFit: true,
                postData: {
                    handStatus: '1',
                },
                colModel: [
                    {
                        label: '标题/流水号',
                        name: 'workflowNumber',
                        sortable: false,
                        align: 'center',
                        formatter: function (cell, opt, row) {
                            var data = {
                                1: '一般',
                                2: '加急',
                                3: '急件',
                                4: '特急',
                            };
                            var color = row.urgencyLevel >= 3 ? 'style="color:red"' : '';
                            var level = row.urgencyLevel ? '[' + data[row.urgencyLevel] + ']' : '';
                            return '<span ' + color + '>' + level + ' </span> ' + row.workflowTitle + '<br>' + row.workflowNumber;
                        },
                    },
                    { label: '请/销假类型', sortable: false, name: 'leaveTypeName', width: 100, align: 'center' },
                    { label: '类型', sortable: false, name: 'workflowTypeName', width: 60, align: 'center' },
                    { label: '当前节点', sortable: false, name: 'currentStepName', align: 'center' },
                    {
                        label: '审批状态',
                        name: 'status',
                        sortable: false,
                        width: 60,
                        align: 'center',
                        formatter: function (cell, opt, row) {
                            var data = {
                                0: '草稿',
                                1: '在办',
                                2: '办结',
                                3: '强制结束',
                                4: '撤销',
                            };
                            return data[row.status];
                        },
                    },
                    { label: '发起时间', sortable: false, name: 'createDate', align: 'center' },
                    {
                        label: '操作',
                        name: '',
                        sortable: false,
                        width: 100,
                        align: 'center',
                        title: false,
                        formatter: function (cell, opt, row) {
                            if ($('#processMyDeal [name="handleStatus"]').val() == 1) {
                                if (row.currentStepName == '重新提交') {
                                    return '<span style="color: #5260ff;cursor:pointer;" class="dealWf" type="restart" row-id="' + opt.rowId + '">重新提交</span> ';
                                }
                                return '<span style="color: #5260ff;cursor:pointer;" class="dealWf" row-id="' + opt.rowId + '">办理</span> ';
                            } else {
                                return '<span style="color: #5260ff;cursor:pointer;" class="seeWf">查看</span>';
                            }
                        },
                    },
                    { label: 'id', sortable: false, name: 'id', width: 10, hidden: true },
                    { label: 'eventid', sortable: false, name: 'eventid', width: 10, hidden: true },
                    { label: 'businessId', sortable: false, name: 'taskId', width: 10, hidden: true },
                    { label: 'workflowNumber', sortable: false, name: 'workflowNumber', width: 10, hidden: true },
                ],
                buidQueryParams: function () {
                    var search = $('#evaLeaveApprovalBaseForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            //时间控件
            laydate.render({
                elem: '#leaveLaunchApprovalDate',
                range: '~',
                showBottom: true,
                done: function (value, date, endDate) {
                    if (value != '') {
                        var dateArr = value.split(' ~ ');
                        $("input[name='serchStartTime']").val(dateArr[0] + ' 00:00:00');
                        $("input[name='serchEndTime']").val(dateArr[1] + ' 59:59:59');
                    }
                },
            });

            //列表刷新
            function refresh() {
                leaveApprovalTable.refresh();
            }

            //搜索按钮
            $('#leaveApprovalSearchBtn').funs('click', function () {
                refresh();
            });

            //重置按钮
            $('#leaveApprovalResetBtn').funs('click', function () {
                $("input[name='motif']").val('');
                $('#leaveLaunchApprovalDate').val('');
                $("input[name='serchStartTime']").val('');
                $("input[name='serchEndTime']").val('');
                $("input[name='handStatus']").val($('.topTabBox span.shell-active').attr('data-value'));
                refresh();
            });

            // //tab切换
            // $(".topTabBox span").click(function() {
            //     $(this).siblings('span').removeClass('shell-active');  // 删除其他兄弟元素的样式
            //     $(this).addClass('shell-active');                            // 添加当前元素的样式
            //     $("input[name='handStatus']").val($(this).attr("data-value"));
            //     refresh()
            // });

            //办理
            $('.jqTable shell-layer-table-page')
                .off('click', '.leaveAuditApproval')
                .on('click', '.leaveAuditApproval', function () {
                    // $.quoteFun('/meeting/meetingRoomApproval/approvalDetail', {
                    //     title : '流程办理',
                    //     ref : refresh,
                    //     taskId:$(this).attr("data-taskId"),
                    //     applyId:$(this).attr("data-applyId"),
                    //     sfInstId:$(this).attr("data-sfInstId"),
                    //     status:$(this).attr("data-status"),
                    //     serial:$(this).attr("data-serial"),
                    //     businessId:$(this).attr("data-businessId")
                    //
                    // });
                    var consumptionType = $(this).attr('consumptionType');
                    var workflowNo = '';
                    if (consumptionType == 2) {
                        //销假
                        workflowNo = 'LEAVE_REDUCE_APPPLY';
                    } else {
                        //后面改成从记录中获取：请假流程有多个，没法指定
                        workflowNo = 'LEAVE_APPLY_1'; //暂时指定一个测试
                    }

                    var rowId = $(this).attr('row-id');
                    var rowData = leaveApprovalTable.getSourceRowData();
                    var type = $(this).attr('type');

                    $.ajax({
                        method: 'get',
                        url: '' + '/ts-workflow/workflow/definition/code/' + workflowNo,
                        success: function (res) {
                            if (res.success) {
                                if (res.object) {
                                    if (type == 'restart') {
                                        var uri = encodeURI(
                                            '/otherView/processAudit/index.html?workflowNo=' +
                                                rowData.workflowNo +
                                                '&businessId=' +
                                                rowData.businessId +
                                                '&wfInstanceId=' +
                                                rowData.wfInstanceId +
                                                '&taskId=' +
                                                rowData.taskId +
                                                '&workflowNumber=' +
                                                rowData.workflowNumber +
                                                '&currentStepName=' +
                                                rowData.currentStepName +
                                                '&currentStepNo=' +
                                                rowData.currentStepNo +
                                                '&role=self&type=restart'
                                        );
                                        var son = window.open(uri, createUUID(), 'menubar=0,titlebar=0,toolbar=0,width=1300, height=600');
                                        common.openedWindow.push(son);
                                    } else {
                                        var uri = encodeURI(
                                            '/otherView/processAudit/index.html?workflowNo=' +
                                                rowData.workflowNo +
                                                '&businessId=' +
                                                rowData.businessId +
                                                '&wfInstanceId=' +
                                                rowData.wfInstanceId +
                                                '&taskId=' +
                                                rowData.taskId +
                                                '&workflowNumber=' +
                                                rowData.workflowNumber +
                                                '&currentStepName=' +
                                                rowData.currentStepName +
                                                '&currentStepNo=' +
                                                rowData.currentStepNo
                                        );
                                        var son = window.open(uri, createUUID(), 'menubar=0,titlebar=0,toolbar=0,width=1300, height=600');
                                        common.openedWindow.push(son);
                                    }
                                } else {
                                    layer.msg(res.message || '获取流程信息失败');
                                }
                            } else {
                                layer.msg(res.message || '审批失败');
                            }
                        },
                    });
                });
            //查看
            $('.jqTable shell-layer-table-page')
                .off('click', '.leaveAuditDetails')
                .on('click', '.leaveAuditDetails', function () {
                    // $.quoteFun('/meeting/myAppointment/meetingDetails', {
                    //     title : '查看详情',
                    //     applyId:$(this).attr("data-applyId"),
                    //     businessId:$(this).attr("data-businessId"),
                    //     ref : refresh
                    // });

                    var consumptionType = $(this).attr('consumptionType');
                    var workflowNo = '';
                    if (consumptionType == 2) {
                        //销假
                        workflowNo = 'LEAVE_REDUCE_APPPLY';
                    } else {
                        //后面改成从记录中获取：请假流程有多个，没法指定
                        workflowNo = 'LEAVE_APPLY_1'; //暂时指定一个测试
                    }
                    var rowId = $(this).attr('row-id');
                    var rowData = leaveApprovalTable.getSourceRowData();

                    $.ajax({
                        method: 'get',
                        url: '' + '/ts-workflow/workflow/definition/code/' + workflowNo,
                        success: function (res) {
                            if (res.success) {
                                if (res.object && res.object.isNormal == 'N') {
                                    var uri = '';
                                    uri = encodeURI(
                                        '/otherView/processAudit/index.html?workflowNo=' +
                                            rowData.workflowNo +
                                            '&businessId=' +
                                            rowData.businessId +
                                            '&wfInstanceId=' +
                                            rowData.wfInstanceId +
                                            '&taskId=' +
                                            rowData.taskId +
                                            '&workflowNumber=' +
                                            rowData.workflowNumber +
                                            '&currentStepName=' +
                                            rowData.currentStepName +
                                            '&currentStepNo=' +
                                            rowData.currentStepNo +
                                            '&role=self&type=see'
                                    );
                                    var son = window.open(uri, createUUID(), 'menubar=0,titlebar=0,toolbar=0,width=1300, height=600');
                                    common.openedWindow.push(son);
                                } else {
                                    layer.msg(res.message || '获取流程信息失败');
                                }
                            } else {
                                layer.msg(res.message || '失败');
                            }
                        },
                    });
                });
        });
    };
});
