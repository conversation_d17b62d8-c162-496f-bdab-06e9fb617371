"use strict";

define(function (require, exports, module) {
	var init = function () {
		return perform();
	}
	module.exports = {
		init: init
	}
	var perform = function () {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect', 'zTreeSearch'], function () {
			var api = "selectInfoStatisticsChannel";
			$('#informationStatistics')
				.off('click', '.infoChannelItem')
				.on('click', '.infoChannelItem', function () {
						$(this).addClass('infoChannelItemPress').siblings('.infoChannelItem').removeClass('infoChannelItemPress');
						api = $(this).attr("btn-type");
						barContainer();
				});
			
			barContainer();
			function barContainer(){
				var dom = document.getElementById("container");
				var myChart = echarts.init(dom);
				var option;
				var xAxisData = [];
				var seriesData = [];
				$.ajax({
					type: 'get',
					url: common.url + '/ts-information/information/' + api,
					async:false,
					success: function (res) {
						if (res.success) {
							$.each(res.object,function(i,obj){
								xAxisData.push(obj.name);
								seriesData.push(obj.number);
							})
						} else {
							layer.msg(res.message);
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});
				
				option = {
					title: {
						//text: '发布数量'
						// subtext: '纯属虚构'
					},
					xAxis: {
						type: 'category',
						data: xAxisData,
						axisLabel: {
							interval: 0,    //强制文字产生间隔
							rotate: 25,     //文字逆时针旋转45°
							textStyle: {    //文字样式
								color: "black",
								fontSize: 12,
								fontFamily: 'Microsoft YaHei'
							}
						}
					},
					yAxis: {
						type: 'value'
					},
					series: [{
						data: seriesData,
						type: 'bar',
						showBackground: true,
						backgroundStyle: {
							color: 'rgba(180, 180, 180, 0.2)'
						},
						itemStyle: {
							normal: {
								label: {
									show: true,      //开启显示
									position: 'top', //在上方显示
									textStyle: {     //数值样式
										color: 'black',
										fontSize: 16
									}
								}
							}
						}
					}]
				};
				
				if (option && typeof option === 'object') {
					myChart.setOption(option);
				}
			}
			
			loadLineContainer();
			function loadLineContainer(){
				var dom = document.getElementById("lineContainer");
				var myChart = echarts.init(dom);
				var option;
				var xAxisData = [];
				var seriesData = [];
				$.ajax({
					type: 'get',
					url: common.url + '/ts-information/information/selectInfoStatisticsMonth',
					async:false,
					success: function (res) {
						if (res.success) {
							$.each(res.object,function(i,obj){
								xAxisData.push(obj.name);
								seriesData.push(obj.number);
							})
						} else {
							layer.msg(res.message);
						}
					},
					error: function (res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message);
					}
				});

				option = {
					title: {
						text: '栏目发布趋势图（近一年）'
						// subtext: '纯属虚构'
					},
					xAxis: {
						type: 'category',
						data: xAxisData
					},
					yAxis: {
						type: 'value'
					},
					series: [{
						data: seriesData,
						type: 'line',
						itemStyle: {
							normal: {
								label: {
									show: true,      //开启显示
									position: 'top', //在上方显示
									textStyle: {     //数值样式
										color: 'black',
										fontSize: 16
									}
								}
							}
						}
					}]
				};

				if (option && typeof option === 'object') {
					myChart.setOption(option);
				}
			}
		})
	}
})