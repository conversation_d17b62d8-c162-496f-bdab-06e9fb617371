'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch;
            var index = 0;
            $('#securityListBox .oa-nav .oa-nav_item').click(function () {
                $('#securityListBox .oa-nav .oa-nav_item').removeClass('active');
                $(this).addClass('active');
                index = $(this).attr('data-value');
                $('#securityListBox .securityCheckListBox').hide();
                $('#securityListBox .securityCheckListBox').eq($(this).index()).show();
                $('#securityListBox .queryForm').hide();
                $('#securityListBox .queryForm').eq($(this).index()).show();
                refreshTable();
            });
            function refreshTable() {
                if (index == 0) {
                    draftsecurityCheckTable.refresh();
                } else if (index == 1) {
                    if (confirmedsecurityCheckTable) {
                        confirmedsecurityCheckTable.refresh();
                    } else {
                        initconfirmedSecurityCheckDataTable();
                    }
                } else if (index == 2) {
                    if (failsecurityCheckTable) {
                        failsecurityCheckTable.refresh();
                    } else {
                        initfailSecurityCheckDataTable();
                    }
                } else if (index == 3) {
                    if (rectificationsecurityCheckTable) {
                        rectificationsecurityCheckTable.refresh();
                    } else {
                        initrectificationSecurityCheckDataTable();
                    }
                } else if (index == 4) {
                    if (completedSecuritysecurityCheckTable) {
                        completedSecuritysecurityCheckTable.refresh();
                    } else {
                        initcompletedSecurityCheckDataTable();
                    }
                }
                getDraftCount();
                getNoFailCountByCurrentUser();
                initProjectSelect();
            }

            var securityCheckRouteChoose;
            function arrToString(arr, field) {
                var str = '';
                for (var i = 0; i < arr.length; i++) {
                    str += arr[i][field] + ',';
                }
                if (str.length > 0) {
                    str = str.substr(0, str.length - 1);
                }
                return str;
            }

            securityCheckRouteChoose = new $.checkSelect('.securityStartCheckRouteChoose', {
                url: '/ts-oa/api/checkroute/list',
                type: 'post', //请求类型
                datatype: 'json',
                label: 'routeName',
                // postData: {
                //     status: 0,
                // },
                value: 'id',
                condition: 'condition',
            });

            $.each($('.securityStartCheckcheckDate'), function () {
                var _this = this;
                laydate.render({
                    elem: this,
                    range: '~',
                    trigger: 'click',
                    showBottom: true,
                    done: function (value, date, endDate) {
                        var dateArr = value.split(' ~ ');
                        $(_this).siblings().eq(0).val(dateArr[0]);
                        $(_this).siblings().eq(1).val(dateArr[1]);
                    },
                });
            });

            // 查询
            $('.securityStartCheckSearch, .securityStartCheckScreenSearch').funs('click', function () {
                refreshTable();
            });
            // 重置
            $('.resetSecurityStartCheck, .resetSecurityStartScreenCheck').funs('click', function () {
                $('.securityStartCheckcheckDate').val('');
                securityCheckRouteChoose.setSelData([]);
                $('#isStandard').val('');
                $('.checkUser').val('');
                $('.securitystartCheckDate').val('');
                $('.securityendCheckDate').val('');

                $('.examineUser').val('');
                $('#assignUserDeptName').val('');
                $('.securitystartCheckDate').val('');
                $('.securityendCheckDate').val('');
                $('.rectifiedcheckDate').val('');
                $('#isRectification').val('');
                $('.inspectedDepartmentNameSearch').val('');
                form.render();
                refreshTable();
            });

            //查询草稿的数量

            getDraftCount();
            getNoFailCountByCurrentUser();
            initProjectSelect();
            
            function getDraftCount() {
                $.ajax({
                    type: 'post',
                    url: '/ts-oa/api/startCheck/list',
                    data:{
                        status: 0
                    },
                    success: function (resp) {

                       if(resp.rows.length>0) {
                        var html = '草稿<span  style="color:red">('+resp.rows.length+')</span>';
                        $("#securityCheckDraftBut").html(html);
                       }else {
                        $("#securityCheckDraftBut").html("草稿");
                       }
                       
                    },
                });
            }

            function getNoFailCountByCurrentUser() {
                $.ajax({
                    type: 'post',
                    url: '/ts-oa/api/startCheck/getNoFailCountByCurrentUser',
                    success: function (resp) {
                       if(resp.object >0) {
                            $('#securityListBox .tab-dot').removeClass('none');
                       }
                    },
                });
            }

            function initProjectSelect() {
              $.ajax({
                url: '/ts-basics-bottom/dictItem/getDictItemByTypeCode',
                type: 'get',
                data: {
                    typeCode: 'PATROL_PROJECT',
                },
                contentType: 'application/json',
                success: function (res) {
                  if(res.success) {
                    let domList = $('#securityListBox .oa-nav-search [name="projectName"]'),
                      child = res.object.map(item => {
                        return `<option value="${item.itemName}">${item.itemName}</option>`
                      });
                    child.unshift('<option value="">全部</option>');
                    child = child.join('');
                    $.each(domList, function(index, dom) {
                      $(dom).empty();
                      $(dom).append(child);
                    })
                    form.render('select');
                  }
                }
              })
            }

            var draftsecurityCheckTable;
            var confirmedsecurityCheckTable;
            var failsecurityCheckTable;
            var rectificationsecurityCheckTable;
            var completedSecuritysecurityCheckTable;

            initDraftSecurityCheckDataTable();

            //查询数量

            //草稿
            function initDraftSecurityCheckDataTable() {
                draftsecurityCheckTable = new $.trasenTable('draftSecurityCheckListTable', {
                    url: common.url + '/ts-oa/api/startCheck/list',
                    pager: '#draftSecurityCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 0,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '所属项目',
                            sortable: false,
                            name: 'projectName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查路线',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查地点',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '被检查科室',
                            sortable: false,
                            name: 'inspectedDepartmentName',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '核查人',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '其他参与人',
                            sortable: false,
                            name: 'otherUserName',
                            width: 150,
                            align: 'left',
                        },
                        
                        {
                            label: '创建时间',
                            sortable: false,
                            name: 'createDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn editEvaBaseLinkMan"    title="编辑" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                                btns += '<button class="layui-btn deletedCheckBut"   title="删除" row-id="' + options.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var opt = $('#securityDraftForm').serializeArray();
                        var data = {};
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.status = 0;
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }
            //待确认
            function initconfirmedSecurityCheckDataTable() {
                confirmedsecurityCheckTable = new $.trasenTable('confirmedSecurityCheckListTable', {
                    url: common.url + '/ts-oa/api/startCheck/list',
                    pager: '#confirmedSecurityCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 1,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '所属项目',
                            sortable: false,
                            name: 'projectName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查路线',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查地点',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '被检查科室',
                            sortable: false,
                            name: 'inspectedDepartmentName',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '检查人',
                            sortable: false,
                            name: 'checkUserName',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '其他人员',
                            sortable: false,
                            name: 'otherUserName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '核查人',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查结果',
                            sortable: false,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'center',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = "<span style='color:red;'>"+cell + '项不达标'+"</span>";
                                } else {
                                    html = '全部达标';
                                }
                                return html;
                            },
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = "";
                                var btns = '';
                                if(row.isPermissions || row.userCode == row.examineUser) {
                                    html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                    btns += '<button class="layui-btn confirmCheck confirmCheckButDeletedClass"  id="confirmCheckBut" title="确认" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>确认 </button>';
                                    html += btns + '</div></div>';
                                }
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                        {
                            label: 'examineUser',
                            sortable: false,
                            name: 'examineUser',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#securityConfirmedForm').serializeArray();
                        var opt = $('#securityConfirmedScreenForm').serializeArray();
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }

            //不通过
            function initfailSecurityCheckDataTable() {
                failsecurityCheckTable = new $.trasenTable('failSecurityCheckListTable', {
                    url: common.url + '/ts-oa/api/startCheck/list',
                    pager: '#failSecurityCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 2,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '所属项目',
                            sortable: false,
                            name: 'projectName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查路线',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查地点',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '被检查科室',
                            sortable: false,
                            name: 'inspectedDepartmentName',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '检查人',
                            sortable: false,
                            name: 'checkUserName',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '其他人员',
                            sortable: false,
                            name: 'otherUserName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '核查人',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '检查结果',
                            sortable: false,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'center',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = "<span style='color:red;'>"+cell + '项不达标'+"</span>";
                                } else {
                                    html = '全部达标';
                                }
                                return html;
                            },
                        },
                        {
                            label: '不通过原因',
                            sortable: false,
                            name: 'remark',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible jqgrid-rownum ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn editEvaBaseLinkMan"  title="编辑" row-id="' + options.rowId + '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#securityFailForm').serializeArray();
                        var opt = $('#securityFailScreenForm').serializeArray();
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }
            //整改中
            function initrectificationSecurityCheckDataTable() {
                rectificationsecurityCheckTable = new $.trasenTable('rectificationSecurityCheckListTable', {
                    url: common.url + '/ts-oa/api/startCheck/list',
                    pager: '#rectificationSecurityCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 3,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: false,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                        },
                        {
                            label: '所属项目',
                            sortable: false,
                            name: 'projectName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查路线',
                            sortable: false,
                            name: 'routeName',
                            width: 150,
                            align: 'left',
                            formatter: function (cell, opt, row) {
                                return '<p class="checkDetail"  row-id="' + opt.rowId + '"><span class="dealLink">' + cell + '</span></p>';
                            },
                        },
                        {
                            label: '检查地点',
                            sortable: false,
                            name: 'checkAddress',
                            width: 120,
                            align: 'left',
                        },
                        {
                            label: '检查人',
                            sortable: false,
                            name: 'checkUserName',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: '其他人员',
                            sortable: false,
                            name: 'otherUserName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '核查人',
                            sortable: false,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                        },
                        
                        {
                            label: '检查结果',
                            sortable: false,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'center',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = cell + '项不达标';
                                } else {
                                    html = '全部达标';
                                }
                                return html;
                            },
                        },
                        {
                            label: '交办科室',
                            sortable: false,
                            name: 'assignUserDeptName',
                            width: 120,
                            align: 'center',
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#securityRectificationForm').serializeArray();
                        var opt = $('#securityRectificationScreenForm').serializeArray();
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }

            //已完成
            function initcompletedSecurityCheckDataTable() {
                completedSecuritysecurityCheckTable = new $.trasenTable('completedSecurityCheckListTable', {
                    url: common.url + '/ts-oa/api/startCheck/list',
                    pager: '#completedSecurityCheckListPage',
                    mtype: 'post',
                    shrinkToFit: true,
                    postData: {
                        status: 4,
                    },
                    colModel: [
                        {
                            label: '检查日期',
                            sortable: true,
                            name: 'checkDate',
                            width: 150,
                            align: 'center',
                            index:'s.check_date',
                        },
                        {
                            label: '所属项目',
                            sortable: false,
                            name: 'projectName',
                            width: 150,
                            align: 'left',
                        },
                        {
                            label: '检查路线',
                            sortable: true,
                            name: 'routeName',
                            width: 150,
                            align: 'left',
                            index:'r.route_name',
                            formatter: function (cell, opt, row) {
                                return '<p class="checkDetail"  row-id="' + opt.rowId + '"><span class="dealLink">' + cell + '</span></p>';
                            },
                        },
                        {
                            label: '检查地点',
                            sortable: true,
                            name: 'checkAddress',
                            width: 120,
                            align: 'left',
                            index:'a1.check_address',
                        },
                        {
                            label: '被检查科室',
                            sortable: true,
                            name: 'inspectedDepartmentName',
                            width: 120,
                            align: 'left',
                            index:'s.inspected_department_name',
                        },
                        {
                            label: '检查人',
                            sortable: true,
                            name: 'checkUserName',
                            width: 120,
                            align: 'center',
                            index:'s.check_user_name',
                        },

                        {
                            label: '其他人员',
                            sortable: true,
                            name: 'otherUserName',
                            width: 150,
                            align: 'left',
                            index:'s.other_user_name',
                        },

                        {
                            label: '核查人',
                            sortable: true,
                            name: 'examineUserName',
                            width: 150,
                            align: 'center',
                            index:'s.examine_user_name',
                        },
                        
                        {
                            label: '是否整改',
                            sortable: true,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'center',
                            index:'s.unqualified_count',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = '是';
                                } else {
                                    html = '否';
                                }
                                return html;
                            },
                        },
                        {
                            label: '整改项',
                            sortable: true,
                            name: 'unqualifiedCount',
                            width: 120,
                            align: 'left',
                            index:'s.unqualified_count',
                            formatter: function (cell, opt, row) {
                                var html = '';
                                if (cell > 0) {
                                    html = cell;
                                } else {
                                    html = '-';
                                }
                                return html;
                            },
                        },
                        {
                            label: '办结时间',
                            sortable: true,
                            name: 'updateDate',
                            width: 120,
                            align: 'center',
                            index:'s.update_date',
                        },
                        {
                            label: 'id',
                            sortable: false,
                            name: 'id',
                            width: 10,
                            hidden: true,
                        },
                        {
                            label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                            name: '',
                            sortable: false,
                            width: 40,
                            editable: false,
                            title: false,
                            align: 'center',
                            classes: 'visible  ui-state-default',
                            formatter: function (cellvalue, options, row) {
                                var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                                var btns = '';
                                btns += '<button class="layui-btn checkPrint"  title="打印" row-id="' + options.rowId + '"> <i class="fa fa-print deal_icon" aria-hidden="true"></i>打印 </button>';
                                btns += '<button class="layui-btn deletedCheckBut"   title="删除" row-id="' + options.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                                html += btns + '</div></div>';
                                return html;
                            },
                        },
                    ],
                    buidQueryParams: function () {
                        var search = $('#securityCompletedForm').serializeArray();
                        var opt = $('#securityCompletedScreenForm').serializeArray();
                        var data = {};
                        for (var i in search) {
                            opt.push(search[i]);
                        }
                        for (var i in opt) {
                            data[opt[i].name] = opt[i].value;
                        }
                        data.checkRoutes = arrToString(securityCheckRouteChoose.getSelData(), 'id');
                        return data;
                    },
                });
            }

            form.render('select');

            //发起检查
            $('#securityListBox .startCheckBut')
                .off('click')
                .on('click', function () {
                    $.quoteFun('/security/check/modules/checkRoute', {
                        ref: refreshTable,
                    });
                });
            function getRowData(id) {
                var data;
                if (index == 0) {
                    data = draftsecurityCheckTable.getSourceRowData(id);
                } else if (index == 1) {
                    data = confirmedsecurityCheckTable.getSourceRowData(id);
                } else if (index == 2) {
                    data = failsecurityCheckTable.getSourceRowData(id);
                } else if (index == 3) {
                    data = rectificationsecurityCheckTable.getSourceRowData(id);
                } else if (index == 4) {
                    data = completedSecuritysecurityCheckTable.getSourceRowData(id);
                }
                return data;
            }
            //编辑 editEvaBaseLinkMan
            $('#securityListBox')
                .off('click', '.editEvaBaseLinkMan')
                .on('click', '.editEvaBaseLinkMan', function () {
                    var data = getRowData($(this).attr('row-id'));
                    $.ajax({
                        url: '/ts-oa/api/startCheck/findById/' + data.id,
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('/security/check/modules/start', {
                                    data: res.object,
                                    ref: refreshTable,
                                });
                            }
                        },
                    });
                });
                //删除

                $('#securityListBox').off('click', '.deletedCheckBut').on('click', '.deletedCheckBut', function () {
                    var data = getRowData($(this).attr('row-id'));
                    var id = data.id;
                    if (!id) {
                        layer.msg('请选择一条需要删除的数据!');
                        return false;
                    }
                    layer.confirm(
                        '确定要删除吗？',
                        {
                            btn: ['确定', '取消'],
                            title: '提示',
                            closeBtn: 0
                        },
                        function (index) {
                            layer.close(index);
                            $.ajax({
                                type: 'post',
                                contentType: 'application/json; charset=utf-8',
                                url: '/ts-oa/api/startCheck/delete/'+id,
                                success: function (resp) {
                                    if (resp.success) {
                                        refreshTable();
                                        layer.closeAll();
                                        layer.msg('操作成功');
                                    } else {
                                        layer.msg(resp.message || '操作失败！');
                                    }
                                },
                            });
                        }
                    );
                });
            //确认检查 confirmCheck
            $('#securityListBox')
                .off('click', '.confirmCheck')
                .on('click', '.confirmCheck', function () {
                    var data = getRowData($(this).attr('row-id'));
                    $.ajax({
                        url: '/ts-oa/api/startCheck/findById/' + data.id,
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('/security/check/modules/confirm', {
                                    data: res.object,
                                    ref: refreshTable,
                                });
                            }
                        },
                    });
                });
            //详情
            $('#securityListBox')
                .off('click', '.checkDetail')
                .on('click', '.checkDetail', function () {
                    var data = getRowData($(this).attr('row-id'));
                    $.ajax({
                        url: '/ts-oa/api/startCheck/findById/' + data.id,
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('/security/check/modules/confirm', {
                                    data: res.object,
                                    type: 'detail',
                                    ref: refreshTable,
                                });
                            }
                        },
                    });
                });
            //打印
            $('#securityListBox')
                .off('click', '.checkPrint')
                .on('click', '.checkPrint', function () {
                    var data = getRowData($(this).attr('row-id'));
                    $.ajax({
                        url: '/ts-oa/api/startCheck/findById/' + data.id,
                        success: function (res) {
                            if (res.success) {
                                $.quoteFun('/security/check/modules/print', {
                                    data: res.object,
                                });
                            }
                        },
                    });
                });

            //导出
            $('.expotCheckBut').funs('click', function () {
                var queryData;
                if (index == 0) {
                    queryData = draftsecurityCheckTable.oTable.getGridParam('postData'); draftsecurityCheckTable.refresh();
                } else if (index == 1) {
                    queryData = confirmedsecurityCheckTable.oTable.getGridParam('postData'); draftsecurityCheckTable.refresh();
                } else if (index == 2) {
                    queryData = failsecurityCheckTable.oTable.getGridParam('postData'); draftsecurityCheckTable.refresh();
                } else if (index == 3) {
                    queryData = rectificationsecurityCheckTable.oTable.getGridParam('postData'); draftsecurityCheckTable.refresh();
                } else if (index == 4) {
                    queryData = completedSecuritysecurityCheckTable.oTable.getGridParam('postData'); draftsecurityCheckTable.refresh();
                }
                var url = common.url + '/ts-oa/api/startCheck/export?';
                var exportParam = '';
                for (var key in queryData) {
                    exportParam += key + '=' + queryData[key] + '&';
                }
                // alert(url + exportParam);

                // return false;
                location.href = url + exportParam;
            });
        });
    };
});
