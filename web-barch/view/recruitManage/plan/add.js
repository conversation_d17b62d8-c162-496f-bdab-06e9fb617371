"use strict";
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(["form", "zTreeSearch", "layer", "element", "trasen"], function () {
      let {
        form,
        laydate,
        layer,
        element,
        upload,
        trasen
      } = layui;
      let API = {
        templatePlanPost: "/ts-hrms/recruitPlan/download/template/planPost",
        getFileAttachmentByBusinessId: "/ts-basics-bottom/fileAttachment/getFileAttachmentByBusinessId",
        getFileAttachmentIds: "/ts-basics-bottom/fileAttachment/getByIds"
      };
      let ed = {};
      //招聘公告id上传
      let recruitNoticeId = [];
      let recruitNoticeArr = [];
      //附件ids上传
      let fileuuId;
      let fileIds = [];
      let fileArr = [];
      let recruitManagePlanAddDialog = layer.open({
        type: 1,
        title: opt.title,
        closeBtn: 1,
        shadeClose: false,
        area: ["1050px", "90%"],
        content: html,
        success: function (layero, index) {
          tinymce.remove("#recruitManageAddAnnouncement");

          // 回显 招聘计划报名时间
          let formDateDefualValue = "";
          if (opt.type === "edit") {
            let dateIndex = opt.data.intervalTime.indexOf("至");
            let dateStrArr = opt.data.intervalTime.split("");
            dateStrArr.splice(dateIndex, 1, "~");
            formDateDefualValue = dateStrArr.join("");

            // 回显招聘计划 岗位附件
            if (opt.data.postFileId) {
              renderPlanAnnouncementHandle(opt.data.postFileId);
            }

            // 回显招聘计划 相关附件
            if (opt.data.businessFileId) {
              renderPlanFileHandle(opt.data.businessFileId);
              fileuuId = opt.data.businessFileId;
            } else {
              fileuuId = createUUID();
            }

            trasen.setNamesVal(layero, opt.data);
          } else {
            fileuuId = createUUID();
          }

          // 报名开始时间 结束时间
          laydate.render({
            elem: "#recruitManageAddFormDate",
            showBottom: true,
            range: "~",
            value: formDateDefualValue || "",
            min: new Date().format("yyyy-MM-dd"),
            done: function (value, date, endDate) {
              let dateArr = value.split(" ~ ");
              $("#recruitManageAddFormStart").val(dateArr[0]);
              $("#recruitManageAddFormEnd").val(dateArr[1]);
            }
          });

          initEditor();
          form.render();
        },
        cancel: function (index) {
          $("recruitManageAddAnnouncement").hide();
          // tinymce.remove("#recruitManageAddAnnouncement");
        }
      });

      // 回显招聘计划 岗位附件
      function renderPlanAnnouncementHandle (fileId) {
        recruitNoticeId[0] = fileId;
        recruitNoticeArr = [];
        $("#RecruitNoticeBox").html("");
        $.ajax({
          type: "get",
          url: common.url + API.getFileAttachmentIds,
          contentType: "application/json;charset=UTF-8",
          data: {
            ids: fileId
          },
          success: function (res) {
            if (res.success) {
              let filesHtml = "";
              let fileInfoArr = res.object.map(item => {
                return {
                  fileName: item.id + "." + item.fileExtension,
                  fileExtension: item.fileExtension,
                  fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.id,
                  fileId: item.id,
                  fileRealName: item.originalName
                };
              });

              fileInfoArr.forEach(item => {
                recruitNoticeArr.push({
                  fileName: item.fileName,
                  fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.fileId
                });
                let isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
                let isDoc = common.isDoc(item.fileName);
                filesHtml += isImg ? `<li>
                                        <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                        ${item.fileRealName}
                                        <a class="previewA viewerImg" fileurl="${item.fileId}" href="javascript:void(0)">预览</a>
                                        <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                      </li>`
                  : "";

                filesHtml += isDoc ? `<li>
                                        <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                        ${item.fileRealName}
                                        <a class="previewA viewerDocBase" filename="${item.fileName}" fileid="${item.fileId}" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/${item.fileId}" href="javascript:void(0)">预览</a>
                                        <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                      </li>` : "";
              });
              $("#RecruitNoticeBox").html(filesHtml);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          }
        });
      }

      // 回显招聘计划 相关附件
      function renderPlanFileHandle (businessId) {
        $("#recruitManageAddPlanFileUl").html("");
        $.ajax({
          type: "get",
          url: common.url + API.getFileAttachmentByBusinessId + "?businessId=" + businessId,
          contentType: "application/json;charset=UTF-8",
          success: function (res) {
            if (res.success) {
              let filesHtml = "";
              let fileInfoArr = res.object.map(item => {
                return {
                  fileName: item.id + "." + item.fileExtension,
                  fileExtension: item.fileExtension,
                  fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.id,
                  fileId: item.id,
                  fileRealName: item.originalName
                };
              });

              fileInfoArr.forEach(item => {
                fileArr.push({
                  fileName: item.fileName,
                  fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.fileId
                });
                let isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
                let isDoc = common.isDoc(item.fileName);

                filesHtml += isImg ? `<li>
                                        <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                        ${item.fileRealName}
                                        <a class="previewA viewerImg" fileurl="${item.fileId}" href="javascript:void(0)">预览</a>
                                        <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                      </li>`
                  : "";

                filesHtml += isDoc ? `<li>
                                        <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                        ${item.fileRealName}
                                        <a class="previewA viewerDocBase" filename="${item.fileName}" fileid="${item.fileId}" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/${item.fileId}" href="javascript:void(0)">预览</a>
                                        <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                      </li>`
                  : "";
              });
              $("#recruitManageAddPlanFileUl").append(filesHtml);
            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          }
        });
      }

      // 招聘岗位Excel
      upload.render({
        elem: "#RecruitmentPositionFile",
        url: "/ts-basics-bottom/fileAttachment/upload?moduleName=hrm",
        accept: "file",
        exts: "xlsx",
        multiple: false,
        done: function (res, index, upload) {
          let filesHtml = "";
          $.each(res.object, function (i, item) {
            recruitNoticeArr.push({
              fileName: item.fileName,
              fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.fileId
            });
            let isDoc = common.isDoc(item.fileName);

            filesHtml += isDoc ? `<li>
                                    <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                    ${item.fileRealName}
                                    <a class="previewA viewerDocBase" filename="${item.fileName}" fileid="${item.fileId}" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/${item.fileId}" href="javascript:void(0)">预览</a>
                                    <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                  </li>`
              : "";

            recruitNoticeId[0] = item.fileId;
          });
          $("#RecruitNoticeBox").html(filesHtml);
        }
      });
      //图片预览
      $("#RecruitNoticeBox").off("click", ".viewerImg").on("click", ".viewerImg", function (e) {
        common.viewerImg(recruitNoticeArr, "/ts-basics-bottom/fileAttachment/downloadFile/" + $(this).attr("fileurl"), "hr");
        e.stopPropagation();
        return false;
      });
      //删除附件
      $("#RecruitNoticeBox").off("click", ".deleteA").on("click", ".deleteA", function () {
        let fileId = $(this).attr("attr-id");
        if ($.inArray(fileId, recruitNoticeId) != -1) {
          recruitNoticeId.splice($.inArray(fileId, recruitNoticeId), 1);
        }
        $(this).parent().remove();
      });

      // 相关附件
      upload.render({
        elem: "#recruitManageAddPlanFile",
        url: "/ts-basics-bottom/fileAttachment/upload?moduleName=hrm&scope=public",
        accept: "file",
        exts: "zip|rar|7z|jpg|png|gif|bmp|jpeg|doc|docx|xls|xlsx|pdf|ppt|pptx|txt",
        multiple: true,
        data: {
          "businessId": fileuuId
        },
        allDone: function (obj) {
        },
        done: function (res, index, upload) {
          let filesHtml = "";
          $.each(res.object, function (i, item) {
            fileArr.push({
              fileName: item.fileName,
              fileUrl: "/ts-basics-bottom/fileAttachment/downloadFile/" + item.fileId
            });
            let isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
            let isDoc = common.isDoc(item.fileName);
            filesHtml += isImg ? `<li>
                                    <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                    ${item.fileRealName}
                                    <a class="previewA viewerImg" fileurl="${item.fileId}" href="javascript:void(0)">预览</a>
                                    <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                  </li>`
              : "";

            filesHtml += isDoc ? `<li>
                                    <img class='success_file_icon' src='/static/img/other/icon_file_tips.png'>
                                    ${item.fileRealName}
                                    <a class="previewA viewerDocBase" filename="${item.fileName}" fileid="${item.fileId}" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/${item.fileId}" href="javascript:void(0)">预览</a>
                                    <a class="deleteA" attr-id="${item.fileId}" href="javascript:void(0)">删除</a>
                                  </li>`
              : "";

            fileIds.push(item.fileId);
          });
          $("#recruitManageAddPlanFileUl").append(filesHtml);
        }
      });
      //图片预览
      $("#recruitManageAddPlanFileUl").off("click", ".viewerImg").on("click", ".viewerImg", function (e) {
        common.viewerImg(fileArr, "/ts-basics-bottom/fileAttachment/downloadFile/" + $(this).attr("fileurl"), "hr");
        e.stopPropagation();
        return false;
      });
      //删除附件
      $("#recruitManageAddPlanFileUl").off("click", ".deleteA").on("click", ".deleteA", function () {
        let fileId = $(this).attr("attr-id");
        if ($.inArray(fileId, fileIds) != -1) {
          fileIds.splice($.inArray(fileId, fileIds), 1);
        }
        if (opt.data.id) {
          $.ajax({
            type: "post",
            async: false,
            url: common.url + "/ts-basics-bottom/fileAttachment/deleteFileId?fileid=" + fileId,
            contentType: "application/json;charset=UTF-8",
            success: function (res) {
              if (res.success && res.statusCode === 200) {
                layer.msg("操作成功!");
              }
            }
          });
        }
        $(this).parent().remove();
      });

      // 下载招聘岗位模版
      $("#recruitManageAddForm").off("click", ".downloadRecruitment").on("click", ".downloadRecruitment", function () {
        let xhr = new XMLHttpRequest();
        let url = common.url + API.templatePlanPost;
        xhr.open("post", url, true);
        xhr.responseType = "blob";
        xhr.setRequestHeader("Content-Type", "application/json");
        xhr.send();
        xhr.onload = function () {
          if (this.status === 200) {
            let url = window.URL.createObjectURL(new Blob([this.response]));
            let link = document.createElement("a");
            link.style.display = "none";
            link.href = url;
            link.setAttribute("download", "招聘岗位模版.xlsx");
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link); //下载完成移除元素
            window.URL.revokeObjectURL(url); //释放掉blob对象
          }
        };
      });

      // 保存
      form.on("submit(recruitManageAddFormSubmit)", function (data) {
        const notice = tinymce.get("recruitManageAddAnnouncement").getContent() || "";
        if (!notice) {
          layer.msg("请填写招聘公告!");
          return;
        }

        if (!recruitNoticeId.length) {
          layer.msg("请上传招聘岗位!");
          return;
        }
        let d = data.field;

        d.notice = notice; // 通知公告
        d.postFileId = recruitNoticeId.join(","); // 招聘岗位id
        d.businessFileId = fileuuId; // 附件ids

        let url;
        if (d.id) {
          url = "/ts-hrms/recruitPlan/edit";
        } else {
          url = "/ts-hrms/recruitPlan/add";
          delete d.id;
        }
        $.loadings();

        $.ajax({
          type: "post",
          url: common.url + url,
          contentType: "application/json;charset=UTF-8",
          data: JSON.stringify(d),
          success: function (res) {
            $.closeloadings();
            if (res.success && res.statusCode === 200) {
              layer.msg("操作成功!");
              opt.ref();
              layer.close(recruitManagePlanAddDialog);
            } else {
              let message = res.message;

              let reg = /\[/g;
              if (reg.test(message)) {
                let str = "";
                JSON.parse(message).forEach(item => str += `<p>${item}</p>`);
                layer.alert(str, {area: ["700px", "350px"]});
              } else {
                layer.alert(message);
              }

            }
          },
          error: function (res) {
            res = JSON.parse(res.responseText);
            layer.msg(res.message);
          }
        });
      });

      // 关闭新增弹窗
      $("#closeRecruitManageAddForm").funs("click", function () {
        layer.close(recruitManagePlanAddDialog);
      });

      //初始化 富文本编辑器
      function initEditor () {
        if (opt.data) {
          $("#recruitManageAddAnnouncement").val(opt.data.notice);
        }
        tinymce.init({
          selector: "#recruitManageAddAnnouncement",
          language: "zh_CN",
          elementpath: false,
          branding: false,
          statusbar: false,
          plugins: ["quickbars", "link", "table", "code", "image", "advlist", "lists", "media", "paste"],
          menubar: "edit insert view format table",
          menu: {
            edit: {
              title: "Edit",
              items: " code | undo redo | cut copy paste pastetext | selectall"
            }
          },
          toolbar: ["undo redo newdocument | bold italic underline strikethrough backcolor forecolor formatselect | fontselect | fontsizeselect | lineheight ", "cut copy paste | link image  media  | table  | alignleft aligncenter alignright alignjustify | outdent indent | bullist numlist | code"],
          table_clone_elements: "p",
          table_grid: false,
          fontsize_formats: '42pt 36pt 26pt 24pt 22pt 18pt 16pt 15pt 14pt 12pt 10.5pt 9pt 7.5pt 6.5pt 5.5pt 5pt',
          font_formats: "微软雅黑='微软雅黑';宋体='宋体';黑体='黑体';仿宋='仿宋';楷体='楷体';隶书='隶书';幼圆='幼圆';Andale Mono=andale mono,times;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier;Georgia=georgia,palatino;Helvetica=helvetica;Impact=impact,chicago;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco;Times New Roman=times new roman,times;Trebuchet MS=trebuchet ms,geneva;Verdana=verdana,geneva;Webdings=webdings;Wingdings=wingdings",
          width: "100%",
          height: "100%",
          auto_focus: true,
          paste_enable_default_filters: false,
          paste_retain_style_properties: "text-align margin align",
          paste_word_valid_elements: "table[width|border],tr,td[colspan|rowspan|width],th[colspan|rowspan|width],thead,tfoot,tbody,h1,h2,h3,h4,h5,img,div[align]",
          setup: function (editor) {
            editor.on('init', function(ed) {
              ed.target.editorCommands.execCommand('fontName', false, '宋体')
            })
          },
          images_upload_handler: function (blobInfo, success, failure) {
            let formData = new FormData();
            formData.append("file", blobInfo.blob());
            $.ajax({
              url: "/ts-basics-bottom/fileAttachment/upload?moduleName=hrm",
              type: "post",
              data: formData,
              processData: false,
              contentType: false,
              async: false,
              success: function (res) {
                if (res.success == false) {
                  failure("图片上传失败" + (res.message ? ": " + res.message : "！"));
                  return;
                }
                success(res.object[0].filePath);
              }
            });
          },
          //paste_data_images: true,
          paste_postprocess: function (plugin, args) {
            let res = args.node.querySelectorAll("img");
            let flag = false;
            for (let i = 0; i < res.length; i++) {
              if (res[i].src.indexOf("file:") != -1) {
                flag = true;
              }
            }
            if (flag) {
              $.ajax({
                type: "get",
                url: "http://localhost:26789/file/readfile",
                success: function (res) {
                  updateImgBase64();
                },
                error: function (res) {
                  layer.open({
                    type: 1,
                    skin: "layui-layer-demo", //样式类名
                    closeBtn: 0, //不显示关闭按钮
                    anim: 2,
                    shadeClose: true, //开启遮罩关闭
                    content: "<div style=\"padding:10px\">粘贴WORD图文模式，您需要先安装一个插件<a style=\"color:blue\" href=\"/static/wordPasterPlug/trasenWordPaster.zip\">点击下载</a>  <br><div>"
                  });
                }
              });
            }
          },
          file_picker_types: "file media",
          convert_urls: false, //这个参数加上去就可以了
          media_alt_source: false,
          media_filter_html: false,
          powerpaste_word_import: "merge", // 参数可以是propmt, merge, clear
          powerpaste_html_import: "merge", // propmt, merge, clear
          powerpaste_allow_local_images: true, //允许带图片
          paste_data_images: true,
          file_picker_callback: function (cb, value, meta) {
            if (meta.filetype == "media") {
              //创建一个隐藏的type=file的文件选择input
              let input = document.createElement("input");
              input.setAttribute("type", "file");
              input.onchange = function () {
                let file = this.files[0];
                fileUpload(file, cb);
              };
              //触发点击
              input.click();
            }
          },
          init_instance_callback: function (editor) {
            // tinyMCE.editors["recruitManageAddAnnouncement"].setContent(opt.data.notice || '');
            //页面初始化事件
            ed = editor;
          }
        });
      }

      //文件上传
      function fileUpload (file, cb) {
        let formData = new FormData();
        //假设接口接收参数为file,值为选中的文件
        formData.append("file", file);
        $.ajax({
          url: "/ts-document/attachment/fileUpload?module=hrm&fillupf=2",
          method: "post",
          contentType: false,
          processData: false,
          data: formData,
          success: function (res) {
            cb(res.location);
          }
        });
      }

      //上传Base64图片
      function updateImgBase64 () {
        let res = ed.iframeElement.contentWindow.document.querySelectorAll("img");
        let ajax = [];
        for (let i = 0; i < res.length; i++) {
          if (res[i].src.indexOf("file:") != -1) {
            ajax.push($.get(`http://localhost:26789/file/readfile?img=${res[i].src}&dataIndex=${i}`));
          }
        }
        if (ajax.length != 0) {
          Promise.all(ajax).then(_res => {
            _res.forEach(_item => {
              res[_item.dataIndex]["data-src"] = res[_item.dataIndex]["src"];
              res[_item.dataIndex]["src"] = _item.base64;
            });
            updateImg();
          });
        }
      }

      /**@desc 提交前替换图片请求服务器资源**/
      function updateImg () {
        let res = ed.iframeElement.contentWindow.document.querySelectorAll("img");
        let ajax = [];
        let dom = [];
        for (let i = 0; i < res.length; i++) {
          if (res[i].src.indexOf("data:image/png;base64,") != -1) {
            dom.push(res[i]);
            ajax.push(
              $.post(`${url}imageBase64Upload`, {
                token,
                module: "richfile",
                fillupf: "2",
                imageBase64: res[i].src.split("data:image/png;base64,")[1]
              })
            );
          }
        }
        if (ajax.length != 0) {
          Promise.all(ajax).then(_res => {
            _res.forEach((_item, index) => {
              if (_item.success) {
                dom[index].src = `${_item.object.location}`;
                dom[index].removeAttribute("data-mce-src");
              }
            });
            //submit();
          });
        }
      }
    });
  };
});
