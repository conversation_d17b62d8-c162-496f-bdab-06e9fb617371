<style>
    .layui-col-xs11 {
        margin-bottom: 10px;
    }

    .addMsgImg .MsgPhoto {
        width: 150px;
        height: 80px;
        border: 1px solid #ccc;
        border-radius: 3px;
        padding: 3px;
        margin-bottom: 5px;
    }

    .addMsgImg .MsgPhotoBtn {
        background: #5260ff;
        color: #fff;
        line-height: 28px;
        border-radius: 3px;
        border: 0;
        width: 100px;
    }

    .addMsgImg .UserNamePhoto {
        width: 180px;
        height: 28px;
        position: absolute;
        top: 0;
    }

    .msgTemplateInputBox {
        display: none;
    }

    .formTabBox span {
        line-height: 30px;
        position: relative;
        top: -1px;
        float: left;
        padding: 0 20px;
        cursor: pointer;
    }

    .formTabBox .on {
        border-bottom: 3px solid #5260ff;
        color: #5260ff;
    }

    #pathListBox p {
        line-height: 30px;
        padding: 10px;
        border-bottom: 1px solid #ccc;
    }

    #pathListBox span {
        float: right;
        cursor: pointer;
        line-height: 24px;
        height: 24px;
        background: #FF0000;
        color: #fff;
        padding: 0 15px;
        margin-top: 3px;
        font-size: 12px;
    }
</style>
<form id="evaBaseAddonLineForm" class="layui-form">
    <input type="hidden" name="id" id="id" value="">
    <div class="layui-content-box">
        <div class="msgTemplateInputBox" style="display: block;">

            <div class="layui-col-xs8">
                <label class="shell-layui-form-label">文件上传</label>
                <div class="shell-layer-input-box">
                    <span id="DocumentFileUploadBtn"
                        style="background:#5260ff; position: relative; top: 5px; color:#fff; border-radius: 2px; font-size:12px; padding:5px 10px; margin-top: 5px; cursor: pointer;">选择附件</span>
                    <div style="color: red; padding-top: 10px;font-size: 13px;">
                        注意：只支持&nbsp;.doc&nbsp;.docx&nbsp;.xls&nbsp;.xlsx 类型的文件</div>
                </div>
                <input type="hidden" name="doc_path" autocomplete="off" class="layui-input" id="doc_path" />
            </div>
            <!--<div class="layui-upload">
                <div class="layui-col-xs11" style="padding-left:110px; box-sizing: border-box;">
                    <table class="layui-table" id="onLineDocumentFileList" style="margin-top:0; display:none;">
                        <thead>
                        &lt;!&ndash;style="visibility: hidden"&ndash;&gt;
                        <tr>
                            <th>文件名</th>
                            <th>大小</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                        </thead>
                        <tbody id="onLineDocumentFileDataList"></tbody>
                    </table>
                </div>
            </div>-->

        </div>
    </div>
    <!--<div class="layer-btn archivesTabBtn">
        <button type="button" class="layui-btn layui-btn-primary" lay-submit="" lay-filter="onLineDocumentSubmitCofirm">保存</button>
        <button type="button" class="layui-btn layui-btn-primary" id="closeLyer">关闭</button>
    </div>-->
</form>
<div id="uploadMessage" style="padding-left: 55px; padding-top: 60px; font-size: 14px;">
    <!--<span>提示信息: </span><span>信息导入成功!&nbsp;总条数:100&nbsp;成功:100&nbsp;失败:0</span>
    <div>
        <span>错误信息: </span>
    </div>-->
</div>