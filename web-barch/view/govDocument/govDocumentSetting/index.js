'use strict';

define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'element'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen,
                zTreeSearch = layui.zTreeSearch,
                element = layui.element;

            //tab 点击事件 收文设置
            $('#receiptSettings').click(function () {
                $('.receiptSettingsInnerTableBox').hide();
                $('.receiptSettingsInnerTableBox').eq(receiptSettingsInnerTableBoxIndex).show();
                $('.govDocumentSettingListBoxForm').hide();
                $('.govDocumentSettingListBoxFormT').eq(receiptSettingsInnerTableBoxIndex).show();
            });

            //发文设置
            $('#dispatchSettings').click(function () {
                $('.innerTableBox').hide();
                $('.innerTableBox').eq(innerTableBoxindex).show();
                $('.govDocumentSettingListBoxFormT').hide();
                $('.govDocumentSettingListBoxForm').eq(innerTableBoxindex).show();
            });

            //收发文切换
            $('#govDocumentSettingListBox .topTabBox .oa-nav_item').click(function () {
                $('#govDocumentSettingListBox .topTabBox .oa-nav_item').removeClass('active');
                $(this).addClass('active');
                $('#govDocumentSettingListBox .govDocumentSettingListBox').css('opacity', '0');
                $('#govDocumentSettingListBox .govDocumentSettingListBox').hide();
                $('#govDocumentSettingListBox .govDocumentSettingListBox').eq($(this).index()).css({
                    opacity: '1',
                    'z-index': '999',
                });
                $('#govDocumentSettingListBox .govDocumentSettingListBox').eq($(this).index()).show();
            });

            var innerTableBoxindex = 0;
            //发文点击事件
            $('.govDocumentSettingListVal span').click(function () {
                innerTableBoxindex = $(this).index();
                $('.receiptSettingsInnerTableBox').hide();
                $('.innerTableBox').eq(innerTableBoxindex).show();
                $('.innerTableBox').eq(innerTableBoxindex).css({
                    opacity: '1',
                    'z-index': '999',
                });
                $('.govDocumentSettingListVal span').removeClass('active');
                $(this).addClass('active');
                $('.innerTableBox').css('opacity', '0');
                $('.innerTableBox').hide();
                $('.innerTableBox').eq($(this).index()).css({
                    opacity: '1',
                    'z-index': '999',
                });
                $('.innerTableBox').eq($(this).index()).show();
                $('.govDocumentSettingListBoxForm').css('opacity', '0');
                $('.govDocumentSettingListBoxForm').hide();
                $('.govDocumentSettingListBoxForm')
                    .eq($(this).index())
                    .css({
                        opacity: '1',
                    })
                    .show();
                govDocunitTable.refresh();
                govReceivefileseqTable.refresh();
            });

            var receiptSettingsInnerTableBoxIndex = 0;
            //收文点击事件
            $('.govDocumentReceiptSettingsListVal span').click(function () {
                receiptSettingsInnerTableBoxIndex = $(this).index();
                $('.innerTableBox').hide();
                $('.receiptSettingsInnerTableBox').eq(receiptSettingsInnerTableBoxIndex).show();
                $('.receiptSettingsInnerTableBox').eq(receiptSettingsInnerTableBoxIndex).css({
                    opacity: '1',
                    'z-index': '999',
                });
                $('.govDocumentReceiptSettingsListVal span').removeClass('active');
                $(this).addClass('active');
                $('.receiptSettingsInnerTableBox').css('opacity', '0');
                $('.receiptSettingsInnerTableBox').hide();
                $('.receiptSettingsInnerTableBox').eq($(this).index()).css({
                    opacity: '1',
                    'z-index': '999',
                });
                $('.receiptSettingsInnerTableBox').eq($(this).index()).show();
                $('.govDocumentSettingListBoxFormT').css('opacity', '0');
                $('.govDocumentSettingListBoxFormT').hide();
                $('.govDocumentSettingListBoxFormT')
                    .eq($(this).index())
                    .css({
                        opacity: '1',
                    })
                    .show();
            });

            //文号
            var GovSenddocnumTable = new $.trasenTable('indexGovSenddocnumTable', {
                url: common.url + '/ts-document/govSendDocnum/list',
                pager: '#indexGovSenddocnumPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '文号类别',
                        sortable: false,
                        name: 'numType',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '文号名称',
                        sortable: false,
                        name: 'numName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '文号预览',
                        sortable: false,
                        name: 'numMode',
                        width: 200,
                        align: 'center',
                    },
                    /* {label: '序号初始值', sortable: false, name: 'initValue', width: 120, align: 'center'},*/
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editEvaGovSenddocnum" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delEvaGovSenddocnum" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovSenddocnumForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
                gridComplete: function () {
                    $('.innerTableBox').hide();
                    $('.receiptSettingsInnerTableBox').hide();
                    $('.innerTableBox').eq(0).show();
                },
            });

            //发文 - 模板
            var templateFileTable = new $.trasenTable('indexTemplateFileTable', {
                url: common.url + '/ts-document/govTemplateFile/list',
                pager: '#indexTemplateFilePage',
                mtype: 'get',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '创建人',
                        sortable: false,
                        name: 'createUserName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '模板名称',
                        sortable: false,
                        name: 'fileName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '模板类型',
                        sortable: false,
                        name: 'fileType',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editTemplateFile" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="addPageOfficeTemplate" row-id="' + opt.rowId + '"> <i class="layui-icon layui-icon-template-1 deal_icon"  aria-hidden="true"></i>模板 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delTemplateFile" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaTemplateFileForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //发文 - 流程设置
            var workFlowFormTable = new $.trasenTable('index_WorkFlowTable', {
                url: common.url + '/ts-document/govTemplateFile/list',
                pager: '#index_WorkFlowPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '所属分类',
                        sortable: false,
                        name: 'createUserName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '流程名称',
                        sortable: false,
                        name: 'fileName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '试用范围',
                        sortable: false,
                        name: 'fileType',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '创建人',
                        sortable: false,
                        name: 'fileType',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '排序',
                        sortable: false,
                        name: 'fileType',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editWorkFlowForm" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delWorkFlowForm" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaWorkFlowForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //机关代字
            var govSenddocWordTable = new $.trasenTable('index_GovSenddocWordTable', {
                url: common.url + '/ts-document/govSendDocword/list',
                pager: '#index_GovSenddocWordPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '机关代字',
                        sortable: false,
                        name: 'wordName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '文号',
                        sortable: false,
                        name: 'docnumName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '对应流程',
                        sortable: false,
                        name: 'processName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '对应模板',
                        sortable: false,
                        name: 'templateName',
                        width: 200,
                        align: 'center',
                    },
                    /*{label: '使用范围', sortable: false, name: 'useRangeName', width: 200, align: 'center'},*/
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="govSenddocNo" row-id="' + opt.rowId + '"> <i class="layui-icon layui-icon-template-1 deal_icon" aria-hidden="true"></i>文号情况 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="editGovSenddocWord" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delGovSenddocWord" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovSenddocWordForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //主题词
            var govSendDoctopicTable = new $.trasenTable('index_GovSendDoctopicTable', {
                url: common.url + '/ts-document/govSendDoctopic/list',
                pager: '#index_GovSendDoctopicPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '类别词',
                        sortable: false,
                        name: 'sortTopicalName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '类属词',
                        sortable: false,
                        name: 'attributeTopical',
                        width: 500,
                        align: 'center',
                        formatter: function (cellvalue, options, rowObject) {
                            return '<div style="white-space: nowrap;text-overflow: ellipsis; width: 100%;overflow: hidden;">' + cellvalue + '</div>';
                        },
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editGovSendDoctopic" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delGovSendDoctopic" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovSendDoctopicForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //标签设置
            var govSendDoclaBelTable = new $.trasenTable('index_GovSendDoclaBbelTable', {
                url: common.url + '/ts-document/govSendDoclabel/list',
                pager: '#index_GovSendDoclaBbelPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '标签名称',
                        sortable: false,
                        name: 'labelName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '标签说明',
                        sortable: false,
                        name: 'labelExplain',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '标签备注',
                        sortable: false,
                        name: 'labelRemarks',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editDispatchSettings" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delDispatchSettings" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovSendDoclaBelForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            ///////////////////////////////////////收文///////////////////////////////////////////////////////

            //收文 - 流程设置
            var receiptProcessWorkFlowTable = new $.trasenTable('index_ReceiptProcessWorkFlowTable', {
                url: common.url + '/ts-document/govTemplateFile/list',
                pager: '#index_ReceiptProcessWorkFlowPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '所属分类',
                        sortable: false,
                        name: 'createUserName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '流程名称',
                        sortable: false,
                        name: 'fileName',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '试用范围',
                        sortable: false,
                        name: 'fileType',
                        width: 200,
                        align: 'center',
                    },
                    {
                        label: '创建人',
                        sortable: false,
                        name: 'fileType',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '排序',
                        sortable: false,
                        name: 'fileType',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editWorkFlowForm" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delWorkFlowForm" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaReceiptProcessWorkFlowForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //收文 - 流水号
            var govReceivefileseqTable = new $.trasenTable('index_GovReceivefileseqTable', {
                url: common.url + '/ts-document/govReceivefileseq/list',
                pager: '#index_GovReceivefileseqPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '流水号名称',
                        sortable: false,
                        name: 'seqName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '对应流程',
                        sortable: false,
                        name: 'seqProceName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '顺序号的位数',
                        sortable: false,
                        name: 'seqBitNum',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: '序号初始值',
                        sortable: false,
                        name: 'seqInitValue',
                        width: 100,
                        align: 'center',
                    },
                    {
                        label: '流水号预览',
                        sortable: false,
                        name: 'seqModer',
                        width: 250,
                        align: 'center',
                    },
                    {
                        label: '流水号格式',
                        sortable: false,
                        name: 'seqFormatr',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editGovReceivefileseq" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delGovReceivefileseq" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: '对应流程ID',
                        sortable: false,
                        name: 'seqProceId',
                        width: 130,
                        hidden: true,
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovReceivefileseqForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            //收文 - 来文单位
            var govDocunitTable = new $.trasenTable('index_GovDocunitTable', {
                url: common.url + '/ts-document/govDocunit/list',
                pager: '#index_GovDocunitPage',
                mtype: 'get',
                sortname: 'CREATE_DATE',
                sortorder: 'DESC',
                rowNum:15,
                shrinkToFit: true,
                rownumbers: false,
                rowList: [15, 30, 50, 100],
                multiselect: false,
                colModel: [
                    {
                        label: '单位分类',
                        sortable: false,
                        name: 'unitTypeName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '单位全称',
                        sortable: false,
                        name: 'unitWholeName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '单位简称',
                        sortable: false,
                        name: 'unitShortName',
                        width: 150,
                        align: 'center',
                    },
                    {
                        label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                        name: '',
                        sortable: false,
                        width: 40,
                        editable: false,
                        align: 'center',
                        title: false,
                        classes: 'visible jqgrid-rownum ui-state-default',
                        formatter: function (cell, opt, row) {
                            var html = '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                            var btns = '';
                            btns += '<button type="button" class="layui-btn  " id="editGovDocunit" row-id="' + opt.rowId + '"> <i class="fa fa-pencil-square-o deal_icon"  aria-hidden="true"></i>编辑 </button> ';
                            btns += '<button type="button" class="layui-btn  " id="delGovDocunit" row-id="' + opt.rowId + '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button> ';
                            html += btns + '</div></div>';
                            return html;
                        },
                    },
                    {
                        label: 'id',
                        sortable: false,
                        name: 'id',
                        width: 130,
                        hidden: true,
                    },
                ],
                buidQueryParams: function () {
                    var search = $('#evaGovDocunitForm').serializeArray();
                    var opt = [];
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                },
            });

            form.render();

            // 收文-刷新流程设置
            function receiptProcessWorkFlowRefresh() {
                receiptProcessWorkFlowTable.refresh();
            }

            // 收文-流程设置搜索
            $('#EvaReceiptProcessWorkFlowSearch').funs('click', function () {
                receiptProcessWorkFlowRefresh();
            });

            //收文-流程设置重置
            $('#EvaReceiptProcessWorkFlowReset').funs('click', function () {
                $('#flowName').val('');
                receiptProcessWorkFlowRefresh();
            });

            // 收文-刷新流水号
            function govReceivefileseqRefresh() {
                govReceivefileseqTable.refresh();
            }

            // 收文-流水号搜索
            $('#EvaGovReceivefileseqSearch').funs('click', function () {
                govReceivefileseqRefresh();
            });

            //收文-流水号重置
            $('#EvaGovReceivefileseqReset').funs('click', function () {
                $('#seqName').val('');
                govReceivefileseqRefresh();
            });

            // 刷新收文-来文单位
            function govDocunitRefresh() {
                govDocunitTable.refresh();
            }

            // 刷新发文-文号设置
            function govSenddocnumRefresh() {
                GovSenddocnumTable.refresh();
            }

            // 刷新发文-模板
            function templateFileRefresh() {
                templateFileTable.refresh();
            }

            //发文-模板搜索
            $('#EvaTemplateFileSearch').funs('click', function () {
                templateFileRefresh();
            });

            // 刷新发文-机关代字
            function govSenddocWordRefresh() {
                govSenddocWordTable.refresh();
            }

            // 刷新发文-标签
            function govSendDoclaBelRefresh() {
                govSendDoclaBelTable.refresh();
            }

            // 刷新发文-主题词
            function govSendDoctopicRefresh() {
                govSendDoctopicTable.refresh();
            }

            // 收文-来文单位 搜索
            $('#EvaGovDocunitSearch').funs('click', function () {
                govDocunitRefresh();
            });

            // 主题词搜索
            $('#EvaGovSendDoctopicSearch').funs('click', function () {
                govSendDoctopicRefresh();
            });

            //主题词重置
            $('#EvaGovSendDoctopicReset').funs('click', function () {
                $('#sortTopicalNameSeach').val('');
                govSendDoctopicRefresh();
            });

            //发文-模板重置
            $('#EvaTemplateFileReset').funs('click', function () {
                $('#fileName').val('');
                templateFileRefresh();
            });

            // 标签设置搜索
            $('#EvaGovSendDoclaBbelSearch').funs('click', function () {
                govSendDoclaBelRefresh();
            });

            //收文 来文单位 重置
            $('#EvaGovDocunitReset').funs('click', function () {
                $('#unitWholeName').val('');
                govDocunitRefresh();
            });

            //标签设置重置
            $('#EvaGovSendDoclaBbelReset').funs('click', function () {
                $('#labelName').val('');
                govSendDoclaBelRefresh();
            });

            // 文号设置搜索
            $('#EvaGovSenddocnumSearch').funs('click', function () {
                govSenddocnumRefresh();
            });

            //文号设置重置
            $('#EvaGovSenddocnumReset').funs('click', function () {
                $('#numName').val('');
                govSenddocnumRefresh();
            });

            // 机关代字搜索
            $('#EvaGovSenddocwordSearch').funs('click', function () {
                govSenddocWordRefresh();
            });

            //机关代字 重置
            $('#EvaGovSenddocwordReset').funs('click', function () {
                $('#wordName').val('');
                govSenddocWordRefresh();
            });

            // 发文- 新增模板设置
            $('#addTemplateFile').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/templateFile/addTemplateFile', {
                    trasen: templateFileTable,
                    title: '新增模板',
                    ref: templateFileRefresh,
                });
            });

            // 发文- 编辑模板设置
            $('#editTemplateFile').funs('click', function () {
                var data = templateFileTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }

                $.quoteFun('/govDocument/govDocumentSetting/templateFile/addTemplateFile', {
                    trasen: templateFileTable,
                    title: '编辑模板',
                    data: data,
                    ref: templateFileRefresh,
                });
            });

            //发文 - 删除模板设置
            $('#delTemplateFile').funs('click', function () {
                var data = templateFileTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.fileName + ' ] ？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govTemplateFile/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info(res.object);
                                    templateFileRefresh();
                                } else {
                                    trasen.info(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 新增文号设置
            $('#addGovSendDocnum').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/addDocnum', {
                    trasen: GovSenddocnumTable,
                    title: '新增文号设置',
                    ref: govSenddocnumRefresh,
                });
            });

            // 修改文号设置
            $('#editEvaGovSenddocnum').funs('click', function () {
                var data = GovSenddocnumTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/addDocnum', {
                    trasen: GovSenddocnumTable,
                    title: '编辑文号设置',
                    data: data,
                    ref: govSenddocnumRefresh,
                });
            });

            //删除文号设置
            $('#delEvaGovSenddocnum').funs('click', function () {
                var data = GovSenddocnumTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                    method: 'deleted',
                };
                layer.confirm(
                    '确定要删除[ ' + data.numName + ' ]？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govSendDocnum/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govSenddocnumRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 新增发文标签
            $('#addGovSendDoclaBbel').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/doclabel/addDoclabel', {
                    trasen: govSendDoclaBelTable,
                    title: '新增发文标签',
                    ref: govSendDoclaBelRefresh,
                });
            });

            // 修改发文标签
            $('#editDispatchSettings').funs('click', function () {
                var data = govSendDoclaBelTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/doclabel/addDoclabel', {
                    trasen: govSendDoclaBelTable,
                    title: '编辑发文标签',
                    data: data,
                    ref: govSendDoclaBelRefresh,
                });
            });

            //删除发文标签
            $('#delDispatchSettings').funs('click', function () {
                var data = govSendDoclaBelTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.labelExplain + ' ]？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govSendDoclabel/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govSendDoclaBelRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 新增机关代字
            $('#addGovSenddocword').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/docword/addDocword', {
                    trasen: govSenddocWordTable,
                    title: '新增机关代字',
                    ref: govSenddocWordRefresh,
                });
            });
            // 机关代字文号情况
            $('#govSenddocNo').funs('click', function () {
                var data = govSenddocWordTable.getSourceRowData(); // 选中行数据
                $.quoteFun('/govDocument/govDocumentSetting/govsenddocNo/index', {
                    trasen: govSenddocWordTable,
                    title: '文号情况列表',
                    data: data,
                    ref: govSenddocWordRefresh,
                });
            })
            // 修改机关代字
            $('#editGovSenddocWord').funs('click', function () {
                var data = govSenddocWordTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/docword/addDocword', {
                    trasen: govSenddocWordTable,
                    title: '编辑机关代字',
                    data: data,
                    ref: govSenddocWordRefresh,
                });
            });

            //删除机关代字
            $('#delGovSenddocWord').funs('click', function () {
                var data = govSenddocWordTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.wordName + ' ]？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govSendDocword/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govSenddocWordRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            // 新增主题词
            $('#addGovSendDoctopic').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/doctopic/addDoctopic', {
                    trasen: govSendDoctopicTable,
                    title: '新增发文主题词',
                    ref: govSendDoctopicRefresh,
                });
            });

            //修改 主题词
            $('#editGovSendDoctopic').funs('click', function () {
                var data = govSendDoctopicTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/doctopic/addDoctopic', {
                    trasen: govSendDoctopicTable,
                    title: '编辑主题词',
                    data: data,
                    ref: govSendDoctopicRefresh,
                });
            });

            //删除 主题词
            $('#delGovSendDoctopic').funs('click', function () {
                var data = govSendDoctopicTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.sortTopical + ' ]？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govSendDoctopic/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govSendDoclaBelRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            //新增收文-来文单位
            $('#addGovDocunit').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/docunit/addDocunit', {
                    trasen: govDocunitTable,
                    title: '新增收文来文单位',
                    ref: govDocunitRefresh,
                });
            });

            //修改收文-来文单位
            $('#editGovDocunit').funs('click', function () {
                var data = govDocunitTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/docunit/addDocunit', {
                    trasen: govDocunitTable,
                    title: '编辑收文来文单位',
                    data: data,
                    ref: govDocunitRefresh,
                });
            });

            //删除收文-来文单位
            $('#delGovDocunit').funs('click', function () {
                var data = govDocunitTable.getSelectRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.unitWholeName + ' ] ？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govDocunit/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govDocunitRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            //新增收文-流水号
            $('#addGovReceivefileseq').funs('click', function () {
                $.quoteFun('/govDocument/govDocumentSetting/receiveFileSeq/addReceiveFileSeq', {
                    trasen: govReceivefileseqTable,
                    title: '新增收文流水号',
                    ref: govReceivefileseqRefresh,
                });
            });

            //编辑收文-流水号
            $('#editGovReceivefileseq').funs('click', function () {
                var data = govReceivefileseqTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                $.quoteFun('/govDocument/govDocumentSetting/receiveFileSeq/addReceiveFileSeq', {
                    trasen: govReceivefileseqTable,
                    title: '编辑收文流水号',
                    data: data,
                    ref: govReceivefileseqRefresh,
                });
            });

            //删除收文-流水号
            $('#delGovReceivefileseq').funs('click', function () {
                var data = govReceivefileseqTable.getSelectRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                var d = {
                    id: data.id,
                };
                layer.confirm(
                    '确定要删除[ ' + data.seqName + ' ] ？',
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    },
                    function (index) {
                        $.ajax({
                            type: 'post',
                            url: common.url + '/ts-document/govReceivefileseq/deletedById',
                            dateType: 'json',
                            contentType: 'application/json',
                            data: JSON.stringify(d),
                            success: function (res) {
                                $.closeloadings();
                                if (res.success) {
                                    layer.closeAll();
                                    trasen.info('操作成功');
                                    govReceivefileseqRefresh();
                                } else {
                                    layer.closeAll();
                                    layer.msg(res.message);
                                }
                            },
                            error: function (res) {
                                res = JSON.parse(res.responseText);
                                layer.msg(res.message);
                            },
                        });
                    }
                );
            });

            //新增PageOffic模板
            $('#addPageOfficeTemplate').funs('click', function () {
                var data = templateFileTable.getSourceRowData(); // 选中行数据
                if (!data) {
                    layer.msg('请先选择一行数据进行操作！');
                    return false;
                }
                addPageOfficeTemplate(data.id, data.fileType);
            });

            //打开PageOffice控件
            function addPageOfficeTemplate(id, fileType) {
                var url = common.url + '/ts-document/govTemplateFile/openTemplateFileForm?templateId=' + id + '&fileType=' + fileType + '&method=edit';
                javascript: POBrowser.openWindowModeless(url, 'width=1370px;height=720px;');
            }
        });
    };
});
