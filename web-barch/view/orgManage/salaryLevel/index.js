"use strict";
define(function (require, exports, module) {
    var init = function () {
      return perform();
    }
    module.exports = {
      init: init
    }
    var perform = function () {
      layui.use(['form', 'trasen'], function () {
          var form = layui.form,
            layer = layui.layer,
            trasen = layui.trasen;
          var firstVal = '';

          //薪级类别下拉框
          getPostCategory()
          // 表格渲染
          var trasenTable = new $.trasenTable("grid-table-salaryLevelTable", {
            url: common.url + '/ts-hrms/salaryLevel/list',
            pager: 'grid-pager-salaryLevelPager',
            shrinkToFit: true,
            postData: {
              salaryLevelCategory: $('#salaryLevelQueryForm input[name="salaryLevelCategory"]').val()
            },
            // 表格头
            colNames: ['ID', '薪级名称', '薪级类别', '薪级类别id', '薪级等级', '状态', '状态id'],
            // 表格字段
            colModel: [
              {name: 'salaryLevelId', index: 'salary_level_id', width: "auto", editable: false, hidden: true},
              {name: 'salaryLevelName', index: 'salary_level_name', width: 150, editable: false, align: 'left'},
              {
                name: 'salaryLevelCategoryName',
                index: 'salary_level_category',
                width: 150,
                editable: false,
                align: 'left'
              },
              {
                name: 'salaryLevelCategory',
                index: 'salary_level_category',
                width: 50,
                editable: false,
                hidden: true
              },
              {name: 'grade', index: 'grade', width: 150, editable: false, align: 'left'},
              {
                name: 'isEnable',
                index: 'is_enable',
                width: 60,
                align: "center",
                editable: false,
                formatter: function (cellvalue, options, rowObject) {
                  var text;
                  if (cellvalue == 1) {
                    text = "启用";
                  } else {
                    text = "<font color='red'>禁用</font>";
                  }
                  return text;
                }
              },
              {
                name: 'isEnable',
                index: 'is_enable1',
                align: "center",
                width: 130,
                sortable: true,
                editable: false,
                hidden: true
              },
            ],
            queryFormId: 'salaryLevelQueryForm'
          });

          function getPostCategory () {
            var html = '';
            $.ajax({
              type: 'post',
              contentType: 'application/json; charset=utf-8',
              url: common.url + '/ts-hrms/dict/combobox' + dict_salary_level_category,
              async: false,
              data: {},
              success: function (res) {
                if (res.object != null) {
                  firstVal = res.object[0].dictValue;
                  $.each(res.object, function (i, v) {
                    html += `<li class="left-item" data-index='${i}'>
                               <div class="item-text" value="${v.dictValue}">
                                 ${v.dictName}
                               </div>
                             </li>`;
                  });
                  $('#salaryGradeTreeSelectUi').html(html);
                  if (!$('#salaryLevelQueryForm input[name="salaryLevelCategory"]').val()) {
                    $('#salaryLevelQueryForm input[name="salaryLevelCategory"]').val(firstVal);
                    $('#salaryGradeTreeSelectUi .item-text').first().addClass('btnactive');
                  }
                }
              }
            });
          }

          //点击事件
          $('#salaryGradeTreeSelectUi')
            .off('click', '.item-text')
            .on('click', '.item-text', function () {
              $('#salaryGradeTreeSelectUi .item-text').removeClass('btnactive');
              $(this).addClass('btnactive');
              var val = $(this).attr('value');
              $('#salaryLevelQueryForm input[name="salaryLevelCategory"]').val(val);
              refreshTable();
            });

          // 表格刷新
          function refreshTable () {
            trasenTable.refresh();
          }

          // 查询
          form.on('submit(salaryLevelQuerySearch)', function (data) {
            refreshTable();
          });

          //重置
          $('#salaryLevelQueryResetBtn').funs('click', function () {
            $('#salaryLevelQueryForm input[name="salaryLevelCategory"]').val(firstVal);
            $('#salaryGradeTreeSelectUi .item-text').removeClass('btnactive');
            $('#salaryGradeTreeSelectUi .item-text').first().addClass('btnactive');
            $('#salaryLevelQueryForm')[0].reset();
            refreshTable();
          });

          // 编辑薪级
          $("#salaryLevelListDiv").off('click', '#salaryLevelTableEditor').on("click", "#salaryLevelTableEditor", function () {
            var rowData = trasenTable.getSelectRowData();
            if (rowData.length || rowData.length == 0) {
              layer.msg('请选择一条记录进行操作！')
              return false;
            }
            $.quoteFun('orgManage/salaryLevel/modules/add', {
              title: '编辑薪级',
              data: rowData,
              ref: refreshTable
            })

          });

          // 新增薪级
          $("#salaryLevelListDiv").off("click", "#salaryLevelAdd").on("click", "#salaryLevelAdd", function () {
            $.quoteFun('orgManage/salaryLevel/modules/add', {
              title: '新增薪级',
              ref: refreshTable
            })
          });

          // 启用
          $("#salaryLevelListDiv").off("click", "#salaryLevelTableEnable").on("click", "#salaryLevelTableEnable", function () {
            var rowData = trasenTable.getSelectRowData();
            if (rowData.length || rowData.length == 0) {
              layer.msg('请选择一条记录进行操作！')
              return false;
            }
            if (rowData.isEnable == 1) {
              layer.msg('当前记录不能启用操作！')
              return false;
            }
            var _url = common.url + "/ts-hrms/salaryLevel/update";
            var _data = {"salaryLevelId": rowData.salaryLevelId, "isEnable": 1};
            _data = JSON.stringify(_data);
            $.ajax({
              type: "post",
              contentType: "application/json; charset=utf-8",
              url: _url,
              data: _data,
              success: function (res) {
                if (res.success) {
                  refreshTable();
                } else {
                  layer.closeAll();
                  layer.msg('操作失败！');
                }
              }
            });
            return false;
          });

          // 禁用
          $("#salaryLevelListDiv").off("click", "#salaryLevelTableBan").on("click", "#salaryLevelTableBan", function () {
            var rowData = trasenTable.getSelectRowData();
            if (rowData.length || rowData.length == 0) {
              layer.msg('请选择一条记录进行操作！')
              return false;
            }
            if (rowData.isEnable == 2) {
              layer.msg('当前记录不能禁用操作！')
              return false;
            }
            var _url = common.url + "/ts-hrms/salaryLevel/update";
            var _data = {"salaryLevelId": rowData.salaryLevelId, "isEnable": 2};
            _data = JSON.stringify(_data);
            $.ajax({
              type: "post",
              contentType: "application/json; charset=utf-8",
              url: _url,
              data: _data,
              success: function (res) {
                if (res.success) {
                  refreshTable();
                } else {
                  layer.closeAll();
                  layer.msg('操作失败！');
                }
              }
            });
            return false;
          });

          // 导入薪级信息
          $("#salaryLevelListDiv").off("click", "#salaryLevelTableImport").on("click", "#salaryLevelTableImport", function () {
            var html = $("#salaryLevelListImportForm").html();
            layui.use(['form', 'upload'], function () {
              var form = layui.form,
                upload = layui.upload;
              var openIndex = layer.open({
                type: 1,
                title: false,
                closeBtn: 1,
                shadeClose: false,
                area: ['200px', '60px'],
                skin: 'yurclass',
                content: html,
                success: function () {
                  upload.render({
                    elem: '#salaryLevelImportBtn',
                    url: common.url + '/ts-hrms/salaryLevel/excelImportSalaryLevelAndWage',
                    accept: 'file',
                    done: function (res) {
                      if (res.success) {
                        refreshTable();
                        layer.closeAll();
                        layer.msg("导入成功：" + res.object + "条");
                      } else {
                        layer.msg(res.message || "导入失败");
                        layer.close(openIndex);
                      }
                    }
                  });
                }
              });
              form.render();
            });
          });

          // 下载导入模板
          $("body").off("click", "#salaryLevelDownTemplateBtn").on("click", "#salaryLevelDownTemplateBtn", function () {
            var _url = common.url + "/ts-hrms/download/template/salarylevelinfo";
            location.href = _url;
          });

        }
      )
      ;
    }
  }
)
;
