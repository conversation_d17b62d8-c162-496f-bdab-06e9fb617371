<!-- 主表格内容 -->
<div class="content-box" id="fileBorrowingListBox">
    <input type="hidden" id="optChoseStatus" />
    <div class="oa-nav dossierReleaseTop">
        <a href="javascript:;" class="oa-nav_item active" id="fileQuery">档案查询</a>
        <a href="javascript:;" class="oa-nav_item" id="borrowingInquiry">借阅查询</a>
    </div>
    <div class="trasen-con-box fileBorrowingListBox" style="display: block">
        <div class="oa-nav-search">
            <form id="fm-fileQueryForm" class="layui-form">
                <input type="hidden" id="fileQuery_dossierFileNum" name="dossierFileNum" />
                <input type="hidden" id="fileQuery_dossierYear" name="dossierYear" />
                <input type="hidden" id="fileQuery_screenCreateDater" name="screenCreateDate" />
                <input type="hidden" id="fileQuery_filingAgency" name="filingAgency" />
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">标题</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="dossierTitle" class="layui-input" id="dossier_Title_Box" search-input="fileManage-borrow" placeholder="请输入标题" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">档案类型</span>
                    <div class="shell-layer-input-box">
                        <select name="dossierFromFileType" id="dossierFromFileType" lay-search>
                            <option value="" selected="true">全部</option>
                            <option value="GZLC">工作流程</option>
                            <option value="FWLC">发文流程</option>
                            <option value="SWLC">收文流程</option>
                            <option value="XXGL">信息管理</option>
                        </select>
                    </div>
                </div>
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">借阅情况</span>
                    <div class="shell-layer-input-box">
                        <select name="statusFlag" id="statusFlag" lay-search>
                            <option value="" selected="true">全部</option>
                            <option value="1">正常</option>
                            <option value="3">借阅中</option>
                        </select>
                    </div>
                </div>
                <!-- <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">保管期限</span>
                    <div class="shell-layer-input-box">
                        <select name="dossierKeepLimit" id="dossierKeepLimit">
                            <option value="" selected="true">全部</option>
                            <option value="1">永久</option>
                            <option value="2">长期</option>
                            <option value="3">短期</option>
                        </select>
                    </div>
                </div> -->
                <div class="shell-search-box">
                    <button type="button" class="layui-btn" id="fm-fileQuerySearch" search-btn="fileManage-borrow">搜索</button>
                    <!-- <button type="button" class="layui-btn" id="fm-fileQueryScreen">筛选</button>
                    <button type="button" class="layui-btn oa-btn-reset" id="fm-fileQueryReset"><i class="layui-icon layui-icon-refresh"></i></button> -->
                </div>
                <div class="shell-search-box">
                    <button type="button" class="search_list" screen-box-tar="fm-fileQueryScreen">
                        <i class="oaicon oa-icon-search_list"></i>
                    </button>
                    <button type="button" class="layui-btn oa-btn-reset" id="fm-fileQueryReset"><i class="layui-icon layui-icon-refresh"></i></button>
                </div>
            </form>
            <div class="screen-box" screen-box="fm-fileQueryScreen">
                <div class="screen-box_tit">更多查询条件</div>
                <div class="screen-box_con">
                    <form class="layui-form row" id="fm-fileQueryScreenForm">
                        <div class="layui-col-md6">
                            <div class="layui-col-md3">
                                <div class="title">保管期限</div>
                            </div>
                            <div class="layui-col-md7">
                                <select name="dossierKeepLimit" id="dossierKeepLimit" lay-search>
                                    <option value="" selected="true">全部</option>
                                    <option value="1">永久</option>
                                    <option value="2">长期</option>
                                    <option value="3">短期</option>
                                </select>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="screen-box_btn">
                    <span class="layui-btn" id="fm-fileQueryScreenSearch" screen-box-tar="fm-fileQueryScreen">搜索</span>
                    <span class="layui-btn layui-btn-primary" id="fm-fileQueryScreenReset">重置</span>
                    <span class="layui-btn layui-btn-primary" screen-box-tar="fm-fileQueryScreen">关闭</span>
                </div>
            </div>
        </div>
        <div class="trasen-con-box">
            <div class="table-box">
                <table id="fm-fileQueryTable"></table>
                <!-- 分页 -->
                <div id="fm-fileQueryPage"></div>
            </div>
        </div>
    </div>

    <!--借阅查询-->
    <div class="trasen-con-box fileBorrowingListBox" style="display: none">
        <div class="oa-nav-search">
            <form id="fm-borrowingInquiryForm" class="layui-form">
                <input type="hidden" id="borrowingInquiry_screenCreateDater" name="screenCreateDate" />
                <input type="hidden" id="borrowingInquiry_screenReturnDate" name="screenReturnDate" />
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">标题</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="borrowFileName" class="layui-input" id="borrowFileName" search-input="fileManage-borrow2" placeholder="请输入标题" />
                    </div>
                </div>
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">借阅人</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="borrowUserName" class="layui-input" id="borrowUserName" search-input="fileManage-borrow2" placeholder="请输入借阅人" />
                    </div>
                </div>
                <!-- <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">借阅部门</span>
                    <div class="shell-layer-input-box">
                        <input type="text" name="borrowDeptName" class="layui-input" id="borrowDeptName" search-input="fileManage-borrow2" placeholder="请输入借阅部门" />
                    </div>
                </div> -->
                <div class="shell-search-box">
                    <span class="shell-layer-input-boxTit">借阅状态</span>
                    <div class="shell-layer-input-box">
                        <select name="borrowStatus" id="borrowStatus" lay-search>
                            <option value="" selected="true">全部</option>
                            <option value="1">正常</option>
                            <option value="3">借阅中</option>
                        </select>
                    </div>
                </div>
                <div class="shell-search-box">
                    <button type="button" class="layui-btn" id="fm-borrowingInquirySearch" search-btn="fileManage-borrow2">搜索</button>
                    <!-- <button type="button" class="layui-btn" id="fm-borrowingInquiryScreen">筛选</button>
                    <button type="button" class="layui-btn oa-btn-reset" id="fm-borrowingInquiryReset"><i class="layui-icon layui-icon-refresh"></i></button> -->
                </div>
                <div class="shell-search-box">
                    <button type="button" class="search_list" screen-box-tar="fm-borrowingInquiryScreen">
                        <i class="oaicon oa-icon-search_list"></i>
                    </button>
                    <button type="button" class="layui-btn oa-btn-reset" id="fm-borrowingInquiryReset"><i class="layui-icon layui-icon-refresh"></i></button>
                </div>
            </form>
            <div class="screen-box" screen-box="fm-borrowingInquiryScreen">
                <div class="screen-box_tit">更多查询条件</div>
                <div class="screen-box_con">
                    <form class="layui-form row" id="fm-borrowingInquiryScreenForm">
                        <div class="layui-col-md6">
                            <div class="layui-col-md3">
                                <div class="title">借阅部门</div>
                            </div>
                            <div class="layui-col-md7">
                                <input type="text" name="borrowDeptName" class="layui-input" id="borrowDeptName" search-input="fileManage-borrow2" placeholder="请输入借阅部门" />
                            </div>
                        </div>
                    </form>
                </div>
                <div class="screen-box_btn">
                    <span class="layui-btn" id="fm-borrowingInquiryScreenSearch" screen-box-tar="fm-borrowingInquiryScreen">搜索</span>
                    <span class="layui-btn layui-btn-primary" id="fm-borrowingInquiryScreenReset">重置</span>
                    <span class="layui-btn layui-btn-primary" screen-box-tar="fm-borrowingInquiryScreen">关闭</span>
                </div>
            </div>
        </div>
        <div class="trasen-con-box">
            <div class="table-box">
                <table id="fm-borrowingInquiryTable"></table>
                <!-- 分页 -->
                <div id="fm-borrowingInquiryPage"></div>
            </div>
        </div>
    </div>
</div>
<script>
    //tab切换
    $('#fileBorrowingListBox .oa-nav .oa-nav_item').click(function () {
        $(this).addClass('active').siblings().removeClass('active');
        $('#fileBorrowingListBox .fileBorrowingListBox').hide();
        $('#fileBorrowingListBox .fileBorrowingListBox').eq($(this).index()).show();
    });
</script>
