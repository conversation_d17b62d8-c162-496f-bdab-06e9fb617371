<style>
    #userDeptGroup .select-box {
        padding: 0 8px;
        margin: 8px 0;
    }
    #userDeptGroup .select-box .select-left-box,
    #userDeptGroup .select-box .select-right-box {
        height: 420px;
        position: relative;
        padding: 8px;
        border: 1px solid #eee;
        border-radius: 8px;
        box-sizing: border-box;
    }
    #userDeptGroup .select-box .select-left-box {
        width: 220px;
        display: flex;
        flex-direction: column;
    }
    #userDeptGroup .select-box .select-right-box {
        flex: 1;
        margin-left: 8px;
    }
    #userDeptGroup .deptTree {
        overflow: auto;
        width: 100%;
        flex-grow: 1;
    }
    #userDeptGroup .oa-nav-search {
        border: 0;
    }
    #userDeptGroup .deptTree .ztree {
        height: 95%;
        top: 0;
    }
    #userDeptGroup .layui-tree-branch {
        display: none;
    }
    #userDeptGroup .layui-form-checkbox {
        display: none;
    }
    #userDeptGroup .value-box {
        padding: 0 8px;
        margin: 8px 0;
    }
    #userDeptGroup .selectUserNameBox {
        width: 100%;
        box-sizing: border-box;
        height: 100px;
        border: 1px solid #eee;
        padding: 8px;
        box-sizing: border-box;
        overflow: auto;
    }
    #userDeptGroup .selectUserNameBox .user-item {
        display: inline-block;
        position: relative;
        padding: 4px 10px;
        margin-right: 8px;
        margin-bottom: 8px;
        font-size: 14px;
        color: #333;
        border-radius: 14px;
        cursor: pointer;
        background: #edefff;
    }
    #userDeptGroup .selectUserNameBox .user-item:hover {
        background-color: #5260FF;
        color: #fff;
    }
    #userDeptGroup .selectUserNameBox .user-item .delete-btn{
        position: absolute;
        right: -6px;
        color: red;
        background-color: #fff;
        border-radius: 100%;
        display: none;
        top: -2px;
    }
    #userDeptGroup .selectUserNameBox .user-item:hover .delete-btn {
        display: inline-block;
    }
    #userDeptGroup .bottom-box{
        width: 100%;
        box-sizing: border-box;
        padding: 8px 15px;
        text-align: right;
        border-top: 1px solid #eee;
        background-color: #fff;
    }
    #userDeptGroup .layui-input {
        width: 140px;
    }
</style>
<div id="userDeptGroup">
    <div class="flex select-box">
        <div class="select-left-box">
            <div class="select-left-search-box">
                <form class="layui-form areaButtonBoxL">
                    <div class="layui-inline fl" style="width: 100%;">
                        <input type="text" placeholder="请输入组织架构、群组名称" id="groupName" autocomplete="off" class="layui-input edi-layui-input" />
                    </div>
                </form>
            </div>
            <div class="deptTree scrollbar-box none">
                <ul id="deptTree" class="ztree"></ul>
            </div>
        </div>
        <div class="select-right-box">
            <div class="oa-nav-search">
                <form method="" lay-filter="component-form-element" id="resourcesUserMainForm" class="layui-form areaButtonBoxL">
                    <div class="layui-inline fl">
                        <input type="text" placeholder="请输入人员账号" name="userAccounts" search-input="common-user" autocomplete="off" class="layui-input edi-layui-input" />
                    </div>
                    <div class="layui-inline fl mgl10">
                        <input type="text" placeholder="请输入人员姓名" name="empName" search-input="common-user" autocomplete="off" class="layui-input edi-layui-input" />
                    </div>
					<div class="layui-inline fl mgl10">
						<select name="orgAttributes" id="orgAttributes" placeholder="请选择" lay-search>
						    <option value="" selected="true">全部</option>
						    <option value="1">医疗类</option>
						    <option value="2">医技类</option>
						    <option value="3">护理类</option>
						    <option value="4">药剂类</option>
						    <option value="5">辅助卫计类</option>
						    <option value="6">行政类</option>
						</select>
					</div>
                    <div class="pubBtnBox fl mgl10">
                        <button type="button" class="layui-btn" lay-submit="" lay-filter="systemManageOrgDepUserScreenSearch" search-btn="common-user">搜索</button>
                        <button type="button" class="layui-btn oa-btn-reset" id="common_userSel_screenReset"><i class="layui-icon layui-icon-refresh"></i></button>
                    </div>
                </form>
            </div>
            <div class="trasen-con-box">
                <div class="table-box">
                    <table id="deptUserTable"></table>
                    <div id="tablePager"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="value-box" id="userShow">
        <!-- <textarea readonly style="width: 100%; height: 60px" class="layui-textarea"></textarea> -->
        <div class="selectUserNameBox" id="select_UserNameBox"></div>
    </div>
    <div class="layer-btn bottom-box">
        <p style="float: left; margin-left: 20px">
            已选人数:
            <span id="allUserNum">0</span>
            人
        </p>
        <a href="javascript:;" class="layui-btn" id="save">保存</a>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="clear">清空</a>
        <a href="javascript:;" class="layui-btn layui-btn-primary" id="close">取消</a>
    </div>
</div>
