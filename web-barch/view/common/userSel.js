'use strict';
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(
      ['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect'],
      function () {
        var form = layui.form,
          laydate = layui.laydate,
          trasen = layui.trasen,
          upload = layui.upload;

        layer.open({
          type: 1,
          title: opt.title,
          closeBtn: 1,
          shadeClose: false,
          area: ['1150px', '650px'],
          skin: 'yourclass',
          content: html,
          success: function (layero, index) {
            let sortData = {
              sidx: 'create_date', //创建时间降序
              sord: 'desc',
            };
            common.overlayScrollbarsSet(' .scrollbar-box');
            //获取排序设置
            commonDictCache.getDict('CUSTOM_CODE', function (dict) {
              const sortConfig = {
                1: {
                  sidx: 'a.create_date', //创建时间降序
                  sord: 'desc',
                },
                2: {
                  sidx: 'a.emp_sort', //排序字段升序
                  sord: 'asc',
                },
                3: {
                  sidx: 'a.entry_date', //入院时间升序
                  sord: 'asc',
                },
                4: {
                  sidx: 'c.seq_no', //组织机构升序
                  sord: 'asc',
                },
              };
              let sortCode = dict.find(
                (e) => e.itemCode == 'HRM_ARCHIVES_MANAGE_SORT'
              );
              if (sortCode && sortCode.itemNameValue) {
                sortData = sortConfig[Number(sortCode.itemNameValue)];
              }
            });
            var reg = new RegExp('\\|', 'g'); //g,表示全部替换。

            var treeData = [];
            var deptId = opt.data.deptTree ? opt.data.deptTree[0].id : null; //用于加载指定科室树
            var groupId = null;

            var deptTreeNameArr = [];
            var deptTreeCodeArr = [];
            var isCheckDept = opt.data.isCheckDept;
            var resourceSetting = {};
            if ('Y' == isCheckDept) {
              var deptTreeCodeStr = $('#' + opt.data.dept_code).val();
              if (deptTreeCodeStr.indexOf('|') != -1) {
                deptTreeCodeStr = deptTreeCodeStr.replace(reg, '');
              }
              if ('' != deptTreeCodeArr) {
                deptTreeCodeArr = deptTreeCodeStr.split(',');
              }
              resourceSetting = {
                view: {
                  dblClickExpand: false,
                  showTitle: true,
                  showIcon: false,
                },
                callback: {
                  beforeClick: zTreeBeforeCheck,
                  onCheck: zTreeOnCheck,
                },
                //勾选时，关联父，不关联子，取消时，关联父子
                check: {
                  enable: true,
                  autoCheckTrigger: true,
                  chkStyle: 'checkbox',
                  chkboxType: {
                    Y: 's',
                    N: 's',
                  },
                },
                data: {
                  simpleData: {
                    enable: true,
                  },
                },
              };
            } else {
              resourceSetting = {
                view: {
                  dblClickExpand: false,
                  showTitle: true,
                  showIcon: false,
                },
                callback: {
                  beforeClick: zTreeBeforeCheck,
                },
                data: {
                  simpleData: {
                    enable: true,
                  },
                },
              };
            }

            setTree();
            function setTree() {
              if (opt.noRenderTree) {
                $('#chooseDeptUserForm .select-left-box').hide();
                return false;
              }

              if (opt.data.deptTree) {
                treeData = opt.data.deptTree;
              } else if (opt.data.isChildOrg && opt.data.isChildOrg == 'Y') {
                $.ajax({
                  method: 'get',
                  url: '/ts-oa/thpsSysetm/getThpsOrgChildsId',
                  async: false,
                  success: function (data) {
                    treeData = data.object;
                  },
                });
              } else {
                $.ajax({
                  method: 'get',
                  url: '/ts-basics-bottom/organization/getTree',
                  async: false,
                  success: function (data) {
                    treeData = data.object;
                  },
                });
                $.ajax({
                  method: 'get',
                  url: '/ts-basics-bottom/employee/orgGroup/getOrgGroupTree?groupType=0',
                  async: false,
                  success: function (data) {
                    treeData.push(data.object[0]);
                  },
                });
                $.ajax({
                  method: 'get',
                  url: '/ts-basics-bottom/employee/orgGroup/getOrgGroupTree?groupType=1',
                  async: false,
                  success: function (data) {
                    treeData.push(data.object[0]);
                  },
                });
              }
              if ('Y' == isCheckDept) {
                $.fn.zTree.init($('#deptTree'), resourceSetting, treeData);
                var SelDeptIdStr = $('#' + opt.data.dept_code).val();
                if (
                  SelDeptIdStr != '' &&
                  SelDeptIdStr != null &&
                  SelDeptIdStr != 'null'
                ) {
                  /*var reg1 = new RegExp('\\|', 'g'); //g,表示全部替换。
                  if (SelDeptIdStr.indexOf('|') != -1) {
                      SelDeptIdStr = SelDeptIdStr.replace(reg1, '');
                  }*/
                  var SelDeptIdArr = SelDeptIdStr.split(',');
                  //获取ztree对象
                  var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                  //遍历勾选角色关联的菜单数据
                  for (var i = 0; i < SelDeptIdArr.length; i++) {
                    //根据角色菜单节点数据的属性搜索，获取与完整菜单树完全匹配的节点JSON对象集合
                    var nodes = treeObj.getNodesByParam(
                      'id',
                      SelDeptIdArr[i],
                      null
                    );
                    deptTreeNameArr.push(nodes[0].name);
                    //勾选当前选中的节点
                    treeObj.checkNode(nodes[0], true, false);
                  }
                }
              } else {
                $.fn.zTree.init($('#deptTree'), resourceSetting, treeData);
              }
            }

            // 列表
            var grid = $('#deptUserTable');
            var dataurl = `/ts-oa/employee/list?empStatus=1&userSel=true`;
            var userNameArr = [];
            var userIdArr = [];
            var userCodeArr = [];
            var deptNameArr = [];
            var deptUserNameArr = [];
            var userNamePhoneArr = [];
            var userDatasArr = opt.data.user_datas || [];
            var userStr =
              $('#' + opt.data.user_str).val() || opt.data.userNameStrs || '';
            var userIdStr =
              $('#' + opt.data.user_id).val() || opt.data.userIdStrs || '';
            var userCodeStr =
              $('#' + opt.data.user_code).val() || opt.data.userCodeStrs || '';
            var deptUserNameStr =
              $('#' + opt.data.dept_user_name).val() ||
              opt.data.deptUserNameStr ||
              '';
            var userNamePhoneStr =
              $('#' + opt.data.user_name_phone).val() ||
              opt.data.userNamePhoneStr ||
              '';

            if (userIdStr.indexOf('|') != -1) {
              userIdStr = userIdStr.replace(reg, '');
            }
            if (userCodeStr.indexOf('|') != -1) {
              userCodeStr = userCodeStr.replace(reg, '');
            }
            if (userStr == 'null') {
              userStr = '';
            }
            var userArr = userStr.split(';');
            userNameArr = userArr[0].split(',');
            userIdArr = userIdStr.split(',');
            userCodeArr = userCodeStr.split(',');

            if ('' != deptUserNameStr) {
              deptUserNameArr = deptUserNameStr.split(',');
            }

            if ('' != userNamePhoneStr) {
              userNamePhoneArr = userNamePhoneStr.split(',');
            }

            if (!userStr) {
              userNameArr = [];
              userIdArr = [];
              userCodeArr = [];
              userDatasArr = [];
            } else {
              for (var i = userNameArr.length - 1; i >= 0; i--) {
                var deptNameIndex = deptTreeNameArr.findIndex((item) => {
                  return item === userNameArr[i];
                });
                if (!userNameArr[i].trim() || deptNameIndex != -1) {
                  userNameArr.splice(i, 1);
                }
              }
              userIdArr = handleDataTrim(userIdArr);
              userCodeArr = handleDataTrim(userCodeArr);
            }
            $('#chooseDeptUserForm #allUserNum').html(userNameArr.length);
            var userHtml = setUserHtml(userNameArr, 'user');
            userHtml += setUserHtml(deptTreeNameArr, 'dept');
            $('#select_UserNameBox').html(userHtml);

            function handleDataTrim(list) {
              var data = common.deepClone(list);
              for (var i = data.length - 1; i >= 0; i--) {
                if (!data[i].trim()) {
                  data.splice(i, 1);
                }
              }
              return data;
            }

            function zTreeBeforeCheck(treeId, treeNode) {
              //存在子级时展开节点
              if (treeNode.children && !treeNode.open) {
                var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                treeObj.expandNode(treeNode, true);
                treeObj.refresh();
              }

              $('#resourcesUserMainForm')[0].reset();
              var dataType = treeNode.dataType;

              if ('group' == dataType) {
                groupId = treeNode.id;
                grid.jqGrid('setGridParam', {
                  mtype: 'get',
                  url: '/ts-basics-bottom/employee/orgGroup/getOrgGroupUser',
                  datatype: 'json',
                  search: true,
                  jsonReader: {
                    page: 'pageNo',
                    total: 'pageCount',
                    records: 'totalCount',
                  },
                  page: 1,
                  postData: {
                    empCode: null,
                    empName: null,
                    userAccounts: null,
                    empDeptName: null,
                    empDeptCode: null,
                    groupId: treeNode.id,
                    deptCodeSeach: null,
                  },
                });
                grid.trigger('reloadGrid');
              } else {
                deptId = treeNode.id;
                grid.jqGrid('setGridParam', {
                  mtype: 'post',
                  url: '/ts-oa/employee/list?userSel=true',
                  datatype: 'json',
                  search: true,
                  jsonReader: {
                    page: 'pageNo',
                    total: 'pageCount',
                    records: 'totalCount',
                  },
                  prmNames: {
                    page: 'pageNo',
                    rows: 'pageSize',
                    sort: 'sidx',
                    order: 'sord',
                    search: 'false',
                    nd: 'nd',
                  },
                  page: 1,
                  postData: {
                    empCode: null,
                    empName: null,
                    userAccounts: null,
                    empDeptName: null,
                    empDeptCode: null,
                    groupId: null,
                    deptCodeSeach: treeNode.id,
                    searchChild: opt.data.searchChild || 'Y',
                    seachOrgChildAll:
                      opt.data.isChildOrg && opt.data.isChildOrg == 'Y'
                        ? 'Y'
                        : 'N',
                  },
                });
                grid.trigger('reloadGrid');
              }
              return true;
            }

            //检索树
            $(document)
              .off('input', '#chooseDeptUserForm #groupName')
              .on('input', '#chooseDeptUserForm #groupName', function () {
                var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                var val = $(this).val();
                setTimeout(function () {
                  $.fn.ztreeQueryHandler(treeObj, val);
                }, 200);
              });

            function zTreeOnCheck(event, treeId, treeNode) {
              var treeObj = $.fn.zTree.getZTreeObj('deptTree');
              var nodes = treeObj.getCheckedNodes(true);
              var userHtml = setUserHtml(userNameArr, 'user');
              var deptTreeNodesArr = [];
              if (nodes.length > 0) {
                for (var j = 0; j < nodes.length; j++) {
                  deptTreeNodesArr.push(nodes[j].name);
                }
                userHtml += setUserHtml(deptTreeNodesArr, 'dept');
              }
              $('#chooseDeptUserForm #allDeptNum')
                .html(nodes.length)
                .parent()
                .removeClass('none');
              $('#chooseDeptUserForm #allUserNum').html(userNameArr.length);
              $('#select_UserNameBox').html(userHtml);
            }
            var table = grid.jqGrid({
              url: dataurl,
              //styleUI : 'Bootstrap',
              mtype: 'post',
              datatype: 'json',
              sortname: sortData?.sidx ?? 'c.code',
              sortorder: sortData?.sord ?? 'asc',
              jsonReader: {
                page: 'pageNo',
                total: 'pageCount',
                records: 'totalCount',
              },
              prmNames: {
                page: 'pageNo',
                rows: 'pageSize',
                sort: 'sidx',
                order: 'sord',
                search: 'false',
                nd: 'nd',
              },
              postData: {
                deptCodeSeach: opt.data.deptTree ? opt.data.deptTree[0].id : '',
                seachOrgChildAll:
                  opt.data.isChildOrg && opt.data.isChildOrg == 'Y' ? 'Y' : 'N',
                searchChild: opt.data.searchChild || 'Y',
              },
              colModel: [
                {
                  label: '工号',
                  name: 'userAccounts',
                  index: 'USER_ACCOUNTS',
                  width: 170,
                  align: 'center',
                  editable: false,
                },
                {
                  label: '姓名',
                  name: 'empName',
                  index: 'EMP_NAME',
                  align: 'center',
                  width: 170,
                },
                {
                  label: '性别',
                  name: 'empSex',
                  index: 'EMP_SEX',
                  width: 130,
                  align: 'center',
                  formatter: function (cellvalue, options, row) {
                    if (row.empSex == 1) {
                      return '女';
                    } else if (row.empSex == 0) {
                      return '男';
                    } else {
                      return '未知';
                    }
                  },
                },
                {
                  label: '所属部门',
                  name: 'empDeptName',
                  index: 'empDeptName',
                  width: 300,
                  align: 'center',
                  editable: false,
                },
                {
                  label: '所属部门Id',
                  name: 'empDeptId',
                  width: 200,
                  hidden: true,
                },
                {
                  label: '状态',
                  name: 'empStatus',
                  index: 'emp_status',
                  width: 65,
                  hidden: true,
                },
                // {
                //   label: "状态",
                //   name: "empStatus",
                //   width: 65,
                //   align: "center",
                //   formatter: function (cellvalue, options, row) {
                //     if (
                //       row.empStatus == 1 ||
                //       row.empStatus == 99 ||
                //       row.empStatus == 88
                //     ) {
                //       return "<font color='blue'>启用</font>";
                //     } else {
                //       return "<font color='red'>停用</font>";
                //     }
                //   },
                // },
                {
                  label: 'id',
                  name: 'id',
                  hidden: true,
                },
                {
                  label: 'empCode',
                  name: 'empCode',
                  hidden: true,
                  key: true,
                },
                {
                  label: 'empPhone',
                  name: 'empPhone',
                  hidden: true,
                },
                {
                  label: 'tag',
                  name: 'tag',
                  width: 10,
                  hidden: true,
                }, //用于多行编辑后需要更新行标志.
              ],
              viewrecords: true,
              //autowidth: true,
              height: 'auto',
              rowNum: opt.rowNum || 300,
              // rowList: opt.rowList,
              rownumbers: true,
              //minColWidth : 100,
              forceFit: false,
              shrinkToFit: true,
              //ondblClickRow: editRow,
              multiselect:
                opt.data.isMultiSelect === undefined
                  ? true
                  : opt.data.isMultiSelect,
              gridview: true,
              altRows: true,
              //隔行变色
              /* loadComplete: function () {
                            grid.setGridWidth($('#centerDiv').width());
                        },*/
              gridComplete: function () {
                var ids = table.getDataIDs();
                for (var i in ids) {
                  if (userCodeArr.indexOf(ids[i]) > -1) {
                    grid.setSelection(ids[i], true);
                  }
                }
              },
              onSelectAll: function (rowid, status) {
                for (var i = 0; i < rowid.length; i++) {
                  var selected_username = table.getCell(rowid[i], 'empName');
                  var selected_usercode = table.getCell(rowid[i], 'empCode');
                  var selected_deptname = table.getCell(
                    rowid[i],
                    'empDeptName'
                  );
                  var selected_phone = table.getCell(rowid[i], 'empPhone');
                  var selected_userdata = {
                    id: rowid[i],
                    username: selected_username,
                    usercode: selected_usercode,
                    sex: table.getCell(rowid, 'empSex'),
                    deptname: selected_deptname,
                    deptcode: table.getCell(rowid, 'empSex'),
                    mobileNo: selected_phone,
                  };
                  if (status) {
                    if ($.inArray(selected_usercode, userCodeArr) == -1) {
                      userCodeArr.push(selected_usercode);
                      userNameArr.push(selected_username);
                      deptNameArr.push(selected_deptname);
                      deptUserNameArr.push(
                        selected_deptname + '-' + selected_username
                      );
                      if ('' != selected_phone && null != selected_phone) {
                        userNamePhoneArr.push(
                          selected_username + '(' + selected_phone + ')'
                        );
                      } else {
                        userNamePhoneArr.push(selected_username);
                      }
                      userIdArr.push(rowid[i]);
                      userDatasArr.push(selected_userdata);
                    }
                  } else {
                    var index = userCodeArr.indexOf(selected_usercode);
                    userNameArr.splice(index, 1);
                    userIdArr.splice(index, 1);
                    userCodeArr.splice(index, 1);
                    deptNameArr.splice(index, 1);
                    deptUserNameArr.splice(index, 1);
                    userNamePhoneArr.splice(index, 1);
                    userDatasArr.splice(
                      userDatasArr.findIndex(
                        (item) => item.usercode == selected_usercode
                      ),
                      1
                    );
                  }
                }
                var userHtml = setUserHtml(userNameArr, 'user');
                var deptTreeNodesArr = [];
                if ('Y' == isCheckDept) {
                  var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                  var nodes = treeObj.getCheckedNodes(true);

                  if (nodes.length > 0) {
                    for (var j = 0; j < nodes.length; j++) {
                      deptTreeNodesArr.push(nodes[j].name);
                    }
                    userHtml += setUserHtml(deptTreeNodesArr, 'dept');
                  }
                  $('#chooseDeptUserForm #allDeptNum')
                    .html(nodes.length)
                    .parent()
                    .removeClass('none');
                }
                $('#chooseDeptUserForm #allUserNum').html(userNameArr.length);
                $('#select_UserNameBox').html(userHtml);
              },
              onSelectRow: function (rowid, status) {
                var selected_username = table.getCell(rowid, 'empName');
                var selected_usercode = table.getCell(rowid, 'empCode');
                var selected_deptname = table.getCell(rowid, 'empDeptName');
                var selected_phone = table.getCell(rowid, 'empPhone');
                var selected_userdata = {
                  id: rowid,
                  username: selected_username,
                  usercode: selected_usercode,
                  sex: table.getCell(rowid, 'empSex'),
                  deptname: selected_deptname,
                  deptcode: table.getCell(rowid, 'empSex'),
                  mobileNo: selected_phone,
                };
                if (status) {
                  if (userIdArr.indexOf(rowid) < 0) {
                    if ($.inArray(selected_usercode, userCodeArr) == -1) {
                      userCodeArr.push(selected_usercode);
                      userNameArr.push(selected_username);
                      deptNameArr.push(selected_deptname);
                      deptUserNameArr.push(
                        selected_deptname + '-' + selected_username
                      );
                      if ('' != selected_phone && null != selected_phone) {
                        userNamePhoneArr.push(
                          selected_username + '(' + selected_phone + ')'
                        );
                      } else {
                        userNamePhoneArr.push(selected_username);
                      }
                      userIdArr.push(rowid);
                      userDatasArr.push(selected_userdata);
                    }
                  }
                } else {
                  var index = userCodeArr.indexOf(selected_usercode);
                  userNameArr.splice(index, 1);
                  userIdArr.splice(index, 1);
                  userCodeArr.splice(index, 1);
                  deptNameArr.splice(index, 1);
                  deptUserNameArr.splice(index, 1);
                  userNamePhoneArr.splice(index, 1);
                  userDatasArr.splice(
                    userDatasArr.findIndex(
                      (item) => item.usercode == selected_usercode
                    ),
                    1
                  );
                }
                if (opt.data.isMultiSelect === false) {
                  $('#select_UserNameBox').html(
                    setUserHtml([selected_username], 'user')
                  );
                  return false;
                }
                var userHtml = setUserHtml(userNameArr, 'user');
                var deptTreeNodesArr = [];
                if ('Y' == isCheckDept) {
                  var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                  var nodes = treeObj.getCheckedNodes(true);

                  if (nodes.length > 0) {
                    for (var j = 0; j < nodes.length; j++) {
                      deptTreeNodesArr.push(nodes[j].name);
                    }
                    userHtml += setUserHtml(deptTreeNodesArr, 'dept');
                  }
                  $('#chooseDeptUserForm #allDeptNum')
                    .html(nodes.length)
                    .parent()
                    .removeClass('none');
                }
                $('#chooseDeptUserForm #allUserNum').html(userNameArr.length);
                $('#select_UserNameBox').html(userHtml);
              },
              pager: '#deptUserPager',
            });
            // 保存
            form.on('submit(chooseUserSubmitCofirm)', function (data) {
              if (opt.data.isMultiSelect === false) {
                var rowId = table.getGridParam('selrow');
                if (!rowId || rowId == null) {
                  layer.msg('请选择一行数据!');
                  return false;
                }
                var rowData = table.jqGrid('getRowData', rowId);
                $('#' + opt.data.user_str).val(rowData.empName); //用户姓名username、部门名称
                $('#' + opt.data.user_code).val(rowData.empCode); //用户code
                $('#' + opt.data.dept_user_name).val(
                  rowData.empDeptName + '-' + rowData.empName
                ); //用户code
                layer.close(layer.index);
                opt.callback(
                  rowData.empName,
                  [rowId],
                  [rowData.empCode],
                  userDatasArr
                );
                return false;
              }
              if ('Y' == isCheckDept) {
                var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                var nodes = treeObj.getCheckedNodes(true);
                layer.close(layer.index);
                var selUser = userNameArr.toString();
                var deptIdArr = [];
                if (nodes.length > 0) {
                  var nodesArr = [];
                  for (var j = 0; j < nodes.length; j++) {
                    nodesArr.push(nodes[j].name);
                    deptIdArr.push(nodes[j].id);
                  }
                  if (selUser == '') {
                    selUser = nodesArr.toString();
                  } else {
                    selUser = selUser + ';' + nodesArr.toString();
                  }
                }
                if (opt.data.user_str) {
                  $('#' + opt.data.user_str).val(selUser); //用户姓名username、部门名称
                  $('#' + opt.data.user_id).val(userIdArr); //用户id
                  $('#' + opt.data.user_code).val(userCodeArr); //用户账号usercode
                  $('#' + opt.data.dept_code).val(deptIdArr); //部门ID
                  $('#' + opt.data.dept_user_name).val(deptUserNameArr);
                  $('#' + opt.data.user_name_phone).val(userNamePhoneArr);
                }
                opt.callback &&
                  opt.callback(
                    selUser,
                    userIdArr,
                    userCodeArr,
                    deptIdArr,
                    deptNameArr,
                    deptUserNameArr,
                    userNamePhoneArr,
                    userDatasArr
                  );
              } else {
                layer.close(layer.index);
                var selUserNameVal = userNameArr.toString();
                if (opt.data.user_str) {
                  $('#' + opt.data.user_str).val(selUserNameVal); //用户姓名username
                  $('#' + opt.data.user_id).val(userIdArr); //用户id
                  $('#' + opt.data.user_code).val(userCodeArr); //用户账号usercode
                  $('#' + opt.data.dept_user_name).val(deptUserNameArr);
                  $('#' + opt.data.user_name_phone).val(userNamePhoneArr);
                }
                opt.callback &&
                  opt.callback(
                    selUserNameVal,
                    userIdArr,
                    userCodeArr,
                    deptNameArr,
                    deptUserNameArr,
                    userNamePhoneArr,
                    userDatasArr
                  );
              }
            });

            //清空所选人员
            form.on('submit(chooseUserSubmitEmpty)', function () {
              // 获取所有选中行id
              var jqGridRowid = table.jqGrid('getGridParam', 'selarrrow');
              for (var i = 0; i < jqGridRowid.length; i++) {
                table.setSelection(jqGridRowid[i], false);
                i--;
              }
              userNameArr = [];
              userIdArr = [];
              userCodeArr = [];
              deptUserNameArr = [];
              userNamePhoneArr = [];
              if ('Y' == isCheckDept) {
                var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                treeObj.checkAllNodes(false);
                $('#chooseDeptUserForm #allDeptNum').html(0);
              }
              $('#chooseDeptUserForm #allUserNum').html(0);
              $('#select_UserNameBox').html('');
            });

            //设置选中
            function setUserHtml(list, type) {
              var htmlStr = '';
              for (var i = 0; i < list.length; i++) {
                htmlStr += `<div class="user-item" data-index="${i}" data-type="${type}">
                                    ${list[i]}
                                    <i class="delete-btn oaicon oa-icon-cuowu"></i>
                                </div>`;
              }
              return htmlStr;
            }

            //单个删除
            $(layero)
              .off('click', '#select_UserNameBox .delete-btn')
              .on('click', '#select_UserNameBox .delete-btn', function () {
                var deleteItem = $(this).parent();
                var type = deleteItem.attr('data-type');
                var index = deleteItem.attr('data-index');
                if (type == 'dept') {
                  var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                  var nodes = treeObj.getCheckedNodes(true);
                  if (nodes.length > 0) {
                    treeObj.checkNode(nodes[index], false, false);
                  }
                  var length = $('#chooseDeptUserForm #allDeptNum').html();
                  $('#chooseDeptUserForm #allDeptNum').html(length - 1);
                } else {
                  table.setSelection(userIdArr[index], false);
                  userNameArr.splice(index, 1);
                  userIdArr.splice(index, 1);
                  userCodeArr.splice(index, 1);
                  deptUserNameArr.splice(index, 1);
                  userNamePhoneArr.splice(index, 1);
                  var length = $('#chooseDeptUserForm #allUserNum').html();
                  $('#chooseDeptUserForm #allUserNum').html(length - 1);
                }
                deleteItem.remove();
                $('#chooseDeptUserForm #select_UserNameBox .user-item').each(
                  (dataIndex, item) => {
                    $(item).attr('data-index', dataIndex);
                  }
                );
              });

            //关闭选人弹窗
            form.on('submit(closeChooseUser)', function () {
              layer.close(layer.index);
            });

            //查询用户
            form.on(
              'submit(systemManageOrgDepUserScreenSearch)',
              function (data) {
                seachDeptUser();
              }
            );

            //检索重置
            $('#common_userSel_screenReset')
              .off('click')
              .on('click', function () {
                $('#resourcesUserMainForm')[0].reset();
                var treeObj = $.fn.zTree.getZTreeObj('deptTree');
                treeObj.cancelSelectedNode();
                deptId = opt.data.deptTree ? opt.data.deptTree[0].id : null;
                groupId = null;
                seachDeptUser();
              });
            $('#chooseDeptUserForm [search-input="common-user"]')
              .off('input')
              .on(
                'input',
                common.debounce(function () {
                  seachDeptUser();
                }, 200)
              );

            function seachDeptUser() {
              var formData = trasen.getNamesVal($('#resourcesUserMainForm'));
              var params = {};
              if (formData) {
                $.each(formData, function (i, v) {
                  if (i != '') {
                    params[i] = v;
                  }
                });
              }
              params['deptCodeSeach'] = deptId;
              params['groupId'] = groupId;
              params['searchChild'] = opt.data.searchChild || 'Y';
              params['seachOrgChildAll'] =
                opt.data.isChildOrg && opt.data.isChildOrg == 'Y' ? 'Y' : 'N';
              grid.jqGrid('setGridParam', {
                mtype: 'post',
                url: '/ts-oa/employee/list?userSel=true',
                datatype: 'json',
                search: true,
                postData: params,
                jsonReader: {
                  page: 'pageNo',
                  total: 'pageCount',
                  records: 'totalCount',
                },
                prmNames: {
                  page: 'pageNo',
                  rows: 'pageSize',
                  sort: 'sidx',
                  order: 'sord',
                  search: 'false',
                  nd: 'nd',
                },
                page: 1,
              });
              grid.trigger('reloadGrid');
              return false;
            }

            // 部门下拉
            // function treeSelect() {
            //     var treeObj = $.fn.zTree.getZTreeObj('deptTree');
            //     var data = treeObj.getNodes();
            //     $.trees('systemManageOrgDepUserPrevDept', {
            //         type: 'local',
            //         // local: 静态
            //         data: data,
            //         // 静态数据
            //         checkbox: false,
            //         // 选择框显示
            //         condition: 'name',
            //         // 检索参数
            //         zTreeOnClick: function (treeId, node) {
            //             // 回调   treeId：树id   node：点击子节点的数据
            //             // id  packageName alink
            //             deptcode = node.id;
            //         },
            //     });
            // }
          },
        });
      }
    );
  };
});
