define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'trasen', 'carousel'], function () {
            var form = layui.form,
                trasen = layui.trasen,
                carousel = layui.carousel;

            var color = '#d81e06',
                type = 'top',
                ystype = '1', // 元素类型
                imgformdata = false, // 图表数据
                imgformthdata = false, // 图表数据
                imgformi = 1,
                imgformList = ''; // 图表类型列表
            var editor = null; // 编辑器对象
            var dropData = []; // 配置的首页的数据
            var tabData = []; // 当前配置的tab的数据
            var topData = []; // 当前配置的tab的数据
            var layersx = []; // 当前布局
            var ysListData = []; // 元素数据

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                maxmin: false,
                shadeClose: false,
                shade: 0.2,
                area: ['100%', '100%'],
                content: html,
                success: function (layero, index) {
                    if (opt.type == 1) {
                        edifun();
                    } else {
                        init();
                    }
                    form.render();
                },
            });

            // 初始化
            function init() {
                // 拉取模版
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/layout/getLayoutsByName',
                    data: {},
                    success: function (res) {
                        if (res.rows) {
                            var arr = res.rows;
                            if (arr.length > 0) {
                                var v = JSON.parse(arr[0].layoutHtml);
                                layersx = v;
                                layerh(v);
                                bjlist(arr[0].layoutName, arr[0].layoutId);
                            }
                        }
                    },
                    error: function (res) {},
                });

                ystree();
            }

            // 编辑
            function edifun() {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/Aftintemplate/getAftintemplateById',
                    data: {
                        templateId: opt.id,
                    },
                    success: function (res) {
                        if (res.success === true) {
                            var content = JSON.parse(res.object[0].cotent || '[]');
                            $('#portalMotifPageSetMHID').val(res.object[0].templateId);
                            $('#portalMotifPageSetMHname').val(res.object[0].templateName);
                            $('#portalMotifPageSetMHterminalid').val(res.object[0].terminalid);
                            layersx = content.layer;
                            dropData = content.data;
                            topData = content.topData || [];
                            layerh(layersx);
                            bjlist(content.layer_s.name, content.layer_s.code);
                            buildView(); // 构建模版
                            screenShow();
                        }
                    },
                    error: function (res) {},
                });
                ystree();
            }

            // 布局功能加载
            function dragLoad(elm, arr) {
                $.quoteFun(
                    'drag',
                    {
                        html: false,
                        elm: elm,
                        data: arr,
                        dropData: function (index, dragXT, dragfx) {
                            var d = dropData[index];
                            if (dragXT < index) {
                                dropData.splice(index, 1);
                                if (dragfx == 'l') {
                                    dropData.splice(dragXT, 0, d);
                                } else if (dragfx == 'r') {
                                    dropData.splice(dragXT + 1, 0, d);
                                }
                            } else if (dragXT > index) {
                                if (dragfx == 'l') {
                                    dropData.splice(dragXT, 0, d);
                                } else if (dragfx == 'r') {
                                    dropData.splice(dragXT + 1, 0, d);
                                }
                                dropData.splice(index, 1);
                            }
                        },
                        callback: function (arr, i) {
                            layersx = arr;
                            if (i) {
                                dropData.splice(i, 1);
                            }
                        },
                    },
                    true
                );
            }

            // 筛选栏渲染
            function screenShow() {
                if (topData.length > 0) {
                    $('#portalMotifPageSetContent').css('top', '50px');
                    $('#portalMotifPageSetScreen').css('height', '50px');
                    var dom = $('#portalMotifPageSetScreen');
                    topData.forEach(function (v, i) {
                        var lx = elmType(v.type);
                        $.portal.init(lx, dom, v);
                    });
                }
            }

            // 构建模版
            function buildView() {
                for (var i in dropData) {
                    var arr = dropData[i] || [];
                    var dom = $('#portalMotifPageSetContent .item').eq(i);
                    arr.forEach(function (v, i) {
                        $.portal.init(elmType(v.type), dom, v, carousel);
                    });
                }
            }

            // 元素树渲染
            function ystree() {
                $.ajax({
                    type: 'get',
                    url: common.url + '/ts-portal/api/portal/elment/getAllElementTree',
                    success: function (res) {
                        if (res.success == true) {
                            var arr = res.object || [];
                            var h = '';
                            var scData = [
                                // {
                                //     pid: -2,
                                //     pname: '布局',
                                //     children: [
                                //         { colUrl: '', content: 'grid', id: '', name: '删格' }
                                //     ]
                                // },
                                {
                                    pid: -1,
                                    pname: '筛选',
                                    children: [{ colUrl: '', content: 'time', id: '', name: '时间' }],
                                },
                            ];
                            ysListData = res.object;
                            arr = scData.concat(arr);
                            for (var i in arr) {
                                if (arr[i].pid != 5) {
                                    h +=
                                        '<li>\
                                                <a style="padding-left: 10px;">' +
                                        arr[i].pname +
                                        '</a>\
                                                <i class="fa fa-minus-square-o flexible" aria-hidden="true"></i>';
                                    if (typeof arr[i].children == 'object') {
                                        h += childtree(arr[i].children, arr[i].pid);
                                    }
                                    h += '</li>';
                                }
                            }
                            $('#portalMotifDraggable').html(h);
                            // 拖拽
                            $.quoteFun('/portalMotif/pageSet/drop', {
                                html: false,
                                data: arr,
                                callback: function (dom, data, i) {
                                    var type = data.type;
                                    if (type == 8 || type == 9 || type == -1) {
                                        topData.push(data);
                                    } else {
                                        if (dropData[i] == undefined) {
                                            var arr = [];
                                            arr.push(data);
                                            dropData[i] = arr;
                                        } else {
                                            dropData[i].push(data);
                                        }
                                    }
                                    var lx = elmType(type);
                                    $.portal.init(lx, dom, data, carousel);
                                },
                            });
                        }
                    },
                    error: function (res) {
                        // layer.msg('加载失败！');
                    },
                });
            }

            // 当前元素类型
            function elmType(type) {
                if (type == -1) {
                    // 筛选
                    return 'screen';
                } else if (type == 1) {
                    // 列表
                    return 'list';
                } else if (type == 2) {
                    // 文本
                    return 'text';
                } else if (type == 3) {
                    // 图
                    return 'img';
                } else if (type == 4) {
                    // 视频
                    return 'video';
                } else if (type == 5) {
                    //
                } else if (type == 6) {
                    // 图表
                    return 'chat';
                } else if (type == 7) {
                    // 快捷链接
                    return 'links';
                } else if (type == 8) {
                    // 多页签
                    return 'morePage';
                } else if (type == 10) {
                    // 方块签
                    return 'magicSign';
                } else if (type == 11) {
                    // 日程
                    return 'calendar';
                }
            }

            // 元素树递归
            function childtree(arr, pid) {
                var h = '<ul style="padding-left: 20px;">';
                for (var i in arr) {
                    h += '<li style="padding-left: 0;">\
                            <a draggable="true" id="' + arr[i].id + '" pid="' + pid + '">' + arr[i].name + '</a>\
                        </li>';
                    if (typeof arr[i].children == 'object') {
                        h += childtree(arr[i].children);
                    }
                    h += '</li>';
                }
                h += '</ul>';
                return h;
            }

            // 基础字段树展开收起
            $(document)
                .off('click', '#portalMotifDraggable .flexible')
                .on('click', '#portalMotifDraggable .flexible', function () {
                    var zt = $(this).hasClass('fa-plus-square-o');
                    if (zt) {
                        $(this).next('ul').show();
                        $(this).removeClass('fa-plus-square-o').addClass('fa-minus-square-o');
                    } else {
                        $(this).next('ul').hide();
                        $(this).removeClass('fa-minus-square-o').addClass('fa-plus-square-o');
                    }
                });

            // 标题图标
            $(document)
                .off('change', '#portalMotifPageSetIconFile')
                .on('change', '#portalMotifPageSetIconFile', function () {
                    var dom = document.querySelector('#portalMotifPageSetIconFile').files[0];
                    var data = new FormData();
                    data.append('file', dom);
                    $.ajax({
                        url: common.url + '/ts-portal/api/portal/elementtype/fileUpload',
                        type: 'post',
                        data: data,
                        processData: false,
                        contentType: false,
                        success: function (res) {
                            if (res.success) {
                                var src = 'http://139.9.248.93:8888' + res.object.url;
                                $('#portalMotifPageSetIconHide').val(src);
                                $('#portalMotifPageSetIconShow').css('background-image', 'url("' + src + '")');
                            } else {
                                layer.msg(res.message);
                            }
                        },
                        error: function (res) {
                            layer.msg('操作失败！');
                        },
                    });
                });

            // 布局选择
            function bjlist(name, code) {
                new $.selectPlug('#portalMotifPageSetBjbox', {
                    url: common.url + '/ts-portal/api/portal/layout/getLayoutsByName',
                    datatype: 'get', // 请求方式
                    searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    textName: 'layoutName', // 选项文字的key（接口返回数据里面的参数）
                    valName: 'layoutId', // 选项id的key（接口返回数据里面的参数）
                    inpTextId: 'portalMotifPageSetBjname', // 需要提交的已选文本的输入框的id属性值
                    inpValId: 'portalMotifPageSetBjval', // 需要提交的已选值的输入框的id属性值
                    inpTextName: 'portalMotifPageSetBjname', // 需要提交的已选文本的输入框的name属性值
                    inpValName: 'portalMotifPageSetBjval', // 需要提交的已选值的输入框的name属性值
                    defaultText: name, // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: code, // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res) {
                            var v = JSON.parse(res.layoutHtml);
                            layersx = v;
                            var _d = layerh(v);
                            buildView(); // 构建模版
                        }
                    },
                });
            }

            // 每个模块布局代码
            function layerh(arr) {
                var html = '';

                arr = countW(arr);
                arr = resetY(arr);

                for (var i in arr) {
                    html += '<div class="item" index="' + i + '" style="width: ' + arr[i][2] + 'px; height: ' + arr[i][3] + 'px; left: ' + arr[i][0] + 'px; top: ' + arr[i][1] + 'px;">';
                    html += '<a href="javascript:;" class="portalMotifPageSetPZYSdelBox" id="portalMotifPageSetPZYSdelBox" index="' + i + '">';
                    html += '<i class="fa fa-times-circle" aria-hidden="true"></i>';
                    html += '</a>';
                    html += '<div class="drag" index="' + i + '" draggable="true"></div>';
                    html += '</div>';
                }
                $('#portalMotifPageSetContent').html(html);
                dragLoad('portalMotifPageSetContent', arr);
                return arr;
            }

            // 计算元素纵坐标
            function resetY(newmk) {
                // var y = 5;
                var index = 0;
                for (var i in newmk) {
                    var v = newmk[i];
                    var _v = newmk[i + 1];

                    if (v[4] > 0) {
                        var arr = [];
                        for (var l in newmk) {
                            if (newmk[l][4] == v[4] - 1) {
                                arr.push(newmk[l]);
                            }
                        }
                        var _h = 0;
                        if (arr.length > 0) {
                            var ln = arr.length - 1;
                            for (var l in arr) {
                                var _now = arr[l];
                                var _next = -1;
                                var _wh = 0;
                                if (l < ln) {
                                    _next = arr[parseInt(l) + 1];
                                }
                                if (v[0] >= _now[0] && v[0] + v[2] <= _now[0] + _now[2] && typeof _next == 'object') {
                                    _wh = _now[1] + _now[3];
                                    _h = _wh;
                                    break;
                                } else if (v[0] >= _now[0] && v[0] + v[2] > _now[0] + _now[2] && typeof _next == 'object') {
                                    _wh = _now[1] + _now[3] > _next[1] + _next[3] ? _now[1] + _now[3] : _next[1] + _next[3];
                                } else if (v[0] < _now[0] && v[0] + v[2] <= _now[0] + _now[2] && typeof _next == 'object') {
                                    _wh = _now[1] + _now[3];
                                } else if (v[0] < _now[0] && v[0] + v[2] > _now[0] + _now[2] && typeof _next == 'object') {
                                    _wh = _now[1] + _now[3] > _next[1] + _next[3] ? _now[1] + _now[3] : _next[1] + _next[3];
                                } else if (v[0] >= _now[0] && v[0] <= _now[0] + _now[2] && _next == -1) {
                                    _wh = _now[1] + _now[3];
                                }

                                _h = _h > _wh ? _h : _wh;
                            }
                        }
                        // _h += 5;
                        newmk[i][1] = _h;
                    }
                }
                return newmk;
            }

            // 计算元素宽高
            function countWChild(arr, wBox, w, h) {
                var rownum = 0;
                var newmk = [];
                for (var i in arr) {
                    var _w = w * arr[i].w;
                    // var _h = h * arr[i].h * 8 + Math.ceil(arr[i].h  - 1 > 0 ? (arr[i].h  - 1):0) * 5;
                    var _h = h * arr[i].h * 8;
                    var _l = 0;
                    var _t = 0;
                    var _arr = [_l, _t, _w, _h];
                    newmk.push(_arr);
                }
                var number = [];
                for (var i in newmk) {
                    var dw = newmk[i][2];
                    if (i == 0) {
                        newmk[i][0] = 0;
                        newmk[i][1] = 5;
                        newmk[i][4] = rownum;
                        number[rownum] = 1;
                        continue;
                    }
                    var _arr = newmk[i - 1];
                    var _x = _arr[0] + _arr[2]; // 当前元素位置
                    if (dw + _x > wBox + 15) {
                        // 元素宽度与位置总和大于画布宽度换行
                        rownum++;
                        newmk[i][0] = 0;
                        newmk[i][1] = _arr[1] + _arr[3];
                    } else {
                        newmk[i][0] = _x;
                        newmk[i][1] = newmk[i - 1][1];
                    }
                    newmk[i][4] = rownum;
                    if (number[rownum]) {
                        number[rownum]++;
                    } else {
                        number[rownum] = 1;
                    }
                }
                for (var i in newmk) {
                    var _n = newmk[i][4];
                    var _num = number[_n] - 1 == 0 ? 0 : ((number[_n] - 1) * 5) / number[_n];

                    // newmk[i][2] = newmk[i][2] - _num;

                    var dw = newmk[i][2];
                    if (i == 0) {
                        newmk[i][0] = 0;
                        continue;
                    }
                    var _arr = newmk[i - 1];
                    var _x = _arr[0] + _arr[2]; // 当前元素位置
                    if (dw + _x > wBox + 15) {
                        // 元素宽度与位置总和大于画布宽度换行
                        newmk[i][0] = 0;
                    } else {
                        newmk[i][0] = _x;
                    }
                }
                return newmk;
            }

            // 计算位置
            function countW(arr) {
                var wBox = $('#portalMotifPageSetContent').width();
                var hBox = $('#portalMotifPageSetContent').height();
                var w = wBox / 12;
                var h = 25;
                var newmk = countWChild(arr, wBox, w, h);
                // var _rows = -1;
                // var _domh = 0;
                // for(var i in newmk){
                //     if(_rows + 1 == newmk[i][4]){
                //         _rows ++;
                //         _domh += newmk[i][3];
                //     }
                // }
                // if(_domh > hBox){
                //     wBox -= 15;
                //     w = wBox / 12;
                //     newmk = countWChild(arr, wBox, w, h);
                // }
                return newmk;
            }

            // 元素样式设置切换
            $('#portalMotifPageSetTab a').funs('click', function () {
                var i = $(this).index();
                $(this).addClass('active').siblings().removeClass('active');
                $('#portalMotifPageSetTabBox .item').eq(i).show().siblings().hide();
            });

            // 元素属性选卡添加
            $('#portalMotifPageSetTitXKadd').funs('click', function () {
                XKsetHtml({});
            });

            $(document)
                .off('click', '#portalMotifPageSetTitXKCon__del')
                .on('click', '#portalMotifPageSetTitXKCon__del', function () {
                    $(this).closest('.portalMotifPageSet__tabbox').remove();
                });

            // 元素属性选卡设置html
            function XKsetHtml(data, title) {
                var len = $('#portalMotifPageSetTitXKCon .xktt').length;
                var h =
                    '<div class="portalMotifPageSet__tabbox"><div class="xktt">\
                            选项' +
                    (len + 1) +
                    '\
                            <span id="portalMotifPageSetTitXKCon__del">\
                                <i class="fa fa-trash-o" aria-hidden="true"></i>\
                            </span>\
                        </div>\
                        <div class="portalMotifPageSetTab--item row">\
                            <div class="label">标题</div>\
                            <div class="con">\
                                <input type="text" value="' +
                    (title || '') +
                    '" id="portalMotifPageSetTitXKConTit' +
                    len +
                    '" class="layui-input">\
                            </div>\
                        </div>\
                        <div class="portalMotifPageSetTab--item row">\
                            <div class="label">数据源</div>\
                            <input type="hidden" value="" id="portalMotifPageSetTitXKConSJY' +
                    len +
                    'con" />\
                            <div class="con" id="portalMotifPageSetTitXKConSJY' +
                    len +
                    '">\
                            </div>\
                        </div></div>';
                $('#portalMotifPageSetTitXKCon').append(h);
                datasource('portalMotifPageSetTitXKConSJY' + len, data, null);
            }

            // 元素属性选卡数据源
            function datasource(dom, value, fn) {
                var arr = [];
                for (var i in ysListData) {
                    if (ysListData[i].children) {
                        arr = arr.concat(ysListData[i].children);
                    }
                }
                new $.selectPlug('#' + dom, {
                    searchType: 'local', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                    data: arr,
                    textName: 'name', 
                    valName: 'id', 
                    inpTextId: dom + 'name', 
                    inpValId: dom + 'val', 
                    inpTextName: dom + 'name', 
                    inpValName: dom + 'val', 
                    defaultText: value.name, // 默认显示值  如果是多选，值格式为：‘可靠的,看到’
                    defaultVal: value.code, // 默认显示值对应的code / id   如果是多选，值格式为：‘000003,000001’
                    callback: function (res) {
                        //res 当前选中项的数据  初始化时默认位false
                        if (res) {
                            $('#' + dom + 'con').val(res.content);
                        }
                    },
                });
            }

            // 配置页面模块点击
            $(document)
                .off('click', '#portalMotifPageSetContent .item')
                .on('click', '#portalMotifPageSetContent .item', function () {
                    var index = $(this).index();
                    var ha = $(this).hasClass('ha');
                    var ac = $(this).hasClass('active');
                    // if(ac == true){
                    //     return false;
                    // }
                    if (ha === false) {
                        $(this).siblings().removeClass('active');
                        $('#portalMotifPageSetRightcon').html('');
                        return false;
                    }
                    var setData = dropData[index][0].set;
                    $(this).addClass('active').siblings().removeClass('active');
                    var html = $('#portalMotifPageSetRight').html();
                    $('#portalMotifPageSetRightcon').html(html);
                    setData.tabcolor = setData.tabcolor || '333333';
                    trasen.setNamesVal($('#portalMotifPageSetRightcon'), setData);
                    form.render();
                    $('#portalMotifPageSetSelectBtn').css('background-color', '#' + setData.color); // 字体颜色
                    $('#portalMotifPageSetLineCoBtn').css('background-color', '#' + setData.borderColor); // 边框颜色
                    $('#portalMotifPageSetTitBgBtn').css('background-color', '#' + setData.bgcolor); // 背景颜色
                    $('#portalMotifPageSetTitmorBtn').css('background-color', '#' + setData.morecolor); // 更多字体颜色
                    $('#portalMotifPageSetRightcon').find('.fontB').prop('checked', setData.fontB); // 加粗
                    $('#portalMotifPageSetRightcon').find('.fontI').prop('checked', setData.fontI); // 斜体
                    $('#portalMotifPageSetRightcon').find('.fontU').prop('checked', setData.fontU); // 下划线
                    $('#portalMotifPageSetTitXKfnBtn').css('background-color', '#' + (setData.tabcolor || '333333')); // tab文本颜色
                    $('#portalMotifPageSetTitXKxzfBtn').css('background-color', '#' + (setData.tabsfcolor || '')); // tab选中字体颜色
                    $('#portalMotifPageSetTitXKbgBtn').css('background-color', '#' + (setData.tabsbgcolor || '')); // tab选中背景颜色
                    // $('#portalMotifPageSetTitXKfn').find('.tabcolor').prop('checked', setData.fontU);
                    // $('#portalMotifPageSetTitXKxzf').find('.tabsfcolor').prop('checked', setData.fontU);
                    colorselect(setData);
                    var xkTbl = dropData[index][0].tab || [];
                    //
                    xkTbl.forEach(function (v, i) {
                        XKsetHtml(
                            {
                                name: v.name,
                                code: v.id,
                            },
                            v.title
                        );
                    });
                });

            // 颜色选择器
            function colorselect(o) {
                // 颜色选择器
                $('#portalMotifPageSetSelect').colpick({
                    color: o.color,
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        // 改变颜色
                        $('#portalMotifPageSetSelect .text').val(hex);
                        $('#portalMotifPageSetSelect .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        // 改变颜色
                        $('#portalMotifPageSetSelect .text').val(hex);
                        $('#portalMotifPageSetSelect .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });

                // 标题栏边框颜色选择器
                $('#portalMotifPageSetLineCo').colpick({
                    color: o.borderColor,
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetLineCo input').val('#' + hex);
                        $('#portalMotifPageSetLineCo .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetLineCo .bg').css('background-color', '#' + hex);
                        $('#portalMotifPageSetLineCo input').val('#' + hex);
                        $(el).colpickHide();
                    },
                });

                // 标题栏背景颜色选择器
                $('#portalMotifPageSetTitBg').colpick({
                    color: o.bgcolor,
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetTitBg .text').val(hex);
                        $('#portalMotifPageSetTitBg .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetTitBg .text').val(hex);
                        $('#portalMotifPageSetTitBg .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });

                // 标题栏更多字体颜色选择器
                $('#portalMotifPageSetTitmor').colpick({
                    color: o.morecolor || '333333',
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetTitmore .text').val(hex);
                        $('#portalMotifPageSetTitmore .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetTitmore .text').val(hex);
                        $('#portalMotifPageSetTitmore .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });

                // tab字体颜色选择器
                $('#portalMotifPageSetTitXKfn').colpick({
                    color: o.tabcolor || '333333',
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetTitXKfn .text').val(hex);
                        $('#portalMotifPageSetTitXKfn .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetTitXKfn .text').val(hex);
                        $('#portalMotifPageSetTitXKfn .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });

                // tab选中字体颜色选择器
                $('#portalMotifPageSetTitXKxzf').colpick({
                    color: o.tabsfcolor || '333333',
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetTitXKxzf .text').val(hex);
                        $('#portalMotifPageSetTitXKxzf .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetTitXKxzf .text').val(hex);
                        $('#portalMotifPageSetTitXKxzf .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });

                // tab选中背景颜色选择器
                $('#portalMotifPageSetTitXKbg').colpick({
                    color: o.tabsbgcolor || '333333',
                    onChange: function (hsb, hex, rgb, el, bySetColor) {
                        $('#portalMotifPageSetTitXKbg .text').val(hex);
                        $('#portalMotifPageSetTitXKbg .bg').css('background-color', '#' + hex);
                    },
                    onSubmit: function (hsb, hex, rgb, el) {
                        $('#portalMotifPageSetTitXKbg .text').val(hex);
                        $('#portalMotifPageSetTitXKbg .bg').css('background-color', '#' + hex);
                        $(el).colpickHide();
                    },
                });
            }

            // 删除元素
            $(document)
                .off('click', '#portalMotifPageSetPZYSdel')
                .on('click', '#portalMotifPageSetPZYSdel', function (e) {
                    e.stopPropagation();
                    var index = $(this).attr('index');
                    if (index == 'pages' || index == 'time') {
                        for (var i in topData) {
                            if (topData[i].type == 8) {
                                topData.splice(i, 1);
                                delScreenFn(this);
                                break;
                            }
                            if (topData[i].type == -1 && topData[i].content == 'time') {
                                topData.splice(i, 1);
                                delScreenFn(this);
                                break;
                            }
                        }
                    } else {
                        var _i = $(this).parent().index();
                        var arr = dropData[index];
                        if (arr.length == 1) {
                            var dom = $(this).closest('.item');
                            dom.html('').removeClass('ha');
                            if (dom.hasClass('active') == true) {
                                dom.removeClass('active');
                                $('#portalMotifPageSetRightcon').html('');
                            }
                            dropData[index] = [];
                        } else {
                            $(this).closest('.portalMotifPageSetPZYStoolBox').remove();
                            arr.splice(_i, 1);
                            dropData[index] = arr;
                        }
                    }
                });

            // 删除筛选元素
            function delScreenFn(_this) {
                $(_this).closest('.portalMotifPageSetPZYStoolBox').remove();
                if (topData.length == 0) {
                    $('#portalMotifPageSetContent').css('top', '5px');
                    $('#portalMotifPageSetScreen').css('height', '0px');
                }
            }

            // 元素设置保存
            form.on('submit(portalMotifPageSetsxCofirm)', function (data) {
                var d = data.field;
                d.fontB = d.fontB == 'on' || d.fontB == 'true' ? true : false;
                d.fontI = d.fontI == 'on' || d.fontI == 'true' ? true : false;
                d.fontU = d.fontU == 'on' || d.fontU == 'true' ? true : false;

                var len = $('#portalMotifPageSetTitXKCon .xktt').length;
                var tab = []; // tab页签
                for (var i = 0; i < len; i++) {
                    var _d = {
                        title: $('#portalMotifPageSetTitXKConTit' + i).val(),
                        name: $('#portalMotifPageSetTitXKConSJY' + i + 'name').val(),
                        id: $('#portalMotifPageSetTitXKConSJY' + i + 'val').val(),
                        content: $('#portalMotifPageSetTitXKConSJY' + i + 'con').val(),
                    };
                    if (_d.name != '') {
                        tab.push(_d);
                    }
                }
                var index = $('#portalMotifPageSetContent > .active').attr('index');
                dropData[index].forEach(function (v, i) {
                    dropData[index][i].set = d;
                    dropData[index][i].tab = tab;
                });
                var dom = $('#portalMotifPageSetContent .active');
                dropData[index].forEach(function (v, i) {
                    if (i > 0) {
                        return false;
                    }
                    $.portal.init(elmType(v.type), dom, v, -1);
                });
            });

            // 保存
            form.on('submit(portalMotifPageSetAddCofirm)', function (data) {
                var d = data.field;
                var id = $('#hsbMotifAddId').val();
                var _d = {
                    data: dropData,
                    topData: topData, // 顶部筛选区域
                    layer: layersx, // 选中布局
                    layer_s: {
                        // 选中的布局名称
                        code: $('#portalMotifPageSetBjval').val(),
                        name: $('#portalMotifPageSetBjname').val(),
                    },
                };
                var data = {
                    templateId: $('#portalMotifPageSetMHID').val(),
                    templateName: $('#portalMotifPageSetMHname').val(),
                    terminalid: $('#portalMotifPageSetMHterminalid').val(),
                    terminalname: $('#portalMotifPageSetMHterminalid option:selected').text(),
                    cotent: JSON.stringify(_d),
                };
                // return
                $.ajax({
                    type: 'put',
                    url: common.url + '/ts-portal/api/portal/Aftintemplate/AftintemplateAddorUpdate',
                    data: data,
                    success: function (res) {
                        if (res.success) {
                            layer.closeAll();
                            layer.msg('操作成功');
                            opt.mTable.refresh();
                        } else {
                            layer.msg(res.message);
                        }
                    },
                    error: function (res) {
                        layer.msg('操作失败！');
                    },
                });
            });

            // 筛选模块点击
            $(document)
                .off('click', '#layoutmodule__screen .layoutmodule__screen')
                .on('click', '#layoutmodule__screen .layoutmodule__screen', function () {
                    var index = $(this).index();
                    var html = $('#portalMotifPageSetRight_sc').html();
                    // $('#portalMotifPageSetRightcon').html(html);
                    // $('#portalMotifPageSetContent .item').removeClass('active');
                });

            // ------ end
        });
    };
});
