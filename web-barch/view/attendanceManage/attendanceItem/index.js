"use strict";
define(function(require, exports, module) {
    var init = function() {
        return perform();
    }
    module.exports = {
        init: init
    }
    var perform = function() {
        layui.use(['form', 'laydate', 'trasen'], function() {
            var form = layui.form,
                layer = layui.layer,
                laydate = layui.laydate,
                trasen = layui.trasen;
            
            getEnumDataList(enum_attendance_item_type, "项目类型", "attendanceItemTypeListSel"); // 项目类型
            form.render("select");

            // 表格渲染
            var trasenTable = new $.trasenTable("grid-table-attendanceItemTable", {
                url: common.url + '/ts-hrms/salaryItem/list',
                pager: 'grid-pager-attendanceItemPager',
                shrinkToFit: true,
                postData: { dataCategory: 2 },
                colModel: [
                    { label: '数据类别', name: 'dataCategory', index: 'data_category', hidden: true },
                    { label: '项目名称', name: 'salaryItemName', index: 'salary_item_name', width: 150, editable: false, align: 'left' },
                    { label: '项目类型', name: 'salaryItemTypeText', index: 'salary_item_type', width: 100, editable: false, align: 'left' },
                    { label: '项目金额', name: 'salaryItemAmount', index: 'salary_item_amount', width: 100, editable: false, align: 'left' },
                    { label: '排序', name: 'serialNumber', index: 'serial_number', width: 100, editable: false, align: 'left' },
                    { label: 'ID', name: 'salaryItemId', hidden: true },
                    { label: '项目类型ID', name: 'salaryItemType', hidden: true },
                    { label: '考勤类型', name: 'attendanceType', hidden: true },
                    { label: '说明', name: 'remark', hidden: true }
                ],
                queryFormId: 'attendanceItemQueryForm'
            });

            // 表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            // 查询
            form.on('submit(attendanceItemSearch)', function(data) {
                refreshTable();
            });

            // 重置
            form.on('submit(attendanceItemReset)', function(data) {
                $("#attendanceItemQueryForm")[0].reset();
                form.render();
                refreshTable();
            });
            
            // 监听列表下拉选项查询变更事件
            form.on('select(selectChangeFilter)', function(data) {
            	refreshTable();
            });

            // 编辑考勤项目
            $("#attendanceItemDiv").off('click', '#attendanceItemTableEditor').on("click", "#attendanceItemTableEditor", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                $.quoteFun('attendanceManage/attendanceItem/modules/add', {
                    title: '编辑考勤项目',
                    data: rowData,
                    ref: refreshTable
                })

            });
            
            
            // 编辑考勤项目
            $("#attendanceItemDiv").off('click', '#attendanceItemTimekeeper').on("click", "#attendanceItemTimekeeper", function() {
            	$.quoteFun('attendanceManage/attendanceItem/modules/timekeeper', {
            		title: '考勤员设置'
            	})
            	
            });

            // 新增考勤项目
            $("#attendanceItemDiv").off("click", "#attendanceItemAdd").on("click", "#attendanceItemAdd", function() {
                $.quoteFun('attendanceManage/attendanceItem/modules/add', {
                    title: '新增考勤项目',
                    ref: refreshTable
                })
            });

            // 删除考勤项目
            $("#attendanceItemDiv").off("click", "#attendanceItemTableDel").on("click", "#attendanceItemTableDel", function() {
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.confirm(
                    '确定要删除该条记录吗？', 
                    {
                        btn: ['确定', '取消'],
                        title: '提示',
                        closeBtn: 0
                    }, 
                    function() {
                    $.ajax({
                        type: "post",
                        contentType: "application/json; charset=utf-8",
                        url: common.url + "/ts-hrms/salaryItem/deletedById/" + rowData.salaryItemId,
                        success: function(res) {
                            if (res.success) {
                                refreshTable();
                                layer.closeAll();
                            } else {
                                layer.closeAll();
                                layer.msg(res.message || '操作失败');
                            }
                        }
                    });
                }, function() {});
            });

        });
    }
});