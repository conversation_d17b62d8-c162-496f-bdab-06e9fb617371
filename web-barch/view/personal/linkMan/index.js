'use strict';

define(function (require, exports, module) {
  var init = function () {
    return perform();
  };
  module.exports = {
    init: init,
  };
  var firstPage = true;
  var pageNo = 1;
  var pageSize = 50;
  var perform = function () {
    layui.use(
      [
        'form',
        'layedit',
        'laydate',
        'trasen',
        'upload',
        'zTreeSearch',
        'laypage',
      ],
      function () {
        var form = layui.form,
          layer = layui.layer,
          layedit = layui.layedit,
          upload = layui.upload,
          laydate = layui.laydate,
          trasen = layui.trasen,
          laypage = layui.laypage,
          zTreeSearch = layui.zTreeSearch;
        if (common.globalSetting.orgCode == 'hnnkyy') {
          $('#linkManBasicListBox .oa-nav #allDuty').removeClass('none');
        } else {
          $('#linkManBasicListBox .oa-nav #allDuty').addClass('none');
        }
        $('#linkManBasicListBox .oa-nav .oa-nav_item').click(function () {
          // tabs 点击切换
          $('#linkManBasicListBox .oa-nav .oa-nav_item').removeClass('active');
          $(this).addClass('active');

          // 显示隐藏对应表单
          $('#linkManBasicListBox .table').hide();
          $('#linkManBasicListBox .table').eq($(this).index()).show();

          // 显示隐藏对应搜索form
          $('#linkManBasicListBox .queryForm').hide();
          $('#linkManBasicListBox .queryForm').eq($(this).index()).show();

          var index = $(this).attr('data-value');

          switch (index) {
            case '1':
              refresh();
              break;
            case '2':
              if (2 == common.globalSetting.orgContactType) {
                $('#deptAddressTreeObjectDiv').hide();
                $('#deptAddressForm').css({
                  position: 'absolute',
                  left: '0px',
                  top: '1px',
                });
                $('#deptAddressTableDiv').css({
                  position: 'absolute',
                  left: '0px',
                });
                initDeptAddressTable();
              } else {
                if (deptAddressTable) {
                  deptAddressTable.refresh();
                } else {
                  initDeptAddressTable();
                }
              }
              break;
            case '3':
              renderPersonalSystemGroupTree();
              break;
            case '4':
              renderTreeGroupList('render');
              break;
            case '5':
              if (linkManDataTable) {
                linkManDataTable.refresh();
              } else {
                initlinkManDataTable();
              }
              break;
            case '6':
              if (beOnDutyTable) {
                beOnDutyTable.refresh();
              } else {
                initBeOnDutyTable();
              }
              break;
            case '7':
              if (generalDutyTable) {
                generalDutyTable.refresh();
              } else {
                $('#linkManBasicListBox #dutyDate').val(formatDate());
                initGeneralDutyTable();
              }
              break;
          }
        });
        let innerLinkManDataTable = null;
        $('.indexInnerLinkManCard').removeClass('none');
        getInnerLinkManDataCard(true);
        // if(common.globalSetting.orgCode == 'hnnkyy' || common.globalSetting.orgCode == 'smzyy') {
        // } else {
        //   $('.indexInnerLinkManTable').removeClass('none');
        //   innerLinkManDataTable = new $.trasenTable('indexInnerLinkManTable', {
        //     url: common.url + '/ts-basics-bottom/employee/linkman/innerLinkManlist',
        //     pager: '#indexInnerLinkManPage',
        //     mtype: 'post',
        //     shrinkToFit: true,
        //     colModel: [
        //       {
        //         label: '员工姓名',
        //         name: 'empName',
        //         width: 120,
        //         align: 'left',
        //         sortable: false,
        //         // formatter: function (cellvalue, options, rowObject) {
        //         //   return cellvalue ? '<div style="color: #5260ff">' + cellvalue + '</div>' : ''
        //         // },
        //       },
        //       {
        //         label: '性别',
        //         name: 'empSex',
        //         width: 80,
        //         align: 'center',
        //         sortable: false,
        //         formatter: function (cellvalue, options, rowObject) {
        //           //性别  0：男  1：女
        //     if(cellvalue != null && cellvalue != ''){
        //       return cellvalue == 0 ? '男' : '女';
        //     }else{
        //       return '未知'
        //     }

        //         },
        //       },
        //       {
        //         label: '部门',
        //         sortable: false,
        //         name: 'empDeptName',
        //         width: 150,
        //         align: 'left',
        //       },
        //       {
        //         label: '电子邮件',
        //         sortable: false,
        //         name: 'empEmail',
        //         width: 150,
        //         align: 'center',
        //       },
        //       {
        //         label: '手机号码',
        //         sortable: false,
        //         name: 'empPhone',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: '手机号码2',
        //         sortable: false,
        //         name: 'empPhoneSecond',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: '座机号码',
        //         sortable: false,
        //         name: 'landlineNumber',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: '移动短号',
        //         sortable: false,
        //         name: 'empBusinessPhone',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: '电信短号',
        //         sortable: false,
        //         name: 'empTelecomBusinessPhone',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: '联通短号',
        //         sortable: false,
        //         name: 'empUnicomBusinessPhone',
        //         width: 120,
        //         align: 'center',
        //       },
        //       {
        //         label: 'id',
        //         sortable: false,
        //         name: 'id',
        //         width: 130,
        //         hidden: true,
        //       },
        //     ],
        //     buidQueryParams: function () {
        //       var search = $('#evaBaseInnerLinkManForm').serializeArray();
        //       var data = {};
        //       for (var i in search) {
        //         data[search[i].name] = search[i].value;
        //       }
        //       return data;
        //     },
        //   });
        // }
        // getInnerLinkManDataCard(true);
        function innerLinkManDataCardFun(count) {
          laypage.render({
            elem: 'innerLinkManDataCard_page',
            count,
            limit: pageSize,
            prev: '<',
            next: '>',
            first: '首页',
            last: '尾页',
            groups: 8,
            layout: ['prev', 'page', 'next'],
            jump: function (obj, first) {
              if (first == true) return;
              pageNo = obj.curr;
              getInnerLinkManDataCard(false);
            },
          });
        }
        function getInnerLinkManDataCard(boolean) {
          var search = $('#evaBaseInnerLinkManForm').serializeArray();
          var data = {};
          for (var i in search) {
            data[search[i].name] = search[i].value;
          }
          data.pageSize = pageSize;
          if (boolean) {
            pageNo = 1;
          }
          data.pageNo = pageNo;
          $('#innerLinkMan #load_indexInnerLinkManCard').removeClass('none');
          $.ajax({
            url:
              common.url +
              '/ts-basics-bottom/employee/linkman/innerLinkManlist',
            method: 'post',
            async: false,
            data,
            success: function (res) {
              $('#innerLinkMan #load_indexInnerLinkManCard').addClass('none');
              if (boolean) {
                innerLinkManDataCardFun(res.totalCount);
              }
              if (res.rows && res.rows.length > 0) {
                var htmls = '';
                for (var i = 0; i < res.rows.length; i++) {
                  let cellvalue = '';
                  if (res.rows[i].empSex != null && res.rows[i].empSex != '') {
                    cellvalue = res.rows[i].empSex == 0 ? '男' : '女';
                  } else {
                    cellvalue = '未知';
                  }
                  let avar = '';
                  if (res.rows[i].avatar != null && res.rows[i].empSex != '') {
                    avar = res.rows[i].avatar;
                  } else {
                    avar =
                      cellvalue == '男'
                        ? '/static/img/other/avar-man.png'
                        : '女'
                        ? '/static/img/other/avar-woman.png'
                        : '/static/img/other/avar-man.png';
                  }
                  htmls += `
                  <div class="card">
                    <div class="cardName">${res.rows[i].empName}</div>
                    <div class="cardContent">
                      <div class="cardAvar">
                        <img src="${avar}" />
                      </div>
                      <div class="cardCard">
                        <div class="label">${
                          res.rows[i].empDeptName || '--'
                        }</div>
                        <div class="label">性别: ${cellvalue}</div>
                        <div class="label">职务: ${
                          res.rows[i].positionName || '--'
                        }</div>
                        <div class="label">手机: ${
                          res.rows[i].empPhone || '--'
                        }</div>
                        <div class="label">短号：${
                          res.rows[i].empBusinessPhone || '--'
                        }</div>
                      </div>
                    </div>
                  </div>
                  `;
                }
                $('#innerLinkMan #indexInnerLinkManCard').html('');
                $('#innerLinkMan #indexInnerLinkManCard').html(htmls);
                $('#innerLinkMan #indexInnerLinkManCard').animate(
                  { scrollTop: '0' },
                  0
                );
              }
            },
          });
        }

        function refresh() {
          getInnerLinkManDataCard(true);
          // if(common.globalSetting.orgCode == 'hnnkyy'  || common.globalSetting.orgCode == 'smzyy') {
          // } else {
          //   innerLinkManDataTable.refresh();
          // }
        }

        $('#linkManBasicListBox #innerLinkManEvaBaseSearch').funs(
          'click',
          function () {
            pageNo = 1;
            getInnerLinkManDataCard(true);
            // if(common.globalSetting.orgCode == 'hnnkyy'  || common.globalSetting.orgCode == 'smzyy') {
            // } else {
            //   refresh();
            // }
          }
        );

        $('#linkManBasicListBox #resetInnerLinkMan').funs('click', function () {
          $('#evaBaseInnerLinkManForm')[0].reset();
          form.render();
          pageNo = 1;
          getInnerLinkManDataCard(true);
          // if(common.globalSetting.orgCode == 'hnnkyy'  || common.globalSetting.orgCode == 'smzyy') {
          // } else {
          //   refresh();
          // }
        });

        var deptTreeObj = {}; // 树对象
        /**@desc 获取所有子节点的id */
        function getAllChildDeptIds(node = {}, list = []) {
          node.id && list.push(node.id);
          if (node.children && node.children.length) {
            node.children.map((child) => {
              getAllChildDeptIds(child, list);
            });
          }
        }
        /**@desc 渲染科室树 绑定与搜索框的联动 */
        function renderInnerLinkManDeptTree(
          treeObj,
          treeNodeName,
          treeSearchInputName,
          searchVal,
          searchName,
          callBack,
          searchData = {}
        ) {
          let selNode =
            (deptTreeObj[treeObj] && deptTreeObj[treeObj].getSelectedNodes()) ||
            null;
          trasen.ztree(treeNodeName, {
            url: common.url + '/ts-basics-bottom/organization/getTree',
            type: 'post',
            checkbox: false,
            condition: 'name',
            data: searchData,
            zTreeAsyncSuccess: function (tree) {
              deptTreeObj[treeObj] = tree;
              if (selNode) {
                tree.selectNode(selNode);
              } else {
                let nodes = tree.getNodes();
                tree.selectNode(nodes[0]);
              }

              /**@desc 科室树搜索 */
              $(treeSearchInputName).bind('input', function (e) {
                let val = e.target.value || '';

                tree && tree.checkAllNodes(false);
                tree && $.fn.ztreeQueryHandler(tree, val);
                if (!val) {
                  let nodes = tree.getNodes() || [];
                  tree.expandAll(false);
                  nodes.length && tree.expandNode(nodes[0], true);
                }
              });
            },
            zTreeOnClick: function (treeId, treeNode) {
              try {
                let tree = deptTreeObj[treeObj],
                  selectedNodes = tree.getSelectedNodes() || [],
                  selectedNode = selectedNodes[0] || {},
                  { id, name } = selectedNode,
                  ids = [];
                getAllChildDeptIds(selectedNode, ids);

                $(searchVal).val(ids.join(','));
                $(searchName).val(name);
                callBack();
              } catch (e) {
                console.log(e);
              }
            },
          });

          $(searchName).bind('input', function () {
            $(searchVal).val('');
            let ztree = deptTreeObj[treeObj];
            if (!ztree) return;
            let nodes = ztree.getSelectedNodes() || [];
            nodes.length && ztree.cancelSelectedNode(nodes[0]);
          });
        }
        renderInnerLinkManDeptTree(
          'innerLinkMan',
          '#innerLinkManTreeObject',
          '#innerLinkMan .oa-search-tree input',
          '#evaBaseInnerLinkManForm [name="orgId"]',
          '#evaBaseInnerLinkManForm [name="orgName"]',
          refresh
        );
        renderInnerLinkManDeptTree(
          'deptAddress',
          '#deptAddressTreeObject',
          '#deptAddress .oa-search-tree input',
          '#deptAddressForm [name="orgId"]',
          '#deptAddressForm [name="orgName"]',
          () => {
            deptAddressTable && deptAddressTable.refresh();
          }
        );

        // 个人系统群组
        let searchPersonalSystemGroup = [];
        let personalSystemGroupList = [];
        let PersonalSystemGroupTable = null;

        function renderPersonalSystemGroupTree() {
          $.ajax({
            url: '/ts-basics-bottom/employee/orgGroup/listCurrentEmpOrgGroup',
            method: 'post',
            async: false,
            data: JSON.stringify({ groupName: '' }),
            contentType: 'application/json; charset=utf-8',
            success: function (res) {
              if (res.success && res.statusCode === 200) {
                searchPersonalSystemGroup = res.object;
                personalSystemGroupList = $.extend([], res.object);
                renderPersonalSystemGroupHtml(personalSystemGroupList);

                // 渲染个人群组成员
                renderPersonalSystemGroupMemberHandle();
              } else {
                $('#linkManBasicListBox #PersonalSystemGroupList').html('');
              }
            },
          });
        }

        function renderPersonalSystemGroupHtml(arr) {
          let str = '';
          $('#linkManBasicListBox #PersonalSystemGroupList').html('');
          arr.forEach((item, index) => {
            let isActive = false;

            if (index === 0) {
              isActive = true;
              $('#linkManBasicListBox #ActivePersonalSystemGroupVal').val(
                item.groupId
              );
            }

            str += `
                    <li data-groupId=${item.groupId}>
                      <span class=${isActive ? 'active' : ''}>${
              item.groupName
            }</span>
                    </li>`;
          });
          $('#linkManBasicListBox #PersonalSystemGroupList').html(str);
        }

        function renderPersonalSystemGroupMemberHandle() {
          $('#PersonalSystemGroupTable').remove();
          $('#PersonalSystemGroupPager').html('');
          $('#PersonalSystemGroupTableBox').append(
            "<table id='PersonalSystemGroupTable'></table>"
          ); //再新增一个grid的渲染容器

          PersonalSystemGroupTable = new $.trasenTable(
            'PersonalSystemGroupTable',
            {
              url:
                common.url +
                `/ts-basics-bottom/employee/orgGroup/listPageOrgGroupEmp`,
              pager: 'PersonalSystemGroupPager',
              rowNum: 100,
              shrinkToFit: true,
              multiselect: false,
              sortname: 'a1.create_date',
              postData: {
                groupId:
                  $(
                    '#linkManBasicListBox #ActivePersonalSystemGroupVal'
                  ).val() || '',
                empName:
                  $('#linkManBasicListBox #PersonalSystemGroupEmpName').val() ||
                  '',
              },
              colModel: [
                {
                  label: '群组',
                  name: 'groupName',
                  sortable: false,
                  align: 'center',
                  width: 100,
                  fixed: true,
                },
                {
                  label: '工号',
                  name: 'empCode',
                  sortable: false,
                  align: 'center',
                  width: 110,
                  fixed: true,
                },
                {
                  label: '姓名',
                  name: 'empName',
                  sortable: false,
                  align: 'center',
                  width: 90,
                  fixed: true,
                },
                {
                  label: '手机号',
                  name: 'empMobile',
                  sortable: false,
                  align: 'center',
                },
                {
                  label: '组织机构',
                  name: 'orgName',
                  sortable: false,
                  align: 'center',
                },
                {
                  label: '岗位',
                  name: 'positionName',
                  sortable: false,
                  align: 'center',
                },
                {
                  label: '职务',
                  name: 'personalIdentityName',
                  sortable: false,
                  align: 'center',
                },
              ],
            }
          );
        }

        $('#linkManBasicListBox #PersonalSystemGroup')
          .off('click', '#PersonalSystemGroupSearchBtn')
          .on('click', '#PersonalSystemGroupSearchBtn', function (e) {
            renderPersonalSystemGroupMemberHandle();
          });

        $('#linkManBasicListBox #PersonalSystemGroup')
          .off('click', '#PersonalSystemGroupReset')
          .on('click', '#PersonalSystemGroupReset', function (e) {
            $('#linkManBasicListBox #PersonalSystemGroupEmpName').val('');
            renderPersonalSystemGroupMemberHandle();
          });

        $('#linkManBasicListBox #PersonalSystemGroup')
          .off('input', '#PersonalSystemGroupSearch')
          .on('input', '#PersonalSystemGroupSearch', function (e) {
            const value = e.target.value;
            if (value === '') {
              renderPersonalSystemGroupHtml(personalSystemGroupList);
            } else {
              var filterPersonalSystemGroupNameArr =
                searchPersonalSystemGroup.filter((item) => {
                  if (item['groupName'].indexOf(value) >= 0) return item;
                });
              renderPersonalSystemGroupHtml(filterPersonalSystemGroupNameArr);
            }
          });

        $('#linkManBasicListBox #PersonalSystemGroupList')
          .off('click', 'li span')
          .on('click', 'li span', function (e) {
            $(this)
              .parent()
              .parent()
              .children()
              .each((index, item) => {
                $(item).find('span').removeClass('active');
              });
            $(this).addClass('active');
            $('#linkManBasicListBox #PersonalSystemGroupEmpName').val('');

            const groupId = $(this).parent().data('groupid');
            $('#linkManBasicListBox #ActivePersonalSystemGroupVal').val(
              groupId
            );

            renderPersonalSystemGroupMemberHandle();
          });

        // 个人群组
        let PersonalGroupTable = null;
        let personalGroupList = [];
        let searchPersonalGroupList = [];

        function renderTreeGroupList(type) {
          $.ajax({
            url: '/ts-basics-bottom/employee/orgGroup/list',
            method: 'post',
            async: false,
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            data: {
              groupType: '1',
              pageNo: '1',
              pageSize: 9999,
              sidx: 'create_date',
              sord: 'desc',
            },
            success: function (res) {
              if (res.rows && res.rows.length > 0) {
                searchPersonalGroupList = res.rows;
                personalGroupList = $.extend([], res.rows);
                renderPersonalGroupHtml(personalGroupList, type);

                addDragetHandle();
                // 渲染群组成员
                renderPersonalGroupListHandle();
              } else {
                searchPersonalGroupList = [];
                personalGroupList = [];
                $('#linkManBasicListBox #personalGroupList').html('');

                $('#PersonalGroupTable').remove();
                $('#PersonalGroupTableBox').append(
                  "<table id='PersonalGroupTable'></table>"
                );
              }
            },
          });
        }

        // 添加拖拽方法
        function addDragetHandle() {
          const homeBlock = $(
            '#linkManBasicListBox #PersonalGroup #personalGroupList li'
          );
          // 拖拽最后 在那个dom上
          let startDrageDom = {};
          homeBlock.each((index, item) => {
            item.ondragstart = function (event) {
              let dom = $(event.target).closest('li');
              startDrageDom.dom = dom;
            };
            // 拖拽 触碰到的盒子 获取dom和sord
            item.ondragenter = function (event) {
              let dom = $(event.target).closest('li');
              dom.after(startDrageDom.dom);
            };
            // 拖拽结束
            item.ondragend = function (event) {
              let arr = [];
              $('#linkManBasicListBox #PersonalGroup #personalGroupList')
                .children()
                .each((groupOrder, item) => {
                  arr.push({
                    groupId: $(item).data('groupid'),
                    groupOrder,
                  });
                });

              $.ajax({
                type: 'post',
                url:
                  common.url + '/ts-basics-bottom/employee/orgGroup/updateSort',
                dateType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(arr),
                success: function (res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    layer.msg('操作成功');
                    renderTreeGroupList('render');
                  } else {
                    layer.msg(res.message || '操作失败');
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res || '操作失败');
                },
              });
            };
          });
        }

        // 个人群组左侧 群组数据
        function renderPersonalGroupHtml(arr, type) {
          let str = '';
          $('#linkManBasicListBox #personalGroupList').html('');
          arr.forEach((item, index) => {
            let isActive = false;
            const groupId = $(
              '#linkManBasicListBox #ActivePersonalGroupVal'
            ).val();
            if (type === 'render') {
              if (index === 0) {
                isActive = true;
                $('#linkManBasicListBox #ActivePersonalGroupVal').val(
                  item.groupId
                );
              }
            } else {
              isActive = item.groupId === groupId;
            }

            str += `
                    <li draggable='true' data-groupId=${item.groupId}>
                      <span class=${isActive ? 'active' : ''}>${
              item.groupName
            }</span>
                      <div data-groupId=${item.groupId}>
                        <img title="编辑" id="personGroupEditBtn" src="/static/img/other/systemGroupEdit.svg" alt="">
                        <img title="删除" id="personGroupDelBtn" src="/static/img/other/systemGroupDel.svg" alt="">
                      </div>
                    </li>`;
          });
          $('#linkManBasicListBox #personalGroupList').html(str);
        }

        function renderPersonalGroupListHandle() {
          $('#PersonalGroupTable').remove();
          $('#PersonalGroupTableBox').append(
            "<table id='PersonalGroupTable'></table>"
          ); //再新增一个grid的渲染容器

          PersonalGroupTable = new $.trasenTable('PersonalGroupTable', {
            url:
              common.url +
              `/ts-basics-bottom/employee/orgGroup/listPageOrgGroupEmp`,
            rowNum: 1000,
            shrinkToFit: true,
            multiselect: false,
            sortname: 'a1.create_date',
            jsonReader: {
              id: 'empCode',
            },
            postData: {
              groupId:
                $('#linkManBasicListBox #ActivePersonalGroupVal').val() || '',
              empName:
                $('#linkManBasicListBox #PersonalGroupEmpName').val() || '',
            },
            colModel: [
              {
                label: '群组',
                name: 'groupName',
                sortable: false,
                align: 'center',
                width: 100,
                fixed: true,
              },
              {
                label: '工号',
                name: 'empCode',
                sortable: false,
                align: 'center',
                width: 110,
                fixed: true,
              },
              {
                label: '姓名',
                name: 'empName',
                sortable: false,
                align: 'center',
                width: 90,
                fixed: true,
              },
              {
                label: '手机号',
                name: 'empMobile',
                sortable: false,
                align: 'center',
              },
              {
                label: '组织机构',
                name: 'orgName',
                sortable: false,
                align: 'center',
              },
              {
                label: '岗位',
                name: 'positionName',
                sortable: false,
                align: 'center',
              },
              {
                label: '职务',
                name: 'personalIdentityName',
                sortable: false,
                align: 'center',
              },
              {
                label:
                  '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                align: 'center',
                classes: 'visible jqgrid-rownum ui-state-default',
                name: 'opt',
                width: 60,
                fixed: true,
                title: false,
                formatter: function (cellvalue, options, row) {
                  return `<a 
                        href="javascript:;" 
                        style="color: #5260FF" 
                        id="RemoveMemberBtn"
                        data-empCode="${row.empCode}"
                        data-groupId="${row.groupId}"
                        >
                          移除
                        </a>`;
                },
              },
            ],
            gridComplete: function () {
              let that = this;
              $(this).jqGrid('sortableRows', {
                update: function () {
                  let arr = [];
                  $.each(
                    $(that).find('tr.jqgrow[role="row"]'),
                    function (i, v) {
                      arr.push({
                        userId: $(this).attr('id'),
                        sort: i,
                        groupId:
                          $(
                            '#linkManBasicListBox #ActivePersonalGroupVal'
                          ).val() || '',
                      });
                    }
                  );
                  $.ajax({
                    url: '/ts-basics-bottom/employee/orgGroup/updateUserGroupSort',
                    method: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify(arr),
                    success: function (res) {
                      if (res.success) {
                        renderPersonalGroupListHandle();
                      }
                    },
                  });
                },
              });
            },
          });
        }

        // 个人群组 移除成员
        $('#PersonalGroupTableBox')
          .off('click', '#RemoveMemberBtn')
          .on('click', '#RemoveMemberBtn', function (e) {
            const empCode = $(this).data('empcode');
            const groupId = $(this).data('groupid');
            $.loadings();

            $.ajax({
              type: 'post',
              url:
                common.url +
                '/ts-basics-bottom/employee/orgGroup/deleteGroupEmp',
              dateType: 'json',
              contentType: 'application/x-www-form-urlencoded',
              data: {
                empCode,
                groupId,
              },
              success: function (res) {
                $.closeloadings();
                if (res.success) {
                  layer.closeAll();
                  layer.msg('操作成功');
                  renderPersonalGroupListHandle();
                  renderTreeGroupList();
                } else {
                  layer.msg(res.message || '操作失败');
                }
              },
              error: function (res) {
                res = JSON.parse(res.responseText);
                layer.msg(res || '操作失败');
              },
            });
          });

        // 个人群组 管理成员
        $('#linkManBasicListBox #PersonalGroup')
          .off('click', '#personGroupAdditem')
          .on('click', '#personGroupAdditem', function (e) {
            const activeGroup = personalGroupList.find(
              (item) =>
                item.groupId ===
                $('#linkManBasicListBox #ActivePersonalGroupVal').val()
            );

            const groupUserNamesArr = activeGroup.groupUserNames
              ? activeGroup.groupUserNames.split(',')
              : [];
            const groupUserStringArr = activeGroup.groupUserString
              ? activeGroup.groupUserString.split(',')
              : [];

            if (groupUserNamesArr.length === groupUserStringArr.length) {
              let userList = [];
              for (let i = 0; i < groupUserStringArr.length; i++) {
                userList.push({
                  name: groupUserNamesArr[i],
                  code: groupUserStringArr[i],
                });
              }

              $.quoteFun('/commonPage/userDeptGroup/index', {
                handClose: true,
                data: {
                  user: true,
                  dept: true,
                  userList,
                },
                callBack: function (list, seluser, dept, wins) {
                  var codes = [];
                  var names = [];
                  for (var i = 0; i < seluser.length; i++) {
                    codes.push(seluser[i].code);
                    names.push(seluser[i].name);
                  }
                  if (!codes.length || !names.length) {
                    layer.msg('员工不能为空');
                    return false;
                  }
                  layer.close(wins);
                  $.ajax({
                    url: '/ts-basics-bottom/employee/orgGroup/update',
                    method: 'post',
                    contentType: 'application/json',
                    data: JSON.stringify({
                      groupUserNames: names.join(',') || '',
                      groupUserString: codes.join(',') || '',
                      groupType: 1,
                      groupId: $(
                        '#linkManBasicListBox #ActivePersonalGroupVal'
                      ).val(),
                      groupName: activeGroup.groupName,
                    }),
                    success: function (res) {
                      if (res.success) {
                        layer.msg('操作成功');
                        renderTreeGroupList();
                      } else {
                        layer.msg(res.message || '操作失败');
                      }
                    },
                  });
                },
              });
            }
          });

        $('#linkManBasicListBox #PersonalGroup')
          .off('click', '#PersonalGroupSearchBtn')
          .on('click', '#PersonalGroupSearchBtn', function (e) {
            renderPersonalGroupListHandle();
          });

        $('#linkManBasicListBox #PersonalGroup')
          .off('click', '#PersonalGroupReset')
          .on('click', '#PersonalGroupReset', function (e) {
            $('#linkManBasicListBox #PersonalGroupEmpName').val('');
            renderPersonalGroupListHandle();
          });

        // 系统群组 搜索
        $('#linkManBasicListBox #PersonalGroup')
          .off('input', '#PersonalGroupSearch')
          .on('input', '#PersonalGroupSearch', function (e) {
            const value = e.target.value;
            if (value === '') {
              renderPersonalGroupHtml(personalGroupList);
            } else {
              var filterGroupNameArr = searchPersonalGroupList.filter(
                (item) => {
                  if (item['groupName'].indexOf(value) >= 0) return item;
                }
              );
              renderPersonalGroupHtml(filterGroupNameArr);
            }
          });

        // 个人群组 行点击
        $('#linkManBasicListBox #personalGroupList')
          .off('click', 'li span')
          .on('click', 'li span', function (e) {
            $(this)
              .parent()
              .parent()
              .children()
              .each((index, item) => {
                $(item).find('span').removeClass('active');
              });
            $(this).addClass('active');
            $('#linkManBasicListBox #PersonalGroupEmpName').val('');

            const groupId = $(this).parent().find('div').data('groupid');
            $('#linkManBasicListBox #ActivePersonalGroupVal').val(groupId);

            renderPersonalGroupListHandle();
          });

        //群组分类
        // $('#linkManBasicListBox #customGroupsClass').funs('click', function () {
        //   $.quoteFun('/personal/personalSettings/groupsClassIndex', {
        //     trasen: customGroupsTable,
        //     title: '群组分类',
        //   });
        // });

        //新增群组
        $('#linkManBasicListBox #personGroupAddBtn').funs('click', function () {
          $.quoteFun('/personal/personalSettings/addCustomGroups', {
            title: '新增群组',
            data: {
              personalGroupList,
            },
            ref: renderTreeGroupList,
          });
        });

        //编辑群组
        $('#linkManBasicListBox #personGroupEditBtn').funs(
          'click',
          function () {
            const groupId = $(this).parent().data('groupid');
            const data = personalGroupList.filter(
              (item) => item.groupId === groupId
            )[0];

            $.quoteFun('/personal/personalSettings/addCustomGroups', {
              title: '编辑群组',
              data,
              ref: renderTreeGroupList,
            });
          }
        );

        //个人群组 删除
        $('#linkManBasicListBox #personGroupDelBtn').funs('click', function () {
          const groupId = $(this).parent().data('groupid');

          layer.confirm(
            '删除后将无法恢复，确定要删除该群组吗？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              $.loadings();

              $.ajax({
                type: 'post',
                url:
                  common.url +
                  '/ts-basics-bottom/employee/orgGroup/deletedById',
                dateType: 'json',
                contentType: 'application/json',
                data: JSON.stringify({
                  groupId,
                }),
                success: function (res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    layer.msg('操作成功');
                    renderTreeGroupList('render');
                  } else {
                    layer.msg(res.message || '操作失败');
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res || '操作失败');
                },
              });
            }
          );
        });

        // 外部联系人
        var linkManDataTable;

        function initlinkManDataTable() {
          linkManDataTable = new $.trasenTable('indexLinkManTable', {
            url: common.url + '/ts-basics-bottom/employee/linkman/list',
            pager: '#indexLinkManPage',
            mtype: 'post',
            shrinkToFit: true,
            colModel: [
              {
                label: '姓名',
                sortable: false,
                name: 'linkmanName',
                width: 110,
                align: 'center',
                formatter: (cellvalue) =>
                  `<div style="color: #5260ff">${cellvalue}</div>`,
              },
              {
                label: '性别',
                sortable: false,
                name: 'linkmanSex',
                width: 50,
                align: 'center',
                formatter: function (cellvalue, options, rowObject) {
                  //性别  0：男  1：女
                  if (cellvalue != null && cellvalue != '') {
                    return cellvalue == 0 ? '男' : '女';
                  } else {
                    return '未知';
                  }
                },
              },
              {
                label: '单位',
                sortable: false,
                name: 'linkmanUnit',
                width: 120,
                align: 'center',
              },
              {
                label: '部门',
                sortable: false,
                name: 'linkmanDepart',
                width: 120,
                align: 'center',
              },
              {
                label: '职务',
                sortable: false,
                name: 'linkmanDuty',
                width: 120,
                align: 'center',
              },
              {
                label: '岗位',
                sortable: false,
                name: 'linkmanProfession',
                width: 100,
                align: 'center',
              },
              {
                label: '联系方式',
                sortable: false,
                name: 'mobilePhone',
                width: 120,
                align: 'center',
              },
              {
                label: '电子邮件',
                sortable: false,
                name: 'linkmanEmail',
                width: 150,
                align: 'center',
              },
              {
                label: '备注',
                sortable: false,
                name: 'linkmanDescribe',
                width: 150,
                align: 'center',
              },
              {
                label:
                  '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
                name: '',
                sortable: false,
                width: 40,
                editable: false,
                title: false,
                align: 'center',
                classes: 'visible jqgrid-rownum ui-state-default',
                formatter: function (cellvalue, options, row) {
                  var html =
                    '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                  var btns = '';
                  btns +=
                    '<button class="layui-btn " id="editEvaBaseLinkMan"  title="编辑" row-id="' +
                    options.rowId +
                    '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                  btns +=
                    '<button class="layui-btn " id="delEvaBaseLinkMan"  title="删除" row-id="' +
                    options.rowId +
                    '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                  html += btns + '</div></div>';
                  return html;
                },
              },
              {
                label: 'id',
                sortable: false,
                name: 'id',
                width: 10,
                hidden: true,
              },
            ],
            buidQueryParams: function () {
              var search = $('#evaBaseLinkManForm').serializeArray();
              var data = {};
              for (var i in search) {
                data[search[i].name] = search[i].value;
              }
              return data;
            },
          });
        }

        function linkManRefresh() {
          linkManDataTable.refresh();
        }

        //外部联系人搜索
        $('#EvaBaseSearchLinkMan,#EvaBaseSearchLinkManScreen').funs(
          'click',
          function () {
            linkManRefresh();
          }
        );

        //重置外部联系人
        $('#resetLinkMan,#resetLinkManScreen').funs('click', function () {
          $('#evaBaseLinkManForm')[0].reset();
          form.render();
          linkManRefresh();
        });

        // 新增外部联系人
        $('#linkManBasicListBox #addLinkMan').funs('click', function () {
          $.quoteFun('/personal/linkMan/add', {
            trasen: linkManDataTable,
            title: '新增外部联系人',
            ref: linkManRefresh,
          });
        });

        //编辑外部联系人
        $('#editEvaBaseLinkMan').funs('click', function () {
          var data = linkManDataTable.getSourceRowData(); // 选中行数据
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          $.quoteFun('/personal/linkMan/add', {
            trasen: linkManDataTable,
            title: '编辑外部联系人',
            data: data,
            ref: linkManRefresh,
          });
        });

        //删除外部联系人
        $('#delEvaBaseLinkMan').funs('click', function () {
          var data = linkManDataTable.getSourceRowData(); // 选中行数据
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          var d = {
            id: data.id,
          };
          layer.confirm(
            '确定删除该外部联系人吗?',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              $.ajax({
                type: 'post',
                url:
                  common.url + '/ts-basics-bottom/employee/linkman/deletedById',
                dateType: 'json',
                contentType: 'application/json',
                data: JSON.stringify(d),
                success: function (res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    trasen.info('操作成功');
                    linkManRefresh();
                  } else {
                    layer.closeAll();
                    layer.msg(res.message);
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message);
                },
              });
            }
          );
        });

        //外部联系人导出
        $('#expotLinkMan').funs('click', function () {
          layer.confirm(
            '您确定导出信息？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              var linkmanName = $('#linkmanName').val();
              var linkmanUnit = $('#linkmanUnit').val();
              var linkmanDepart = $('#linkmanDepart').val();
              //window.open(common.url + '/ts-basics-bottom/employee/excel/expotEmployee?linkmanName=' + linkmanName + '&linkmanUnit=' + linkmanUnit+ '&linkmanDepart=' + linkmanDepart);
              window.location.href =
                common.url +
                '/ts-basics-bottom/employee/excel/expotLinkMan?linkmanName=' +
                linkmanName +
                '&linkmanUnit=' +
                linkmanUnit +
                '&linkmanDepart=' +
                linkmanDepart;
              layer.closeAll();
              layer.close(index);
            }
          );
        });

        //外部联系人导入
        $('#importLinkMan').funs('click', function () {
          $.quoteFun('/personal/linkMan/import', {
            trasen: linkManDataTable,
            title: '外部联系人导入',
            ref: linkManRefresh,
          });
        });

        //内部联系人导出
        // $('#expotInnerLinkMan').funs('click', function () {
        //     layer.confirm(
        //         '您确定导出信息？',
        //         {
        //             title: '提示', btn: ['确定', '取消']
        //         },
        //         function (index) {
        //             var employeeName = $('#employeeName').val();
        //             var orgName = $('#orgName').val();
        //             var empSex = $('#empSex').val();
        //             //window.open(common.url + '/ts-basics-bottom/employee/excel/expotInnerLinkMan?empName=' + empName + '&empPhone=' + empPhone);
        //             window.location.href = common.url + '/ts-basics-bottom/employee/excel/expotInnerLinkMan?employeeName=' + employeeName + '&orgName=' + orgName + '&empSex=' + empSex;
        //             layer.closeAll();
        //             layer.close(index);
        //         }
        //     );
        // });

        var isDeptAdressAdmin =
          common.userInfo.sysRoleCode &&
          common.userInfo.sysRoleCode.indexOf('CONTACTS-CRUD') !== -1;
        if (!isDeptAdressAdmin && common.userInfo.usercode != 'admin') {
          $('#deptAddress #deptAddressAdd').hide();
          $('#deptAddress #deptAddressImport').hide();
        }
        let deptAddressTable;
        // 科室通讯录
        function initDeptAddressTable() {
          let colModel = [
            {
              label: '科室名称',
              sortable: false,
              name: 'orgName',
              align: 'center',
              width: 80,
            },
            {
              label: '联系科室（人）',
              sortable: false,
              name: 'name',
              align: 'center',
              width: 80,
            },
            {
              label: '电话',
              sortable: false,
              name: 'tel',
              align: 'center',
              autowidth: true,
            },
          ];
          if (isDeptAdressAdmin || common.userInfo.usercode == 'admin') {
            colModel.push({
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              title: false,
              align: 'center',
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cellvalue, options, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                btns +=
                  '<button class="layui-btn " id="editDeptAddress"  title="编辑" row-id="' +
                  options.rowId +
                  '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                btns +=
                  '<button class="layui-btn " id="delDeptAddress"  title="删除" row-id="' +
                  options.rowId +
                  '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                html += btns + '</div></div>';
                return html;
              },
            });
          }
          deptAddressTable = new $.trasenTable('deptAddressTable', {
            // url: common.url + '/ts-basics-bottom/organization/getAllOrgList',
            url: '/ts-basics-bottom/organizationContacts/list',
            pager: '#deptAddressPage',
            mtype: 'get',
            shrinkToFit: true,
            colModel: colModel,
            buidQueryParams: function () {
              var search = $('#deptAddressForm').serializeArray();
              var data = {};
              for (var i in search) {
                data[search[i].name] = search[i].value;
              }
              return data;
            },
          });
        }

        function deptAddressRefresh() {
          deptAddressTable.refresh();
        }

        $('#linkManBasicListBox #deptAddressSearch').funs('click', function () {
          deptAddressRefresh();
        });
        $('#linkManBasicListBox #resetdeptAddressSearch').funs(
          'click',
          function () {
            $('#deptAddressForm')[0].reset();
            form.render();
            deptAddressRefresh();
          }
        );

        //新增科室通讯录
        $('#linkManBasicListBox #deptAddressAdd').funs('click', function () {
          $.quoteFun('/personal/linkMan/addDeptAddress', {
            trasen: deptAddressTable,
            title: '新增',
            ref: deptAddressRefresh,
          });
        });

        //编辑科室通讯录
        $('#editDeptAddress').funs('click', function () {
          var data = deptAddressTable.getSourceRowData(); // 选中行数据
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          $.quoteFun('/personal/linkMan/addDeptAddress', {
            trasen: deptAddressTable,
            title: '编辑外部联系人',
            data: data,
            ref: deptAddressRefresh,
          });
        });

        //删除科室通讯录
        $('#delDeptAddress').funs('click', function () {
          var data = deptAddressTable.getSourceRowData(); // 选中行数据
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          layer.confirm(
            '确定删除该科室电话吗?',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              $.ajax({
                type: 'post',
                url: '/ts-basics-bottom/organizationContacts/delete/' + data.id,
                dateType: 'json',
                contentType: 'application/json',
                success: function (res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    trasen.info('操作成功');
                    deptAddressRefresh();
                  } else {
                    layer.closeAll();
                    layer.msg(res.message);
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message);
                },
              });
            }
          );
        });

        //科室排班导入
        $('#deptAddressImport').funs('click', function () {
          $.quoteFun('/personal/linkMan/importDeptAddress', {
            trasen: deptAddressTable,
            title: '导入',
            ref: deptAddressRefresh,
          });
        });

        //分类管理
        // $('#linkmanclass').funs('click', function () {
        //   $.quoteFun('/personal/linkMan/classIndex', {
        //     trasen: linkManDataTable,
        //     title: '分类管理',
        //     ref: linkManRefresh,
        //   });
        // });

        let isZbgly =
          common.userInfo.sysRoleCode &&
          common.userInfo.sysRoleCode.indexOf('zbgly') !== -1;
        if (!isZbgly && common.userInfo.usercode != 'admin') {
          $('#linkManBasicListBox #beOnDutyAdd').hide();
          $('#linkManBasicListBox #beOnDutyExport').hide();
          $('#linkManBasicListBox #beOnDutyImport').hide();
        }

        //科室值班通讯录
        laydate.render({
          elem: '#searchBeOnDutyTime',
          showBottom: true,
          type: 'datetime',
          format: 'yyyy-MM-dd HH:mm',
          range: '~',
          showBottom: true,
          done: function (value, date, endDate) {
            var dateArr = value.split(' ~ ');
            $('#linkManBasicListBox #starttime').val(dateArr[0]);
            $('#linkManBasicListBox #endtime').val(dateArr[1]);
          },
        });

        var beOnDutyTable = null;
        function initBeOnDutyTable() {
          let colModel = [
            {
              label: '部门/科室',
              name: 'deptName',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '值班人',
              name: 'dutyPerson',
              width: 80,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                return (
                  '<span style="color: #02a7f0;cursor:pointer;" id="beOnDutyItemDetails" id ="' +
                  rowObject.id +
                  '">' +
                  cellvalue +
                  '</span>'
                );
              },
            },
            {
              label: '值班时间',
              sortable: false,
              name: 'empDeptName',
              width: 300,
              align: 'center',
              formatter: function (cellvalue, options, row) {
                let starttime = row.starttime;
                let endtime = row.endtime;
                return `${starttime} ~ ${endtime}`;
              },
            },
            {
              label: '值班电话',
              sortable: false,
              name: 'telephone',
              width: 150,
              align: 'center',
            },
            {
              label: '值班地点',
              sortable: false,
              name: 'place',
              width: 150,
              align: 'center',
            },
            {
              label: '值班领导',
              sortable: false,
              name: 'leaderPerson',
              width: 80,
              align: 'center',
            },
            {
              label: '值班领导电话',
              sortable: false,
              name: 'leaderTelephone',
              width: 120,
              align: 'center',
            },
            {
              label: 'id',
              sortable: false,
              name: 'id',
              width: 130,
              hidden: true,
            },
          ];
          if (isZbgly || common.userInfo.usercode == 'admin') {
            colModel.push({
              label:
                '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
              name: '',
              sortable: false,
              width: 40,
              editable: false,
              title: false,
              align: 'center',
              classes: 'visible jqgrid-rownum ui-state-default',
              formatter: function (cellvalue, options, row) {
                var html =
                  '<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">';
                var btns = '';
                btns +=
                  '<button class="layui-btn " id="beOnDutyEdit"  title="编辑" row-id="' +
                  options.rowId +
                  '"> <i class="fa fa-pencil-square-o deal_icon" aria-hidden="true"></i>编辑 </button>';
                btns +=
                  '<button class="layui-btn " id="beOnDutyDel"  title="删除" row-id="' +
                  options.rowId +
                  '"> <i class="fa fa-trash deal_icon" aria-hidden="true"></i>删除 </button>';
                html += btns + '</div></div>';
                return html;
              },
            });
          }
          beOnDutyTable = new $.trasenTable('beonDutyTable', {
            url: common.url + '/ts-oa/api/dutyAddressBook/list',
            pager: '#beonDutyPage',
            mtype: 'GET',
            shrinkToFit: true,
            colModel: colModel,
            buidQueryParams: function () {
              var search = $('#beOnDutySearchForm').serializeArray();
              var data = {};
              for (var i in search) {
                data[search[i].name] = search[i].value;
              }
              return data;
            },
          });
        }

        function beOnDutyTableRefresh() {
          beOnDutyTable.refresh();
        }

        $('#linkManBasicListBox #beOnDutySearch').funs('click', function () {
          beOnDutyTableRefresh();
        });

        $('#linkManBasicListBox #beOnDutyReset').funs('click', function () {
          $('#beOnDutySearchForm')[0].reset();
          $('#linkManBasicListBox #starttime').val('');
          $('#linkManBasicListBox #endtime').val('');
          form.render();
          beOnDutyTableRefresh();
        });

        $('#linkManBasicListBox #beOnDutyAdd').funs('click', function () {
          $.quoteFun('/personal/linkMan/addBeOnDuty', {
            trasen: beOnDutyTable,
            title: '新增',
            ref: beOnDutyTableRefresh,
          });
        });

        $('#linkManBasicListBox #beOnDutyItemDetails').funs(
          'click',
          function () {
            var data = beOnDutyTable.getSourceRowData();
            if (!data) {
              layer.msg('请先选择一行数据进行操作！');
              return false;
            }

            $.quoteFun('/personal/linkMan/addBeOnDuty', {
              trasen: beOnDutyTable,
              data,
              title: '详情',
              ref: beOnDutyTableRefresh,
            });
          }
        );

        $('#linkManBasicListBox #beOnDutyEdit').funs('click', function () {
          var data = beOnDutyTable.getSourceRowData();
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          $.quoteFun('/personal/linkMan/addBeOnDuty', {
            trasen: beOnDutyTable,
            title: '编辑',
            data,
            ref: beOnDutyTableRefresh,
          });
        });

        $('#linkManBasicListBox #beOnDutyDel').funs('click', function () {
          var data = beOnDutyTable.getSourceRowData(); // 选中行数据
          if (!data) {
            layer.msg('请先选择一行数据进行操作！');
            return false;
          }
          layer.confirm(
            '确定删除该条数据吗?',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              $.ajax({
                type: 'post',
                url:
                  common.url + '/ts-oa/api/dutyAddressBook/delete/' + data.id,
                dateType: 'json',
                contentType: 'application/json',
                success: function (res) {
                  $.closeloadings();
                  if (res.success) {
                    layer.closeAll();
                    trasen.info('操作成功');
                    beOnDutyTableRefresh();
                  } else {
                    layer.closeAll();
                    layer.msg(res.message);
                  }
                },
                error: function (res) {
                  res = JSON.parse(res.responseText);
                  layer.msg(res.message);
                },
              });
            }
          );
        });

        //科室排班导出
        $('#beOnDutyExport').funs('click', function () {
          layer.confirm(
            '您确定导出信息？',
            {
              btn: ['确定', '取消'],
              title: '提示',
              closeBtn: 0,
            },
            function (index) {
              var condition = $('#condition').val();
              window.location.href =
                common.url +
                '/ts-oa/api/dutyAddressBook/export?condition=' +
                condition;
              layer.closeAll();
              layer.close(index);
            }
          );
        });
        //科室排班导入
        $('#beOnDutyImport').funs('click', function () {
          $.quoteFun('/personal/linkMan/importBeOnDuty', {
            trasen: beOnDutyTable,
            title: '导入',
            ref: beOnDutyTableRefresh,
          });
        });

        //总值班
        laydate.render({
          elem: '#searchGeneralDutyTime',
          type: 'month',
          format: 'yyyy-MM',
          value: formatDate(),
          done: function (value, date, endDate) {
            $('#linkManBasicListBox #dutyDate').val(value);
          },
        });

        function formatDate() {
          let date = new Date();
          let year = date.getFullYear();
          let month = `0${date.getMonth() + 1}`.slice(-2);

          return `${year}-${month}`;
        }

        var generalDutyTable = null;
        function initGeneralDutyTable() {
          let colModel = [
            {
              label: '日期',
              name: 'dutyDate',
              width: 110,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                return '<span>' + cellvalue.split(' ')[0] + '</span>';
              },
            },
            {
              label: '星期',
              name: 'week',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '值班人工号1',
              name: 'dutyOneCode',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '值班员1',
              name: 'dutyOnePerson',
              width: 80,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                let value = cellvalue ? cellvalue : '';
                return (
                  '<span style="color: #02a7f0;cursor:pointer;" id="chooseGeneral" id ="' +
                  rowObject.id +
                  '" data-type="dutyOne">' +
                  value +
                  '</span>'
                );
              },
            },
            {
              label: '值班人工号2',
              name: 'dutyTwoCode',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '值班员2',
              name: 'dutyTwoPerson',
              width: 80,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                let value = cellvalue ? cellvalue : '';
                return (
                  '<div style="color: #02a7f0;cursor:pointer;height:100%;display:flex;justify-content:center;align-items:center" id="chooseGeneral" row-id ="' +
                  rowObject.id +
                  '" data-type="dutyTwo">' +
                  value +
                  '</div>'
                );
              },
            },
            {
              label: '二线值班领导工号',
              name: 'leaderCode',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '二线值班领导',
              name: 'leaderPerson',
              width: 80,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                let value = cellvalue ? cellvalue : '';
                return (
                  '<span style="color: #02a7f0;cursor:pointer;" id="chooseGeneral" row-id ="' +
                  rowObject.id +
                  '" data-type="leader">' +
                  cellvalue +
                  '</span>'
                );
              },
            },
            {
              label: '三线值班领导工号',
              name: 'leaderTwoCode',
              width: 110,
              align: 'center',
              sortable: false,
            },
            {
              label: '三线值班领导',
              name: 'leaderTwoPerson',
              width: 80,
              align: 'center',
              sortable: false,
              formatter: function (cellvalue, options, rowObject) {
                let value = cellvalue ? cellvalue : '';
                return (
                  '<div style="color: #02a7f0;cursor:pointer;height:100%;display:flex;justify-content:center;align-items:center" id="chooseGeneral" row-id ="' +
                  rowObject.id +
                  '" data-type="leaderTwo">' +
                  value +
                  '</div>'
                );
              },
            },
            {
              label: '备注',
              sortable: false,
              name: 'remark',
              width: 150,
              align: 'center',
            },
          ];
          generalDutyTable = new $.trasenTable('generalDutyTable', {
            url: common.url + '/ts-oa/api/totalDutySchedule/list',
            pager: '#generalDutyPage',
            mtype: 'GET',
            shrinkToFit: true,
            colModel: colModel,
            postData: {
              dutyDate: formatDate(),
            },
            buidQueryParams: function () {
              var search = $('#generalDutySearchForm').serializeArray();
              var data = {};
              for (var i in search) {
                data[search[i].name] = search[i].value;
              }
              if (!data.dutyDate) {
                data.dutyDate = formatDate();
              }
              return data;
            },
          });
        }

        function generalDutyTableRefresh() {
          generalDutyTable.refresh();
        }

        $('#linkManBasicListBox #generalDutySearch').funs('click', function () {
          generalDutyTableRefresh();
        });

        $('#linkManBasicListBox #generalDutyReset').funs('click', function () {
          $('#generalDutySearchForm #dutyPerson').val();
          form.render();
          generalDutyTableRefresh();
        });
        var checkCode = '';
        // 选择人
        $('#linkManBasicListBox #chooseGeneral').funs('click', function () {
          var row = generalDutyTable.getSourceRowData(); // 选中行数据
          var dataType = $(this).attr('data-type');
          var arr = [];
          checkCode = row[`${dataType}Code`];
          if (
            row[`${dataType}Person`] != null &&
            row[`${dataType}Person`] != ''
          ) {
            arr = [
              {
                name: row[`${dataType}Person`],
                code: row[`${dataType}Code`],
              },
            ];
          }
          $.quoteFun('/commonPage/userDeptGroupRadio/index', {
            data: {
              user: true,
              dept: true,
              userList: arr,
            },
            callBack: function (list, seluser, dept) {
              row[`${dataType}Person`] = seluser[0].name;
              row[`${dataType}Code`] = seluser[0].code;
              updateTableRow(row, `${dataType}Person`, `${dataType}Code`);
            },
          });
        });
        function updateTableRow(row, name, code) {
          if (row[code] == checkCode) {
            checkCode = '';
            return;
          }
          checkCode = '';
          let paramData = {};
          paramData.id = row.id;
          paramData[name] = row[name];
          paramData[code] = row[code];
          $.ajax({
            type: 'post',
            url: common.url + '/ts-oa/api/totalDutySchedule/update',
            dateType: 'json',
            contentType: 'application/json',
            data: JSON.stringify(paramData),
            success: function (res) {
              $.closeloadings();
              if (res.success) {
                layer.closeAll();
                generalDutyTableRefresh();
                trasen.info('操作成功');
              } else {
                layer.closeAll();
                layer.msg(res.message);
              }
            },
            error: function (res) {
              res = JSON.parse(res.responseText);
              layer.msg(res.message);
            },
          });
        }

        //总值班
        $('#generalDutyImport').funs('click', function () {
          $.quoteFun('/personal/linkMan/importGeneral', {
            trasen: generalDutyTable,
            title: '导入',
            ref: generalDutyTableRefresh,
          });
        });

        //总值班 导出
        $('#generalDutyExport').funs('click', function () {
          let dutyDate = $('#linkManBasicListBox #dutyDate').val();
          window.location.href =
            common.url +
            `/ts-oa/api/totalDutySchedule/export?dutyDate=${dutyDate}&pageSize=100&pageNo=1`;
        });
        //预览
        $('#generalDutyPrview').funs('click', function () {
          let dutyDate = $('#linkManBasicListBox #dutyDate').val();
          if (dutyDate == '') {
            dutyDate = formatDate();
          }
          let num = new Date(
            Number(dutyDate.split('-')[0]),
            Number(dutyDate.split('-')[1]),
            0
          ).getDate();
          $.ajax({
            type: 'get',
            url:
              common.url +
              `/ts-oa/api/totalDutySchedule/list?dutyDate=${dutyDate}&pageSize=100&pageNo=1`,
            dateType: 'json',
            contentType: 'application/json',
            success: function (res) {
              var all = res.rows;
              if (all.length == 0) {
                layer.msg('暂无值班数据可预览');
                return;
              }
              if (all.length != num) {
                layer.msg('月份值班数据不全，无法预览，请补齐值班数据');
                return;
              }
              $.quoteFun('/personal/linkMan/generalPrview', {
                trasen: generalDutyTable,
                date: $('#generalDutySearchForm #dutyDate').val(),
                data: all,
                title: '预览',
                ref: generalDutyTableRefresh,
              });
            },
            error: function (res) {
              layer.msg(res.message);
            },
          });
        });
      }
    );
  };
});
