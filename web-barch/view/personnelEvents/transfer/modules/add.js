"use strict";
define(function(require, exports, module) {
	exports.init = function(opt, html) {
		layui.use(['form', 'trasen', 'upload', 'zTreeSearch'], function() {
			var form = layui.form,
				laydate = layui.laydate,
				upload = layui.upload,
				trasen = layui.trasen,
				zTreeSearch = layui.zTreeSearch;

			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['720px', '580px'],
				skin: 'yourclass',
				content: html,
				success: function(layero, index) {
					getEmployeeComboxByStatus("employeeAddSel", "1,99"); // 员工列表(在职人员)
					getPositionList(); // 职称类型
					getDictInfoList(dict_employee_transfer_type, "调动类型", "changeTypeSel"); // 调动类型
					getDictInfoList(dict_personal_identity, "原岗位类别", "oldPersonalIdentity");
					getDictInfoList(dict_personal_identity, "新岗位类别", "newPersonalIdentity");

					getDictInfoList(dict_org_attributes, "人员类别", "orgAttributes");
					getDictInfoList(dict_post_type, "岗位类型", "postType");
			
					var businessId = '';
					if (opt.data) {
						// 编辑时员工信息不能编辑
						$("#employeeAddSel").addClass("layui-disabled");
						$("#employeeAddSel").attr("disabled", true);
						$("#employeeNo").addClass("layui-disabled");
						$("#employeeNo").attr("disabled", true);
						$("#genderTextPersonnelChangeAdd").val(opt.data.genderText);

						trasen.setNamesVal(layero, opt.data); // 渲染表格数据
					/* 	$("#employeeTranserTimekeeper").val(opt.data.timekeeperId); */

					} else {
						businessId = randomString();
					}

					var publicUpload = $.publicUpload($("#employeeTranserFileupload"), {
						formId: "employeeTranserFile",
						fileTableEl: $("#infoInfoDynamiccheckFilefileTableId"),
						deleted: true,
						width: 800,
						formData: {
							businessId: $("#employeeTranserAddHtml #personnelChangeId").val() || businessId
						},
						callback: function() {
							// var businessId = $("#trainResourceModulesAddCourseFile input[name='businessId']").val();
							// $("#trainResourceModulesAddCourseFile input[name='businessId']").val(businessId);
						}
					});
				//	getTimekeeperList();

					//获取医院编码
					if("csjkyy" == common.globalSetting.orgCode){
						$('#employeeTranserAddHtml #sxrqLabel').html('<span class="required">*</span>到岗时间'); 
						 $("#employeeTranserAddHtml #YZW").empty();
						 $("#employeeTranserAddHtml #XZW").empty();
						 $("#employeeTranserAddHtml #ZCGB").empty();
						form.render();
					}else{
						//不是经开医院 移除这三个
						 $("#employeeTranserAddHtml #GWSX").empty();
						 $("#employeeTranserAddHtml #RYLB").empty();
						  $("#employeeTranserAddHtml #GWLX").empty();
					}
					
					if(common.globalSetting.orgCode === 'pjxdyrmyy'){
						// 去掉  新职务  中层干部的必填
						$('#newPositionName').attr('lay-verify', ' ');
						$('#zhongceng').attr('lay-verify', ' ');
						$("#xzwReqImg").hide();
						$("#zcgbReqImg").hide();
					}
					
					form.render();
				}
			});

			// 签订日期的时间选择器
			laydate.render({
				elem: '#changeStartDate',
				trigger: 'click'
			});

			// 开始结束时间的时间选择器
			loadDateByBeginEnd('startTime', 'endTime');


			// 组织机构下拉选择树
			treeSelect();

			function treeSelect() {
				//新部门
				zTreeSearch.init('#trasferNewOrgDep', {
					url: common.url + '/ts-basics-bottom/organization/getTree',
					type: 'post',
					checkbox: false,
					condition: 'name',
					zTreeOnClick: function(treeId, treeNode) {
						$("#newOrgId").val(treeNode.id); // 组织机构ID
						$(".newOrgName").val(treeNode.name); // 组织机构名称
					}
				});
			}

/* 			function getTimekeeperList() {
				var html = '<option value="">请选择</option>';
				$.ajax({
					type: "get",
					contentType: "application/json; charset=utf-8",
					url: common.url + "/ts-hrms/timekeeper/getAuditlist",
					data: {},
					async: false,
					success: function(res) {
						if (res.object != null) {
							for (var i = 0; i < res.object.length; i++) {
								if (res.object[i] != null) {
									html += '<option value="' + res.object[i].timekeeperId + '">' + res.object[i].name + '</option>';
								}
							}
							$('#employeeTranserTimekeeper').html(html).val(opt.data && opt.data.timekeeperId || '');
						}
					}
				});

				form.render();
			} */

			//获取职称列表
			function getPositionList() {
				var html = '<option value="">请选择职务</option>';
				var _data = {};
				$.ajax({
					type: "post",
					contentType: "application/json; charset=utf-8",
					url: common.url + "/ts-basics-bottom/position/getList",
					data: JSON.stringify(_data),
					success: function(res) {
						if (res.object != null) {
							$.each(res.object, function(i, v) {
								if(opt.data && opt.data.newPositionId == v.positionId){
									html += '<option selected value="' + v.positionId + '">' + v.positionName + '</option>';
								}else{
									html += '<option value="' + v.positionId + '">' + v.positionName + '</option>';
								}
								
							});
							$("#newPositionName").html(html);
							form.render('select');
						}
					}
				});
			
			}

			// 监听申请人下拉选择
			form.on('select(transferIdSelFilter)', function(data) {
				var employeeName = $(data.elem).find("option:selected").attr("data-name");
				var employeeNo = $(data.elem).find("option:selected").attr("data-no");
				var oldPositionId = $(data.elem).find("option:selected").attr("data-positionid");

				var oldOrgId = $(data.elem).find("option:selected").attr("data-orgid");
				var gender = $(data.elem).find("option:selected").attr("data-gender");
				var genderText = $(data.elem).find("option:selected").attr("data-genderText");
				var personalIdentity = $(data.elem).find("option:selected").attr("data-personalIdentity");


				$("#employeeName").val(employeeName);
				$("#employeeNo").val(employeeNo);
				$("#oldPositionId").val(oldPositionId);

				$("#oldOrgId").val(oldOrgId);
				$("#genderPersonnelChangeAdd").val(gender);
				$("#genderTextPersonnelChangeAdd").val(genderText);
				$("#oldPersonalIdentity").val(personalIdentity);
				var oldPositionName = $(data.elem).find("option:selected").attr("data-positionname");
				if (oldPositionName != "null") {
					$("#oldPositionName").val(oldPositionName);
				} else {
					$("#oldPositionName").val("");
				}
				var oldOrgName = $(data.elem).find("option:selected").attr("data-orgname");
				if (oldOrgName != "null") {
					$("#oldOrgName").val(oldOrgName);
				} else {
					$("#oldOrgName").val("");
				}

				form.render();
			});

			form.on('select(newPositionIdFilter)', function(data) {
				var itemVal = $("#newPositionName").val();
				$("#newPositionId").val(itemVal);
			});

			form.on('submit(employeeTranserAddSub)', function(data) {
				// 模拟点击提交
				$('#employeeTranserSaveSub').trigger('click');
			});

			// 保存
			form.on('submit(employeeTranserSaveSub)', function(data) {
				//验证是否是审核人
				var _data = {};
				_data.timekeeperId = data.field.employeeId;
				$.ajax({
					type: "post",
					contentType: "application/json; charset=utf-8",
					url: common.url + "/ts-hrms/timekeeper/list?timekeeperId=" + data.field.employeeId,
					data: JSON.stringify(_data),
					success: function(res) {
						if (res.success && res.object != null && res.object.length > 0) {
							layer.confirm(data.field.employeeName + " 为考勤员，确定要调动吗", {
								btn: ['确定', '取消'],
								title: '提示',
								closeBtn: 0
							}, function() {
								saveTeansfer(data);
							}, function() {});
						} else {
							saveTeansfer(data);
						}
					}
				});

			});

			//保存调动单
			function saveTeansfer(data) {
				var _url = common.url + "/ts-hrms/personnelChange/save";
				if (opt.data) {
					_url = common.url + "/ts-hrms/personnelChange/update"
				}
				data.field.businessId = $("#employeeTranserFile input[name='businessId']").val();
				var _data = JSON.stringify(data.field);
				$.ajax({
					type: "post",
					contentType: "application/json; charset=utf-8",
					url: _url,
					data: _data,
					success: function(res) {
						if (res.success) {
							opt.ref && opt.ref();
							layer.closeAll();
							layer.msg('保存成功！');
						} else {
							layer.msg(res.message || '操作失败');
						}
					}
				});
				return false;
			}

			// 取消
			$('#employeeTranserAddHtml #employeeTranserClose').off('click').on('click', function() {
				layer.closeAll();
			});

		});
	}
});
