'use strict';
define(function (require, exports, module) {
  var init = function () {
    return perform();
  };
  module.exports = {
    init: init
  };

  var allBtns = [
      //所有的操作按钮
      {title: '编辑', class: 'myOrderEdit', icon: 'fa fa-pencil-square-o'}, //               0   编辑
      {title: '终止', class: 'myOrderEnd', icon: 'fa fa-ban'},              //               1   终止
      {title: '复制', class: 'myOrderCopy', icon: 'fa fa-copy'},            //               2   复制
      {title: '响应', class: 'myOrderRespone', icon: 'fa fa-check-circle-o'}, //             3   响应
      {title: '处理', class: 'myOrderHandle', icon: 'svg-icon icon_work_order_handle'},//    4   处理
      {title: '协助', class: 'myOrderAssist', icon: 'fa fa-handshake-o'},     //             5   协助
      {title: '暂停', class: 'myOrderpause', icon: 'fa fa-clock-o'},          //             6   暂停
      {title: '催办', class: 'myOrderurge', icon: 'svg-icon icon_work_order_send_message'},//7   催办
      {title: '转发', class: 'myOrderResend', icon: 'fa fa-share'},         //               8   转发
      {title: '验收', class: 'myOrderAcceptance', icon: 'fa fa-check-square-o'}, //          9   验收
      {title: '开启', class: 'myOrderStart', icon: 'fa fa-play-circle-o'}, //                10  开启
      {title: '评价', class: 'myOrderComment', icon: 'fa fa-star-half-o'}, //                11  评价
      {title: '存入知识库', class: 'saveInlib', icon: 'fa fa-leanpub'}, //                    12  存入知识库
      {title: '费用登记', class: 'costRegister', icon: 'fa fa-money'}, //                     13 费用登记
      {title: '追回工单', class: 'callbackOrder', icon: 'svg-icon initiate_application'}//    14 退回工单
    ],
    power = {
      '1': {
        //报修人权限
        待派单: [],
        待接单: [],
        处理中: [],
        待验收: [],
        待评价: [],
        已完成: [],
        已暂停: [],
        已终止: []
      },
      '2': {
        //处理人权限
        待派单: [],
        待接单: [3],
        处理中: [4, 5, 8, 6, 13],
        待验收: [13, 14],
        待评价: [13],
        已完成: [13, 12],
        已暂停: [13, 10],
        已终止: [13]
      },
      '3': {
        //协助人权限
        待派单: [],
        待接单: [],
        处理中: [4, 13],
        待验收: [13],
        待评价: [13],
        已完成: [13],
        已暂停: [13],
        已终止: [13]
      }
    },
    queryData = {},
    allTable = null, //全部表格
    handleOrderParticipatedTable = null, //参与过的表格
    tableLoaded = false,
    affectScope = {
      1: '个人事件',
      2: '科室事件',
      3: '多科室事件',
      4: '全院事件'
    },
    userInfo = {},
    API = {
      getList: '/ts-worksheet/workSheet/workSheetList',
      workSheetListBusCounts: '/ts-worksheet/workSheet/workSheetListBusCounts/4',
      getPeopleInfoList: '/ts-worksheet/workSheetPeopple/getPeopleInfoList',
      getUserInfo: '/ts-worksheet/workSheetPeopple/loginPersonInfo', //获取当前登录人信息
      workSheetTookPartPageList:
        '/ts-worksheet/workSheet/workSheetTookPartPageList',
      getHandleHistory: '/ts-worksheet/workSheet/selectAllTaskWorkHoursList/', //获取处理记录
      getHastenList: '/ts-worksheet/workSheetHasten/getHastenInfoList/', //获取催办信息记录列表
      getKnowledgeBaseInfo:
        '/ts-worksheet/workSheet/selectSubmitKnowledgeBaseInfo/', //提交知识库所需解决方案信息
      callBackOrder: '/ts-worksheet/workSheet/workSheetToRecover' //追回工单
    };

  var perform = function () {
    var currentIndex = 0;
    //全部

    //全部——表格
    var allTableCol = [
        {
          label: '工单编号',
          name: 'workNumber',
          sortable: false,
          hidden: false,
          align: 'center',
          width: 100,
          fixed: true
        },
        {
          label: '报修时间',
          name: 'createTime',
          sortable: true,
          index: 'a.create_time',
          width: 100,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          sortable: false,
          width: 65,
          editable: false,
          align: 'left',
          fixed: true
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          editable: false,
          sortable: false,
          align: 'left',
          fixed: true
        },
        {
          label: '报修电话',
          name: 'repairPhone',
          sortable: false,
          hidden: true,
          width: 110,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          sortable: false,
          width: 160,
          editable: false,
          align: 'left',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '处理人',
          name: 'fkUserName',
          sortable: false,
          hidden: false,
          width: 65,
          align: 'left',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = '';
            if (cell) {
              html = `<span title="${cell} ${
                row.assistName ? '协助人：' + row.assistName : ''
              }">${cell}</span>`;
            }
            return html;
          }
        },
        {
          label: '派单时间',
          name: 'sendTime',
          sortable: false,
          index: 'b.create_time',
          hidden: true,
          width: 100,
          align: 'center',
          fixed: true
        },
        {
          label: '接单时间',
          name: 'receiveTime',
          sortable: false,
          index: 'b.create_time',
          hidden: true,
          width: 100,
          align: 'center',
          fixed: true
        },
        {
          label: '要求日期',
          name: 'requiredCompletionTime',
          sortable: true,
          index: 'a.required_completion_time',
          hidden: false,
          width: 90,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (!cell) {
              return '';
            }
            let days = dayjs().diff(dayjs(cell), 'day');
            let html =
              '<span style="' +
              (days >= -3 &&
              ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
                ? 'color: red;'
                : '') +
              `">${cell}</span>`;
            return html;
          }
        },
        {
          label: '完成时间',
          name: 'actualCompletionTime',
          sortable: false,
          index: 'a.actual_completion_time',
          hidden: true,
          width: 100,
          align: 'center',
          fixed: true
        },
        {
          label: '联系方式',
          name: 'fkUserPhone',
          sortable: false,
          hidden: true,
          width: 110,
          align: 'center',
          fixed: true
        },
        {
          label: '处理工时',
          name: 'workHours',
          sortable: true,
          index: 'a.work_hours',
          hidden: false,
          width: 65,
          align: 'center',
          fixed: true,
          formatter: processingHours
        },
        {
          label: '确认时间',
          name: 'confirmTime',
          sortable: false,
          index: 'b.create_time',
          hidden: true,
          width: 100,
          align: 'center',
          fixed: true
        },
        {
          label: '催办次数',
          name: 'hatenCount',
          sortable: true,
          index: 'haten_count',
          hidden: false,
          width: 65,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = `<p name="openUrgeDetail"
                          class="table-cell-action-span"
                          data-rowId="${opt.rowId}"
                          style="cursor: pointer;">
                          <span class="dealLink">${cell}</span></p>`;
            return html;
          }
        },
        {
          label: '状态',
          name: 'workStatusName',
          sortable: false,
          hidden: false,
          width: 50,
          align: 'center',
          fixed: true
        },
        {
          label: '暂停时间',
          name: 'suspendedTime',
          sortable: false,
          index: 'b.create_time',
          hidden: true,
          width: 100,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '暂停原因',
          name: 'suspendedRemark',
          sortable: false,
          hidden: true,
          width: 80,
          editable: false,
          align: 'left'
        },
        {
          label: '终止人',
          name: 'terminationByName',
          sortable: false,
          hidden: true,
          width: 65,
          align: 'left',
          fixed: true
        },
        {
          label: '终止时间',
          name: 'terminationTime',
          sortable: false,
          index: 'a.actual_completion_time',
          hidden: true,
          width: 100,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '终止原因',
          name: 'terminationRemark',
          sortable: false,
          hidden: true,
          width: 80,
          editable: false,
          align: 'left'
        },
        {
          label: '备注',
          name: 'remark',
          sortable: false,
          hidden: true,
          width: 80,
          editable: false,
          align: 'left'
        },
        {
          label: '报修方式',
          name: 'repairType',
          sortable: false,
          hidden: true,
          width: 65,
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '紧急程度',
          name: 'faultEmergency',
          sortable: false,
          index: 'a.fault_emergency',
          hidden: true,
          width: 65,
          editable: false,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            return ['', '非常紧急', '比较急', '常规处理'][cell];
          }
        },
        {
          label: '影响范围',
          name: 'faultAffectScope',
          sortable: false,
          hidden: true,
          width: 65,
          editable: false
        },
        {
          label: '报修地址',
          name: 'repairDeptAddress',
          sortable: false,
          hidden: true,
          width: 65,
          editable: false
        },
        {
          label: '设备名称',
          name: 'faultEquipmentName',
          sortable: false,
          hidden: true,
          width: 65,
          editable: false
        },
        {
          label: '处理人ID',
          name: 'fkUserId',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '节点ID',
          name: 'pkWsTaskId',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '科室ID',
          name: 'repairManDeptId',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '报修人ID',
          name: 'repairManId',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '业务类型ID',
          name: 'fkFaultTypeId',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '人员类型',
          name: 'peopleType',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '上传附件',
          name: 'wsFileOutVoList',
          sortable: false,
          hidden: true,
          width: 150,
          editable: false
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 65,
          fixed: true,
          editable: false,
          resizable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            //操作栏
            return computedActionColumn(cell, opt, row);
          }
        }
      ],
      //参与过的——表格
      participatedCol = [
        {
          label: '工单编号',
          name: 'workNumber',
          hidden: false,
          sortable: false,
          align: 'center',
          width: 100,
          fixed: true
        },
        {
          label: '故障描述',
          name: 'faultDeion',
          width: 220,
          sortable: false,
          editable: false,
          align: 'left',
          formatter: computedFaultDeion,
          cellattr: function () {
            return 'title=""';
          }
        },
        {
          label: '报修时间',
          name: 'createTime',
          width: 100,
          sortable: true,
          index: 'a.create_time',
          editable: false,
          align: 'center',
          fixed: true
        },
        {
          label: '状态',
          name: 'workStatusName',
          sortable: false,
          hidden: false,
          width: 50,
          align: 'center',
          fixed: true
        },
        {
          label: '处理工时',
          name: 'workHours',
          sortable: false,
          hidden: false,
          width: 65,
          align: 'right',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = `<div name="openHandleTimeLine"
                          data-rowId="${opt.rowId}"
                          class="table-cell-action-span"
                          style="text-decoration: underline; color: #4395ff;"
                          >
                          ${cell}</div>`;
            return html;
          }
        },
        {
          label: '我的投入工时',
          name: 'putIntoWorkHours',
          sortable: false,
          editable: false,
          align: 'right',
          width: 100,
          fixed: true
        },
        {
          label: '处理人',
          name: 'fkUserName',
          hidden: false,
          sortable: false,
          width: 65,
          align: 'left',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = '';
            if (cell) {
              html = `<span title="${cell} ${
                row.assistName ? '协助人：' + row.assistName : ''
              }">${cell}</span>`;
            }
            return html;
          }
        },
        {
          label: '报修科室',
          name: 'repairManDeptName',
          width: 65,
          sortable: false,
          editable: false,
          align: 'left',
          fixed: true
        },
        {
          label: '报修人',
          name: 'repairManName',
          width: 65,
          sortable: false,
          editable: false,
          align: 'left',
          fixed: true
        },
        {
          label: '报修电话',
          name: 'repairPhone',
          width: 62,
          sortable: false,
          editable: false,
          align: 'left',
          hidden: true
        },
        {
          label: '要求日期',
          name: 'requiredCompletionTime',
          sortable: false,
          hidden: true,
          width: 90,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            if (!cell) {
              return '';
            }
            let days = dayjs().diff(dayjs(cell), 'day');
            let html =
              '<span style="' +
              (days >= -3 &&
              ['待派单', '待接单', '处理中'].indexOf(row.workStatusName) >= 0
                ? 'color: red;'
                : '') +
              `">${cell}</span>`;
            return html;
          }
        },

        {
          label: '催办次数',
          name: 'hatenCount',
          sortable: false,
          hidden: true,
          width: 65,
          align: 'center',
          fixed: true,
          formatter: function (cell, opt, row) {
            let html = `<p name="openUrgeDetail" 
                          data-rowId="${opt.rowId}"
                          style="cursor: pointer;">
                          <span class="dealLink">${cell}</span></p>`;
            return html;
          }
        },
        {
          label: '备注',
          name: 'remark',
          hidden: 'true',
          sortable: false,
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '报修方式',
          name: 'repairType',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '紧急程度',
          name: 'faultEmergency',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '影响范围',
          name: 'faultAffectScope',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '报修地址',
          name: 'repairDeptAddress',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '设备名称',
          name: 'faultEquipmentName',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '处理人ID',
          name: 'fkUserId',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '节点ID',
          name: 'pkWsTaskId',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '科室ID',
          name: 'repairManDeptId',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '报修人ID',
          name: 'repairManId',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '故障类型ID',
          name: 'fkFaultTypeId',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '人员类型',
          name: 'peopleType',
          hidden: true,
          width: 100,
          editable: false
        },
        {
          label: '<i class="oaicon oa-icon-gengduo1" aria-hidden="true"></i>',
          name: '',
          sortable: false,
          width: 40,
          fixed: true,
          editable: false,
          resizable: false,
          align: 'center',
          title: false,
          classes: 'visible jqgrid-rownum ui-state-default',
          formatter: function (cell, opt, row) {
            var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`,
              btns =
                `<button type="button" class="layui-btn myOrderCopy" row-id=` +
                opt.rowId +
                '> <i class="fa fa-copy deal_icon"  aria-hidden="true"></i>复制</button> '
                + 
                `<button type="button" class="layui-btn costRegister" row-id="${opt.rowId}" > 
                  <i class="fa fa-money"  aria-hidden="true"></i>
                  费用登记
                </button> `;
            html += btns + '</div></div>';
            return html;
          }
        }
      ];

    function initPage () {
      $.ajax({
        url: API.getUserInfo,
        type: 'GET',
        async: false,
        success: function (res) {
          if (res.success) {
            userInfo = res.object;
          }
        }
      });
      tableLoaded = false;
      let tabIndex = location.hash.split('=')[1] || 3;
      currentIndex = Number(tabIndex);

      location.hash = '#/myHandleOrder';
      renderOptionsBar(currentIndex);
      updateOptionsBar();
      layui.use(['form', 'laydate', 'layer'], function () {
        var form = layui.form,
          laydate = layui.laydate;

        renderOrderList(currentIndex);
        form.render();
      });
    }

    initPage();
    renderSearchBar();
    // 事件监听
    Event.create('refreshNowPageDialog').listen('#/myHandleOrder', (e) => {
      if (e) {
        let index = Number(e.type);
        handleRouterChange(index);
        return
      }
      const activeTab = $('#myHandleOrder .myHandleOrder-options-item-text.myHandleOrder-option-active-text')[0];
      if (!activeTab) {
        return
      }
      let activeIndex = Number(activeTab.getAttribute('data-index'));

      $(`#myHandleOrder #myWorkSheet${activeIndex + 1}-searchBtn`).trigger('click');
    });

    //创建事件监听对象
    Event.create('myHandleOrderJumpHandle').listen('jump', (e) => {
      let active = e ? String(e.type) : 0;
      setTimeout(() => {
        $('.myHandleOrder-options-item-text')[active].click();
      }, 300)
    });
    
    //事件处理
    function handleRouterChange (index) {
      if (!$('.myHandleOrder-options-item-text')[index] || !tableLoaded) {
        setTimeout(() => {
          handleRouterChange(index);
        }, 50);
        return
      }
      $('.myHandleOrder-options-item-text')[index].click();
    }

    /*-----------------------------------渲染方法---------------------------------*/
    function renderOrderList (index) {
      allTable = renderTable(
        API.getList,
        allTableCol,
        'orderAllForm',
        'allHandleOrderPager',
        'allHandleOrderTable',
        index
      );

      // computedColOrder(
      //   [
      //     '工单编号',
      //     '故障描述',
      //     '报修时间',
      //     '要求日期',
      //     '状态',
      //     '催办次数',
      //     '处理人',
      //     '处理工时',
      //     '报修科室',
      //     '报修人'
      //   ],
      //   '#allHandleOrderTable'
      // );
      // changeSortble('#allHandleOrderTable', 'true', [
      //   'createTime',
      //   'requiredCompletionTime',
      //   'hatenCount'
      // ]);

      bindFormBtnEvent(allTable, updateOptionsBar);
      bindTableActionEvent(allTable);
      firstLoadTableCol(index);
    }

    function firstLoadTableCol (index = 0) {
      if (!tableLoaded) {
        setTimeout(() => {
          firstLoadTableCol(index)
        }, 50);
        return
      }

      reRenderTableCol(index);
    }

    //头部分类切换
    function renderOptionsBar (currentIndex) {
      var node = $('#myHandleOrder .myHandleOrder-options-item-text')[currentIndex];
      node.className += ' myHandleOrder-option-active-text';

      $('#myHandleOrder #myWorkOrderOptionsBar').funs('click', function (e) {
        var tar = e.target,
          clickIndex = parseInt(tar.getAttribute('data-index'));
        if (
          clickIndex == null ||
          clickIndex == currentIndex ||
          isNaN(clickIndex)
        ) {
          return;
        }
        $('#myHandleOrder .myHandleOrder-options-item-text').removeClass(
          'myHandleOrder-option-active-text'
        );
        tar.className += ' myHandleOrder-option-active-text';

        $('#myHandleOrder .oa-nav-search .queryForm').hide();
        $('#myHandleOrder .oa-nav-search .queryForm')
          .eq(clickIndex)
          .show();

        currentIndex = clickIndex;
        if (currentIndex == 9) {
          $('#myHandleOrder #participatedForm').css('display', 'block');
          $('#myHandleOrder #orderAllForm').css('display', 'none');
          if (!handleOrderParticipatedTable) {
            handleOrderParticipatedTable = renderTable(
              API.workSheetTookPartPageList,
              participatedCol,
              'participatedForm',
              'handleOrderParticipatedPager',
              'handleOrderParticipatedTable'
            );
            bindFormBtnEvent(handleOrderParticipatedTable, updateOptionsBar);
          }
          bindTableActionEvent(handleOrderParticipatedTable);
          setTimeout(() => {
            $('#myHandleOrder #myWorkSheet10-searchBtn').trigger('click');
          }, 100);
        } else {
          //重新绑定事件
          bindTableActionEvent(allTable);
          //重置表格列的隐藏或者显示
          let allShows = [
              'workNumber',
              'createTime',
              'repairManDeptName',
              'repairManName',
              'faultDeion',
              'fkUserName',
              'requiredCompletionTime',
              'workHours',
              'hatenCount',
              'workStatusName'
            ],
            allHide = [
              'remark',
              'repairType',
              'faultEmergency',
              'faultAffectScope',
              'repairPhone',
              'repairDeptAddress',
              'faultEquipmentName',
              'fkUserId',
              'pkWsTaskId',
              'repairManDeptId',
              'repairManId',
              'fkFaultTypeId',
              'peopleType',
              'wsFileOutVoList',
              'sendTime',
              'fkUserPhone',
              'receiveTime',
              'actualCompletionTime',
              'confirmTime',
              'suspendedTime',
              'suspendedRemark',
              'terminationTime',
              'terminationRemark',
              'terminationByName'
            ];
          changeJQGrid('#allHandleOrderTable', 'showCol', allShows);
          changeJQGrid('#allHandleOrderTable', 'hideCol', allHide);

          //重置列展示
          let sortCol = [
            'createTime',
            'sendTime',
            'receiveTime',
            'requiredCompletionTime',
            'actualCompletionTime',
            'workHours',
            'confirmTime',
            'hatenCount',
            'suspendedTime',
            'terminationTime',
            'faultEmergency'
          ];
          changeSortble('#allHandleOrderTable', 'false', sortCol);
          $('#myHandleOrder #participatedForm').css('display', 'none');
          $('#myHandleOrder #orderAllForm').css('display', 'block');
          queryData = {};

          reRenderTableCol(clickIndex);
          $(`#myHandleOrder #myWorkSheet${clickIndex + 1}-searchBtn`).trigger('click');
        }
      });
    }

    function reRenderTableCol (clickIndex) {
      switch (clickIndex) {
        case 0:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '状态',
              '催办次数',
              '处理人',
              '处理工时',
              '报修科室',
              '报修人'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime',
            'hatenCount'
          ]);
          break;
        case 1:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '报修科室',
              '报修人',
              '备注'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', ['remark']);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
            'hatenCount',
            'fkUserName',
            'workHours'
          ]);
          break;
        case 2:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '派单时间',
              '处理人',
              '联系方式',
              '紧急程度',
              '催办次数',
              '报修科室',
              '报修人',
              '报修电话'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime',
            'sendTime',
            'hatenCount',
            'faultEmergency'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'faultEmergency',
            'repairPhone',
            'sendTime',
            'fkUserPhone'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workHours',
            'workStatusName'
          ]);
          break;
        case 3:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '接单时间',
              '处理人',
              '联系方式',
              '处理工时',
              '催办次数',
              '报修科室',
              '报修人',
              '报修电话'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime',
            'receiveTime',
            'hatenCount'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'repairPhone',
            'receiveTime',
            'fkUserPhone'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
          ]);
          break;
        case 4:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '完成时间',
              '处理人',
              '联系方式',
              '处理工时',
              '催办次数',
              '报修科室',
              '报修人',
              '报修电话'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime',
            'actualCompletionTime',
            'hatenCount',
            'workHours'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'repairPhone',
            'actualCompletionTime',
            'fkUserPhone'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
          ]);
          break;
        case 5:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '派单时间',
              '完成时间',
              '确认时间',
              '处理人',
              '处理工时',
              '催办次数',
              '报修科室',
              '报修人'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', ['confirmTime']);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'actualCompletionTime',
            'sendTime',
            'confirmTime'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
            'repairPhone'
          ]);
          break;
        case 6:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '派单时间',
              '完成时间',
              '确认时间',
              '处理人',
              '处理工时',
              '报修科室',
              '报修人'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', ['actualCompletionTime']);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'actualCompletionTime',
            'sendTime',
            'confirmTime'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
            'repairPhone',
            'requiredCompletionTime',
            'hatenCount'
          ]);
          break;
        case 7:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '要求日期',
              '处理人',
              '联系方式',
              '处理工时',
              '报修科室',
              '报修人',
              '报修电话',
              '暂停时间',
              '暂停原因'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'requiredCompletionTime',
            'suspendedTime'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'repairPhone',
            'fkUserPhone',
            'suspendedTime',
            'suspendedRemark'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
            'hatenCount'
          ]);
          break;
        case 8:
          computedColOrder(
            [
              '工单编号',
              '故障描述',
              '报修时间',
              '处理人',
              '联系方式',
              '处理工时',
              '报修科室',
              '报修人',
              '报修电话',
              '终止时间',
              '终止人',
              '终止原因'
            ],
            '#allHandleOrderTable'
          );
          changeSortble('#allHandleOrderTable', 'true', [
            'createTime',
            'terminationTime'
          ]);
          changeJQGrid('#allHandleOrderTable', 'showCol', [
            'repairPhone',
            'fkUserPhone',
            'terminationTime',
            'terminationRemark',
            'terminationByName'
          ]);
          changeJQGrid('#allHandleOrderTable', 'hideCol', [
            'workStatusName',
            'hatenCount',
            'requiredCompletionTime'
          ]);
          break;
        default:
          break;
      }
      $('#myHandleOrder #allHandleOrderTable').setGridWidth(
        $('#myHandleOrder #orderAllForm .table-box').width() - 3
      );
    }

    function renderTable (url, table, formNode, pager, tableNode, workStatus) {
      tableNode = 'myHandleOrder #' + tableNode;
      return new $.trasenTable(tableNode, {
        url: common.url + url,
        pager: pager,
        shrinkToFit: true,
        mtype: 'post',
        datatype: 'json',
        sortname: 'a.create_time',
        colModel: table,
        postData: {workStatus: workStatus || 0, type: 2},
        loadComplete () {
          tableLoaded = true;
        },
        buidQueryParams: function () {
          tableLoaded = false;
          updateOptionsBar();
          return queryData;
        }
      });
    }
  };

  /*-----------------------------------事件绑定---------------------------------*/
  function bindFormBtnEvent (bindTable, updateOptionsBar) {
    $('.printOrder', $('#' + bindTable.tableId)).funs('click', function(e){
      var tNode = e.target,
      selectRowId = tNode.getAttribute('row-id'),
        selectRowData = bindTable.getSourceRowData(selectRowId);
      
      let aDom = document.createElement('a');
      aDom.href = '/ts-worksheet/workSheet/print/' + selectRowData.workNumber
      aDom.click();
      return
    })
    $('.myOrderurge', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      $.quoteFun('orderInfoDesk/modules/urge', {
        title: '催办',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderRespone', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'response';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '响应',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderHandle', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'handle';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '处理',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderResend', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'resend';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '转发',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderEnd', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'end';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '终止',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderAssist', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'assist';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '协助',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderpause', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      data.entryType = 'pause';
      $.quoteFun('myWorkOrder/modules/handle', {
        title: '暂停',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderAcceptance', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      $.quoteFun('myWorkOrder/modules/acceptance', {
        title: '验收',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderComment', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      $.quoteFun('myWorkOrder/modules/comment', {
        title: '评价',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderEdit', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      $.quoteFun('myWorkOrder/modules/edit', {
        title: '编辑',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });

    $('#myHandleOrder [name="myorderCreaderOrderBtn"]').funs('click', function () {
      $.quoteFun('myWorkOrder/modules/edit', {
        title: '创建工单',
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderStart', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      $.quoteFun('myWorkOrder/modules/start', {
        title: '开启',
        data: data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.myOrderCopy', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        data = bindTable.getSourceRowData(rowId);
      delete data.workNumber;
      delete data.pkWsTaskId;
      delete data.pkWsSheetId;

      $.quoteFun('myWorkOrder/modules/edit', {
        title: '复制',
        data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.saveInlib', $('#' + bindTable.tableId)).funs('click', function (e) {
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        rowData = bindTable.getSourceRowData(rowId),
        data = {
          knowledgeTitle: rowData.faultDeion,
          recommendedWorkHours: rowData.workHours
        };
      $.ajax({
        url: API.getKnowledgeBaseInfo + rowData.workNumber,
        type: 'GET',
        async: false,
        success: function (res) {
          if (res.success) {
            data.takeRemark = (res.object || {}).takeRemark;
          } else {
            layer.msg(res.message || '出错啦');
          }
        }
      });

      $.quoteFun('myWorkOrder/modules/addLibrary', {
        title: '新增知识点',
        data,
        ref: function () {
          bindTable.refresh();
        }
      });
    });
    $('.costRegister', $('#' + bindTable.tableId)).funs('click', function(e){
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        rowData = bindTable.getSourceRowData(rowId);

      $.quoteFun('myHandleOrder/modules/costRegister',{
        data: rowData,
        ref: function(){
          bindTable.refresh();
        }
      })
    })
    $('.callbackOrder', $('#' + bindTable.tableId)).funs('click', function(e){
      var tNode = e.target,
        rowId = tNode.getAttribute('row-id'),
        rowData = bindTable.getSourceRowData(rowId);
        
      layer.open({
        title: '提示',
        content: '确定要追回到处理中状态吗?',
        btn: ['确定', '取消'],
        btn1: function (index, layero) {
          $.ajax({
            url: API.callBackOrder,
            type: 'POST',
            contentType: 'application/json; charset=utf-8',
            data: JSON.stringify({
              pkWsTaskId: rowData.pkWsTaskId,
              fkUserId: userInfo.userId
            }),
            success: function (res) {
              layer.close(index);
              if (res.success) {
                layer.msg('成功追回工单');
                bindTable.refresh();
              } else {
                layer.msg(res.message || '操作失败');
              }
            }
          });
        },
        btn2: function (index, layero) {
          layer.close(index);
        }
      });
    })
  }

  /*-----------------------------------内部方法---------------------------------*/
  function insetSelectData (nodeName, data) {
    for (var i = 0; i < data.length; i++) {
      var item = data[i],
        cNode = '<option value=' + item.value + '>' + item.label + '</option>';
      $(nodeName).append(cNode);
    }
  }

  //绑定点击事件
  function bindTableActionEvent (bindTable) {
    //表格点击故障描述展示工单详情
    $('span[name="openFaultAllInfo"]', $('#' + bindTable.tableId)).funs(
      'click',
      e => {
        let rowId = e.target.getAttribute('data-rowId'),
          rowData = bindTable.getSourceRowData(rowId);
        $.quoteFun('orderInfoDesk/modules/distribute', {
          title: '工单详情',
          data: rowData
        });
      }
    );

    //表格点击催办次数展示催办详情
    $('span[name="openUrgeDetail"]', $('#' + bindTable.tableId)).funs(
      'click',
      e => {
        let rowId = e.target.getAttribute('data-rowId'),
          rowData = bindTable.getSourceRowData(rowId);

        $.ajax({
          url: API.getHastenList + rowData.workNumber,
          type: 'get',
          success: function (res) {
            if (res.success == false) {
              layer.msg(res.message || '催办记录加载失败');
              return;
            }
            let lineHtml = '';
            (res.object || []).forEach(item => {
              lineHtml += `
              <li class="layui-timeline-item">
                <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
                <div class="layui-timeline-content layui-text">
                  <div class="layui-timeline-title">
                    <span>${item.createByName}</span>
                    <span class='hasten-time'>${item.createTime}</span>
                  </div>
                </div>
              </li>
              `;
            });

            if (!res.object || !res.object.length) {
              lineHtml = `<div style="height: 100%; line-height: 130px;">暂无数据</div>`;
            }

            $.quoteFun('orderInfoDesk/modules/hastenList', {
              title: '催办记录',
              data: lineHtml
            });
          }
        });
      }
    );

    //表格点击处理工时，查看具体工时记录
    $('div[name="openHandleTimeLine"]', $('#' + bindTable.tableId))
      .off('click')
      .funs('click', e => {
        let rowId = e.target.getAttribute('data-rowId'),
          rowData = bindTable.getSourceRowData(rowId);
        $.ajax({
          url: API.getHandleHistory + rowData.workNumber,
          type: 'get',
          success: function (res) {
            if (res.success == false) {
              layer.msg(res.message || '工时列表获取失败');
              return;
            }

            let lineHtml = '';
            (res.object || []).forEach(item => {
              lineHtml += `
            <li class="layui-timeline-item">
              <i class="layui-icon layui-timeline-axis">&#xe63f;</i>
              <div class="layui-timeline-content layui-text">
                <div class="layui-timeline-title flex flex-row-between">
                  <span class="strong-font">${item.fkUserName}</span>
                  <span class='strong-font'>投入工时: ${item.workHours}H</span>
                  <span class='strong-font'>${item.fkUserDeptName}</span>
                </div>
                <div class="time-line-item-content">
                  跟进时间：${item.createTime}
                </div>
              </div>
            </li>
            `;
            });

            if (!res.object || !res.object.length) {
              lineHtml = `<div style="height: 100%; line-height: 130px;">暂无数据</div>`;
            }

            $.quoteFun('orderInfoDesk/modules/hastenList', {
              title: '工时记录',
              data: lineHtml
            });
          }
        });
      });

    //导出点击事件
    $('#myHandleOrder [name="export"]')
      .off('click')
      .bind('click', e => {
        let node = $(
            '#myHandleOrder #myWorkOrderOptionsBar .myHandleOrder-option-active-text'
          )[0],
          nowTabIndex = parseInt(node.getAttribute('data-index')),
          tableName = nowTabIndex == 9 ? '#handleOrderParticipatedTable' : '#allHandleOrderTable';

        let cols = $(tableName).jqGrid('getGridParam', 'colModel'),
          tableData = $(tableName).jqGrid('getGridParam', 'userData'),
          showCols = cols.filter(item => !item.hidden);

        showCols.shift();
        showCols.pop();
        let newTable = document.createElement('table'),
          newTableHead = document.createElement('tr');
        showCols.forEach(item => {
          let th = document.createElement('th');
          th.innerHTML = item.label;
          newTableHead.append(th);
        });
        newTable.append(newTableHead);
        tableData.forEach(item => {
          let tr = document.createElement('tr');
          for (let i = 0; i < showCols.length; i++) {
            let td = document.createElement('td');
            td.innerHTML = item[showCols[i].name];
            tr.append(td);
          }
          newTable.append(tr);
        });

        var tableToExcel = (function () {
          var uri = 'data:application/vnd.ms-excel;base64,',
            template =
              '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><meta http-equiv="content-type" content="application/vnd.ms-excel; charset=UTF-8"><head><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
            base64 = function (s) {
              return window.btoa(unescape(encodeURIComponent(s)));
            },
            // 下面这段函数作用是：将template中的变量替换为页面内容ctx获取到的值
            format = function (s, c) {
              return s.replace(/{(\w+)}/g, function (m, p) {
                return c[p];
              });
            };
          return function (table, name) {
            // 获取表单的名字和表单查询的内容
            var ctx = {worksheet: name || 'Worksheet', table: table.innerHTML};
            // format()函数：通过格式操作使任意类型的数据转换成一个字符串
            // base64()：进行编码
            let a = document.createElement('a');
            a.href = uri + base64(format(template, ctx));
            a.download =
              '我的工单—' +
              $('.myHandleOrder-option-active-text')[0].innerHTML +
              '表格.xls'; //设置被下载的超链接目标（文件名）
            a.click();
          };
        })();

        tableToExcel(newTable);
        //以防万一又要用接口导出
        // layui.use([ 'form' ], function(){
        //     let form = layui.form

        //     let downLoadData = {}
        //     if(searchedList[nowTabIndex]){
        //         let btnIndex = nowTabIndex + 1,
        //             data = Object.assign( form.val(`myWorkSheet${btnIndex}`), form.val(`myWorkSheet${btnIndex}-hidden`) );
        //         delete data.fkUserFullName;
        //         delete data.repairName;
        //         delete data.repairManDeptName;
        //         filterParams(data);

        //         downLoadData = { ...data }
        //     }
        // })
      });
  }

  //计算操作栏
  function computedActionColumn (cell, opt, row) {
    var html = `<div class="table-more-btn"><div class="more-btn"><i class="layui-icon layui-icon-more-vertical tableMoreBtn"></i></div><div class="more-box">`;
    var btns = '',
      peopleType = row.peopleType,
      workStatus = row.workStatusName,
      btnList = [],
      powerList = []; //权限数组
    switch (peopleType) {
      case '1': //为报修人时
        powerList = power['1'][workStatus];
        powerList.forEach(item => {
          btnList.push(allBtns[item]);
        });
        break;
      case '2': //为处理人时
        powerList = power['2'][workStatus];
        powerList.forEach(item => {
          btnList.push(allBtns[item]);
        });
        break;
      case '3': //为协助人时
        powerList = power['3'][workStatus];
        powerList.forEach(item => {
          btnList.push(allBtns[item]);
        });
        break;
      case '4': //即是报修人 又是 处理人
        powerList = power['1'][workStatus].concat(power['2'][workStatus]);
        powerList = Array.from(new Set(powerList));
        powerList.forEach(item => {
          btnList.push(allBtns[item]);
        });
        break;
      case '5': //即是报修人 也是 协助人
        powerList = power['1'][workStatus].concat(power['3'][workStatus]);
        powerList = Array.from(new Set(powerList));
        powerList.forEach(item => {
          btnList.push(allBtns[item]);
        });
        break;
      default:
        btnList = [];
        break;
    }

    //临时解决导出
    if(hasPrintWorkOrder){
      btnList.push({title: '打印工单', class: 'printOrder', icon: 'fa fa-print'});
    }

    if (btnList.length > 1) {
      for (var i = 0; i < btnList.length; i++) {
        var item = btnList[i];
        btns +=
          `<button type="button" class="layui-btn ${item.class}" row-id=` +
          opt.rowId +
          '> <i class="' + item.icon + ' deal_icon"  aria-hidden="true"></i>' +
          item.title +
          '</button> ';
      }
      html += btns + '</div></div>';
    } else {
      btnList.length
        ? html = `<div class="table-action ${btnList[0].class}" row-id="${opt.rowId}">${btnList[0].title}</div>` : html = '';
    }
    return html;
  }

  //渲染搜索框
  function renderSearchBar () {
    layui.use(['zTreeSearch', 'form', 'laydate'], function () {
      var zTreeSearch = layui.zTreeSearch,
        form = layui.form,
        laydate = layui.laydate;

      //全部 -搜索渲染
      //渲染搜索的报修范围选择器
      $('#myHandleOrder [render="time"]').each(function (index, item) {
        laydate.render({
          elem: item,
          format: 'yyyy-MM-dd',
          type: 'date',
          trigger: 'click'
        });
      });

      //渲染报修科室
      $('#myHandleOrder [render="repairDep"]').each(function (index, item) {
        let repaireManId = item.getAttribute('data-repairman-id'),
          repaireManDom = $(`#myHandleOrder #${repaireManId}`);
        repaireManDom.empty();
        zTreeSearch.init(`#myHandleOrder [depNameVal="repairManDeptName${index}"]`, {
          url: common.url + '/ts-basics-bottom/organization/getTree',
          type: 'post',
          checkbox: false,
          condition: 'name',
          zTreeOnClick: function (treeId, treeNode) {
            if (treeNode) {
              $(`#myHandleOrder [depIdVal="repairManDeptId${index}"]`).val(treeNode.id);
              let options = {
                url: '/ts-basics-bottom/employee/getEmployeePageList',
                datatype: 'POST', // 请求方式
                searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
                textName: 'employeeName', 
                valName: 'employeeId', 
                inpTextName: 'repairName', 
                inpValName: 'repairManId', 
                condition: 'employeeName',
                required: 'none',
                layout: 'concat',
                labelConcatList: 'orgName',
                data: {
                  orgId: treeNode.id,
                  pageSize: 10
                },
                callback: function (res) {
                  if (res) {
                  }
                }
              };
              new $.selectPlug(`#myHandleOrder #${repaireManId}`, options);
            }
          },
          callback: function () {
            $(`#myHandleOrder [depIdVal="repairManDeptId${index}"]`).val('');
            let options = {
              url: '/ts-basics-bottom/employee/getEmployeePageList',
              datatype: 'POST', // 请求方式
              searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
              textName: 'employeeName', 
              valName: 'employeeId', 
              inpTextName: 'repairName', 
              inpValName: 'repairManId', 
              condition: 'employeeName',
              required: 'none',
              layout: 'concat',
              labelConcatList: 'orgName',
              data: {
                pageSize: 10
              },
              callback: function (res) {
                if (res) {
                }
              }
            };
            new $.selectPlug(`#myHandleOrder #${repaireManId}`, options);
          }
        });
        $(`#myHandleOrder [depNameVal="repairManDeptName${index}"]`).bind(
          'input propertychange',
          function (e) {
            $(`#myHandleOrder [depIdVal="repairManDeptId${index}"]`)[0].value = '';
          }
        );
        $(`#myHandleOrder [depNameVal="repairManDeptName${index}"]`).bind('blur', function () {
          $(`#myHandleOrder [depIdVal="repairManDeptId${index}"]`)[0].value
            ? null
            : ($(`#myHandleOrder [depNameVal="repairManDeptName${index}"]`)[0].value = '');
        });
      });

      //渲染报修人
      let options = {
        url: '/ts-basics-bottom/employee/getEmployeePageList',
        datatype: 'POST', // 请求方式
        searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
        textName: 'employeeName', 
        valName: 'employeeId', 
        inpTextName: 'repairName', 
        inpValName: 'repairManId', 
        condition: 'employeeName',
        required: 'none',
        layout: 'concat',
        labelConcatList: 'orgName',
        data: {
          pageSize: 10
        },
        callback: function (res) {
          if (res) {
          }
        }
      };
      $('#myHandleOrder [render="repairMan"]').each(function (index, item) {
        new $.selectPlug("#myHandleOrder [repairMan='" + index + "']", options);
      });

      //渲染处理人
      let workSheetSearch1fkUserName = {
        url: '/ts-worksheet/workSheetPeopple/getPeopleInfoList',
        datatype: 'POST', // 请求方式
        searchType: 'json', //json：动态数据，url 和 datatype 为必需  local:静态数据，不会发请求。
        textName: 'name', 
        valName: 'userId', 
        inpTextName: 'fkUserName', 
        inpValName: 'userId', 
        condition: 'employeeName',
        layout: 'concat',
        labelConcatList: 'deptName',
        data: {
          pageSize: 10
        },
        callback: function (res) {
        }
      };
      $('#myHandleOrder [render="handler"]').each(function (index, item) {
        new $.selectPlug(
          "#myHandleOrder [handler='" + index + "']",
          workSheetSearch1fkUserName
        );
      });

      //添加搜索点击事件
      $('#myHandleOrder [btnType="searchBtn"]').each(function (index, item) {
        let btnIndex = index + 1;
        $(
          `#myHandleOrder #myWorkSheet${btnIndex}-searchBtn, #myHandleOrder #myWorkSheet${btnIndex}-searchBtnHidden`
        )
          .off('click')
          .bind('click', e => {
            let data = Object.assign(
              form.val(`myHandleOrder${btnIndex}`),
              form.val(`myHandleOrder${btnIndex}-hidden`)
            );
            delete data.fkUserFullName;
            delete data.repairName;
            delete data.repairManDeptName;
            filterParams(data);
            queryData = Object.assign({}, data, { type: 2 });
            queryData.workStatus = index;
            if (btnIndex < 10) {
              allTable.refresh();
            } else if (btnIndex == 10) {
              handleOrderParticipatedTable.refresh();
            }
          });
      });
      //添加重置事件
      $('#myHandleOrder [btntype="resetBtn"]').each(function (index, item) {
        let btnIndex = index + 1;
        $(
          `#myHandleOrder #myWorkSheet${btnIndex}-reset, #myHandleOrder #myWorkSheet${btnIndex}-refresh`
        ).funs('click', e => {
          $(`#myHandleOrder #myWorkSheet${btnIndex}-searchFormHidden`)[0] &&
          $(`#myHandleOrder #myWorkSheet${btnIndex}-searchFormHidden`)[0].reset();
          $(`#myHandleOrder #myWorkSheet${btnIndex}-searchForm`)[0].reset();
          $(
            `#myHandleOrder #myWorkSheet${btnIndex}-fkUserName input, #myHandleOrder #myWorkSheet${btnIndex}-repairName input`
          ).val('');
          $(`#myHandleOrder #myWorkSheet${btnIndex}-repairManDeptId`).val('');

          //强行清除缓存
          $(
            `#myHandleOrder #orderInfo${btnIndex}-fkUserName .SelectPullDown, #myHandleOrder #orderInfo${btnIndex}-repairName .SelectPullDown`
          ).addClass('SelectPullDownId'); //添加上类名
          $(
            `#myHandleOrder #orderInfo${btnIndex}-fkUserName .SelectPullDownId, #myHandleOrder #orderInfo${btnIndex}-repairName .SelectPullDownId`
          ).trigger('keyup'); //触发事件，清空缓存数据
          $(
            `#myHandleOrder #orderInfo${btnIndex}-fkUserName .SelectPullDownId, #myHandleOrder #orderInfo${btnIndex}-repairName .SelectPullDownId`
          ).removeClass('SelectPullDownId'); //清除添加的类名，避免造成影响

          form.render();
          $(`#myHandleOrder #myWorkSheet${btnIndex}-searchBtn`).trigger('click');
        });
      });

      //参与过的搜索点击事件
    });
  }

  //tab标签数据刷新
  function updateOptionsBar () {
    $.ajax({
      url: API.workSheetListBusCounts,
      type: 'GET',
      success: function (res) {
        if (res.success == false) {
          layer.msg(res.message || '今日统计数据加载失败');
          return;
        }
        let object = res.object || {};
        $('#myHandleOrder .myHandleOrder-options-item-text')[1].innerHTML =
          '待派单(' + object[1] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[2].innerHTML =
          '待接单(' + object[2] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[3].innerHTML =
          '处理中(' + object[3] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[4].innerHTML =
          '待验收(' + object[4] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[5].innerHTML =
          '待评价(' + object[5] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[7].innerHTML =
          '已暂停(' + object[7] + ')';
        $('#myHandleOrder .myHandleOrder-options-item-text')[8].innerHTML =
          '已终止(' + object[8] + ')';
      }
    });
  }

  //过滤掉空属性
  function filterParams (obj) {
    for (let key in obj) {
      if (obj[key] == '') {
        obj[key] = null;
      }
    }
  }

  //更换JQGrid表格，调整表格大小
  function changeJQGrid (table, type, columns) {
    table = '#myHandleOrder ' + table;
    if (
      Object.prototype.toString.call(table) != '[object String]' ||
      (type != 'hideCol' && type != 'showCol') ||
      Object.prototype.toString.call(columns) != '[object Array]'
    ) {
      return;
    }

    let cols = $(table).jqGrid('getGridParam', 'colModel'),
      width = 0;

    cols.forEach(item => {
      if (columns.indexOf(item.name) >= 0) {
        if (
          (item.hidden && type == 'showCol') ||
          (!item.hidden && type == 'hideCol')
        )
          width = width / 1 + item.width / 1;
      }
    });

    $(table).jqGrid(type, columns);

    if (type == 'hideCol') {
      $(table).setGridWidth($(table).getGridParam('width') + width);
    } else {
      $(table).setGridWidth($(table).getGridParam('width') - width);
    }
  }

  function changeSortble (table, type, columns) {
    table = '#myHandleOrder ' + table;
    if (
      Object.prototype.toString.call(table) != '[object String]' ||
      (type != 'true' && type != 'false') ||
      Object.prototype.toString.call(columns) != '[object Array]'
    ) {
      return;
    }

    columns.forEach(item => {
      $(table).setColProp(item, {sortable: type});
    });
  }

  function processingHours (cell, opt, row) {
    let html =
      `<p name="openHandleTimeLine"
          data-rowId="${opt.rowId}"
          class="table-cell-action-span"
          style="cursor: pointer;"
          ><span class="dealLink">${cell}</span></div>`;
    return html;
  }

  //计算故障描述渲染方式
  function computedFaultDeion (cell, opt, row) {
    let html = '<p style="cursor: pointer;">';
    if (cell) {
      let isEmergency = row.faultEmergency < 3 ? true : false,
        isReback =
          (row.backRemark ? true : false) && row.workStatusName == '待派单',
        isNotHandled =
          (row.noPassCounts > 0 ? true : false) &&
          row.workStatusName == '处理中';
      if(row.workSheetType == 5){
        let url = encodeURI('/view-new/processView/see/index.html?' + 'workflowNo=' + row.workflowNo + '&businessId=' + row.lbusinessId + '&wfInstanceId=' + row.workflowInstId + '&currentStepNo=end' + '&role=deal&type=see');
        html += `<a class="come-from-tech" target="_blank" href="${url}" >
          <img src="../../static/img/other/from_process.svg" style="height: 22px;" />  
        </a>`
      }
      html +=
        (isEmergency
          ? row.faultEmergency == 2
            ? `<span 
                          title="影响范围：${affectScope[row.faultAffectScope]}"
                          style="
                            display: inline-block; 
                            background: #FFEFDF;
                            border-radius: 9px;
                            border: 1px solid #FF6010;
                            color:#FF6010; 
                            line-height: 17px;
                            font-size: 12px;
                            padding: 0 9px;
                            "
                        >比较急</span>`
            : `<span 
                              title="影响范围：${
              affectScope[row.faultAffectScope]
            }"
                              style="
                                line-height: 17px;
                                display: inline-block; 
                                background: #FFEEEF;
                                border-radius: 9px;
                                border: 1px solid #F93A4A;
                                font-size: 12px;
                                color:#F93A4A;
                                padding: 0 9px;
                                "
                            >非常紧急</span>`
          : '') +
        (isReback
          ? `<span
                        title="退回${
            row.backRemark ? '，原因：' + row.backRemark : ''
          }"
                        style="
                          display: inline-block;
                          color: #999999;
                          line-height: 17px;
                          background: #F5F5F5;
                          border-radius: 9px;
                          border: 1px solid #999999;
                          font-size: 12px;
                          padding: 0 9px;
                          "
                      >退回</span>`
          : '') +
        (isNotHandled
          ? `<span
                        title="提单人退回${row.noPassCounts}次"
                        style="
                          display: inline-block;
                          color: #999999;
                          line-height: 17px;
                          background: #F5F5F5;
                          border-radius: 9px;
                          border: 1px solid #999999;
                          font-size: 12px;
                          padding: 0 9px;
                          "
                      >打回</span>`
          : '') +
        (Number(row.assistFlag) && ['2', '4'].indexOf(row.peopleType) >= 0
          ? `<span 
                      style="
                        display: inline-block; 
                        color: #00BFD0; 
                        background: #D4FFF1;
                        border-radius: 9px;
                        border: 1px solid #00BFD0;
                        line-height: 17px;
                        font-size: 12px;
                        padding: 0 9px;
                        "
                      >已协助</span>`
          : Number(row.theAssistFlag)
            ? `<span 
                          style="
                            display: inline-block; 
                            color: #00BFD0; 
                            background: #D4FFF1;
                            border-radius: 9px;
                            border: 1px solid #00BFD0;
                            line-height: 17px;
                            font-size: 12px;
                            padding: 0 9px;
                            "
                          >已协助</span>`
            : '') +
        `<span 
          class="dealLink"
          name="openFaultAllInfo" 
          data-rowId="${opt.rowId}"
          title="${cell}"
          >${cell}</span>`;
    }
    return html;
  }

  //重新排列jGrid表格展示顺序
  function computedColOrder (list = [], table) {
    table = '#myHandleOrder ' + table;
    let cols = $(table).jqGrid('getGridParam', 'colModel'),
      showCols = [],
      hiddenCols = [];
    cols.forEach((item, index) => {
      let listIndex = list.indexOf(item.label);
      listIndex >= 0 ? (showCols[listIndex] = index) : hiddenCols.push(index);
    });
    hiddenCols.shift();
    let newColList = [0];
    showCols.map(col => newColList.push(col));
    hiddenCols.map(col => newColList.push(col));

    $(table).remapColumns(newColList, true);
  }
});
