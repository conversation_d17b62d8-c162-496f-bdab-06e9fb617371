'use strict';
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        var lineData = [];
        var baseData = {};
        var filterData = [];
        layui.use(['form'], function () {
            layer.open({
                type: 1,
                title: '个人主页',
                closeBtn: 1,
                shadeClose: false,
                area: ['100%', '100%'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $.ajax({
                        type: 'post',
                        contentType: 'application/json; charset=utf-8',
                        url: common.url + '/ts-hrms/employee/getPeriodEmployeeDetail/' + opt.data.employeeId,
                        success: function (res) {
                            if (res.success && res.object) {
                                baseData = res.object;
//                                lineData.unshift(baseData);
//                                setFilterData();
                                $('#userTimeLine .baseInfo .img img').attr('src', res.object.avatar || '/public/images/img/tacitly.png');
                                $.each($('#userTimeLine [key]'), function (i, v) {
                                    var attr = $(this).attr('key');
                                    $(this).text(res.object[attr] || '');
                                });
                                
                            }
                        },
                    });
                    $.ajax({
                        type: 'get',
                        url: '/ts-hrms/employee/getLifeCycle/' + opt.data.employeeId,
                        success: function (res) {
                            if (res.success && res.object) {
                                lineData = lineData.concat(res.object);
                            }
                            setFilterData();
                        },
                    });
                },
            });
            var types = {
                1: '学历',
                2: '职称',
                3: '科研论文',
                4: '奖惩记录',
                5: '培训',
                7: '岗位信息',
                8: '入院',
            };
            function setFilterData(arr) {
                filterData = [];
                if (arr) {
                    for (var i = 0; i < lineData.length; i++) {
                        if (arr.indexOf(lineData[i].type) == -1) {
                            filterData.push(lineData[i]);
                        }
                    }
                } else {
                    filterData = lineData;
                }
                initTimeline();
            }
            function initTimeline() {
                var html = '';
                for (var i = 0; i < filterData.length; i++) {
                    var left = 'left';
                    left = i % 2 == 1 ? 'right' : 'left';
                    var t = filterData[i].type != 6 ? (types[filterData[i].type] ? types[filterData[i].type] : '入院') : filterData[i].title;
                    var con = '';
                    var isLast = i == filterData.length - 1 ? '' : '<div class="lLine"></div>';
                    var type = 1;
                    switch (filterData[i].type * 1) {
                        case 1:
                            type = 3;
                            con += '<div><span class="label">学历：</span><span class="con">' + (filterData[i].data.educationTypeText || '') + '</span></div>';
                            con += '<div><span class="label">形式：</span><span class="con">' + (filterData[i].data.learnWayText || '') + '</span></div>';
                            con += '<div><span class="label">学校：</span><span class="con">' + (filterData[i].data.schoolName || '') + '</span></div>';
                            con += '<div><span class="label">专业：</span><span class="con">' + (filterData[i].data.professional || '') + '</span></div>';
                            break;
                        case 2:
                            type = 2;
                            con += '<div><span class="label">职称：</span><span class="con">' + (filterData[i].data.jobtitleNameText || '') + '</span></div>';
                            con += '<div><span class="label">等级：</span><span class="con">' + (filterData[i].data.jobtitleLevelText || '') + '</span></div>';
                            con += '<div><span class="label">获取途径：</span><span class="con">' + (filterData[i].data.acceptMethodText || '') + '</span></div>';
                            break;
                        case 3:
                            type = 4;
                            con += '<div><span class="label">专业：</span><span class="con">' + (filterData[i].data.papersBooksSpeciality || '') + '</span></div>';
                            con += '<div><span class="label">标题：</span><span class="con">' + (filterData[i].data.papersBooksTitle || '') + '</span></div>';
                            con += '<div><span class="label">出版处：</span><span class="con">' + (filterData[i].data.publishingOffice || '') + '</span></div>';
                            break;
                        case 4:
                            type = 5;
                            con += '<div><span class="label">名称：</span><span class="con">' + (filterData[i].data.rewardPenaltyTitle || '') + '</span></div>';
                            con += '<div><span class="label">原因：</span><span class="con">' + (filterData[i].data.rewardPenaltyReason || '') + '</span></div>';
                            break;
                        case 5:
                            type = 6;
                            con += '<div><span class="label">规培名称：</span><span class="con">' + (filterData[i].data.profession || '') + '</span></div>';
                            con += '<div><span class="label">规培单位：</span><span class="con">' + (filterData[i].data.place || '') + '</span></div>';
                            con += '<div><span class="label">时间：</span><span class="con">' + (filterData[i].data.acquisitionDate || '') + '</span></div>';
                            break;
                        case 6:
                            if (['延聘', '返聘', '调动'].indexOf(filterData[i].title) != -1) {
                                con += '<div><span class="label">原科室：</span><span class="con">' + (filterData[i].data.oldOrgName || '') + '</span></div>';
                                // con += '<div><span class="label">原职务：</span><span class="con">' + lineData[i].data.execute + '</span></div>';
                                con += '<div><span class="label">现科室：</span><span class="con">' + (filterData[i].data.newOrgName || '') + '</span></div>';
                                // con += '<div><span class="label">现职务：</span><span class="con">' + lineData[i].data.execute + '</span></div>';
                                con += '<div><span class="label">生效日期：</span><span class="con">' + (filterData[i].data.effectiveDate || '') + '</span></div>';
                                con += '<div><span class="label">变动原因：</span><span class="con">' + (filterData[i].data.cause || '') + '</span></div>';
                            } else {
                                con += '<div><span class="label">类型：</span><span class="con">' + (filterData[i].data.cause || '') + '</span></div>';
                                con += '<div><span class="label">原因：</span><span class="con">' + (filterData[i].data.remark || '') + '</span></div>';
                            }
                            break;
                        case 7:
                            type = 7;
                            con += '<div><span class="label">岗位名称：</span><span class="con">' + (filterData[i].data.postCategory || '') + '</span></div>';
                            con += '<div><span class="label">职称等级：</span><span class="con">' + (filterData[i].data.postId || '') + '</span></div>';
                            con += '<div><span class="label">获取时间：</span><span class="con">' + (filterData[i].data.employDutyEquallyDate || '') + '</span></div>';
                            break;
                        case 8:
                            con += '<div><span class="label">科室：</span><span class="con">' + (filterData[i].data.orgName  || '')+ '</span></div>';
                          /*  con += '<div><span class="label">岗位类别：</span><span class="con">' + (filterData[i].data.postCategory  || '')+ '</span></div>';
                            con += '<div><span class="label">岗位等级：</span><span class="con">' + (filterData[i].data.postName  || '')+ '</span></div>'; */
                            con += '<div><span class="label">执业类别：</span><span class="con">' + (filterData[i].data.operationType || '') + '</span></div>';
                            con += '<div><span class="label">执业范围：</span><span class="con">' +( filterData[i].data.operationScope  || '')+ '</span></div>';
                            break;
                  /*      default:
                            con += '<div><span class="label">科室：</span><span class="con">' + filterData[i].orgName + '</span></div>';
//                          con += '<div><span class="label">职称：</span><span class="con">' + filterData[i].orgName+ '</span></div>';
                            con += '<div><span class="label">岗位：</span><span class="con">' + filterData[i].postCategory + '</span></div>';
                            con += '<div><span class="label">等级：</span><span class="con">' + filterData[i].postName + '</span></div>';
                            con += '<div><span class="label">执业类别：</span><span class="con">' + filterData[i].operationType + '</span></div>';
                            con += '<div><span class="label">执业范围：</span><span class="con">' + filterData[i].operationScope + '</span></div>';
                            break;*/
                    }
                    html +=
                        '<div class="timeLineBox ' +
                        left +
                        '">\
                            <div class="wLine"></div>' +
                        isLast +
                        '<div class="icon" type="' +
                        type +
                        '"></div>\
                            <div class="timeBox">\
                                <div class="title">' +
                        (filterData[i].sort || filterData[i].entryDate || '') +
                        ' ' +
                        t +
                        '</div>\
                                <div class="info">\
                                   ' +
                        con +
                        '\
                                </div>\
                            </div>\
                        </div>';
                }
                $('#userTimeLine .timeLine').html(html);
            }
            $('#userTimeLine')
                .off('click', '#closeScreen')
                .on('click', '#closeScreen', function () {
                    $('#userTimeLine .screenType').addClass('none');
                });
            $('#userTimeLine')
                .off('click', '#screenType')
                .on('click', '#screenType', function () {
                    $('#userTimeLine .screenType').removeClass('none');
                });
            $('#userTimeLine')
                .off('click', '.screenType .type')
                .on('click', '.screenType .type', function () {
                    $(this).toggleClass('sel');
                    var items = $('#userTimeLine .screenType .type.sel');
                    var arr = [];
                    for (var i = 0; i < items.length; i++) {
                        arr.push($(items[i]).attr('type'));
                    }
                    setFilterData(arr);
                });

            // 编辑员工档案
            $('#userTimeLine')
                .off('click', '#editUser')
                .on('click', '#editUser', function () {
                    $.quoteFun('personnelmgr/employee/modules/add', {
                        title: '编辑员工档案',
                        data: opt.data,
                    });
                });
        });
    };
});
