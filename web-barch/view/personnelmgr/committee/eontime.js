"use strict";
define(function(require, exports, module) {
	exports.init = function(opt, html) {
		layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload'], function() {

			var form = layui.form,
				laydate = layui.laydate,
				trasen = layui.trasen,
				upload = layui.upload;

			//附件上传
			var fileIds = [];
			var fileArr = [];
			var close = '';
			layer.open({
				type: 1,
				title: opt.title,
				closeBtn: 1,
				shadeClose: false,
				area: ['800px', '450px'],
				skin: 'yourclass',
				content: html,
				success: function(layero, index) {
					close = index;
					var disabled = '';
					if (opt.data) {
						disabled = 'readonly';
						if (opt.data.filesId) {
							opt.data.filesId = String(opt.data.filesId)
              fileIds = fileIds.concat(opt.data.filesId.split(','))
							$.ajax({
								url: common.url + '/ts-basics-bottom/fileAttachment/getByIds',
								method: 'get',
								async: false,
								data: {
									ids: opt.data.filesId
								},
								success: function(res) {
									if (res.success) {
										let filesHtml = '';
										let fileInfoArr = res.object.map(item => {
											return {
												fileName: item.id + '.' + item.fileExtension,
												fileExtension: item.fileExtension,
												fileUrl: '/ts-basics-bottom/fileAttachment/downloadFile/' + item.id,
												fileId: item.id,
												fileRealName: item.originalName
											}
										})

										fileInfoArr.forEach(item => {
											fileArr.push({
												fileName: item.fileName,
												fileUrl: '/ts-basics-bottom/fileAttachment/downloadFile/' + item.fileId,
											});
											var isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
											var isDoc = common.isDoc(item.fileName);

											filesHtml += isImg ? '<li>' + item.fileRealName + '<a class="previewA viewerImg" fileurl="' +
												item.fileId + '" href="javascript:void(0)">预览</a><a class="deleteA" attr-id="' + item.fileId +
												'" href="javascript:void(0)">删除</a></li>' : '';
											filesHtml += isDoc ? '<li>' + item.fileRealName +
												'<a class="previewA viewerDocBase" filename="' + item.fileName + '" fileid="' + item.fileId +
												'" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/' + item.fileId +
												'" href="javascript:void(0)">预览</a><a class="deleteA" attr-id="' + item.fileId +
												'" href="javascript:void(0)">删除</a></li>' : '';
										})
										$("#fileListUl").append(filesHtml);
									}
								}
							})
						}
						trasen.setNamesVal($('#CommitteeEontimeForm'), opt.data);
						var dateStr = deleteHtmlTag(opt.data.forhowlong);
						var forhowlong = dateStr.split(" 至 ");
						$("#forhowlongStart").val(forhowlong[0]);
						$("#forhowlongEnd").val(forhowlong[1]);
						$("#CommitteeEontimeForm textarea").val(opt.data.remark);
					}

				 /* 	if (opt.optType && 'details' == opt.optType) {
						$("#ThesisAddForm input").prop("disabled", true);
						 $("#ThesisAddForm select").prop("disabled", true);
						$("#evaBaseSubmitCofirm").hide();
						$(".deleteA").hide();
						$("#fileUploadDiv").hide();
					} */
					
					form.render();
				}
			});
			
			
			laydate.render({
				elem: '#forhowlongStart'
			});
			
			laydate.render({
				elem: '#forhowlongEnd'
			});
			
			function deleteHtmlTag(str){
			 str = str.replace(/<[^>]+>|&[^>]+;/g,"").trim();//去掉所有的html标签和&nbsp;之类的特殊符合
			 return str;
			}

			upload.render({
				elem: '#fileUploadDiv',
				url: '/ts-basics-bottom/fileAttachment/upload?moduleName=hrm&scope=public',
				accept: 'file',
				exts: 'zip|rar|7z|jpg|png|gif|bmp|jpeg|doc|docx|xls|xlsx|pdf|ppt|pptx|txt',
				multiple: true,
				allDone: function(obj) {},
				done: function(res, index, upload) {
					var filesHtml = "";
					$.each(res.object, function(i, item) {
						fileArr.push({
							fileName: item.fileName,
							fileUrl: '/ts-basics-bottom/fileAttachment/downloadFile/' + item.fileId,
						});
						var isImg = /\.(gif|jpg|jpeg|png|GIF|JPG|PNG|bmp|BMP)$/.test(item.fileName);
						var isDoc = common.isDoc(item.fileName);

						filesHtml += isImg ? '<li>' + item.fileRealName + '<a class="previewA viewerImg" fileurl="' + item.fileId +
							'" href="javascript:void(0)">预览</a><a class="deleteA" attr-id="' + item.fileId +
							'" href="javascript:void(0)">删除</a></li>' : '';
						filesHtml += isDoc ? '<li>' + item.fileRealName + '<a class="previewA viewerDocBase" filename="' + item
							.fileName + '" fileid="' + item.fileId + '" fileurl="/ts-basics-bottom/fileAttachment/downloadFile/' +
							item.fileId + '" href="javascript:void(0)">预览</a><a class="deleteA" attr-id="' + item.fileId +
							'" href="javascript:void(0)">删除</a></li>' : '';

						fileIds.push(item.fileId);
					})
					$("#fileListUl").append(filesHtml);
				}
			});

			//图片预览
			$('#CommitteeEontimeForm').off('click', '.viewerImg').on('click', '.viewerImg', function(e) {
				common.viewerImg(fileArr, '/ts-basics-bottom/fileAttachment/downloadFile/' + $(this).attr('fileurl'), 'hr');
				e.stopPropagation();
				return false;
			});

			//删除附件
			$('body').off('click', '.deleteA').on('click', '.deleteA', function() {
				var fileId = $(this).attr("attr-id");
				if ($.inArray(fileId, fileIds) != -1) {
					fileIds.splice($.inArray(fileId, fileIds), 1);
				}
				$(this).parent().remove();
			});

			// 保存
			form.on('submit(evaBaseSubmitCofirm)', function(data) {
			var d = data.field;
				d.filesId = fileIds.join(",");
				var startD = $("#forhowlongStart").val();
				var endD = $("#forhowlongEnd").val();
				d.forhowlong = startD + " 至 " + endD;
				var url;
				if (d.id) {
					url = '/ts-hrms/committee/update';
				} else {
					url = '/ts-hrms/committee/save';
				}
				if (!d.id) {
					delete d.id;
				}
				$.loadings();
				$.ajax({
					type: "post",
					url: common.url + url,
					data: JSON.stringify(d),
					contentType: 'application/json;charset=UTF-8',
					success: function(res) {
						
						$.closeloadings();
						if (res.success) {
							
							layer.close(close);
							layer.msg(res.message || '操作成功');
							opt.ref();
						} else {
							layer.msg(res.message || '操作失败');
						}
					},
					error: function(res) {
						res = JSON.parse(res.responseText);
						layer.msg(res.message || '操作失败');
					}
				});
			});
		})
	};
});
