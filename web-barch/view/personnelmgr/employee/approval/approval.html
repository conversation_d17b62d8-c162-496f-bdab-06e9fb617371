<style>
    #personCustomApproval .top {
        position: absolute;
        z-index: 100;
        top: 0;
        left: 0;
        width: 100%;
        height: 60px;
        min-width: 1024px;
        background: #f2f2f2;
        box-sizing: border-box;
        padding: 0 20px;
    }
    
    #personCustomApproval .layui-form-radioed.layui-radio-disbaled > i {
        color: #5363F6 !important;
    }

    #personCustomApproval .layui-textarea {
        min-height: 40px !important;
        border: 1px solid transparent;
    }

    #personCustomApproval .firstNameBox {
        display: table;
        height: 45px;
        margin-top: 8px;
        float: left;
        margin-right: 15px;
    }

    #personCustomApproval #firstName {
        display: table-cell;
        width: 45px;
        height: 45px;
        /* line-height: 45px; */
        text-align: center;
        border-radius: 50%;
        background: #5260ff;
        /* margin-top: 6px; */
        /* margin-right: 15px; */
        color: #fff;
        vertical-align: middle;
    }

    #personCustomApproval .content-box {
        min-width: 1024px;
        max-width: 1780px;
        margin: 0 auto;
        position: relative;
        height: 100%;
    }

    #personCustomApproval .inner_content {
        margin-left: 130px;
        padding-top: 10px;
        padding-bottom: 10px;
        position: relative;
        box-sizing: border-box;
    }

    #personCustomApproval #leftNav {
        position: fixed;
        top: 70px;
        z-index: 99;
        background-color: #fff;
    }

    #personCustomApproval .nav {
        line-height: 40px;
        font-size: 14px;
        display: block;
        height: 40px;
        width: 120px;
        text-align: center;
        position: relative;
        border: 1px solid #ccc;
        border-bottom: none;
    }

    #personCustomApproval .nav a {
        padding: 0 10px;
        display: block;
    }

    #personCustomApproval .nav:nth-last-child(1) {
        border: 1px solid #ccc;
    }

    #personCustomApproval .nav.active {
        border-color: #5260ff;
    }

    #personCustomApproval .nav.active a {
        color: #fff;
        background-color: #5260ff;
    }

    #personCustomApproval #wfName {
        line-height: 36px;
        height: 36px;
        font-size: 16px;
    }

    #personCustomApproval #userInfo {
        color: #666;
    }

    #personCustomApproval #taskBox {
        float: right;
        width: 400px;
        height: 100%;
        overflow-y: auto;
    }

    #personCustomApproval .form-table {
        width: 100%;
        table-layout: fixed;
    }

    #personCustomApproval .form-table .hide{
        display: none;
    }

    #personCustomApproval .form-table table{
        width: 100%;
    }

    #personCustomApproval .textarea-box {
        vertical-align: top;
    }

    #personCustomApproval .person-avatar {
        width: 120px;
        height: 120px;
    }

    #personCustomApproval .form-table td {
        padding: 3px;
        white-space: nowrap;
        border: 1px solid #ccc;
        text-align: center;
        overflow: auto;
    }

    /* #personCustomApproval .form-table .children-table-no-border {
        border-right: 0px;
        border-bottom: 0px;
    }   

    #personCustomApproval .form-table .children-table-no-border table td{
        border-top: 0px;
        border-left: 0px;
    }

    #personCustomApproval .form-table .children-table-no-border table tr td:last-child{
        border-right: 0px;
    }

    #personCustomApproval .form-table tr:last-child .children-table-no-border{
        border-top: 0px !important;
    } */

    #personCustomApproval .after-td{
        background-color: rgb(242, 244, 255);
    }

    #personCustomApproval .form-table .white-space-wrap {
        white-space: wrap;
    }

    #personCustomApproval .trasen-upload {
      background-color: transparent;
    }

    #personCustomApproval .trasen-upload-list .success_file_icon {
      height: 16px;
      margin-bottom: 4px;
    }
</style>
<div class="layui-tab-content" id="personCustomApproval">
    <div class="top">
        <div class="firstNameBox">
            <span id="firstName">在办</span>
        </div>
        <div class="fl">
            <p id="wfName"></p>
            <p id="userInfo">
                <span>发起人：</span>
                <span style="margin-left: 10px">当前节点：</span>
            </p>
        </div>
    </div>
    <div class="layui-content-box" style="top: 60px; padding: 0">
        <div class="content-box">
            <div id="leftNav">
                <ul>
                    <li class="nav active">
                        <a href="javascript:;">流程表单</a>
                    </li>
                    <li class="nav">
                        <a href="javascript:;">流程信息</a>
                    </li>
                </ul>
            </div>
            <div class="layui-form inner_content">
                <table class="form-table">
                    <thead>
                        <tr>
                            <td style="width: 120px">字段名称</td>
                            <td>修改前</td>
                            <td>修改后</td>
                            <td style="width: 160px">审核结果</td>
                            <td style="width: 160px">原因说明</td>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
            <div class="inner_content none">
                <div id="taskBox"></div>
                <div id="wfBox" style="overflow: hidden"></div>
            </div>
        </div>
    </div>
    <div class="layer-btn archivesTabBtn">
        <!-- <button type="button" class="layui-btn layui-btn-normal" id="reminders">催办</button>
        <button type="button" class="layui-btn layui-btn-normal" id="cancel">撤销</button> -->
        <!-- <button type="button" class="layui-btn layui-btn-normal" id="back">退回</button> -->
        <button type="button" class="layui-btn layui-btn-green" id="agree">提交</button>
        <a href="javascript:;" class="layui-btn layui-btn-red" id="close">关闭</a>
    </div>
</div>
<!-- 不走公共方法，重写单独方法 -->
<!-- <script src="../../../../view-new/processView/core.js"></script> -->
<script type="text/html" id="formTableTem">
    {{# layui.each(d,function(index, item){ }}
    <tr class="{{item.className}}">
        <td class="white-space-wrap">{{item.showName}}</td>
        {{# if(item.updateType == 1){ }}
            {{# if(item.fileType == 'file'){ }}
                <td field-type="{{item.fileType}}">
                    <input type="text" class="none fileCom" value="{{item.beforeData}}" />
                </td>
                <td class="after-td" field-type="{{item.fileType}}">
                    <input type="text" class="none fileCom" value="{{item.afterData}}" />
                </td>
            {{# } else if(item.fieldName == 'avatar'){ }}
                <td field-type="{{item.fileType}}">
                    {{# if(item.beforeData){ }}
                        <img class="person-avatar" src="{{item.beforeData}}" alt="">
                    {{# } }}
                </td>
                <td class="after-td" field-type="{{item.fileType}}">
                    {{# if(item.afterData){ }}
                        <img class="person-avatar" src="{{item.afterData}}" alt="">
                    {{# } }}
                </td>
            {{#}else{ }}
                <td>{{item.beforeData}}</td>
                <td class="after-td">{{item.afterData}}</td>
            {{# } }}
        {{# } }}
        <!--  -->
        {{# if(item.updateType == 2){ }}
        <td colspan="2" class="children-table-no-border">
            <table>
                <thead>
                    <tr>
                        <td></td>
                        {{# layui.each(item.fields,function(n, col){ }}
                        <td class="white-space-wrap">{{col.showName}}</td>
                        {{# }) }}
                    </tr>
                </thead>

                <tbody>
                    {{# layui.each(item.beforeData,function(x, data){ }}
                    <tr>

                        {{# if(x == 0){ }}
                            <td rowspan="{{item.beforeData.length}}">修改前</td>
                        {{# } }}

                        {{# layui.each(item.fields,function(n, col){ }}
                        {{# if(col.fieldType == 'file'){ }}
                        <td field-type="{{col.fieldType}}"><input type="text" class="none fileCom"
                                value="{{data[col.fieldName] || col.fieldName}}" /></td>
                        {{#}else{ }}
                        <td field-type="{{col.fieldType}}">{{ data[col.fieldName] || '' }}</td>
                        {{# } }} {{# }) }}
                    </tr>
                    {{# }) }}


                    {{# layui.each(item.afterData,function(x, data){ }}
                    <tr class="after-td">

                        {{# if(x == 0){ }}
                            <td rowspan="{{item.afterData.length}}">修改后</td>
                        {{# } }}

                        {{# layui.each(item.fields,function(n, col){ }} {{# if(col.fieldType == 'file'){ }}
                        <td field-type="{{col.fieldType}}"><input type="text" class="none fileCom"
                                value="{{data[col.fieldName] || col.fieldName}}" /></td>
                        {{#}else{ }}
                        <td field-type="{{col.fieldType}}">{{ data[col.fieldName]||'' }}</td>
                        {{# } }} {{# }) }}
                    </tr>
                    {{# }) }}
                </tbody>
            </table>
        </td>
        {{# } }}
        <td class="check-item" name="{{item.fieldName}}">
            <input type="radio" name="{{item.fieldName}}" value="1" title="合格">
            <input type="radio" name="{{item.fieldName}}" value="2" title="不合格">
        </td>
        <td class="textarea-box">
            <textarea name="{{item.fieldName}}" class='layui-textarea' placeholder="请输入原因说明"></textarea>
        </td>
    </tr>
    {{# }) }}
</script>
