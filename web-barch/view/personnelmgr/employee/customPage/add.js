'use strict';
define(function (require, exports, module) {
  	exports.init = function (opt, html) {
    var utils = require('util');
		layui.use(['form', 'laytpl', 'upload', 'laydate', 'trasen'], function () {
			var trasen = layui.trasen;
			var form = layui.form;
			var laytpl = layui.laytpl;
			var upload = layui.upload;
			var laydate = layui.laydate;

			var buttonLoading = false;
			//计算工龄
			function getAge(start, end) {
				if (!start || !end) {
				return '';
				}
				var s = new Date(start).getTime();
				var e = new Date(end).getTime();
				var d = Math.floor((e - s) / (1000 * 60 * 60 * 24 * 365));
				d = d < 0 ? 0 : d;
				return d;
			}

			var basicGroupData = []; //最初始栏目信息
			var groupData = [];
			var customDataNum = {};
			var jobData = [];

			// 基础数据源
			var databaseData = common.deepClone(opt.data) || {};

			// 渲染后数据源 包含暂存 或者 审批中 审批驳回
			var formData = common.deepClone(opt.data) || {};

			// 流程驳回 驳回字段回显的 formData值
			var rejectFormData = null;
			// 流程通过 不合格字段回显的 formData值
			var passFormData = null;

			// 存储暂存后的数据 用来对比是否关闭弹窗提示
			var closeCheckStagingPageData = null;

			// 数据源转为 页面元素接口
			var formPageData = [];

			var personalIdentity = undefined;
			var editStatus = {};
			var formIds = [];
			var employeeId = opt.employeeId || '';
			var isSelf = employeeId == common.userInfo.id;

			getJobData();
			var wins = layer.open({
				type: 1,
				title: opt.title || '员工档案新增',
				closeBtn: 2,
				maxmin: false,
				shadeClose: false,
				area: ['100%', '100%'], //宽高
				skin: "personCustomAdd-dialog",
				content: html,
				success: function(layero, index) {
					handleSetPageOperate()

					if(formData && formData['1'] && formData['1'][0] && formData['1'][0].personal_identity) {
						personalIdentity = formData['1'][0].personal_identity;
					}					
					handleGetCustomData();
				},
			});

			function handleIsAdminAndProcessInfo() {
				let result = false;
				if(formData && formData['1'] && formData['1'][0] && formData['1'][0].employee_no) {
					let employeeNo = formData['1'][0].employee_no;
					$.ajax({
						url: `/ts-basics-bottom/cusotmEmployee/getEmployeeTask/${employeeNo}`,
						method: 'get',
						async: false,
						success: function(res) {
							if (res.success) {
								result = res.object;
							}
						},
					});
				}
				return result;
			}

			function getJobData() {
				$.ajax({
					url: '/ts-basics-bottom/jobtitleBasic/getJobtitleTree',
					method: 'post',
					async: false,
					success: function(res) {
						if (res.success && res.object) {
							jobData = res.object;
						}
					},
				});
			}


			function findJobData(pid, arr) {
				for (var i = 0; i < arr.length; i++) {
					if (arr[i].id == pid) {
						return arr[i];
					}
					if (arr[i].children && arr[i].children.length) {
						var d = findJobData(pid, arr[i].children);
						if (d) {
							return d;
						}
					}
				}
			}

			function handleSetPageOperate() {
				if(opt.type == 'preview') {
					$('#personCustomAdd #save').addClass('none')
				} else {
					$('#personCustomAdd #save').removeClass('none')
				}
			};

			function handleCheckInfoProcess() {
				$.ajax({
					url: `/ts-basics-bottom/employee/reviewStatus/${employeeId}`,
					method: "get",
					async: false,
					success: function (res) {
						if (res.success) {
							editStatus = res.object || {};
							// status 1 审批中   label展示红色 字段为审批中修改的值 禁止操作
							// status 2 审批成功  信息修改成功（审批项合格修改值、不合格修改失败）
							// status 3 驳回	   label区分颜色 信息修改不成功,展示驳回修改的值 审批项不合格字段展示tips
							if(editStatus) {
								switch (editStatus.status) {
									case '1':
										$('#personCustomAdd #ApprovalBox').show();
										$('#personCustomAdd #save').hide();
										break;
									case '2':
										// 保留审批提交不合格的数据
										if(editStatus.data && editStatus.data.length) {
											editStatus.data = editStatus.data.filter(f => f.auditStatus != 1);
										}
										$('#personCustomAdd #ApprovalPassBox').show();

										// 有提交不合格的数据 显示提示
										if(editStatus.data && editStatus.data.length) {
											$('#personCustomAdd #ApprovalPassBox .field-fail-tips').show();
										} else {
											$('#personCustomAdd #ApprovalPassBox .field-pass-tips').show();
										}
										break;
									case '3':
										$('#personCustomAdd #RejectStagingBox').show();
										$('#personCustomAdd #RejectStagingBox #RejectTips').show();
										break;
								}

								// 审批通过 驳回 显示暂存按钮
								if (editStatus.status != '1') $('#personCustomAdd #StagingBtn').show();
								(editStatus.data && editStatus.data.length > 0) && handleSetFormValue()
								if(editStatus && editStatus.status == '3' && editStatus.data.length) {
									rejectFormData = common.deepClone(formData);
								}
								if(editStatus && editStatus.status == '2' && editStatus.data.length) {
									passFormData = common.deepClone(formData);
								}
							}

							function handleSetFormValue() {
								editStatus.data.forEach(i => {
									if (i.updateType == 2 && i.afterData)
										i.afterData = JSON.parse(i.afterData);

									if (i.updateType == 1) {
										formData[i.groupId][0][i.fieldName] =  i.afterData
									} else {
										formData[i.groupId] = i.afterData
									}
								});
							};
						}
					}
				})
			}

			function handleRenderPendingList(type = 'record') {
				$.ajax({
					url: `/ts-basics-bottom/cusotmEmployee/getstorage/${employeeId}`,
					method: "get",
					async: false,
					success: function (res) {						
						if (res.success && res.object) {
							if(Object.keys(res.object).length) {
								let { content, storageDate} = res.object;

								// feat: 人员档案暂存记录明细为空 兼容处理
								let stagingContent = JSON.parse(content);
								let contentStagingData = stagingContent.stagingData || stagingContent;
								if(JSON.stringify(contentStagingData) == '{}' || Object.values(contentStagingData).some(s => JSON.stringify(s) == '[{}]')) {
									return false;
								}

								let $sdom = $('#personCustomAdd #StagingRecord')
								$sdom.show();
								$sdom.text(`上次暂存：${storageDate}`);

								if (type == 'echo') {
									if (editStatus.status != '2') {
										$('#personCustomAdd #RejectStagingBox').show();
										$('#personCustomAdd #RejectStagingBox #StagingTips').show();
									} else {
										$('#personCustomAdd #ApprovalPassBox').show();
										$('#personCustomAdd #ApprovalPassBox #StagingTips').show();
									}

									// let {stagingData, changeField} = JSON.parse(content);
									formData = common.deepClone(contentStagingData);
									setTimeout(() => {
										if(stagingContent.changeField) handleSetStagingHighlight(stagingContent.changeField)
									}, 800);
								}
							}
						}
					}
				})
			};
			
			// 获取表单配置权限
			function handleGetCustomData() {
				$.ajax({
					url: '/ts-basics-bottom/employeeField/getList',
					contentType: 'application/json',
					method: 'post',
					async: false,
					data: JSON.stringify({
						employeeId: opt.employeeId,
					}),
					success: function(res) {
						if (res.success) {
							basicGroupData = common.deepClone(res.object) || [];

							// 新增 编辑 去除技术档案栏目
							if (['add', 'edit'].includes(opt.type)) {
								let index = res.object.findIndex(item => item.id === 'jsda');
								if(index != -1) res.object.splice(index, 1);
							}

							// 根据岗位 查询栏目权限
							var showColumnId = [];
							if(personalIdentity) {
								$.ajax({
									url: `/ts-basics-bottom/api/columnAuthority/byPersonalIdentity/${personalIdentity}`,
									method: "get",
									async: false,
									success: function (res) {
										if (res.success) {
											showColumnId = res.object.map(item => item.columnId);
										}
									}
								})
							}

							// 岗位 栏目权限
							if (showColumnId && showColumnId.length > 0) {
								res.object = res.object.filter(item => showColumnId.includes(item.id));
							}

							groupData = common.deepClone(res.object) || [];

							// 编辑 登陆人==修改人 才有暂存权限 与 查看信息编辑审批权限
							// 在initFormData之前进行 设置暂存值、流程值
							if(opt.type == 'edit' && isSelf) {
								handleCheckInfoProcess();
								handleRenderPendingList('echo');
							}

							if (opt.type != 'add') initFormData();
							getCustomDataNum();

							common.globalSetting.orgCode == 'jssrmyy' && handleSetCustomOperate()

							initCon(); // render页面
						} else {
							layer.msg(res.message || '获取数据失败');
						}
					},
				});
			}

			//将表单数据赋值到解析模板上
			function initFormData() {
				for (var i = 0; i < groupData.length; i++) {
					var g = groupData[i];
					formIds.push({
						groupid: g.id,
						ids: [],
					});
					if (g.isDetailed == 1) {
						var d = formData[g.id] || [];
						g.dataList = [];
						for (var x = 0; x < g.fields.length; x++) {
							g.fields[x].isEdit = opt.type == 'preview' ? 0 : g.isEdit;
						}
						for (var x = 0; x < d.length; x++) {
							formIds[i].ids.push(d[x].id);
							var f = common.deepClone(g.fields);
							for (var z = 0; z < f.length; z++) {
								f[z].value = d[x][f[z].fieldName];
							}
							g.dataList.push(f);
						}
					} else {
						var d = (formData[g.id] && formData[g.id][0]) || {};
						for (var x = 0; x < g.fields.length; x++) {
							g.fields[x].value = d[g.fields[x].fieldName];
							opt.type == 'preview' && (g.fields[x].isEdit = 0);
						}
					}
				}
			}

			function initCon() {
				laytpl($('#customTemHtml').html()).render({
						type: opt.type || 'add',
						list: groupData,
						ids: formIds,
					},
					function(html) {
						html += '<button class="none" lay-submit lay-filter="personCustomAdd"></button>';
						$('#personCustomAdd .custom-con .layui-form').html(html);
					}
				);

				for (var i = 0; i < groupData.length; i++) {
					if (groupData[i].isDetailed != 1) {
						components(groupData[i].fields, $('#personCustomAdd .group-con[group-index="' + i + '"]'));
					} else {
						if(opt.type != 'add') {
							$.each($('#personCustomAdd .group-con[group-index="' + i + '"] .detail-item'), function(n, item) {
								var fields = groupData[i].dataList[n];
								components(fields, $(this));
							});
						} else {
							let num = customDataNum[Number(groupData[i].id)];
							if(num) {
								for(var j = 0; j < num; j++) {
									setRowHtml('init', groupData[i].fields, i)
								}
							}
						}
					}
				}

				// 为本人修改 且没有暂存记录 与 流程修改记录不为审批中
				if(opt.type == 'edit' && isSelf && editStatus.status != '1') {
					let addOpenFormItem = groupData.filter(f => f.showOpenBy === 1);
					for (let i = 0; i < addOpenFormItem.length; i++) {
						const e = addOpenFormItem[i];
						let findDom = $('#personCustomAdd').find(`.group-con[group-id=${e.id}]`);
						if (!findDom.length) continue;
						if (findDom.children('.detail-item').length === 0) {
							let renderItemType = e.showDelete ? 'init' : 'add';
							setRowHtml(renderItemType, e.fields, findDom.attr('group-index'));
						} else {
							if(e.showDelete) {
								findDom.children('.detail-item').eq(0).find('.del-icon').remove();
							}
						}
					}
				}
				form.render('select');
				form.render('radio');

				$('#personCustomAdd .custom-con h2').titleNav({
					container: $('#personCustomAdd .layui-content-box'),
					offsetTop: 48,
				});

				// 审批状态设置高亮
				if (editStatus && editStatus.data && editStatus.data.length > 0) {
					handleSetStatusHighlightOperate()
				}

				// 记录渲染后 数据源 用作提交、暂存对比
				formPageData = formDataFormatter(formData); 
			}

			function handleSetStatusHighlightOperate() {
				let $pdom = $('#personCustomAdd');
				let fieldItems = editStatus.data.filter(i => i.updateType == 1);
				let detailsItems = editStatus.data.filter(i => i.updateType == 2);
				if (editStatus.status == 1) {
					$pdom.find('.add-operate').hide();
					$pdom.find('.del-icon').hide();

					fieldItems.forEach(f => {
						let fd = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						$(fd).find(`.layui-form-label[field-item=${f.fieldName}]`).addClass('red');
					})
					detailsItems.forEach(f => {
						let fd = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						$(fd).find(`.group-name`).addClass('red');
					})
				}
				if (editStatus.status == 2 || editStatus.status == 3) {
					let fPass = fieldItems.filter(f => f.auditStatus == 1);
					let fOut = fieldItems.filter(f => f.auditStatus == 2);
					let dPass = detailsItems.filter(f => f.auditStatus == 1);
					let dOut = detailsItems.filter(f => f.auditStatus == 2);
					fPass.forEach(f => {
						let fd = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						$(fd).find(`.layui-form-label[field-item=${f.fieldName}]`).addClass('violet');
					})
					fOut.forEach(f => {
						let groupIdItem = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						let itemLabelDom = $(groupIdItem).find(`.layui-form-label[field-item=${f.fieldName}]`);
						itemLabelDom.addClass('red');
						f.remark && itemLabelDom.prepend(`<img class="remark-tips" src="/static/img/other/remark-tips.svg" remark="${f.remark}"/>`);
					})
					dPass.forEach(f => {
						let fd = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						$(fd).find(`.group-name`).addClass('violet');
					})
					dOut.forEach(f => {
						let groupIdItem = $pdom.find(`.group-con[group-id=${f.groupId}]`);
						let groupNameDom = $(groupIdItem).find(`.group-name`);
						groupNameDom.addClass('red');
						f.remark && groupNameDom.prepend(`<img class="remark-tips" src="/static/img/other/remark-tips.svg" remark="${f.remark}"/>`);
					})
					let remarkTips = null;
					$('.remark-tips').on({
						mouseenter: function() {
							let remark = $(this).attr('remark');
							remarkTips = layer.tips(`<span style="color: #fff;">${remark}</span>`, $(this), {tips:[1, '#FB4050']});
						},
						mouseleave: function() {
							layer.close(remarkTips);
						}
					})
				}
			}

			//模板解析
			function components(fields, $dom) {
				$dom.attr('filter-key', new Date().getTime());
				for (var i = 0; i < fields.length; i++) {
					if(
						(fields[i].isHide != 0 && fields[i].isAllowDeleted == 0) || 
						(fields[i].isDisabled != 0 && fields[i].isAllowDeleted == 1)
					) {
						continue;
					}

					var type = fields[i].fieldType;
					switch (type) {
						case 'input':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'showLabelRequired|required');
								}
								if (field.isOnly) {
									$el.prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('disabled', false);
									} else {
										$el.prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
								if (field.decimalDigit) {
									if ($el.attr('lay-verify')) {
										$el.attr('lay-verify', $el.attr('lay-verify') + '|' + field.decimalDigit);
									} else {
										$el.attr('lay-verify', field.decimalDigit);
									}
								}
								
								if (opt.type != 'add' && field.fieldName === 'employee_no') {
									$el.prop('disabled', true);
									$el.addClass("layui-disabled");
								}
							})(fields[i]);
							break;
						case 'number':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'showLabelRequired|required');
								}
								if (field.isOnly) {
									$el.prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('disabled', false);
									} else {
										$el.prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
							})(fields[i]);
							break;
						case 'radio':
							(function(field) {
								var name = field.fieldName;
								var arr = (field.optionValue && field.optionValue.split('|')) || [];
								var opts = '';
								for (var s = 0; s < arr.length; s++) {
									opts += '<input type="radio" name="' + name + '" value="' + arr[s].split(':')[0] + '" title="' + arr[s].split(
										':')[1] + '">';
								}
								$dom.find('[radio-name="' + name + '"]').html(opts);
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (field.value) {
									$dom.find('[name="' + name + '"][value="' + field.value + '"]').prop('checked', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
							})(fields[i]);
							break;
						case 'checkbox':
							(function(field) {
								var name = field.fieldName;
								var arr = (field.optionValue && field.optionValue.split('|')) || [];
								var opts = '';
								for (var s = 0; s < arr.length; s++) {
									opts += ' <label class="labelCheckbox">' + '<input type="checkbox" class="self-checkbox" name="' + name +
										'" value="' + arr[s].split(':')[0] + '" />' + '<i class="self-checkbox-icon"></i>' + arr[s].split(':')[
											1] + ' </label>';
								}
								$dom.find('[checkbox-name="' + name + '"]').html(opts);
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (field.value) {
									var vals = field.value.split(',');
									for (var v = 0; v < vals.length; v++) {
										$dom.find('[name="' + name + '"][value="' + vals[v] + '"]').prop('checked', true);
									}
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
							})(fields[i]);
							break;
						case 'img':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'fixedDom|showLabelRequired');
								}
								if (field.isOnly) {
									$dom.find('[img-upload="' + name + '"]').addClass('none');
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[img-upload="' + name + '"]').removeClass('none');
									} else {
										$dom.find('[img-upload="' + name + '"]').addClass('none');
									}
								}
								upload.render({
									elem: $dom.find(' [img-upload="' + name + '"]')[0], //绑定元素
									url: '/ts-basics-bottom/fileAttachment/upload?moduleName=hrm', //上传接口
									accept: 'images',
									acceptMime: 'image/*',
									done: function(res) {
										//上传完毕回调
										//如果上传失败
										if (res.code > 0) {
											return layer.msg('上传失败');
										} else {
											//上传成功
											$dom.find('[name="' + name + '"]').val(res.object[0].filePath);
											$dom.find('[img-name="' + name + '"]').attr('src', res.object[0].filePath);
											$dom.find('[img-name="' + name + '"]').parent().removeClass('require-border-color')
										}
									},
									error: function() {
										//请求异常回调
									},
								});
								if (field.value) {
									$el.val(field.value);
									$dom.find('[img-name="' + name + '"]').attr('src', field.value);
								}
							})(fields[i]);
							break;
						case 'date':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								var format = field.dataFormat || 'yyyy-MM-dd';
								var type = {
									'yyyy-MM': 'month',
									'yyyy-MM-dd': 'date',
									'yyyy-MM-dd HH:mm': 'datetime',
									'yyyy-MM-dd HH:mm:ss': 'datetime',
								};
								var classes = {
									'yyyy-MM-dd HH:mm': 'time_Hm',
								};
								laydate.render({
									elem: $dom.find('[name="' + name + '"]')[0],
									trigger: 'click',
									format: format,
									type: type[format],
									classes: classes[format],
									done: function(data) {
										var n = this.elem.attr('name');
										if (n == 'entry_date') {
											if (data) {
												$('[name="year_work"]').val(getAge(data, new Date().format('yyyy-MM-dd')));
											} else {
												$('[name="year_work"]').val('');
											}
										}
										if (n == 'work_start_date') {
											if (data) {
												$('[name="bdwlxgl"]').val(getAge(data, new Date().format('yyyy-MM-dd')));
											} else {
												$('[name="bdwlxgl"]').val('');
											}
										}
										if (n == 'positive_time') {
											if(data) {
												const value = new Date(data);
												const nowValue = new Date(new Date().format('yyyy-MM-dd'));

												if (value.getTime() > nowValue.getTime()) {
													$('[name="employee_status"]').val('99');
													$('[name="employee_status"]').parent().find('input').val('试用期');
												}
											}
										}
										if(n == 'start_employ_date') {
											if(data) {
												let hrHtscVal = $dom.find('[name="hr_htsc"]').val() || '';
												// 重新计算合同结束时间
												if(hrHtscVal >= 1){
													let startDate = $dom.find('[name="start_employ_date"]').val();
													let newEndDate = getData(startDate, hrHtscVal);
													$dom.find('[name="end_employ_date"]').val(newEndDate);
												}
												
											}
										}
									},
								});
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'showLabelRequired|required');
								}
                // if(['start_employ_date', 'end_employ_date'].includes(field.fieldName)) {
                //   let esType = fields.find(item=>item.fieldName == 'establishment_type') || {}
                //   if(['2', '6'].includes(esType.value)) {
                //     !$el.parent().siblings().find('span.required').length
                //       && $el.parent().siblings().prepend('<span class="required">*</span>');
                //     $el.attr('lay-verify', 'showLabelRequired|required');
                //   }
                // }
                let attrIsMust = field.oldIsMust >= 0 && String(field.oldIsMust) != 'null' ? field.oldIsMust : field.isMust;
                $el.attr('data-static-must',  attrIsMust);
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
							})(fields[i]);
							break;
						case 'select':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');

								if (field.fieldName == 'concurrent_position' || field.fieldName == 'position_id') {

									if (opt.type != 'add') {
										if (field.isEdit == 1) {
											$el.prop('disabled', false);
										} else {
											$el.prop('disabled', true);
										}
									}
									//兼任职务先写死。。。。。
									$.ajax({
										url: '/ts-basics-bottom/position/list',
										type: 'post',
										async: false,
										data: {
											page: 1,
											pageSize: 50
										},
										success: function(res) {
											if (res.rows) {
												var opts = '<option value="">请选择</option>';
												for (var i = 0; i < res.rows.length; i++) {
													opts += '<option value="' + res.rows[i].positionId + '">' + res.rows[i].positionName +
														'</option>';
												}
												$dom.find('[name="' + name + '"]').html(opts);
												if (field.value) {
													$el.val(field.value);
												}
												form.render('select');
											}
										},
									});
									return false;
								}
								if (field.fieldName == 'plgw') {
									$el.attr('lay-filter', 'plgwFilter');
									form.on('select(plgwFilter)', function(data) {
										$.ajax({
											url: '/ts-basics-bottom/post/getShowList',
											contentType: 'application/json',
											type: 'post',
											data: JSON.stringify({
												postCategory: data.value
											}),
											success: function(res) {
												if (res.object) {
													var opts = '<option value="">请选择</option>';
													for (var q = 0; q < res.object.length; q++) {
														opts += '<option value="' + res.object[q].postId + '">' + res.object[q].postName + '</option>';
													}
													$dom.find('[name="gwdj"]').html(opts);
													form.render('select');
												}
											},
										});
									});
									if (opt.type != 'add') {
										if (field.isEdit == 1) {
											$el.prop('disabled', false);
										} else {
											$el.prop('disabled', true);
										}
									}
								}
								if (field.fieldName == 'gwdj') {
									$.ajax({
										url: '/ts-basics-bottom/post/getShowList',
										contentType: 'application/json',
										type: 'post',
										data: JSON.stringify({
											postCategory: $('[name="plgw"]').val()
										}),
										success: function(res) {
											if (res.object) {
												var opts = '<option value="">请选择</option>';
												for (var q = 0; q < res.object.length; q++) {
													opts += '<option value="' + res.object[q].postId + '">' + res.object[q].postName + '</option>';
												}
												$dom.find('[name="gwdj"]').html(opts);
												if (field.value) {
													$el.val(field.value);
												}
												form.render('select');
											}
										},
									});
									if (opt.type != 'add') {
										if (field.isEdit == 1) {
											$el.prop('disabled', false);
										} else {
											$el.prop('disabled', true);
										}
									}
								}
                // 编制类型 为 合同制、劳务派遣 时，合同开始时间和合同结束时间必填
                if (field.fieldName == 'establishment_type') {
					if("jssrmyy" == common.globalSetting.orgCode){
						$el.attr('lay-filter', 'establishment_type');
															form.on('select(establishment_type)', function(data) {
						  let startDate = $dom.find('[name="start_employ_date"]'),
							endDate = $dom.find('[name="end_employ_date"]'),
							hrHtscVal = $('[name="hr_htsc"]' ,$el.closest('.group-con')).val();
							if(['2', '6'].includes(data.value) && hrHtscVal != '无固定期限') {
							!startDate.parent().siblings().find('span.required').length
							  && !startDate.parent().siblings().prepend('<span class="required">*</span>');
							startDate.attr('lay-verify', 'required');
						
							!endDate.parent().siblings().find('span.required').length
							  && !endDate.parent().siblings().prepend('<span class="required">*</span>');
							endDate.attr('lay-verify', 'required');
						  } else {
							if(startDate.attr('data-static-must') != 1) {
							  let startRequireSpan = startDate.parent().siblings().find('span.required');
							  startRequireSpan.length && startRequireSpan.remove();
							  startDate.attr('lay-verify', '');
							}
							if(endDate.attr('data-static-must') != 1) {
							  let endRequireSpan = endDate.parent().siblings().find('span.required');
							  endRequireSpan.length && endRequireSpan.remove();
							  endDate.attr('lay-verify', '');
							}
						  }
						})
					}
                }
								//合同时长 处理
								if (field.fieldName == 'hr_htsc') {
									
									if("jssrmyy" == common.globalSetting.orgCode){
										
										$el.attr('lay-filter', 'hr_htsc');
										form.on('select(hr_htsc)', function(data) {
											let startDate = $dom.find('[name="start_employ_date"]'),
											endDate = $dom.find('[name="end_employ_date"]');
											if(data.value == '无固定期限') {
												let endRequireSpan = endDate.parent().siblings().find('span.required');
												endRequireSpan.length && endRequireSpan.remove();
												endDate.attr('lay-verify', '');
												endDate.val('');
											} else {
												if(data.value >= 1 && startDate.val()) {
													let newStartDate = getData(startDate.val(), data.value);
													endDate.val(newStartDate);
												}
											let isMustt = endDate.attr('data-static-must') == 1,
												endRequireSpan = endDate.parent().siblings().find('span.required');
												!isMustt && endRequireSpan.length && endRequireSpan.remove();
												isMustt && !endDate.parent().siblings().find('span.required').length
													&& !endDate.parent().siblings().prepend('<span class="required">*</span>');
												endDate.attr('lay-verify', isMustt ? 'required' : '');
											}
										})
									}
								}
		
								//薪级类别
								if (field.fieldName == 'salary_level_type') {
									$.ajax({
										url: '/ts-hrms/dict/combobox/salary_level_category',
										contentType: 'application/json',
										type: 'post',
										data: JSON.stringify({}),
										success: function(res) {
											if (res.object) {
												var opts = '<option value="">请选择</option>';
												for (var q = 0; q < res.object.length; q++) {
													opts += '<option value="' + res.object[q].dictValue + '">' + res.object[q].dictName + '</option>';
												}
												$dom.find('[name="salary_level_type"]').html(opts);
												if (field.value) {
													$el.val(field.value);
												}
												form.render('select');

												$el.attr('lay-filter', 'salary_level_type_Filter');
												form.on('select(salary_level_type_Filter)', function(data) {

													$.ajax({
														url: '/ts-hrms/salaryLevel/combobox',
														contentType: 'application/json',
														type: 'post',
														data: JSON.stringify({
															salaryLevelCategory: data.value
														}),
														success: function(res) {
															if (res.object) {
																var opts = '<option value="">请选择</option>';
																for (var q = 0; q < res.object.length; q++) {
																	opts += '<option value="' + res.object[q].salaryLevelId + '" data-type="' + res.object[q]
																		.salaryLevelCategory + '">' + res.object[q].salaryLevelName + '</option>';
																}
																$dom.find('[name="salary_level_id"]').html(opts);
																form.render('select');
															}
														},
													});
												});

											}
										},
									});
								}
								// 薪级等级
								if (field.fieldName == 'salary_level_id') {
									$.ajax({
										url: '/ts-hrms/salaryLevel/combobox',
										contentType: 'application/json',
										type: 'post',
										data: JSON.stringify({}),
										success: function(res) {
											if (res.object) {
												var opts = '<option value="">请选择</option>';
												for (var q = 0; q < res.object.length; q++) {
													opts += '<option value="' + res.object[q].salaryLevelId + '" data-type="' + res.object[q].salaryLevelCategory +
														'">' + res.object[q].salaryLevelName + '</option>';
												}
												$dom.find('[name="salary_level_id"]').html(opts);
												if (field.value) {
													$el.val(field.value);
												}
												form.render('select');

												$el.attr('lay-filter', 'salary_level_id_Filter');
												form.on('select(salary_level_id_Filter)', function(data) {
													var type = $(data.elem).find('option[value="' + data.value + '"]').attr('data-type');
													$.ajax({
														url: 'ts-hrms/salaryLevelWage/getNewSalaryLeveCombobox',
														contentType: 'application/json',
														type: 'post',
														data: JSON.stringify({
															salaryLevelCategory: type
														}),
														success: function(res) {
															if (res.object) {
																var opts = '<option value="">请选择</option>';
																for (var q = 0; q < res.object.length; q++) {
																	opts += '<option value="' + res.object[q].salaryLevelId + '">' + res.object[q].salaryLevelName +
																		'</option>';
																}
																$dom.find('[name="salary_levelwage_id"]').html(opts);
																form.render('select');
															}
														},
													});
												});

											}
										},
									});
								}
								//薪级工资
								/*        if (field.fieldName == 'salary_levelwage_id') {
											$.ajax({
												url: 'ts-hrms/salaryLevelWage/getNewSalaryLeveCombobox',
												contentType: 'application/json',
												type: 'post',
												data: JSON.stringify({ salaryLevelCategory: $dom.find('option[value="' + $('[name="salary_level_id"]').val() + '"]').attr('data-type') }),
												success: function (res) {
													if (res.object) {
														var opts = '<option value="">请选择</option>';
														for (var q = 0; q < res.object.length; q++) {
															opts += '<option value="' + res.object[q].salaryLevelId + '">' + res.object[q].salaryLevelName + '</option>';
														}
														$dom.find('[name="salary_levelwage_id"]').html(opts);
														if (field.value) {
															$el.val(field.value);
														}
														form.render('select');
													}
												},
											});
										} */
                // 编辑类型为 learn_way 学习方式 时， 如果选中值是 自考（4） 学制（school_system），入学时间（start_time） 非必填
                if(field.fieldName == 'learn_way') {
                  let filterKey = utils.guid();
                  $el.attr('lay-filter', filterKey);
                  let relatedField = fields.filter(item=>['school_system', 'start_time'].includes(item.fieldName));
									form.on(`select(${filterKey})`, function(data) {
                    let isSelfTaught = data.value == 4;
                    relatedField.map(rField => {
                      let relatedDom = $(`[lay-filter="${filterKey}"]`).closest('.detail-item').find(`[name="${rField.fieldName}"]`);
                      if(isSelfTaught || (!isSelfTaught && rField.oldIsMust != 1)) {
                        relatedDom.parent().siblings().find('span.required').remove();
                        relatedDom.attr('lay-verify', '');
                      } else {
                        !relatedDom.parent().siblings().find('span.required').length
                        && relatedDom.parent().siblings().prepend('<span class="required">*</span>')
                        relatedDom.attr('lay-verify', 'required');
                      }
                    })
                  })
                }

								if (field.dataSource == 1) {
									var opts = '<option value="">请选择</option>';
									var arr = (field.optionValue && field.optionValue.split('|')) || [];
									for (var s = 0; s < arr.length; s++) {
										opts += '<option value="' + arr[s].split(':')[0] + '">' + arr[s].split(':')[1] + '</option>';
									}
									$dom.find('[name="' + name + '"]').html(opts);
								} else if (field.dataSource == 4) {
									$.ajax({
										url: '/ts-basics-bottom/dictItem/getDictItemByTypeCode',
										method: 'get',
										data: {
											typeCode: field.dictSource,
										},
										success: function(res) {
											if (res.success) {
												var html = '<option value="">请选择</option>';
												var list = res.object || [];
												for (var i = 0; i < list.length; i++) {
													html += '<option value="' + list[i].itemNameValue + '">' + list[i].itemName + '</option>';
												}

												$dom.find('[name="' + name + '"]').html(html);

												// select为政治面貌 且 入党时间不为必填 添加事件 写死
												if (field.fieldName == 'political_status') {

													if (field.value === '1') {
														$dom.find('[name="party_date"]').parent().siblings().prepend('<span class="required">*</span>');
														$dom.find('[name="party_date"]').attr('lay-verify', 'required');
													}

													$($el).change(function () {
														// 中共党员 入党时间为必填
														if($(this).val() === '1') {
															const labelItem = $dom.find('[name="party_date"]').parent().siblings();
															const requiredLength = labelItem.find('.required').length
															if(requiredLength === 0) {
																labelItem.prepend('<span class="required">*</span>');
															}
															$dom.find('[name="party_date"]').attr('lay-verify', 'required');
														} else {
															$dom.find('[name="party_date"]').parent().siblings().children().eq(0).remove();
															$dom.find('[name="party_date"]').attr('lay-verify', '');

															$dom.find('[name="party_date"]').val('');
														}
													})
												}
								
												if (field.value) {
													$el.val(field.value);
												}

												form.render('select');
											}
										},
									});
								}
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
							})(fields[i]);
							break;
						case 'jobType_1':
							(function(field, $dom) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								$el.attr('lay-filter', 'jobType_1_' + $dom.attr('filter-key'));
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
								if (field.value) {
									$el.data('data', field.value);
								}
								form.on('select(jobType_1_' + $dom.attr('filter-key') + ')', function() {
									initJobType2($dom);
								});
							})(fields[i], $dom);
							break;
						case 'jobType_2':
							(function(field, $dom) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								$el.attr('lay-filter', 'jobType_2_' + $dom.attr('filter-key'));
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
								if (field.value) {
									$el.data('data', field.value);
								}
								form.on('select(jobType_2_' + $dom.attr('filter-key') + ')', function() {
									initJobType3($dom);
								});
							})(fields[i], $dom);
							break;
						case 'jobType_3':
							(function(field, $dom) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								$el.attr('lay-filter', 'jobType_3_' + $dom.attr('filter-key'));
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$dom.find('[name="' + name + '"]').prop('disabled', false);
									} else {
										$dom.find('[name="' + name + '"]').prop('disabled', true);
									}
								}
								if (field.value) {
									$el.data('data', field.value);
								}
							})(fields[i], $dom);
							break;
						case 'textarea':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'showLabelRequired|required');
								}
								if (field.isOnly) {
									$el.prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('disabled', false);
									} else {
										$el.prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
							})(fields[i]);
							break;
						case 'file':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$el.prop('readonly', true);
								}
								if (field.value) {
									$el.val(field.value);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('readonly', false);
									} else {
										$el.prop('readonly', true);
									}
									trasen.uploadList($dom.find('[name="' + name + '"]'), {
										disabled: !field.isEdit,
										delete: !!field.isEdit,
										isConfirm: true,
									});
								} else {
									trasen.uploadList($dom.find('[name="' + name + '"]'), {
										disabled: !!field.isOnly,
										delete: !!!field.isOnly,
										isConfirm: true,
									});
								}
							})(fields[i]);
							break;
						case 'address':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'fixedDom|showLabelRequired|required');
								}
								if (field.isOnly) {
									$el.prop('disabled', true);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('disabled', false);
									} else {
										$el.prop('disabled', true);
									}
								}
								if (field.value) {
									$el.val(field.value);
								}
								trasen.cityAddress($el);
							})(fields[i]);
							break;
						case 'personChose':
							(function(field) {
								var name = field.fieldName;
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find('[name="' + name + '"]').prop('readonly', true);
								}
							})(fields[i]);
							break;
						case 'deptChose':
							(function(field) {
								var name = field.fieldName;
								var $el = $dom.find('[name="' + name + '"]');
								if (field.isMust == 1) {
									$el.attr('lay-verify', 'showLabelRequired|required');
									$el.prev().attr('lay-verify', 'required');
								}
								if (field.isOnly) {
									$el.prop('disabled', true);
									$el.prev().prop('disabled', true);
								}
								if (field.value) {
									$el.val(field.value);
								}
								if (opt.type != 'add') {
									if (field.isEdit == 1) {
										$el.prop('disabled', false);
										$el.prev().prop('disabled', false);
									} else {
										$el.prop('disabled', true);
										$el.prev().prop('disabled', true);
									}
								}
								trasen.deptSel.init($el, {
									textEl: $el.prev(),
								});
							})(fields[i]);
							break;
						case 'serialNumber':
							(function(field) {
								var name = field.fieldName;
								if (field.isMust == 1) {
									$dom.find('[name="' + name + '"]').attr('lay-verify', 'showLabelRequired|required');
								}
								if (field.isOnly) {
									$dom.find(' [name="' + name + '"]').prop('readonly', true);
								}
							})(fields[i]);
							break;
					}
				}
				$.each($dom.find('.jobType'), function() {
					initJobType1($(this), $dom);
				});
				form.render('select');
				form.render('radio');
			}

			//职称联动
			function initJobType1($el, $dom) {
				$el.append($('<option value="">请选择</option>'));
				for (var i = 0; i < jobData.length; i++) {
					$el.append($('<option value="' + jobData[i].id + '"></option>').text(jobData[i].name));
				}
				if ($el.data('data')) {
					$el.val($el.data('data'));
				}
				form.render('select');
				initJobType2($dom);
			}

			function initJobType2($dom) {
				var $el = $dom.find('[lay-filter="jobType_2_' + $dom.attr('filter-key') + '"]');
				var pid = $dom.find('[lay-filter="jobType_1_' + $dom.attr('filter-key') + '"]').val();
				if (!pid) {
					$el.html($('<option value="">请选择</option>'));
					initJobType3($dom);
					form.render('select');
					return;
				} else {
					var d = findJobData(pid, jobData).children || [];
					$el.html('');
					$el.append($('<option value="">请选择</option>'));
					for (var i = 0; i < d.length; i++) {
						$el.append($('<option value="' + d[i].id + '"></option>').text(d[i].name));
					}
					if ($el.data('data')) {
						$el.val($el.data('data'));
					}
					form.render('select');
					initJobType3($dom);
				}
			}

			function initJobType3($dom) {
				var $el = $dom.find('[lay-filter="jobType_3_' + $dom.attr('filter-key') + '"]');
				var pid = $dom.find('[lay-filter="jobType_2_' + $dom.attr('filter-key') + '"]').val();
				if (!pid) {
					$el.html($('<option value="">请选择</option>'));
					form.render('select');
					return;
				} else {
					var d = findJobData(pid, jobData).children || [];
					$el.html('');
					$el.append($('<option value="">请选择</option>'));
					for (var i = 0; i < d.length; i++) {
						$el.append($('<option value="' + d[i].id + '"></option>').text(d[i].name));
					}
					if ($el.data('data')) {
						$el.val($el.data('data'));
					}
					form.render('select');
				}
			}

			//新增子表行
			$('#personCustomAdd')
				.off('click', '.add-table-detail')
				.on('click', '.add-table-detail', function() {
					var key = new Date().getTime();
					var groupIndex = $(this).closest('.layui-row').attr('group-index');
					var selGroup = groupData[groupIndex];
					var tbody = $('#personCustomAdd .group-con[group-index="' + groupIndex + '"]').find('tbody');
					var fields = selGroup.fields;
					var tr = $('<tr row-key="' + key + '"></tr>');
					for (var i = 0; i < fields.length; i++) {
						if (fields[i].isHide == 0) {
							var td = $('<td></td>');
							laytpl($('#fieldTem').html()).render({
									type: opt.type || 'add',
									field: fields[i],
								},
								function(html) {
									td.html(html);
								}
							);
							tr.append(td);
						}
					}
					tr.append('<td><button class="layui-btn layui-btn-sm layui-btn-red table-detail-del">删除</button></td>');
					tbody.append(tr);
					components(fields, tr);
					return false;
				});

			//删除
			$('#personCustomAdd')
				.off('click', '.table-detail-del')
				.on('click', '.table-detail-del', function() {
					const vm = this;
					layer.confirm('确定要删除该条数据吗?', {
						btn: ['确定', '取消'],
						title: '提示',
						closeBtn: 0
					}, function(index) {
						layer.close(index)
						$(vm).closest('tr').remove();

					}, function () {});
					return false;
				});

			// 2.0 展开
			$('#personCustomAdd')
				.off('click', '.open-icon')
				.on('click', '.open-icon', function() {
					$(this).closest('.detail-item').toggleClass('open')
					return false;
				});

			// 2.0 新增子表行
			$('#personCustomAdd')
				.off('click', '.item-heard .add-operate')
				.on('click', '.item-heard .add-operate', function() {
					let groupIndex = $(this).closest('.layui-row').attr('group-index');
					let selGroup = groupData[groupIndex];
					let fields = selGroup.fields;
					setRowHtml('add', fields, groupIndex);
				});

			function setRowHtml(type, fields, groupIndex) {
				let key = new Date().getTime();
				let rowItem = $('#personCustomAdd .layui-row[group-index="' + groupIndex + '"]');
				let formHtml = 
					`<div class='detail-item open' group-index=${groupIndex} row-key=${key}>
						<img class="open-icon ${fields.length <= 3 ? 'hidden': ''}" src="/static/img/other/open.svg" />`
				let formItemDom = ''
				for (let i = 0; i < fields.length; i++) {
					const fileldItem = fields[i];
					let dom = '';
					if (
						(fileldItem.isHide == 0 && fileldItem.isAllowDeleted == 0) ||
						(fileldItem.isDisabled == 0 && fileldItem.isAllowDeleted == 1)
					) {
						if (fileldItem.isHide == 0) {
							laytpl($('#fieldTem').html()).render({
								type: opt.type || 'add',
								field: fileldItem
							},
							function(render) {
								dom = render;
							});
						}
						let colValue = fileldItem.fieldType == 'textarea' || fileldItem.fieldType == 'file' ? 12 : 4;
						formItemDom += 
						`<div class="layui-col-md${colValue} detail-form-item">
							<div class="layui-form-item" style="width: 100%">
							<label class="layui-form-label custom-form-label">
								${fileldItem.isMust === 1 ? '<span class="required">*</span>' : ''}
								${ fileldItem.showName }
							</label>
							<div class="layui-input-block custom-form-input-block" field-item="${fileldItem.fieldName}">
								${dom}
							</div>
						</div>
						</div>`
					}
				}
				formHtml += `
					${formItemDom}
					${type == 'add' ? '<img class="del-icon" src="/static/img/other/del.svg" />' : ''}
				</div>`
				rowItem.append(formHtml);
				let formDom = $(`#personCustomAdd .layui-row[group-index=${groupIndex}] .detail-item[row-key=${key}]`);
				components(fields, formDom);
			}

			// 2.0删除
			$('#personCustomAdd')
				.off('click', '.del-icon')
				.on('click', '.del-icon', function() {
					const vm = this;
					layer.confirm('确定要删除该条数据吗?', {
						btn: ['确定', '取消'],
						title: '提示',
						closeBtn: 0
					}, function(index) {
						layer.close(index)
						$(vm).closest('.detail-item').remove();

					}, function () {});
					return false;
				});

				form.verify({
					// 验证跳转
					fixedDom: function(...arg) {
						let $dom = $(arg[1]),
							name = $dom.attr('name'),
							tagName = $dom.prop('tagName'),
							val = arg[0],
							boxScrollTop = $('#personCustomAdd .layui-content-box').scrollTop() - 100,
							scrollTop = undefined;

						if (val) return false;

						switch (tagName) {
							// 附件/头像
							case "INPUT":
								if (name == 'avatar') { // 头像
									$dom.siblings(".img-box").addClass('require-border-color');
								} else {
									// 附件
									let fileContainer = $dom.siblings('.trasen-upload');
									if (fileContainer.length) {
										fileContainer.find('.trasen-upload-top').addClass('require-border-color')
									}
								}
								break;
						}
						scrollTop = boxScrollTop + $dom.closest('.layui-form-item').offset().top;

						if(scrollTop != undefined) {
							$('#personCustomAdd .layui-content-box').animate({
								scrollTop
							});
						}
					},

					showLabelRequired: function(...arg) {
						let value = arg[0];
						// 获取title
						let cloneD = $(arg[1]).closest('.layui-form-item').find('.custom-form-label').clone();
						$(cloneD).find('.required').remove();
						let text = $(cloneD).text().trim();
						if(!value) {
							return `${text}必填！请补充提交`;
						}
					}
				})

			//验证用
			form.on('submit(personCustomAdd)', function () {
				if (!buttonLoading) {
					buttonLoading = true;
					$('#personCustomAdd #save').attr('disabled', 'disabled');
				} else {
					return false;
				}

				var data = dealFormData();
				if(!data) {
					buttonLoading = false;
					$('#personCustomAdd #save').removeAttr('disabled');
					return false;
				}

				// 设置栏目必填 校验
				if (personalIdentity) {
					let columnRequired = [];
					let columnName = '';
					$.ajax({
						url: `/ts-basics-bottom/api/columnAuthorityRequired/byPersonalIdentity/${personalIdentity}`,
						method: "get",
						async: false,
						success: function (res) {
							if (res.success) {
								let requiredColumnId = res.object.map(m => m.columnId);
								columnRequired = requiredColumnId.filter(s => {
									let find = data.customFileds.find(f => f.groupId == s) || {
										detailFields: []
									};
									return find.detailFields.length == 0;
								})

								let filterColumn = basicGroupData.filter(f => columnRequired.includes(f.id)) || {
									groupName: ''
								};
								columnName =  filterColumn.map(m => m.groupName).join(',');
							}
						}
					})

					if(columnRequired.length && columnRequired.length > 0) {
						layer.msg(`${columnName}至少有一条数据`);
						$('#personCustomAdd #save').removeAttr('disabled');
						buttonLoading = false;
						return false;
					}
				}

				// 学历判断
				let xl = data.customFileds.find(f => f.groupName == '学历信息')
				if (xl && xl.detailFields && xl.detailFields.length) {
					let xlResult = xl.detailFields.filter(f => {
						return f.find(fd => fd.fieldName == 'highest_level' && fd.value == '1');
					})
					if (xlResult.length == 0) {
						layer.msg('请设置最高学历');
						buttonLoading = false;
						$('#personCustomAdd #save').removeAttr('disabled');
						return false;
					}
					if (xlResult.length > 1) {
						layer.msg('学历信息只能有一个最高学历');
						buttonLoading = false;
						$('#personCustomAdd #save').removeAttr('disabled');
						return false;
					}
				}

				// 职称判断
				let zc = data.customFileds.find(f => f.groupName == '职称信息')
				if (zc && zc.detailFields && zc.detailFields.length) {
					let zcResult = zc.detailFields.filter(f => {
						return f.find(fd => fd.fieldName == 'highest_level' && fd.value == '1');
					})
					if (zcResult.length == 0) {
						layer.msg('请设置最高职称');
						buttonLoading = false;
						$('#personCustomAdd #save').removeAttr('disabled');
						return false;
					}
					if (zcResult.length > 1) {
						layer.msg('职称信息只能有一个最高职称');
						buttonLoading = false;
						$('#personCustomAdd #save').removeAttr('disabled');
						return false;
					}
				}

				let beforeDiff = formDataFormatter(databaseData); //提交 原数据对比页面数据
				let afterDiff = common.deepClone(data.customFileds);
				let diffResult = handleDiff(beforeDiff, afterDiff);

				if (JSON.stringify(diffResult) == '{}') {
					layer.confirm('您没有进行任何数据修改，不需要进行提交操作',{
						btn: ['确定'],
						title: '提示',
						closeBtn: 0
					}, function (index) {
						layer.close(index);
						buttonLoading = false;
						$('#personCustomAdd #save').removeAttr('disabled');
						return false;
					}); 
				} else {
					$.ajax({
						url: '/ts-basics-bottom/cusotmEmployee/save',
						contentType: 'application/json',
						method: 'post',
						data: JSON.stringify(data),
						async: false,
						success: function (res) {
							if (res.success) {
								let isAdminAndInfo = handleIsAdminAndProcessInfo();
								if (!isAdminAndInfo) {
									opt.ref && opt.ref();
									layer.msg('保存成功');
									layer.close(wins);
									return false;
								}

								let tips = '';
								if (isAdminAndInfo.isAdmin == 'true') {
									tips = '您已提交保存成功，管理员无需走审批流程';
								} else {
									let processText = '';
									if (isAdminAndInfo.stepName && isAdminAndInfo.assigneeNames) {
										processText = `当前审批人:${isAdminAndInfo.stepName}【${isAdminAndInfo.assigneeNames}】`;
									}
									tips = `
										<div>您已经提交成功，系统正在后台走流程审批</div>
										${processText}
									`;
								}

								layer.confirm(tips,{
									btn: ['确定'],
									title: '提示',
									closeBtn: 0
								}, function (index) {
									opt.ref && opt.ref();
									layer.msg('保存成功');
									layer.close(index);
									layer.close(wins);
								}); 
							} else {
								layer.msg(res.message || '保存失败');
							}
						},
						complete: function (res) {
							buttonLoading = false;
							$('#personCustomAdd #save').removeAttr('disabled');
						}
					});
				}
				return false;
			});

			function dealFormData(check = true) {
				var data = [];
				var groups = $('#personCustomAdd .group-con[group-index]');
				let flag = true;
				$.each(groups, function(i, n) {
					var group = groupData[i];
					var d = {
						groupId: group.id,
						isDetailed: group.isDetailed,
						employeeId: employeeId,
						fields: [],
						detailFields: [],
					};
					if (group.isDetailed != 1) {
						var f = trasen.getNamesVal($(this));
						for (var x = 0; x < group.fields.length; x++) {
							var field = group.fields[x];

							if (field.isHide == 0) {
								d.fields.push({
									id: field.id,
									fieldName: field.fieldName,
									showName: field.showName,
									value: f[field.fieldName] || '',
								});
							}
						}
					} else {
						var trs = $(this).find('.detail-item');
						$.each(trs, function(x, tr) {
							var sd = [];

							var f = trasen.getNamesVal($(this));
							for (var x = 0; x < group.fields.length; x++) {
								var field = group.fields[x];

								if (field.fieldType == 'file' && field.isMust == 1 && check) {

									let fileListItem = $('.trasen-upload[upload-id="' + f[field.fieldName] + '"] .trasen-upload-list').children(
										'li')
									if (fileListItem.length === 0) {
										layer.msg(group.groupName + field.showName + '必填');
										flag = false;

									}
								}
								if (field.isHide == 0) {
									sd.push({
										id: field.id,
										fieldName: field.fieldName,
										showName: field.showName,
										value: f[field.fieldName] || '',
									});
								}
							}
							if (sd.length) {
								d.detailFields.push(sd);
							}
						});
					}
					data.push(d);
				});
				if(flag){
					return {
						customFileds: data,
						employeeId: employeeId,
					};
				}else{
					return false;
				}

			}

			function formDataFormatter(data) {
				let formatterData = common.deepClone([]);

				let eachData = common.deepClone(data);
				groupData.forEach(group => {
					var d = {
						groupId: group.id,
						isDetailed: group.isDetailed,
						fields: [],
						detailFields: [],
					};
					if (group.isDetailed != 1) {
						var basicData = (eachData[group.id] && eachData[group.id][0]) || {};
						for (var x = 0; x < group.fields.length; x++) {
							var field = group.fields[x];
							if (field.isHide == 0) {
								d.fields.push({
									id: field.id,
									fieldName: field.fieldName,
									showName: field.showName,
									value: basicData[field.fieldName] || '',
								});
							}
						}
					} else {
						var childBaseData = eachData[group.id] || [];
						childBaseData.forEach(row => {
							var sd = [];

							for (var x = 0; x < group.fields.length; x++) {
								var field = group.fields[x];
								if (field.isHide == 0) {
									sd.push({
										id: field.id,
										fieldName: field.fieldName,
										showName: field.showName,
										value: row[field.fieldName] || '',
									});
								}
							}
							if (sd.length) {
								d.detailFields.push(sd);
							}
						});
					}
					formatterData.push(d);
				});
				return formatterData;
			};

			//保存人员信息
			$('#personCustomAdd #save')
				.off('click')
				.on('click', function() {
					$('[lay-filter="personCustomAdd"]').trigger('click');
					return false;
				});


			//暂存人员信息
			$('#personCustomAdd #StagingBtn')
				.off('click')
				.on('click', function() {
					let $el = $(this);
					$el.prop('disabled', true);

					let {customFileds = []} = dealFormData(false); //暂存不需要校验信息
					let stagingPageData = common.deepClone(customFileds);
					
					let beforeDiff = formDataFormatter(databaseData); //暂存 原数据对比页面数据 记录修改的字段 回显高亮
					// 暂存时有驳回情况，使用驳回数据更新后 进行对比；
					if(editStatus && editStatus.status == '3' && editStatus.data.length && rejectFormData) {
						beforeDiff = formDataFormatter(rejectFormData);
					}
					// 暂存时有通过 不合格字段情况，使用通过数据更新后 进行对比；
					if(editStatus && editStatus.status == '2' && editStatus.data.length && passFormData) {
						beforeDiff = formDataFormatter(passFormData);
					}

					let afterDiff = common.deepClone(stagingPageData);
					let changeField = handleDiff(beforeDiff, afterDiff);

					let stagingData = common.deepClone({});					
					stagingPageData.forEach(i => {
						stagingData[i.groupId] = [];

						if (i.isDetailed == 1) {
							i.detailFields.forEach(j => {
								let data = common.deepClone({});
									j.forEach(({fieldName,value}) => data[fieldName] = value)
									stagingData[i.groupId].push(data);
							})
						} else {
							let data = common.deepClone({});
							i.fields.forEach(({fieldName,value}) => data[fieldName] = value)
							stagingData[i.groupId].push(data);
						}
					});

					$.ajax({
						url: `/ts-basics-bottom/cusotmEmployee/storage`,
						method: "post",
						contentType: "application/json",
						data: JSON.stringify({
							employeeId: opt.employeeId,
							content: JSON.stringify({
								stagingData,
								changeField 
							})
						}),
						async: false,
						success: function (res) {
							if (res.success) {
								layer.msg('暂存数据成功!');
								// 记录这一次暂存时候的页面数据 用来关闭时候对比
								closeCheckStagingPageData = common.deepClone(stagingPageData);
								handleRenderPendingList();
								setTimeout(() => {
									$el.prop('disabled', false);
								}, 100);
							}
						},
						error: function (res) {
							setTimeout(() => {
								$el.prop('disabled', false);
							}, 100);
						}
					})
					return false;
				});

				// 给暂存字段添加组件高亮底色
				function handleSetStagingHighlight(setField) {
					for (const key in setField) {
						const group = setField[key];
						const $group = $(`#personCustomAdd`).find(`.group-con[group-id=${key}]`);
						let findItem = groupData.find(f => f.id == key);

						if (Object.prototype.toString.call(group) === '[object Object]') {
							for (const field in group) {
								let fieldType = (findItem.fields.find(fItem => fItem.fieldName == field) || {}).fieldType;
								handleOperateHighlightDom($group, field, fieldType)
							}

						} else {
							group.forEach((row, index) => {
								if(typeof row != 'boolean' && row) {
									for (const field in row) {
										let fieldType = (findItem.fields.find(fItem => fItem.fieldName == field) || {}).fieldType;
										let $childrenDom = $group.find('.detail-item').eq(index);
										handleOperateHighlightDom($childrenDom, field, fieldType)
									}
								}
							})
						}

						function handleOperateHighlightDom($dom, field, fieldType) {
							switch (fieldType) {
								case 'input':
								case 'textarea':
								case 'number':
								case 'date':
									$dom.find(`[name=${field}]`).addClass('staging');
									break;
								case 'address':
								case 'select':
								case 'jobType_1':
								case 'jobType_2':
								case 'jobType_3':
									$dom.find(`[name=${field}]`).closest('.layui-form-item').find('.layui-form-select input').addClass('staging');
									break;
								case 'deptChose':
									$dom.find(`[name=${field}]`).siblings().addClass('staging');
									break;
							}
						}
					}
				}

			$('#personCustomAdd #close')
				.off('click')
				.on('click', function() {
					if (editStatus.status == '1') {
						layer.close(wins);
						return false;
					}

					// 页面数据
					let {customFileds = []} = dealFormData(false);
					let currentPageData = common.deepClone(customFileds);

					// 是否暂存过 
					if (closeCheckStagingPageData) {
						let stagingDataDiff = common.deepClone(closeCheckStagingPageData);
						let stagingDiffResult = handleDiff(stagingDataDiff, currentPageData);
						if (JSON.stringify(stagingDiffResult) == '{}') {
							layer.close(wins);
							return false;
						}
					}

					let beforeDiffData = common.deepClone(formPageData);
					let diffResult = handleDiff(beforeDiffData, currentPageData);
					if (JSON.stringify(diffResult) == '{}') {
						layer.close(wins);
						return false;
					}

					layer.confirm('您存在修改未提交的信息，是否继续退出？',{
						btn: ['确定', '取消'],
						title: '提示',
						closeBtn: 0
					}, function (index) {
							layer.close(wins);
							layer.close(index);
					}); 
					return false;
				});

			function handleSetCustomOperate() {
				groupData.forEach(group => {
					reSetIsMust(group.fields || []);
					// 由于在有初始数据的时候部分渲染使用了 dataList，所以dataList也要修改
					(group.dataList || []).forEach(datas => {
						reSetIsMust(datas || []);
					})
				})

				function reSetIsMust(fields) {
					// 编辑类型为 learn_way 学习方式 时， 如果选中值是 自考（4） 学制（school_system），入学时间（start_time） 非必填
					// 提前修改 groupData 内的数据，避免因为顺序打乱导致渲染错误
					if (fields.some(field => field.fieldName == 'learn_way')) {
						let relatedField = fields.filter(item =>
							['school_system', 'start_time'].includes(item.fieldName)
						),
						learnWay = fields.find(item => item.fieldName == 'learn_way') || {}
						relatedField.forEach(item => {
							item.oldIsMust = item.isMust;
							item.isMust = learnWay.value == 4 ? 0 : item.isMust;
						});
					}
					// 合同时长  为 '无固定期限' 时，使合同结束时间为非必填
					if(
						fields.some(field => field.fieldName == 'hr_htsc') &&
						fields.some(field => field.fieldName == 'end_employ_date')
					) {
						let hrHtsc = fields.find(item => item.fieldName == 'hr_htsc') || {},
							endEmploy = fields.find(field => field.fieldName == 'end_employ_date');
						endEmploy.oldIsMust = endEmploy.isMust;
						hrHtsc.value !== undefined && hrHtsc.value !== null && (endEmploy.isMust = hrHtsc.value == '无固定期限' ? 0 : 1);
					}
				}
			}

			// 益阳妇幼需求 某些项目默认条数
			function getCustomDataNum() {
				$.ajax({
					url: '/ts-hrms/individuation/getnumberconfig',
					contentType: 'application/json',
					method: 'get',
					async: false,
					success: function(res) {
						if (res.success) {
							customDataNum = res.object || {};
						} else {
						//	layer.msg(res.message || '获取数据失败');
						}
					},
				});
			}

			//身份证联动出生日期 退休日期 年龄 性别
			$('#personCustomAdd')
				.off('change', '[name="identity_number"]')
				.on('change', '[name="identity_number"]', function() {
					var idNumber = $(this).val();
					if (!checkIDCard(idNumber)) {
						layer.msg('身份证号有误!');
						return false;
					}

					// 获取性别
					function getGender(id) {
						var gender = '';
						if (id.length === 18) {
							gender = id.slice(16, 17) % 2 === 0 ? '1' : '0';
						} else if (id.length === 15) {
							gender = id.slice(14, 15) % 2 === 0 ? '1' : '0';
						}
						return gender;
					}
					var gender = getGender(idNumber);

					var birthday = idNumber.slice(6, 10) + '-' + idNumber.slice(10, 12) + '-' + idNumber.slice(12, 14);
					$('#personCustomAdd [name="gender"]').val(gender)
					form.render('select');

					$('#personCustomAdd [name="emp_age"]').val(dayjs().diff(dayjs(birthday), 'year'));

					$('#personCustomAdd [name="birthday"]').val(birthday);
					// 男 退休日期 为出生日期 增加60年
					// 女 退休日期 为出生日期 增加55年
					let retirement_time = gender == 0 ? dayjs(birthday).add(60, 'year') : dayjs(birthday).add(55, 'year');
					$('#personCustomAdd [name="retirement_time"]').val(retirement_time.format('YYYY-MM-DD'));
				});

			// 时间戳转换为年月日
  		// 时间戳转换为年月日
		  function getData(val,year) { 
			  var date = new Date(val);
			  date.setYear(Number(date.getFullYear()) + Number(year)) //增加一年
			  date.setDate(date.getDate() - 1)  //减少一天
			  var y=date.getFullYear();
			  var m=date.getMonth()+1;
			  var d=date.getDate();
			  m = m<10?("0"+m):m;
			  d = d<10?("0"+d):d;
			  return y + "-"+ m + "-"+d ;
			}

			//身份证校验
			function checkIDCard(idcode) {
				// 加权因子
				var weight_factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
				// 校验码
				var check_code = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];

				var code = idcode + '';
				var last = idcode[17]; //最后一位

				var seventeen = code.substring(0, 17);

				// ISO 7064:1983.MOD 11-2
				// 判断最后一位校验码是否正确
				var arr = seventeen.split('');
				var len = arr.length;
				var num = 0;
				for (var i = 0; i < len; i++) {
					num = num + arr[i] * weight_factor[i];
				}

				// 获取余数
				var resisue = num % 11;
				var last_no = check_code[resisue];

				// 格式的正则
				// 正则思路
				/*
				第一位不可能是0
				第二位到第六位可以是0-9
				第七位到第十位是年份，所以七八位为19或者20
				十一位和十二位是月份，这两位是01-12之间的数值
				十三位和十四位是日期，是从01-31之间的数值
				十五，十六，十七都是数字0-9
				十八位可能是数字0-9，也可能是X
				*/
				var idcard_patter =
					/^[1-9][0-9]{5}([1][9][0-9]{2}|[2][0][0|1][0-9])([0][1-9]|[1][0|1|2])([0][1-9]|[1|2][0-9]|[3][0|1])[0-9]{3}([0-9]|[X])$/;

				// 判断格式是否正确
				var format = idcard_patter.test(idcode);

				// 返回验证结果，校验码和格式同时正确才算是合法的身份证号码
				return last === last_no && format ? true : false;
			}

			function handleDiff(before, after) {
				const modifiedKeys = {};
			
				let beforeGroupIds = before.map((f) => f.groupId);
				let afterGroupIds = after.map((f) => f.groupId);
				// 取出并集
				let originGroupIds = afterGroupIds.filter((f) => beforeGroupIds.includes(f));
				
				for (let i = 0; i < originGroupIds.length; i++) {
					const id = originGroupIds[i];
			
					// 存在并集里 比字段
					if (originGroupIds.includes(id)) {
						let beforeObj = before.find((f) => f.groupId == id);
						let afterObj = after.find((f) => f.groupId == id);
						let diffItemResult = handleCompareObjects(beforeObj, afterObj);
						if(diffItemResult) {
							modifiedKeys[id] = diffItemResult;
						}
					} else {
						// 不存在并集里 为新增删除项目大类型 删除页面找不到元素不处理 找得到下面的字段全部高亮
						// 未碰到实际场景
						// modifiedKeys[id] = "addOrEditGroup";
					}
				}
				return modifiedKeys;
			}
			
			function handleCompareObjects(before, after) {
				// 子表
				if (before.isDetailed) {
					let detailFieldsBefore = (before.detailFields || []).map((f) => {
						let data = Object.create({});
						f.forEach(({ fieldName, value }) => (data[fieldName] = value));
						return data;
					});
			
					let detailFieldsAfter = (after.detailFields || []).map((f) => {
						let data = Object.create({});
						f.forEach(({ fieldName, value }) => (data[fieldName] = value));
						return data;
					});
			
					let arr = [];
					let forNum = Math.max(detailFieldsBefore.length, detailFieldsAfter.length);
					for (let i = 0; i < forNum; i++) {
						const beforeI = detailFieldsBefore[i] || {};
						const afterI = detailFieldsAfter[i] || {};
						arr.push(handleObjectDiff(beforeI, afterI));
					}


					if(arr.filter(Boolean).length == 0) return false;
					return arr;
				} else {
					// 基础表
					let beforeData = Object.create({});
					let afterData = Object.create({});
			
					before.fields.forEach(
						({ fieldName, value }) => (beforeData[fieldName] = value)
					);
					after.fields.forEach(
						({ fieldName, value }) => (afterData[fieldName] = value)
					);
			
					return handleObjectDiff(beforeData, afterData);
				}
			
				function handleObjectDiff(obj1, obj2) {
					const diffData = {};
					for (const key in obj1) {
						if (obj1.hasOwnProperty(key) && obj2.hasOwnProperty(key)) {
							if (obj1[key] !== obj2[key]) {
								diffData[key] = {
									before: obj1[key],
									after: obj2[key],
								};
							}
						} else if (obj1.hasOwnProperty(key) && !obj2.hasOwnProperty(key)) {
							diffData[key] = {
								before: obj1[key],
								after: undefined,
							};
						}
					}
		
					for (const key in obj2) {
						if (obj2.hasOwnProperty(key) && !obj1.hasOwnProperty(key)) {
							diffData[key] = {
								before: undefined,
								after: obj2[key],
							};
						}
					}

					if(Object.keys(diffData).length == 0) return false;
					return diffData;
				}
			}
		});
	};
});
