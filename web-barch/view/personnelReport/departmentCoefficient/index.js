'use strict';
define(function (require, exports, module) {
    var init = function () {
        return perform();
    };
    module.exports = {
        init: init,
    };
    var perform = function () {
        layui.use(['form', 'laytpl', 'tree', 'layedit', 'laydate', 'trasen', 'workflow','zTreeSearch','upload'], function () {
            var form = layui.form,
                laytpl = layui.laytpl,
                layer = layui.layer,
                workflow = layui.workflow,
                trasen = layui.trasen,
                upload = layui.upload,
                laydate = layui.laydate,
                zTreeSearch = layui.zTreeSearch;
			var tableData = [];
            var deptObj = {};
            var pOrg = {};
            var checkNodes = [];
            var treeObj;
            var setting = {
                data: {
                    simpleData: {
                        enable: false,
                    },
                },
                check: {
                    chkboxType: {
                        Y: 's',
                        N: 's',
                    },
                    enable: true,
                    chkStyle: 'checkbox',
                },
                callback: {
                    onClick: function (event, treeId, treeNode) {
                        return false;
                    },
                    onCheck: function (event, treeId, treeNode) {
                        var nodes = treeObj.getCheckedNodes(true);
                        var ids = [];
                        for (var i = 0; i < nodes.length; i++) {
                            ids.push(nodes[i].id);
                        }
                        getData(ids);
                    },
                    onNodeCreated: function (e, id, node) {},
                },
                view: {
                    dblClickExpand: true,
                    showTitle: true,
                    showLine: false,
                },
            };

            function initTree() {
                $.ajax({
                    url: common.url + '/ts-basics-bottom/organization/getTree2',
                    type: 'post',
                    success: function (res) {
                        if (res.success) {
                            var zNodes = res.object;
                            if (treeObj) {
                                treeObj.destroy();
                            }
                            treeObj = $.fn.zTree.init($('#departmentCoefficientOrgTree'), setting, zNodes);
                            treeObj.checkAllNodes(true);
                            var nodes = treeObj.getCheckedNodes(true);
                            var ids = [];
                            for (var i = 0; i < nodes.length; i++) {
                                ids.push(nodes[i].id);
                            }
                            getData(ids);
                        } else {
                            layer.msg('树加载失败.');
                        }
                    },
                });
            }
            initTree();

            function getData(ids) {
                $.ajax({
                    url: '/ts-hrms/orgdistribution/getCoefficient',
                    method: 'post',
                    contentType: 'application/json; charset=utf-8',
                    data: JSON.stringify(ids),
                    success: function (res) {
                        tableData = res.object || [];
                        deptObj = {};
                        pOrg = {};
                        dealDataRow();
                        tableTemp();
                    },
                });
            }

            function dealDataRow() {
                for (var i = 0; i < tableData.length; i++) {
                    if (!pOrg[tableData[i].pOrgId]) {
                        pOrg[tableData[i].pOrgId] = {
                            row: 0,
                            del: false,
                            persons: 0,
                            coefficient:0,
                        };
                    }
                    if (!deptObj[tableData[i].orgId]) {
                        deptObj[tableData[i].orgId] = {
                            row: 0,
                            persons: 0,
                            types: {},
                            coefficient:0,
                        };
                    }
                    if (!deptObj[tableData[i].orgId].types[tableData[i].postName]) {
                        deptObj[tableData[i].orgId].types[tableData[i].postName] = {
                            row: 0,
                            persons: 0,
                            coefficient:0,
                        };
                    }
                    
                    pOrg[tableData[i].pOrgId].row++;
                    pOrg[tableData[i].pOrgId].persons += tableData[i].empName.split(',').length;
                    if(tableData[i].coefficient) {
                        deptObj[tableData[i].orgId].coefficient += tableData[i].coefficient;
                    }
                    
                    deptObj[tableData[i].orgId].row++;
                    deptObj[tableData[i].orgId].types[tableData[i].postName].row++;
                    deptObj[tableData[i].orgId].types[tableData[i].postName].persons += tableData[i].empName.split(',').length;
                    deptObj[tableData[i].orgId].persons += tableData[i].empName.split(',').length;
                }
            }

            function tableTemp() {
                $('#departmentCoefficient .oa-table tbody').html('');
                var html = '';
                for (var i = 0; i < tableData.length; i++) {
                    var pO = 1;
                    var deptRow = 1;
                    var postRow = 1;
                    if (deptObj[tableData[i].orgId].row && deptObj[tableData[i].orgId].row > 1) {
                        if (!deptObj[tableData[i].orgId].del) {
                            deptRow = deptObj[tableData[i].orgId].row;
                            deptObj[tableData[i].orgId].del = true;
                        } else {
                            deptRow = 0;
                        }
                    }
                    if (deptObj[tableData[i].orgId].types[tableData[i].postName].row && deptObj[tableData[i].orgId].types[tableData[i].postName].row > 1) {
                        if (!deptObj[tableData[i].orgId].types[tableData[i].postName].del) {
                            postRow = deptObj[tableData[i].orgId].types[tableData[i].postName].row;
                            deptObj[tableData[i].orgId].types[tableData[i].postName].del = true;
                        } else {
                            postRow = 0;
                        }
                    }
                    if (pOrg[tableData[i].pOrgId].row && pOrg[tableData[i].pOrgId].row > 1) {
                        if (!pOrg[tableData[i].pOrgId].del) {
                            pO = pOrg[tableData[i].pOrgId].row;
                            pOrg[tableData[i].pOrgId].del = true;
                        } else {
                            pO = 0;
                        }
                    }
                    var person = deptObj[tableData[i].orgId].types[tableData[i].postName].persons;
                    html +=
                        '<tr>' +
                        (pO == 0 ? '' : '<td  rowspan="' + pO + '">' + tableData[i].pOrgName + '（' + pOrg[tableData[i].pOrgId].persons + '）' + '</td>') +
                        (deptRow == 0 ? '' : '<td  rowspan="' + deptRow + '">' + tableData[i].orgName + '（' + deptObj[tableData[i].orgId].persons + '）' + '</td>') +
                        (deptRow == 0 ? '' : '<td  rowspan="' + deptRow + '">' + Number(deptObj[tableData[i].orgId].coefficient).toFixed(2) + '</td>') +
                        (postRow == 0 ? '' : '<td  rowspan="' + postRow + '">' + (tableData[i].postName + '（' + person + '）') + '</td>') +
                        '<td>' +
                        (tableData[i].jobtitleName + '（' + tableData[i].empName.split(',').length + '）') +
                        '</td>' +
                        '<td >' +
                        nameTable(tableData[i].empName) +
                        '</td>' +
                        '</tr>';
                }

                $('#departmentCoefficient .oa-table tbody').html(html);
            }
			
			$('#departmentCoefficient')
			    .off('click', '.name-click')
			    .on('click', '.name-click', function () {
					var employeeId  = $(this).attr("name-value");
					var empName  = $(this).attr("name-empName");
					layer.prompt({
						formType: 0,
						value: '',
						title: '请输入 <font color="#FF0000">'+empName+'</font> 的绩效系数',
						btn: ['确定','取消'], //按钮，
						btnAlign: 'c'
					}, function(value,index){
						var content = {
							"employeeId": employeeId,
							"coefficient":value
						}
						$.ajax({
							type: 'POST',
							url: "/ts-hrms/employee/updateCoefficient",
							contentType: 'application/json; charset=utf-8',
							data: JSON.stringify(content),
							dataType: "json",
							success: function (data) {
								 layer.close(index);
								 initTree();
								 layer.msg('操作成功');
							}
						});
					},function (value,index) {
			 
					});
					
			    });
				
			

            function nameTable(str) {
                var nameArr = str ? str.split(',') : [];
                if (nameArr.length) {
                    var a = nameArr.length % 7;
                    if (a != 0) {
                        var arr = new Array(7 - a);
                        nameArr = [].concat(nameArr, arr);
                    }
                } else {
                    nameArr.length = 7;
                }
                var table = '<table class="user-table">';
                for (var i = 0; i < nameArr.length; i++) {
                    if (i % 7 == 0) {
                        if (i / 7 > 0) {
                            table += '</tr>';
                        }
                        table += '<tr>';
                    }
					var _index ='';
					if(nameArr[i] != undefined){
						_index = nameArr[i].indexOf("--");
						table += '<td class="name-click" name-empName="'+nameArr[i].substring(0,_index) +'"  name-value="'+nameArr[i].substring(_index + 2) +'">' + (nameArr[i].substring(0,_index) || '') + '</td>';
					}else{
						 table += '<td class="name-click" name-value="'+nameArr[i] +'">' + (nameArr[i] || '') + '</td>';
					}
                }
                table += '</table>';
                return table;
            }

            //导出

            $('#departmentCoefficient')
                .off('click', '#exportCoeBtn')
                .on('click', '#exportCoeBtn', function () {
                    tableToExcel('exportCoeTable', 'exportCoeBtn');
                });

            function tableToExcel(tableid, btnname) {
                var uri = 'data:application/vnd.ms-excel;base64,',
                    template =
                        '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40"><head><meta charset="UTF-8"><!--[if gte mso 9]><xml><x:ExcelWorkbook><x:ExcelWorksheets><x:ExcelWorksheet><x:Name>{worksheet}</x:Name><x:WorksheetOptions><x:DisplayGridlines/></x:WorksheetOptions></x:ExcelWorksheet></x:ExcelWorksheets></x:ExcelWorkbook></xml><![endif]--></head><body><table>{table}</table></body></html>',
                    base64 = function (s) {
                        return window.btoa(unescape(encodeURIComponent(s)));
                    },
                    format = function (s, c) {
                        return s.replace(/{(\w+)}/g, function (m, p) {
                            return c[p];
                        });
                    };
                //根据ID获取table表格HTML
                var table = document.getElementById(tableid);
                var ctx = { worksheet: 'Worksheet', table: table.innerHTML };

                var alink = document.createElement('a');
                alink.href = uri + base64(format(template, ctx));
                alink.download = '系数分布报表.xls';
                alink.click();
            }
        });
    };
});
