"use strict";
define(function (require, exports, module) {
    var init = function () {
        return perform();
    }
    module.exports = {
        init: init
    }

    var perform = function () {
        layui.use(['form', 'layedit', 'laydate', 'laytpl', 'upload', 'trasen'], function () {
            var form = layui.form,
                layer = layui.layer,
                layedit = layui.layedit,
                laytpl = layui.laytpl,
                upload = layui.upload,
                laydate = layui.laydate,
                trasen = layui.trasen;
            //表格渲染
            var trasenTable = new $.trasenTable("grid-table-systemProjectDevelopment", {
                url: common.url + '/ts-cp/projectInfo/list',
                pager: 'grid-pager-systemProjectDevelopment',
                sortname: "project_code",
                //表格字段
                colModel: [
                    {
                        label: 'ID',
                        name: 'projectInfoId',
                        index: 'project_info_id',
                        width: "auto",
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '项目编号',
                        name: 'projectCode',
                        index: 'project_code',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '项目名称',
                        name: 'projectName',
                        index: 'project_name',
                        width: 300,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '项目类型',
                        name: 'projectTypeValue',
                        index: 'project_type',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {label: '所属部门', name: 'deptName', index: 'dept_name', width: 150, align: "center", editable: false},
                    {label: '项目经理', name: 'pmName', index: 'pm_name', width: 150, align: "center", editable: false},
                    {label: '项目状态', name: 'statusValue', index: 'status', width: 150, align: "center", editable: false},
                    {
                        label: '相关人员',
                        name: 'refUserNameList',
                        index: 'refUserNameList',
                        width: 150,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '相关人员代码',
                        name: 'refUserCodeList',
                        index: 'refUserCodeList',
                        width: 150,
                        align: "center",
                        editable: false,
                        hidden: true
                    },
                    {
                        label: '创建人',
                        name: 'createUser',
                        index: 'create_user',
                        width: 150,
                        align: "center",
                        editable: false
                    },
                    {
                        label: '创建时间',
                        name: 'createDate',
                        index: 'create_date',
                        width: 150,
                        align: "center",
                        editable: false
                    }
                ],
                buidQueryParams: function () {
                    var search = $("#systemProjectDevelopmentqueryForm").serializeArray();
                    var opt = $("#systemProjectDevelopmentscreening").serializeArray();
                    var data = {};
                    for (var i in search) {
                        opt.push(search[i]);
                    }
                    for (var i in opt) {
                        data[opt[i].name] = opt[i].value;
                    }
                    return data;
                }
            });

            //表格刷新
            function refreshTable() {
                trasenTable.refresh();
            }

            initProjectType();
            initProjectStatus();

            $(document).on('click', '#cancel', function () {
                layer.closeAll('page');
            });

            //查询
            form.on('submit(systemProjectDevelopmentsearch)', function (data) {
                refreshTable()
            });


            form.on('radio(systemProjectDevelopmentaddProjectInfoFormDivDivbonusFilter)', function () {
                var isAccountingBonus = $(this).val();
                if (isAccountingBonus == 1) {
                    $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #bonusTypeDiv").show();
                } else if (isAccountingBonus == 2) {
                    $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #bonusTypeDiv").hide();
                }
            });

            var projectCodeIsExist = false;
            $("body").off('blur', '#systemProjectDevelopmentaddProjectInfoFormDivDiv #projectCode').on('blur', '#systemProjectDevelopmentaddProjectInfoFormDivDiv #projectCode', function () {
                var projectCode = $(this).val();
                $.ajax({
                    type: 'get',
                    url: common.url + "/ts-cp/baseProject/findByCode/" + projectCode,
                    success: function (data) {
                        if (data && data.length != 0) {
                            layer.msg('项目编码已存在！');
                            projectCodeIsExist = true;
                        } else {
                            projectCodeIsExist = false;
                        }
                    }
                });
            });

            // 新增操作
            $("#systemProjectDevelopment").off("click", "#addProjectInfo").on("click", "#addProjectInfo", function () {
                var html = systemProjectDevelopmentaddProjectInfoFormDiv.innerHTML;
                layer.open({
                    type: 1,
                    title: '新增项目',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['780px', '380px'], //宽高
                    content: html,
                    scrollbar: true,

                    success: function (layero, index) {
                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv .layDate").each(function (i, e) {
                            laydate.render({
                                elem: this
                            });
                        })

                        initEmployeeSelect('#systemProjectDevelopmentaddProjectInfoFormDivDiv #pmName');
                        initRefUserNameListSelect();
                        initdeptCodeSelect('#deptCodeChooseBox');
//                      initProjectStatusAdd();
                        form.render("radio");
                        form.render("select");
                        $('#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserNameListBox .layui-form-select').remove();
                    },
                });
            });

            //提交数据
            form.on('submit(systemProjectDevelopmentaddProjectInfoFormDivDivformSaveProjectInfo)', function (data) {
                //获取员工选中的文本 赋值给data
                var projectInfoId = data.field['projectInfoId'];
                var options = $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserNameList").find("option:selected");
                var refUserNameList = "";
                var refUserCodeList = "";
                for (var i = 0; i < options.length; i++) {
                    refUserNameList += options[i].text + ",";
                    refUserCodeList += options[i].value + ",";
                }
                data.field.refUserCodeList = refUserCodeList;
                data.field.refUserNameList = refUserNameList;
                if (!projectCodeIsExist) {
                    if (projectInfoId) {
                        trasen.ajax({
                            url: common.url + '/ts-cp/projectInfo/update',
                            type: 'POST',
                            data: JSON.stringify(data.field),
                            success: function (data) {
                                layer.closeAll('page');
                                refreshTable();
                            }
                        })
                    } else {
                        trasen.ajax({
                            url: common.url + '/ts-cp/projectInfo/save',
                            type: 'post',
                            data: JSON.stringify(data.field),
                            success: function (data) {
                                layer.closeAll('page');
                                refreshTable();
                            }
                        })
                    }
                } else {
                    layer.msg("项目编码已存在！");
                }
            });

            //导出
            $("#systemProjectDevelopment").off("click", "#exportDevelopment").on("click", "#exportDevelopment", function () {
                layer.confirm('确定要导出吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function (index) {
                    var url = common.url + "/ts-cp/projectInfo/export?";
                    var queryData = trasenTable.oTable.getGridParam("postData");
                    var exportParam = "";
                    for (var key in queryData) {
                        exportParam += (key + "=" + queryData[key] + "&")
                    }
                    //encodeURIComponent
                    location.href = (url + (exportParam));
                    layer.close(index);
                }, function () {
                });
            });

            //编辑
            $("#systemProjectDevelopment").off("click", "#projectInfoEditor").on("click", "#projectInfoEditor", function () {
                var html = systemProjectDevelopmentaddProjectInfoFormDiv.innerHTML;
                var rowData = trasenTable.getSelectRowData();
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.open({
                    type: 1,
                    title: '编辑',
                    closeBtn: 1,
                    shadeClose: false,
                    area: ['780px', '380px'], //宽高
                    content: html,
                    success: function (layero, index) {
                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv .layDate").each(function (i, e) {
                            laydate.render({
                                elem: this
                            });
                        })

                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #addProjectInfoForm #addProjectInfoFormDiv").show();
                        var id = rowData['projectInfoId'];
                        $.ajax({
                            type: 'post',
                            url: common.url + "/ts-cp/projectInfo/findById/" + id,
                            success: function (data) {
                                if (data && data.success) {
                                    //项目类型
                                    var html = "";
                                    if (data.object.projectType == 1) {
                                        html += '<input type="radio" name="projectType" value="1" title="新技术研发" checked="">'
                                        html += '<input type="radio" name="projectType" value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" value="4" title="合同项目">';
                                    } else if (data.object.projectType == 2) {
                                        html += '<input type="radio" name="projectType" value="1" title="新技术研发" >'
                                        html += '<input type="radio" name="projectType" value="2" title="新产品(项目)研发" checked="">';
                                        html += '<input type="radio" name="projectType" value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" value="4" title="合同项目">';
                                    } else if (data.object.projectType == 3) {
                                        html += '<input type="radio" name="projectType" value="1" title="新技术研发">'
                                        html += '<input type="radio" name="projectType" value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" value="3" title="产品升级改造"  checked="">';
                                        html += '<input type="radio" name="projectType" value="4" title="合同项目">';
                                    } else if (data.object.projectType == 4) {
                                        html += '<input type="radio" name="projectType" value="1" title="新技术研发" >'
                                        html += '<input type="radio" name="projectType" value="2" title="新产品(项目)研发">';
                                        html += '<input type="radio" name="projectType" value="3" title="产品升级改造">';
                                        html += '<input type="radio" name="projectType" value="4" title="合同项目" checked="">';
                                    }

                                    var accountingBonusHtml = "";
                                    if (data.object.isAccountingBonus == 1) {
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="1" title="是" checked="" disabled>';
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="2" title="否" disabled>';
                                    } else if (data.object.isAccountingBonus == 2) {
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="1" title="是">';
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="2" title="否" checked="">';
                                    } else {
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="1" title="是" >';
                                        accountingBonusHtml += '<input type="radio" name="isAccountingBonus" lay-filter="bonusFilter" value="2" title="否" checked="">';
                                    }
                                    if (data.object.isAccountingBonus == 1) {
                                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #bonusTypeDiv").show();
                                    }

                                    initEmployeeSelect('#systemProjectDevelopmentaddProjectInfoFormDivDiv #pmName');
                                    initdeptCodeSelect('#deptCodeChooseBox');
                                    trasen.setNamesVal(layero, data.object);
                                    form.render("select");
                                    $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #projectType").html(html);
                                    $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #isAccountingBonus").html(accountingBonusHtml);
                                    form.render("radio");
                                    initRefUserNameListSelect();
                                    $('#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserNameListBox .layui-form-select').remove();
                                    updateRUNList(rowData);
                                }
                            }
                        })

                    }
                });
            });

            //删除
            $("#systemProjectDevelopment").off("click", "#projectInfoDelete").on("click", "#projectInfoDelete", function () {
                var rowData = trasenTable.getSelectRowData();
                var id = rowData.projectInfoId;
                if (rowData.length || rowData.length == 0) {
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
                layer.confirm('确定要删除吗？', {
                    btn: ['确定', '取消'],
                    title: '提示',
                    closeBtn: 0
                }, function () {
                    trasen.post(common.url + "/ts-cp/projectInfo/delete/" + id, null, function (data) {
                        layer.closeAll('page');
                        layer.msg('操作成功.', {icon: 1});
                        refreshTable();
                    })
                }, function () {
                    layer.closeAll('page');
                });
            })

            //项目经理
            function initEmployeeSelect(el) {
                $.SelectPullDown(el, {
                    url: common.url + "/ts-hr/employee/selectList",
                    textName: 'name',
                    valName: 'code',
                    inpTextName: 'pmName',
                    inpValName: 'pmCode',
                    callback: function (res) {
                        if (res) {
                            var _url = common.url + "/ts-hr/employee/get/" + res.employeeId;
                            $.ajax({
                                type: "post",
                                contentType: "application/json; charset=utf-8",
                                url: _url,
                                success: function (data) {
                                    var obj = data.object;
                                    if (obj != null && obj != undefined && obj != '') {
                                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #addProjectInfoForm #deptName").val(obj.organizationName);
                                        $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #addProjectInfoForm #deptCode").val(obj.organizationId);
                                    }
                                }
                            });
                        }
                    }
                });

            }


            //相关人员多选
            function initRefUserNameListSelect() {
                $.publicSelect2($("#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserNameList"), {
                    url: "/ts-hr/employee/selectList",
                    valueName: "code",
                    textName: "name",
                    hiddenEl: $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserCodeList"),
                    width: '600px'
                })
            }

            //所属部门
            function initdeptCodeSelect(el) {
                // $.SelectPullDown(el, {
                //     url: common.url + "/ts-hr/dept/list",
                //     textName: 'name',
                //     valName: 'code',
                //     inpTextName: 'deptName',
                //     inpValName: 'deptCode',
                //     inpValId : "deptCode",
                //     inpTextId:"deptName",
                //     callback: function (res) {
                //         if (res) {

                //         }
                //     }
                // });
                $.allTreeSelect('#systemProjectDevelopmentaddProjectInfoFormDivDiv #deptName', {
                    id: '#deptCode',
                    url: common.url + "/ts-hr/organization/getTree",
                    callback: function (data) {
                        $('#systemProjectDevelopmentaddProjectInfoFormDivDiv #deptCode').val(data.id);
                    }
                });

            }
            form.render('select');

            laydate.render({
                elem: '#systemProjectDevelopment #queryEntryDate' //指定元素
                , type: 'year'
            });

            //筛选
            $("#systemProjectDevelopment").off('click', '#screen').on("click", "#screen", function () {
                $("#systemProjectDevelopmentscreeningBox").fadeToggle()
                return false

                // $('#querypmNameChooseBox .layui-form-select').remove();
                // $('#querydeptCodeChooseBox .layui-form-select').remove();
            });
            initEmployeeSelect('#systemProjectDevelopmentquerypmNameChooseBox');
            // initdeptCodeSelect('#systemProjectDevelopmentquerydeptCodeChooseBox');
            $.allTreeSelect('#systemProjectDevelopment #querydeptName', {
                id: '#querydeptCode',
                url: common.url + "/ts-hr/organization/getTree",
                callback: function (data) {
                    $('#systemProjectDevelopment #querydeptCode').val(data.id);
                }
            });

            // 查询
            form.on('submit(systemProjectDevelopmentscreeningSub)', function (data) {
                refreshTable();
            });

            // 取消
            $("#systemProjectDevelopment").off("click", "#screenCennel").on("click", "#screenCennel", function () {
                $("#systemProjectDevelopmentscreeningBox").fadeOut(200);
            });

            // 重置
            $("#systemProjectDevelopment").off("click", "#screenCRest").on("click", "#screenCRest", function () {
//				document.getElementById("screening").reset();
                $('#systemProjectDevelopmentscreening input,#systemProjectDevelopmentscreening select').val('');
                $('#systemProjectDevelopmentscreening .select2-selection__rendered').attr('title', '').html('<span class="select2-selection__placeholder">请选择</span>');
                refreshTable();
                return false;
            });

            // 初始化项目类型
            function initProjectType() {
                $.publicSelect(form, $("#systemProjectDevelopmentscreening select[name='projectType']"), {
                    url: '/ts-hr/dict/combobox/cp_project_project_type',
                    selectedValue: '',
                    type: 'get',
                })
            }

            // 初始化项目状态
            function initProjectStatus() {
                $.publicSelect(form, $("#systemProjectDevelopmentscreening select[name='status']"), {
                    url: '/ts-hr/dict/combobox/cp_project_project_status',
                    selectedValue: '',
                    type: 'get',
                })
            }

            // 初始化项目状态-添加
            function initProjectStatusAdd() {
                $.publicSelect(form, $("#addProjectInfoForm select[name='status']"), {
                    url: '/ts-hr/dict/combobox/cp_project_project_status',
                    selectedValue: 1,
                    type: 'get',
                })
            }


            // 更新相关人员
            function updateRUNList(rowData) {
                var refUserNameList = rowData['refUserNameList'];
                var refUserNames = new Array(); //定义一数组
                refUserNames = refUserNameList.split(","); //字符分割

                var refUserCodeList = rowData['refUserCodeList'];
                var refUserCodes = new Array(); //定义一数组
                refUserCodes = refUserCodeList.split(","); //字符分割
                var multipleDefaultValues = [];
                for (var i = 0; i < refUserNames.length; i++) {
                    if (refUserNames[i] != null && refUserNames[i] != '' && refUserNames[i] != undefined) {
                        multipleDefaultValues.push({
                            name: refUserNames[i],
                            value: refUserCodes[i]
                        })
                    }
                }

                $.publicSelect2($("#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserNameList"), {
                    url: "/ts-hr/employee/selectList",
                    valueName: "code",
                    textName: "name",
                    hiddenEl: $("#systemProjectDevelopmentaddProjectInfoFormDivDiv #refUserCodeList"),
                    multipleDefaultValue: multipleDefaultValues,
                    width: '600px'
                })

            }
        })
    }
})
