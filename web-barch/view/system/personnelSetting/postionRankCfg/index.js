"use strict";

define(function (require, exports, module) {
    var init= function() {

        return perform();
    }

    module.exports = {
        init:init
    }

    //模版页面脚本
    var perform = function(){
    	layui.use(['form', 'laydate','laytpl','trasen'],function() {
	    var laydate = layui.laydate,
	    	form = layui.form,
            laytpl = layui.laytpl,
	    	trasen = layui.trasen;

	    //日期
	    laydate.render({
	        elem: '#date'
	    });
	    laydate.render({
	        elem: '#date1'
	    });

		var trasenTable = new $.trasenTable("grid-table", {
		        url: common.url + '/ts-hr/postion/rank/cfg/list',
		        colNames: ['ID','等级名称','初始化积分','晋级积分','排序'],
		        colModel:[
                    {name : 'postionRankCfgId',index : 'postionRankCfgId',width:"auto",align : "center", sortable: true,editable : false,hidden:true},
		            {name : 'postionRankName',index : 'postion_rank_name',width:100,align : "center", sortable: true,editable : false},
                    {name : 'score',index : 'score',width:100,align : "center", sortable: true,editable : false},
                    {name : 'promotionScore',index : 'promotion_score',width:100,align : "center", sortable: true,editable : false},
                    {name : 'sortNo',index : 'sort_no',width:50,align : "center", sortable: true,editable : false}
		        ],
				sortable:true,
            	sortname:'sort_no',
            	sortorder:'asc',
            	queryFormId: 'queryForm'
		    });

		    function refreshTable(){
		        trasenTable.refresh();
		    }
		    refreshTable()

		    //关闭layer
		    $("body").on("click","#close",function(){
		        layer.closeAll()
		    });

			//修改
			$("body").off("click","#postionnelSerttingeditor").on("click","#postionnelSerttingeditor",function(){
		        var rowData = trasenTable.getSelectRowData();
                if(rowData.length || rowData.length == 0){
                    layer.msg('请选择一条记录进行操作！')
                    return false;
                }
		        var html = $("#postionnelSerttingaddHtml").html();
		            var data = { //数据
		                  "rowData":rowData
		                }
		            //var getTpl = postionnelSerttingeditorHtml.innerHTML;
		            layer.open({
	                    type: 1,
	                    title: '编辑',
	                    closeBtn: 0,
	                    shadeClose: false,
	                    area: ['680px', '250px'],
	                    skin: 'yourclass',
	                    content: html,
	                    success: function (layero, index) {
	                    	trasen.setNamesVal(layero, data.rowData);
			                form.render();
	                    }
	                });
		        
		    });

			//添加层
			$("body").off("click","#postionnelSerttingadd").on("click","#postionnelSerttingadd",function(){
		        var html = $("#postionnelSerttingaddHtml").html();
		            layer.open({
		                type: 1,
		                title: '新增',
		                closeBtn: 0,
		                shadeClose: false,
		                area: ['680px', '250px'],
		                skin: 'yourclass',
		                content: html
		            });
		            form.render();
			});
			
			form.on('submit(formSave)', function (data) {
				var _url  = null;
                if(data.field.postionRankCfgId) {
                    _url =  common.url + "/ts-hr/postion/rank/cfg/update";
                }else{
                    _url =  common.url + "/ts-hr/postion/rank/cfg/save";
                }
				var _data = JSON.stringify(data.field);
                $.ajax({
                    type: "post",
                    contentType: "application/json; charset=utf-8",
                    url: _url,
                    data: _data,
                    success: function (res) {
                        if (res.success) {
                            trasenTable.refresh();
                            layer.closeAll();
                            layer.msg('操作成功');
                        } else {
                            layer.closeAll();
                            layer.msg('操作失败！');
                        }
                    }
                });
            });

		    //查询
		    form.on('submit(search)', function (data) {
                refreshTable();
            });

			//删除
			$("body").off("click","#deleteBtn").on("click","#deleteBtn",function(){
				var rowData = trasenTable.getSelectRowData();//表格行数据
				if(rowData.length || rowData.length == 0){
						layer.msg('请选择一条记录进行操作！')
						return false;
				}
				layer.confirm('确定要删除吗？', {
					btn: ['确定', '取消'],
          title: '提示',
          closeBtn: 0
				}, function (index) {
					var _url = common.url + "/ts-hr/postion/rank/cfg/delete/" + rowData.postionRankCfgId;
					$.ajax({
						type:"post", 
                    	contentType: "application/json; charset=utf-8", 
						url:_url, 
						success:function (res) {
							if (res.success) {
								refreshTable();
								layer.msg('操作成功.', { icon: 1 });
								layer.close(index);
							} else {
								layer.closeAll();
								layer.msg('操作失败！');
							}
						}
					});
                }, function () {
                });
		    });

		});
    };

})

