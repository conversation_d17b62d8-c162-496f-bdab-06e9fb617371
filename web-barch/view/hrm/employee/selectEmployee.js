"use strict";
define(function (require, exports, module) {
    exports.init = function (opt, html) {
        layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'zTreeSearch', 'layedit'], function () {

            var form = layui.form, laydate = layui.laydate, trasen = layui.trasen, upload = layui.upload,
                layer = layui.layer, element = layui.element, zTreeSearch = layui.zTreeSearch,
                layedit = layui.layedit;

            var fileArray = new Array();
            var oldFileArray = new Array();

            layedit.set({
                uploadImage: {
                    url: common.url + '/ts-document/attachment/fileUpload?module=hrm&fillupf=1',
                    accept: 'image',
                    acceptMime: 'image/*',
                    exts: 'jpg|png|gif|bmp|jpeg',
                    size: 1024 * 10,
                    done: function (data) {
                    }
                }
                , uploadVideo: {
                    url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                    accept: 'video',
                    acceptMime: 'video/*',
                    exts: 'mp4|flv|avi|rm|rmvb',
                    size: 1024 * 10 * 2,
                    done: function (data) {
                    }
                }
                , uploadFiles: {
                    url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                    accept: 'file',
                    acceptMime: 'file/*',
                    size: '20480',
                    done: function (data) {
                    }
                }
                , calldel: {
                    url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                    done: function (data) {
                    }
                }
                , facePath: 'http://knifez.gitee.io/kz.layedit/Content/Layui-KnifeZ/'
                , devmode: true
                , tool: [
                    'strong', 'italic', 'underline', 'del', 'addhr', '|', 'fontFomatt', 'fontfamily', 'fontSize', 'fontBackColor', 'colorpicker', 'face'
                    , '|', 'left', 'center', 'right', '|', 'link', 'unlink', 'anchors', 'image_alt', 'video', 'images',//'attachment',
                    , '|'
                    , 'table', 'fullScreen'
                ]
                //, height: '680px'
            });

            layer.open({
                type: 1,
                title: opt.title,
                closeBtn: 1,
                shadeClose: false,
                area: ['800px', '600px'],
                skin: 'yourclass',
                content: html,
                success: function (layero, index) {
                    $('#id').val(opt.id);
                    if (opt.data) {
                        trasen.setNamesVal($('#evaBaseAddForm'), opt.data);
                        $('#empBirth').val(opt.data.empBirth)//出生年月
                        $('#empHiredate').val(opt.data.empHiredate)//入职时间
                        $('#empFiredate').val(opt.data.empFiredate)//离职时间
                        if (opt.data.empHeadImg && opt.data.empHeadImg != undefined) {
                            $("#UserPhoto").attr("src", common.url + "/ts-document/attachment/" + opt.data.empHeadImg);//头像
                        }
                        if (opt.data.signatureImgName && opt.data.signatureImgName != undefined) {
                            $("#signatureImg").attr("src", common.url + "/ts-document/attachment/" + opt.data.signatureImgName);//签章图片
                        }
                        if (opt.data.isSmsReminder && opt.data.isSmsReminder == 1) {
                            $('#isSmsReminderSelect').prop("checked", true);
                        }else{
                        	$('#isSmsReminderSelect').prop("checked", false);
                        }
                        if (opt.data.isVoiceReminder && opt.data.isVoiceReminder == 1) {
                            $('#isVoiceReminderSelect').prop("checked", true);
                        }else{
                        	$('#isVoiceReminderSelect').prop("checked", false);
                        }
                        if (opt.data.isWxReminder && opt.data.isWxReminder == 1) {
                            $('#isWxReminderSelect').prop("checked", true);
                        }else{
                        	$('#isWxReminderSelect').prop("checked", false);
                        }
                        if (opt.data.isDisplayPhoneNo && opt.data.isDisplayPhoneNo == 1) {
                            $('#isDisplayPhoneNoSelect').prop("checked", true);
                        }else{
                        	 $('#isDisplayPhoneNoSelect').prop("checked", false);
                        }
                        if (opt.data.isUseSignature && opt.data.isUseSignature == 1) {
                            $('#isUseSignatureSelect').prop("checked", true);
                        }else{
                        	$('#isUseSignatureSelect').prop("checked", false);
                        }

                        if (opt.none) {
                            $('#empCode').attr('readonly', '');
                            $('#userAccounts').attr('readonly', '');
                            $('#empPassword').attr('readonly', '');
                        }
                    }
                    form.render();
                }
            });

            //出生年月
            laydate.render({
                elem: '#empBirth',
                showBottom: true,
                done: function (value, date, endDate) {
                    if (CompareDate(value, dataTime)) {
                        layer.msg("请选择正确的出生年月");
                        $("#empAge").val("");
                    } else {
                        // 计算年龄
                        $("#empAge").val(jsGetAge(value));
                    }
                }
            });

            //入职日期
            laydate.render({
                elem: '#empHiredate',
                showBottom: true
            });

            //离职日期
            laydate.render({
                elem: '#empFiredate',
                showBottom: true
            });

            //部门下拉框赋值
            function treeSelect() {
                zTreeSearch.init('#empDeptName', {
                    url: common.url + '/ts-oa/thpsSysetm/getDeptList',
                    type: 'get',
                    checkbox: false,
                    condition: 'name',
                    zTreeOnClick: function (treeId, treeNode) {
                        $("#empDeptId").val(treeNode.id);
                        /*$("#empDeptName").val(treeNode.name);*/
                    }
                });
            }

            treeSelect();

            //职务下拉框赋值
            $.ajax({
                type: "post",
                url: common.url + '/ts-oa/employee/duty/list',
                data: {pageSize: 200, pageNo: 1},
                success: function (res) {
                    if (res) {
                        var postType = ' <option value="">请选择</option>';
                        if (opt.data) {//修改
                            $.each(res.rows, function (i, v) {
                                if (opt.data.empDutyId == v.id) {
                                    postType += '<option  value="' + v.id + '" selected="selected">' + v.dutyName + '</option>';
                                } else {
                                    postType += '<option value="' + v.id + '">' + v.dutyName + '</option>';
                                }
                            });
                        } else {//增加
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.id + '">' + v.dutyName + '</option>';
                            });
                        }
                        $('#empDutyId').html(postType);
                        form.render();
                    }
                }
            });

            //员工类型下拉框赋值
            $.ajax({
                type: "post",
                url: common.url + '/ts-oa/employee/employeeType/list',
                data: {pageSize: 200, pageNo: 1},
                success: function (res) {
                    if (res) {
                        var postType = ' <option value="">请选择</option>';
                        if (opt.data) {//修改
                            $.each(res.rows, function (i, v) {
                                if (opt.data.empType == v.userType) {
                                    postType += '<option  value="' + v.userType + '" selected="selected">' + v.userType + '</option>';
                                } else {
                                    postType += '<option value="' + v.userType + '">' + v.userType + '</option>';
                                }
                            });
                        } else {//增加
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.userType + '">' + v.userType + '</option>';
                            });
                        }
                        $('#empType').html(postType);
                        form.render();
                    }
                }
            });

            //员工职称下拉框赋值
            $.ajax({
                type: "post",
                url: common.url + '/ts-oa/employee/title/list',
                data: {pageSize: 200, pageNo: 1},
                success: function (res) {
                    if (res) {
                        var postType = ' <option value="">请选择</option>';
                        if (opt.data) {//修改
                            $.each(res.rows, function (i, v) {
                                if (opt.data.empTitleId == v.id) {
                                    postType += '<option  value="' + v.id + '" selected="selected">' + v.titleName + '</option>';
                                } else {
                                    postType += '<option value="' + v.id + '">' + v.titleName + '</option>';
                                }
                            });
                        } else {//增加
                            $.each(res.rows, function (i, v) {
                                postType += '<option value="' + v.id + '">' + v.titleName + '</option>';
                            });
                        }
                        $('#empTitleId').html(postType);
                        form.render();
                    }
                }
            });

            //监听下拉选择
            form.on('select(empDutyId)', function (data) {
                var dutyName = data.elem[data.elem.selectedIndex].text;
                $("#empDutyName").val(dutyName);
                form.render();
            });

            //头像上传
            var uploadInst = upload.render({
                elem: '#UserPhotoBtn',
                url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                field: "file",
                before: function (obj) {
                    //预读本地文件示例，不支持ie8
                    obj.preview(function (index, file, result) {
                        $('#UserPhoto').attr('src', result); //图片链接（base64）
                    });
                },
                done: function (res, index, upload) {
                    //如果上传失败
                    if (res.code > 0) {
                        return layer.msg('上传失败');
                    } else {
                        //上传成功
                        $('#empHeadImg').val(res.object[0].filePath)
                    }

                },
                error: function () {
                    //演示失败状态，并实现重传
                    var demoText = $('#UserPhotoText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function () {
                        uploadInst.upload();
                    });
                }
            });

            //签章图片
            var signatureImg = upload.render({
                elem: '#signatureImgBtn',
                url: common.url + '/ts-document/attachment/fileUpload?module=hrm',
                field: "file",
                before: function (obj) {
                    //预读本地文件示例，不支持ie8
                    obj.preview(function (index, file, result) {
                        $('#signatureImg').attr('src', result); //图片链接（base64）
                    });
                },
                done: function (res, index, upload) {
                    //如果上传失败
                    if (res.code > 0) {
                        return layer.msg('上传失败');
                    } else {
                        //上传成功
                        $('#signatureImgName').val(res.object[0].filePath)
                    }

                },
                error: function () {
                    //演示失败状态，并实现重传
                    var demoText = $('#signatureImgText');
                    demoText.html('<span style="color: #FF5722;">上传失败</span> <a class="layui-btn layui-btn-xs demo-reload">重试</a>');
                    demoText.find('.demo-reload').on('click', function () {
                        signatureImg.upload();
                    });
                }
            });

            //根据身份证号获取出生日期
            function getBirthdayFromIdCard(idCard) {
                var birthday = "";
                if (idCard != null && idCard != "") {
                    if (idCard.length == 15) {
                        birthday = "19" + idCard.substr(6, 6);
                    } else if (idCard.length == 18) {
                        birthday = idCard.substr(6, 8);
                    }

                    birthday = birthday.replace(/(.{4})(.{2})/, "$1-$2-");
                }
                return birthday;
            }

            var myDate = new Date();
            var year = myDate.getFullYear();
            var monthTime = myDate.getMonth() + 1;
            var day = myDate.getDate();
            var dataTime = year + "-" + monthTime + "-" + day;
            // 输入身份证就算出生年月和年龄
            $('#empIdcard').bind('input propertychange', function () {
                var idCard = $(this).val() == null || $(this).val() == '' ? "" : $(this).val();
                var birth;
                if (idCard.length == 18) {
                    birth = getBirthdayFromIdCard(idCard);
                    if (CompareDate(birth, dataTime)) {
                        layer.msg("请输入正确的身份证号码");
                        $("#empBirth").val("");
                    } else {
                        $("#empBirth").val(birth); // 给日期赋值
                    }
                } else {
                    $("#empBirth").val("");
                }
                // 计算年龄
                $("#empAge").val(jsGetAge(birth));
            });

            //时间的比较(开始时间，结束时间) yyyy-MM-dd hh ss mm
            function CompareDate(time1, time2) {
                return ((new Date(time1.replace(/-/g, "\/"))) > (new Date(time2.replace(/-/g, "\/"))));
            }

            /*根据出生日期算出年龄*/
            function jsGetAge(strBirthday) {
                var returnAge;
                if (strBirthday && typeof (strBirthday) != "undefined") {
                    var strBirthdayArr = strBirthday.split("-");
                    var birthYear = strBirthdayArr[0];
                    var birthMonth = strBirthdayArr[1];
                    var birthDay = strBirthdayArr[2];

                    var d = new Date();
                    var nowYear = d.getFullYear();
                    var nowMonth = d.getMonth() + 1;
                    var nowDay = d.getDate();

                    if (nowYear == birthYear) {
                        returnAge = 0;//同年 则为0岁
                    } else {
                        var ageDiff = nowYear - birthYear; //年之差
                        if (ageDiff > 0) {
                            if (nowMonth == birthMonth) {
                                var dayDiff = nowDay - birthDay;//日之差
                                if (dayDiff < 0) {
                                    returnAge = ageDiff - 1;
                                } else {
                                    returnAge = ageDiff;
                                }
                            } else {
                                var monthDiff = nowMonth - birthMonth;//月之差
                                if (monthDiff < 0) {
                                    returnAge = ageDiff - 1;
                                } else {
                                    returnAge = ageDiff;
                                }
                            }
                        } else {
                            //returnAge = -1;//返回-1 表示出生日期输入错误 晚于今天
                            returnAge = "";//返回"" 表示出生日期输入错误 晚于今天
                        }
                    }
                }
                return returnAge;//返回周岁年龄
            }


        })
    };
});