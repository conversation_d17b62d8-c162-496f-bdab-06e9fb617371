<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1" />
    <meta content="webkit" name="renderer" />
   <!-- <meta http-equiv="Content-Security-Policy" content=" worker-src blob:; child-src blob: gap:; img-src 'self' blob: data:; default-src * 'self' 'unsafe-inline' 'unsafe-eval' data: gap: content:">-->
    <!-- <meta http-equiv="cache-control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" /> -->
    <meta http-equiv="Expires" content="0" />
    <title>综合协同办公平台</title>
    <link rel="stylesheet" href="/js/plug/codemirror5-3.0/lib/codemirror.css">
    <script src="/js/plug/codemirror5-3.0/lib/codemirror.js"></script>
    <script src="/js/plug/codemirror5-3.0/lib/util/matchbrackets.js"></script>

    <script src="/js/plug/layui/layui.js"></script>
    <script src="/js/plug/jQuery/jquery.js"></script>
    <script src="/js/plug/jQuery/jquery.cookie.js"></script>
    <script src="/js/plug/waterFall.js"></script>
    <script>
        /** @desc 何锴 2022/9/8 兼容chrome49 start*/
        var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
            function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
            return new (P || (P = Promise))(function (resolve, reject) {
                function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
                function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
                function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
                step((generator = generator.apply(thisArg, _arguments || [])).next());
            });
        };
        var __generator = (this && this.__generator) || function (thisArg, body) {
            var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g;
            return g = { next: verb(0), "throw": verb(1), "return": verb(2) }, typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
            function verb(n) { return function (v) { return step([n, v]); }; }
            function step(op) {
                if (f) throw new TypeError("Generator is already executing.");
                while (_) try {
                    if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
                    if (y = 0, t) op = [op[0] & 2, t.value];
                    switch (op[0]) {
                        case 0: case 1: t = op; break;
                        case 4: _.label++; return { value: op[1], done: false };
                        case 5: _.label++; y = op[1]; op = [0]; continue;
                        case 7: op = _.ops.pop(); _.trys.pop(); continue;
                        default:
                            if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                            if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                            if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                            if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                            if (t[2]) _.ops.pop();
                            _.trys.pop(); continue;
                    }
                    op = body.call(thisArg, _);
                } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
                if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
            }
        };
        (function () {
            return __awaiter(this, void 0, void 0, function () {
                return __generator(this, function (_a) {
                    switch (_a.label) {
                        case 0: return [4 /*yield*/, new Promise(function (resolve, reject) {
                                var head = document.getElementsByTagName('head')[0], script = document.createElement('script');
                                script.onload = script.onreadystatechange = function () {
                                    if (!script.readyState || script.readyState === "loaded" || script.readyState === "complete") {
                                        resolve();
                                    }
                                };
                                script.onerror = function () {
                                    reject(false);
                                };
                                script.src = 'oaType.js';
                                head.appendChild(script);
                            })["catch"](function (error) { })];
                        case 1:
                            _a.sent();
                            if (isNewOA && (!window.parent || !window.parent.hasNewFrame)) {
                                location.href = location.origin + '/container/index';
                            }
                            return [2 /*return*/];
                    }
                });
            });
        })();
        /** @desc 何锴 2022/9/8 兼容chrome49 end*/
        (function () {
            var getparames = function (url) {
                var href = url;
                var nownrl = href.split('?');
                var pms = '';
                var pmsArr = [];
                var obj = {};
                if (nownrl.length > 1) {
                    pms = nownrl[1];
                    pmsArr = pms.split('&');
                    for (var i in pmsArr) {
                        var o = pmsArr[i].split('=');
                        obj[o[0]] = o[1];
                    }
                    return obj;
                };
                // var obj = getparames(location.href);
                // if (obj.inform) {
                //     $.cookie('THPMSCookie', obj.token);
                //     $.cookie('token', obj.token);
                // }
                return obj;
            };
            var obj = getparames(location.href);
            var sessionCookie = '';
            if (obj.token || obj.inform) {
                sessionCookie = obj.token || obj.inform;
                $.cookie('THPMSCookie', obj.token || obj.inform);
                $.cookie('token', obj.token || obj.inform);
            } else {
              sessionCookie = $.cookie('token')
            }
            
            sessionStorage.setItem('THPMSCookie', sessionCookie);

            if (!obj.bs) {
                var hash = location.hash.replace(/\?.+/, '');
                if (!hash) {
                    hash = '#/index';
                }
                location.href = location.origin + hash;
            }
            if (obj.token) {
            } else {
                // location.href = location.origin + '#/index'
            }
        })();
        (function () {
            if (!$.cookie('token')) {
                location.href = location.origin + '/login.html'
            }
        })();
    </script>
    <script src="/js/common/config.js"></script>
    <script src="/js/plug/sea.js"></script>
    <script src="/js/plug/seajs-css.js"></script>
    <script src="/js/src/sea.config.js"></script>
    <script src="/js/src/css.js"></script>
</head>

<body class="layui-layout-body">
    <div class="layui-layout layui-layout-admin body-content" id="mainContentBox">
        <!-- top -->
        <div class="layui-header layui-header-color flexbox" id="mainTopMenuBox">
            <div class="hearder-top-box">
                <div class="layui-logo">
                    <img src="/static/img/home/<USER>" id="terrace-logo" />
                    <span class="xian"></span>
                    <img class="logo-font-department" src="./static/img/other/logo-font-department.png" alt="">
                </div>

                <!-- 头部右侧内容 -->
                <ul class="layui-nav layui-layout-right">
                    <li class="layui-nav-item top_icon" id="onlineCount">
                        <i class="oaicon oa-icon-renyuan-kaoqin"
                            style="font-size: 22px; line-height: 0; vertical-align: middle"></i>
                        在线人数:
                        <span id="count" style="display: inline">0</span>人
                    </li>
                    <!-- <li class="layui-nav-item top_icon">
                        <a href="#/email/emailManagement" class="messageNum" title="邮件">
                            <i class="fa fa-envelope-o" aria-hidden="true" style="font-size: 20px"></i>
                            <span id="emailNum">0</span>
                        </a>
                    </li> -->
                    <!-- 账户信息 -->
                    <li class="layui-nav-item top_icon">
                        <a href="javascript:;" id="usericon">
                            <img src="/static/img/home/<USER>" alt="" class="userImg" />
                        </a>
                        <dl class="layui-nav-child mainPersenInfo" style="text-align: left">
                            <div class="user_img">
                                <div class="fl circle"><img src="/static/img/home/<USER>" class="userImg"
                                        alt="" /></div>
                                <div class="oveflow-hide">
                                    <p id="user-name"></p>
                                    <p class="label" id="user-dept"></p>
                                </div>
                            </div>
                            <div class="user_info">
                                <div class="info_item">
                                    <span class="label">工号</span>
                                    <span class="content-text" id="user-code"></span>
                                </div>
                                <!--  <div class="info_item">
                                    <span class="label">职务</span>
                                    <span class="content-text" id="user-job"></span>
                                </div> -->
                                <div class="info_item">
                                    <span class="label">登录IP</span>
                                    <span class="content-text" id="user-ip"></span>
                                </div>
                                <div class="info_item">
                                    <span class="label">登录时间</span>
                                    <span class="content-text" id="user-time"></span>
                                </div>
                            </div>
                            <div class="user_opt">
                                <span id="main-editor-msg">修改密码</span>
                                <span id="main-sign-out">退出登录</span>
                            </div>
                        </dl>
                    </li>
                    <li class="layui-nav-item user-login-info">
                        <div class="info-top">
                            <p id="user-name-top"></p>
                        </div>
                        <div class="info-bottom">
                            上次登陆&nbsp;<span id="user-login-time" style="padding-right: 16px;"></span>
                            IP&nbsp;<span id="user-login-ip"></span>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="hearder-bottom-box">
                <!-- 门户 -->
                <div id="index-door">
                    <i class="oaicon oa-icon-door"></i>
                    <div id="door-list" class="none"></div>
                </div>
                <!-- 头部区域（可配合layui已有的水平导航） -->
                <ul class="top-nav" id="top-nav">
                    <!-- <li class="nav-item">
                    <a href="#">首页</a>
                </li> -->
                </ul>
            </div>



        </div>
        <!-- 主体区域 -->
        <div class="layui-body iframe-body iframe-body-bg" id="mainBody">
            <!-- 菜单左侧时面包屑 -->
            <div style="position: relative">
                <div class="cutright">
                    <a href="javascript:;" id="frameCutTabBoxLeftBtn">
                        <i class="fa fa-angle-left" aria-hidden="true"></i>
                    </a>
                    <a href="javascript:;" id="frameCutTabBoxRightBtn">
                        <i class="fa fa-angle-right" aria-hidden="true"></i>
                    </a>
                </div>
                <div id="frameCutTabBox">
                    <div id="cuttab"></div>
                </div>
            </div>

            <div id="content"></div>
        </div>
        <!-- 左边菜单 -->
        <div id="normal-menu">
            <div class="toggleLeftMenu"><i class="oaicon oa-icon-zhankai" aria-hidden="true"></i></div>
            <div id="main-nav"></div>
        </div>
        <!-- 左侧图标菜单 -->
        <div id="small-menu">
            <div class="toggleLeftMenu"><i class="oaicon oa-icon-zhankai" aria-hidden="true"></i></div>
            <div id="main-nav-icon"></div>
        </div>
    </div>
    <div id="cuttab-contextmenu">
        <a href="javascript:;" id="refreshCut">刷新当前页签</a>
        <a href="javascript:;" id="closeCut">关闭当前页签</a>
        <a href="javascript:;" id="closeAllCut">关闭全部页签</a>
        <a href="javascript:;" id="closeOtherCut">关闭其他页签</a>
    </div>
</body>

</html>
<script>
    layui.config({
        base: '/js/plug/layui/lay/',
        version: true,
    });
    layui.use(['trasen', 'element', 'laydate', 'form'], function () {
        var element = (layui.element = layui.element);
        layui.trasen = layui.trasen;
        layui.form = layui.form;
        seajs.use('/js/utils/layVerify.js?1.0');
        element.render('nav');
    });
    document.write(`<script src="/js/common/common.js?${Math.random(10000)}"><\/script>`);
    document.write(`<script src="/js/src/core.js?${Math.random(10000)}"><\/script>`);
    document.write(`<script src="index.js?${Math.random(10000)}"><\/script>`)
</script>
