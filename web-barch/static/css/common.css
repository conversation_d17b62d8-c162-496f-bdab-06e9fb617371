/* 基础样式初始化 */
html,
body {
    height: 100%;
    width: 100%;
    font-family: 'MicrosoftYaHei' !important;
    font-size: 14px;
}
pre {
    font-family: 'MicrosoftYaHei' !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}

/* 布局 */
#mainContentBox {
    height: 100%;
    width: 100%;
    position: relative;
    z-index: 10;
}

/* 头部菜单 */
#mainTopMenuBox {
    height: 94px;
    width: 100%;
    display: flex;
    flex-direction: column;
}

#mainTopMenuBox .hearder-top-box {
    height: 54px;
    width: 100%;
    background-color: #fff;
}

#mainTopMenuBox .hearder-top-box .layui-logo {
    /* width: 180px; */
    height: 54px;
    padding: 5px 16px 5px 12px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
}

#mainTopMenuBox .layui-logo img {
    width: 230px;
    height: 40px;
    display: block;
    margin: auto;
}

#mainTopMenuBox .layui-logo .xian {
    width: 1px;
    height: 30px;
    background: #666666;
    margin: 0 15px;
}

#mainTopMenuBox .layui-logo .logo-font-department {
    width: 190px;
    height: 25px;
    margin: 0;
}

#mainTopMenuBox .hearder-bottom-box {
    height: 40px;
    width: 100%;
    background-color: #5260ff;
    background: linear-gradient(#5260ff, #5260ff);
}

#mainTopMenuBox #index-door {
    position: absolute;
    left: 0px;
    top: 54px;
    width: 40px;
    height: 40px;
    text-align: center;
}

#mainTopMenuBox #index-door.door-check {
    background-color: #3641bb;
}

#mainTopMenuBox #index-door i {
    font-size: 20px;
    line-height: 40px;
    color: #fff;
}

#mainTopMenuBox #index-door #door-list {
    position: absolute;
    top: 40px;
    background-color: #fff;
    padding: 4px 0;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.5);
}

#mainTopMenuBox #index-door #door-list .door-item {
    white-space: nowrap;
    word-break: unset;
    line-height: 28px;
    width: 190px;
    text-align: left;
    padding: 0 8px;
    cursor: pointer;
}

#mainTopMenuBox #index-door #door-list .door-item .door-icon {
    float: right;
    height: 20px;
    width: 34px;
    margin-top: 5px;
    border-radius: 13px;
    background-color: #cccccc;
    position: relative;
}

#mainTopMenuBox #index-door #door-list .door-item .door-icon::after {
    position: absolute;
    content: '';
    width: 16px;
    height: 16px;
    top: 2px;
    left: 2px;
    background-color: #fff;
    border-radius: 50%;
}

#mainTopMenuBox #index-door #door-list .door-item.door-def .door-icon {
    background-color: #5260ff;
}

#mainTopMenuBox #index-door #door-list .door-item.door-def .door-icon::after {
    left: auto;
    right: 2px;
}

#mainTopMenuBox #index-door #door-list .door-item.door-item-check {
    background-color: rgba(82, 96, 255, 0.08);
}

#mainTopMenuBox #index-door #door-list .door-item .door-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}

/* 头部菜单 */
/* top */
#mainTopMenuBox .top-nav {
    position: absolute;
    top: 54px;
    left: 56px;
    border-radius: 2px;
    font-size: 0;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    height: 40px;
}

#mainTopMenuBox .top-nav .nav-item {
    line-height: 24px;
    position: relative;
    display: inline-block;
    vertical-align: middle;
    font-size: 14px;
    min-width: 96px;
    text-align: center;
}

#mainTopMenuBox .top-nav .nav-item a {
    display: block;
    height: 24px;
    padding: 0 20px;
    color: #fff;
    /* transition: all 0.3s; */
    position: relative;
    white-space: nowrap;
}

#mainTopMenuBox .top-nav .nav-item .nav-item a {
    color: #333;
}

#mainTopMenuBox .top-nav .nav-item.nav-this > a:after {
    position: absolute;
    content: '';
    height: 4px;
    background-color: transparent;
    left: 20px;
    right: 20px;
    bottom: 0px;
}

#mainTopMenuBox .top-nav .nav-item:hover > a {
    color: #fff;
}

#mainTopMenuBox .top-nav .nav-item.nav-this > a {
    font-weight: 800;
    color: #5260ff;
    background-color: #fff;
    border-radius: 20px;
}

#mainTopMenuBox .top-nav .nav-item#topMenuMore {
    position: relative;
}

#mainTopMenuBox .top-nav .nav-item#topMenuMore .topOtherMenu {
    position: absolute;
    top: 100%;
    margin-top: 0px;
    left: 0;
    display: none;
    background-color: #fff;
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

#mainTopMenuBox .top-nav .nav-item .nav-item {
    display: block;
    word-break: keep-all;
    line-height: 40px;
}

#mainTopMenuBox .top-nav .nav-item .nav-item:hover > a {
    color: #fff;
    background-color: #5260ff;
}

#mainTopMenuBox .top-nav .nav-item .nav-item.nav-this > a {
    background-color: #5260ff;
    color: #fff;
    font-weight: 400;
}

#mainTopMenuBox .top-nav .nav-item .nav-item.nav-this > a:after {
    display: none;
}

#mainTopMenuBox .top-nav .nav-item .top-other-nav {
    position: absolute;
    background-color: #fff;
    top: 100%;
    min-width: 120px;
    margin-top: 0px;
    left: 0;
    padding: 5px 0;
    border-radius: 0px 0px 4px 4px;
    display: none;
    /* visibility: hidden; */
    /* opacity: 0; */
    /* transition: all 0.3s; */
    box-shadow: 0px 2px 10px 0px rgba(0, 0, 0, 0.1);
}

#mainTopMenuBox .top-nav .nav-item .top-other-nav .top-other-nav {
    left: 100%;
    top: 0;
    margin-top: 0;
}

#mainTopMenuBox .top-nav .nav-item:hover > a + .top-other-nav {
    display: block;
    /* opacity: 1; */
    /* visibility: visible; */
}

#mainTopMenuBox .top-nav .nav-item#topMenuMore .topOtherMenu .top-other-nav {
    top: 0;
    margin-top: 0;
    left: 100%;
}

#mainTopMenuBox .layui-layout-right {
    position: absolute !important;
    /* width: 200px; */
    margin: 0;
    text-align: right;
    height: 54px;
    display: flex;
    align-items: center;
    padding: 0 10px;
}

#mainTopMenuBox .layui-layout-right .layui-nav-item {
    line-height: 54px;
    color: #5260FF;
}

#mainTopMenuBox .layui-layout-right .user-login-info {
    display: flex;
    flex-direction: column;
    text-align: left;
}

#mainTopMenuBox .layui-layout-right .user-login-info .info-top {
    font-size: 14px;
    font-family: MicrosoftYaHei-Bold, MicrosoftYaHei;
    font-weight: bold;
    color: #7275A0;
    line-height: 19px;
}

#mainTopMenuBox .layui-layout-right .user-login-info .info-bottom {
    font-size: 12px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(51, 51, 51, 0.7);
    line-height: 17px;
    display: flex;
}

#mainTopMenuBox .layui-layout-right .user-login-info span {
    display: block;
}

#mainTopMenuBox .layui-nav.layui-layout-right .layui-this:after {
    background-color: transparent;
}

#mainTopMenuBox #onlineCount {
    cursor: default;
}

#mainTopMenuBox #usericon {
    height: 32px;
    width: 32px;
    padding: 0;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f2f2f2;
    margin: 0 8px;
}

#mainTopMenuBox #usericon img {
    display: block;
    height: 100%;
    width: 100%;
}

#mainTopMenuBox .mainPersenInfo {
    top: 45px;
    z-index: 1005;
    padding: 0;
    border: none;
    width: 260px;
    box-shadow: 0px 0px 6px 0px #ccc;
    left: auto !important;
    right: 0px;
    color: #333;
    background: #fff;
}

#mainTopMenuBox .mainPersenInfo .user_img {
    height: 50px;
    width: 220px;
    padding: 20px;
    background: #eeefff;
    position: relative;
}

#mainTopMenuBox .mainPersenInfo .user_img:after {
    position: absolute;
    content: '';
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-image: url(/static/img/home/<USER>
    background-repeat: no-repeat;
    background-position: left bottom;
    background-size: auto 80%;
    transform: rotateY(180deg);
}

#mainTopMenuBox .mainPersenInfo .user_img img {
    width: 50px;
    height: 50px;
}

#mainTopMenuBox .mainPersenInfo .user_img > div.oveflow-hide {
    height: 40px;
    margin-top: 10px;
    padding-left: 10px;
    overflow: hidden;
}

#mainTopMenuBox .mainPersenInfo .user_img p {
    line-height: 20px;
    width: 100%;
}

#mainTopMenuBox .mainPersenInfo .user_info {
    line-height: 40px;
    width: 220px;
    padding: 10px 20px;
    background: #fff;
}

#mainTopMenuBox .mainPersenInfo .user_info span {
    display: inline-block;
    font-size: 14px;
}

#mainTopMenuBox .mainPersenInfo .label {
    width: 60px;
    text-align: left;
    color: #999;
}

#mainTopMenuBox .mainPersenInfo .content-text {
    color: #333;
    text-align: left;
}

#mainTopMenuBox .mainPersenInfo .user_opt {
    line-height: 40px;
    width: 220px;
    padding: 10px 20px;
    border-top: 1px solid #eee;
    text-align: center;
}

#mainTopMenuBox .mainPersenInfo .user_opt span {
    display: inline-block;
    margin: 0 8px;
    width: 70px;
    padding-left: 20px;
    text-align: center;
    line-height: 20px;
    cursor: pointer;
    color: #333;
}

#mainTopMenuBox .mainPersenInfo .user_opt span#main-editor-msg {
    background-image: url(/static/img/home/<USER>
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: 5px center;
}

#mainTopMenuBox .mainPersenInfo .user_opt span#main-editor-msg:hover {
    background-image: url(/static/img/home/<USER>
    color: #5260ff;
}

#mainTopMenuBox .mainPersenInfo .user_opt span#main-sign-out {
    background-image: url(/static/img/home/<USER>
    background-repeat: no-repeat;
    background-size: 16px 16px;
    background-position: 5px center;
}

#mainTopMenuBox .mainPersenInfo .user_opt span#main-sign-out:hover {
    background-image: url(/static/img/home/<USER>
    color: #5260ff;
}

#mainTopMenuBox .circle {
    background-color: #f2f2f2;
    overflow: hidden;
    border-radius: 50%;
}

#mainTopMenuBox .layui-layout-right #count {
    /* padding: 0 10px 0 7px;
    background-color: #d3d7ff;
    border-radius: 10px;
    color: #5260ff;
    margin-left: 3px;
    position: relative; */
}

#mainTopMenuBox .layui-layout-right #count:after {
    /* position: absolute;
    content: '';
    border-right: 5px solid #d3d7ff;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    top: 50%;
    margin-top: -5px;
    left: -3px; */
}

#mainTopMenuBox .layui-layout-right span {
    display: none;
}

#mainTopMenuBox .top_icon {
    margin: 0 5px;
}

#mainTopMenuBox .messageNum {
    position: relative;
}

#mainTopMenuBox .messageNum:hover i {
    color: #5260ff;
}

#mainTopMenuBox .messageNum i {
    color: #d8d8d8;
}

#mainTopMenuBox .messageNum span {
    content: '';
    position: absolute;
    display: block;
    top: -4px;
    left: 26px;
    min-width: 14px;
    text-align: center;
    font-size: 16px;
    line-height: 16px;
    background-color: red;
    color: #fff;
    border-radius: 17px;
    padding: 5px;
    transform: scale(0.7);
}

#mainTopMenuBox .messageNum:hover i {
    color: #fff;
}

#mainTopMenuBox .nav-item .more-nav {
    /* line-height: 59px; */
    height: 40px;
}

/* 主体区域 */
#mainBody {
    background-color: #efeff4;
    top: 94px;
    bottom: 0;
    /* transition: all 0.3s; */
    /* left: 0; */
}

#small-menu {
    left: -50px;
}

.body-content-menu #mainBody {
    left: 50px;
}

.body-content-menu #normal-menu {
    left: -210px;
}

.body-content-menu #small-menu {
    left: 0;
}

.body-content #mainBody {
    left: 0;
}

.body-content #normal-menu {
    display: none;
}

.toggleLeftMenu {
    height: 26px;
    font-size: 0;
    padding: 5px 0;
    padding-left: 15px;
    cursor: pointer;
}

.toggleLeftMenu i {
    display: inline-block;
    vertical-align: top;
    font-size: 20px;
    color: #c2ccdc;
}

/* cuttab */
#mainBody .cutright {
    float: right;
    height: 30px;
    line-height: 30px;
    background-color: #fff;
    margin-right: 8px;
    border-radius: 0 0 4px 0;
}

#mainBody .cutright a {
    width: 30px;
    height: 20px;
    display: inline-block;
    line-height: 15px;
    text-align: center;
    position: relative;
}

#mainBody .cutright i {
    font-size: 20px !important;
    color: #b3b3b3;
    font-weight: bold;
}

#mainBody .cutright a:hover i {
    color: #5260ff;
}

#mainBody #frameCutTabBox {
    font-size: 0;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    margin-left: 8px;
    background-color: #fff;
    border-radius: 0 0 0 4px;
}

#mainBody #cuttab {
    position: relative;
    left: 0;
    font-size: 0;
    background-color: transparent;
    transition: all 0.3s;
}

#mainBody #cuttab .cuttab-box {
    display: inline-block;
    height: 30px;
    line-height: 30px;
    font-size: 14px;
    /* margin: 0 5px; */
    position: relative;
    color: #666;
    width: 118px;
    box-sizing: border-box;
    border-right: 1px solid #eee;
}

/* #mainBody #cuttab .cuttab-box:after {
    content: '';
    position: absolute;
    right: -5px;
    height: 10px;
    width: 1px;
    top: 50%;
    margin-top: -5px;
    background-color: #ccc;
} */

#mainBody #cuttab .cuttab-box:nth-last-child(1):after {
    background-color: transparent;
}

#mainBody #cuttab .cuttab-box .cuttab-text {
    cursor: pointer;
    padding-right: 10px;
    padding-left: 8px;
    overflow: hidden;
    margin-right: 30px;
    text-overflow: ellipsis;
    white-space: nowrap;
    /* padding: 0 10px; */
}

#mainBody #cuttab .cuttab-box i {
    font-size: 14px;
    cursor: pointer;
    float: right;
    margin-right: 8px;
    color: #c2ccdc;
}

#mainBody #cuttab .cuttab-box i.cuttab-close {
    font-size: 18px;
}

#mainBody #cuttab .cuttab-box:hover {
    color: #5260ff;
}

#mainBody #cuttab .cuttab-box.active {
    color: #5260ff;
}

/* #mainBody #cuttab .cuttab-box.active span {
    font-weight: 800;
} */
.layui-body {
    left: 160px;
}

#mainBody > #content {
    position: absolute;
    top: 38px;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

#mainBody #content .content-hide-box {
    position: absolute;
    border-radius: 0px;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    overflow: hidden;
    z-index: 1;
    padding: 0 8px 8px;
}

#mainBody #content .content-hide-box > .content-box {
    position: relative;
    border-radius: 4px;
    height: 100%;
    width: 100%;
    overflow: hidden;
    overflow-y: hidden;
    background-color: #fff;
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    padding: 8px;
}

/* 左侧菜单 */
#normal-menu {
    position: absolute;
    top: 94px;
    bottom: 0;
    left: 0;
    width: 160px;
    box-sizing: border-box;
    transition: all 0.3s;
    background-color: #fff;
}

#main-nav {
    position: absolute;
    top: 40px;
    bottom: 0;
    right: 0;
    left: 0;
}

#main-nav a {
    display: block;
    position: relative;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#main-nav a .nav-item-icon {
    font-size: 20px;
    display: inline-block;
    /* vertical-align: middle; */
    vertical-align: top;
    color: #c2ccdc;
    margin-right: 8px;
}

#main-nav li {
    font-size: 14px;
    line-height: 40px;
    position: relative;
}

#main-nav .open-contor {
    position: absolute;
    right: 0;
    top: 0;
    height: 40px;
    width: 30px;
    line-height: 40px;
    text-align: center;
    font-size: 16px;
    cursor: pointer;
    opacity: 0;
}

#main-nav .nav-item.has-childs > a {
    font-weight: 400;
}

#main-nav .nav-item > a:hover .open-contor {
    opacity: 1;
}

#main-nav .nav-item .open-contor .fa-angle-down {
    display: none;
}

#main-nav .nav-item .child-nav-item {
    max-height: 0;
    overflow: hidden;
    /* transition: max-height 0.5s ease-in-out; */
}

#main-nav .nav-item .child-nav-item a {
    color: #666;
}

#main-nav .nav-item.open > a {
    font-weight: 700;
    background-color: #eaf1fd;
    color: #5260ff;
}

#main-nav .nav-item.open > a:after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 4px;
    bottom: 0;
    background-color: #5260ff;
}

#main-nav .nav-item.nav-this > a {
    font-weight: 700;
    color: #5260ff;
}

#main-nav .nav-item.active > a {
    background-color: #eaf1fd;
    font-weight: 700;
}

#main-nav .nav-item.active > a:after {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    width: 4px;
    bottom: 0;
    background-color: #5260ff;
}

#main-nav .nav-item.active .nav-item-icon {
    font-weight: 400;
}

/* #main-nav .nav-item.active > .child-nav-item {
    max-height: 1000px;
}
#main-nav .nav-item.active > .open-contor .fa-angle-down {
    display: inline-block;
}
#main-nav .nav-item.active > .open-contor .fa-angle-up {
    display: none;
} */
#main-nav .nav-item.open > .child-nav-item {
    max-height: 1500px;
}

#main-nav .nav-item.open > a .open-contor .fa-angle-down {
    display: inline-block;
}

#main-nav .nav-item.open > a .open-contor .fa-angle-up {
    display: none;
}

#main-nav .nav-item.open > a .open-contor {
    opacity: 1;
}

/*  */
#small-menu {
    position: absolute;
    top: 40px;
    bottom: 0;
    width: 50px;
    box-sizing: border-box;
    transition: all 0.3s;
    background-color: #fff;
}

#main-nav-icon li {
    line-height: 26px;
    position: relative;
}

#main-nav-icon li a {
    color: #333;
}

#main-nav-icon li a i {
    color: #c2ccdc;
}

#main-nav-icon .firstLevel li a {
    display: block;
    text-align: center;
    padding: 12px 0;
}

#main-nav-icon .firstLevel li a i {
    font-size: 20px;
    margin: 0;
    vertical-align: top;
    display: inline-block;
}

#main-nav-icon li.active > a {
    color: #fff;
    background-color: #5260ff;
}

#main-nav-icon li.active > a i {
    color: #fff;
}

#main-nav-icon .nextLevel {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 160px;
    z-index: 1000;
    background-color: #fff;
    display: none;
    padding: 4px 0;
    border-radius: 4px;
    box-shadow: 1px 0px 4px 0 #ccc;
}

#main-nav-icon .nextLevel li a {
    font-size: 14px;
    padding: 7px 20px;
}

#main-nav-icon li:hover > a {
    background-color: #5260ff;
    color: #fff;
}

#main-nav-icon li:hover > a + .nextLevel {
    display: block;
}

/* 右键菜单那 */
#cuttab-contextmenu {
    display: none;
    position: fixed;
    background-color: #fff;
    border-radius: 4px;
    color: #333;
    padding: 10px 0px 10px 0px;
    top: 71px;
    left: 200px;
    z-index: 1000;
    box-shadow: 0 2px 4px 0 #666;
}

#cuttab-contextmenu a {
    display: block;
    height: 30px;
    padding: 0 50px 0 20px;
    line-height: 30px;
}

#cuttab-contextmenu a:hover {
    color: #5260ff;
    background-color: #edefff;
}

/**
TODO 基础公用样式
**/
.theme-color {
    color: #5260ff;
}

.theme-background-color {
    background-color: #12257c !important;
}

.menu-backgroud-color {
    background-color: #5d657d !important;
}

.content_bg {
    background-image: url(/static/img/other/empty_2.png);
    background-repeat: no-repeat;
    background-position: center;
    position: relative;
    background-size: auto 50%;
}

.content_bg:after {
    position: absolute;
    content: '暂 无 数 据';
    left: 0;
    bottom: 20%;
    width: 100%;
    color: #ccc;
    font-size: 14px;
    font-weight: 400;
    text-align: center;
}

.content_bg.no_bg {
    background-image: unset;
}

.content_bg.no_bg:after {
    content: '' !important;
}

.no-select {
    user-select: none;
}

.text-hide {
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
}

.border-top {
    box-shadow: 0px -0.5px 0px 0.5px #eee !important;
}

.flex {
    display: flex;
}

.flex_1 {
    flex: 1;
}

.mgl10 {
    margin-left: 10px;
}

.font-0 {
    font-size: 0;
}

.font-14 {
    font-size: 14px;
}

.font-15 {
    font-size: 15px;
}

.font-16 {
    font-size: 16px;
}

.font-18 {
    font-size: 18px;
}

.font-22 {
    font-size: 22px;
}

.bg-trans {
    background-color: transparent !important;
}

.row:after {
    content: '';
    display: block;
    clear: both;
}

.more:hover {
    color: #5260ff !important;
}

.none {
    display: none !important;
}

.fl {
    float: left;
}

.fr {
    float: right;
}

.alink {
    color: #5260ff !important;
    cursor: pointer;
    font-weight: 400;
}

.opera_normal {
    color: #5260ff !important;
    cursor: pointer;
    font-weight: 400;
}

.required,
.reuqired {
    color: red;
}

.overflow-hide {
    overflow: hidden !important;
}

.overflow-v {
    overflow: visible !important;
}

.archivesTabBtn {
    width: 100%;
    box-sizing: border-box;
    position: absolute;
    left: 0;
    bottom: 0;
    padding: 8px 15px;
    text-align: right;
    border-top: 1px solid #eee;
    background-color: #fff;
}

#loadingAllBox {
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background-color: rgba(0, 0, 0, 0.05);
    z-index: 192830129830982011;
}

#loadingAllBox .img {
    width: 40px;
    height: 40px;
    background: url('../img/other/loading-2.gif') no-repeat;
    /*background:url("../images/Loading.png") no-repeat;*/
    /*background-size:40px 40px;*/
    position: absolute;
    left: calc(50% - 25px);
    top: 45%;
    /*animation: loadRotate 1s infinite linear;
  -moz-animation: loadRotate 1s infinite linear;
  -webkit-animation: loadRotate 1s infinite linear;
  -o-animation: loadRotate 1s infinite linear;*/
}

.content-bg-con {
    position: relative;
    background-repeat: no-repeat;
    background-position: center;
    background-size: auto 50%;
}

.content-bg-con:before {
    position: absolute;
    bottom: 20%;
    left: 0;
    right: 0;
    text-align: center;
    color: #333;
    opacity: 0.25;
}

.content-bg-con.home-page {
    background-size: 50%;
    background-image: url(/static/img/other/newEmpty.png);
}

.content-bg-con.home-page:before {
    content: '暂 无 数 据 ';
    top: 68%;
}

.content-bg-con.two-col-home-page {
    background-size: 50%;
    background-image: url(/static/img/other/newEmpty.png);
}

.content-bg-con.two-col-home-page:before {
    content: '暂 无 数 据 ';
    bottom: 10%;
}

.content-bg-con.con-one {
    background-image: url(/static/img/other/newEmpty.png);
}

.content-bg-con.con-one:before {
    content: '暂 无 数 据 ';
}

.content-bg-con.con-two {
    background-size: 50% auto;
    background-image: url(/static/img/other/empty.png);
}

.content-bg-con.con-two:before {
    content: '暂 无 数 据';
}

.content-bg-con.con-three {
    background-image: url(/static/img/other/empty.png);
}

.content-bg-con.con-three:before {
    content: '暂 无 数 据 ';
}

.content-bg-con.noBg {
    background-image: none;
}

.content-bg-con.noBg:before {
    display: none;
}

/* 权限按钮控制 */
[data-permission='on'] {
    display: none;
}

/* 更多按钮 */
.oa-more-btn {
    position: relative;
    z-index: 10001;
}

.oa-more-btn:hover {
    opacity: 1;
}

.oa-more-btn-box {
    position: absolute;
    display: none;
    top: 100%;
    right: 0;
    margin-top: 1px;
    background-color: #fff;
    color: #5260ff;
    border-radius: 4px;
    box-shadow: 0px 2px 4px 0px rgba(55, 61, 125, 0.5);
}

.oa-more-btn-box .oa-more-btn-item {
    display: block;
    height: 34px;
    line-height: 34px;
    padding: 0 8px;
    min-width: 104px;
    color: #5260ff;
    background-color: #fff;
    text-align: center;
    border: none;
    outline: none;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
}

.oa-more-btn:hover .oa-more-btn-box {
    display: block;
}

.oa-more-btn-box .oa-more-btn-item:hover {
    background-color: rgba(82, 96, 255, 0.08);
    opacity: 1;
}

/* 复选框样式 */
label.labelCheckbox {
    height: auto;
    line-height: 28px;
    user-select: none;
    margin-top: 2px;
    display: inline-block;
}

input[type='checkbox'].self-checkbox {
    display: none;
}

input[type='checkbox'].self-checkbox ~ .self-checkbox-icon {
    padding: 0;
    height: 17px !important;
    width: 17px !important;
    /*border:1px #c4d2d5 solid;*/
    text-align: center;
    margin: 0 5px;
    display: inline-block !important;
    vertical-align: top;
    position: relative;
    cursor: default;
    position: relative;
    visibility: hidden;
    font-size: 14px;
    border-radius: 2px !important;
    top: 6px;
    font-style: normal;
}

input[type='checkbox'].self-checkbox + .layui-form-checkbox {
    display: none;
}

input[type='checkbox'].self-checkbox ~ .self-checkbox-icon::after {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #fff;
    border: 1px solid #ccc;
    border-radius: 2px;
    color: #000;
    width: 15px;
    height: 15px;
    line-height: 15px;
    text-align: center;
    display: inline-block;
    visibility: visible;
    padding-left: 0px;
    text-align: center;
    content: ' ';
    border-radius: 3px;
}

input[type='checkbox'].self-checkbox:checked ~ .self-checkbox-icon::after {
    color: #fff;
    background-color: #5260ff;
    content: '✓';
    font-size: 12px;
    font-weight: bold;
}

input[type='checkbox'].self-checkbox:disabled ~ .self-checkbox-icon::after {
    background-color: #ccc;
}

/* 复选框开关样式 */
input[type='checkbox'].self-checkbox-switch {
    display: none;
}

input[type='checkbox'].self-checkbox-switch ~ .self-checkbox-switch-icon {
    display: inline-block;
    height: 24px;
    width: 56px;
    border: 1px solid #d2d2d2;
    background-color: #fff;
    border-radius: 20px;
    font-style: normal;
    position: relative;
    vertical-align: top;
}

input[type='checkbox'].self-checkbox-switch ~ .self-checkbox-switch-icon:after {
    content: '';
    position: absolute;
    border-radius: 20px;
    height: 20px;
    width: 20px;
    top: 2px;
    left: 5px;
    background-color: #d2d2d2;
    transition: left 0.3s;
}

input[type='checkbox'].self-checkbox-switch:checked ~ .self-checkbox-switch-icon {
    background-color: #5260ff;
    border-color: #5260ff;
}

input[type='checkbox'].self-checkbox-switch:checked ~ .self-checkbox-switch-icon:after {
    background-color: #fff;
    left: 31px;
}

/* 时间框 */
.oa-date {
    background-image: url(/static/img/other/timeIcon.png);
    background-repeat: no-repeat;
    background-position: calc(100% - 6px) center;
    background-size: 16px;
}

/* 下拉样式 */
.oa-tree-sel {
    background-image: url(/static/img/other/ztree_open.png);
    background-repeat: no-repeat;
    background-position: calc(100% - 6px) center;
    background-size: 16px;
}

/* TODO 旧样式 */
.shell-search-box {
    /* width: 220px; */
    width: auto;
    float: left;
    margin-right: 5px;
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.shell-search-box .shell-layer-input-boxTit {
    float: left;
    line-height: 30px;
    padding: 10px 5px 0 10px;
    text-align: right;
    padding-top: 0;
}

.shell-search-box .shell-layer-input-box {
    padding-left: 5px;
    width: 140px;
}

.shell-search-box .shell-layer-input-box.shell-layer-time-input {
    width: 192px;
}

.shell-layui-form-label {
    width: 90px;
    padding: 0 10px;
    font-size: 13px;
    height: 32px;
    line-height: 32px;
    text-align: right;
    position: absolute;
    left: 0;
    top: 0;
}

.shell-layer-input-box {
    min-height: 32px;
    padding-left: 110px;
    margin-bottom: 15px;
    position: relative;
}

.lay-label-title {
    width: 110px;
    padding: 0 10px;
    font-size: 14px;
    height: 32px;
    line-height: 32px;
    text-align: right;
    position: absolute;
    left: 0;
    top: 0;
}

.lay-input-box {
    min-height: 32px;
}

.lay-input-box,
.lay-input-box-right {
    padding-left: 128px;
    padding-bottom: 5px;
    position: relative;
}

/**
TODO 选择框
**/
.tra-lay-select-title {
    /* display: inline-block; */
    width: 100%;
    min-height: 32px;
    position: relative;
    font-size: inherit;
    /* padding-bottom: 5px; */
}

.tra-lay-select-title .choiceBox input {
    width: 100%;
}

/* .tra-lay-select-title .choiceBox {
    width: 100%;
} */

.tra-lay-select-title i.icon {
    content: '';
    border-left: 4px transparent solid;
    border-right: 4px transparent solid;
    border-top: 4px #c2c2c2 solid;
    display: inline-block;
    position: absolute;
    right: 7px;
    top: 12px;
    -webkit-transition: all 0.3s;
    -moz-transition: all 0.3s;
    -ms-transition: all 0.3s;
    -o-transition: all 0.3s;
    transition: all 0.3s;
}

.tra-lay-select-title .layui-input:focus ~ i.icon {
    transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -o-transform: rotate(180deg);
}

.tra-lay-select-title .layui-input {
    height: 28px;
    width: 100%;
    padding-right: 20px;
    box-sizing: border-box;
    text-align: left !important;
    /* border: 1px #d2d2d2 solid; */
    /*padding-left:10px !important;
    padding-right:17px !important;*/
}

.tra-lay-select-dl {
    width: 100%;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100000100000;
}

.tra-lay-select-dl.search-input-dl {
    position: absolute !important;
}

.tra-lay-select-dl dl {
    position: absolute;
    left: 0;
    top: 30px;
    padding: 5px 0;
    z-index: 999;
    min-width: 100%;
    border: 1px solid #d2d2d2;
    max-height: 200px;
    overflow-y: auto;
    background-color: #fff;
    border-radius: 2px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
    box-sizing: border-box;
}

.tra-lay-select-dl dl dd,
.tra-lay-select-dl dl dt {
    padding: 0 10px;
    line-height: 26px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    font-size: inherit;
    margin: 0;
}

.tra-lay-select-dl dl dd.select {
    background-color: #d5d5d5;
}

.tra-lay-select-dl dl dd:hover,
.tra-lay-select-dl dl dt:hover {
    background-color: #d5d5d5;
}

.tra-lay-select-dl dl dd.layui-select-tips {
    padding-left: 10px !important;
    color: #999;
}

.tra-lay-select-title span.choice {
    color: #333;
    border: 1px #eee solid;
    line-height: 14px;
    border-radius: 3px;
    padding: 3px;
    margin: 4px 3px;
    background: #ffffff;
    display: inline-block;
    float: left;
    position: relative;
    z-index: 10;
}
.tra-lay-select-title span.choice a.choiceCloseTag {
    color: #999;
}
.tra-lay-select-title span.choice a.choiceCloseTag:hover {
    color: #5260ff;
}

.tra-lay-select-title .choiceBox {
    /*border:1px #B7BDC0 solid;*/
    /* border: 1px rgba(0, 0, 0, 0.1) solid; */
    border-radius: 2px;
    /* padding: 5px 0;
    min-height: 16px; */
}

.tra-lay-select-title .choiceBox input {
    width: 100% !important;
    height: 100%;
    /* border: 0; */
    background: none;
    position: absolute;
    left: 0;
    top: 0;
}

/* TODO search-select */
.search-select-person {
    border: 1px solid #d2d2d2 !important;
    padding: 0 8px;
    border-radius: 4px;
    width: auto;
    height: auto;
    min-height: 30px;
    cursor: pointer;
    font-family: Tahoma;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.search-select-person .search-check-item {
    height: 24px !important;
    line-height: 24px;
    color: #000 !important;
    border: none !important;
    text-align: center !important;
    padding: 0 5px !important;
    border-radius: 5px !important;
    -webkit-user-select: none; /* Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* Internet Explorer/Edge */
    user-select: none; /* Non-prefixed version, currently supported by Chrome, Opera and Firefox */
}

.search-select-person .search-check-item.select {
    background: #5260ff !important;
    color: #fff !important;
}

.search-select-person .search-check-item:hover {
    background: #5260ff !important;
    color: #fff !important;
}

.search-select-person .search-check-item .dept-name {
    color: #a0a0a0 !important;
}

.search-select-person .search-check-item:hover .dept-name {
    color: #fff !important;
}

/* TODO time-pick */
#time-pick {
    position: fixed;
    /* display: none; */
    top: 0;
    left: 0;
    height: 200px;
    width: 300px;
    background-color: #fff;
    margin-top: 10px;
    border: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#time-pick .arrow {
    position: absolute;
    width: 0;
    height: 0;
    top: -8px;
    left: 24px;
    margin-right: 3px;
    border: 6px solid #e4e7ed;
    border-color: transparent;
    border-top-width: 0;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
}

#time-pick .arrow:after {
    position: absolute;
    content: ' ';
    border-bottom: 8px solid #fff;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    top: 1px;
}

#time-pick .time-pick-box {
    height: 100%;
    width: 100%;
    overflow: auto;
}

#time-pick .time-pick-box .time-pick-item {
    padding: 8px 10px;
    font-size: 14px;
    line-height: 20px;
}

#time-pick .time-pick-box .time-pick-item:hover {
    background-color: #f5f7fa;
    font-weight: 700;
    cursor: pointer;
}

#time-pick .time-pick-box .time-pick-item.disabled:hover {
    cursor: no-drop;
}

#time-pick .time-pick-box .time-pick-item.active {
    color: #5260ff;
    font-weight: 700;
}

/* Ztree */
.ztree li span.button {
    height: 24px;
    width: 24px;
    background-image: url(/static/img/other/ztree_all.png) !important;
    background-position: center center !important;
}

.ztree li span.button.ico_close {
    background-image: url(/static/img/other/ztree_folder.png) !important;
    background-position: center center !important;
}

.ztree li span.button.ico_open {
    background-image: url(/static/img/other/ztree_folder.png) !important;
    background-position: center center !important;
}

.ztree li span.button.noline_close,
.ztree li span.button.root_close,
.ztree li span.button.center_close,
.ztree li span.button.roots_close,
.ztree li span.button.bottom_close {
    background-image: url(/static/img/other/ztree_close.png) !important;
    background-position: center center !important;
}

.ztree li span.button.noline_open,
.ztree li span.button.root_open,
.ztree li span.button.center_open,
.ztree li span.button.roots_open,
.ztree li span.button.bottom_open {
    background-image: url(/static/img/other/ztree_open.png) !important;
    background-position: center center !important;
}

.ztree li span.button.root_open + a .ico_open,
.ztree li span.button.root_close + a .ico_open,
.ztree li span.button.root_open + a .ico_close,
.ztree li span.button.root_close + a .ico_close {
    background-image: url(/static/img/other/ztree_all.png) !important;
    background-position: center center !important;
}

.ztree li span.button.ico_docu {
    background-image: url(/static/img/other/ztree_file.png) !important;
    background-position: center center !important;
}

.ztree li ul.line {
    background-image: none !important;
}

.ztree * {
    font-size: 14px;
    color: #342525;
    font-weight: 400;
}

.ztree li a {
    height: 24px;
}

.ztree li span {
    line-height: 24px;
}

.ztree li span.button.switch {
    height: 24px;
    width: 16px;
}

.ztree li a:hover {
    text-decoration: none;
}

.ztree li a:hover span {
    color: #5260ff;
}

.ztree li a.curSelectedNode {
    background-color: rgba(82, 96, 255, 0.1);
    border: none;
    height: 24px;
    opacity: 1;
    padding-top: 1px;
    /* width: calc(100% - 30px); */
}

.ztree li a.curSelectedNode span {
    color: #5260ff;
}

.ztree li span.button.ico_docu {
    margin: 0;
}

.ztree li span.button.noline_docu {
    background: none !important;
}

.ztree li span.button.chk {
    background-image: url('./img/zTreeStandard.png') !important;
}

.ztree li span.button.chk.checkbox_false_full {
    background-position: 0 -14px !important;
}

/* 表格样式 */
table.oa-table {
    table-layout: fixed;
    width: 100%;
    font-size: 14px;
    margin-bottom: 8px;
    border: none;
}

table.oa-table tr.hide-row {
    visibility: collapse;
}

table.oa-table tr.hide-row td {
    height: 0;
    padding: 0;
    border: none;
}

table.oa-table td,
table.oa-table th {
    height: 30px;
    text-align: center;
    border: 1px solid #e7ebf0;
    /* overflow: hidden; */
    /* text-overflow: ellipsis;
    white-space: nowrap; */
}

table.oa-table td {
    color: #333;
    /* padding: 2px 5px; */
}

table.oa-table tbody tr:nth-child(2n) {
    background-color: #f9f9f9;
}

table.oa-table tbody tr:hover {
    background-color: rgba(82, 96, 255, 0.08);
}

table.oa-table tbody tr.active {
    background-color: rgba(82, 96, 255, 0.08);
}

table.oa-table th {
    background-color: #d2def0;
}

table.oa-table.no-zebra tbody tr:nth-child(2n) {
    background-color: #fff;
}

table.oa-table.no-zebra tbody tr:nth-child(2n):hover {
    background-color: rgba(82, 96, 255, 0.08);
}

table.oa-table.no-hover tbody tr:hover,
table.oa-table.no-zebra.no-hover tbody tr:hover {
    background-color: transparent;
}

#workSheetWaitingListInform ul {
    background-color: #FFF;
    padding: 8px 16px;
    border-radius: 4px;
    box-shadow: 0 0 5px #33333380;
    width: 210px;
}

#workSheetWaitingListInform ul li {
    padding: 8px 0;
    border-bottom: 1px solid #33333380;
}

#workSheetWaitingListInform ul li:nth-child(n*2) {
    background-color: #f9f9f9;
}

#workSheetWaitingListInform ul li:hover {
    background-color: #e7ebf0;
}

#workSheetWaitingListInform ul li:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

#workSheetWaitingListInform ul li:first-child {
    padding-top: 0;
}

#workSheetWaitingListInform ul li div {
    color: #333333b3;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

#workSheetWaitingListInform ul li .title {
    color: #333;
    font-weight: 600;
}

#fileUploadContent .fileUploadDiv {
    width: 126px;
    height: 44px;
    background: #F4F4F4;
    border-radius: 2px;
    position: relative;
    cursor: pointer;
    margin-bottom: 8px;
}

#fileUploadContent .addfile {
    width: 56px;
    height: 20px;
    font-size: 14px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(51, 51, 51, 0.7);
    line-height: 20px;
    position: absolute;
    top: 12px;
    right: 20px;
}

#fileUploadContent .addfileImg {
    position: absolute;
    left: 8px;
    top: 5px;
}

#fileUploadContent .fileListUl > li {
    height: 24px;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
}

#fileUploadContent .previewA {
    margin-left: 40px;
    color: #5260FF;
}

#fileUploadContent .deleteA {
    margin-left: 16px;
    color: #999999;
}

.layui-layer-page {
    animation-name: none !important;
}

.viewer-container .viewer-toolbar .viewer-prev,
.viewer-container .viewer-toolbar .viewer-next {
  position: fixed;
  top: 50%;
  height: 48px;
  width: 48px;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.viewer-container .viewer-toolbar .viewer-prev::before,
.viewer-container .viewer-toolbar .viewer-next::before {
  transform: scale(1.2);
}
.viewer-container .viewer-toolbar .viewer-prev {
  left: 16px;
}
.viewer-container .viewer-toolbar .viewer-next {
  right: 16px;
}