// 自定义验证规则
layui.form.verify({
    id_card: function (value, item) {
        var reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/;
        var $ = layui.$;
        var verifyName = $(item).attr('name'),
            formElem = $(item).parents('.layui-form'); //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find('input[name=' + verifyName + ']'); //获取需要校验的元素

        if (!value || reg.test(value) == false) {
            $(item).addClass('layui-form-danger');
            $(item).focus();
            return '身份证输入不合法';
        }
    },
    the_phone: function (value, item) {
        var reg = /^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])\d{8}$/;
        var $ = layui.$;
        var verifyName = $(item).attr('name'),
            formElem = $(item).parents('.layui-form'); //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find('input[name=' + verifyName + ']'); //获取需要校验的元素

        if (!value || reg.test(value) == false) {
            $(item).addClass('layui-form-danger');
            $(item).focus();
            return '手机号码输入不合法';
        }
    },
    the_phone2: function (value, item) {
        var reg = /^(13[0-9]|14[01456879]|15[0-3,5-9]|16[2567]|17[0-8]|18[0-9]|19[0-3,5-9])\d{8}$/;
        var $ = layui.$;
        var verifyName = $(item).attr('name'),
            formElem = $(item).parents('.layui-form'); //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find('input[name=' + verifyName + ']'); //获取需要校验的元素

        if (value != '' && reg.test(value) == false) {
            $(item).addClass('layui-form-danger');
            $(item).focus();
            return '手机号码输入不合法';
        }
    },
    the_email: function (value, item) {
        var reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        var $ = layui.$;
        var verifyName = $(item).attr('name'),
            formElem = $(item).parents('.layui-form'); //获取当前所在的form元素，如果存在的话
        verifyElem = formElem.find('input[name=' + verifyName + ']'); //获取需要校验的元素

        if (value != '' && reg.test(value) == false) {
            $(item).addClass('layui-form-danger');
            $(item).focus();
            return '邮箱不合法';
        }
    },
    isPhone: function (value, item) {
        if (!common.reg.isPhone(value) && value) {
            $(item).focus();
            return '请输入正确的手机号码';
        }
    },
    idCard: function (value, item) {
        if (!common.reg.isIdCard(value)) {
            $(item).focus();
            return '请输入正确的身份证号码';
        }
    },
});
