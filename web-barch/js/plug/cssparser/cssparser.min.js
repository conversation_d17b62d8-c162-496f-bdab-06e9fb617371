/* parser generated by jison 0.4.18 */
/*
  Returns a Parser object of the following structure:

  Parser: {
    yy: {}
  }

  Parser.prototype: {
    yy: {},
    trace: function(),
    symbols_: {associative list: name ==> number},
    terminals_: {associative list: number ==> name},
    productions_: [...],
    performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate, $$, _$),
    table: [...],
    defaultActions: {...},
    parseError: function(str, hash),
    parse: function(input),

    lexer: {
        EOF: 1,
        parseError: function(str, hash),
        setInput: function(input),
        input: function(),
        unput: function(str),
        more: function(),
        less: function(n),
        pastInput: function(),
        upcomingInput: function(),
        showPosition: function(),
        test_match: function(regex_match_array, rule_index),
        next: function(),
        lex: function(),
        begin: function(condition),
        popState: function(),
        _currentRules: function(),
        topState: function(),
        pushState: function(condition),

        options: {
            ranges: boolean           (optional: true ==> token location info will include a .range[] member)
            flex: boolean             (optional: true ==> flex-like lexing behaviour where the rules are tested exhaustively to find the longest match)
            backtrack_lexer: boolean  (optional: true ==> lexer regexes are tested in order and for each matching regex the action code is invoked; the lexer terminates the scan when a token is returned by the action code)
        },

        performAction: function(yy, yy_, $avoiding_name_collisions, YY_START),
        rules: [...],
        conditions: {associative list: name ==> set},
    }
  }


  token location info (@$, _$, etc.): {
    first_line: n,
    last_line: n,
    first_column: n,
    last_column: n,
    range: [start_number, end_number]       (where the numbers are indexes into the input string, regular zero-based)
  }


  the parseError function receives a 'hash' object with these members for lexer and parser errors: {
    text:        (matched text)
    token:       (the produced terminal token, if any)
    line:        (yylineno)
  }
  while parser (grammar) errors will also provide these members, i.e. parser errors deliver a superset of attributes: {
    loc:         (yylloc)
    expected:    (string describing the set of expected tokens)
    recoverable: (boolean: TRUE when the parser has a error recovery rule available for this particular error)
  }
*/
var cssparser = (function () {
    var o = function (k, v, o, l) {
            for (o = o || {}, l = k.length; l--; o[k[l]] = v);
            return o;
        },
        $V0 = [1, 5],
        $V1 = [1, 6],
        $V2 = [1, 25],
        $V3 = [1, 26],
        $V4 = [1, 27],
        $V5 = [1, 43],
        $V6 = [1, 21],
        $V7 = [1, 31],
        $V8 = [1, 44],
        $V9 = [1, 20],
        $Va = [1, 45],
        $Vb = [1, 52],
        $Vc = [1, 46],
        $Vd = [1, 39],
        $Ve = [1, 49],
        $Vf = [1, 50],
        $Vg = [1, 48],
        $Vh = [1, 47],
        $Vi = [1, 40],
        $Vj = [1, 41],
        $Vk = [1, 42],
        $Vl = [1, 51],
        $Vm = [5, 7, 8, 25, 27, 30, 36, 37, 46, 48, 56, 57, 67, 85, 86, 121, 122, 123, 124, 125, 126, 127, 128],
        $Vn = [1, 57],
        $Vo = [5, 7, 8, 15, 25, 27, 30, 36, 37, 46, 48, 56, 57, 67, 85, 86, 121, 122, 123, 124, 125, 126, 127, 128],
        $Vp = [14, 51],
        $Vq = [1, 67],
        $Vr = [1, 69],
        $Vs = [1, 66],
        $Vt = [1, 68],
        $Vu = [14, 51, 69],
        $Vv = [1, 74],
        $Vw = [1, 73],
        $Vx = [1, 75],
        $Vy = [1, 82],
        $Vz = [1, 87],
        $VA = [14, 51, 67, 69, 85, 86, 87, 112, 113, 121, 122, 123, 124, 125, 126, 127, 128],
        $VB = [1, 95],
        $VC = [1, 96],
        $VD = [1, 94],
        $VE = [1, 99],
        $VF = [1, 100],
        $VG = [1, 101],
        $VH = [1, 102],
        $VI = [1, 106],
        $VJ = [1, 107],
        $VK = [1, 118],
        $VL = [1, 127],
        $VM = [1, 128],
        $VN = [1, 129],
        $VO = [14, 15, 17, 51, 52, 62, 63, 65, 67, 69, 82, 83, 85, 86, 87, 88, 89, 102, 106, 107, 112, 113, 121, 122, 123, 124, 125, 126, 127, 128, 130, 131, 132, 133, 134, 135, 140, 142, 144, 148],
        $VP = [2, 180],
        $VQ = [67, 85, 86, 121, 122, 123, 124, 125, 126, 127, 128],
        $VR = [17, 62, 65, 94, 123, 125, 148],
        $VS = [1, 142],
        $VT = [14, 17, 51],
        $VU = [1, 144],
        $VV = [123, 125, 148],
        $VW = [14, 63, 64],
        $VX = [1, 163],
        $VY = [5, 7, 8, 15, 25, 27, 30, 36, 37, 46, 48, 56, 57, 67, 85, 86, 121, 122, 123, 124, 125, 126, 127, 128, 144, 148],
        $VZ = [15, 17],
        $V_ = [15, 17, 102],
        $V$ = [15, 25, 27, 30, 36, 37, 46, 48, 56, 57, 67, 85, 86, 121, 122, 123, 124, 125, 126, 127, 128],
        $V01 = [1, 190],
        $V11 = [62, 65],
        $V21 = [1, 211],
        $V31 = [1, 212],
        $V41 = [1, 207],
        $V51 = [52, 107, 123, 125, 140, 142, 144, 147, 148],
        $V61 = [52, 62, 67, 69, 83, 85, 86, 88, 107, 121, 122, 123, 124, 125, 126, 127, 128, 139, 140, 142, 144, 148],
        $V71 = [1, 235],
        $V81 = [1, 232],
        $V91 = [1, 233],
        $Va1 = [1, 234],
        $Vb1 = [15, 123, 125, 144, 148],
        $Vc1 = [14, 17, 51, 63],
        $Vd1 = [69, 130],
        $Ve1 = [15, 17, 51, 52, 62, 69, 83, 85, 86, 87, 88, 89, 102, 106, 107, 121, 122, 123, 125, 127, 130, 140, 142, 144, 148],
        $Vf1 = [1, 246],
        $Vg1 = [15, 17, 51, 69, 102, 106],
        $Vh1 = [15, 17, 51, 52, 62, 69, 83, 102, 106, 107, 121, 122, 123, 125, 127, 140, 142, 144, 148],
        $Vi1 = [1, 250],
        $Vj1 = [1, 251],
        $Vk1 = [1, 252],
        $Vl1 = [1, 253],
        $Vm1 = [1, 254],
        $Vn1 = [15, 17, 51, 52, 62, 69, 83, 85, 86, 87, 88, 89, 102, 106, 107, 121, 122, 123, 125, 127, 140, 142, 144, 148],
        $Vo1 = [52, 62, 83, 107, 121, 122, 123, 125, 127, 140, 142, 144, 148],
        $Vp1 = [69, 85, 86, 87, 88, 89];
    var parser = {
        trace: function trace() {},
        yy: {},
        symbols_: {
            error: 2,
            stylesheet: 3,
            StylesheetList: 4,
            EOF: 5,
            StylesheetComponent: 6,
            CDO: 7,
            CDC: 8,
            QualifiedRule: 9,
            AtRule: 10,
            RuleList: 11,
            RuleListComponent: 12,
            RuleBlock: 13,
            LEFT_CURLY_BRACKET: 14,
            RIGHT_CURLY_BRACKET: 15,
            AtSimpleRules: 16,
            SEMICOLON: 17,
            AtNestedRule: 18,
            AtFontface: 19,
            AtKeyframes: 20,
            AtPage: 21,
            AtRuleCharset: 22,
            AtImport: 23,
            AtNamespace: 24,
            AT_CHARSET: 25,
            StringVal: 26,
            AT_IMPORT: 27,
            UrlOrStringVal: 28,
            MediaQueryList: 29,
            AT_NAMESPACE: 30,
            IDENT: 31,
            AtNestedRuleComponent: 32,
            AtMedia: 33,
            AtDocument: 34,
            AtSupport: 35,
            AT_MEDIA: 36,
            AT_KEYFRAMES: 37,
            AtKeyframesName: 38,
            AtKeyframesBlockList: 39,
            AtKeyframesBlock: 40,
            AtKeyframesSelector: 41,
            DeclarationList: 42,
            IdentVal: 43,
            PercentageVal: 44,
            AtPageComponent: 45,
            AT_PAGE: 46,
            PseudoClassSelectorList: 47,
            AT_DOCUMENT: 48,
            AtDocumentFuncValList: 49,
            AtDocumentFuncVal: 50,
            COMMA: 51,
            URL_FUNC: 52,
            URL_PREFIX_FUNC: 53,
            DOMAIN_FUNC: 54,
            REGEXP_FUNC: 55,
            AT_FONT_FACE: 56,
            AT_SUPPORTS: 57,
            AtSupportExpressionList: 58,
            AtSupportExpression: 59,
            AndOrOperator: 60,
            AtSupportExpressionComponent: 61,
            OPERATOR_NOT: 62,
            OPERATOR_AND: 63,
            OPERATOR_OR: 64,
            LEFT_PARENTHESIS: 65,
            PropertyName: 66,
            COLON: 67,
            PropertyValue: 68,
            RIGHT_PARENTHESIS: 69,
            PropertyValueComponent: 70,
            SinglePropertyValue: 71,
            SequencialPropertyValue: 72,
            GenericPropertyValue: 73,
            CalcOperator: 74,
            UrlVal: 75,
            FunctionVal: 76,
            GenericNumericVal: 77,
            HashVal: 78,
            CalcFunction: 79,
            FUNCTION: 80,
            FunctionParameters: 81,
            ASSIGN_MARK: 82,
            CALC_FUNC: 83,
            CalcExpression: 84,
            ASTERISK: 85,
            ASTERISK_WITH_WHITESPACE: 86,
            PLUS_SIGN: 87,
            HYPHEN_MINUS: 88,
            SOLIDUS: 89,
            MediaQuery: 90,
            MediaQueryExpressionList: 91,
            OnlyNot: 92,
            And: 93,
            OPERATOR_ONLY: 94,
            MediaQueryExpression: 95,
            MediaFeature: 96,
            GenericVal: 97,
            SelectorList: 98,
            Declaration: 99,
            DeclarationComponent: 100,
            DeclarationMandatoryComponent: 101,
            IMPORTANT: 102,
            DeclarationMandatoryPart: 103,
            DeclarationMandatoryPartWithIEHack: 104,
            UNDERSCORE: 105,
            REVERSE_SOLIDUS: 106,
            NUMBER: 107,
            SelectorGroup: 108,
            Selector: 109,
            SelectorCombinator: 110,
            DescendantSelector: 111,
            GREATER_THAN_SIGN: 112,
            TILDE: 113,
            UniversalSelector: 114,
            ClassSelector: 115,
            TypeSelector: 116,
            IdSelector: 117,
            AttributeSelector: 118,
            PseudoClassSelector: 119,
            PseudoElementSelector: 120,
            HASH_STRING: 121,
            HEXA_NUMBER: 122,
            GENERAL_IDENT: 123,
            FULL_STOP: 124,
            SELECTOR_TYPE_WITH_WHITESPACE: 125,
            SELECTOR_CLASS_WITH_WHITESPACE: 126,
            SELECTOR_ID_WITH_WHITESPACE: 127,
            LEFT_SQUARE_BRACKET: 128,
            SelectorAttrOperator: 129,
            RIGHT_SQUARE_BRACKET: 130,
            INCLUDE_MATCH: 131,
            DASH_MATCH: 132,
            PREFIX_MATCH: 133,
            SUFFIX_MATCH: 134,
            SUBSTRING_MATCH: 135,
            PseudoClassFunc: 136,
            PseudoClassFuncParam: 137,
            PseudoClassFuncParam_an_plus_b: 138,
            N: 139,
            DIMENSION: 140,
            NumberVal: 141,
            STRING: 142,
            DimensionVal: 143,
            PERCENTAGE: 144,
            IdOrUrlOrStringVal: 145,
            NumericVal: 146,
            HexaNumericVal: 147,
            VENDOR_PREFIX_IDENT: 148,
            $accept: 0,
            $end: 1,
        },
        terminals_: {
            2: 'error',
            5: 'EOF',
            7: 'CDO',
            8: 'CDC',
            14: 'LEFT_CURLY_BRACKET',
            15: 'RIGHT_CURLY_BRACKET',
            17: 'SEMICOLON',
            25: 'AT_CHARSET',
            27: 'AT_IMPORT',
            30: 'AT_NAMESPACE',
            36: 'AT_MEDIA',
            37: 'AT_KEYFRAMES',
            46: 'AT_PAGE',
            48: 'AT_DOCUMENT',
            51: 'COMMA',
            52: 'URL_FUNC',
            53: 'URL_PREFIX_FUNC',
            54: 'DOMAIN_FUNC',
            55: 'REGEXP_FUNC',
            56: 'AT_FONT_FACE',
            57: 'AT_SUPPORTS',
            62: 'OPERATOR_NOT',
            63: 'OPERATOR_AND',
            64: 'OPERATOR_OR',
            65: 'LEFT_PARENTHESIS',
            67: 'COLON',
            69: 'RIGHT_PARENTHESIS',
            82: 'ASSIGN_MARK',
            83: 'CALC_FUNC',
            85: 'ASTERISK',
            86: 'ASTERISK_WITH_WHITESPACE',
            87: 'PLUS_SIGN',
            88: 'HYPHEN_MINUS',
            89: 'SOLIDUS',
            94: 'OPERATOR_ONLY',
            102: 'IMPORTANT',
            105: 'UNDERSCORE',
            106: 'REVERSE_SOLIDUS',
            107: 'NUMBER',
            112: 'GREATER_THAN_SIGN',
            113: 'TILDE',
            121: 'HASH_STRING',
            122: 'HEXA_NUMBER',
            123: 'GENERAL_IDENT',
            124: 'FULL_STOP',
            125: 'SELECTOR_TYPE_WITH_WHITESPACE',
            126: 'SELECTOR_CLASS_WITH_WHITESPACE',
            127: 'SELECTOR_ID_WITH_WHITESPACE',
            128: 'LEFT_SQUARE_BRACKET',
            130: 'RIGHT_SQUARE_BRACKET',
            131: 'INCLUDE_MATCH',
            132: 'DASH_MATCH',
            133: 'PREFIX_MATCH',
            134: 'SUFFIX_MATCH',
            135: 'SUBSTRING_MATCH',
            139: 'N',
            140: 'DIMENSION',
            142: 'STRING',
            144: 'PERCENTAGE',
            147: 'HexaNumericVal',
            148: 'VENDOR_PREFIX_IDENT',
        },
        productions_: [
            0,
            [3, 2],
            [3, 1],
            [4, 1],
            [4, 2],
            [6, 1],
            [6, 1],
            [6, 1],
            [6, 1],
            [11, 1],
            [11, 2],
            [12, 1],
            [12, 1],
            [13, 3],
            [13, 2],
            [10, 2],
            [10, 1],
            [10, 1],
            [10, 1],
            [10, 1],
            [16, 1],
            [16, 1],
            [16, 1],
            [22, 2],
            [23, 2],
            [23, 3],
            [24, 2],
            [24, 3],
            [18, 2],
            [32, 1],
            [32, 1],
            [32, 1],
            [33, 2],
            [20, 4],
            [20, 5],
            [39, 1],
            [39, 2],
            [40, 2],
            [38, 1],
            [38, 1],
            [41, 1],
            [41, 1],
            [21, 2],
            [45, 1],
            [45, 2],
            [34, 2],
            [49, 1],
            [49, 3],
            [50, 1],
            [50, 1],
            [50, 1],
            [50, 1],
            [19, 2],
            [35, 2],
            [58, 1],
            [58, 3],
            [59, 1],
            [59, 2],
            [60, 1],
            [60, 1],
            [61, 5],
            [66, 1],
            [68, 1],
            [68, 3],
            [70, 1],
            [70, 1],
            [72, 2],
            [72, 2],
            [71, 1],
            [71, 3],
            [73, 1],
            [73, 1],
            [73, 1],
            [73, 1],
            [73, 1],
            [73, 1],
            [73, 1],
            [76, 2],
            [76, 3],
            [80, 2],
            [80, 2],
            [81, 1],
            [81, 3],
            [79, 2],
            [79, 3],
            [84, 1],
            [84, 3],
            [74, 1],
            [74, 1],
            [74, 1],
            [74, 1],
            [74, 1],
            [29, 1],
            [29, 3],
            [90, 1],
            [90, 1],
            [90, 2],
            [90, 3],
            [90, 4],
            [92, 1],
            [92, 1],
            [93, 1],
            [91, 1],
            [91, 3],
            [95, 3],
            [95, 5],
            [96, 1],
            [9, 2],
            [42, 3],
            [42, 2],
            [99, 1],
            [99, 2],
            [99, 3],
            [100, 1],
            [100, 2],
            [101, 1],
            [101, 1],
            [104, 2],
            [104, 2],
            [104, 2],
            [104, 3],
            [103, 3],
            [98, 1],
            [98, 3],
            [108, 1],
            [108, 2],
            [108, 3],
            [108, 1],
            [108, 2],
            [108, 3],
            [110, 1],
            [110, 1],
            [110, 1],
            [109, 1],
            [109, 1],
            [109, 1],
            [109, 1],
            [109, 1],
            [109, 1],
            [109, 1],
            [114, 1],
            [117, 1],
            [117, 1],
            [116, 1],
            [115, 2],
            [115, 2],
            [115, 2],
            [115, 2],
            [115, 2],
            [111, 1],
            [111, 1],
            [111, 1],
            [111, 1],
            [118, 5],
            [118, 3],
            [129, 1],
            [129, 1],
            [129, 1],
            [129, 1],
            [129, 1],
            [129, 1],
            [120, 3],
            [47, 1],
            [47, 2],
            [119, 2],
            [119, 2],
            [136, 2],
            [136, 3],
            [137, 1],
            [137, 1],
            [138, 1],
            [138, 3],
            [138, 4],
            [138, 1],
            [138, 1],
            [138, 3],
            [141, 1],
            [26, 1],
            [143, 1],
            [75, 1],
            [43, 1],
            [78, 1],
            [78, 1],
            [78, 1],
            [44, 1],
            [28, 1],
            [28, 1],
            [145, 1],
            [145, 1],
            [145, 1],
            [77, 1],
            [77, 1],
            [77, 1],
            [146, 1],
            [146, 1],
            [97, 1],
            [97, 1],
            [31, 1],
            [31, 1],
            [31, 1],
        ],
        performAction: function anonymous(yytext, yyleng, yylineno, yy, yystate /* action[1] */, $$ /* vstack */, _$ /* lstack */) {
            /* this == yyval */

            var $0 = $$.length - 1;
            switch (yystate) {
                case 1:
                    return $$[$0 - 1];

                    break;
                case 2:
                    return StyleSheet.create();

                    break;
                case 3:
                    this.$ = StyleSheet.create().add($$[$0]);
                    break;
                case 4:
                case 36:
                case 67:
                    this.$ = $$[$0 - 1].add($$[$0]);
                    break;
                case 9:
                    this.$ = [$$[$0]];
                    break;
                case 10:
                    this.$ = concat($$[$0 - 1], $$[$0]);
                    break;
                case 13:
                    this.$ = $$[$0 - 1];
                    break;
                case 14:
                    this.$ = null;
                    break;
                case 23:
                    this.$ = AtCharset.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 24:
                    this.$ = AtImport.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 25:
                    this.$ = AtImport.create($$[$0 - 2])
                        .set('value', $$[$0 - 1])
                        .set('nextExpression', $$[$0]);
                    break;
                case 26:
                    this.$ = AtNamespace.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 27:
                    this.$ = AtNamespace.create($$[$0 - 2])
                        .set('prefix', $$[$0 - 1])
                        .set('value', $$[$0]);
                    break;
                case 28:
                case 42:
                    this.$ = $$[$0 - 1].set('nestedRules', $$[$0]);
                    break;
                case 32:
                    this.$ = AtMedia.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 33:
                    this.$ = AtKeyframes.create($$[$0 - 3]).set('name', $$[$0 - 2]);
                    break;
                case 34:
                    this.$ = AtKeyframes.create($$[$0 - 4])
                        .set('name', $$[$0 - 3])
                        .set('value', $$[$0 - 1]);
                    break;
                case 35:
                    this.$ = AtKeyframesBlockList.create().add($$[$0]);
                    break;
                case 37:
                    this.$ = AtKeyframesBlock.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 43:
                    this.$ = AtPage.create($$[$0]);
                    break;
                case 44:
                    this.$ = AtPage.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 45:
                    this.$ = AtDocument.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 46:
                    this.$ = concat($$[$0], []);
                    break;
                case 47:
                case 63:
                case 112:
                    this.$ = concat($$[$0 - 2], $$[$0]);
                    break;
                case 48:
                    this.$ = FunctionVal.create('url', $$[$0]);
                    break;
                case 49:
                    this.$ = FunctionVal.create('url-prefix', $$[$0]);
                    break;
                case 50:
                    this.$ = FunctionVal.create('domain', $$[$0]);
                    break;
                case 51:
                    this.$ = FunctionVal.create('regexp', $$[$0]);
                    break;
                case 52:
                    this.$ = AtFontface.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 53:
                    this.$ = AtSupport.create($$[$0 - 1]).set('value', $$[$0]);
                    break;
                case 55:
                    this.$ = $$[$0 - 2];
                    $$[$0 - 2].set('nextExpression', $$[$0 - 1]);
                    $$[$0 - 1].set('nextExpression', $$[$0]);

                    break;
                case 57:
                    this.$ = $$[$0].set('operator', $$[$0 - 1]);
                    break;
                case 58:
                case 59:
                case 101:
                    this.$ = Operator.create($$[$0]);
                    break;
                case 60:
                    this.$ = AtSupportExpression.create()
                        .set('property', $$[$0 - 3])
                        .set('value', $$[$0 - 1]);
                    break;
                case 66:
                    this.$ = SequenceVal.create($$[$0 - 1]).add($$[$0]);
                    break;
                case 69:
                    this.$ = Expression.create()
                        .set('operator', $$[$0 - 1])
                        .set('lhs', $$[$0 - 2])
                        .set('rhs', $$[$0]);
                    break;
                case 77:
                case 166:
                    this.$ = FunctionVal.create($$[$0 - 1]);
                    break;
                case 78:
                case 167:
                    this.$ = FunctionVal.create($$[$0 - 2], $$[$0 - 1]);
                    break;
                case 80:
                    this.$ = IdentVal.create($$[$0 - 1]);
                    break;
                case 81:
                    this.$ = $$[$0];
                    break;
                case 82:
                case 86:
                    this.$ = Expression.create($$[$0 - 1], $$[$0 - 2], $$[$0]) /* same as `Expression.create().set('operator', $$[$0-1]).set('lhs', $$[$0-2]).set('rhs', $$[$0])` */;
                    break;
                case 83:
                    this.$ = FunctionVal.create('calc');
                    break;
                case 84:
                    this.$ = FunctionVal.create('calc', $$[$0 - 1]);
                    break;
                case 88:
                case 199:
                    this.$ = $$[$0].trimRight();
                    break;
                case 92:
                    this.$ = MediaQueryList.create().add($$[$0]);
                    break;
                case 93:
                case 123:
                    this.$ = $$[$0 - 2].add($$[$0]);
                    break;
                case 95:
                    this.$ = MediaQuery.create().set('mediaType', $$[$0]);
                    break;
                case 96:
                    this.$ = MediaQuery.create()
                        .set('prefix', $$[$0 - 1])
                        .set('mediaType', $$[$0]);
                    break;
                case 97:
                    this.$ = MediaQuery.create().set('mediaType', $$[$0 - 2]);
                    this.$.set('nextExpression', $$[$0 - 1]);
                    $$[$0 - 1].set('nextExpression', $$[$0]);

                    break;
                case 98:
                    this.$ = MediaQuery.create()
                        .set('prefix', $$[$0 - 3])
                        .set('mediaType', $$[$0 - 2]);
                    this.$.set('nextExpression', $$[$0 - 1]);
                    $$[$0 - 1].set('nextExpression', $$[$0]);

                    break;
                case 103:
                    this.$ = $$[$0 - 2];
                    $$[$0 - 2].set('nextExpression', $$[$0 - 1]);
                    $$[$0 - 1].set('nextExpression', $$[$0]);

                    break;
                case 104:
                    this.$ = MediaQueryExpression.create($$[$0 - 1]);
                    break;
                case 105:
                    this.$ = MediaQueryExpression.create($$[$0 - 3], $$[$0 - 1]);
                    break;
                case 107:
                    this.$ = QualifiedRule.create($$[$0]).set('selectors', $$[$0 - 1]);
                    break;
                case 108:
                    this.$ = DeclarationList.create($$[$0 - 1]);
                    break;
                case 109:
                    this.$ = DeclarationList.create();
                    break;
                case 114:
                    this.$ = $$[$0 - 1].set('important', true);
                    break;
                case 116:
                    this.$ = $$[$0].set('ieOnlyHack', true);
                    break;
                case 117:
                case 118:
                    this.$ = $$[$0].set('asteriskHack', true);
                    break;
                case 119:
                    this.$ = $$[$0].set('underscoreHack', true);
                    break;
                case 120:
                    this.$ = $$[$0 - 2].set('backslashHack', true);
                    break;
                case 121:
                    this.$ = Declaration.create($$[$0 - 2], $$[$0]);
                    break;
                case 122:
                    this.$ = SelectorList.create().add($$[$0]);
                    break;
                case 125:
                    this.$ = $$[$0 - 1];
                    $$[$0 - 1].set('nextSelector', $$[$0]);

                    break;
                case 126:
                case 129:
                    this.$ = $$[$0 - 2];
                    $$[$0 - 2].set('nextSelector', $$[$0 - 1]);
                    $$[$0 - 1].set('nextSelector', $$[$0]);

                    break;
                case 128:
                    var combinator = DescendantSelectorCombinator.create(' ');

                    // $$[$0-1] -> combinator -> $$[$0]
                    this.$ = $$[$0 - 1];
                    this.$.set('nextSelector', combinator);
                    combinator.set('nextSelector', $$[$0]);

                    break;
                case 130:
                    this.$ = ChildSelectorCombinator.create($$[$0]);
                    break;
                case 131:
                    this.$ = AdjacentSiblingSelectorCombinator.create($$[$0]);
                    break;
                case 132:
                    this.$ = SiblingSelectorCombinator.create($$[$0]);
                    break;
                case 140:
                    this.$ = UniversalSelector.create($$[$0]);
                    break;
                case 141:
                case 142:
                    this.$ = IdSelector.create(HashVal.create($$[$0]));
                    break;
                case 143:
                    this.$ = TypeSelector.create($$[$0]);
                    break;
                case 144:
                case 145:
                case 146:
                case 147:
                case 148:
                    this.$ = ClassSelector.create($$[$0 - 1] + $$[$0]);
                    break;
                case 149:
                    this.$ = UniversalSelector.create($$[$0].trimRight());
                    break;
                case 150:
                    this.$ = TypeSelector.create($$[$0].trimRight());
                    break;
                case 151:
                    this.$ = ClassSelector.create($$[$0].trimRight());
                    break;
                case 152:
                    this.$ = IdSelector.create(HashVal.create($$[$0].trimRight()));
                    break;
                case 153:
                    this.$ = AttributeSelector.create(Expression.create($$[$0 - 2], $$[$0 - 3], $$[$0 - 1]));
                    break;
                case 154:
                    this.$ = AttributeSelector.create($$[$0 - 1]);
                    break;
                case 155:
                    this.$ = Operator.create($$[$0]) /* include   */;
                    break;
                case 156:
                    this.$ = Operator.create($$[$0]) /* dash      */;
                    break;
                case 157:
                    this.$ = Operator.create($$[$0]) /* prefix    */;
                    break;
                case 158:
                    this.$ = Operator.create($$[$0]) /* suffix    */;
                    break;
                case 159:
                    this.$ = Operator.create($$[$0]) /* substring */;
                    break;
                case 160:
                    this.$ = Operator.create($$[$0]) /* equal     */;
                    break;
                case 161:
                    this.$ = PseudoElementSelector.create($$[$0]);
                    break;
                case 163:
                    this.$ = $$[$0 - 1].set('nextSelector', $$[$0]);
                    break;
                case 164:
                case 165:
                    this.$ = PseudoClassSelector.create($$[$0]);
                    break;
                case 170:
                    this.$ = $$[$0] /* n */;
                    break;
                case 171:
                    this.$ = $$[$0 - 2] + $$[$0 - 1] + $$[$0] /* n + 1 */;
                    break;
                case 172:
                    this.$ = $$[$0 - 3] + $$[$0 - 2] + $$[$0 - 1] /* -n + 1 */;
                    break;
                case 173:
                    this.$ = $$[$0] /* 1 */;
                    break;
                case 174:
                    this.$ = $$[$0] /* 2n or -2n */;
                    break;
                case 175:
                    this.$ = $$[$0 - 2] + $$[$0 - 1] /* -n + 1 */;
                    break;
                case 176:
                    this.$ = NumberVal.create($$[$0]);
                    break;
                case 177:
                    this.$ = StringVal.create($$[$0]);
                    break;
                case 178:
                    this.$ = DimensionVal.create($$[$0]);
                    break;
                case 179:
                    this.$ = UrlVal.create($$[$0]);
                    break;
                case 180:
                    this.$ = IdentVal.create($$[$0]);
                    break;
                case 181:
                case 182:
                    this.$ = HashVal.create($$[$0]);
                    break;
                case 183:
                    this.$ = HashVal.create($$[$0].trimRight());
                    break;
                case 184:
                    this.$ = PercentageVal.create($$[$0]);
                    break;
            }
        },
        table: [
            {
                3: 1,
                4: 2,
                5: [1, 3],
                6: 4,
                7: $V0,
                8: $V1,
                9: 7,
                10: 8,
                16: 10,
                18: 11,
                19: 12,
                20: 13,
                21: 14,
                22: 16,
                23: 17,
                24: 18,
                25: $V2,
                27: $V3,
                30: $V4,
                32: 19,
                33: 28,
                34: 29,
                35: 30,
                36: $V5,
                37: $V6,
                45: 22,
                46: $V7,
                48: $V8,
                56: $V9,
                57: $Va,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                98: 9,
                108: 15,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            { 1: [3] },
            {
                5: [1, 53],
                6: 54,
                7: $V0,
                8: $V1,
                9: 7,
                10: 8,
                16: 10,
                18: 11,
                19: 12,
                20: 13,
                21: 14,
                22: 16,
                23: 17,
                24: 18,
                25: $V2,
                27: $V3,
                30: $V4,
                32: 19,
                33: 28,
                34: 29,
                35: 30,
                36: $V5,
                37: $V6,
                45: 22,
                46: $V7,
                48: $V8,
                56: $V9,
                57: $Va,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                98: 9,
                108: 15,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            { 1: [2, 2] },
            o($Vm, [2, 3]),
            o($Vm, [2, 5]),
            o($Vm, [2, 6]),
            o($Vm, [2, 7]),
            o($Vm, [2, 8]),
            { 14: $Vn, 42: 55, 51: [1, 56] },
            { 17: [1, 58] },
            o($Vo, [2, 16]),
            o($Vo, [2, 17]),
            o($Vo, [2, 18]),
            o($Vo, [2, 19]),
            o($Vp, [2, 122]),
            { 17: [2, 20] },
            { 17: [2, 21] },
            { 17: [2, 22] },
            { 13: 59, 14: [1, 60] },
            { 14: $Vn, 42: 61 },
            { 26: 64, 31: 65, 38: 62, 43: 63, 123: $Vq, 125: $Vr, 142: $Vs, 148: $Vt },
            { 14: $Vn, 42: 70 },
            o($Vu, [2, 124], {
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                108: 71,
                110: 72,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                87: $Vv,
                112: $Vw,
                113: $Vx,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            }),
            o($Vu, [2, 127], {
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                108: 76,
                110: 77,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                87: $Vv,
                112: $Vw,
                113: $Vx,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            }),
            { 26: 78, 142: $Vs },
            { 26: 80, 28: 79, 52: $Vy, 75: 81, 142: $Vs },
            { 26: 80, 28: 83, 31: 84, 52: $Vy, 75: 81, 123: $Vq, 125: $Vr, 142: $Vs, 148: $Vt },
            { 14: [2, 29] },
            { 14: [2, 30] },
            { 14: [2, 31] },
            { 14: [2, 43], 47: 85, 67: $Vz, 119: 86 },
            o($VA, [2, 133]),
            o($VA, [2, 134]),
            o($VA, [2, 135]),
            o($VA, [2, 136]),
            o($VA, [2, 137]),
            o($VA, [2, 138]),
            o($VA, [2, 139]),
            o($VA, [2, 149]),
            o($VA, [2, 150]),
            o($VA, [2, 151]),
            o($VA, [2, 152]),
            { 29: 88, 31: 65, 43: 91, 62: $VB, 65: $VC, 90: 89, 91: 90, 92: 92, 94: $VD, 95: 93, 123: $Vq, 125: $Vr, 148: $Vt },
            { 49: 97, 50: 98, 52: $VE, 53: $VF, 54: $VG, 55: $VH },
            { 58: 103, 59: 104, 61: 105, 62: $VI, 65: $VJ },
            o($VA, [2, 140]),
            { 31: 108, 62: [1, 112], 63: [1, 109], 64: [1, 110], 94: [1, 111], 123: $Vq, 125: $Vr, 148: $Vt },
            o($VA, [2, 143]),
            o($VA, [2, 141]),
            o($VA, [2, 142]),
            { 31: 65, 43: 113, 123: $Vq, 125: $Vr, 148: $Vt },
            { 31: 65, 43: 114, 62: $VK, 67: [1, 116], 80: 117, 123: $Vq, 125: $Vr, 136: 115, 148: $Vt },
            { 1: [2, 1] },
            o($Vm, [2, 4]),
            o($Vo, [2, 107]),
            {
                67: $Vb,
                85: $Vc,
                86: $Vd,
                108: 119,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            { 15: [1, 121], 31: 65, 43: 130, 66: 126, 85: $VL, 86: $VM, 99: 120, 100: 122, 101: 123, 103: 124, 104: 125, 105: $VN, 123: $Vq, 125: $Vr, 148: $Vt },
            o($Vo, [2, 15]),
            o($Vo, [2, 28]),
            {
                9: 134,
                10: 135,
                11: 131,
                12: 133,
                15: [1, 132],
                16: 10,
                18: 11,
                19: 12,
                20: 13,
                21: 14,
                22: 16,
                23: 17,
                24: 18,
                25: $V2,
                27: $V3,
                30: $V4,
                32: 19,
                33: 28,
                34: 29,
                35: 30,
                36: $V5,
                37: $V6,
                45: 22,
                46: $V7,
                48: $V8,
                56: $V9,
                57: $Va,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                98: 9,
                108: 15,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            o($Vo, [2, 52]),
            { 14: [1, 136] },
            { 14: [2, 38] },
            { 14: [2, 39] },
            o($VO, $VP),
            o([14, 15, 17, 51, 52, 62, 65, 69, 83, 85, 86, 87, 88, 89, 94, 102, 106, 107, 121, 122, 123, 125, 127, 130, 140, 142, 144, 148], [2, 177]),
            o($VO, [2, 197]),
            o($VO, [2, 198]),
            o($VO, [2, 199]),
            o($Vo, [2, 42]),
            o($Vu, [2, 125]),
            {
                67: $Vb,
                85: $Vc,
                86: $Vd,
                108: 137,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            o($VQ, [2, 130]),
            o($VQ, [2, 131]),
            o($VQ, [2, 132]),
            o($Vu, [2, 128]),
            {
                67: $Vb,
                85: $Vc,
                86: $Vd,
                108: 138,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            { 17: [2, 23] },
            { 17: [2, 24], 29: 139, 31: 65, 43: 91, 62: $VB, 65: $VC, 90: 89, 91: 90, 92: 92, 94: $VD, 95: 93, 123: $Vq, 125: $Vr, 148: $Vt },
            o($VR, [2, 185]),
            o($VR, [2, 186]),
            o([15, 17, 51, 52, 62, 65, 69, 83, 85, 86, 87, 88, 89, 94, 102, 106, 107, 121, 122, 123, 125, 127, 130, 140, 142, 144, 148], [2, 179]),
            { 17: [2, 26] },
            { 26: 80, 28: 140, 52: $Vy, 75: 81, 142: $Vs },
            { 14: [2, 44] },
            { 14: [2, 162], 47: 141, 67: $Vz, 119: 86 },
            { 31: 65, 43: 114, 62: $VK, 80: 117, 123: $Vq, 125: $Vr, 136: 115, 148: $Vt },
            { 14: [2, 32], 51: $VS },
            o($VT, [2, 92]),
            o($VT, [2, 94]),
            o($VT, [2, 95], { 93: 143, 63: $VU }),
            { 31: 65, 43: 145, 123: $Vq, 125: $Vr, 148: $Vt },
            o($VT, [2, 102], { 93: 146, 63: $VU }),
            o($VV, [2, 99]),
            o($VV, [2, 100]),
            { 31: 65, 43: 148, 96: 147, 123: $Vq, 125: $Vr, 148: $Vt },
            { 14: [2, 45], 51: [1, 149] },
            o($Vp, [2, 46]),
            o($Vp, [2, 48]),
            o($Vp, [2, 49]),
            o($Vp, [2, 50]),
            o($Vp, [2, 51]),
            { 14: [2, 53], 60: 150, 63: [1, 151], 64: [1, 152] },
            o($VW, [2, 54]),
            o($VW, [2, 56]),
            { 61: 153, 65: $VJ },
            { 31: 65, 43: 130, 66: 154, 123: $Vq, 125: $Vr, 148: $Vt },
            o($VA, [2, 144]),
            o($VA, [2, 145]),
            o($VA, [2, 146]),
            o($VA, [2, 147]),
            o($VA, [2, 148]),
            { 82: [1, 162], 129: 155, 130: [1, 156], 131: [1, 157], 132: [1, 158], 133: [1, 159], 134: [1, 160], 135: [1, 161] },
            o($VA, [2, 164], { 65: $VX }),
            o($VA, [2, 165]),
            { 31: 65, 43: 164, 123: $Vq, 125: $Vr, 148: $Vt },
            {
                67: $Vb,
                69: [1, 165],
                85: $Vc,
                86: $Vd,
                88: [1, 170],
                107: [1, 171],
                108: 167,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
                137: 166,
                138: 168,
                139: [1, 169],
                140: [1, 172],
            },
            { 65: [1, 173] },
            o($Vp, [2, 123]),
            { 15: [1, 174] },
            o($VY, [2, 109]),
            { 15: [2, 110], 17: [1, 175] },
            o($VZ, [2, 113], { 102: [1, 176] }),
            o($V_, [2, 115], { 106: [1, 177] }),
            o($V_, [2, 116]),
            { 67: [1, 178] },
            { 31: 65, 43: 130, 66: 126, 103: 179, 123: $Vq, 125: $Vr, 148: $Vt },
            { 31: 65, 43: 130, 66: 126, 103: 180, 123: $Vq, 125: $Vr, 148: $Vt },
            { 31: 65, 43: 130, 66: 126, 103: 181, 123: $Vq, 125: $Vr, 148: $Vt },
            { 67: [2, 61] },
            { 15: [1, 182] },
            o($Vo, [2, 14]),
            {
                9: 134,
                10: 135,
                11: 183,
                12: 133,
                15: [2, 9],
                16: 10,
                18: 11,
                19: 12,
                20: 13,
                21: 14,
                22: 16,
                23: 17,
                24: 18,
                25: $V2,
                27: $V3,
                30: $V4,
                32: 19,
                33: 28,
                34: 29,
                35: 30,
                36: $V5,
                37: $V6,
                45: 22,
                46: $V7,
                48: $V8,
                56: $V9,
                57: $Va,
                67: $Vb,
                85: $Vc,
                86: $Vd,
                98: 9,
                108: 15,
                109: 23,
                111: 24,
                114: 32,
                115: 33,
                116: 34,
                117: 35,
                118: 36,
                119: 37,
                120: 38,
                121: $Ve,
                122: $Vf,
                123: $Vg,
                124: $Vh,
                125: $Vi,
                126: $Vj,
                127: $Vk,
                128: $Vl,
            },
            o($V$, [2, 11]),
            o($V$, [2, 12]),
            { 15: [1, 184], 31: 65, 39: 185, 40: 186, 41: 187, 43: 188, 44: 189, 123: $Vq, 125: $Vr, 144: $V01, 148: $Vt },
            o($Vu, [2, 126]),
            o($Vu, [2, 129]),
            { 17: [2, 25], 51: $VS },
            { 17: [2, 27] },
            { 14: [2, 163] },
            { 31: 65, 43: 91, 62: $VB, 65: $VC, 90: 191, 91: 90, 92: 92, 94: $VD, 95: 93, 123: $Vq, 125: $Vr, 148: $Vt },
            { 65: $VC, 91: 192, 95: 93 },
            { 65: [2, 101] },
            o($VT, [2, 96], { 93: 193, 63: $VU }),
            { 65: $VC, 91: 194, 95: 93 },
            { 67: [1, 196], 69: [1, 195] },
            o([67, 69], [2, 106]),
            { 50: 197, 52: $VE, 53: $VF, 54: $VG, 55: $VH },
            { 59: 198, 61: 105, 62: $VI, 65: $VJ },
            o($V11, [2, 58]),
            o($V11, [2, 59]),
            o($VW, [2, 57]),
            { 67: [1, 199] },
            {
                26: 203,
                31: 65,
                43: 205,
                44: 210,
                52: $Vy,
                75: 204,
                77: 206,
                97: 200,
                107: $V21,
                123: $Vq,
                125: $Vr,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                145: 201,
                146: 202,
                147: $V41,
                148: $Vt,
            },
            o($VA, [2, 154]),
            o($V51, [2, 155]),
            o($V51, [2, 156]),
            o($V51, [2, 157]),
            o($V51, [2, 158]),
            o($V51, [2, 159]),
            o($V51, [2, 160]),
            o($V61, [2, 79]),
            o($VA, [2, 161]),
            o($VA, [2, 166]),
            { 69: [1, 213] },
            { 69: [2, 168] },
            { 69: [2, 169] },
            { 69: [2, 170], 87: [1, 214] },
            { 107: [1, 216], 139: [1, 215] },
            { 69: [2, 173] },
            { 69: [2, 174] },
            o($V61, [2, 80]),
            o($VY, [2, 108]),
            { 15: [2, 111], 31: 65, 43: 130, 66: 126, 85: $VL, 86: $VM, 99: 217, 100: 122, 101: 123, 103: 124, 104: 125, 105: $VN, 123: $Vq, 125: $Vr, 148: $Vt },
            o($VZ, [2, 114]),
            { 107: [1, 218] },
            {
                26: 224,
                31: 65,
                43: 226,
                44: 210,
                52: $Vy,
                62: $VK,
                68: 219,
                70: 220,
                71: 221,
                72: 222,
                73: 223,
                75: 225,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                148: $Vt,
            },
            o($V_, [2, 117]),
            o($V_, [2, 118]),
            o($V_, [2, 119]),
            o($Vo, [2, 13]),
            { 15: [2, 10] },
            o($Vo, [2, 33]),
            { 15: [1, 236], 31: 65, 40: 237, 41: 187, 43: 188, 44: 189, 123: $Vq, 125: $Vr, 144: $V01, 148: $Vt },
            o($Vb1, [2, 35]),
            { 14: $Vn, 42: 238 },
            { 14: [2, 40] },
            { 14: [2, 41] },
            o([14, 15, 17, 51, 52, 62, 69, 83, 85, 86, 87, 88, 89, 102, 106, 107, 121, 122, 123, 125, 127, 130, 140, 142, 144, 148], [2, 184]),
            o($VT, [2, 93]),
            o($VT, [2, 97]),
            { 65: $VC, 91: 239, 95: 93 },
            o($VT, [2, 103]),
            o($Vc1, [2, 104]),
            {
                26: 203,
                31: 65,
                43: 205,
                44: 210,
                52: $Vy,
                75: 204,
                77: 206,
                97: 240,
                107: $V21,
                123: $Vq,
                125: $Vr,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                145: 201,
                146: 202,
                147: $V41,
                148: $Vt,
            },
            o($Vp, [2, 47]),
            o($VW, [2, 55]),
            {
                26: 224,
                31: 65,
                43: 226,
                44: 210,
                52: $Vy,
                62: $VK,
                68: 241,
                70: 220,
                71: 221,
                72: 222,
                73: 223,
                75: 225,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                148: $Vt,
            },
            { 130: [1, 242] },
            o($Vd1, [2, 195]),
            o($Vd1, [2, 196]),
            o($Vd1, [2, 187]),
            o($Vd1, [2, 188]),
            o($Vd1, [2, 189]),
            o($Vd1, [2, 193]),
            o($Vd1, [2, 194]),
            o($Ve1, [2, 190]),
            o($Ve1, [2, 191]),
            o($Ve1, [2, 192]),
            o($Ve1, [2, 176]),
            o($Ve1, [2, 178]),
            o($VA, [2, 167]),
            { 107: [1, 243] },
            { 87: [1, 244] },
            { 139: [1, 245] },
            { 15: [2, 112] },
            o($V_, [2, 120]),
            o([15, 17, 102, 106], [2, 121], { 51: $Vf1 }),
            o($Vg1, [2, 62]),
            o($Vg1, [2, 64], {
                31: 65,
                141: 208,
                143: 209,
                44: 210,
                73: 223,
                26: 224,
                75: 225,
                43: 226,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                71: 247,
                52: $Vy,
                62: $VK,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                142: $Vs,
                144: $V01,
                148: $Vt,
            }),
            o($Vg1, [2, 65], {
                31: 65,
                141: 208,
                143: 209,
                44: 210,
                73: 223,
                26: 224,
                75: 225,
                43: 226,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                71: 248,
                52: $Vy,
                62: $VK,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                142: $Vs,
                144: $V01,
                148: $Vt,
            }),
            o($Vh1, [2, 68], { 74: 249, 85: $Vi1, 86: $Vj1, 87: $Vk1, 88: $Vl1, 89: $Vm1 }),
            o($Vn1, [2, 70]),
            o($Vn1, [2, 71]),
            o($Vn1, [2, 72], { 65: $VX }),
            o($Vn1, [2, 73]),
            o($Vn1, [2, 74]),
            o($Vn1, [2, 75]),
            o($Vn1, [2, 76]),
            {
                26: 224,
                31: 258,
                43: 226,
                44: 210,
                52: $Vy,
                62: $VK,
                68: 257,
                69: [1, 255],
                70: 220,
                71: 221,
                72: 222,
                73: 223,
                75: 225,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                81: 256,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                148: $Vt,
            },
            o($Vn1, [2, 181]),
            o($Vn1, [2, 182]),
            o($Vn1, [2, 183]),
            { 44: 210, 69: [1, 259], 77: 261, 84: 260, 107: $V21, 140: $V31, 141: 208, 143: 209, 144: $V01 },
            o($Vo, [2, 34]),
            o($Vb1, [2, 36]),
            o($Vb1, [2, 37]),
            o($VT, [2, 98]),
            { 69: [1, 262] },
            { 51: $Vf1, 69: [1, 263] },
            o($VA, [2, 153]),
            { 69: [2, 171] },
            { 107: [1, 264] },
            { 69: [2, 175] },
            {
                26: 224,
                31: 65,
                43: 226,
                44: 210,
                52: $Vy,
                62: $VK,
                70: 265,
                71: 221,
                72: 222,
                73: 223,
                75: 225,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                148: $Vt,
            },
            o($Vh1, [2, 66]),
            o($Vh1, [2, 67]),
            {
                26: 224,
                31: 65,
                43: 226,
                44: 210,
                52: $Vy,
                62: $VK,
                71: 266,
                73: 223,
                75: 225,
                76: 227,
                77: 228,
                78: 229,
                79: 230,
                80: 231,
                83: $V71,
                107: $V21,
                121: $V81,
                122: $V91,
                123: $Vq,
                125: $Vr,
                127: $Va1,
                140: $V31,
                141: 208,
                142: $Vs,
                143: 209,
                144: $V01,
                148: $Vt,
            },
            o($Vo1, [2, 87]),
            o($Vo1, [2, 88]),
            o($Vo1, [2, 89]),
            o($Vo1, [2, 90]),
            o($Vo1, [2, 91]),
            o($Vn1, [2, 77]),
            { 69: [1, 267] },
            { 51: $Vf1, 69: [2, 81] },
            o([51, 52, 62, 65, 69, 83, 85, 86, 87, 88, 89, 107, 121, 122, 123, 125, 127, 140, 142, 144, 148], $VP, { 82: [1, 268] }),
            o($Vn1, [2, 83]),
            { 69: [1, 269], 74: 270, 85: $Vi1, 86: $Vj1, 87: $Vk1, 88: $Vl1, 89: $Vm1 },
            o($Vp1, [2, 85]),
            o($Vc1, [2, 105]),
            o($VW, [2, 60]),
            { 69: [2, 172] },
            o($Vg1, [2, 63]),
            o($Vh1, [2, 69]),
            o($Vn1, [2, 78]),
            { 44: 210, 77: 271, 107: $V21, 140: $V31, 141: 208, 143: 209, 144: $V01 },
            o($Vn1, [2, 84]),
            { 44: 210, 77: 272, 107: $V21, 140: $V31, 141: 208, 143: 209, 144: $V01 },
            { 69: [2, 82] },
            o($Vp1, [2, 86]),
        ],
        defaultActions: {
            3: [2, 2],
            16: [2, 20],
            17: [2, 21],
            18: [2, 22],
            28: [2, 29],
            29: [2, 30],
            30: [2, 31],
            53: [2, 1],
            63: [2, 38],
            64: [2, 39],
            78: [2, 23],
            83: [2, 26],
            85: [2, 44],
            130: [2, 61],
            140: [2, 27],
            141: [2, 163],
            144: [2, 101],
            167: [2, 168],
            168: [2, 169],
            171: [2, 173],
            172: [2, 174],
            183: [2, 10],
            188: [2, 40],
            189: [2, 41],
            217: [2, 112],
            243: [2, 171],
            245: [2, 175],
            264: [2, 172],
            271: [2, 82],
        },
        parseError: function parseError(str, hash) {
            if (hash.recoverable) {
                this.trace(str);
            } else {
                var error = new Error(str);
                error.hash = hash;
                throw error;
            }
        },
        parse: function parse(input) {
            var self = this,
                stack = [0],
                tstack = [],
                vstack = [null],
                lstack = [],
                table = this.table,
                yytext = '',
                yylineno = 0,
                yyleng = 0,
                recovering = 0,
                TERROR = 2,
                EOF = 1;
            var args = lstack.slice.call(arguments, 1);
            var lexer = Object.create(this.lexer);
            var sharedState = { yy: {} };
            for (var k in this.yy) {
                if (Object.prototype.hasOwnProperty.call(this.yy, k)) {
                    sharedState.yy[k] = this.yy[k];
                }
            }
            lexer.setInput(input, sharedState.yy);
            sharedState.yy.lexer = lexer;
            sharedState.yy.parser = this;
            if (typeof lexer.yylloc == 'undefined') {
                lexer.yylloc = {};
            }
            var yyloc = lexer.yylloc;
            lstack.push(yyloc);
            var ranges = lexer.options && lexer.options.ranges;
            if (typeof sharedState.yy.parseError === 'function') {
                this.parseError = sharedState.yy.parseError;
            } else {
                this.parseError = Object.getPrototypeOf(this).parseError;
            }
            function popStack(n) {
                stack.length = stack.length - 2 * n;
                vstack.length = vstack.length - n;
                lstack.length = lstack.length - n;
            }
            _token_stack: var lex = function () {
                var token;
                token = lexer.lex() || EOF;
                if (typeof token !== 'number') {
                    token = self.symbols_[token] || token;
                }
                return token;
            };
            var symbol,
                preErrorSymbol,
                state,
                action,
                a,
                r,
                yyval = {},
                p,
                len,
                newState,
                expected;
            while (true) {
                state = stack[stack.length - 1];
                if (this.defaultActions[state]) {
                    action = this.defaultActions[state];
                } else {
                    if (symbol === null || typeof symbol == 'undefined') {
                        symbol = lex();
                    }
                    action = table[state] && table[state][symbol];
                }
                if (typeof action === 'undefined' || !action.length || !action[0]) {
                    var errStr = '';
                    expected = [];
                    for (p in table[state]) {
                        if (this.terminals_[p] && p > TERROR) {
                            expected.push("'" + this.terminals_[p] + "'");
                        }
                    }
                    if (lexer.showPosition) {
                        errStr = 'Parse error on line ' + (yylineno + 1) + ':\n' + lexer.showPosition() + '\nExpecting ' + expected.join(', ') + ", got '" + (this.terminals_[symbol] || symbol) + "'";
                    } else {
                        errStr = 'Parse error on line ' + (yylineno + 1) + ': Unexpected ' + (symbol == EOF ? 'end of input' : "'" + (this.terminals_[symbol] || symbol) + "'");
                    }
                    this.parseError(errStr, {
                        text: lexer.match,
                        token: this.terminals_[symbol] || symbol,
                        line: lexer.yylineno,
                        loc: yyloc,
                        expected: expected,
                    });
                }
                if (action[0] instanceof Array && action.length > 1) {
                    throw new Error('Parse Error: multiple actions possible at state: ' + state + ', token: ' + symbol);
                }
                switch (action[0]) {
                    case 1:
                        stack.push(symbol);
                        vstack.push(lexer.yytext);
                        lstack.push(lexer.yylloc);
                        stack.push(action[1]);
                        symbol = null;
                        if (!preErrorSymbol) {
                            yyleng = lexer.yyleng;
                            yytext = lexer.yytext;
                            yylineno = lexer.yylineno;
                            yyloc = lexer.yylloc;
                            if (recovering > 0) {
                                recovering--;
                            }
                        } else {
                            symbol = preErrorSymbol;
                            preErrorSymbol = null;
                        }
                        break;
                    case 2:
                        len = this.productions_[action[1]][1];
                        yyval.$ = vstack[vstack.length - len];
                        yyval._$ = {
                            first_line: lstack[lstack.length - (len || 1)].first_line,
                            last_line: lstack[lstack.length - 1].last_line,
                            first_column: lstack[lstack.length - (len || 1)].first_column,
                            last_column: lstack[lstack.length - 1].last_column,
                        };
                        if (ranges) {
                            yyval._$.range = [lstack[lstack.length - (len || 1)].range[0], lstack[lstack.length - 1].range[1]];
                        }
                        r = this.performAction.apply(yyval, [yytext, yyleng, yylineno, sharedState.yy, action[1], vstack, lstack].concat(args));
                        if (typeof r !== 'undefined') {
                            return r;
                        }
                        if (len) {
                            stack = stack.slice(0, -1 * len * 2);
                            vstack = vstack.slice(0, -1 * len);
                            lstack = lstack.slice(0, -1 * len);
                        }
                        stack.push(this.productions_[action[1]][0]);
                        vstack.push(yyval.$);
                        lstack.push(yyval._$);
                        newState = table[stack[stack.length - 2]][stack[stack.length - 1]];
                        stack.push(newState);
                        break;
                    case 3:
                        return true;
                }
            }
            return true;
        },
    };

    ('use strict');

    var _get = function get(object, property, receiver) {
        if (object === null) object = Function.prototype;
        var desc = Object.getOwnPropertyDescriptor(object, property);
        if (desc === undefined) {
            var parent = Object.getPrototypeOf(object);
            if (parent === null) {
                return undefined;
            } else {
                return get(parent, property, receiver);
            }
        } else if ('value' in desc) {
            return desc.value;
        } else {
            var getter = desc.get;
            if (getter === undefined) {
                return undefined;
            }
            return getter.call(receiver);
        }
    };

    var _createClass = (function () {
        function defineProperties(target, props) {
            for (var i = 0; i < props.length; i++) {
                var descriptor = props[i];
                descriptor.enumerable = descriptor.enumerable || false;
                descriptor.configurable = true;
                if ('value' in descriptor) descriptor.writable = true;
                Object.defineProperty(target, descriptor.key, descriptor);
            }
        }
        return function (Constructor, protoProps, staticProps) {
            if (protoProps) defineProperties(Constructor.prototype, protoProps);
            if (staticProps) defineProperties(Constructor, staticProps);
            return Constructor;
        };
    })();

    function _possibleConstructorReturn(self, call) {
        if (!self) {
            throw new ReferenceError("this hasn't been initialised - super() hasn't been called");
        }
        return call && (typeof call === 'object' || typeof call === 'function') ? call : self;
    }

    function _inherits(subClass, superClass) {
        if (typeof superClass !== 'function' && superClass !== null) {
            throw new TypeError('Super expression must either be null or a function, not ' + typeof superClass);
        }
        subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } });
        if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : (subClass.__proto__ = superClass);
    }

    function _classCallCheck(instance, Constructor) {
        if (!(instance instanceof Constructor)) {
            throw new TypeError('Cannot call a class as a function');
        }
    }

    var concat = function concat(l, r) {
        l = l ? l : [];
        r = r ? r : [];

        l = l instanceof Array ? l : [l];
        r = r instanceof Array ? r : [r];

        return l.concat(r);
    };

    var stripFalsy = function stripFalsy(o) {
        for (var k in o) {
            if (!o[k]) {
                delete o[k];
            }
        }

        return o;
    };

    var join = function join(o, delimiter) {
        if (isArray(o)) {
            return o.join(delimiter);
        }

        return o;
    };

    var joinValues = function joinValues(target, source) {
        for (var prop in source) {
            if (prop in target) {
                target[prop] = concat(target[prop], source[prop]);
            } else {
                target[prop] = source[prop];
            }
        }

        return target;
    };

    var mixin = function mixin(target, source) {
        var result = {};

        for (var prop in target) {
            result[prop] = target[prop];
        }

        for (var prop in source) {
            result[prop] = source[prop];
        }

        return result;
    };

    var isArray = function isArray(o) {
        return Object.prototype.toString.call(o) === '[object Array]';
    };

    var toAtomic = function toAtomic(o) {
        if (o instanceof CSSObject) {
            return o.toAtomicJSON();
        } else if (isArray(o)) {
            return o.map(function (item) {
                return toAtomic(item);
            });
        }

        return o;
    };

    var toDeep = function toDeep(o) {
        if (o instanceof CSSObject) {
            return o.toDeepJSON();
        } else if (isArray(o)) {
            return o.map(function (item) {
                return toDeep(item);
            });
        }

        return o;
    };

    var toSimple = function toSimple(o) {
        if (o instanceof CSSObject) {
            return o.toSimpleJSON();
        } else if (isArray(o)) {
            return o.map(function (item) {
                return toSimple(item);
            });
        }

        return o;
    };

    var toJSON = function toJSON(o, level) {
        level = level.toLowerCase();

        if (!o) {
            return o;
        }

        switch (level) {
            case 'atomic':
                return toAtomic(o);
            case 'deep':
                return toDeep(o);
            case 'simple':
                return toSimple(o);
        }

        return o;
    };

    var CSSObject = (function () {
        function CSSObject() {
            _classCallCheck(this, CSSObject);

            this._props_ = {};
        }

        _createClass(
            CSSObject,
            [
                {
                    key: 'setOptions',
                    value: function setOptions(customOptions) {
                        CSSObject._options = mixin(this.options, customOptions);
                    },
                },
                {
                    key: 'getType',
                    value: function getType(type) {
                        return 'OBJECT';
                    },
                },
                {
                    key: 'set',
                    value: function set(key, value) {
                        if (value || value !== undefined) {
                            this._props_[key] = value;
                        }
                        return this;
                    },
                },
                {
                    key: 'get',
                    value: function get(key, defaultValue) {
                        if (key in this._props_) {
                            return this._props_[key];
                        }
                        return defaultValue;
                    },
                },
                {
                    key: 'add',
                    value: function add(component, prop) {
                        prop = prop || 'value';

                        if (component) {
                            var source = this.get(prop, []);
                            source.push(component);
                            this.set(prop, source);
                        }

                        return this;
                    },
                },
                {
                    key: 'toAtomicJSON',
                    value: function toAtomicJSON() {
                        var _this = this;

                        var json = {
                            type: this.getType(),
                        };

                        var self = this;
                        Object.keys(this._props_).map(function (key) {
                            json[key] = toAtomic(_this.get(key, null));
                        });

                        return json;
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        var _this2 = this;

                        var json = {
                            type: this.getType(),
                        };

                        var self = this;
                        Object.keys(this._props_).map(function (key) {
                            json[key] = toDeep(_this2.get(key, null));
                        });

                        return json;
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('value'));
                    },
                },
                {
                    key: 'toJSON',
                    value: function toJSON(level) {
                        switch (level) {
                            case 'atomic':
                                return toAtomic(this);
                            case 'deep':
                                return toDeep(this);
                            case 'simple':
                                return toSimple(this);
                        }
                    },
                },
                {
                    key: 'options',
                    get: function get() {
                        return CSSObject._options;
                    },
                    set: function set(customOptions) {
                        console.warn('For beautify AST output, `setOptions()` method would be recommended instead of assigning directly.');
                        this.setOptions(customOptions);
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new CSSObject().set('value', value);
                    },
                },
            ]
        );

        return CSSObject;
    })();

    CSSObject._options = {
        commaDelimiter: ',',
        whitespaceDelimiter: ' ',
    };

    var StyleSheet = (function (_CSSObject) {
        _inherits(StyleSheet, _CSSObject);

        function StyleSheet() {
            _classCallCheck(this, StyleSheet);

            return _possibleConstructorReturn(this, (StyleSheet.__proto__ || Object.getPrototypeOf(StyleSheet)).call(this));
        }

        _createClass(
            StyleSheet,
            [
                {
                    key: 'getType',
                    value: function getType(type) {
                        return 'STYLESHEET';
                    },
                },
                {
                    key: 'toAtomicJSON',
                    value: function toAtomicJSON() {
                        var json = _get(StyleSheet.prototype.__proto__ || Object.getPrototypeOf(StyleSheet.prototype), 'toAtomicJSON', this).call(this);
                        json.level = 'atomic';

                        return json;
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        var json = _get(StyleSheet.prototype.__proto__ || Object.getPrototypeOf(StyleSheet.prototype), 'toDeepJSON', this).call(this);
                        json.level = 'deep';

                        return json;
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return {
                            type: 'stylesheet',
                            level: 'simple',
                            value: toSimple(this.get('value', [])),
                        };
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create() {
                        return new StyleSheet();
                    },
                },
            ]
        );

        return StyleSheet;
    })(CSSObject);

    var Operator = (function (_CSSObject2) {
        _inherits(Operator, _CSSObject2);

        function Operator() {
            _classCallCheck(this, Operator);

            return _possibleConstructorReturn(this, (Operator.__proto__ || Object.getPrototypeOf(Operator)).apply(this, arguments));
        }

        _createClass(
            Operator,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'OPERATOR';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = toSimple(this.get('value'));
                        var nextExpression = this.get('nextExpression');
                        if (nextExpression) {
                            json += ' ' + toSimple(nextExpression);
                        }

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new Operator().set('value', value);
                    },
                },
            ]
        );

        return Operator;
    })(CSSObject);

    var Expression = (function (_CSSObject3) {
        _inherits(Expression, _CSSObject3);

        function Expression() {
            _classCallCheck(this, Expression);

            return _possibleConstructorReturn(this, (Expression.__proto__ || Object.getPrototypeOf(Expression)).apply(this, arguments));
        }

        _createClass(
            Expression,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'EXPRESSION';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var lhs = toSimple(this.get('lhs'));
                        var operator = toSimple(this.get('operator'));
                        var rhs = toSimple(this.get('rhs'));

                        // for beautifying, added some spaces bewteen nodes
                        return lhs + ' ' + operator + ' ' + rhs;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(operator, lhs, rhs) {
                        return new Expression().set('operator', operator).set('lhs', lhs).set('rhs', rhs);
                    },
                },
            ]
        );

        return Expression;
    })(CSSObject);

    var PrimitiveVal = (function (_CSSObject4) {
        _inherits(PrimitiveVal, _CSSObject4);

        function PrimitiveVal() {
            _classCallCheck(this, PrimitiveVal);

            return _possibleConstructorReturn(this, (PrimitiveVal.__proto__ || Object.getPrototypeOf(PrimitiveVal)).apply(this, arguments));
        }

        _createClass(PrimitiveVal, [
            {
                key: 'getType',
                value: function getType() {
                    return 'PRIMITIVE_VALUE';
                },
            },
            {
                key: 'toDeepJSON',
                value: function toDeepJSON() {
                    return this.toSimpleJSON();
                },
            },
            {
                key: 'toSimpleJSON',
                value: function toSimpleJSON() {
                    return this.get('value');
                },
            },
        ]);

        return PrimitiveVal;
    })(CSSObject);

    var NumberVal = (function (_PrimitiveVal) {
        _inherits(NumberVal, _PrimitiveVal);

        function NumberVal() {
            _classCallCheck(this, NumberVal);

            return _possibleConstructorReturn(this, (NumberVal.__proto__ || Object.getPrototypeOf(NumberVal)).apply(this, arguments));
        }

        _createClass(
            NumberVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'NUMBER';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new NumberVal().set('value', parseFloat(value));
                    },
                },
            ]
        );

        return NumberVal;
    })(PrimitiveVal);

    var HashVal = (function (_PrimitiveVal2) {
        _inherits(HashVal, _PrimitiveVal2);

        function HashVal() {
            _classCallCheck(this, HashVal);

            return _possibleConstructorReturn(this, (HashVal.__proto__ || Object.getPrototypeOf(HashVal)).apply(this, arguments));
        }

        _createClass(
            HashVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'HASH';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new HashVal().set('value', value);
                    },
                },
            ]
        );

        return HashVal;
    })(PrimitiveVal);

    var UnitVal = (function (_PrimitiveVal3) {
        _inherits(UnitVal, _PrimitiveVal3);

        function UnitVal() {
            _classCallCheck(this, UnitVal);

            return _possibleConstructorReturn(this, (UnitVal.__proto__ || Object.getPrototypeOf(UnitVal)).apply(this, arguments));
        }

        _createClass(UnitVal, [
            {
                key: 'toSimpleJSON',
                value: function toSimpleJSON() {
                    return this.get('value') + this.get('unit', '');
                },
            },
        ]);

        return UnitVal;
    })(PrimitiveVal);

    var StringVal = (function (_UnitVal) {
        _inherits(StringVal, _UnitVal);

        function StringVal() {
            _classCallCheck(this, StringVal);

            return _possibleConstructorReturn(this, (StringVal.__proto__ || Object.getPrototypeOf(StringVal)).apply(this, arguments));
        }

        _createClass(
            StringVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'STRING';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new StringVal().set('value', value);
                    },
                },
            ]
        );

        return StringVal;
    })(UnitVal);

    var PercentageVal = (function (_UnitVal2) {
        _inherits(PercentageVal, _UnitVal2);

        function PercentageVal() {
            _classCallCheck(this, PercentageVal);

            return _possibleConstructorReturn(this, (PercentageVal.__proto__ || Object.getPrototypeOf(PercentageVal)).apply(this, arguments));
        }

        _createClass(
            PercentageVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'PERCENTAGE';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        var result = value.match(/(([\+\-]?[0-9]+(\.[0-9]+)?)|([\+\-]?\.[0-9]+))([%])/);

                        return new PercentageVal().set('value', parseFloat(result[1])).set('unit', result[5]);
                    },
                },
            ]
        );

        return PercentageVal;
    })(UnitVal);

    var DimensionVal = (function (_UnitVal3) {
        _inherits(DimensionVal, _UnitVal3);

        function DimensionVal() {
            _classCallCheck(this, DimensionVal);

            return _possibleConstructorReturn(this, (DimensionVal.__proto__ || Object.getPrototypeOf(DimensionVal)).apply(this, arguments));
        }

        _createClass(
            DimensionVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'DIMENSION';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        var result = value.match(/(([\+\-]?[0-9]+(\.[0-9]+)?)|([\+\-]?\.[0-9]+))([a-zA-Z]+)/);

                        return new DimensionVal().set('value', parseFloat(result[1])).set('unit', result[5]);
                    },
                },
            ]
        );

        return DimensionVal;
    })(UnitVal);

    var IdentVal = (function (_PrimitiveVal4) {
        _inherits(IdentVal, _PrimitiveVal4);

        function IdentVal() {
            _classCallCheck(this, IdentVal);

            return _possibleConstructorReturn(this, (IdentVal.__proto__ || Object.getPrototypeOf(IdentVal)).apply(this, arguments));
        }

        _createClass(
            IdentVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'ID';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return this.toSimpleJSON();
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('vendorPrefix', '')) + toSimple(this.get('value'));
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        var result = value.match(/([-](webkit|moz|o|ms)[-])?([0-9a-zA-Z-]*)/);

                        return new IdentVal().set('vendorPrefix', result[1]).set('value', result[3]);
                    },
                },
            ]
        );

        return IdentVal;
    })(PrimitiveVal);

    var UrlVal = (function (_PrimitiveVal5) {
        _inherits(UrlVal, _PrimitiveVal5);

        function UrlVal() {
            _classCallCheck(this, UrlVal);

            return _possibleConstructorReturn(this, (UrlVal.__proto__ || Object.getPrototypeOf(UrlVal)).apply(this, arguments));
        }

        _createClass(
            UrlVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'URL';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('name')) + '(' + toSimple(this.get('value')) + ')';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        var urlVal = new UrlVal();
                        var result = value.match(/([0-9a-zA-Z\-]+)\((.+)\)/);

                        if (result) {
                            urlVal.set('name', IdentVal.create(result[1].trim()));
                            urlVal.set('value', result[2].trim());
                        }
                        return urlVal;
                    },
                },
            ]
        );

        return UrlVal;
    })(PrimitiveVal);

    var FunctionVal = (function (_PrimitiveVal6) {
        _inherits(FunctionVal, _PrimitiveVal6);

        function FunctionVal() {
            _classCallCheck(this, FunctionVal);

            return _possibleConstructorReturn(this, (FunctionVal.__proto__ || Object.getPrototypeOf(FunctionVal)).apply(this, arguments));
        }

        _createClass(
            FunctionVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'FUNCTION';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('name')) + '(' + join(toSimple(this.get('parameters')), this.options.commaDelimiter) + ')';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(name, parameters) {
                        return new FunctionVal().set('name', name).set('parameters', parameters);
                    },
                },
            ]
        );

        return FunctionVal;
    })(PrimitiveVal);

    var SequenceVal = (function (_PrimitiveVal7) {
        _inherits(SequenceVal, _PrimitiveVal7);

        function SequenceVal() {
            _classCallCheck(this, SequenceVal);

            return _possibleConstructorReturn(this, (SequenceVal.__proto__ || Object.getPrototypeOf(SequenceVal)).apply(this, arguments));
        }

        _createClass(
            SequenceVal,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'SEQUENCE';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return {
                            type: this.getType(),
                            value: toSimple(this.get('value', [])),
                        };
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('value', [])).join(this.options.whitespaceDelimiter);
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(item) {
                        return new SequenceVal().add(item);
                    },
                },
            ]
        );

        return SequenceVal;
    })(PrimitiveVal);

    var QualifiedRule = (function (_CSSObject5) {
        _inherits(QualifiedRule, _CSSObject5);

        function QualifiedRule() {
            _classCallCheck(this, QualifiedRule);

            return _possibleConstructorReturn(this, (QualifiedRule.__proto__ || Object.getPrototypeOf(QualifiedRule)).apply(this, arguments));
        }

        _createClass(
            QualifiedRule,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'QUALIFIED_RULE';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return {
                            type: 'rule',
                            selectors: toSimple(this.get('selectors')),
                            declarations: toSimple(this.get('value')),
                        };
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new QualifiedRule().set('value', value);
                    },
                },
            ]
        );

        return QualifiedRule;
    })(CSSObject);

    var Declaration = (function (_CSSObject6) {
        _inherits(Declaration, _CSSObject6);

        function Declaration() {
            _classCallCheck(this, Declaration);

            return _possibleConstructorReturn(this, (Declaration.__proto__ || Object.getPrototypeOf(Declaration)).apply(this, arguments));
        }

        _createClass(
            Declaration,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'DECLARATION';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = {};
                        var value = toSimple(this.get('value'));
                        if (this.get('backslashHack', false)) {
                            value += '\\9';
                        }
                        if (this.get('important', false)) {
                            value += ' !important';
                        }

                        var property = toSimple(this.get('property'));
                        if (this.get('asteriskHack', false)) {
                            property = '*' + property;
                        }
                        if (this.get('underscoreHack', false)) {
                            property = '_' + property;
                        }
                        json[property] = value;

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(property, value) {
                        return new Declaration().set('property', property).set('value', value);
                    },
                },
            ]
        );

        return Declaration;
    })(CSSObject);

    var DeclarationList = (function (_CSSObject7) {
        _inherits(DeclarationList, _CSSObject7);

        function DeclarationList() {
            _classCallCheck(this, DeclarationList);

            return _possibleConstructorReturn(this, (DeclarationList.__proto__ || Object.getPrototypeOf(DeclarationList)).apply(this, arguments));
        }

        _createClass(
            DeclarationList,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'DECLARATION_LIST';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = {};

                        toSimple(this.get('value')).map(function (o) {
                            joinValues(json, o);
                        });

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return (
                            new DeclarationList()
                                // force value to array
                                .set('value', concat(value, []))
                        );
                    },
                },
            ]
        );

        return DeclarationList;
    })(CSSObject);

    var MediaQueryList = (function (_CSSObject8) {
        _inherits(MediaQueryList, _CSSObject8);

        function MediaQueryList() {
            _classCallCheck(this, MediaQueryList);

            return _possibleConstructorReturn(this, (MediaQueryList.__proto__ || Object.getPrototypeOf(MediaQueryList)).apply(this, arguments));
        }

        _createClass(
            MediaQueryList,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'MEDIA_QUERY_LIST';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return this.toSimpleJSON();
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return toSimple(this.get('value'));
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create() {
                        return new MediaQueryList();
                    },
                },
            ]
        );

        return MediaQueryList;
    })(CSSObject);

    var MediaQuery = (function (_CSSObject9) {
        _inherits(MediaQuery, _CSSObject9);

        function MediaQuery() {
            _classCallCheck(this, MediaQuery);

            return _possibleConstructorReturn(this, (MediaQuery.__proto__ || Object.getPrototypeOf(MediaQuery)).apply(this, arguments));
        }

        _createClass(
            MediaQuery,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'MEDIA_QUERY';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return this.toSimpleJSON();
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = toSimple(this.get('mediaType'));

                        var prefix = this.get('prefix');
                        if (prefix) {
                            json = toSimple(prefix) + ' ' + json;
                        }
                        var nextExpression = this.get('nextExpression');
                        if (nextExpression) {
                            json += ' ' + toSimple(nextExpression);
                        }

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create() {
                        return new MediaQuery();
                    },
                },
            ]
        );

        return MediaQuery;
    })(CSSObject);

    var MediaQueryExpression = (function (_CSSObject10) {
        _inherits(MediaQueryExpression, _CSSObject10);

        function MediaQueryExpression() {
            _classCallCheck(this, MediaQueryExpression);

            return _possibleConstructorReturn(this, (MediaQueryExpression.__proto__ || Object.getPrototypeOf(MediaQueryExpression)).apply(this, arguments));
        }

        _createClass(
            MediaQueryExpression,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'MEDIA_QUERY_EXPRESSION';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return this.toSimpleJSON();
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var expression = '(' + toSimple(this.get('mediaFeature'));

                        var value = toSimple(this.get('value'));
                        if (value) {
                            expression += ': ' + value;
                        }

                        expression += ')';

                        var nextExpression = this.get('nextExpression');
                        if (nextExpression) {
                            expression += ' ' + toSimple(nextExpression);
                        }

                        return expression;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(feature, value) {
                        return new MediaQueryExpression().set('mediaFeature', feature).set('value', value);
                    },
                },
            ]
        );

        return MediaQueryExpression;
    })(CSSObject);

    var SelectorList = (function (_CSSObject11) {
        _inherits(SelectorList, _CSSObject11);

        function SelectorList() {
            _classCallCheck(this, SelectorList);

            return _possibleConstructorReturn(this, (SelectorList.__proto__ || Object.getPrototypeOf(SelectorList)).apply(this, arguments));
        }

        _createClass(
            SelectorList,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'SELECTOR_LIST';
                    },
                },
                {
                    key: 'toDeepJSON',
                    value: function toDeepJSON() {
                        return this.toSimpleJSON();
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return this.get('value').map(function (o) {
                            return toSimple(o);
                        });
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new SelectorList();
                    },
                },
            ]
        );

        return SelectorList;
    })(CSSObject);

    var Selector = (function (_CSSObject12) {
        _inherits(Selector, _CSSObject12);

        function Selector() {
            _classCallCheck(this, Selector);

            return _possibleConstructorReturn(this, (Selector.__proto__ || Object.getPrototypeOf(Selector)).apply(this, arguments));
        }

        _createClass(Selector, [
            {
                key: 'getType',
                value: function getType() {
                    return 'SELECTOR';
                },
            },
            {
                key: 'toDeepJSON',
                value: function toDeepJSON() {
                    return this.toSimpleJSON();
                },
            },
            {
                key: 'toSimpleJSON',
                value: function toSimpleJSON() {
                    var selector = toSimple(this.get('value'));
                    var nextSelector = toSimple(this.get('nextSelector'));
                    if (nextSelector) {
                        selector += nextSelector;
                    }

                    return selector;
                },
            },
        ]);

        return Selector;
    })(CSSObject);

    var SelectorCombinator = (function (_Selector) {
        _inherits(SelectorCombinator, _Selector);

        function SelectorCombinator() {
            _classCallCheck(this, SelectorCombinator);

            return _possibleConstructorReturn(this, (SelectorCombinator.__proto__ || Object.getPrototypeOf(SelectorCombinator)).apply(this, arguments));
        }

        _createClass(
            SelectorCombinator,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'SELECTOR_COMBINATOR';
                    },
                },
                {
                    key: 'getRelation',
                    value: function getRelation() {
                        return 'UNKNOWN';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        // for beautify
                        var selector = ' ' + toSimple(this.get('value')) + ' ';
                        var nextSelector = toSimple(this.get('nextSelector'));
                        if (nextSelector) {
                            selector += nextSelector;
                        }
                        return selector;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new SelectorCombinator().set('value', value);
                    },
                },
            ]
        );

        return SelectorCombinator;
    })(Selector);

    var DescendantSelectorCombinator = (function (_SelectorCombinator) {
        _inherits(DescendantSelectorCombinator, _SelectorCombinator);

        function DescendantSelectorCombinator() {
            _classCallCheck(this, DescendantSelectorCombinator);

            return _possibleConstructorReturn(this, (DescendantSelectorCombinator.__proto__ || Object.getPrototypeOf(DescendantSelectorCombinator)).apply(this, arguments));
        }

        _createClass(
            DescendantSelectorCombinator,
            [
                {
                    key: 'getRelation',
                    value: function getRelation() {
                        return 'DESCEDANT';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        // for beautify
                        var selector = toSimple(this.get('value'));
                        var nextSelector = toSimple(this.get('nextSelector'));
                        if (nextSelector) {
                            selector += nextSelector;
                        }
                        return selector;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new DescendantSelectorCombinator().set('value', value);
                    },
                },
            ]
        );

        return DescendantSelectorCombinator;
    })(SelectorCombinator);

    var ChildSelectorCombinator = (function (_SelectorCombinator2) {
        _inherits(ChildSelectorCombinator, _SelectorCombinator2);

        function ChildSelectorCombinator() {
            _classCallCheck(this, ChildSelectorCombinator);

            return _possibleConstructorReturn(this, (ChildSelectorCombinator.__proto__ || Object.getPrototypeOf(ChildSelectorCombinator)).apply(this, arguments));
        }

        _createClass(
            ChildSelectorCombinator,
            [
                {
                    key: 'getRelation',
                    value: function getRelation() {
                        return 'CHILD';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new ChildSelectorCombinator().set('value', value);
                    },
                },
            ]
        );

        return ChildSelectorCombinator;
    })(SelectorCombinator);

    var AdjacentSiblingSelectorCombinator = (function (_SelectorCombinator3) {
        _inherits(AdjacentSiblingSelectorCombinator, _SelectorCombinator3);

        function AdjacentSiblingSelectorCombinator() {
            _classCallCheck(this, AdjacentSiblingSelectorCombinator);

            return _possibleConstructorReturn(this, (AdjacentSiblingSelectorCombinator.__proto__ || Object.getPrototypeOf(AdjacentSiblingSelectorCombinator)).apply(this, arguments));
        }

        _createClass(
            AdjacentSiblingSelectorCombinator,
            [
                {
                    key: 'getRelation',
                    value: function getRelation() {
                        return 'ADJACENT_SIBLING';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new AdjacentSiblingSelectorCombinator().set('value', value);
                    },
                },
            ]
        );

        return AdjacentSiblingSelectorCombinator;
    })(SelectorCombinator);

    var SiblingSelectorCombinator = (function (_SelectorCombinator4) {
        _inherits(SiblingSelectorCombinator, _SelectorCombinator4);

        function SiblingSelectorCombinator() {
            _classCallCheck(this, SiblingSelectorCombinator);

            return _possibleConstructorReturn(this, (SiblingSelectorCombinator.__proto__ || Object.getPrototypeOf(SiblingSelectorCombinator)).apply(this, arguments));
        }

        _createClass(
            SiblingSelectorCombinator,
            [
                {
                    key: 'getRelation',
                    value: function getRelation() {
                        return 'SIBLING';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new SiblingSelectorCombinator().set('value', value);
                    },
                },
            ]
        );

        return SiblingSelectorCombinator;
    })(SelectorCombinator);

    var ClassSelector = (function (_Selector2) {
        _inherits(ClassSelector, _Selector2);

        function ClassSelector() {
            _classCallCheck(this, ClassSelector);

            return _possibleConstructorReturn(this, (ClassSelector.__proto__ || Object.getPrototypeOf(ClassSelector)).apply(this, arguments));
        }

        _createClass(
            ClassSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'CLASS_SELECTOR';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new ClassSelector().set('value', value);
                    },
                },
            ]
        );

        return ClassSelector;
    })(Selector);

    var TypeSelector = (function (_Selector3) {
        _inherits(TypeSelector, _Selector3);

        function TypeSelector() {
            _classCallCheck(this, TypeSelector);

            return _possibleConstructorReturn(this, (TypeSelector.__proto__ || Object.getPrototypeOf(TypeSelector)).apply(this, arguments));
        }

        _createClass(
            TypeSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'TYPE_SELECTOR';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new TypeSelector().set('value', value);
                    },
                },
            ]
        );

        return TypeSelector;
    })(Selector);

    var IdSelector = (function (_Selector4) {
        _inherits(IdSelector, _Selector4);

        function IdSelector() {
            _classCallCheck(this, IdSelector);

            return _possibleConstructorReturn(this, (IdSelector.__proto__ || Object.getPrototypeOf(IdSelector)).apply(this, arguments));
        }

        _createClass(
            IdSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'ID_SELECTOR';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new IdSelector().set('value', value);
                    },
                },
            ]
        );

        return IdSelector;
    })(Selector);

    var UniversalSelector = (function (_Selector5) {
        _inherits(UniversalSelector, _Selector5);

        function UniversalSelector() {
            _classCallCheck(this, UniversalSelector);

            return _possibleConstructorReturn(this, (UniversalSelector.__proto__ || Object.getPrototypeOf(UniversalSelector)).apply(this, arguments));
        }

        _createClass(
            UniversalSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'UNIVERSAL_SELECTOR';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new UniversalSelector().set('value', value);
                    },
                },
            ]
        );

        return UniversalSelector;
    })(Selector);

    var PseudoClassSelector = (function (_Selector6) {
        _inherits(PseudoClassSelector, _Selector6);

        function PseudoClassSelector() {
            _classCallCheck(this, PseudoClassSelector);

            return _possibleConstructorReturn(this, (PseudoClassSelector.__proto__ || Object.getPrototypeOf(PseudoClassSelector)).apply(this, arguments));
        }

        _createClass(
            PseudoClassSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'PSEUDO_CLASS_SELECTOR';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var selector = ':' + toSimple(this.get('value'));
                        var nextSelector = toSimple(this.get('nextSelector'));
                        if (nextSelector) {
                            selector += nextSelector;
                        }
                        return selector;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new PseudoClassSelector().set('value', value);
                    },
                },
            ]
        );

        return PseudoClassSelector;
    })(Selector);

    var PseudoElementSelector = (function (_Selector7) {
        _inherits(PseudoElementSelector, _Selector7);

        function PseudoElementSelector() {
            _classCallCheck(this, PseudoElementSelector);

            return _possibleConstructorReturn(this, (PseudoElementSelector.__proto__ || Object.getPrototypeOf(PseudoElementSelector)).apply(this, arguments));
        }

        _createClass(
            PseudoElementSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'PSEUDO_ELEMENT_SELECTOR';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var selector = '::' + toSimple(this.get('value'));
                        var nextSelector = toSimple(this.get('nextSelector'));
                        if (nextSelector) {
                            selector += nextSelector;
                        }
                        return selector;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new PseudoElementSelector().set('value', value);
                    },
                },
            ]
        );

        return PseudoElementSelector;
    })(Selector);

    var AttributeSelector = (function (_Selector8) {
        _inherits(AttributeSelector, _Selector8);

        function AttributeSelector() {
            _classCallCheck(this, AttributeSelector);

            return _possibleConstructorReturn(this, (AttributeSelector.__proto__ || Object.getPrototypeOf(AttributeSelector)).apply(this, arguments));
        }

        _createClass(
            AttributeSelector,
            [
                {
                    key: 'getType',
                    value: function getType() {
                        return 'ATTRIBUTE_SELECTOR';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var selector = '[' + toSimple(this.get('value')) + ']';
                        var nextSelector = toSimple(this.get('nextSelector'));
                        if (nextSelector) {
                            selector += ' ' + nextSelector;
                        }
                        return selector;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(value) {
                        return new AttributeSelector().set('value', value);
                    },
                },
            ]
        );

        return AttributeSelector;
    })(Selector);

    var AtRule = (function (_CSSObject13) {
        _inherits(AtRule, _CSSObject13);

        function AtRule() {
            _classCallCheck(this, AtRule);

            return _possibleConstructorReturn(this, (AtRule.__proto__ || Object.getPrototypeOf(AtRule)).apply(this, arguments));
        }

        _createClass(AtRule, [
            {
                key: 'getType',
                value: function getType() {
                    return 'AT_RULE';
                },
            },
            {
                key: 'toSimpleJSON',
                value: function toSimpleJSON() {
                    return {
                        type: '@' + toSimple(this.get('rule')),
                        value: toSimple(this.get('value')),
                    };
                },
            },
            {
                key: 'setRule',
                value: function setRule(rule) {
                    var regexp = /@(.+)/;
                    var result = rule.match(regexp);

                    if (result) {
                        var identVal = IdentVal.create(result[1]);
                        identVal.set('prefix', '@');
                        this.set('rule', identVal);
                    }

                    return this;
                },
            },
        ]);

        return AtRule;
    })(CSSObject);

    var AtCharset = (function (_AtRule) {
        _inherits(AtCharset, _AtRule);

        function AtCharset() {
            _classCallCheck(this, AtCharset);

            return _possibleConstructorReturn(this, (AtCharset.__proto__ || Object.getPrototypeOf(AtCharset)).apply(this, arguments));
        }

        _createClass(AtCharset, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtCharset().setRule(rule);
                },
            },
        ]);

        return AtCharset;
    })(AtRule);

    var AtImport = (function (_AtRule2) {
        _inherits(AtImport, _AtRule2);

        function AtImport() {
            _classCallCheck(this, AtImport);

            return _possibleConstructorReturn(this, (AtImport.__proto__ || Object.getPrototypeOf(AtImport)).apply(this, arguments));
        }

        _createClass(
            AtImport,
            [
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return joinValues(_get(AtImport.prototype.__proto__ || Object.getPrototypeOf(AtImport.prototype), 'toSimpleJSON', this).call(this), {
                            mediaQuery: toSimple(this.get('nextExpression')),
                        });
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(rule) {
                        return new AtImport().setRule(rule);
                    },
                },
            ]
        );

        return AtImport;
    })(AtRule);

    var AtNamespace = (function (_AtRule3) {
        _inherits(AtNamespace, _AtRule3);

        function AtNamespace() {
            _classCallCheck(this, AtNamespace);

            return _possibleConstructorReturn(this, (AtNamespace.__proto__ || Object.getPrototypeOf(AtNamespace)).apply(this, arguments));
        }

        _createClass(
            AtNamespace,
            [
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return joinValues(_get(AtNamespace.prototype.__proto__ || Object.getPrototypeOf(AtNamespace.prototype), 'toSimpleJSON', this).call(this), {
                            prefix: toSimple(this.get('prefix')),
                        });
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(rule) {
                        return new AtNamespace().setRule(rule);
                    },
                },
            ]
        );

        return AtNamespace;
    })(AtRule);

    var AtFontface = (function (_AtRule4) {
        _inherits(AtFontface, _AtRule4);

        function AtFontface() {
            _classCallCheck(this, AtFontface);

            return _possibleConstructorReturn(this, (AtFontface.__proto__ || Object.getPrototypeOf(AtFontface)).apply(this, arguments));
        }

        _createClass(AtFontface, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtFontface().setRule(rule);
                },
            },
        ]);

        return AtFontface;
    })(AtRule);

    var AtNestedRule = (function (_AtRule5) {
        _inherits(AtNestedRule, _AtRule5);

        function AtNestedRule() {
            _classCallCheck(this, AtNestedRule);

            return _possibleConstructorReturn(this, (AtNestedRule.__proto__ || Object.getPrototypeOf(AtNestedRule)).apply(this, arguments));
        }

        _createClass(AtNestedRule, [
            {
                key: 'toSimpleJSON',
                value: function toSimpleJSON() {
                    return joinValues(_get(AtNestedRule.prototype.__proto__ || Object.getPrototypeOf(AtNestedRule.prototype), 'toSimpleJSON', this).call(this), {
                        nestedRules: toSimple(this.get('nestedRules')),
                    });
                },
            },
        ]);

        return AtNestedRule;
    })(AtRule);

    var AtMedia = (function (_AtNestedRule) {
        _inherits(AtMedia, _AtNestedRule);

        function AtMedia() {
            _classCallCheck(this, AtMedia);

            return _possibleConstructorReturn(this, (AtMedia.__proto__ || Object.getPrototypeOf(AtMedia)).apply(this, arguments));
        }

        _createClass(AtMedia, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtMedia().setRule(rule);
                },
            },
        ]);

        return AtMedia;
    })(AtNestedRule);

    var AtKeyframes = (function (_AtRule6) {
        _inherits(AtKeyframes, _AtRule6);

        function AtKeyframes() {
            _classCallCheck(this, AtKeyframes);

            return _possibleConstructorReturn(this, (AtKeyframes.__proto__ || Object.getPrototypeOf(AtKeyframes)).apply(this, arguments));
        }

        _createClass(
            AtKeyframes,
            [
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        return {
                            type: '@' + toSimple(this.get('rule')),
                            name: toSimple(this.get('name')),
                            keyframes: toSimple(this.get('value')),
                        };
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(rule) {
                        return new AtKeyframes().setRule(rule);
                    },
                },
            ]
        );

        return AtKeyframes;
    })(AtRule);

    var AtKeyframesBlockList = (function (_CSSObject14) {
        _inherits(AtKeyframesBlockList, _CSSObject14);

        function AtKeyframesBlockList() {
            _classCallCheck(this, AtKeyframesBlockList);

            return _possibleConstructorReturn(this, (AtKeyframesBlockList.__proto__ || Object.getPrototypeOf(AtKeyframesBlockList)).apply(this, arguments));
        }

        _createClass(
            AtKeyframesBlockList,
            [
                {
                    key: 'getType',
                    value: function getType(type) {
                        return 'KEYFRAME_BLOCK_LIST';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = {};
                        toSimple(this.get('value')).map(function (o) {
                            joinValues(json, o);
                        });

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create() {
                        return new AtKeyframesBlockList();
                    },
                },
            ]
        );

        return AtKeyframesBlockList;
    })(CSSObject);

    var AtKeyframesBlock = (function (_CSSObject15) {
        _inherits(AtKeyframesBlock, _CSSObject15);

        function AtKeyframesBlock() {
            _classCallCheck(this, AtKeyframesBlock);

            return _possibleConstructorReturn(this, (AtKeyframesBlock.__proto__ || Object.getPrototypeOf(AtKeyframesBlock)).apply(this, arguments));
        }

        _createClass(
            AtKeyframesBlock,
            [
                {
                    key: 'getType',
                    value: function getType(type) {
                        return 'KEYFRAME_BLOCK';
                    },
                },
                {
                    key: 'toSimpleJSON',
                    value: function toSimpleJSON() {
                        var json = {};
                        json[toSimple(this.get('selector'))] = toSimple(this.get('value'));

                        return json;
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(selector) {
                        return new AtKeyframesBlock().set('selector', selector);
                    },
                },
            ]
        );

        return AtKeyframesBlock;
    })(CSSObject);

    var AtSupport = (function (_AtNestedRule2) {
        _inherits(AtSupport, _AtNestedRule2);

        function AtSupport() {
            _classCallCheck(this, AtSupport);

            return _possibleConstructorReturn(this, (AtSupport.__proto__ || Object.getPrototypeOf(AtSupport)).apply(this, arguments));
        }

        _createClass(AtSupport, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtSupport().setRule(rule);
                },
            },
        ]);

        return AtSupport;
    })(AtNestedRule);

    var AtSupportExpression = (function (_CSSObject16) {
        _inherits(AtSupportExpression, _CSSObject16);

        function AtSupportExpression() {
            _classCallCheck(this, AtSupportExpression);

            return _possibleConstructorReturn(this, (AtSupportExpression.__proto__ || Object.getPrototypeOf(AtSupportExpression)).apply(this, arguments));
        }

        _createClass(
            AtSupportExpression,
            [
                {
                    key: 'getType',
                    value: function getType(type) {
                        return 'SUPPORT_EXPRESSION';
                    },
                },
            ],
            [
                {
                    key: 'create',
                    value: function create(selector) {
                        return new AtSupportExpression();
                    },
                },
            ]
        );

        return AtSupportExpression;
    })(CSSObject);

    var AtPage = (function (_AtNestedRule3) {
        _inherits(AtPage, _AtNestedRule3);

        function AtPage() {
            _classCallCheck(this, AtPage);

            return _possibleConstructorReturn(this, (AtPage.__proto__ || Object.getPrototypeOf(AtPage)).apply(this, arguments));
        }

        _createClass(AtPage, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtPage().setRule(rule);
                },
            },
        ]);

        return AtPage;
    })(AtNestedRule);

    var AtDocument = (function (_AtNestedRule4) {
        _inherits(AtDocument, _AtNestedRule4);

        function AtDocument() {
            _classCallCheck(this, AtDocument);

            return _possibleConstructorReturn(this, (AtDocument.__proto__ || Object.getPrototypeOf(AtDocument)).apply(this, arguments));
        }

        _createClass(AtDocument, null, [
            {
                key: 'create',
                value: function create(rule) {
                    return new AtDocument().setRule(rule);
                },
            },
        ]);

        return AtDocument;
    })(AtNestedRule);
    /* generated by jison-lex 0.3.4 */
    var lexer = (function () {
        var lexer = {
            EOF: 1,

            parseError: function parseError(str, hash) {
                if (this.yy.parser) {
                    this.yy.parser.parseError(str, hash);
                } else {
                    throw new Error(str);
                }
            },

            // resets the lexer, sets new input
            setInput: function (input, yy) {
                this.yy = yy || this.yy || {};
                this._input = input;
                this._more = this._backtrack = this.done = false;
                this.yylineno = this.yyleng = 0;
                this.yytext = this.matched = this.match = '';
                this.conditionStack = ['INITIAL'];
                this.yylloc = {
                    first_line: 1,
                    first_column: 0,
                    last_line: 1,
                    last_column: 0,
                };
                if (this.options.ranges) {
                    this.yylloc.range = [0, 0];
                }
                this.offset = 0;
                return this;
            },

            // consumes and returns one char from the input
            input: function () {
                var ch = this._input[0];
                this.yytext += ch;
                this.yyleng++;
                this.offset++;
                this.match += ch;
                this.matched += ch;
                var lines = ch.match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno++;
                    this.yylloc.last_line++;
                } else {
                    this.yylloc.last_column++;
                }
                if (this.options.ranges) {
                    this.yylloc.range[1]++;
                }

                this._input = this._input.slice(1);
                return ch;
            },

            // unshifts one char (or a string) into the input
            unput: function (ch) {
                var len = ch.length;
                var lines = ch.split(/(?:\r\n?|\n)/g);

                this._input = ch + this._input;
                this.yytext = this.yytext.substr(0, this.yytext.length - len);
                //this.yyleng -= len;
                this.offset -= len;
                var oldLines = this.match.split(/(?:\r\n?|\n)/g);
                this.match = this.match.substr(0, this.match.length - 1);
                this.matched = this.matched.substr(0, this.matched.length - 1);

                if (lines.length - 1) {
                    this.yylineno -= lines.length - 1;
                }
                var r = this.yylloc.range;

                this.yylloc = {
                    first_line: this.yylloc.first_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.first_column,
                    last_column: lines
                        ? (lines.length === oldLines.length ? this.yylloc.first_column : 0) + oldLines[oldLines.length - lines.length].length - lines[0].length
                        : this.yylloc.first_column - len,
                };

                if (this.options.ranges) {
                    this.yylloc.range = [r[0], r[0] + this.yyleng - len];
                }
                this.yyleng = this.yytext.length;
                return this;
            },

            // When called from action, caches matched text and appends it on next action
            more: function () {
                this._more = true;
                return this;
            },

            // When called from action, signals the lexer that this rule fails to match the input, so the next matching rule (regex) should be tested instead.
            reject: function () {
                if (this.options.backtrack_lexer) {
                    this._backtrack = true;
                } else {
                    return this.parseError(
                        'Lexical error on line ' +
                            (this.yylineno + 1) +
                            '. You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n' +
                            this.showPosition(),
                        {
                            text: '',
                            token: null,
                            line: this.yylineno,
                        }
                    );
                }
                return this;
            },

            // retain first n characters of the match
            less: function (n) {
                this.unput(this.match.slice(n));
            },

            // displays already matched input, i.e. for error messages
            pastInput: function () {
                var past = this.matched.substr(0, this.matched.length - this.match.length);
                return (past.length > 20 ? '...' : '') + past.substr(-20).replace(/\n/g, '');
            },

            // displays upcoming input, i.e. for error messages
            upcomingInput: function () {
                var next = this.match;
                if (next.length < 20) {
                    next += this._input.substr(0, 20 - next.length);
                }
                return (next.substr(0, 20) + (next.length > 20 ? '...' : '')).replace(/\n/g, '');
            },

            // displays the character position where the lexing error occurred, i.e. for error messages
            showPosition: function () {
                var pre = this.pastInput();
                var c = new Array(pre.length + 1).join('-');
                return pre + this.upcomingInput() + '\n' + c + '^';
            },

            // test the lexed token: return FALSE when not a match, otherwise return token
            test_match: function (match, indexed_rule) {
                var token, lines, backup;

                if (this.options.backtrack_lexer) {
                    // save context
                    backup = {
                        yylineno: this.yylineno,
                        yylloc: {
                            first_line: this.yylloc.first_line,
                            last_line: this.last_line,
                            first_column: this.yylloc.first_column,
                            last_column: this.yylloc.last_column,
                        },
                        yytext: this.yytext,
                        match: this.match,
                        matches: this.matches,
                        matched: this.matched,
                        yyleng: this.yyleng,
                        offset: this.offset,
                        _more: this._more,
                        _input: this._input,
                        yy: this.yy,
                        conditionStack: this.conditionStack.slice(0),
                        done: this.done,
                    };
                    if (this.options.ranges) {
                        backup.yylloc.range = this.yylloc.range.slice(0);
                    }
                }

                lines = match[0].match(/(?:\r\n?|\n).*/g);
                if (lines) {
                    this.yylineno += lines.length;
                }
                this.yylloc = {
                    first_line: this.yylloc.last_line,
                    last_line: this.yylineno + 1,
                    first_column: this.yylloc.last_column,
                    last_column: lines ? lines[lines.length - 1].length - lines[lines.length - 1].match(/\r?\n?/)[0].length : this.yylloc.last_column + match[0].length,
                };
                this.yytext += match[0];
                this.match += match[0];
                this.matches = match;
                this.yyleng = this.yytext.length;
                if (this.options.ranges) {
                    this.yylloc.range = [this.offset, (this.offset += this.yyleng)];
                }
                this._more = false;
                this._backtrack = false;
                this._input = this._input.slice(match[0].length);
                this.matched += match[0];
                token = this.performAction.call(this, this.yy, this, indexed_rule, this.conditionStack[this.conditionStack.length - 1]);
                if (this.done && this._input) {
                    this.done = false;
                }
                if (token) {
                    return token;
                } else if (this._backtrack) {
                    // recover context
                    for (var k in backup) {
                        this[k] = backup[k];
                    }
                    return false; // rule action called reject() implying the next rule should be tested instead.
                }
                return false;
            },

            // return next match in input
            next: function () {
                if (this.done) {
                    return this.EOF;
                }
                if (!this._input) {
                    this.done = true;
                }

                var token, match, tempMatch, index;
                if (!this._more) {
                    this.yytext = '';
                    this.match = '';
                }
                var rules = this._currentRules();
                for (var i = 0; i < rules.length; i++) {
                    tempMatch = this._input.match(this.rules[rules[i]]);
                    if (tempMatch && (!match || tempMatch[0].length > match[0].length)) {
                        match = tempMatch;
                        index = i;
                        if (this.options.backtrack_lexer) {
                            token = this.test_match(tempMatch, rules[i]);
                            if (token !== false) {
                                return token;
                            } else if (this._backtrack) {
                                match = false;
                                continue; // rule action called reject() implying a rule MISmatch.
                            } else {
                                // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)
                                return false;
                            }
                        } else if (!this.options.flex) {
                            break;
                        }
                    }
                }
                if (match) {
                    token = this.test_match(match, rules[index]);
                    if (token !== false) {
                        return token;
                    }
                    // else: this is a lexer rule which consumes input without producing a token (e.g. whitespace)
                    return false;
                }
                if (this._input === '') {
                    return this.EOF;
                } else {
                    return this.parseError('Lexical error on line ' + (this.yylineno + 1) + '. Unrecognized text.\n' + this.showPosition(), {
                        text: '',
                        token: null,
                        line: this.yylineno,
                    });
                }
            },

            // return next match that has a token
            lex: function lex() {
                var r = this.next();
                if (r) {
                    return r;
                } else {
                    return this.lex();
                }
            },

            // activates a new lexer condition state (pushes the new lexer condition state onto the condition stack)
            begin: function begin(condition) {
                this.conditionStack.push(condition);
            },

            // pop the previously active lexer condition state off the condition stack
            popState: function popState() {
                var n = this.conditionStack.length - 1;
                if (n > 0) {
                    return this.conditionStack.pop();
                } else {
                    return this.conditionStack[0];
                }
            },

            // produce the lexer rule set which is active for the currently active lexer condition state
            _currentRules: function _currentRules() {
                if (this.conditionStack.length && this.conditionStack[this.conditionStack.length - 1]) {
                    return this.conditions[this.conditionStack[this.conditionStack.length - 1]].rules;
                } else {
                    return this.conditions['INITIAL'].rules;
                }
            },

            // return the currently active lexer condition state; when an index argument is provided it produces the N-th previous condition state, if available
            topState: function topState(n) {
                n = this.conditionStack.length - 1 - Math.abs(n || 0);
                if (n >= 0) {
                    return this.conditionStack[n];
                } else {
                    return 'INITIAL';
                }
            },

            // alias for begin(condition)
            pushState: function pushState(condition) {
                this.begin(condition);
            },

            // return the number of states currently on the stack
            stateStackSize: function stateStackSize() {
                return this.conditionStack.length;
            },
            options: {},
            performAction: function anonymous(yy, yy_, $avoiding_name_collisions, YY_START) {
                var YYSTATE = YY_START;
                switch ($avoiding_name_collisions) {
                    case 0:
                        function _token(tok) {
                            /*console.log(tok);*/ return tok;
                        }
                        break;
                    case 1:
                        break;
                    case 2:
                        break;
                    case 3:
                        return _token('VENDOR_PREFIX_IDENT');
                        break;
                    case 4:
                        return _token('CALC_FUNC');
                        break;
                    case 5:
                        return _token('URL_FUNC');
                        break;
                    case 6:
                        return _token('URL_PREFIX_FUNC');
                        break;
                    case 7:
                        return _token('DOMAIN_FUNC');
                        break;
                    case 8:
                        return _token('REGEXP_FUNC');
                        break;
                    case 9:
                        return _token('AT_CHARSET');
                        break;
                    case 10:
                        return _token('AT_IMPORT');
                        break;
                    case 11:
                        return _token('AT_NAMESPACE');
                        break;
                    case 12:
                        return _token('AT_MEDIA');
                        break;
                    case 13:
                        return _token('AT_DOCUMENT');
                        break;
                    case 14:
                        return _token('AT_PAGE');
                        break;
                    case 15:
                        return _token('AT_SUPPORTS');
                        break;
                    case 16:
                        return _token('AT_FONT_FACE');
                        break;
                    case 17:
                        return _token('AT_VIEWPORT');
                        break;
                    case 18:
                        return _token('AT_COUNTER_STYLE');
                        break;
                    case 19:
                        return _token('AT_FONT_FEATURE_VALUES');
                        break;
                    case 20:
                        return _token('AT_KEYFRAMES');
                        break;
                    case 21:
                        return _token('AT_KEYWORD');
                        break;
                    case 22:
                        return _token('SELECTOR_CLASS_WITH_WHITESPACE');
                        break;
                    case 23:
                        return _token('SELECTOR_ID_WITH_WHITESPACE');
                        break;
                    case 24:
                        return _token('HASH_STRING');
                        break;
                    case 25:
                        return _token('HEXA_NUMBER');
                        break;
                    case 26:
                        return _token('DIMENSION');
                        break;
                    case 27:
                        return _token('PERCENTAGE');
                        break;
                    case 28:
                        return _token('NUMBER');
                        break;
                    case 29:
                        return _token('UNICODE_RANGE');
                        break;
                    case 30:
                        return _token('GENERAL_IDENT');
                        break;
                    case 31:
                        return _token('INCLUDE_MATCH');
                        break;
                    case 32:
                        return _token('DASH_MATCH');
                        break;
                    case 33:
                        return _token('PREFIX_MATCH');
                        break;
                    case 34:
                        return _token('SUFFIX_MATCH');
                        break;
                    case 35:
                        return _token('SUBSTRING_MATCH');
                        break;
                    case 36:
                        return _token('COLUMN');
                        break;
                    case 37:
                        return _token('CDO');
                        break;
                    case 38:
                        return _token('CDC');
                        break;
                    case 39:
                        return _token('ASSIGN_MARK');
                        break;
                    case 40:
                        return _token('NUMBER_SIGN');
                        break;
                    case 41:
                        return _token('DOLLAR_SIGN');
                        break;
                    case 42:
                        return _token('APOSTROPHE');
                        break;
                    case 43:
                        return _token('LEFT_PARENTHESIS');
                        break;
                    case 44:
                        return _token('RIGHT_PARENTHESIS');
                        break;
                    case 45:
                        return _token('ASTERISK_WITH_WHITESPACE');
                        break;
                    case 46:
                        return _token('ASTERISK');
                        break;
                    case 47:
                        return _token('PLUS_SIGN');
                        break;
                    case 48:
                        return _token('COMMA');
                        break;
                    case 49:
                        return _token('HYPHEN_MINUS');
                        break;
                    case 50:
                        return _token('FULL_STOP');
                        break;
                    case 51:
                        return _token('SOLIDUS');
                        break;
                    case 52:
                        return _token('COLON');
                        break;
                    case 53:
                        return _token('SEMICOLON');
                        break;
                    case 54:
                        return _token('LESS_THAN_SIGN');
                        break;
                    case 55:
                        return _token('GREATER_THAN_SIGN');
                        break;
                    case 56:
                        return _token('COMMERCIAL_AT');
                        break;
                    case 57:
                        return _token('LEFT_SQUARE_BRACKET');
                        break;
                    case 58:
                        return _token('REVERSE_SOLIDUS');
                        break;
                    case 59:
                        return _token('RIGHT_SQUARE_BRACKET');
                        break;
                    case 60:
                        return _token('CIRCUMFLEX_ACCENT');
                        break;
                    case 61:
                        return _token('LEFT_CURLY_BRACKET');
                        break;
                    case 62:
                        return _token('RIGHT_CURLY_BRACKET');
                        break;
                    case 63:
                        return _token('VERTICAL_LINE ');
                        break;
                    case 64:
                        return _token('TILDE');
                        break;
                    case 65:
                        return _token('UNDERSCORE');
                        break;
                    case 66:
                        return _token('GENERAL_IDENT');
                        break;
                    case 67:
                        return _token('GENERAL_IDENT');
                        break;
                    case 68:
                        return _token('GENERAL_IDENT');
                        break;
                    case 69:
                        return _token('OPERATOR_AND');
                        break;
                    case 70:
                        return _token('OPERATOR_OR');
                        break;
                    case 71:
                        return _token('OPERATOR_ONLY');
                        break;
                    case 72:
                        return _token('OPERATOR_NOT');
                        break;
                    case 73:
                        return _token('STRING');
                        break;
                    case 74:
                        return _token('GENERAL_IDENT');
                        break;
                    case 75:
                        return _token('N');
                        break;
                    case 76:
                        return _token('SELECTOR_TYPE_WITH_WHITESPACE');
                        break;
                    case 77:
                        return _token('GENERAL_IDENT');
                        break;
                    case 78:
                        return _token('IMPORTANT');
                        break;
                    case 79:
                        return _token('EOF');
                        break;
                    case 80:
                        return _token(yy_.yytext);
                        break;
                }
            },
            rules: [
                /^(?:{hack})/,
                /^(?:\/\*[^*]*\*+([^/][^*]*\*+)*\/)/,
                /^(?:(\s)+)/,
                /^(?:([-](webkit|moz|o|ms\b)[-])([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:calc\()/,
                /^(?:url(\(((\s))*((([!#$%&*-~]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))*)|(("([\t !#$%&(-~]|\\{nl}|'|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*")|('([\t !#$%&(-~]|\\{nl}|"|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*')))((\s))*\)))/,
                /^(?:url-prefix(\(((\s))*((([!#$%&*-~]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))*)|(("([\t !#$%&(-~]|\\{nl}|'|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*")|('([\t !#$%&(-~]|\\{nl}|"|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*')))((\s))*\)))/,
                /^(?:domain(\(((\s))*((([!#$%&*-~]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))*)|(("([\t !#$%&(-~]|\\{nl}|'|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*")|('([\t !#$%&(-~]|\\{nl}|"|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*')))((\s))*\)))/,
                /^(?:regexp\(((\s))*(("([\t !#$%&(-~]|\\{nl}|'|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*")|('([\t !#$%&(-~]|\\{nl}|"|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*'))((\s))*\))/,
                /^(?:@charset\b)/,
                /^(?:@import\b)/,
                /^(?:@namespace\b)/,
                /^(?:@media\b)/,
                /^(?:@document\b)/,
                /^(?:@page\b)/,
                /^(?:@supports\b)/,
                /^(?:@font-face\b)/,
                /^(?:@viewport\b)/,
                /^(?:@counter-style\b)/,
                /^(?:@font-feature-values\b)/,
                /^(?:@([-](webkit|moz|o|ms\b)[-])?keyframes\b)/,
                /^(?:[@]([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:[.]([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*)(\s)+)/,
                /^(?:[#]([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*)(\s)+)/,
                /^(?:[#]([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:[#]([0-9a-fA-F])+)/,
                /^(?:(([\+\-]?[0-9]+(\.[0-9]+)?)|([\+\-]?\.[0-9]+))([a-zA-Z])+)/,
                /^(?:(([\+\-]?[0-9]+(\.[0-9]+)?)|([\+\-]?\.[0-9]+))%)/,
                /^(?:(([\+\-]?[0-9]+(\.[0-9]+)?)|([\+\-]?\.[0-9]+)))/,
                /^(?:[Uu]\+(\?{1,6}|{h}(\?{0,5}|{h}(\?{0,4}|{h}(\?{0,3}|{h}(\?{0,2}|{h}(\??|{h})))))))/,
                /^(?:-([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:~=)/,
                /^(?:\|=)/,
                /^(?:\^=)/,
                /^(?:\$=)/,
                /^(?:\*=)/,
                /^(?:\|\|)/,
                /^(?:<!--)/,
                /^(?:-->)/,
                /^(?:=)/,
                /^(?:#)/,
                /^(?:\$)/,
                /^(?:‘)/,
                /^(?:\()/,
                /^(?:\))/,
                /^(?:[*](\s)+)/,
                /^(?:[*])/,
                /^(?:\+)/,
                /^(?:,)/,
                /^(?:-)/,
                /^(?:\.)/,
                /^(?:\/)/,
                /^(?::)/,
                /^(?:;)/,
                /^(?:<)/,
                /^(?:>)/,
                /^(?:@)/,
                /^(?:\[)/,
                /^(?:\\)/,
                /^(?:\])/,
                /^(?:\^)/,
                /^(?:\{)/,
                /^(?:\})/,
                /^(?:\|)/,
                /^(?:~)/,
                /^(?:[_])/,
                /^(?:((and\b)|(or\b)|(only\b)|(not\b))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))+)/,
                /^(?:{media_type}([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))+)/,
                /^(?:{media_feature}([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))+)/,
                /^(?:(and\b))/,
                /^(?:(or\b))/,
                /^(?:(only\b))/,
                /^(?:(not\b))/,
                /^(?:(("([\t !#$%&(-~]|\\{nl}|'|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*")|('([\t !#$%&(-~]|\\{nl}|"|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])|([\u4e00-\u9fa5_a-zA-Z0-9]))*')))/,
                /^(?:n([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:n\b)/,
                /^(?:([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*)(\s)+)/,
                /^(?:([-]?([a-zA-Z]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377]))([_]|([a-zA-Z0-9-]|([\200-\377])|((\\{h}{1,6}[ \t\r\n\f]?)|\\[ -~\200-\377])))*))/,
                /^(?:!important\b)/,
                /^(?:$)/,
                /^(?:.)/,
            ],
            conditions: {
                INITIAL: {
                    rules: [
                        0,
                        1,
                        2,
                        3,
                        4,
                        5,
                        6,
                        7,
                        8,
                        9,
                        10,
                        11,
                        12,
                        13,
                        14,
                        15,
                        16,
                        17,
                        18,
                        19,
                        20,
                        21,
                        22,
                        23,
                        24,
                        25,
                        26,
                        27,
                        28,
                        29,
                        30,
                        31,
                        32,
                        33,
                        34,
                        35,
                        36,
                        37,
                        38,
                        39,
                        40,
                        41,
                        42,
                        43,
                        44,
                        45,
                        46,
                        47,
                        48,
                        49,
                        50,
                        51,
                        52,
                        53,
                        54,
                        55,
                        56,
                        57,
                        58,
                        59,
                        60,
                        61,
                        62,
                        63,
                        64,
                        65,
                        66,
                        67,
                        68,
                        69,
                        70,
                        71,
                        72,
                        73,
                        74,
                        75,
                        76,
                        77,
                        78,
                        79,
                        80,
                    ],
                    inclusive: true,
                },
            },
        };
        return lexer;
    })();
    parser.lexer = lexer;
    function Parser() {
        this.yy = {};
    }
    Parser.prototype = parser;
    parser.Parser = Parser;
    return new Parser();
})();

if (typeof require !== 'undefined' && typeof exports !== 'undefined') {
    exports.parser = cssparser;
    exports.Parser = cssparser.Parser;
    exports.parse = function () {
        return cssparser.parse.apply(cssparser, arguments);
    };
    exports.main = function commonjsMain(args) {
        if (!args[1]) {
            console.log('Usage: ' + args[0] + ' FILE');
            process.exit(1);
        }
        var source = require('fs').readFileSync(require('path').normalize(args[1]), 'utf8');
        return exports.parser.parse(source);
    };
    if (typeof module !== 'undefined' && require.main === module) {
        exports.main(process.argv.slice(1));
    }
}
