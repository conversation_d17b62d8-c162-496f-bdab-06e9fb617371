package cn.trasen.worksheet.module.service.impl;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.trasen.BootComm.excel.ExcelNewUtils;
import cn.trasen.BootComm.utils.QrCode;
import cn.trasen.BootComm.utils.Response;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.EmployeeResp;
import cn.trasen.homs.bean.base.FileAttachmentByBusinessIdListRes;
import cn.trasen.homs.bean.base.FileAttachmentResp;
import cn.trasen.homs.bean.base.MessageWebPushReq;
import cn.trasen.homs.bean.document.Attachment;
import cn.trasen.homs.bean.hrms.HrmsSchedulingManage;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.bean.ThpsUserResp;
import cn.trasen.homs.core.exception.BusinessException;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.service.UserLoginService;
import cn.trasen.homs.core.utils.DateUtil;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.FileAttachmentFeignService;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.homs.feign.base.MessageFeignService;
import cn.trasen.homs.feign.hrms.HrmsScheduleFeignService;
import cn.trasen.homs.feign.oa.DocumentFeignClient;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import cn.trasen.worksheet.common.constant.CommonlyConstants;
import cn.trasen.worksheet.common.constant.ConstantYml;
import cn.trasen.worksheet.common.enums.CuttingOperatorEnum;
import cn.trasen.worksheet.common.enums.DictCodeEnum;
import cn.trasen.worksheet.common.enums.IndexEnum;
import cn.trasen.worksheet.common.enums.PermissionsEnum;
import cn.trasen.worksheet.common.enums.PropertyNameEnum;
import cn.trasen.worksheet.common.enums.WorkSheetStatusEnum;
import cn.trasen.worksheet.common.enums.WorkSheetTaskEnum;
import cn.trasen.worksheet.common.util.DateUtils;
import cn.trasen.worksheet.common.util.DictUtils;
import cn.trasen.worksheet.common.util.ExportUtil;
import cn.trasen.worksheet.common.util.FeignInfoUitls;
import cn.trasen.worksheet.common.util.FileUtils;
import cn.trasen.worksheet.common.util.IdUtils;
import cn.trasen.worksheet.common.util.MyBeanUtils;
import cn.trasen.worksheet.common.util.OtherUtils;
import cn.trasen.worksheet.common.util.TreeUtils;
import cn.trasen.worksheet.module.dto.inputVo.WsFileInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetHomeListInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetMobileStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetPeoppleInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWorkSheetStatisticalInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetListSelectInputVo;
import cn.trasen.worksheet.module.dto.outVo.WebSocketMsgOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsEvaluationOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsFileOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetHomePageOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetPeopleInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsSheetRemindOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetHomeListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetOderAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWorkSheetSendAgingOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsCostPageListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetDataCollectionOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoByFaultEquipmentOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobileInfoOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetMobilleStatisticsOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetSaveOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsSheetScreenListOutVo;
import cn.trasen.worksheet.module.dto.outVo.WsWsTaskInfoOutVo;
import cn.trasen.worksheet.module.dto.thirdParty.FlowInputVo;
import cn.trasen.worksheet.module.entity.WsCustometLog;
import cn.trasen.worksheet.module.entity.WsExternalPersonnel;
import cn.trasen.worksheet.module.entity.WsFaultType;
import cn.trasen.worksheet.module.entity.WsFileFile;
import cn.trasen.worksheet.module.entity.WsOmMeau;
import cn.trasen.worksheet.module.entity.WsWsSheet;
import cn.trasen.worksheet.module.entity.WsWsTask;
import cn.trasen.worksheet.module.mapper.WsWsSheetMapper;
import cn.trasen.worksheet.module.service.WsCustometLogService;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsExternalPersonnelService;
import cn.trasen.worksheet.module.service.WsFaultTypeService;
import cn.trasen.worksheet.module.service.WsFileService;
import cn.trasen.worksheet.module.service.WsOmMeauService;
import cn.trasen.worksheet.module.service.WsSheetPeopleService;
import cn.trasen.worksheet.module.service.WsSheetService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;
import cn.trasen.worksheet.module.service.WsWsCostService;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date: 2021/6/17 14:43
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 */
@Service
@Slf4j
public class WsSheetServiceImpl implements WsSheetService {

    @Autowired
    private WsWsSheetMapper wsSheetMapper;
    @Autowired
    private WsSheetTaskService wsSheetTaskService;
    @Autowired
    private WsFileService wsFileService;
    @Autowired
    private WsCustometLogService wsCustometLogService;
    @Autowired
    private WsFaultTypeService wsFaultTypeService;
    @Autowired
    private WsExternalPersonnelService wsExternalPersonnelService;
    @Autowired
    private WsEvaluationService wsEvaluationService;
    @Autowired
    private HrmsScheduleFeignService hrmsScheduleFeignService;
    @Autowired
    private WsWsCostService wsCostService;
    @Autowired
    private DocumentFeignClient documentFeignClient;
    @Autowired
    private FileAttachmentFeignService fileAttachmentFeignService;
    @Autowired
    private WsOmMeauService omMeauService;
    @Autowired
    private WsSheetPeopleService peopleService;
    
    @Autowired
    private GlobalSettingsFeignService globalSettingsFeignService;
    
    @Value("${externalPersonnel.ORG_ID}")
    private String defaultOrgId;
    @Value("${oaUrl.scanQrCodeWxUrl}")
    private String scanQrCodeWxUrl;
    @Value("${oaUrl.scanQrCodeUrl}")
    private String scanQrCodeUrl;
    @Value("${oaUrl.workInfoUrl}")
    private String workInfoUrl;
    @Value("${oaUrl.dpdUrl}")
    private String dpdUrl;
    @Value("${scheduling_enable_disable}")
    private int schedulingEnableDisable;
    @Value("${thirdPartyComponents}")
    private Integer thirdPartyComponents;
    @Value("${dingTalkUrl}")
    private String dingTalkUrl;


    /**
     * 工单保存
     *
     * @param wsSheetInputVo 工单信息
     * @return
     */
    @Transactional
    @Override
    public PlatformResult save(WsWsSheetInputVo wsSheetInputVo) {
        if (!StringUtils.isEmpty(wsSheetInputVo.getPkWsTaskId())) {
            WsWsTask wsTaskTemp = Optional.ofNullable(wsSheetTaskService.selectOneWsTaskById(wsSheetInputVo.getPkWsTaskId()))
                    .map(temp -> temp.get())
                    .orElseThrow(new BusinessException("未查询到数据"));
            if (IndexEnum.ZERO.getValue() == wsTaskTemp.getAssist() && IndexEnum.ONE.getValue() == wsTaskTemp.getComplete()) {
                throw new BusinessException("当前步骤已有其他人操作过，请刷新当前页面");
            }
        }
        
        // 工单节点表
        WsWsSheet wsSheet = new WsWsSheet();
        WsWsTask wsTask = new WsWsTask();
        // 初始化数据
        if (StringUtil.isEmpty(wsSheetInputVo.getPkWsTaskId())) {
            wsSheet = initWorkInfo(wsSheetInputVo, wsTask);
        } else {
            // 核对数据发生更改
            wsSheet = wsSheetTaskService.checkworkSheetChange(wsSheetInputVo, wsTask);
        }
        // 处理冗余字段
        FeignInfoUitls.fillNameById(wsSheet,
                Arrays.asList(
                        PropertyNameEnum.REPAIRMAN_ID.getName(),
                        PropertyNameEnum.FK_USER_ID.getName()),
                Arrays.asList(
                        wsSheetInputVo.getRepairManId(),
                        wsSheetInputVo.getFkUserId()
                ));
        
        if(!StringUtil.isEmpty(wsSheetInputVo.getRepairPhone())) {
        	wsSheet.setRepairPhone(wsSheetInputVo.getRepairPhone());        
        }

       
        if (StringUtil.isEmpty(wsSheetInputVo.getFkUserId())) {
            initWorkAndTaskStatus(wsSheet, wsTask, WorkSheetStatusEnum.SNET.getValue(), WorkSheetTaskEnum.SUBMIT.getValue());
        } else {
            wsTask.setFkUserId(wsSheet.getFkUserId());
            wsTask.setFkUserDeptId(wsSheet.getFkUserDeptId());
            // 修复外部机构人员所属机构问题
            if (wsSheet.getFkUserDeptId().equals(defaultOrgId)) {
                WsExternalPersonnel wsExternalPersonnel = wsExternalPersonnelService.selectOneById(wsSheetInputVo.getFkUserId());
                wsSheet.setFkUserDeptId(wsExternalPersonnel.getInstitutionalAffiliations());
                wsSheet.setFkUserDeptName(wsExternalPersonnel.getInstitutionalAffiliations());
            }
          
	            if (wsSheetInputVo.getFkUserId().equals(UserInfoHolder.getCurrentUserId())) {
	                initWorkAndTaskStatus(wsSheet, wsTask, WorkSheetStatusEnum.PROCESSING.getValue(), WorkSheetTaskEnum.ACCEPT_WORK_SHEET.getValue());
	            } else if (StringUtils.isEmpty(wsSheetInputVo.getPkWsTaskId())) {
	                initWorkAndTaskStatus(wsSheet, wsTask, WorkSheetStatusEnum.WAITING.getValue(), WorkSheetTaskEnum.SUBMIT.getValue());
	            } else if (!StringUtils.isEmpty(wsSheetInputVo.getPkWsTaskId())) {
	                initWorkAndTaskStatus(wsSheet, wsTask, WorkSheetStatusEnum.WAITING.getValue(), WorkSheetTaskEnum.EDITOR.getValue());
	            }
				
        }
        if (StringUtils.isEmpty(wsSheetInputVo.getPkWsTaskId())) {
            // 拼接备注
            String taskRemark = "报修人：" + wsSheet.getRepairManName() + "-" + wsSheet.getRepairManDeptName() + CommonlyConstants.CuttOperator.CUT +
                    (StringUtils.isEmpty(wsSheet.getFkUserId()) ? "" : "处理人：" + wsSheet.getFkUserName() + "-" + wsSheet.getFkUserDeptName() + CommonlyConstants.CuttOperator.CUT) +
                    (null == wsSheet.getRequiredCompletionTime() ? "" : "要求时间：" + DateUtil.format(wsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + CommonlyConstants.CuttOperator.CUT) +
                    "备注说明：" + wsSheet.getRemark();

            wsTask.setTakeRemark(taskRemark);
            synchronized (WsWsSheet.class) {
                if (!StringUtil.isEmpty(wsSheetInputVo.getPkCustometLogId())) {
                    WsCustometLog wsCustometLog = wsCustometLogService.selectOneById(wsSheetInputVo.getPkCustometLogId());
                    if (StringUtil.isEmpty(wsCustometLog.getWorkNumber())) {
                        wsSheet.setWorkNumber(IdUtils.getWorkNumber());
                        wsSheetMapper.insertWsSheet(wsSheet);
                    } else {
                        return PlatformResult.failure("该业务已有其他人员保存，请勿重复操作");
                    }
                } else {
                    wsSheet.setWorkNumber(IdUtils.getWorkNumber());
                    wsSheetMapper.insertWsSheet(wsSheet);
                    if (IndexEnum.FIVE.getValue() == wsSheet.getWorkSheetType()) {
                        // 消息推送
                        wsSheetTaskService.assembledMessages(wsSheet.getWorkNumber(), "WF", null);
                    }
                }
            }
            wsTask.setWorkNumber(wsSheet.getWorkNumber());

            // 电话报修，工单信息管理通话记录
            if (!StringUtil.isEmpty(wsSheetInputVo.getPkCustometLogId())) {
                WsCustometLog wsCustometLog = Optional.ofNullable(wsCustometLogService.selectOneById(wsSheetInputVo.getPkCustometLogId()))
                        .map(WsCustometLog::get)
                        .orElseThrow(() -> new BusinessException("未查询到通话记录信息"));
                // 特殊处理回拨
                if (IndexEnum.FIVE.getValue() == wsSheetInputVo.getCallWorkStatus()) {
                    wsCustometLog.setCallWorkStatus(wsSheetInputVo.getCallWorkStatus());
                }
                wsCustometLog.setWorkNumber(wsSheet.getWorkNumber());
                wsCustometLog.setUpdateTime(new Date());
                wsCustometLog.setUpdateBy(UserInfoHolder.getCurrentUserId());
                wsCustometLogService.updateWsCustometLog(wsCustometLog);
            }
            
            
            // 工单上报，推送提醒
            pushWeChatMessage(
                    wsSheet.getBusinessDeptId(),
                    DateUtils.dateToStringFormat("yyyy-MM-dd", new Date()),
                    wsSheet.getWorkNumber(),
                    "报修科室：" + wsSheet.getRepairManDeptName() +
                            ",报修人：" + wsSheet.getRepairManName() +
                            ",报修电话：" + wsSheet.getRepairPhone() +
                            ",故障描述：" + wsSheet.getFaultDeion()
            );

            if (!StringUtil.isEmpty(wsSheetInputVo.getFkUserId())) {
                // 消息推送
                wsSheetTaskService.assembledMessages(wsSheet.getWorkNumber(), WorkSheetTaskEnum.DISPATCH.getValue(), null);
            }
        } else {
            WsWsTask wsTaskTemp = Optional.ofNullable(wsSheetTaskService.selectOneWsTaskById(wsSheetInputVo.getPkWsTaskId()))
                    .map(temp -> temp.get())
                    .orElseThrow(() -> new BusinessException("未查询到数据"));
            // 当前节点步骤更新为已完成
            wsSheetTaskService.workSheetTaskComplete(wsTaskTemp, null);
            // 创建新步骤，初始化数据
            wsTask.setPkWsTaskId(IdUtils.getId());
            wsTask.setWorkNumber(wsTaskTemp.getWorkNumber());
            wsTask.setWorkStatus(wsSheet.getWorkStatus());
            wsTask.setTaskName(WorkSheetTaskEnum.EDITOR.getValue());
            wsTask.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
            wsTask.setAssist(CommonlyConstants.YesOrNo.NO);
            wsTask.setComplete(CommonlyConstants.YesOrNo.NO);
            wsTask.setAssist(CommonlyConstants.YesOrNo.NO);
            wsSheetMapper.updateWsSheetByWorkNumber(wsSheet);
        }
        // 处理冗余字段
        if (!StringUtil.isEmpty(wsTask.getFkUserId())) {
            FeignInfoUitls.fillNameById(wsTask,
                    Arrays.asList(
                            PropertyNameEnum.FK_USER_ID.getName()),
                    Arrays.asList(
                            wsSheetInputVo.getFkUserId()
                    ));
            // 修复外部机构人员所属机构问题
            if (wsTask.getFkUserDeptId().equals(defaultOrgId)) {
                WsExternalPersonnel wsExternalPersonnel = wsExternalPersonnelService.selectOneById(wsTask.getFkUserId());
                wsTask.setFkUserDeptId(wsExternalPersonnel.getInstitutionalAffiliations());
                wsTask.setFkUserDeptName(wsExternalPersonnel.getInstitutionalAffiliations());
            }
        }
        wsSheetTaskService.insertWsTask(wsTask);
        
        
        if (!CollectionUtil.isEmpty(wsSheetInputVo.getAudioList())) {
        	List<WsFileInputVo> wsFileInputVo = wsSheetInputVo.getWsFileInputVo();
        	wsFileInputVo.addAll(wsSheetInputVo.getAudioList());
        }
        if (!CollectionUtil.isEmpty(wsSheetInputVo.getWsFileInputVo())) {
            wsFileService.deleteFileByWorkNumber(wsSheetInputVo.getWorkNumber());
            List<WsFileFile> files = Lists.newArrayList();
            for (int i = 0; i < wsSheetInputVo.getWsFileInputVo().size(); i++) {
                WsFileFile wsFileFile = new WsFileFile();
                MyBeanUtils.copyBeanNotNull2Bean(wsSheetInputVo.getWsFileInputVo().get(i), wsFileFile);
                wsFileFile.setWorkNumber(wsTask.getWorkNumber());
                wsFileFile.setPkWsFileId(IdUtils.getId());
                files.add(wsFileFile);
            }
            wsFileService.insertBatchFile(files);
        }
        // 待处理工单数量
        long count = wsSheetMapper.selectAllList(wsSheetInputVo.getBusinessDeptId(), null, null)
                .stream()
                .filter(
                        wsSheetTemp ->
                                WorkSheetStatusEnum.SNET.getValue().equals(wsSheetTemp.getWorkStatus()) ||
                                        WorkSheetStatusEnum.WAITING.getValue().equals(wsSheetTemp.getWorkStatus()) ||
                                        WorkSheetStatusEnum.PROCESSING.getValue().equals(wsSheetTemp.getWorkStatus())
                ).count();
        return PlatformResult.success(new WsWsSheetSaveOutVo(wsSheetInputVo.getBusinessDeptName(), count));
    }


    /**
     * 工单上报，推送提醒
     *
     * @param deptId
     * @param date
     * @param workNumber
     * @return
     */
    @Override
    public Boolean pushWeChatMessage(String deptId, String date, String workNumber, String content) {
//        List<HrmsSchedulingManage> informationOnDuty = informationOnDuty(deptId, date);
//        if (CollectionUtil.isEmpty(informationOnDuty)) {
//            return false;
//        }
//        wsSheetTaskService.pushMessage(
//                workNumber,
//                "存在报修工单，请及时处理",
//                content,
//                null,
//                informationOnDuty.stream()
//                        .map(HrmsSchedulingManage::getEmployeeNo)
//                        .collect(Collectors.joining(",")),
//                informationOnDuty.stream()
//                        .map(HrmsSchedulingManage::getEmployeeId)
//                        .collect(Collectors.joining(","))
//        );
//
//        return wsSheetTaskService.pushOaMessage(
//                content,
//                "3",
//                informationOnDuty.stream()
//                        .map(HrmsSchedulingManage::getEmployeeNo)
//                        .collect(Collectors.joining(",")),
//                "存在报修工单，请及时处理",
//                "1",
//                dpdUrl + "&isWeChat=1",
//                null
//        );

    	PlatformResult<GlobalSetting> res = globalSettingsFeignService.getGlobalSetting("Y");
    	GlobalSetting globalSetting = res.getObject();
    	
    	String receiverCode = "";
    	String receiverId = "";
    	
    	if("lyszyyy".equals(globalSetting.getOrgCode())) {
//          PlatformResult<List<ThpsUserResp>> userinfo = systemUserFeignService.selectUserListBySysRoleCode("WORKSHEET_SERVICE_COUNTER");
    		receiverCode = "15200843142,13637476017,18873183576,15116248984,15974287348,18874770395";
    		receiverId = "15200843142,13637476017,18873183576,15116248984,15974287348,18874770395";
    		
    		if(!receiverCode.contains(UserInfoHolder.getCurrentUserCode())){
    			wsSheetTaskService.pushMessage(
    	                workNumber,
    	                "存在报修工单，请及时处理",
    	                content,
    	                null,
    	                receiverCode,
    	                receiverId
    	        );
    		}
    		  return true;
    	}else {
    		List<HrmsSchedulingManage> informationOnDuty = informationOnDuty(deptId, date);
            // 接收人工号
            receiverCode =
                    informationOnDuty.stream()
                            .map(HrmsSchedulingManage::getEmployeeNo)
                            .collect(Collectors.joining(","));
            // 接收人id
            receiverId =
                    informationOnDuty.stream()
                            .map(HrmsSchedulingManage::getEmployeeId)
                            .collect(Collectors.joining(","));
            
            
            if (IndexEnum.ZERO.getValue() == schedulingEnableDisable) {
                WsWorkSheetPeoppleInputVo peoppleInputVo = new WsWorkSheetPeoppleInputVo();
                peoppleInputVo.setDeptId(deptId);
                List<WsSheetPeopleInfoOutVo> oaPeopleList = peopleService.getOAPeopleList(peoppleInputVo);
                receiverCode =
                        oaPeopleList.stream()
                                .map(WsSheetPeopleInfoOutVo::getUserCode)
                                .collect(Collectors.joining(","));
                receiverId =
                        oaPeopleList.stream()
                                .map(WsSheetPeopleInfoOutVo::getUserId)
                                .collect(Collectors.joining(","));
            }
            if (StringUtils.isEmpty(receiverId) || StringUtils.isEmpty(receiverCode)) {
                return false;
            }
            
            wsSheetTaskService.pushMessage(
                    workNumber,
                    "存在报修工单，请及时处理",
                    content,
                    null,
                    receiverCode,
                    receiverId
            );

            return wsSheetTaskService.pushOaMessage(
                    content,
                    "3",
                    receiverCode,
                    "存在报修工单，请及时处理",
                    "1",
                    dpdUrl + "&isWeChat=1",
                    null
            );
    	}
    }

    /**
     * 获取值班信息
     *
     * @param deptId 科室id
     * @param date   日期 yyyy-MM-dd
     * @return
     */
    @Override
    public List<HrmsSchedulingManage> informationOnDuty(String deptId, String date) {
        Date index = new Date();
        HrmsSchedulingManage hrmsSchedulingManage = new HrmsSchedulingManage();
        hrmsSchedulingManage.setEmpOrgId(deptId);
        hrmsSchedulingManage.setSchedulingDate(date);
        PlatformResult<List<HrmsSchedulingManage>> schedulingByDateAndOrg = null;
        log.info("---------------------调用HRMS服务查询值班信息开始");
        try {
            // 无token，模拟登录
            if (org.apache.commons.lang3.StringUtils.isEmpty(UserInfoHolder.getToken())) {
                UserLoginService.loginContext(CommonlyConstants.Om.HardwareInfo.OM_TOKEN);
            }
            schedulingByDateAndOrg = hrmsScheduleFeignService.getSchedulingByDateAndOrg(hrmsSchedulingManage);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("---------------------调用HRMS服务查询值班信息失败：" + e.getMessage());
            return null;
        }
        if (!schedulingByDateAndOrg.isSuccess()) {
            log.error("---------------------调用HRMS服务查询值班信息失败：" + schedulingByDateAndOrg.getMessage());
            return null;
        }
        return schedulingByDateAndOrg.getObject().stream()
                .filter(temp -> {
                    String[] split = temp.getFrequencyTime().split(",");
                    Boolean flag = false;
                    for (String splitTemp : split) {
                        String[] dateString = splitTemp.split(" - ");
                        if (2 != dateString.length) {
                            continue;
                        }
                        Date start = DateUtils.stringtoDateYMDHMS(temp.getSchedulingDate().trim() + " " + dateString[0] + ":00");
                        Date end = DateUtils.stringtoDateYMDHMS(temp.getSchedulingDate().trim() + " " + dateString[1] + ":00");
                        // 第二个时间为次日，时间加+1
                        if (end.getTime() < start.getTime()) {
                            end = DateUtils.dateAddNDay(end, 1);
                        }
                        if (start.getTime() < index.getTime() && index.getTime() < end.getTime()) {
                            flag = true;
                            break;
                        }
                    }
                    return flag;
                })
                .collect(Collectors.toList());
    }

    /**
     * 电话已解决
     *
     * @param wsSheetInputVo
     * @return
     */
    @Override
    public int phoneHasBeenResolved(WsWsSheetInputVo wsSheetInputVo) {
        // 工单
        WsWsSheet wsSheet = new WsWsSheet();
        BeanUtils.copyProperties(wsSheetInputVo, wsSheet);
        wsSheet.setPkWsSheetId(IdUtils.getId());
        wsSheet.setWorkNumber(IdUtils.getWorkNumber());
        wsSheet.setWorkStatus(WorkSheetStatusEnum.ACCEPTANCE.getValue());
        wsSheet.setFkUserId(UserInfoHolder.getCurrentUserId());
        wsSheet.setFkUserName(UserInfoHolder.getCurrentUserName());
        wsSheet.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsSheet.setFkUserDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsSheet.setFkUserDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        wsSheet.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));

        // 节点
        WsWsTask wsTask = new WsWsTask();
        wsTask.setWorkNumber(wsSheet.getWorkNumber());
        wsTask.setPkWsTaskId(IdUtils.getId());
        wsTask.setWorkStatus(WorkSheetStatusEnum.ACCEPTANCE.getValue());
        wsTask.setTaskName(WorkSheetTaskEnum.PROCESSING_COMPLETE.getValue());
        wsTask.setComplete(CommonlyConstants.YesOrNo.NO);
        wsTask.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
        wsTask.setAssist(CommonlyConstants.YesOrNo.NO);
        wsTask.setFkUserId(UserInfoHolder.getCurrentUserId());
        wsTask.setFkUserName(UserInfoHolder.getCurrentUserName());
        wsTask.setFkUserDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsTask.setFkUserDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
        wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        // 电话报修，工单信息管理通话记录
        if (!StringUtil.isEmpty(wsSheetInputVo.getPkCustometLogId())) {
            WsCustometLog wsCustometLog = Optional.ofNullable(wsCustometLogService.selectOneById(wsSheetInputVo.getPkCustometLogId()))
                    .map(WsCustometLog::get)
                    .orElseThrow(() -> new BusinessException("未查询到通话记录信息"));
            wsCustometLog.setWorkNumber(wsSheet.getWorkNumber());
            wsCustometLog.setCallWorkStatus(IndexEnum.FOUR.getValue());
            wsCustometLog.setUpdateTime(new Date());
            wsCustometLog.setUpdateBy(UserInfoHolder.getCurrentUserId());
            wsCustometLogService.updateWsCustometLog(wsCustometLog);
        } else {
            throw new BusinessException("未电话报修，请未点击电话已解决");
        }
        // 处理冗余字段
        FeignInfoUitls.fillNameById(wsSheet,
                Arrays.asList(
                        PropertyNameEnum.REPAIRMAN_ID.getName(),
                        PropertyNameEnum.FK_USER_ID.getName()),
                Arrays.asList(
                        wsSheetInputVo.getRepairManId(),
                        wsSheetInputVo.getFkUserId()
                ));
        // 拼接备注
        String taskRemark = "报修人：" + wsSheet.getRepairManName() + "-" + wsSheet.getRepairManDeptName() + CommonlyConstants.CuttOperator.CUT +
                (StringUtils.isEmpty(wsSheet.getFkUserId()) ? "" : "处理人：" + wsSheet.getFkUserName() + "-" + wsSheet.getFkUserDeptName() + CommonlyConstants.CuttOperator.CUT) +
                (null == wsSheet.getRequiredCompletionTime() ? "" : "要求时间：" + DateUtil.format(wsSheet.getRequiredCompletionTime(), "yyyy-MM-dd") + wsSheet.getRemark() + CommonlyConstants.CuttOperator.CUT) +
                "电话报修：电话已解决。";
        wsTask.setTakeRemark(taskRemark);
        wsSheetTaskService.insertWsTask(wsTask);
        return wsSheetMapper.insertWsSheet(wsSheet);
    }

    /**
     * 工单详情
     *
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public PlatformResult workSheetInfo(String workNumber, String taskName) {
        WsWsSheet wsWsSheet = selectOneWsSheet(workNumber);
        String repairManId = "";
        String fkUserId = "";
        if (UserInfoHolder.getCurrentUserId().equals(wsWsSheet.getFkUserId())) {
            fkUserId = UserInfoHolder.getCurrentUserId();
        } else if (UserInfoHolder.getCurrentUserId().equals(wsWsSheet.getRepairManId())) {
            repairManId = UserInfoHolder.getCurrentUserId();
        }
        // 工单业务信息
        WsWsSheetInfoOutVo wsSheetInfoOutVo = wsSheetMapper.selectOneWsSheetInfo(workNumber, repairManId, fkUserId);
        //费用信息
        List<WsWsCostPageListOutVo> wsCostOutVoList = wsCostService.getListByWorkNumber(workNumber);

        // 费用字典信息
        List<DictItemResp> costStatus = DictUtils.getList(DictCodeEnum.COST_STATUS.getValue());

        List<FileAttachmentByBusinessIdListRes> attachments = Lists.newArrayList();
        // 附件业务id
        List<String> fileIds = wsCostOutVoList.stream()
                .filter(costPageListInputVoTemp -> !StringUtils.isEmpty(costPageListInputVoTemp.getFiles()))
                .map(WsWsCostPageListOutVo::getFiles)
                .collect(Collectors.toList());
        if (!StringUtils.isEmpty(fileIds)) {
            try {
                log.info("----------------------------------------调用基础服务-附件业务id查询附件信开始");
                PlatformResult<List<FileAttachmentByBusinessIdListRes>> attachment = fileAttachmentFeignService.listFileAttachmentByBusinessIdList(fileIds);
                if (!attachment.isSuccess()) {
                    log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + attachment.getMessage());
                } else {
                    attachments = attachment.getObject();
                    log.info("----------------------------------------调用基础服务-附件业务id查询附件信完成");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + e.getMessage());
            }
        }
        List<FileAttachmentByBusinessIdListRes> finalAttachments = attachments;
        wsCostOutVoList = wsCostOutVoList.stream()
                .map(cost -> {
                    // 填充费用状态字典name
                    DictItemResp dictItemResp = costStatus.stream()
                            .filter(costStatusTemp -> (cost.getCostStatus() + "").equals(costStatusTemp.getItemNameValue()))
                            .findFirst()
                            .map(temp -> temp)
                            .orElseGet(() -> new DictItemResp());
                    cost.setCostStatusName(dictItemResp.getItemName());
                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                            .filter(attachment -> attachment.getBusinessId().equals(cost.getFiles()))
                            .map(attachment -> attachment.getFileAttachmentRespList())
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(collect)) {
                        cost.setFileCount("0");
                    } else {
                        cost.setFileCount(collect.get(0).size() + "");
                    }
                    return cost;
                }).collect(Collectors.toList());

//        wsCostOutVoList.forEach(wsCostOutVo -> {
//            // 填充字典name
//            wsCostOutVo.setCostStatusName(
//                    DictUtils.getDictItemRespItemName(DictCodeEnum.COST_STATUS.getValue(), wsCostOutVo.getCostStatus() + "")
//            );
//            WsOmMeau omMeau = omMeauService.seleteOneOmMeauByWorkNumber(wsCostOutVo.getWorkNumber());
//            wsCostOutVo.setBusinessDeptId(omMeau.getDeptId());
//            wsCostOutVo.setBusinessDeptName(omMeau.getDeptName());
//        });

        // 节点日志信息
        List<WsWsTaskInfoOutVo> wsTaskInfoOutVoList = wsSheetTaskService.selectOneWsTaskInfo(workNumber, taskName);
        // 附件信息
        List<WsFileOutVo> wsFileFileList = wsFileService.selectAllList(workNumber);
        // 填充中文名称
        wsSheetInfoOutVo.setWorkStatusVaule(WorkSheetStatusEnum.getByValue(wsSheetInfoOutVo.getWorkStatus()).getName());
        // 填充节点附件信息
        List<WsFileOutVo> taskFiles = wsFileService.selectAllByTaskIdList(
                wsTaskInfoOutVoList.stream()
                        .map(WsWsTaskInfoOutVo::getPkWsTaskId)
                        .collect(Collectors.toList())
        );
        if (!CollectionUtil.isEmpty(taskFiles)) {
            wsTaskInfoOutVoList.stream()
                    .map(taskInfo -> {
                        taskInfo.setFiles(
                                taskFiles.stream()
                                        .filter(taskFileTemp -> taskFileTemp.getFkWsTaskId().equals(taskInfo.getPkWsTaskId()))
                                        .collect(Collectors.toList())
                        );
                        return taskInfo;
                    })
                    .count();
        }
        try {
            // 转换字典值
            wsSheetInfoOutVo.setRepairTypeValue(DictUtils.getDictItemRespItemName(DictCodeEnum.REPAIR_TYPE.getValue(), wsSheetInfoOutVo.getRepairType() + ""));
            wsSheetInfoOutVo.setFaultAffectScopeValue(DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_AFFECT_SCOPE.getValue(), wsSheetInfoOutVo.getFaultAffectScope() + ""));
            wsSheetInfoOutVo.setFaultEmergencyValue(DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_EMERGENCY.getValue(), wsSheetInfoOutVo.getFaultEmergency() + ""));
            wsTaskInfoOutVoList.forEach(temp -> {
                temp.setTaskNameVaule(WorkSheetTaskEnum.getByValue(temp.getTaskName() + "").getName());
            });
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("错误信息：" + e.getMessage());
        }
        // 填充录音文件录音时长
        wsFileFileList.forEach(temp -> {
            if (!StringUtils.isEmpty(temp.getFileSuffix())) {
                temp.setDuration(FileUtils.mp3ToDurationLong(ConstantYml.getInstance().getFileServerIpHost() + temp.getFileUrl(), UserInfoHolder.getToken()));
            }
        });
        // 处理超期天数
        wsSheetInfoOutVo.setCqDays(DateUtils.dateMinusDate(DateUtils.getDayStartToDate(), wsSheetInfoOutVo.getRequiredCompletionTime()));
        return PlatformResult.success(new WsWsSheetDataCollectionOutVo(wsSheetInfoOutVo, wsFileFileList, wsTaskInfoOutVoList, wsCostOutVoList));
    }

    @Override
    public PlatformResult workSheetInfoWf(String businessId) {
        WsWsSheet wsWsSheet = wsSheetMapper.selectOneWsSheetByBusinessId(businessId);
        String repairManId = "";
        String fkUserId = "";
        if (UserInfoHolder.getCurrentUserId().equals(wsWsSheet.getFkUserId())) {
            fkUserId = UserInfoHolder.getCurrentUserId();
        } else if (UserInfoHolder.getCurrentUserId().equals(wsWsSheet.getRepairManId())) {
            repairManId = UserInfoHolder.getCurrentUserId();
        }
        // 工单业务信息
        WsWsSheetInfoOutVo wsSheetInfoOutVo = wsSheetMapper.selectOneWsSheetInfo(wsWsSheet.getWorkNumber(), repairManId, fkUserId);
        //费用信息
        List<WsWsCostPageListOutVo> wsCostOutVoList = wsCostService.getListByWorkNumber(wsWsSheet.getWorkNumber());

        // 费用字典信息
        List<DictItemResp> costStatus = DictUtils.getList(DictCodeEnum.COST_STATUS.getValue());

        List<FileAttachmentByBusinessIdListRes> attachments = Lists.newArrayList();
        // 附件业务id
        List<String> fileIds = wsCostOutVoList.stream()
                .filter(costPageListInputVoTemp -> !StringUtils.isEmpty(costPageListInputVoTemp.getFiles()))
                .map(WsWsCostPageListOutVo::getFiles)
                .collect(Collectors.toList());
        if (!StringUtils.isEmpty(fileIds)) {
            try {
                log.info("----------------------------------------调用基础服务-附件业务id查询附件信开始");
                PlatformResult<List<FileAttachmentByBusinessIdListRes>> attachment = fileAttachmentFeignService.listFileAttachmentByBusinessIdList(fileIds);
                if (!attachment.isSuccess()) {
                    log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + attachment.getMessage());
                } else {
                    attachments = attachment.getObject();
                    log.info("----------------------------------------调用基础服务-附件业务id查询附件信完成");
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("----------------------------------------调用基础服务-附件业务id查询附件信失败：" + e.getMessage());
            }
        }
        List<FileAttachmentByBusinessIdListRes> finalAttachments = attachments;
        wsCostOutVoList = wsCostOutVoList.stream()
                .map(cost -> {
                    // 填充费用状态字典name
                    DictItemResp dictItemResp = costStatus.stream()
                            .filter(costStatusTemp -> (cost.getCostStatus() + "").equals(costStatusTemp.getItemNameValue()))
                            .findFirst()
                            .map(temp -> temp)
                            .orElseGet(() -> new DictItemResp());
                    cost.setCostStatusName(dictItemResp.getItemName());
                    List<List<FileAttachmentResp>> collect = finalAttachments.stream()
                            .filter(attachment -> attachment.getBusinessId().equals(cost.getFiles()))
                            .map(attachment -> attachment.getFileAttachmentRespList())
                            .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(collect)) {
                        cost.setFileCount("0");
                    } else {
                        cost.setFileCount(collect.get(0).size() + "");
                    }
                    return cost;
                }).collect(Collectors.toList());

//        wsCostOutVoList.forEach(wsCostOutVo -> {
//            // 填充字典name
//            wsCostOutVo.setCostStatusName(
//                    DictUtils.getDictItemRespItemName(DictCodeEnum.COST_STATUS.getValue(), wsCostOutVo.getCostStatus() + "")
//            );
//            WsOmMeau omMeau = omMeauService.seleteOneOmMeauByWorkNumber(wsCostOutVo.getWorkNumber());
//            wsCostOutVo.setBusinessDeptId(omMeau.getDeptId());
//            wsCostOutVo.setBusinessDeptName(omMeau.getDeptName());
//        });

        // 节点日志信息
        List<WsWsTaskInfoOutVo> wsTaskInfoOutVoList = wsSheetTaskService.selectOneWsTaskInfo(wsWsSheet.getWorkNumber(), null);
        // 附件信息
        List<WsFileOutVo> wsFileFileList = wsFileService.selectAllList(wsWsSheet.getWorkNumber());
        // 填充中文名称
        wsSheetInfoOutVo.setWorkStatusVaule(WorkSheetStatusEnum.getByValue(wsSheetInfoOutVo.getWorkStatus()).getName());
        // 填充节点附件信息
        List<WsFileOutVo> taskFiles = wsFileService.selectAllByTaskIdList(
                wsTaskInfoOutVoList.stream()
                        .map(WsWsTaskInfoOutVo::getPkWsTaskId)
                        .collect(Collectors.toList())
        );
        if (!CollectionUtil.isEmpty(taskFiles)) {
            wsTaskInfoOutVoList.stream()
                    .map(taskInfo -> {
                        taskInfo.setFiles(
                                taskFiles.stream()
                                        .filter(taskFileTemp -> taskFileTemp.getFkWsTaskId().equals(taskInfo.getPkWsTaskId()))
                                        .collect(Collectors.toList())
                        );
                        return taskInfo;
                    })
                    .count();
        }
        try {
            // 转换字典值
            wsSheetInfoOutVo.setRepairTypeValue(DictUtils.getDictItemRespItemName(DictCodeEnum.REPAIR_TYPE.getValue(), wsSheetInfoOutVo.getRepairType() + ""));
            wsSheetInfoOutVo.setFaultAffectScopeValue(DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_AFFECT_SCOPE.getValue(), wsSheetInfoOutVo.getFaultAffectScope() + ""));
            wsSheetInfoOutVo.setFaultEmergencyValue(DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_EMERGENCY.getValue(), wsSheetInfoOutVo.getFaultEmergency() + ""));
            wsTaskInfoOutVoList.forEach(temp -> {
                temp.setTaskNameVaule(WorkSheetTaskEnum.getByValue(temp.getTaskName() + "").getName());
            });
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("错误信息：" + e.getMessage());
        }
        // 填充录音文件录音时长
        wsFileFileList.forEach(temp -> {
            if (!StringUtils.isEmpty(temp.getFileSuffix())) {
                temp.setDuration(FileUtils.mp3ToDurationLong(ConstantYml.getInstance().getFileServerIpHost() + temp.getFileUrl(), UserInfoHolder.getToken()));
            }
        });
        // 处理超期天数
        wsSheetInfoOutVo.setCqDays(DateUtils.dateMinusDate(DateUtils.getDayStartToDate(), wsSheetInfoOutVo.getRequiredCompletionTime()));
        return PlatformResult.success(new WsWsSheetDataCollectionOutVo(wsSheetInfoOutVo, wsFileFileList, wsTaskInfoOutVoList, wsCostOutVoList));
    }

    /**
     * 工单编辑页面详情
     *
     * @param workNumber 工单编号
     * @return
     */
    @Override
    public PlatformResult workSheetEditInfo(String workNumber) {
        WsWsSheetOutVo wsSheetOutVo = wsSheetMapper.selectOneWsWsSheetOutVo(workNumber);
        // 补充附件信息
        wsSheetOutVo.setWsFileOutVoList(wsFileService.selectAllList(workNumber));
        return PlatformResult.success(wsSheetOutVo);
    }


    /**
     * 工单分页列表
     *
     * @param page
     * @param wsSheetListSelectInputVo
     * @return
     */
    @Override
    public List<WsWsSheetListOutVo> getWorkSheetPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
    	long begin = System.currentTimeMillis();
        List<String> repairDeptIdList = null;
        List<String> fkDeptIdList = null;
        // 服务台列表数据进行数据权限管理
        if (wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.TEN.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.ELEVEN.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.TWELVE.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.THIRTEEN.getValue() + "")) {
            wsSheetListSelectInputVo.setFkUserId(UserInfoHolder.getCurrentUserId());
            ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
            // 人员数据权限过滤
            String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "").replace("'", "");
            // 跨科室
            if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
                wsSheetListSelectInputVo.setAdmin(true);
                repairDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                // 本科室
            } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
                wsSheetListSelectInputVo.setAdmin(true);
                repairDeptIdList = Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue()));
            } else {
                //本人
                wsSheetListSelectInputVo.setAdmin(false);
                // 服务台数据特殊处理（服务台数据权限最小单位为科室）
                repairDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                // 本科室
            }
        } else {
            if (StringUtils.isEmpty(wsSheetListSelectInputVo.getType())) {
                wsSheetListSelectInputVo.setFkId(UserInfoHolder.getCurrentUserId());
                wsSheetListSelectInputVo.setRepairId(UserInfoHolder.getCurrentUserId());
            } else {
                if (wsSheetListSelectInputVo.getType().equals(IndexEnum.ONE.getValue() + "")) {
                    wsSheetListSelectInputVo.setRepairId(UserInfoHolder.getCurrentUserId());
                } else if (wsSheetListSelectInputVo.getType().equals(IndexEnum.TWO.getValue() + "")) {
                    wsSheetListSelectInputVo.setFkId(UserInfoHolder.getCurrentUserId());
                } else {
                    throw new BusinessException("工单移动端端列表type参数类型错误");
                }
            }
        }
        wsSheetListSelectInputVo.setFkDeptIdList(fkDeptIdList);
        wsSheetListSelectInputVo.setRepairDeptIdList(repairDeptIdList);
        wsSheetListSelectInputVo.initEndTime();
        wsSheetListSelectInputVo.setTheUserId(UserInfoHolder.getCurrentUserId());
        if (!StringUtil.isEmpty(wsSheetListSelectInputVo.getWorkStatusValue())) {
            wsSheetListSelectInputVo.setWorkStatusValueList(Arrays.asList(wsSheetListSelectInputVo.getWorkStatusValue().split(CuttingOperatorEnum.COMMA.getValue())));
        }

        if (!StringUtil.isEmpty(wsSheetListSelectInputVo.getWorkStatusG())) {
            wsSheetListSelectInputVo.setWorkStatusGList(Arrays.asList(wsSheetListSelectInputVo.getWorkStatusG().split(CuttingOperatorEnum.COMMA.getValue())));
        }
        long end = System.currentTimeMillis();
        System.err.println("1====:"+(end - begin));
        List<WsWsSheetListOutVo> workSheetPageList = wsSheetMapper.getWorkSheetPageList(page, wsSheetListSelectInputVo);
        long end1 = System.currentTimeMillis();
        System.err.println("2====:"+(end1-end));
        // 处理工单字段
        workSheetPageList.forEach(temp -> {
            // 处理超期天数
            temp.setCqDays(DateUtils.dateMinusDate(DateUtils.getDayStartToDate(), temp.getRequiredCompletionTime()));
            temp.setWorkStatusName(WorkSheetStatusEnum.getByValue(temp.getWorkStatus()).getName());
            // 默认为仅能查看业务
            String type = "0";
            String currentUserId = UserInfoHolder.getCurrentUserId();
            // 即为报修人也为协助人：5
            if (!StringUtils.isEmpty(temp.getAssistId()) && currentUserId.equals(temp.getRepairManId()) && temp.getAssistId().contains(currentUserId)) {
                type = "5";
                // 即为报修人也为处理人：4
            } else if (currentUserId.equals(temp.getRepairManId()) && currentUserId.equals(temp.getFkUserId())) {
                type = "4";
                // 协助人：3
            } else if (!StringUtils.isEmpty(temp.getAssistId()) && temp.getAssistId().contains(currentUserId)) {
                type = "3";
                // 报修人：1
            } else if (UserInfoHolder.getCurrentUserId().equals(temp.getRepairManId())) {
                type = "1";
                // 处理人：2
            } else if (UserInfoHolder.getCurrentUserId().equals(temp.getFkUserId())) {
                type = "2";
            }
            temp.setPeopleType(type);
        });
        System.err.println("3====:"+(System.currentTimeMillis()-end1));
        return workSheetPageList;
    }

    /**
     * 申请人首页-工单列表分页
     *
     * @param page
     * @return
     */
    @Override
    public List<WsWsSheetListOutVo> getApplicantWorkSheetPageList(Page page) {
        List<WsWsSheetListOutVo> applicantWorkSheetPageList = wsSheetMapper.getApplicantWorkSheetPageList(page, UserInfoHolder.getCurrentUserId());        // 处理工单字段
        applicantWorkSheetPageList.forEach(temp -> {
            temp.setWorkStatusName(WorkSheetStatusEnum.getByValue(temp.getWorkStatus()).getName());

        });
        return applicantWorkSheetPageList;
    }

    /**
     * 创建工单右侧列表
     *
     * @param page
     * @param faultDeion
     * @return
     */
    @Override
    public List<WsWsSheetListOutVo> getCreateWorkSheetPageList(Page page, String faultDeion, String repairManDeptId) {
        return wsSheetMapper.getCreateWorkSheetPageList(page, faultDeion, repairManDeptId).stream().map(temp -> {
            WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getWorkStatus());
            temp.setWorkStatusName(null == byValue ? null : byValue.getName());
            return temp;
        }).collect(Collectors.toList());
    }

    @Override
    public void updateByWorkNumber(WsWsSheet wsSheet) {
        wsSheetMapper.updateByWorkNumber(wsSheet);
    }

    @Override
    public void update(WsWsSheet wsSheet) {
        wsSheetMapper.updateByPrimaryKey(wsSheet);
    }

    @Override
    public void insertWsSheet(WsWsSheet wsSheet) {
        wsSheetMapper.insertWsSheet(wsSheet);
    }

    @Transactional
    @Override
    public void updateWsSheetByWorkNumber(WsWsSheet wsSheet) {
        wsSheetMapper.updateWsSheetByWorkNumber(wsSheet);
    }

    @Override
    public void updateBatch(List<WsWsSheet> wsSheet) {
        wsSheetMapper.updateBatch(wsSheet);
    }


    /**
     * 获取服务台、工单列表上各工单状态业务数据量
     *
     * @return
     */
    @Override
    public PlatformResult workSheetListBusCounts(String type, WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
        // 查询参数
        Map<String, Object> map = Maps.newHashMap();
        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
        // 人员数据权限过滤
        String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "");
        map.put("type", type);
        // 跨科室
        if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
            map.put("admin", true);
            map.put("repairDeptId", Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue())));
            map.put("fkDeptId", Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue())));
            map.put("fkUserId", UserInfoHolder.getCurrentUserId());
            // 本科室
        } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
            map.put("admin", true);
            map.put("repairDeptId", Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue())));
            map.put("fkDeptId", Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue())));
            map.put("fkUserId", UserInfoHolder.getCurrentUserId());
        } else {
            //本人
            map.put("admin", false);
            map.put("fkUserId", UserInfoHolder.getCurrentUserId());
            // 服务台数据特殊处理（服务台数据权限最小单位为科室）
            map.put("repairDeptId", Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue())));
            map.put("fkDeptId", Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue())));
        }
//        // 用户为工单业务管理员，查看用户所在科室所有业务
//        if (!StringUtils.isEmpty(currentUserInfo.getSysRoleCode()) &&
//                currentUserInfo.getSysRoleCode().contains(CommonlyConstants.Work_Sheet_Role.WORK_SHEET_BUSINESSEXCEPTION_ROLE)) {
//            map.put("admin", true);
//            map.put("repairDeptId", currentUserInfo.getDeptId());
//            map.put("fkDeptId", currentUserInfo.getDeptId());
//        }

        map.put("workNumber", wsSheetListSelectInputVo.getWorkNumber());
        map.put("faultDeion", wsSheetListSelectInputVo.getFaultDeion());
        map.put("beginTime", wsSheetListSelectInputVo.getBeginTime());
        map.put("endTime", wsSheetListSelectInputVo.getEndTime());
        map.put("workStatusG", StringUtil.isEmpty(wsSheetListSelectInputVo.getWorkStatusG()) ? null : Arrays.asList(wsSheetListSelectInputVo.getWorkStatusG().split(CuttingOperatorEnum.COMMA.getValue())));
        map.put("repairManDeptId", wsSheetListSelectInputVo.getRepairManDeptId());
        map.put("userId", wsSheetListSelectInputVo.getUserId());
        map.put("repairManId", wsSheetListSelectInputVo.getRepairManId());
        map.put("beginRequiredCompletionTime", wsSheetListSelectInputVo.getBeginRequiredCompletionTime());
        map.put("endRequiredCompletionTime", wsSheetListSelectInputVo.getEndRequiredCompletionTime());
        // 各种状态工单业务条数
        Map<String, Object> countsMap = wsSheetMapper.selectCountGroupByWorkNumber(map)
                .stream()
                .collect(Collectors.toMap(temp -> temp.get("workStatus") + "", temp -> temp.get("counts")));
        WorkSheetStatusEnum.fillStatusInfo(countsMap);
        // 12为未建单数量、13为未接来电未读数量
        Map<String, Object> mapTemp = wsCustometLogService.workOrderNotProcessedCounts(map);
        countsMap.put("12", Integer.parseInt(mapTemp.get("wjd") + ""));
        countsMap.put("13", Integer.parseInt(mapTemp.get("isRead") + ""));
        return PlatformResult.success(countsMap);
    }

    /**
     * 指定科室 获取服务台、工单列表上各工单状态业务数据量
     *
     * @param fkUserDeptId
     * @return
     */
    @Override
    public Map<String, Object> selectCountGroupByWorkNumber(String fkUserDeptId) {
        Map<String, Object> map = Maps.newHashMap();
        map.put("fkDeptId", Arrays.asList(fkUserDeptId.split(CuttingOperatorEnum.COMMA.getValue())));
        // 各种状态工单业务条数
        Map<String, Object> countsMap = wsSheetMapper.selectCountGroupByWorkNumber(map)
                .stream()
                .collect(Collectors.toMap(temp -> temp.get("workStatus") + "", temp -> temp.get("counts")));
        // 12为未建单数量、13为未接来电未读数量
        Map<String, Object> mapTemp = wsCustometLogService.workOrderNotProcessedCounts(map);
        countsMap.put("12", Integer.parseInt(mapTemp.get("wjd") + ""));
        return countsMap;
    }

    @Override
    public List<WsWsSheetListOutVo> getTookPartPageList(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo) {
        wsSheetListSelectInputVo.setFkUserId(UserInfoHolder.getCurrentUserId());

        if (!StringUtil.isEmpty(wsSheetListSelectInputVo.getWorkStatusValue())) {
            wsSheetListSelectInputVo.setWorkStatusValueList(Arrays.asList(wsSheetListSelectInputVo.getWorkStatusValue().split(CuttingOperatorEnum.COMMA.getValue())));
        }
        List<WsWsSheetListOutVo> workSheeTookParttPageList = wsSheetMapper.getTookPartPageList(page, wsSheetListSelectInputVo);
        // 处理工单字段
        workSheeTookParttPageList.forEach(temp -> {
            temp.setWorkStatusName(WorkSheetStatusEnum.getByValue(temp.getWorkStatus()).getName());
        });
        return workSheeTookParttPageList;
    }

    @Override
    public WsWsSheet selectOneWsSheet(String workNumber) {
        return wsSheetMapper.selectOneWsSheet(workNumber);
    }

    @Override
    public WsWsSheetInfoOutVo selectOneWsSheetInfo(String workNumber, String repairManId, String fkUserId) {
        return wsSheetMapper.selectOneWsSheetInfo(workNumber, repairManId, fkUserId);
    }

    @Override
    public WsWsSheet selectOneWsSheetByTaskId(String taskId) {
        return wsSheetMapper.selectOneWsSheetByTaskId(taskId);
    }

    @Override
    public List<WsWsSheet> selectWsSheetListByWorkStatus(String workStatus) {
        return wsSheetMapper.selectWsSheetListByWorkStatus(workStatus);
    }

    /**
     * 查询人员所属所有工单业务
     *
     * @param fkUserId
     * @return
     */
    @Override
    public List<WsWsSheet> selectListWsSheetByFkUserId(String fkUserId) {
        return wsSheetMapper.selectListWsSheetByFkUserId(fkUserId);
    }

    /**
     * 根据故障设备id查询工单信息
     *
     * @param faultEquipmentId
     * @return
     */
    @Override
    public List<WsWsSheetInfoByFaultEquipmentOutVo> selectListWsSheetByfaultEquipmentId(String faultEquipmentId) {
        return wsSheetMapper.selectListWsSheetByfaultEquipmentId(faultEquipmentId);
    }


    /**
     * 初始化工单、节点信息
     *
     * @param wsSheetInputVo
     * @return
     */
    public WsWsSheet initWorkInfo(WsWsSheetInputVo wsSheetInputVo, WsWsTask wsTask) {
        // 工单
        WsWsSheet wsSheet = new WsWsSheet();
        MyBeanUtils.copyBeanNotNull2Bean(wsSheetInputVo, wsSheet);
        wsSheet.setPkWsSheetId(IdUtils.getId());
        wsSheet.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
        wsSheet.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
        // 电话报修自带工单编号，不初始化
//        if (StringUtil.isEmpty(wsSheet.getWorkNumber())) {
//            wsSheet.setWorkNumber(IdUtils.getWorkNumber());
//        }
        // 节点
        wsTask.setWorkNumber(wsSheet.getWorkNumber());
        wsTask.setPkWsTaskId(IdUtils.getId());
        wsTask.setComplete(CommonlyConstants.YesOrNo.NO);
        wsTask.setWorkHours(Float.parseFloat(IndexEnum.ZERO.getValue() + ""));
        wsTask.setAssist(CommonlyConstants.YesOrNo.NO);
        wsTask.setFkUserDeptId(wsSheetInputVo.getFkUserDeptId());
        wsTask.setFkUserId(wsSheetInputVo.getFkUserId());
        wsTask.setCreateByDeptId(UserInfoHolder.getCurrentUserInfo().getDeptId());
//        // 拼接备注
//        String taskRemark = "报修人：" + wsSheetInputVo.getRepairManDeptId() + "-" + wsSheetInputVo.getRepairManId() + "/n" +
//                (StringUtils.isEmpty(wsSheetInputVo.getFkUserId()) ? "" : "处理人：" + wsSheetInputVo.getFkUserId() + "-" + wsSheetInputVo.getFkUserDeptId() + "/n") +
//                "要求时间：" + wsSheetInputVo.getRequiredCompletionTime() + "  " + wsSheetInputVo.getRemark();
//        wsTask.setTakeRemark(taskRemark);

        return wsSheet;
    }

    /**
     * 修改工单表、节点表状态
     *
     * @param wsSheet
     * @param wsTask
     * @param workStatus
     * @return
     */
    public WsWsSheet initWorkAndTaskStatus(WsWsSheet wsSheet, WsWsTask wsTask, String workStatus, String taskName) {
        wsSheet.setWorkStatus(workStatus);
        wsTask.setWorkStatus(workStatus);
        wsTask.setTaskName(taskName);
        return wsSheet;
    }

    /**
     * 工单业务完成，更新工单数据
     */
    @Transactional
    @Override
    public WsWsSheet workSheetComplete(WsWsSheet wsSheet, String workStatus, Date actualCompletionTime, Float workHours) {
        wsSheet.setWorkHours((workHours == null ? 0f : workHours) + (wsSheet.getWorkHours() == null ? 0f : wsSheet.getWorkHours()));
        if(WorkSheetStatusEnum.TERMINATED.getValue().equals(workStatus)){
            wsSheet.setEndWorkStatus(wsSheet.getWorkStatus());
        }
        wsSheet.setWorkStatus(workStatus);
        wsSheet.setUpdateBy(UserInfoHolder.getCurrentUserId());
        wsSheet.setUpdateByName(UserInfoHolder.getCurrentUserName());
        wsSheet.setUpdateTime(new Date());
        if (null != actualCompletionTime) {
            wsSheet.setActualCompletionTime(new DateTime());
        }
        updateWsSheetByWorkNumber(wsSheet);
        return wsSheet;
    }

    /**
     * 科室办理业务Top榜单
     *
     * @param beginTime
     * @param endTime
     * @param fkDeptId
     * @return 有值为科室级、null为全院级
     */
    @Override
    public List<Map<String, Object>> getDeptCountTopDatas(Page page, String beginTime, String endTime, String fkDeptId) {
        return wsSheetMapper.getDeptCountTopDatas(page, beginTime, endTime, fkDeptId);
    }

    /**
     * 一级故障类型各类型工单数量
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public List<Map<String, Object>> getLevelOneFaultTypeDatas(String beginTime, String endTime, String deptId) {
        // 所有工单信息
        List<WsWsSheet> wsWsSheetLists = selectAllList(deptId, beginTime, endTime);
        // 所有故障类型信息
        List<WsFaultType> wsFaultTypeList = wsFaultTypeService.selectFaultTypeAllList();
        // 一级故障类型id
        List<WsFaultType> parentIdList = wsFaultTypeList.stream()
                .filter(temp -> StringUtil.isEmpty(temp.getParentId()))
                .collect(Collectors.toList());
        // 返回数据
        List<Map<String, Object>> levelOneFaultTypeList = Lists.newArrayList();
        
        // 组装数据
        parentIdList.forEach(parentId -> {
            List<WsFaultType> subNodes = Lists.newArrayList();
            // 查询改节点下所有节点id
            TreeUtils.queryQllSubNodes(wsFaultTypeList, parentId.getPkFaultTypeId(), subNodes, "getParentId", "getPkFaultTypeId");
            List<String> subNodeIds = subNodes.stream().map(WsFaultType::getPkFaultTypeId).collect(Collectors.toList());
            Map<String, Object> levelOneFaultTypeMap = Maps.newHashMap();
            levelOneFaultTypeMap.put("categoryName", parentId.getCategoryName());
            // 总数
            levelOneFaultTypeMap.put(
                    "total",
                    wsWsSheetLists.stream()
                            .filter(wsSheet -> ((subNodeIds).contains(wsSheet.getFkFaultTypeId()) || (wsSheet.getFkFaultTypeId() + "").equals(parentId.getPkFaultTypeId())))
                            .collect(Collectors.toList())
                            .size()
            );
            // 完成数量
            levelOneFaultTypeMap.put(
                    "wcTotal",
                    wsWsSheetLists.stream()
                            .filter(wsSheet -> ((subNodeIds).contains(wsSheet.getFkFaultTypeId()) || (wsSheet.getFkFaultTypeId() + "").equals(parentId.getPkFaultTypeId())))
                            .filter(wsSheet ->
                                    ((wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.EVALUATE.getValue())
                                            || (wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.COMPLETED.getValue())
                                            || (wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.TERMINATED.getValue())
                                    )
                            )
                            .collect(Collectors.toList())
                            .size()
            );
            levelOneFaultTypeList.add(levelOneFaultTypeMap);
        });

        Map<String, Object> levelOneFaultTypeMapNull = Maps.newHashMap();
        //单独添加类型为null的数据
        levelOneFaultTypeMapNull.put("categoryName", "未知类型");
     // 总数
        levelOneFaultTypeMapNull.put(
                "total",
                wsWsSheetLists.stream()
                        .filter(wsSheet -> (  wsSheet.getFkFaultTypeId() == null ||  wsSheet.getFkFaultTypeId().isEmpty() ))
                        .collect(Collectors.toList())
                        .size()
        );
        // 完成数量
        levelOneFaultTypeMapNull.put(
                "wcTotal",
                wsWsSheetLists.stream()
                        .filter(wsSheet -> ( wsSheet.getFkFaultTypeId() == null ||  wsSheet.getFkFaultTypeId().isEmpty() ))
                        .filter(wsSheet ->
                                ((wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.EVALUATE.getValue())
                                        || (wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.COMPLETED.getValue())
                                        || (wsSheet.getWorkStatus() + "").equals(WorkSheetStatusEnum.TERMINATED.getValue())
                                )
                        )
                        .collect(Collectors.toList())
                        .size()
        );
        levelOneFaultTypeList.add(levelOneFaultTypeMapNull);
        
        return levelOneFaultTypeList;

//        return Optional.ofNullable(beginTime)
//                .map(temp -> {
//                    if (StringUtil.isEmpty(endTime)) {
//                        throw new BusinessException("开始时间、结束时间都是必选");
//                    }
//                    return wsSheetMapper.getLevelOneFaultTypeDatas(temp, endTime);
//                })
//                .orElse(wsSheetMapper.getLevelOneFaultTypeDatas(null, null));
    }

    /**
     * 知识库点赞Top榜单
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public List<Map<String, Object>> getKnowledgeLikeCountTopDatas(Page page, String beginTime, String endTime, String deptId) {
        return wsSheetMapper.getKnowledgeLikeCountTopDatas(page, beginTime, endTime, deptId);
    }

    /**
     * 大屏工单关键数据指标
     *
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    @Override
    public Map<String, Object> getKeyDataIndicatorsOfWorkOrder(String fkDeptId) {
        //
        WsWorkSheetHomeListInputVo workSheetHomeListInputVo = new WsWorkSheetHomeListInputVo();
        // 初始化查询参数
        if (!StringUtils.isEmpty(fkDeptId)) {
            workSheetHomeListInputVo.setType(IndexEnum.TWO.getValue());
            workSheetHomeListInputVo.setFkUserDeptId(fkDeptId);
        }
        workSheetHomeListInputVo.setBeginTime(DateUtils.getDayStart());
        workSheetHomeListInputVo.setEndTime(DateUtils.getDayEnd());
        // 今日数据
        // 今日各状态建单数据
        List<Map<String, Object>> dayCountByWorkStatus =
                DictUtils.dictCompletion(
                        wsSheetMapper.getCountGroupByWorkStatus(workSheetHomeListInputVo),
                        "work_status",
                        "counts",
                        DictCodeEnum.WORK_STATUS.getValue());
        // List转Map
        Map<String, Object> resultMap = dayCountByWorkStatus
                .stream()
                .collect(Collectors.toMap(temp -> temp.get("work_status") + "", temp -> temp.get("counts")));
        resultMap.put("dayjrjd", OtherUtils.sumInt(dayCountByWorkStatus, "counts"));

        // 本月数据
        workSheetHomeListInputVo.setBeginTime(DateUtils.getMonthStart());
        workSheetHomeListInputVo.setEndTime(DateUtils.getCurrentTime());
        List<Map<String, Object>> monthCountByWorkStatus = wsSheetMapper.getCountGroupByWorkStatus(workSheetHomeListInputVo);
        resultMap.put("monthjd", OtherUtils.sumInt(monthCountByWorkStatus, "counts"));
        resultMap.put(
                "monthwwc",
                OtherUtils.sumInt(
                        monthCountByWorkStatus
                                .stream()
                                .filter(temp -> WorkSheetStatusEnum.STATISTICS_TO_UNFINISHED.getValue().contains(temp.get("work_status") + ""))
                                .collect(Collectors.toList()),
                        "counts"
                )
        );
        // 完成率
        resultMap.put(
                "monthwcl",
                IndexEnum.ZERO.getValue() == Float.parseFloat(resultMap.get("monthjd") + "") ?
                        IndexEnum.ZERO.getValue() :
                        (1 -
                                (
                                        Float.parseFloat(
                                                IndexEnum.ZERO.getValue() == Float.parseFloat(resultMap.get("monthwwc") + "") ?
                                                        IndexEnum.ZERO.getValue() + "" :
                                                        resultMap.get("monthwwc") + ""
                                        )
                                )
                                        /
                                        Float.parseFloat(resultMap.get("monthjd") + "")
                        ) * 100
        );
        resultMap.put("wzpgdsl", wsSheetMapper.noSendOrders(fkDeptId, DateUtils.getDayStart(), DateUtils.getCurrentTime()));
        return resultMap;
    }

    /**
     * 获取今日，各科室的建单数据及总数
     *
     * @param page
     * @return
     */
    @Override
    public List<Map<String, Object>> getDayGroupByDept(Page page) {
        return wsSheetMapper.getDayGroupByDept(page, DateUtils.getDayStart(), DateUtils.getDayEnd());
    }

    /**
     * 月度划分，提单、电话提单、办结趋势数据
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    @Override
    public List<Map<String, Object>> getHotTrend(String type) {
        return wsSheetMapper.getHotTrend(type, UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    /**
     * 处理中工单
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    @Override
    public List<Map<String, Object>> getProcessTheWorkOrder(String type) {
        return wsSheetMapper.getProcessTheWorkOrder(type, UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    /**
     * 科室工单统计
     *
     * @param type 1为科室级、null为全院级
     * @return
     */
    @Override
    public List<Map<String, Object>> getDeptWorkSheetCount(String type) {
        return wsSheetMapper.getDeptWorkSheetCount(type, UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    /**
     * 科室工单质量
     *
     * @param fkDeptId 有值为科室级、null为全院级
     * @param limit    返回数据条数
     * @return
     */
    @Override
    public WsEvaluationOutVo getDepartmentWorkOrderQuality(String fkDeptId, String beginTime, String endTime, int limit) {
        WsEvaluationOutVo departmentWorkOrderQuality = wsSheetMapper.getDepartmentWorkOrderQuality(fkDeptId, beginTime, endTime);
        return Optional.ofNullable(departmentWorkOrderQuality).map(temp -> {
            departmentWorkOrderQuality.setWsEvaluationTopOutVo(wsEvaluationService.getUserEvaluationAverageScore(fkDeptId, beginTime, endTime, limit));
            return temp;
        }).orElse(new WsEvaluationOutVo());

    }

    /**
     * 服务台人员页面-统计指标
     *
     * @param fkDeptId 用户id
     * @return
     */
    @Override
    public Map<String, Object> getServiceDeskStaffStatisticalIndicators(String fkDeptId) {
        if (StringUtil.isEmpty(fkDeptId)) {
            ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
            // 人员数据权限过滤
            String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "");
            // 跨科室
            if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
                fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
                // 本科室
            } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
                fkDeptId = currentUserInfo.getDeptId();
            } else {
                // 服务台数据特殊处理（服务台数据权限最小单位为科室）
                fkDeptId = orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId();
            }
        }
        return wsSheetMapper.getServiceDeskStaffStatisticalIndicators(Arrays.asList(fkDeptId.split(CuttingOperatorEnum.COMMA.getValue())));
    }

    /**
     * 异常工单各种状态统计
     *
     * @return
     */
    @Override
    public Map<String, Object> getAbnormalWorkSheetStatisCounts(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        return wsSheetMapper.getAbnormalWorkSheetStatisCounts(wsWorkSheetHomeListInputVo);
    }

    /**
     * 超期工单数据
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getExceedTimeWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> collect = wsSheetMapper.getExceedTimeWorkSheets(page, wsWorkSheetHomeListInputVo)
                .stream()
                .map(temp -> {
                    WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getWorkStatus());
                    temp.setWorkStatusName(null == byValue ? null : byValue.getName());
                    return temp;
                }).collect(Collectors.toList());

        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                collect.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        collect.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (temp.getFkUserId().equals(user.getEmployeeId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return collect;
    }

    /**
     * 催办工单
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getHastenWorkSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> hastenWorkSheets = wsSheetMapper.getHastenWorkSheets(page, wsWorkSheetHomeListInputVo);
        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                hastenWorkSheets.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        hastenWorkSheets.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (temp.getFkUserId().equals(user.getEmployeeId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return hastenWorkSheets;
    }

    /**
     * 今日终止/暂停工单
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getSuspendTerminateSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> collect = wsSheetMapper.getSuspendTerminateSheets(page, wsWorkSheetHomeListInputVo)
                .stream()
                .map(temp -> {
                    WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getWorkStatus());
                    temp.setWorkStatusName(null == byValue ? null : byValue.getName());
                    return temp;
                }).collect(Collectors.toList());
        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                collect.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        collect.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (temp.getFkUserId().equals(user.getEmployeeId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return collect;
    }

    /**
     * 今日终止工单
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getTodayTerminationSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        wsWorkSheetHomeListInputVo.setWorkStatus(WorkSheetStatusEnum.TERMINATED.getValue());
        wsWorkSheetHomeListInputVo.setComplete(IndexEnum.ONE.getValue());
        return getSuspendTerminateSheets(page, wsWorkSheetHomeListInputVo);
    }

    /**
     * 今日暂停工单
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getTodaySuspendedSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        wsWorkSheetHomeListInputVo.setWorkStatus(WorkSheetStatusEnum.SUSPENDED.getValue());
        wsWorkSheetHomeListInputVo.setComplete(IndexEnum.ZERO.getValue());
        return getSuspendTerminateSheets(page, wsWorkSheetHomeListInputVo);
    }

    /**
     * 差评工单数据
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getBadReviewSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> collect = wsSheetMapper.getBadReviewSheets(page, wsWorkSheetHomeListInputVo)
                .stream()
                .map(temp -> {
                    WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getWorkStatus());
                    temp.setWorkStatusName(null == byValue ? null : byValue.getName());
                    return temp;
                }).collect(Collectors.toList());

        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                collect.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        collect.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (temp.getFkUserId().equals(user.getEmployeeId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return collect;
    }

    /**
     * 打回工单数据
     *
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getBackSheets(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> collect = wsSheetMapper.getBackSheets(page, wsWorkSheetHomeListInputVo)
                .stream()
                .map(temp -> {
                    WorkSheetStatusEnum byValue = WorkSheetStatusEnum.getByValue(temp.getWorkStatus());
                    temp.setWorkStatusName(null == byValue ? null : byValue.getName());
                    return temp;
                }).collect(Collectors.toList());

        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                collect.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        collect.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (user.getEmployeeId().equals(temp.getFkUserId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return collect;
    }

    /**
     * 协助工单数据
     *
     * @param page
     * @param wsWorkSheetHomeListInputVo
     * @return
     */
    @Override
    public List<WsWorkSheetHomeListOutVo> getAssistWorkOrder(Page page, WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        if (IndexEnum.FOUR.getValue() == wsWorkSheetHomeListInputVo.getType() || IndexEnum.FIVE.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserDeptId())
                    .orElseThrow(() -> new BusinessException("科室id不能为空"));
        } else if (IndexEnum.SIX.getValue() == wsWorkSheetHomeListInputVo.getType()) {
            Optional.ofNullable(wsWorkSheetHomeListInputVo.getFkUserId())
                    .orElseThrow(() -> new BusinessException("用户id不能为空"));
        }
        List<WsWorkSheetHomeListOutVo> collect = wsSheetMapper.getAssistWorkOrder(page, wsWorkSheetHomeListInputVo);

        // 补充头像
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                collect.stream()
                        .map(WsWorkSheetHomeListOutVo::getFkUserId)
                        .collect(Collectors.toList())
        );
        collect.forEach(temp -> {
            userListByIds.forEach(user -> {
                if (temp.getFkUserId().equals(user.getEmployeeId())) {
                    temp.setFkUserUrl(user.getAvatar());
                }
            });
        });
        return collect;

    }

    /**
     * 处理人、报修人各状态工单数量
     *
     * @param wsWorkSheetHomeListInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getCountGroupByWorkStatus(WsWorkSheetHomeListInputVo wsWorkSheetHomeListInputVo) {
        return Optional.ofNullable(wsWorkSheetHomeListInputVo.getType())
                .map(temp -> {
                    if (StringUtil.isEmpty(wsWorkSheetHomeListInputVo.getFkUserId())) {
                        throw new BusinessException("类型、用户id同时必传");
                    }
                    List<Map<String, Object>> countGroupByWorkStatus = wsSheetMapper.getCountGroupByWorkStatus(wsWorkSheetHomeListInputVo);
                    WorkSheetStatusEnum.fillStatusInfo(countGroupByWorkStatus, "work_status", "counts");
                    return countGroupByWorkStatus.stream()
                            .sorted(Comparator.comparing(o -> Integer.parseInt(o.get("work_status") + "")))
                            .collect(Collectors.toList());
                })
                .orElseThrow(() -> new BusinessException("类型、用户id同时必传"));
    }

    /**
     * 工单处理情况
     *
     * @return
     */
    @Override
    public Map<String, Object> getWorkOrderProcessing(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        List<Map<String, Object>> workOrderProcessing = wsSheetMapper.getWorkOrderProcessing(wsWorkSheetStatisticalInputVo);
        // 无数据补零
        DateUtils.timeIntervalZeroize(
                wsWorkSheetStatisticalInputVo.getDayOrMonthType(),
                wsWorkSheetStatisticalInputVo.getBeginTime(),
                wsWorkSheetStatisticalInputVo.getEndTime(),
                workOrderProcessing,
                "tdcounts,bjcounts"
        );
        Map<String, Object> map = Maps.newHashMap();
        // 时间升序
        List<Map<String, Object>> orderWorkOrderProcessing = workOrderProcessing.stream()
                .sorted(Comparator.comparing(o -> DateUtils.stringtoDate(o.get("date") + "", wsWorkSheetStatisticalInputVo.getDayOrMonthType())))
                .collect(Collectors.toList());
        map.put("dayOrMonthType", wsWorkSheetStatisticalInputVo.getDayOrMonthType());
        map.put("list", orderWorkOrderProcessing);
        return map;
    }

    /**
     * 总未完成工单
     *
     * @return
     */
    @Override
    public Integer getWorkOrderUnfinished(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
    	Integer UnfinishedNumber = wsSheetMapper.getWorkOrderUnfinished(wsWorkSheetStatisticalInputVo);
        return UnfinishedNumber;
    }

    /**
     * 工单各状态数据统计
     *
     * @return
     */
    @Override
    public List<Map> getWorkGroupByType(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        Optional.ofNullable(wsWorkSheetStatisticalInputVo.getStatusType())
                .orElseThrow(() -> new BusinessException("工单状态不能为空"));
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);

        // 补齐字典
        List<Map> collect = DictUtils.dictCompletion(
                wsSheetMapper.getWorkByTypeDatas(wsWorkSheetStatisticalInputVo),
                "name",
                "value",
                DictCodeEnum.getByParam(wsWorkSheetStatisticalInputVo.getStatusType()).getValue()
        )
                // 重新排序,升序
                .stream()
                .sorted(Comparator.comparingInt(o -> Integer.parseInt(o.get("name") + "")))
                .collect(Collectors.toList());
        DictUtils.dictValueToName(Map.class, collect, "name", DictCodeEnum.getByParam(wsWorkSheetStatisticalInputVo.getStatusType()).getValue());
        return collect;

    }

//    /**
//     * 工单各报修方式数据统计
//     *
//     * @return
//     */
//    @Override
//    public List<Map> getWorkGroupByRepairTypeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
//        Optional.ofNullable(wsWorkSheetStatisticalInputVo.getStatusType())
//                .orElseThrow(() -> new BusinessException("工单状态不能为空"));
//        // 初始化时间
//        intiTime(wsWorkSheetStatisticalInputVo);
//        // 补全工单状态字典
//        List<Map> collect = DictUtils.dictCompletion(
//                Map.class,
//                wsSheetMapper.getWorkByTypeDatas(wsWorkSheetStatisticalInputVo),
//                "name",
//                "count",
//                DictCodeEnum.REPAIR_TYPE.getValue()
//        )
//                // 重新排序,升序
//                .stream()
//                .sorted(Comparator.comparingInt(o -> Integer.parseInt(o.get("name") + "")))
//                .collect(Collectors.toList());
//
//        // 字典value值替换为name值
//        DictUtils.dictValueToName(Map.class, collect, "name", DictCodeEnum.REPAIR_TYPE.getValue());
//        return collect;
//
//    }
//
//    /**
//     * 工单各故障紧急程度数据统计
//     *
//     * @return
//     */
//    @Override
//    public List<Map> getWorkGroupByFaultEmergencyDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
//        Optional.ofNullable(wsWorkSheetStatisticalInputVo.getStatusType())
//                .orElseThrow(() -> new BusinessException("工单状态不能为空"));
//        // 初始化时间
//        intiTime(wsWorkSheetStatisticalInputVo);
//        // 补全工单状态字典
//        List<Map> collect = DictUtils.dictCompletion(
//                Map.class,
//                wsSheetMapper.getWorkByTypeDatas(wsWorkSheetStatisticalInputVo),
//                "name",
//                "count",
//                DictCodeEnum.FAULT_EMERGENCY.getValue()
//        )
//                // 重新排序,升序
//                .stream()
//                .sorted(Comparator.comparingInt(o -> Integer.parseInt(o.get("name") + "")))
//                .collect(Collectors.toList());
//
//        // 字典value值替换为name值
//        DictUtils.dictValueToName(Map.class, collect, "name", DictCodeEnum.FAULT_EMERGENCY.getValue());
//        return collect;
//    }
//
//    /**
//     * 工单各故障影响范围数据统计
//     *
//     * @return
//     */
//    @Override
//    public List<Map> getWorkGroupByFaultAffectScopeDatas(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
//        Optional.ofNullable(wsWorkSheetStatisticalInputVo.getStatusType())
//                .orElseThrow(() -> new BusinessException("工单状态不能为空"));
//        // 初始化时间
//        intiTime(wsWorkSheetStatisticalInputVo);
//        // 补全工单状态字典
//        List<Map> collect = DictUtils.dictCompletion(
//                Map.class,
//                wsSheetMapper.getWorkByTypeDatas(wsWorkSheetStatisticalInputVo),
//                "name",
//                "count",
//                DictCodeEnum.FAULT_AFFECT_SCOPE.getValue()
//        )
//                // 重新排序,升序
//                .stream()
//                .sorted(Comparator.comparingInt(o -> Integer.parseInt(o.get("name") + "")))
//                .collect(Collectors.toList());
//
//        // 字典value值替换为name值
//        DictUtils.dictValueToName(Map.class, collect, "name", DictCodeEnum.FAULT_AFFECT_SCOPE.getValue());
//        return collect;
//
//    }

    /**
     * 科室接单统计
     *
     * @return
     */
    @Override
    public List<Map<String, Object>> getDeptReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return wsSheetMapper.getDeptReceiveWorkSheetDatas(page, wsWorkSheetStatisticalInputVo);
    }

    @Override
    public List<Map<String, Object>> getDeptUserReceiveWorkSheetDatas(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return wsSheetMapper.getDeptUserReceiveWorkSheetDatas(page, wsWorkSheetStatisticalInputVo);
    }
    
    /**
     * 科室接单统计
     *
     * @param page
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getDeptBillOfLading(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return getDeptCountTopDatas(page, wsWorkSheetStatisticalInputVo.getBeginTime(), wsWorkSheetStatisticalInputVo.getEndTime(), UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    @Override
    public List<Map<String, Object>> getLevelOneFaultTypeDatasToDate(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return getLevelOneFaultTypeDatas(wsWorkSheetStatisticalInputVo.getBeginTime(), wsWorkSheetStatisticalInputVo.getEndTime(), UserInfoHolder.getCurrentUserInfo().getDeptId());
    }

    /**
     * 工单各设备故障分类数据统计
     *
     * @return
     */
    @Override
    public List<Map> getFaultEquipment(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return wsSheetMapper.getFaultEquipment(wsWorkSheetStatisticalInputVo);
    }

    /**
     * 科室服务平均用时排名
     *
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getDeptQualityOfService(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        return wsSheetMapper.getDeptQualityOfService(wsWorkSheetStatisticalInputVo);
    }

    /**
     * 人员服务质量
     *
     * @param page
     * @param wsWorkSheetStatisticalInputVo
     * @return
     */
    @Override
    public List<Map<String, Object>> getQualityOfPersonnelService(Page page, WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        List<Map<String, Object>> resultList = wsSheetMapper.getQualityOfPersonnelService(page, wsWorkSheetStatisticalInputVo);

        List<String> fkUserId = resultList
                .stream()
                .map(temp -> temp.get("fkUserId") + "")
                .collect(Collectors.toList());

        // 获取所有用户信息
        List<EmployeeResp> userListByIds = FeignInfoUitls.getUserListByIds(
                resultList
                        .stream()
                        .map(temp -> temp.get("fkUserId") + "")
                        .collect(Collectors.toList())
        );
        // 填充用户工号
        resultList.forEach(map -> {
            userListByIds.forEach(user -> {
                if ((map.get("fkUserId") + "").equals(user.getEmployeeId())) {
                    map.put("employeeNo", user.getEmployeeNo());
                }
            });
        });
        return resultList;
    }


    /**
     * 初始化查询参数时间
     */
    @Override
    public void intiTime(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        if (IndexEnum.ZERO.getValue() == wsWorkSheetStatisticalInputVo.getType()) {
            wsWorkSheetStatisticalInputVo.setBeginTime(DateUtils.getDayStart());
            wsWorkSheetStatisticalInputVo.setEndTime(DateUtils.getDayEnd());
        } else if (IndexEnum.ONE.getValue() == wsWorkSheetStatisticalInputVo.getType()) {
            wsWorkSheetStatisticalInputVo.setBeginTime(DateUtils.getWeekStart());
            wsWorkSheetStatisticalInputVo.setEndTime(DateUtils.getCurrentTime());
        } else if (IndexEnum.TWO.getValue() == wsWorkSheetStatisticalInputVo.getType()) {
            wsWorkSheetStatisticalInputVo.setBeginTime(DateUtils.getMonthStart());
            wsWorkSheetStatisticalInputVo.setEndTime(DateUtils.getCurrentTime());
        } else if (IndexEnum.THREE.getValue() == wsWorkSheetStatisticalInputVo.getType()) {
            wsWorkSheetStatisticalInputVo.setBeginTime(DateUtils.getYearStart());
            wsWorkSheetStatisticalInputVo.setEndTime(DateUtils.getCurrentTime());
        } else if (IndexEnum.FOUR.getValue() == wsWorkSheetStatisticalInputVo.getType()) {
            // 所有工单数量数据区间，两个key（days,months）
            Map<String, Object> map = wsSheetMapper.getAllWorkOrderTemporalInterval();
            // 年为单位
            if (IndexEnum.TWELVE.getValue() < Integer.parseInt(map.get("months") + "")) {
                wsWorkSheetStatisticalInputVo.setType(IndexEnum.TWO.getValue());

                // 月为单位
            } else if (IndexEnum.THIRTY_ONE.getValue() < Integer.parseInt(map.get("days") + "")) {
                wsWorkSheetStatisticalInputVo.setType(IndexEnum.ONE.getValue());

                // 日为单位
            } else {
                wsWorkSheetStatisticalInputVo.setType(IndexEnum.ZERO.getValue());
            }
            wsWorkSheetStatisticalInputVo.setBeginTime(null);
            wsWorkSheetStatisticalInputVo.setEndTime(null);
        }
    }

    @Override
    public List<WsWsSheet> selectAllList(String deptId, String beginTime, String endTime) {
        return wsSheetMapper.selectAllList(deptId, beginTime, endTime);
    }

    /**
     * 移动端，工单列表，各页签数量
     *
     * @return
     */
    @Override
    public Map<String, Object> mobileWorkSheetListBusCounts() {
        Map<String, Object> map = Maps.newHashMap();
        map.put("fkUserId", UserInfoHolder.getCurrentUserId());

//        ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
//        // 人员数据权限过滤
//        String orgRang = currentUserInfo.getOrgRang().replace(")","").replace("(","");
//        // 跨科室
//        if(orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())){
//            map.put("admin", true);
//            map.put("repairDeptId", orgRang+",'"+currentUserInfo.getDeptId()+"'");
//            map.put("fkDeptId", orgRang+",'"+currentUserInfo.getDeptId()+"'");
//            // 本科室
//        }else if(orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())){
//            map.put("admin", true);
//            map.put("repairDeptId", "'"+currentUserInfo.getDeptId()+"'");
//            map.put("fkDeptId", "'"+currentUserInfo.getDeptId()+"'");
//        }else{
//            //本人
//            map.put("admin", false);
//            map.put("fkUserId", UserInfoHolder.getCurrentUserId());
//            // 服务台数据特殊处理（服务台数据权限最小单位为科室）
//            map.put("repairDeptId", orgRang+",'"+currentUserInfo.getDeptId()+"'");
//            map.put("fkDeptId", orgRang+",'"+currentUserInfo.getDeptId()+"'");
//        }
        return wsSheetMapper.mobileWorkSheetListBusCounts(map);
    }

    /**
     * 移动端-工作台-我的工单状态业务数量
     *
     * @return
     */
    @Override
    public Map<String, Object> mobileWorkbenchWorkSheetBusCounts() {
        return wsSheetMapper.mobileWorkbenchWorkSheetBusCounts(UserInfoHolder.getCurrentUserId());
    }

    /**
     * 查询移动端个人详情，个人综合评分、接单、平均处理用时、完结率、返工率等
     *
     * @return
     */
    @Override
    public WsWsSheetMobileInfoOutVo selectMobileInfo() {
        return wsSheetMapper.selectMobileInfo(UserInfoHolder.getCurrentUserId());
    }

    /**
     * 移动端-工单统计-工单状态、紧急程度、影响范围
     *
     * @param wsWorkSheetMobileStatisticalInputVo
     * @return
     */
    @Override
    public List<Map> getWorkOrderType(WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo) {
        WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo = new WsWorkSheetStatisticalInputVo(
                StringUtils.isEmpty(wsWorkSheetMobileStatisticalInputVo.getBeginTime()) ?
                        null : wsWorkSheetMobileStatisticalInputVo.getBeginTime() + " 00:00:00",
                StringUtil.isEmpty(wsWorkSheetMobileStatisticalInputVo.getEndTime()) ?
                        null : DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm:ss", DateUtils.dateAddNDay(DateUtils.stringtoDate(wsWorkSheetMobileStatisticalInputVo.getEndTime()), 1)),
                wsWorkSheetMobileStatisticalInputVo.getStatusType(),
                IndexEnum.FIVE.getValue()
        );
        // 根据WsWorkSheetMobileStatisticalInputVo.statusType
        // 查询工单状态、报修方式、故障紧急程度、故障影响范围中单个类型的每个状态的工单数量
        List<Map> workGroupByType = getWorkGroupByType(wsWorkSheetStatisticalInputVo);
        workGroupByType.forEach(temp -> {
            temp.put("value", Integer.parseInt(temp.get("value") + ""));
        });
        return workGroupByType;
    }

    /**
     * 移动端-工单统计-工单情况
     *
     * @param wsWorkSheetMobileStatisticalInputVo
     * @return
     */
    @Override
    public Map<String, Object> getWorkOrderSituation(WsWorkSheetMobileStatisticalInputVo wsWorkSheetMobileStatisticalInputVo) {

        WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo = new WsWorkSheetStatisticalInputVo(
                StringUtils.isEmpty(wsWorkSheetMobileStatisticalInputVo.getBeginTime()) ?
                        null : wsWorkSheetMobileStatisticalInputVo.getBeginTime() + " 00:00:00",
                StringUtil.isEmpty(wsWorkSheetMobileStatisticalInputVo.getEndTime()) ?
                        null : DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm:ss", DateUtils.dateAddNDay(DateUtils.stringtoDate(wsWorkSheetMobileStatisticalInputVo.getEndTime()), 1)),
                wsWorkSheetMobileStatisticalInputVo.getStatusType(),
                IndexEnum.FIVE.getValue()
        );
        // 根据WsWorkSheetMobileStatisticalInputVo.statusType
        // 查询工单状态、报修方式、故障紧急程度、故障影响范围中单个类型的每个状态的工单数量
        // 补齐字典
        List<Map<String, Object>> workByTypeDatas =
                DictUtils.dictCompletion(
                        wsSheetMapper.getWorkByTypeDatas(wsWorkSheetStatisticalInputVo),
                        "name",
                        "value",
                        DictCodeEnum.getByParam(wsWorkSheetStatisticalInputVo.getStatusType()).getValue()
                );

        // 完结数
        double sum = OtherUtils.sumInt(
                workByTypeDatas
                        .stream()
                        .filter(temp -> WorkSheetStatusEnum.STATISTICS_TO_COMPLETE.getValue().contains(temp.get("name") + ""))
                        .collect(Collectors.toList()),
                "value"
        );
        // 总数
        double value = OtherUtils.sumInt(workByTypeDatas, "value");
        // list转Map
        Map<String, Object> returnMap = workByTypeDatas
                .stream()
                .collect(Collectors.toMap(temp -> temp.get("name") + "", temp -> temp.get("value") + ""));
        // 完结率
        if (IndexEnum.ZERO.getValue() == value) {
            returnMap.put("endProportion", "0");
        } else {
            returnMap.put("endProportion", String.format("%.2f", (sum / value) * 100));
        }
        wsWorkSheetMobileStatisticalInputVo.setBeginTime(StringUtils.isEmpty(wsWorkSheetMobileStatisticalInputVo.getBeginTime()) ?
                null : wsWorkSheetMobileStatisticalInputVo.getBeginTime() + " 00:00:00");
        wsWorkSheetMobileStatisticalInputVo.setEndTime(StringUtil.isEmpty(wsWorkSheetMobileStatisticalInputVo.getEndTime()) ?
                null : DateUtils.dateToStringFormat("yyyy-MM-dd HH:mm:ss", DateUtils.dateAddNDay(DateUtils.stringtoDate(wsWorkSheetMobileStatisticalInputVo.getEndTime()), 1)));
        // 填充验收通过率、平均用时、综合评分
        returnMap.putAll(wsSheetMapper.passRateAndAvgWorkHoursAndAvgScore(wsWorkSheetMobileStatisticalInputVo));
        return returnMap;
    }

    /**
     * 大屏工单处理中数据
     *
     * @param page
     * @param fkDeptId
     * @return
     */
    @Override
    public List<WsWsSheetScreenListOutVo> wsSheetScreenPageList(Page page, String fkDeptId) {
        List<WsWsSheetScreenListOutVo> screenListOutVos = wsSheetMapper.wsSheetScreenPageList(page, fkDeptId);
        // 填充字典中文值
        List<DictItemResp> dictItemRespList = DictUtils.getList(DictCodeEnum.FAULT_EMERGENCY.getValue());
        screenListOutVos.forEach(temp -> {
            temp.setWorkStatusValue(WorkSheetStatusEnum.getByValue(temp.getWorkStatus()).getName());
            dictItemRespList.forEach(dictItemResp -> {
                if (dictItemResp.getItemNameValue().equals(temp.getFaultEmergency() + "")) {
                    temp.setFaultEmergencyValue(dictItemResp.getItemName());
                }
            });
        });
        return screenListOutVos;
    }

    /**
     * 大屏，工单分配
     *
     * @param page
     * @param fkDeptId 有值为科室级、null为全院级
     * @return
     */
    @Override
    public List<Map<String, Object>> wsSheetDistributionScreenPageList(Page page, String fkDeptId, String beginTime, String endTime) {
        List<Map<String, Object>> sheetDistributionScreenPageList = wsSheetMapper.wsSheetDistributionScreenPageList(page, fkDeptId, beginTime, endTime);
        List<EmployeeResp> users = FeignInfoUitls.getUserListByIds(
                sheetDistributionScreenPageList
                        .stream()
                        .map(temp -> temp.get("fk_user_id") + "")
                        .collect(Collectors.toList())
        );
        // 填充人员去向
        sheetDistributionScreenPageList.forEach(temp -> {
            users.forEach(user -> {
                if ((temp.get("fk_user_id") + "").equals(user.getEmployeeId())) {
                    temp.put("qs", user.getWorkStatusLable());
                }
            });
        });
        return sheetDistributionScreenPageList;
    }

    /**
     * 未派工单数量
     *
     * @param fkDeptId  有值为科室级、null为全院级
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public int noSendOrders(String fkDeptId, String beginTime, String endTime) {
        return wsSheetMapper.noSendOrders(fkDeptId, beginTime, endTime);
    }

    /**
     * 待接单、待验收、待评价存在工单时normal为true
     * 已暂停、已终止存在工单时abnormal为true
     * 反之亦然
     *
     * @return
     */
    @Override
    public PlatformResult<WsSheetRemindOutVo> workSheetremind() {
        WsSheetRemindOutVo wsSheetRemindOutVo = new WsSheetRemindOutVo();
        try {
            Map<String, Object> map = wsSheetMapper.workSheetremind(UserInfoHolder.getCurrentUserId());
            if (IndexEnum.ZERO.getValue() == Integer.parseInt(map.get("normal") + "")) {
                wsSheetRemindOutVo.setNormal(false);
            }
            if (IndexEnum.ZERO.getValue() == Integer.parseInt(map.get("abnormal") + "")) {
                wsSheetRemindOutVo.setAbnormal(false);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("查询异常");
        }
        return PlatformResult.success(wsSheetRemindOutVo);
    }

    /**
     * 移动端-我的工单待派单、在办、待验收、待评价数量
     *
     * @return
     */
    @Override
    public WsWsSheetMobilleStatisticsOutVo mobileMyWorkOrderStatistics() {
        List<Map<String, Object>> workByTypeDatas = wsSheetMapper.getWorkByTypeDatas(
                new WsWorkSheetStatisticalInputVo(
                        DictCodeEnum.WORK_STATUS.getParam(),
                        UserInfoHolder.getCurrentUserId()
                )
        );
        return new WsWsSheetMobilleStatisticsOutVo(
                workByTypeDatas.stream()
                        .filter(temp -> (WorkSheetStatusEnum.WAITING.getValue() + "").equals(temp.get("name") + ""))
                        .mapToInt(e -> Integer.parseInt(e.get("value").toString()))
                        .sum(),
                workByTypeDatas.stream()
                        .filter(temp -> (WorkSheetStatusEnum.MOBILE_PROCESS.getValue() + "").contains(temp.get("name") + ""))
                        .mapToInt(e -> Integer.parseInt(e.get("value").toString()))
                        .sum(),
                workByTypeDatas.stream()
                        .filter(temp -> (WorkSheetStatusEnum.ACCEPTANCE.getValue() + "").equals(temp.get("name") + ""))
                        .mapToInt(e -> Integer.parseInt(e.get("value").toString()))
                        .sum(),
                workByTypeDatas.stream()
                        .filter(temp -> (WorkSheetStatusEnum.EVALUATE.getValue() + "").equals(temp.get("name") + ""))
                        .mapToInt(e -> Integer.parseInt(e.get("value").toString()))
                        .sum()
        );
    }

    /**
     * 流程模块保存工单
     *
     * @param request
     */
    @Override
    public PlatformResult flowToWorkSheet(HttpServletRequest request) {
        FlowInputVo flowInputVo = new FlowInputVo();
        String repairType = DictUtils.getDictItemRespItemVaule(DictCodeEnum.REPAIR_TYPE.getValue(), request.getParameter("repairType") + "");
        String fkFaultTypeId = request.getParameter("fkFaultTypeId") + "";
        String lBusinessId = request.getParameter("L_BusinessId") + "";
        String workflowInstId = request.getParameter("workflowInstId") + "";
        String workflowNo = request.getParameter("workflowNo") + "";
        if (!StringUtil.isEmpty(fkFaultTypeId) && !"null".equals(fkFaultTypeId)) {
            flowInputVo.setFkFaultTypeId(fkFaultTypeId);
        }
        String userCode = request.getParameter("L_LaunchUserCode") + "";
        flowInputVo.setBusinessDeptId(request.getParameter("businessDeptId") + "");
        flowInputVo.setFaultDeion(request.getParameter("faultDeion") + "");
        flowInputVo.setFkHospitalDistrictId(StringUtil.isEmpty(request.getParameter("fkHospitalDistrictId")) ? "" : request.getParameter("fkHospitalDistrictId"));
        flowInputVo.setFileIds(StringUtil.isEmpty(request.getParameter("fileIds")) ? "" : request.getParameter("fileIds"));
        if (!StringUtils.isEmpty(repairType) && null != OtherUtils.parseInt(repairType)) {
            flowInputVo.setRepairType(OtherUtils.parseInt(repairType));
        }
        if (0 == flowInputVo.getRepairType()) {
            flowInputVo.setRepairType(3);
        }
        EmployeeResp employeeResp = FeignInfoUitls.getUserByCode(userCode);
        flowInputVo.setRepairManId(employeeResp.getEmployeeId());
        flowInputVo.setRepairManDeptId(employeeResp.getOrgId());
        WsWsSheetInputVo wsSheetInputVo = new WsWsSheetInputVo();
        MyBeanUtils.copyBeanNotNull2Bean(flowInputVo, wsSheetInputVo);
        wsSheetInputVo.setFaultAffectScope(IndexEnum.ONE.getValue());
        wsSheetInputVo.setFaultEmergency(IndexEnum.THREE.getValue());
        wsSheetInputVo.setWorkSheetType(IndexEnum.FIVE.getValue());
        wsSheetInputVo.setCreateBy(employeeResp.getEmployeeId());
        wsSheetInputVo.setCreateByName(employeeResp.getEmployeeName());
        wsSheetInputVo.setRemark("");
        wsSheetInputVo.setLBusinessId(lBusinessId);
        wsSheetInputVo.setWorkflowInstId(workflowInstId);
        wsSheetInputVo.setWorkflowNo(workflowNo);
        if (!StringUtil.isEmpty(flowInputVo.getFileIds())) {
            try {
                log.info("-------------------------------------------调用OA服务-查询附件信息开始");

                PlatformResult<List<Attachment>> byIds = documentFeignClient.selectByIds(flowInputVo.getFileIds(), UserInfoHolder.getCurrentUserId());
                if (byIds.isSuccess()) {
                    wsSheetInputVo.setWsFileInputVo(
                            byIds.getObject().stream()
                                    .map(temp -> {
                                        WsFileInputVo fileInputVo = new WsFileInputVo();
                                        fileInputVo.setFkFileId(temp.getId());
                                        fileInputVo.setFileUrl(temp.getRealPath());
                                        fileInputVo.setFkFileName(temp.getOriginalName());
                                        return fileInputVo;
                                    }).collect(Collectors.toList())
                    );
                } else {
                    log.error("-------------------------------------------调用OA服务-查询附件信息失败：" + byIds.getMessage());
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("-------------------------------------------调用OA服务-查询附件信息失败：" + e.getMessage());

            }
        }
        return save(wsSheetInputVo);
    }

    /**
     * 生成扫码报修的二维码
     *
     * @return
     */
    @Override
    public String scanQrCode(Integer scanQrCodeSize) {
        try {
            String url = scanQrCodeUrl();
            log.info("二维码内容---------------------："+url);
            if(null == scanQrCodeSize){
                scanQrCodeSize = 160;
            }
            return Base64.getEncoder().encodeToString(
                    QrCode.createQrCode(
                            url,
                            scanQrCodeSize,
                            3,
                            "png"
                    )
            );
        } catch (Exception e) {
            log.error("生成扫码报修二维码失败：" + e.getMessage());
        }
        throw new BusinessException("生成扫码报修二维码失败");
    }

    /**
     * 二维码内容
     * @return
     */
    private String scanQrCodeUrl() {
        return IndexEnum.ZERO.getValue() == thirdPartyComponents ?
                dingTalkUrl :
                scanQrCodeWxUrl +
                        "?loginType=1&url=" +
                        new String(org.apache.commons.codec.binary.Base64.encodeBase64(scanQrCodeUrl.getBytes()), StandardCharsets.UTF_8);
    }


    /**
     * 填充处理人员信息，处理中工单数量
     *
     * @param peopleInfoVos 处理人员信息
     * @return
     */
    @Override
    public List<WsSheetPeopleInfoOutVo> fillPeopleProcessCount(List<WsSheetPeopleInfoOutVo> peopleInfoVos) {
        if (CollectionUtil.isEmpty(peopleInfoVos)) {
            return null;
        }
        List<WsSheetPeopleInfoOutVo> peopleProcessCounts = wsSheetMapper.peopleProcessCount(
                peopleInfoVos.stream()
                        .map(WsSheetPeopleInfoOutVo::getUserId)
                        .collect(Collectors.toList())
        );
        // 填充处理中工单数量并排序
        List<WsSheetPeopleInfoOutVo> returnPeoples = peopleInfoVos.stream()
                .map(peopleInfoVo -> {
                    if (CollectionUtil.isEmpty(peopleProcessCounts)) {
                        peopleInfoVo.setProcessCount(0);
                    } else {
                        peopleProcessCounts.forEach(peopleProcessCount -> {
                            if (peopleInfoVo.getUserId().equals(peopleProcessCount.getUserId())) {
                                peopleInfoVo.setProcessCount(peopleProcessCount.getProcessCount());
                            } else if (null == peopleInfoVo.getProcessCount()) {
                                peopleInfoVo.setProcessCount(0);
                            }
                        });
                    }
                    return peopleInfoVo;
                })
                .collect(Collectors.toList());
        returnPeoples.forEach(returnPeople -> {
            returnPeople.setProcessCountString(returnPeople.getProcessCount() + "个");
        });
        return returnPeoples.stream()
                .sorted(Comparator.comparing(WsSheetPeopleInfoOutVo::getProcessCount, Comparator.nullsLast(Integer::compareTo)))
                .collect(Collectors.toList());
    }

    /**
     * 查询科室未派单工单信息
     *
     * @param deptId
     * @return
     */
    @Override
    public List<Map<String, Object>> unprocessedMessageAlertsDpdDjd(String workStatus, String deptId, List<String> list) {
        return wsSheetMapper.unprocessedMessageAlertsDpdDjd(workStatus, deptId, list);
    }

    /**
     * 查询处理人，某工单状态数量
     *
     * @param fkUserId
     * @param workStatus
     * @return
     */
    @Override
    public int fkUserWorkStatusCount(String fkUserId, String fkDeptId, String workStatus) {
        return wsSheetMapper.fkUserWorkStatusCount(fkUserId, fkDeptId, workStatus);
    }

    /**
     * OA首页 待接单、待派单数量
     *
     * @return
     */
    @Override
    public WsSheetHomePageOutVo dpdDjdCount() {
        WsSheetHomePageOutVo homePageOutVo = new WsSheetHomePageOutVo();
        homePageOutVo.setDpd(fkUserWorkStatusCount(null, UserInfoHolder.getCurrentUserInfo().getDeptId(), "1"));
        homePageOutVo.setDjd(fkUserWorkStatusCount(UserInfoHolder.getCurrentUserId(), null, "2"));
        return homePageOutVo;
    }

    /**
     * 导出Excel
     *
     * @param page
     * @param wsSheetListSelectInputVo
     * @param response
     * @param request
     */
    @Override
    public void exportExcel(Page page, WsWsSheetListSelectInputVo wsSheetListSelectInputVo, HttpServletResponse response, HttpServletRequest request) {
        List<String> repairDeptIdList = null;
        List<String> fkDeptIdList = null;
        // 服务台列表数据进行数据权限管理
        if (wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.TEN.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.ELEVEN.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.TWELVE.getValue() + "") ||
                wsSheetListSelectInputVo.getWorkStatus().equals(IndexEnum.THIRTEEN.getValue() + "")) {
            wsSheetListSelectInputVo.setFkUserId(UserInfoHolder.getCurrentUserId());
            ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
            // 人员数据权限过滤
            log.info("========orgRang1=======" + UserInfoHolder.getOrgRang());
            
            String orgRang = currentUserInfo.getOrgRang().replace(")", "").replace("(", "").replace("'", "").replace("'", "");
            
            log.info("========orgRang2=======" + orgRang);
            // 跨科室
            if (orgRang.contains(PermissionsEnum.SELF_SUB_DEPT.getValue())) {
                wsSheetListSelectInputVo.setAdmin(true);
                repairDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                // 本科室
            } else if (orgRang.contains(PermissionsEnum.SELF_DEPT.getValue())) {
                wsSheetListSelectInputVo.setAdmin(true);
                repairDeptIdList = Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList(currentUserInfo.getDeptId().split(CuttingOperatorEnum.COMMA.getValue()));
            } else {
                //本人
                wsSheetListSelectInputVo.setAdmin(false);
                // 服务台数据特殊处理（服务台数据权限最小单位为科室）
                repairDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                fkDeptIdList = Arrays.asList((orgRang + CuttingOperatorEnum.COMMA.getValue() + currentUserInfo.getDeptId()).split(CuttingOperatorEnum.COMMA.getValue()));
                // 本科室
            }
        } else {
            if (StringUtils.isEmpty(wsSheetListSelectInputVo.getType())) {
                wsSheetListSelectInputVo.setFkId(UserInfoHolder.getCurrentUserId());
                wsSheetListSelectInputVo.setRepairId(UserInfoHolder.getCurrentUserId());
            } else {
                if (wsSheetListSelectInputVo.getType().equals(IndexEnum.ONE.getValue() + "")) {
                    wsSheetListSelectInputVo.setRepairId(UserInfoHolder.getCurrentUserId());
                } else if (wsSheetListSelectInputVo.getType().equals(IndexEnum.TWO.getValue() + "")) {
                    wsSheetListSelectInputVo.setFkId(UserInfoHolder.getCurrentUserId());
                } else {
                    throw new BusinessException("工单移动端端列表type参数类型错误");
                }
            }
        }
        wsSheetListSelectInputVo.setFkDeptIdList(fkDeptIdList);
        wsSheetListSelectInputVo.setRepairDeptIdList(repairDeptIdList);
        wsSheetListSelectInputVo.initEndTime();
        wsSheetListSelectInputVo.setTheUserId(UserInfoHolder.getCurrentUserId());
        if (!StringUtil.isEmpty(wsSheetListSelectInputVo.getWorkStatusValue())) {
            wsSheetListSelectInputVo.setWorkStatusValueList(Arrays.asList(wsSheetListSelectInputVo.getWorkStatusValue().split(CuttingOperatorEnum.COMMA.getValue())));
        }
        List<WsWsSheetListOutVo> workSheetPageList = wsSheetMapper.getWorkSheetPageList(page, wsSheetListSelectInputVo);

        // 文件名、表头
        String excelName = "";
        // 列头
        List<String> headList = Lists.newArrayList();
        List<Map<String, Object>> dataList = Lists.newArrayList();
        if ("13".equals(wsSheetListSelectInputVo.getWorkStatus())) {
            excelName = "全部工单";
            headList.add("工单号");
            headList.add("报修科室");
            headList.add("报修人");
            headList.add("报修时间");
            headList.add("故障描述");
            headList.add("业务类型");
            headList.add("建单人");
            headList.add("要求日期");
            headList.add("处理人");
            headList.add("处理科室");
            headList.add("状态");
            headList.add("催办次数");
            workSheetPageList.forEach(workSheet -> {
                Map map = new HashMap<>();
                map.put("工单号", workSheet.getWorkNumber());
                map.put("报修科室", workSheet.getRepairManDeptName());
                map.put("报修人", workSheet.getRepairManName());
                map.put("报修时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getCreateTime()));
                map.put("故障描述", workSheet.getFaultDeion());
                map.put("业务类型", workSheet.getFkFaultTypeName());
                map.put("建单人", workSheet.getCreateByName());
                map.put("要求日期", DateUtils.dateToStringFormat("yyyy-MM-dd", workSheet.getRequiredCompletionTime()));
                map.put("处理人", workSheet.getFkUserName());
                map.put("处理科室", workSheet.getFkUserDeptName());
                map.put("状态", WorkSheetStatusEnum.getByValue(workSheet.getWorkStatus()).getName());
                map.put("催办次数", workSheet.getHatenCount());
                dataList.add(map);
            });
        } else if ("12".equals(wsSheetListSelectInputVo.getWorkStatus())) {
            excelName = "待派单";
            headList.add("工单号");
            headList.add("故障描述");
            headList.add("业务类型");
            headList.add("报修时间");
            headList.add("要求日期");
            headList.add("报修科室");
            headList.add("报修人");
            workSheetPageList.forEach(workSheet -> {
                Map map = new HashMap<>();
                map.put("工单号", workSheet.getWorkNumber());
                map.put("故障描述", workSheet.getFaultDeion());
                map.put("业务类型", workSheet.getFkFaultTypeName());
                map.put("报修时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getCreateTime()));
                map.put("要求日期", DateUtils.dateToStringFormat("yyyy-MM-dd", workSheet.getRequiredCompletionTime()));
                map.put("报修科室", workSheet.getRepairManDeptName());
                map.put("报修人", workSheet.getRepairManName());

                dataList.add(map);
            });
        } else if ("10".equals(wsSheetListSelectInputVo.getWorkStatus())) {
            excelName = "处理中";
            headList.add("工单号");
            headList.add("报修科室");
            headList.add("报修人");
            headList.add("报修时间");
            headList.add("报修电话");
            headList.add("故障描述");
            headList.add("业务类型");
            headList.add("建单人");
            headList.add("要求日期");
            headList.add("处理人");
            headList.add("处理科室");
            headList.add("联系方式");
            headList.add("状态");
            headList.add("催办次数");
            workSheetPageList.forEach(workSheet -> {
                Map map = new HashMap<>();
                map.put("工单号", workSheet.getWorkNumber());
                map.put("报修科室", workSheet.getRepairManDeptName());
                map.put("报修人", workSheet.getRepairManName());
                map.put("报修时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getCreateTime()));
                map.put("报修电话", workSheet.getRepairPhone());
                map.put("故障描述", workSheet.getFaultDeion());
                map.put("业务类型", workSheet.getFkFaultTypeName());
                map.put("建单人", workSheet.getCreateByName());
                map.put("要求日期", DateUtils.dateToStringFormat("yyyy-MM-dd", workSheet.getRequiredCompletionTime()));
                map.put("处理人", workSheet.getFkUserName());
                map.put("处理科室", workSheet.getFkUserDeptName());
                map.put("联系方式", workSheet.getFkUserPhone());
                map.put("状态", WorkSheetStatusEnum.getByValue(workSheet.getWorkStatus()).getName());
                map.put("催办次数", workSheet.getHatenCount());
                dataList.add(map);
            });
        } else if ("11".equals(wsSheetListSelectInputVo.getWorkStatus())) {
            excelName = "已完成";
            headList.add("工单号");
            headList.add("故障描述");
            headList.add("业务类型");
            headList.add("报修时间");
            headList.add("确认时间");
            headList.add("终止时间");
            headList.add("报修科室");
            headList.add("报修人");
            headList.add("报修电话");
            headList.add("处理人");
            headList.add("处理科室");
            headList.add("状态");
            workSheetPageList.forEach(workSheet -> {
                Map map = new HashMap<>();
                map.put("工单号", workSheet.getWorkNumber());
                map.put("故障描述", workSheet.getFaultDeion());
                map.put("业务类型", workSheet.getFkFaultTypeName());
                map.put("报修时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getCreateTime()));
                map.put("确认时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getConfirmTime()));
                map.put("终止时间", DateUtils.dateToStringFormat("MM-dd HH:mm", workSheet.getTerminationTime()));
                map.put("报修科室", workSheet.getRepairManDeptName());
                map.put("报修人", workSheet.getRepairManName());
                map.put("报修电话", workSheet.getRepairPhone());
                map.put("处理人", workSheet.getFkUserName());
                map.put("处理科室", workSheet.getFkUserDeptName());
                map.put("状态", WorkSheetStatusEnum.getByValue(workSheet.getWorkStatus()).getName());
                dataList.add(map);
            });
        }
        try {
            log.info("----------------------------------------------生成Excel开始：" + new Date());
            ExportUtil.createExcel(excelName, headList, headList, dataList, response, request);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("----------------------------------------------生成Excel失败：" + new Date() + "。" + e.getMessage());
        }
    }

    /**
     * 导出故障申请表单
     *
     * @param response
     * @param request
     * @param workNumber
     */
    @Override
    public void printExcel(HttpServletResponse response, HttpServletRequest request, String workNumber) {
        try {
            WsWsSheet wsSheet = Optional.ofNullable(wsSheetMapper.selectOneWsSheet(workNumber))
                    .orElseThrow(() -> new BusinessException("未查询到工单信息"));
            String fkFaultTypeName = Optional.ofNullable(wsFaultTypeService.selectOne(wsSheet.getFkFaultTypeId()))
                    .map(temp -> temp.getCategoryName())
                    .orElseGet(() -> "");
            WsOmMeau omMeau = new WsOmMeau();
            omMeau.setDeptId(wsSheet.getBusinessDeptId());
            String meau = Optional.ofNullable(omMeauService.seleteOneOmMeau(omMeau))
                    .map(temp -> temp.getDeptName())
                    .orElseGet(null);
            Map<String, String> dataMap = new HashMap();
            dataMap.put("$date", DateUtils.getCurrentTime());
            dataMap.put("$repairManName", wsSheet.getRepairManName());
            dataMap.put("$fkUserName", StringUtil.isEmpty(wsSheet.getFkUserName()) ? "" : wsSheet.getFkUserName());
            dataMap.put("$repairDeptAddress", StringUtil.isEmpty(wsSheet.getRepairDeptAddress()) ? "" : wsSheet.getRepairDeptAddress());
            dataMap.put("$repairPhone", StringUtil.isEmpty(wsSheet.getRepairPhone()) ? "" : wsSheet.getRepairPhone());
            dataMap.put("$fkFaultTypeName", StringUtil.isEmpty(fkFaultTypeName) ? "" : fkFaultTypeName);
            dataMap.put("$faultEquipmentName", StringUtil.isEmpty(wsSheet.getFaultEquipmentName()) ? "" : wsSheet.getFaultEquipmentName());
            dataMap.put("$faultDeion", wsSheet.getFaultDeion());
            dataMap.put("$fkUserDeptName", StringUtil.isEmpty(meau) ? "" : meau);
            dataMap.put("$faultAffectScopeValue", DictUtils.getDictItemRespItemName(DictCodeEnum.FAULT_AFFECT_SCOPE.getValue(), wsSheet.getFaultAffectScope() + ""));
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("故障处理申请单.xlsx", "UTF-8"));
            ClassPathResource resource = new ClassPathResource("/template/故障处理申请单.xlsx");
            if (!resource.exists()) {
                log.error("数据模板不存在！");
                throw new BusinessException("数据模板不存在！");
            }
            byte[] excelBytes = ExcelNewUtils.write(resource.getInputStream(), dataMap);
            Response.Write(response, excelBytes);
        } catch (IOException e) {
            log.error("导出报错:" + e.getMessage());
            e.printStackTrace();
        }
    }


    /**
     * 时效性分析-派单时效
     *
     * @return
     */
    @Override
    public List<WsWorkSheetSendAgingOutVo> selectSheetSendAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        List<WsWorkSheetSendAgingOutVo> agingOutVos = wsSheetMapper.selectSheetSendAgingOutVo(wsWorkSheetStatisticalInputVo);
        // 转换时间
        agingOutVos.forEach(temp -> {
            temp.setDispatchAvgTimeValue(OtherUtils.secToTime(temp.getDispatchAvgtTime()));
            temp.setDispatchLongestTimeValue(OtherUtils.secToTime(temp.getDispatchLongestTime()));
            temp.setDispatchShortestTimeValue(OtherUtils.secToTime(temp.getDispatchShortestTime()));
            temp.setDispatchTimeValue(OtherUtils.secToTime(temp.getDispatchTime()));
            
            if (temp.getDispatchTimeDifference() < 0) {
                temp.setFlag(false);
                temp.setDispatchTimeDifferenceValue(OtherUtils.secToTime(-1 * temp.getDispatchTimeDifference()));
                temp.setDispatchTimeDifferenceValue("快" + temp.getDispatchTimeDifferenceValue());
            } else {
                temp.setFlag(true);
                temp.setDispatchTimeDifferenceValue(OtherUtils.secToTime(temp.getDispatchTimeDifference()));
                temp.setDispatchTimeDifferenceValue("慢" + temp.getDispatchAvgTimeValue());

            }
        });
        return agingOutVos;
    }

    /**
     * 时效性分析-接单时效
     *
     * @return
     */
    @Override
    public List<WsWorkSheetOderAgingOutVo> selectSheetOderAgingOutVo(WsWorkSheetStatisticalInputVo wsWorkSheetStatisticalInputVo) {
        // 初始化时间
        intiTime(wsWorkSheetStatisticalInputVo);
        List<WsWorkSheetOderAgingOutVo> oderAgingOutVos = wsSheetMapper.selectSheetOderAgingOutVo(wsWorkSheetStatisticalInputVo);
        // 转换时间
        oderAgingOutVos.forEach(temp -> {
            temp.setOrderAvgTimeValue(OtherUtils.secToTime(temp.getOrderAvgtTime()));
            temp.setOrderLongestTimeValue(OtherUtils.secToTime(temp.getOrderLongestTime()));
            temp.setOrderShortestTimeValue(OtherUtils.secToTime(temp.getOrderShortestTime()));
            temp.setCsl(temp.getCsl() + "%");
            temp.setZsl(temp.getZsl() + "%");
        });
        return oderAgingOutVos;
    }

}
