package cn.trasen.worksheet.module.controller;



import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.annotation.ControllerLog;
import cn.trasen.homs.core.annotation.NoRepeatSubmit;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.worksheet.module.dto.inputVo.WsWsEvaluateInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsSheetAssistInputVo;
import cn.trasen.worksheet.module.dto.inputVo.WsWsTaskInputVo;
import cn.trasen.worksheet.module.service.KnowledgeTypeService;
import cn.trasen.worksheet.module.service.WsEvaluationService;
import cn.trasen.worksheet.module.service.WsSheetTaskService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * <AUTHOR>
 * @date: 2021/6/17 13:57
 * @Copyright: Copyright (c) 2006 - 2021
 * @Company: 湖南创星科技股份有限公司
 * @Version: V1.0
 * 工单节点
 */
@RestController
@Api(tags = "工单业务节点操作")
public class WorkSheetTaskController {

    @Autowired
    private WsEvaluationService wsEvaluationService;

    @Autowired
    private WsSheetTaskService wsSheetTaskService;

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="恢复所需信息")
    @ApiOperation(value = "恢复所需信息", notes = "恢复所需信息")
    @GetMapping("/workTask/openNodeInfo")
    public PlatformResult openNodeInfo(String pkWsTaskId){
        try {
        	return wsSheetTaskService.openNodeInfo(pkWsTaskId);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="终止")
    @ApiOperation(value = "终止", notes = "终止")
    @PostMapping("/workTask/workSheetTerminated")
    public PlatformResult workSheetTerminated(@RequestBody WsWsTaskInputVo wsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetTerminated(wsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="退回")
    @ApiOperation(value = "退回", notes = "退回")
    @PostMapping("/workSheet/workSheetBack")
    public PlatformResult workSheetBack(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetBack(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }


    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="处理人追回")
    @ApiOperation(value = "处理人追回", notes = "处理人追回")
    @PostMapping("/workSheet/workSheetToRecover")
    public PlatformResult workSheetToRecover(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetToRecover(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="终止撤回")
    @ApiOperation(value = "终止撤回", notes = "终止撤回")
    @PostMapping("/workSheet/workSheetToRecoverEnd")
    public PlatformResult workSheetToRecoverEnd(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetToRecoverEnd(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="派单")
    @ApiOperation(value = "派单", notes = "派单")
    @PostMapping("/workSheet/workSheetDispatch")
    public PlatformResult workSheetDispatch(@RequestBody @Validated WsWsTaskInputVo wsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetDispatch(wsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="转发")
    @ApiOperation(value = "转发", notes = "转发")
    @PostMapping("/workSheet/workSheetResend")
    public PlatformResult workSheetResend(@RequestBody @Validated WsWsTaskInputVo wsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetResend(wsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="接单")
    @ApiOperation(value = "接单", notes = "接单")
    @PostMapping("/workSheet/workSheetAccept")
    public PlatformResult workSheetAccept(@RequestBody @Validated WsWsTaskInputVo wsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetAccept(wsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="认领")
    @ApiOperation(value = "认领", notes = "认领")
    @PostMapping("/workSheet/toClaim")
    public PlatformResult toClaim(@RequestBody @Validated WsWsTaskInputVo wsTaskInputVo){
        try {
        	return wsSheetTaskService.toClaim(wsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="协助")
    @ApiOperation(value = "协助", notes = "协助")
    @PostMapping("/workSheet/workSheetAssist")
    public PlatformResult workSheetAssist(@RequestBody @Validated WsWsSheetAssistInputVo wsSheetAssistInputVo){
        try {
        	return wsSheetTaskService.addAssistTask(wsSheetAssistInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="更新进度")
    @ApiOperation(value = "更新进度", notes = "更新进度")
    @PostMapping("/workSheet/workSheetUpdateProgress")
    public PlatformResult workSheetUpdateProgress(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetUpdateProgress(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="处理完成")
    @ApiOperation(value = "处理完成", notes = "处理完成")
    @PostMapping("/workSheet/workSheetProcessingComplete")
    public PlatformResult workSheetProcessingComplete(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetProcessingComplete(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="验收")
    @ApiOperation(value = "验收", notes = "验收")
    @PostMapping("/workSheet/workSheetAcceptance")
    public PlatformResult workSheetAcceptance(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetAcceptance(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="评价")
    @ApiOperation(value = "评价", notes = "评价")
    @PostMapping("/workSheet/workSheetToEvaluate")
    public PlatformResult workSheetToEvaluate(@RequestBody @Validated WsWsEvaluateInputVo wsEvaluateInputVo){
        try {
        	return wsEvaluationService.workSheetToEvaluate(wsEvaluateInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="暂停")
    @ApiOperation(value = "暂停", notes = "暂停")
    @PostMapping("/workSheet/workSheetHasStopped")
    public PlatformResult workSheetHasStopped(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetHasStopped(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @NoRepeatSubmit(lockTime = 2)
    @ControllerLog(description="开启")
    @ApiOperation(value = "开启", notes = "开启")
    @PostMapping("/workSheet/workSheetHadRecovered")
    public PlatformResult workSheetHadRecovered(@RequestBody @Validated WsWsTaskInputVo wsWsTaskInputVo){
        try {
        	return wsSheetTaskService.workSheetHadRecovered(wsWsTaskInputVo);
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @ControllerLog(description="工时列表")
    @ApiOperation(value = "工时列表", notes = "工时列表")
    @GetMapping("/workSheet/selectAllTaskWorkHoursList/{workNumber}")
    public PlatformResult selectAllTaskWorkHoursList(@PathVariable @ApiParam(value = "工单编号") String workNumber){
        try {
        	return PlatformResult.success(wsSheetTaskService.selectAllTaskWorkHoursList(workNumber));
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }

    @ControllerLog(description="提交知识库所需解决方案信息")
    @ApiOperation(value = "提交知识库所需解决方案信息", notes = "提交知识库所需解决方案信息")
    @GetMapping("/workSheet/selectSubmitKnowledgeBaseInfo/{workNumber}")
    public PlatformResult selectSubmitKnowledgeBaseInfo(@PathVariable @ApiParam(value = "工单编号") String workNumber){
        try {
        	return PlatformResult.success(wsSheetTaskService.selectSubmitKnowledgeBaseInfo(workNumber));
	   	}catch(Exception e) {
	   		e.printStackTrace();
	   		return PlatformResult.failure(e.getMessage());
	   	}
    }




}
