package cn.trasen.ams.common.interceptor;

/**
 * SQL词法分析器 - 更准确地解析SQL token
 * 解决原版本无法正确处理字符串、注释中括号的问题
 */
public class SqlTokenizer {
    private final String sql;
    private int position;
    private final int length;

    public SqlTokenizer(String sql) {
        this.sql = sql;
        this.position = 0;
        this.length = sql.length();
    }

    public boolean hasNext() {
        skipWhitespaceAndComments();
        return position < length;
    }

    public String next() {
        skipWhitespaceAndComments();
        if (position >= length) {
            return null;
        }

        // 处理字符串字面量
        if (sql.charAt(position) == '\'' || sql.charAt(position) == '"') {
            return readString();
        }

        // 处理标识符或关键字
        if (Character.isLetter(sql.charAt(position)) || sql.charAt(position) == '_' || sql.charAt(position) == '`') {
            return readIdentifier();
        }

        // 处理操作符
        return readOperator();
    }

    private void skipWhitespaceAndComments() {
        while (position < length) {
            char c = sql.charAt(position);
            
            // 跳过空白字符
            if (Character.isWhitespace(c)) {
                position++;
                continue;
            }
            
            // 跳过单行注释 --
            if (c == '-' && position + 1 < length && sql.charAt(position + 1) == '-') {
                skipToEndOfLine();
                continue;
            }
            
            // 跳过多行注释 /* */
            if (c == '/' && position + 1 < length && sql.charAt(position + 1) == '*') {
                skipMultiLineComment();
                continue;
            }
            
            break;
        }
    }

    private void skipToEndOfLine() {
        while (position < length && sql.charAt(position) != '\n') {
            position++;
        }
        if (position < length) position++; // 跳过换行符
    }

    private void skipMultiLineComment() {
        position += 2; // 跳过 /*
        while (position + 1 < length) {
            if (sql.charAt(position) == '*' && sql.charAt(position + 1) == '/') {
                position += 2;
                break;
            }
            position++;
        }
    }

    private String readString() {
        char quote = sql.charAt(position);
        int start = position++;
        
        while (position < length) {
            if (sql.charAt(position) == quote) {
                position++;
                break;
            } else if (sql.charAt(position) == '\\') {
                position += 2; // 跳过转义字符
            } else {
                position++;
            }
        }
        
        return sql.substring(start, position);
    }

    private String readIdentifier() {
        int start = position;
        
        if (sql.charAt(position) == '`') {
            position++;
            while (position < length && sql.charAt(position) != '`') {
                position++;
            }
            if (position < length) position++; // 跳过结束的`
        } else {
            while (position < length && 
                   (Character.isLetterOrDigit(sql.charAt(position)) || 
                    sql.charAt(position) == '_' || 
                    sql.charAt(position) == '.')) {
                position++;
            }
        }
        
        return sql.substring(start, position);
    }

    private String readOperator() {
        int start = position;
        char c = sql.charAt(position++);
        
        // 处理多字符操作符
        if (position < length) {
            char next = sql.charAt(position);
            if ((c == '<' && (next == '=' || next == '>')) ||
                (c == '>' && next == '=') ||
                (c == '!' && next == '=')) {
                position++;
            }
        }
        
        return sql.substring(start, position);
    }

    /**
     * 获取当前位置
     */
    public int getPosition() {
        return position;
    }

    /**
     * 重置到指定位置
     */
    public void setPosition(int position) {
        this.position = Math.max(0, Math.min(position, length));
    }
}
