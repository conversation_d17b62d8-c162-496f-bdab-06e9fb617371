package cn.trasen.ams.common.interceptor;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * MySQL SQL拦截器 - 自动添加机构代码条件
 * 自动为SELECT语句添加sso_org_code条件，实现数据隔离
 *
 * <AUTHOR>
 * @date 2025/7/15 10:26
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class SsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(SsoOrgCodeInterceptor.class);

    // 正则表达式模式
    private static final Pattern SELECT_PATTERN = Pattern.compile(
            "(?i)\\bFROM\\b\\s+([\\w\\.]+)(?:\\s+AS\\s+([\\w]+)|\\s+([\\w]+))?",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern ORG_CODE_PATTERN = Pattern.compile(
            "\\b(\\w+\\.)?`?sso_org_code`?\\s*[=<>!]|\\b(\\w+\\.)?`?sso_org_code`?\\s+LIKE|\\b(\\w+\\.)?`?sso_org_code`?\\s+IS\\s+(?:NOT\\s+)?NULL",
            Pattern.CASE_INSENSITIVE
    );

    private static final Pattern WHERE_PATTERN = Pattern.compile("\\bWHERE\\b", Pattern.CASE_INSENSITIVE);

    // SQL关键字
    private static final String[] SQL_KEYWORDS = {
            " WHERE ", " GROUP BY ", " ORDER BY ", " HAVING ", " LIMIT ", " UNION "
    };

    // 需要排除的表
    private static final Set<String> EXCLUDE_TABLES = new HashSet<>(Arrays.asList("d_category22"));

    // 需要添加sso_org_code条件的表前缀
    private static final Set<String> INCLUDE_TABLE_PREFIXES = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        if (enableLogging) {
            log.info("=== MySQL拦截器开始执行 ===");
        }

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            String modifiedSql = processSql(originalSql);

            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.info("=== MySQL拦截器执行完成，耗时: {}ms ===", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("MySQL拦截器执行异常", e);
            throw e;
        }
    }

    /**
     * 处理SQL语句
     */
    private String processSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || user.getCorpcode() == null || user.getCorpcode().trim().isEmpty()) {
                if (enableLogging) {
                    log.warn("无法获取用户信息或机构代码为空");
                }
                return sql;
            }

            SqlAnalysisResult analysis = analyzeSelectSql(sql);
            if (analysis == null || !shouldAddOrgCodeCondition(analysis.getMainTable())) {
                return sql;
            }

            if (hasOrgCodeInWhereClause(sql)) {
                if (enableLogging) {
                    log.debug("WHERE子句中已包含sso_org_code条件，跳过修改");
                }
                return sql;
            }

            String modifiedSql = addOrgCodeCondition(sql, analysis, user.getCorpcode());

            if (enableLogging && !sql.equals(modifiedSql)) {
                log.info("SQL修改完成 - 主表: {}, 机构代码: {}", analysis.getMainTable(), user.getCorpcode());
                log.debug("修改后SQL: {}", modifiedSql);
            }

            return modifiedSql;

        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            return sql;
        }
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();

            // 尝试修改sql字段
            try {
                java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
                sqlField.setAccessible(true);
                sqlField.set(boundSql, modifiedSql);
                log.info("SQL修改成功");
                return;
            } catch (Exception e) {
                log.debug("修改sql字段失败，尝试sqlSource字段");
            }

            // 尝试修改sqlSource字段
            try {
                java.lang.reflect.Field sqlSourceField = boundSql.getClass().getDeclaredField("sqlSource");
                sqlSourceField.setAccessible(true);
                sqlSourceField.set(boundSql, modifiedSql);
                log.info("通过sqlSource字段修改成功");
                return;
            } catch (Exception e) {
                log.warn("修改sqlSource字段失败: {}", e.getMessage());
            }

            log.error("所有SQL修改方法都失败了");

        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
        }
    }

    /**
     * SQL分析结果类
     */
    private static class SqlAnalysisResult {
        private final String mainTable;
        private final String tableAlias;

        public SqlAnalysisResult(String mainTable, String tableAlias) {
            this.mainTable = mainTable;
            this.tableAlias = tableAlias;
        }

        public String getMainTable() {
            return mainTable;
        }

        public String getTableAlias() {
            return tableAlias;
        }
    }

    /**
     * 分析SELECT语句
     */
    private SqlAnalysisResult analyzeSelectSql(String sql) {
        if (!sql.toUpperCase().trim().startsWith("SELECT")) {
            return null;
        }

        try {
            int fromIndex = findMainFromClause(sql);
            if (fromIndex == -1) {
                return null;
            }

            String fromClause = sql.substring(fromIndex + 5).trim();
            String[] parts = fromClause.split("\\s+", 3);

            if (parts.length == 0) {
                return null;
            }

            String mainTable = parts[0];
            String tableAlias = null;

            // 检查别名
            if (parts.length >= 2) {
                String secondPart = parts[1];
                if ("AS".equalsIgnoreCase(secondPart) && parts.length >= 3) {
                    tableAlias = parts[2];
                } else if (!isMySqlKeyword(secondPart)) {
                    tableAlias = secondPart;
                }
            }

            if (enableLogging) {
                log.debug("SELECT语句分析 - 主表: {}, 别名: {}", mainTable, tableAlias);
            }

            return new SqlAnalysisResult(mainTable, tableAlias);

        } catch (Exception e) {
            log.warn("解析SELECT语句失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 找到主FROM子句位置
     */
    private int findMainFromClause(String sql) {
        String upperSql = sql.toUpperCase();
        int currentIndex = 0;
        int lastFromIndex = -1;

        while (true) {
            int fromIndex = upperSql.indexOf(" FROM ", currentIndex);
            if (fromIndex == -1) {
                fromIndex = upperSql.indexOf("FROM ", currentIndex);
            }

            if (fromIndex == -1) {
                break;
            }

            if (!isInSubquery(sql, fromIndex)) {
                lastFromIndex = fromIndex;
            }

            currentIndex = fromIndex + 5;
        }

        return lastFromIndex;
    }

    /**
     * 判断是否在子查询中
     */
    private boolean isInSubquery(String sql, int position) {
        int leftParens = 0;
        int rightParens = 0;

        for (int i = 0; i < position; i++) {
            char c = sql.charAt(i);
            if (c == '(') {
                leftParens++;
            } else if (c == ')') {
                rightParens++;
            }
        }

        return leftParens > rightParens;
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }

        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 检查WHERE子句中是否已包含sso_org_code条件
     */
    private boolean hasOrgCodeInWhereClause(String sql) {
        String upperSql = sql.toUpperCase();
        int currentIndex = 0;

        while (true) {
            int whereIndex = upperSql.indexOf(" WHERE ", currentIndex);
            if (whereIndex == -1) {
                break;
            }

            String whereClause = extractWhereClause(sql, whereIndex);

            if (enableLogging) {
                log.debug("检查WHERE子句: {}", whereClause);
            }

            if (ORG_CODE_PATTERN.matcher(whereClause).find()) {
                if (enableLogging) {
                    log.debug("找到sso_org_code条件，跳过SQL修改");
                }
                return true;
            }

            currentIndex = whereIndex + 7;
        }

        return false;
    }

    /**
     * 提取WHERE子句内容
     */
    private String extractWhereClause(String sql, int whereIndex) {
        String afterWhere = sql.substring(whereIndex + 7);
        String upperAfterWhere = afterWhere.toUpperCase();

        int minIndex = Integer.MAX_VALUE;
        for (String keyword : SQL_KEYWORDS) {
            int index = upperAfterWhere.indexOf(keyword);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }

        return minIndex == Integer.MAX_VALUE ? afterWhere : afterWhere.substring(0, minIndex);
    }

    /**
     * 添加机构代码条件
     */
    private String addOrgCodeCondition(String sql, SqlAnalysisResult analysis, String orgCode) {
        String orgCodeField = buildOrgCodeField(analysis);
        String condition = orgCodeField + " = '" + orgCode + "'";

        int whereIndex = findMainWhereClause(sql);

        if (whereIndex == -1) {
            return addWhereClause(sql, condition);
        } else {
            return addAndCondition(sql, whereIndex, condition);
        }
    }

    /**
     * 构建机构代码字段名
     */
    private String buildOrgCodeField(SqlAnalysisResult analysis) {
        String pureTableName = getPureTableName(analysis.getMainTable());

        if (analysis.getTableAlias() != null && !analysis.getTableAlias().equals(pureTableName)) {
            return analysis.getTableAlias() + ".sso_org_code";
        }

        return pureTableName + ".sso_org_code";
    }

    /**
     * 查找主查询的WHERE子句位置
     */
    private int findMainWhereClause(String sql) {
        Matcher matcher = WHERE_PATTERN.matcher(sql);

        while (matcher.find()) {
            int whereIndex = matcher.start();
            if (!isInSubquery(sql, whereIndex)) {
                return whereIndex;
            }
        }

        return -1;
    }

    /**
     * 添加WHERE子句
     */
    private String addWhereClause(String sql, String condition) {
        String upperSql = sql.toUpperCase();
        int fromIndex = upperSql.indexOf(" FROM ");
        if (fromIndex == -1) {
            fromIndex = upperSql.indexOf("FROM ");
        }

        if (fromIndex == -1) {
            return sql;
        }

        int fromEndIndex = findFromClauseEnd(sql, fromIndex);
        String beforeFromEnd = sql.substring(0, fromEndIndex);
        String afterFromEnd = sql.substring(fromEndIndex);

        return beforeFromEnd + " WHERE " + condition + afterFromEnd;
    }

    /**
     * 查找FROM子句结束位置
     */
    private int findFromClauseEnd(String sql, int fromIndex) {
        String afterFrom = sql.substring(fromIndex);
        String upperAfterFrom = afterFrom.toUpperCase();

        int minIndex = Integer.MAX_VALUE;
        for (String keyword : SQL_KEYWORDS) {
            int index = upperAfterFrom.indexOf(keyword);
            if (index != -1 && index < minIndex) {
                minIndex = index;
            }
        }

        return minIndex == Integer.MAX_VALUE ? sql.length() : fromIndex + minIndex;
    }

    /**
     * 添加AND条件
     */
    private String addAndCondition(String sql, int whereIndex, String condition) {
        String beforeWhere = sql.substring(0, whereIndex);
        String afterWhere = sql.substring(whereIndex + 5); // 跳过"WHERE"

        // 跳过空白字符
        int whitespaceIndex = 0;
        while (whitespaceIndex < afterWhere.length() && Character.isWhitespace(afterWhere.charAt(whitespaceIndex))) {
            whitespaceIndex++;
        }

        String actualAfterWhere = afterWhere.substring(whitespaceIndex);

        // 检查是否以AND开头
        if (actualAfterWhere.toUpperCase().startsWith("AND ")) {
            return beforeWhere + "WHERE " + condition + " AND " + actualAfterWhere.substring(4);
        }

        return beforeWhere + "WHERE " + condition + " AND " + actualAfterWhere;
    }

    /**
     * 获取纯表名
     */
    private static String getPureTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return tableName;
        }

        String pureTableName = tableName;

        if (tableName.contains(".")) {
            pureTableName = tableName.split("\\.")[1];
        }

        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 判断是否为MySQL关键字
     */
    private boolean isMySqlKeyword(String word) {
        if (word == null || word.trim().isEmpty()) {
            return false;
        }

        // 简化的MySQL关键字检查
        String[] keywords = {"SELECT", "FROM", "WHERE", "AND", "OR", "GROUP", "BY", "ORDER", "HAVING", "LIMIT", "AS"};
        return Arrays.stream(keywords).anyMatch(keyword -> keyword.equalsIgnoreCase(word));
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }

            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }

            log.info("多机构拦截器配置 - 启用SQL修改: {}, 启用日志: {}", enableSqlModification, enableLogging);
        }
    }
}