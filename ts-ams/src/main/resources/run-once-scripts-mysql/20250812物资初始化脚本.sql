INSERT INTO ts_thps.thps_role (`ID`, `SYS_CODE`, `SYS_NAME`, `ROLE_CODE`, `ROLE_NAME`, `ENABLED`, `SYS_FLAG`, `REMARK`, `CREATEUSER`, `CREATETIME`, `UPDATEUSER`, `UPDATETIME`, `group_id`, `IS_DEFAULT`, `seq_no`, `ORG_ID`, `ORG_NAME`, `CATEGORY_CODE`, `CATEGORY_NAME`) VALUES ('1603ECD24826464C8368B5C8F1E39D70', '', NULL, 'ZCSB_GLY', '资产设备管理员', '1', '0', 'AMS_资产设备最高管理员', 'admin', '2025-03-08 07:18:33', 'admin', '2025-08-15 01:51:07', NULL, 'N', '0', '432816904746987111', '北海市第二人民医院', 'ROLE_CAT_DEFAULT', '默认分类') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `SYS_CODE` = VALUES(`SYS_CODE`), `SYS_NAME` = VALUES(`SYS_NAME`), `ROLE_CODE` = VALUES(`ROLE_CODE`), `ROLE_NAME` = VALUES(`ROLE_NAME`), `ENABLED` = VALUES(`ENABLED`), `SYS_FLAG` = VALUES(`SYS_FLAG`), `REMARK` = VALUES(`REMARK`), `CREATEUSER` = VALUES(`CREATEUSER`), `CREATETIME` = VALUES(`CREATETIME`), `UPDATEUSER` = VALUES(`UPDATEUSER`), `UPDATETIME` = VALUES(`UPDATETIME`), `group_id` = VALUES(`group_id`), `IS_DEFAULT` = VALUES(`IS_DEFAULT`), `seq_no` = VALUES(`seq_no`), `ORG_ID` = VALUES(`ORG_ID`), `ORG_NAME` = VALUES(`ORG_NAME`), `CATEGORY_CODE` = VALUES(`CATEGORY_CODE`), `CATEGORY_NAME` = VALUES(`CATEGORY_NAME`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('06D88B79288E467D84BA9C50E9CF13BA', '2', '3D70440069F743C6BC755E395FF5A358', '出入库管理', '#', '1', '1', '1', '', 'admin', '2025-07-28 02:10:33', 'admin', '2025-08-12 09:57:04', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-出入库管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('19B6C50652F44CB6B5F5715684E5B0F9', '3', '6351DB4AB5904ADDA1DEBC7614DE300A', '库存查询', '/ts-web-equipment/material/warehouse-management/inventory-query', '1', '1', '1', '', 'admin', '2025-08-13 02:38:19', 'admin', '2025-08-13 02:38:19', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-库存查询', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('1E86D401741A47B5BBA2964F5589B643', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '物资字典', '/ts-web-equipment/material/base-setting/material-dictionary', '6', '1', '1', '', 'admin', '2025-07-23 01:26:29', 'admin', '2025-08-12 09:58:53', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('3D70440069F743C6BC755E395FF5A358', '1', 'C2F079CE73B344D8A721E0BB60D513FE', '物资管理', '#', '11', '1', '1', '', 'admin', '2025-07-21 07:11:09', 'admin', '2025-08-12 09:56:48', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('41596C54453646E8BC86E8AF3EB76911', '3', '06D88B79288E467D84BA9C50E9CF13BA', '退货管理', '/ts-web-equipment/material/inventory-management/material-return-goods-manage', '3', '1', '1', '', 'admin', '2025-07-28 02:13:01', 'admin', '2025-08-12 09:57:58', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-退货管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('43F7D5A031254035A903C0737D48766E', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '方式代码维护', '/ts-web-equipment/material/base-setting/method-code-maintenance', '7', '1', '1', '', 'admin', '2025-07-25 07:26:10', 'admin', '2025-08-12 09:59:02', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-方式代码维护', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('81CA71673F224C63A3A732605DB65C86', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '生产厂家', '/ts-web-equipment/material/base-setting/material-manufacturer', '8', '1', '1', '', 'admin', '2025-07-26 01:32:57', 'admin', '2025-08-12 09:59:09', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-生产厂家', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('9A638FED20C04EB1BA4A0FDCFACB69A9', '3', '06D88B79288E467D84BA9C50E9CF13BA', '入库管理', '/ts-web-equipment/material/inventory-management/material-store-manage', '1', '1', '1', '', 'admin', '2025-07-28 02:11:59', 'admin', '2025-08-12 09:57:34', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-入库管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('A07D528030BF472AB948A72448C5CAC5', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '库房对应物资分类', '/ts-web-equipment/material/base-setting/warehouse-be-material-classsification', '4', '1', '1', '', 'admin', '2025-07-23 08:55:59', 'admin', '2025-08-12 09:58:38', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('A4356CC2F64B4E679B7D23B728E43C4C', '3', '6351DB4AB5904ADDA1DEBC7614DE300A', '月结', '/ts-web-equipment/material/warehouse-management/monthly-settlement', '2', '1', '1', '', 'admin', '2025-08-13 02:39:49', 'admin', '2025-08-13 02:39:49', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-月结', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('AD3DB660E8F84E48A43F2F289459EA10', '2', '3D70440069F743C6BC755E395FF5A358', '基础设置', '#', '999', '1', '1', '', 'admin', '2025-07-21 07:12:51', 'admin', '2025-08-12 09:57:13', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-基础设置', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('B33559B5F23942808A347BFA2145A74F', '3', '06D88B79288E467D84BA9C50E9CF13BA', '科室申领', '/ts-web-equipment/material/inventory-management/material-dept-application', '5', '1', '1', '', 'admin', '2025-07-28 02:14:46', 'admin', '2025-08-12 09:58:10', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-退货管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('B8CD48BB30CB445D921305947EFC61BC', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '物资库房', '/ts-web-equipment/material/base-setting/material-warehouse', '1', '1', '1', '', 'admin', '2025-07-21 07:30:11', 'admin', '2025-08-12 09:58:20', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('C161EB1A5DEF49E5871FCBB69CE14ADB', '3', '06D88B79288E467D84BA9C50E9CF13BA', '退库管理', '/ts-web-equipment/material/inventory-management/material-return-store-manage', '4', '1', '1', '', 'admin', '2025-07-28 02:13:41', 'admin', '2025-08-12 09:58:04', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-退货管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('C3610A68A3654B90BB2533FDB8AE3C36', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '供应商', '/ts-web-equipment/material/base-setting/material-supplier', '9', '1', '1', '', 'admin', '2025-07-26 01:33:23', 'admin', '2025-08-12 09:59:16', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-供应商', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('E9E9FD9E73D0489D90F0E91453B5FEB0', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '物资分类', '/ts-web-equipment/material/base-setting/material-classification', '2', '1', '1', '', 'admin', '2025-07-22 06:38:58', 'admin', '2025-08-12 09:58:26', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('EBC82104E68646BB805ADDD3E5BDE507', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '库房对应用户', '/ts-web-equipment/material/base-setting/warehouse-be-person', '5', '1', '1', '', 'admin', '2025-07-23 08:59:51', 'admin', '2025-08-12 09:58:48', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('F1D49B1ADA794C3F9C1BBAC0196BB92F', '3', 'AD3DB660E8F84E48A43F2F289459EA10', '会计期间', '/ts-web-equipment/material/base-setting/accounting-period', '3', '1', '1', '', 'admin', '2025-07-23 06:54:16', 'admin', '2025-08-12 09:58:33', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-物资库房', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_thps.thps_sysmenu (`id`, `menulevel`, `pid`, `menuname`, `alink`, `sort`, `status`, `menutype`, `icon`, `appuser`, `apptime`, `updateuser`, `updatetime`, `syscode`, `packagename`, `is_expand`, `SHOW`, `ROUTE`, `DESCRIPTION`, `SHOW_MODE`, `SHOW_TOOLBAR`, `PUBLISH_FROM_DESIGN`, `MENU_OPEN_WAY`) VALUES ('FFEF67F0513E42CE9F44398FF818863F', '3', '06D88B79288E467D84BA9C50E9CF13BA', '出库管理', '/ts-web-equipment/material/inventory-management/material-outbound-manage', '2', '1', '1', '', 'admin', '2025-07-28 02:12:35', 'admin', '2025-08-12 09:57:50', 'ts-platform', '', 'Y', 'Y', NULL, 'AMS-出库管理', '0', '0', 'N', '01') ON DUPLICATE KEY UPDATE `id` = VALUES(`id`), `menulevel` = VALUES(`menulevel`), `pid` = VALUES(`pid`), `menuname` = VALUES(`menuname`), `alink` = VALUES(`alink`), `sort` = VALUES(`sort`), `status` = VALUES(`status`), `menutype` = VALUES(`menutype`), `icon` = VALUES(`icon`), `appuser` = VALUES(`appuser`), `apptime` = VALUES(`apptime`), `updateuser` = VALUES(`updateuser`), `updatetime` = VALUES(`updatetime`), `syscode` = VALUES(`syscode`), `packagename` = VALUES(`packagename`), `is_expand` = VALUES(`is_expand`), `SHOW` = VALUES(`SHOW`), `ROUTE` = VALUES(`ROUTE`), `DESCRIPTION` = VALUES(`DESCRIPTION`), `SHOW_MODE` = VALUES(`SHOW_MODE`), `SHOW_TOOLBAR` = VALUES(`SHOW_TOOLBAR`), `PUBLISH_FROM_DESIGN` = VALUES(`PUBLISH_FROM_DESIGN`), `MENU_OPEN_WAY` = VALUES(`MENU_OPEN_WAY`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('928933102763761664', 'AMS_WAREHOUSE_LEVEL', '仓库等级', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-21 00:00:00', 'admin', 'admin', '2025-08-06 09:52:45', 'N', '', '', '432816904746987520', '北海市第二人民医院', 'bhsdermyy', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929665446063759360', 'AMS_M_SKU_UNIT', '物资字典单位', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:24:04', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929665815036682240', 'AMS_M_SKU_MINI_UNIT', '物资字典最小单位', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:23:52', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929666377358630912', 'AMS_M_SKU_LEVEL', '物资字典级别', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:23:31', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929667130533994496', 'AMS_M_ACCOUNT_SUBJECT', '会计科目', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:22:34', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929668015188205568', 'AMS_M_JCLX', '集采类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:22:20', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929668363898445824', 'AMS_M_HIGH_VALUE_CATE', '高值类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:21:17', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929668628080877568', 'AMS_M_MI_TYPE', '医保分类', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:20:52', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929669003953430528', 'AMS_M_SKU_ORIGIN', '物资字典来源', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:20:29', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929669341729120256', 'AMS_M_SKU_USE_TYPE', '物资使用类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:20:13', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('929669629944913920', 'AMS_M_PURCHASE_TYPE', ' 物资采购类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-23 00:00:00', 'admin', 'admin', '2025-08-06 09:19:52', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930289808773079040', 'AMS_DATA_MODIFY_TYPE', '数据修改类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 00:00:00', 'admin', 'admin', '2025-08-06 09:18:53', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930352089942843392', 'AMS_RECEIPT_METHOD', '方式码对应的单据类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 00:00:00', 'admin', 'admin', '2025-08-06 09:18:38', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930353272686239744', 'AMS_METHOD_CODE_TYPE', '方式码类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 00:00:00', 'admin', 'admin', '2025-08-06 09:17:35', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930380796380176384', 'AMS_ENTERPRISE_TYPE', '企业性质', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 00:00:00', 'admin', 'admin', '2025-08-06 09:17:14', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930416965050556416', 'AMS_CERT_TYPE', '证书类型', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 00:00:00', 'admin', 'admin', '2025-08-06 09:52:17', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('930417863717937152', 'AMS_CERT_STATUS', '证书状态', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-25 19:08:38', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('932457907160932352', 'AMS_M_ORD_STAT', '物资单据状态', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-31 00:00:00', 'admin', 'admin', '2025-08-06 09:10:34', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('932458740623663104', 'AMS_M_ORD_RETURN_STAT', '物资单据退回状态', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-07-31 00:00:00', 'admin', 'admin', '2025-08-06 09:10:22', 'N', '', '', '432816904746987520', '北海市第二人民医院', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('938974620329201664', 'AMS_M_BASE_SETTING', '物资基础设置', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-08-18 09:49:55', NULL, NULL, NULL, 'N', NULL, NULL, '445897456614420480', '信息管理部', 'bhsdermyy', 'admin') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('938974679359836160', 'AMS_D_BASE_SETTING', '资产设备基础设置', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-08-18 09:50:09', NULL, NULL, NULL, 'N', NULL, NULL, '445897456614420480', '信息管理部', 'bhsdermyy', 'admin') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('type_33752954', 'AMS_M_BASE_SETTING', '物资基础设置', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-08-05 00:00:00', 'admin', 'admin', '2025-08-06 09:10:00', 'N', '', '', '', '', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_type (`ID`, `TYPE_CODE`, `TYPE_NAME`, `REMARK`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `sso_org_code`, `sso_org_name`) VALUES ('type_716035974', 'AMS_D_BASE_SETTING', '资产设备基础设置', '从Redis恢复', 'AMS', 'admin', 'admin', '2025-08-05 00:00:00', 'admin', 'admin', '2025-08-06 09:09:47', 'N', '', '', '', '', '', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `TYPE_CODE` = VALUES(`TYPE_CODE`), `TYPE_NAME` = VALUES(`TYPE_NAME`), `REMARK` = VALUES(`REMARK`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('928939780087341056', '928933102763761664', '1', '一级库房', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-21 17:15:02', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', 'bhsdermyy', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('928939825805254656', '928933102763761664', '2', '二级库房', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-21 17:15:13', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', 'bhsdermyy', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('928939904045801472', '928933102763761664', '3', '三级库房', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-21 17:15:32', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', 'bhsdermyy', '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929665496160526336', '929665446063759360', '1', '个', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:18:47', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929665551231737856', '929665446063759360', '2', '台', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:19:00', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929665863455727616', '929665815036682240', '1', '个', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:20:14', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929666514235547648', '929666377358630912', '1', 'Ⅰ级', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:22:49', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929666567465459712', '929666377358630912', '2', 'Ⅱ级', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:23:02', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929666625611096064', '929666377358630912', '3', 'Ⅲ级', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:23:16', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929667257977921536', '929667130533994496', '1', '科目一', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:25:47', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929667300659159040', '929667130533994496', '2', '科目二', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:25:57', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929667352467202048', '929667130533994496', '3', '科目三', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:26:09', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929667394817089536', '929667130533994496', '4', '科目四', '', '', '4', 'AMS', 'admin', 'admin', '2025-07-23 17:26:19', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929668072197185536', '929668015188205568', '1', '类型1', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:29:01', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929668425634406400', '929668363898445824', '1', '类型一', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:30:25', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929668688122339328', '929668628080877568', '1', '甲类', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:31:28', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929668754899853312', '929668628080877568', '2', '乙类', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:31:44', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929668824013594624', '929668628080877568', '3', '自费', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:32:00', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669063189585920', '929669003953430528', '1', '国产', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:32:57', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669102855118848', '929669003953430528', '2', '合资', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:33:07', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669153400676352', '929669003953430528', '3', '进口', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:33:19', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669401166602240', '929669341729120256', '1', '常规', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:34:18', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669433680846848', '929669341729120256', '2', '植入', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:34:25', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669480912904192', '929669341729120256', '3', '接触', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:34:37', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669711599624192', '929669629944913920', '1', '招标', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-23 17:35:32', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669753324560384', '929669629944913920', '2', '合同', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-23 17:35:42', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669801227706368', '929669629944913920', '3', '协议', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-23 17:35:53', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('929669856835788800', '929669629944913920', '4', '电商平台', '', '', '4', 'AMS', 'admin', 'admin', '2025-07-23 17:36:06', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930289906663940096', '930289808773079040', 'c', '新增', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 10:39:58', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', 'c', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930289972682285056', '930289808773079040', 'u', '修改', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-25 10:40:13', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', 'u', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930290023991205888', '930289808773079040', 'd', '删除', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-25 10:40:26', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', 'd', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352204384428032', '930352089942843392', '1', '入库', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 14:47:31', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352241428520960', '930352089942843392', '2', '出库', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-25 14:47:39', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352297451839488', '930352089942843392', '3', '申领', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-25 14:47:53', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352367777734656', '930352089942843392', '4', '退货', '', '', '4', 'AMS', 'admin', 'admin', '2025-07-25 14:48:10', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352412921028608', '930352089942843392', '5', '退库', '', '', '5', 'AMS', 'admin', 'admin', '2025-07-25 14:48:20', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '5', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352513919868928', '930352089942843392', '6', '保损', '', '', '6', 'AMS', 'admin', 'admin', '2025-07-25 14:48:44', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '6', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930352565153292288', '930352089942843392', '7', '盘点', '', '', '7', 'AMS', 'admin', 'admin', '2025-07-25 14:48:57', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '7', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930353359994871808', '930353272686239744', '1', '增', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 14:52:06', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930353392043548672', '930353272686239744', '2', '减', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-25 14:52:14', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930353420019556352', '930353272686239744', '3', '其他', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-25 14:52:20', 'admin', 'admin', '2025-08-05 17:48:47', 'Y', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930380934829957120', '930380796380176384', '1', '国有企业', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 16:41:41', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930380978660433920', '930380796380176384', '2', '集体企业', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-25 16:41:51', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930381019454234624', '930380796380176384', '3', '股份合作企业', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-25 16:42:01', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930381073141325824', '930380796380176384', '4', '联营企业', '', '', '4', 'AMS', 'admin', 'admin', '2025-07-25 16:42:13', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930381124831928320', '930380796380176384', '5', '有限责任公司', '', '', '5', 'AMS', 'admin', 'admin', '2025-07-25 16:42:26', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '5', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930381185531895808', '930380796380176384', '6', '股份有限公司', '', '', '6', 'AMS', 'admin', 'admin', '2025-07-25 16:42:40', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '6', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417165953523712', '930416965050556416', '1', '营业执照', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 19:05:39', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417204734058496', '930416965050556416', '2', '经营许可证', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-25 19:05:48', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417246442217472', '930416965050556416', '3', '产品注册证', '', '', '3', 'AMS', 'admin', 'admin', '2025-07-25 19:05:58', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '3', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417306735337472', '930416965050556416', '4', '卫生许可证', '', '', '4', 'AMS', 'admin', 'admin', '2025-07-25 19:06:12', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '4', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417920022274048', '930417863717937152', '1', '有效', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-25 19:08:38', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('930417971230531584', '930417863717937152', '0', '过期', '', '', '0', 'AMS', 'admin', 'admin', '2025-07-25 19:08:51', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('932458134311854080', '932457907160932352', '1', '已登记', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-31 10:15:43', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('932458247998464000', '932457907160932352', '2', '已审核', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-31 10:16:11', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('932458987026440192', '932458740623663104', '0', '未退货', '', '', '0', 'AMS', 'admin', 'admin', '2025-07-31 10:19:07', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '0', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('932459073471045632', '932458740623663104', '1', '部分退货', '', '', '1', 'AMS', 'admin', 'admin', '2025-07-31 10:19:27', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '1', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('932459113258213376', '932458740623663104', '2', '全部退货', '', '', '2', 'AMS', 'admin', 'admin', '2025-07-31 10:19:37', 'admin', 'admin', '2025-08-05 17:48:47', 'N', '', '', '432816904746987520', '北海市第二人民医院', '2', '0', '1', NULL, '') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('938974620476002304', '938974620329201664', 'm_accounting_period_config', '会计期间配置', NULL, '会计期间配置', '1', 'AMS', 'admin', 'admin', '2025-08-18 09:49:55', NULL, NULL, NULL, 'N', NULL, NULL, '445897456614420480', '信息管理部', '{"isAutoGen":"1","isNatureMonth":"0","startDay":"26"}', NULL, '1', 'bhsdermyy', 'admin') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
INSERT INTO ts_base_oa.comm_dict_item (`ID`, `DIC_TYPE_ID`, `ITEM_CODE`, `ITEM_NAME`, `ITEM_IMG`, `REMARK`, `SORT`, `SYS_CODE`, `CREATE_USER`, `CREATE_USER_NAME`, `CREATE_DATE`, `UPDATE_USER`, `UPDATE_USER_NAME`, `UPDATE_DATE`, `IS_DELETED`, `ORG_CODE`, `HOSP_CODE`, `CREATE_DEPT`, `CREATE_DEPT_NAME`, `ITEM_NAME_VALUE`, `IS_ITEM_IMG`, `is_enable`, `sso_org_code`, `sso_org_name`) VALUES ('938974679393390592', '938974679359836160', 'd_asset_tag_tpl', '资产标签模板配置', NULL, '资产标签模板配置', '1', 'AMS', 'admin', 'admin', '2025-08-18 09:50:09', 'admin', 'admin', '2025-08-18 09:50:15', 'N', NULL, NULL, '445897456614420480', '信息管理部', '2', NULL, '1', 'bhsdermyy', 'admin') ON DUPLICATE KEY UPDATE `ID` = VALUES(`ID`), `DIC_TYPE_ID` = VALUES(`DIC_TYPE_ID`), `ITEM_CODE` = VALUES(`ITEM_CODE`), `ITEM_NAME` = VALUES(`ITEM_NAME`), `ITEM_IMG` = VALUES(`ITEM_IMG`), `REMARK` = VALUES(`REMARK`), `SORT` = VALUES(`SORT`), `SYS_CODE` = VALUES(`SYS_CODE`), `CREATE_USER` = VALUES(`CREATE_USER`), `CREATE_USER_NAME` = VALUES(`CREATE_USER_NAME`), `CREATE_DATE` = VALUES(`CREATE_DATE`), `UPDATE_USER` = VALUES(`UPDATE_USER`), `UPDATE_USER_NAME` = VALUES(`UPDATE_USER_NAME`), `UPDATE_DATE` = VALUES(`UPDATE_DATE`), `IS_DELETED` = VALUES(`IS_DELETED`), `ORG_CODE` = VALUES(`ORG_CODE`), `HOSP_CODE` = VALUES(`HOSP_CODE`), `CREATE_DEPT` = VALUES(`CREATE_DEPT`), `CREATE_DEPT_NAME` = VALUES(`CREATE_DEPT_NAME`), `ITEM_NAME_VALUE` = VALUES(`ITEM_NAME_VALUE`), `IS_ITEM_IMG` = VALUES(`IS_ITEM_IMG`), `is_enable` = VALUES(`is_enable`), `sso_org_code` = VALUES(`sso_org_code`), `sso_org_name` = VALUES(`sso_org_name`);
