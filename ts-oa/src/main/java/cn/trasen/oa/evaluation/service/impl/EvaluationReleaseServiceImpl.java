package cn.trasen.oa.evaluation.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.evaluation.dao.EvaluationReleaseMapper;
import cn.trasen.oa.evaluation.model.EvaluationRelease;
import cn.trasen.oa.evaluation.service.EvaluationReleaseService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName EvaluationReleaseServiceImpl
 * @Description TODO
 * @date 2024��4��10�� ����5:10:57
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class EvaluationReleaseServiceImpl implements EvaluationReleaseService {

	@Autowired
	private EvaluationReleaseMapper mapper;

	@Transactional(readOnly = false)
	@Override
	public Integer save(EvaluationRelease record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(EvaluationRelease record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		EvaluationRelease record = new EvaluationRelease();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public EvaluationRelease selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<EvaluationRelease> getDataSetList(Page page, EvaluationRelease record) {
		Example example = new Example(EvaluationRelease.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<EvaluationRelease> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public void deleteEvaluationReleaseStatus(String masterId) {
		// TODO Auto-generated method stub
		 mapper.deleteEvaluationReleaseStatus(masterId);
	}

	@Override
	public DataSet<EvaluationRelease> getlist(Page page, EvaluationRelease record) {
		// TODO Auto-generated method stub
		//ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		//record.setUserCode(user.getUsercode());
		//根据当前账号机构编码过滤
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<EvaluationRelease> records = mapper.getlist(page, record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public Map<String, Object> selectReleaseCount(EvaluationRelease record) {
		// TODO Auto-generated method stub
		return mapper.selectReleaseCount(record);
	}
}
