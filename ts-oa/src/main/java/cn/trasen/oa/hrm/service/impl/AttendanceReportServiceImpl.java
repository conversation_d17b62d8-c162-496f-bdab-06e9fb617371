package cn.trasen.oa.hrm.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Lists;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.trasen.BootComm.utils.CommTree;
import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.bean.base.OrganizationListRes;
import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.bpm.WfContants;
import cn.trasen.homs.bpm.service.WorkflowInstanceService;
import cn.trasen.homs.bpm.service.WorkflowTaskService;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.bean.ThpsUserResp;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.homs.feign.base.HrmsOrganizationFeignService;
import cn.trasen.homs.feign.message.NoticeService;
import cn.trasen.homs.feign.sso.SystemUserFeignService;
import cn.trasen.oa.hrm.dao.AttendanceReportMapper;
import cn.trasen.oa.hrm.model.AttendanceReport;
import cn.trasen.oa.hrm.po.JdGridTableEntity;
import cn.trasen.oa.hrm.po.StepFrom;
import cn.trasen.oa.hrm.service.AttendanceReportService;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName AttendanceReportServiceImpl
 * @Description TODO
 * @date 2023��2��2�� ����11:32:03
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
@Slf4j
public class AttendanceReportServiceImpl implements AttendanceReportService {

	@Autowired
	private AttendanceReportMapper mapper;
	
	@Autowired
	private DictItemFeignService dictItemFeignService;
	
	@Autowired
	private HrmsOrganizationFeignService hrmsOrganizationFeignService;
	
	@Autowired
	private WorkflowInstanceService workflowInstanceService;
	
//	@Autowired
//	private WorkflowFeignService workflowFeignService;
//	
	@Autowired
	private WorkflowTaskService workflowTaskService;
	
	@Autowired
	private SystemUserFeignService systemUserFeignService;

	@Transactional(readOnly = false)
	@Override
	public void save(Map<String, Object> record) {
		
		StringBuffer sql = new StringBuffer();
		StringBuffer fieldStr = new StringBuffer();
	    fieldStr.append("id,report_dept_code,report_dept_name,status,report_date,create_date,create_user,create_user_name,is_deleted,user_code,name,");
	    
	    PlatformResult<List<DictItemResp>> dictItemByTypeCodeResult = dictItemFeignService.getDictItemByTypeCode("ATTENDANCE_REPORT");
	    List<DictItemResp> dictItemRespList = dictItemByTypeCodeResult.getObject();
		for (DictItemResp dictItemResp : dictItemRespList) {
			fieldStr.append(dictItemResp.getItemCode()).append(",");
		}
			
	    String status = "2";
	    ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
	    
		//封装新增语句
		fieldStr = fieldStr.deleteCharAt(fieldStr.length() - 1);
		sql.append("insert into toa_attendance_report")
		.append(" (").append(fieldStr).append(") values ('")
		.append(String.valueOf(IdWork.id.nextId())).append("',");
		sql.append("'").append((String) record.get("reportDeptCode")).append("',");
		sql.append("'").append((String) record.get("reportDeptName")).append("',");
		sql.append("'").append(status).append("',");
		sql.append("'").append((String) record.get("reportDate")).append("',");
		sql.append("'").append(DateUtil.format(new Date(),DatePattern.NORM_DATETIME_FORMATTER)).append("',");
		sql.append("'").append(currentUserInfo.getUsercode()).append("',");
		sql.append("'").append(currentUserInfo.getUsername()).append("',");
		sql.append("'N',");
		sql.append("'").append((String) record.get("userCode")).append("',");
		sql.append("'").append((String) record.get("name")).append("',");
		
		for (DictItemResp dictItemResp : dictItemRespList) {
			String value = (String) record.get(dictItemResp.getItemCode());
			
			if(StringUtils.isNotBlank(value)){
				sql.append("'").append(value).append("',");
			}else{
				sql.append("'0',");
			}
		}
		
		sql.deleteCharAt(sql.length() - 1);
		sql.append(");");
		
		log.info("插入sql:" + sql.toString());
		
		//插入数据
		 mapper.excuteSql(sql.toString());
	
	}

	@Transactional(readOnly = false)
	@Override
	public void update(Map<String, Object> record) {

		StringBuffer fieldStr = new StringBuffer();
		ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
		fieldStr.append("update toa_attendance_report set update_user='").append(currentUserInfo.getUsercode()).append("',");
		fieldStr.append("update_user_name='").append(currentUserInfo.getUsername()).append("',");
		fieldStr.append("update_date='").append(DateUtil.format(new Date(),DatePattern.NORM_DATETIME_FORMATTER)).append("',");
		fieldStr.append("report_date='").append(record.get("reportDate")).append("',");
		fieldStr.append("name='").append(record.get("name")).append("',");
		fieldStr.append("user_code='").append(record.get("userCode")).append("',");
		//20250217，按爱丽更改添加科室字段
		fieldStr.append("report_dept_code='").append(record.get("reportDeptCode")).append("',");
		fieldStr.append("report_dept_name='").append(record.get("reportDeptName")).append("',");
				
		 PlatformResult<List<DictItemResp>> dictItemByTypeCodeResult = dictItemFeignService.getDictItemByTypeCode("ATTENDANCE_REPORT");
	    List<DictItemResp> dictItemRespList = dictItemByTypeCodeResult.getObject();
		for (DictItemResp dictItemResp : dictItemRespList) {
			
			String value = (String) record.get(dictItemResp.getItemCode());
			if(StringUtils.isBlank(value)){
				value = "";
			}
			//if(StringUtils.isNotBlank(value)){
				fieldStr.append(dictItemResp.getItemCode() + "='").append(value).append("',");
			//}
			
		}
		fieldStr.deleteCharAt(fieldStr.length() - 1);
		
		
		fieldStr.append(" where id='").append(record.get("id")).append("'");
		
		log.info("更新sql:" + fieldStr.toString());
		
		 //数据
		 mapper.excuteSql(fieldStr.toString());
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		AttendanceReport record = new AttendanceReport();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public AttendanceReport selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public List<Map<String,String>> getDataSetList(Page page, AttendanceReport record) {
		
		
		//默认查看自己科室的数据
		if(!"Y".equals(record.getAttendanceManage())){
			if(StringUtils.isBlank(record.getReportDeptCode())){
				record.setReportDeptCode(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			}else{
				record.setReportDeptCode(record.getReportDeptCode());
			}
		}else{
			PlatformResult<List<String>> hrmsOrganizationAndNextList = hrmsOrganizationFeignService.getHrmsOrganizationAndNextList(record.getReportDeptCode());
			if(hrmsOrganizationAndNextList.isSuccess()){
				record.setReportDeptCode(null);
				record.setReportDeptCodeList(hrmsOrganizationAndNextList.getObject());
			}
			record.setStatus("2");
		}
		
		//默认查看本月的数据
		if(StringUtils.isBlank(record.getReportDate())){
			record.setReportDate(DateUtil.format(DateUtil.date(), "yyyy-MM"));
		}
		//根据当前登录账号机构编码过滤查询数据
//		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, String>> dataList = mapper.getDataList(page,record);
		
		for (Map<String, String> map : dataList) {
			String status = map.get("status");
			if("0".equals(status)){
				map.put("status", "未上报");
			}
			if("1".equals(status)){
				map.put("status", "已上报");
			}
			if("2".equals(status)){
				map.put("status", "审批通过");
			}
			if("3".equals(status)){
				map.put("status", "审批不通过");
			}
			if("4".equals(status)){
				map.put("status", "已撤销");
			}
		}
		
		return dataList;
		
	}

	@Override
	@Transactional(readOnly = false)
	public List<JdGridTableEntity> getTableHeadCols(String attendanceManage) {
		
 		List<JdGridTableEntity> result = Lists.newArrayList();
		
		PlatformResult<List<DictItemResp>> dictItemByTypeCodeResult = dictItemFeignService.getDictItemByTypeCode("ATTENDANCE_REPORT");
		
		if(dictItemByTypeCodeResult.isSuccess()){
			List<DictItemResp> dictItemRespList = dictItemByTypeCodeResult.getObject();
			List<Map<String, String>> fieldList = mapper.getTableField();
			
			if (CollectionUtils.isNotEmpty(dictItemRespList)) {
				
				JdGridTableEntity entityID = new JdGridTableEntity();
				entityID.setLabel("id");
				entityID.setName("id");
				entityID.setWidth(100);
				entityID.setHidden(true);
				entityID.setSortable(false);
				entityID.setEditable(false);
				entityID.setAlign("center");
				result.add(entityID);
				
				JdGridTableEntity entityCODE = new JdGridTableEntity();
				entityCODE.setLabel("工号");
				entityCODE.setName("user_code");
				entityCODE.setWidth(100);
				entityCODE.setSortable(false);
				entityCODE.setEditable(false);
				entityCODE.setAlign("center");
				result.add(entityCODE);
				
				JdGridTableEntity entityNAME = new JdGridTableEntity();
				entityNAME.setLabel("姓名");
				entityNAME.setName("name");
				entityNAME.setWidth(100);
				entityNAME.setSortable(false);
				entityNAME.setEditable(false);
				entityNAME.setAlign("center");
				result.add(entityNAME);
				
				JdGridTableEntity entityDEPT = new JdGridTableEntity();
				entityDEPT.setLabel("考勤科室");
				entityDEPT.setName("report_dept_name");
				entityDEPT.setWidth(180);
				entityDEPT.setSortable(false);
				entityDEPT.setEditable(false);
				entityDEPT.setAlign("center");
				result.add(entityDEPT);
				
				JdGridTableEntity entityUSER = new JdGridTableEntity();
				entityUSER.setLabel("上报人");
				entityUSER.setName("create_user_name");
				entityUSER.setWidth(80);
				entityUSER.setSortable(false);
				entityUSER.setEditable(false);
				entityUSER.setAlign("center");
				result.add(entityUSER);
				
				JdGridTableEntity entityDATE = new JdGridTableEntity();
				entityDATE.setLabel("考勤月份");
				entityDATE.setName("report_date");
				entityDATE.setWidth(100);
				entityDATE.setSortable(false);
				entityDATE.setEditable(false);
				entityDATE.setAlign("center");
				result.add(entityDATE);
				
				//Boolean attendanceManage = UserInfoHolder.getRight("ATTENDANCE_MANAGE");
				
				
				if(!"Y".equals(attendanceManage)){
					
					JdGridTableEntity entitySTATUS = new JdGridTableEntity();
					entitySTATUS.setLabel("状态");
					entitySTATUS.setName("status");
					entitySTATUS.setWidth(80);
					entitySTATUS.setSortable(false);
					entitySTATUS.setEditable(false);
					entitySTATUS.setAlign("center");
					result.add(entitySTATUS);
					
					JdGridTableEntity entityCURRENTSTEPNAME = new JdGridTableEntity();
					entityCURRENTSTEPNAME.setLabel("当前节点");
					entityCURRENTSTEPNAME.setName("currentStepName");
					entityCURRENTSTEPNAME.setWidth(120);
					entityCURRENTSTEPNAME.setSortable(false);
					entityCURRENTSTEPNAME.setEditable(false);
					entityCURRENTSTEPNAME.setAlign("center");
					result.add(entityCURRENTSTEPNAME);
					
					JdGridTableEntity entityASSIGNEENAMES = new JdGridTableEntity();
					entityASSIGNEENAMES.setLabel("当前审批人");
					entityASSIGNEENAMES.setName("assigneeNames");
					entityASSIGNEENAMES.setWidth(120);
					entityASSIGNEENAMES.setSortable(false);
					entityASSIGNEENAMES.setEditable(false);
					entityASSIGNEENAMES.setAlign("center");
					result.add(entityASSIGNEENAMES);
				}
				
				for (DictItemResp dictItemResp : dictItemRespList) {
					
					//判断数据库中字段  没有则新建字段
					boolean isExistence = false;
					for (Map<String, String> map : fieldList) {
						String columnName = map.get("columnName");
						if(columnName.equals(dictItemResp.getItemCode())){
							isExistence = true;
						}
					}
					if(!isExistence){
						mapper.createNewField(dictItemResp.getItemCode(),dictItemResp.getItemName());
					}
					
					JdGridTableEntity entity = new JdGridTableEntity();
					entity.setLabel(dictItemResp.getItemName());
					entity.setName(dictItemResp.getItemCode());
					if("备注".equals(dictItemResp.getItemName())){
						entity.setWidth(150);
					}else{
						entity.setWidth(80);
					}
					entity.setSortable(false);
					entity.setEditable(false);
					entity.setAlign("center");
					result.add(entity);
				}
			}
		}
		
		return result;
	}

	@Override
	@Transactional(readOnly = false)
	public String importTemplateData(List<List<String>> list,AttendanceReport attendanceReport) {
		
		
		Example example = new Example(AttendanceReport.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("reportDeptCode",attendanceReport.getReportDeptCode())
		.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode())
        .andEqualTo("reportDate", attendanceReport.getReportDate());
        List<AttendanceReport> attendanceReportList = mapper.selectByExample(example);
        
        
        if(CollectionUtils.isNotEmpty(attendanceReportList)){
        	
        	if("1".equals(attendanceReportList.get(0).getStatus()) || "2".equals(attendanceReportList.get(0).getStatus())){
             	Assert.isTrue(false, "考勤数据已上报 ，请勿重复提交！");
        	}
        }
		//导入之前先清空数据
		mapper.deleteDataByMonth(attendanceReport);
		
		
		StringBuffer sql = new StringBuffer();
		
		StringBuffer improtMsg = new StringBuffer();//导入提示文本
		
		//获取表头
		List<String> headList = list.get(0);
		String head0 = headList.get(0);
		String head1 = headList.get(1);
		if(!head0.equals("工号")) {
			improtMsg.append("excel第一列名称必须为工号");
		}
		Assert.isTrue(head0.equals("工号"), "excel第一列名称必须为工号");
		
		if(!head1.equals("姓名")) {
			improtMsg.append("excel第二列名称必须为姓名");
		}
		Assert.isTrue(head1.equals("姓名"), "excel第二列名称必须为姓名");
		
	    long count = headList.stream().distinct().count();
	    Assert.isTrue(headList.size() == count, "excel表中存在重复列，请检查后重新导入！");
	    
	    //过滤姓名不存在的数据
	    List<List<String>> listSecond = Lists.newArrayList(); 
		for (int i = 1; i < list.size(); i++) {
			List<String> row = list.get(i);
			Assert.isTrue(StringUtils.isNoneBlank(row.get(0)), "第" + i + "行的姓名数据为空，请检查后重新导入！");
			if(StringUtils.isNoneBlank(row.get(0))) {
				listSecond.add(row);
			}
		}
	    
	    StringBuffer fieldStr = new StringBuffer();
	    fieldStr.append("id,report_dept_code,report_dept_name,status,report_date,create_date,create_user,create_user_name,is_deleted,user_code,name,");
	    
	    PlatformResult<List<DictItemResp>> dictItemByTypeCodeResult = dictItemFeignService.getDictItemByTypeCode("ATTENDANCE_REPORT");
	    if(dictItemByTypeCodeResult.isSuccess()){
			List<DictItemResp> dictItemRespList = dictItemByTypeCodeResult.getObject();
			for (DictItemResp dictItemResp : dictItemRespList) {
				fieldStr.append(dictItemResp.getItemCode()).append(",");
			}
			
	    }
	
	    String status = "0";
	    ThpsUser currentUserInfo = UserInfoHolder.getCurrentUserInfo();
		//封装新增语句
		fieldStr = fieldStr.deleteCharAt(fieldStr.length() - 1);
		for (List<String> strings : listSecond) {
			sql.append("insert into toa_attendance_report")
			   .append(" (")
			   .append(fieldStr)
			   .append(", sso_org_code")
			   .append(") values ('")
			   .append(String.valueOf(IdWork.id.nextId()))
			   .append("',")
			   .append("'").append(attendanceReport.getReportDeptCode()).append("',")
			   .append("'").append(attendanceReport.getReportDeptName()).append("',")
			   .append("'").append(status).append("',")
			   .append("'").append(attendanceReport.getReportDate()).append("',")
			   .append("'").append(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_FORMATTER)).append("',")
			   .append("'").append(currentUserInfo.getUsercode()).append("',")
			   .append("'").append(currentUserInfo.getUsername()).append("',")
			   .append("'N',");
			List<String> row = strings;
			//row = row.subList(0, headSize+2);
			if (StringUtils.isNotBlank(row.get(0))) {
				for (String cell : row) {
					if (StringUtils.isNotBlank(cell)) {
						sql.append("'").append(cell).append("'").append(",");
					} else {
						sql.append("' ',");
					}
				}
			}
			sql.append("'").append(UserInfoHolder.getCurrentUserCorpCode()).append("'");
			sql.append(");");
		}
		
		log.info("插入sql:" + sql.toString());
		
		if(listSecond.size() > 0) {
			//插入工资数据
			 mapper.excuteSql(sql.toString());
 		}
		
		improtMsg.append("本次成功导入").append(listSecond.size()).append("条。");
		
		return improtMsg.toString();
	}

	@Override
	@Transactional(readOnly = false)
	public void submitApproval(AttendanceReport record) {
		
		Example example = new Example(AttendanceReport.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("reportDeptCode",record.getReportDeptCode())
        .andEqualTo("reportDate", record.getReportDate());
        List<AttendanceReport> list = mapper.selectByExample(example);
        
        if(CollectionUtils.isNotEmpty(list)){
        	
        	if("1".equals(list.get(0).getStatus()) || "2".equals(list.get(0).getStatus())){
             	Assert.isTrue(false, "考勤数据已上报 ，请勿重复提交！");
        	}
        	 
        	String businessId = String.valueOf(IdWork.id.nextId());
        	
        	
        	String reportDeptCode = list.get(0).getReportDeptCode();
        	
        	String approver = mapper.selectApproverByDeptCode(reportDeptCode);
        	
        	Map<String, Object> workflowParams = new HashMap<>();
        	if(StringUtils.isNotBlank(approver)){
        		List<String> approverList = Arrays.asList(approver.split(","));
            	approverList = approverList.stream().distinct().collect(Collectors.toList());
            	List<String> L_UserNames = mapper.selectApproverName(approverList);
            	workflowParams.put("L_UserIds", String.join(",", approverList));
        		workflowParams.put("L_UserNames", String.join(",", L_UserNames));
        	}
        	
    		//启动流程
    		//L_BusinessId 流程关联数据ID users 选择用户code 多个用逗号隔开   names 选择用户名称  wfStepId 节点ID
    		workflowParams.put("L_BusinessId", businessId);//流程关联数据ID
    		
    		String workflowId = workflowInstanceService.doStartProcessInstance("L_90001", workflowParams);
    		
            for (AttendanceReport attendanceReport : list) {
            	attendanceReport.setBusinessId(businessId);
            	attendanceReport.setWorkflowId(workflowId);
            	attendanceReport.setStatus("1");
            	mapper.updateByPrimaryKeySelective(attendanceReport);
    		}
        }else{
        	Assert.isTrue(false, "考勤数据为空 ，请先导入数据！");
        }
		
	}

	@Override
	public List<TreeModel> selecAllTreeNode(String reportDate,String attendanceManage) {
		
		//boolean ADMIN = UserInfoHolder.getRight("ADMIN");
		//boolean ATTENDANCE_MANAGE = UserInfoHolder.getRight("ATTENDANCE_MANAGE");
		
		Example example = new Example(AttendanceReport.class);
        example.createCriteria()
				.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        		.andEqualTo("reportDate", reportDate)
				.andEqualTo("status", "2")
				//根据当前登录账号机构编码过滤查询数据
				.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<AttendanceReport> list = mapper.selectByExample(example);
		
        List<String> reportDeptCodes = new ArrayList<>();
        for (AttendanceReport attendanceReport : list) {
        	reportDeptCodes.add(attendanceReport.getReportDeptCode());
		}
		
        reportDeptCodes = reportDeptCodes.stream().distinct().collect(Collectors.toList());
        
		if(StringUtils.isNoneBlank(attendanceManage) && "Y".equals(attendanceManage)){
			 List<TreeModel> items = hrmsOrganizationFeignService.getTree().getObject();
			 if (CollectionUtils.isNotEmpty(items)) {
				 
				 for (TreeModel treeModel : items) {
					 setClass(reportDeptCodes, treeModel.getChildren());
				 }
			  }
			 
			 return items;
		}else{
			
			String deptcode = UserInfoHolder.getCurrentUserInfo().getDeptcode();
			
			String orgRang = UserInfoHolder.getOrgRang();
			orgRang = orgRang.replace("(", "");
			orgRang = orgRang.replace(")", "");
			orgRang = orgRang.replaceAll("'", "");
			if(StringUtils.isNotBlank(orgRang)){
				
				deptcode = deptcode + "," + orgRang;
			}
			
			String[] orgRangArray = deptcode.split(",");
			
			List<String> idList = Arrays.asList(orgRangArray);
			
			PlatformResult<List<OrganizationListRes>> listByIds = hrmsOrganizationFeignService.getListByIds(idList);
			
			List<TreeModel> treeModelList = new ArrayList<>();
			
			if(listByIds.isSuccess()){
				List<OrganizationListRes> organizationList = listByIds.getObject();
				if (CollectionUtils.isNotEmpty(organizationList)) {
					
					boolean isTree = false;
					
					for (OrganizationListRes item : organizationList) {
						TreeModel node = new TreeModel();
						if(reportDeptCodes.contains(item.getOrganizationId())){
							node.setIconSkin("attendance-icon");
						}
						node.setId(item.getOrganizationId());
						node.setPid(item.getParentId());
						node.setName(item.getName());
						node.setOpen(true);
						node.setCode(item.getCode());
						treeModelList.add(node);
						
						 if (("".equals(item.getParentId())) || ("0".equals(item.getParentId())) || ("root".equals(item.getParentId().toLowerCase()))
				                    || ("/".equals(item.getParentId().toLowerCase()))) {
							 isTree = true;
						 }
					}
					
					if(isTree){
						CommTree commTree = new CommTree();
						treeModelList = commTree.CommTreeList(treeModelList);
					}
				}
				
			}
			return treeModelList;
		}
		
	}

	private void setClass(List<String> reportDeptCodes, List<TreeModel> items) {
		if (CollectionUtils.isNotEmpty(items)) {
			for (TreeModel item : items) {
				item.setOpen(true);
				if(reportDeptCodes.contains(item.getId())){
					item.setIconSkin("attendance-icon");
				}
				setClass(reportDeptCodes,item.getChildren());
			}
		}
	}

	@Override
	public List<Map<String, String>> selectApprovalData(AttendanceReport record) {
        return mapper.selectApprovalData(record);
	}

	@Override
	@Transactional(readOnly = false)
	public void cancelApproval(AttendanceReport record) {
		
		Example example = new Example(AttendanceReport.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("reportDeptCode",record.getReportDeptCode())
        .andEqualTo("reportDate", record.getReportDate());
		
        List<AttendanceReport> list = mapper.selectByExample(example);
        
        if(CollectionUtils.isNotEmpty(list)){
        	
        	if("1".equals(list.get(0).getStatus())){
        		//流程撤销
//            	Map<String, Object> map = new HashMap<>();
//                map.put("reason",record.getReason());
//                map.put("workflowInstId", list.get(0).getWorkflowId());
//                workflowFeignService.doUndoInstance(map);
                workflowInstanceService.doUndoInstance(list.get(0).getWorkflowId(), record.getReason());
        	}else{
        		Assert.isTrue(false, "此数据已无法撤销，请刷新页面！");
        	}
        }
        
        for (AttendanceReport attendanceReport : list) {
        	attendanceReport.setStatus("4");
        	mapper.updateByPrimaryKeySelective(attendanceReport);
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void completeTask(StepFrom record) {
		Map<String,Object> paramsMap = new HashMap<>();
		
		if (StringUtils.isNotBlank(record.getComment())) {
			paramsMap.put("handleMarkedWords", record.getComment());
			paramsMap.put("L_TaskRemark", record.getComment());
		}
		
		Example example = new Example(AttendanceReport.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("businessId",record.getBusinessId());
        List<AttendanceReport> list = mapper.selectByExample(example);
        
		if("1".equals(record.getCompleteType())) {
			workflowTaskService.completeTask(record.getTaskId(), paramsMap);
		}else {
			
			paramsMap.put(WfContants.WF_VARIABLE_SELECT_RESPONSE, "不通过");
			
			workflowTaskService.doRejectTask(record.getTaskId(), paramsMap);
			for (AttendanceReport attendanceReport : list) {
				attendanceReport.setStatus("3");
				mapper.updateByPrimaryKeySelective(attendanceReport);
			}
		}
	}

	@Override
	@Transactional(readOnly = false)
	public void finishExamine(String transferRecordId) {
		Example example = new Example(AttendanceReport.class);
        example.createCriteria().andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("businessId",transferRecordId);
        List<AttendanceReport> list = mapper.selectByExample(example);
        for (AttendanceReport attendanceReport : list) {
			attendanceReport.setStatus("2");
			mapper.updateByPrimaryKeySelective(attendanceReport);
		}
        
        PlatformResult<List<ThpsUserResp>> roleUser = systemUserFeignService.selectUserListBySysRoleCode("ATTENDANCE_MANAGE");
        if(roleUser.isSuccess()){
        	List<ThpsUserResp> roleUserList = roleUser.getObject();
        	
        	StringBuffer receiver = new StringBuffer();
        	
        	for (ThpsUserResp thpsUserResp : roleUserList) {
        		receiver.append(thpsUserResp.getUsercode()).append(",");
			}
        	
        	String content = "考勤汇总上报提醒：";
			content += "您收到一条来自" + list.get(0).getReportDeptName() + "-" + list.get(0).getCreateUserName()  + "上报的考勤汇总，请登录OA系统查看！";
			NoticeReq noticeVo = NoticeReq.builder()
					.content(content)
					.noticeType("3")
					.subject("考勤汇总上报提醒")
					.sender("admin")
					.senderName("admin")
					.receiver(receiver.toString())
					.wxSendType("2")
					.source("考勤汇总上报")
					//.toUrl()
					//.url()
					.build();
			NoticeService.sendAsynNotice(noticeVo);
        }
	}

	@Override
	public String getReportStatus(AttendanceReport record) {
		Example example = new Example(AttendanceReport.class);
        example.createCriteria()
		.andEqualTo("isDeleted", Contants.IS_DELETED_FALSE)
        .andEqualTo("reportDeptCode",record.getReportDeptCode())
        .andEqualTo("reportDate", record.getReportDate());
		//根据当前登录账号机构编码过滤查询数据
//		.andEqualTo("ssoOrgCode", UserInfoHolder.getCurrentUserCorpCode());
        List<AttendanceReport> list = mapper.selectByExample(example);
        
          
        if(null != list && list.size() > 0){
        	return list.get(0).getStatus();
        }else{
        	return "-1";
        }
	}

	@Override
	@Transactional(readOnly = false)
	public void updateStatus() {
		mapper.updateStatus();
	}

	@Override
	public List<Map<String, Object>> attendanceReportDetails(AttendanceReport record) {
		//根据当前登录账号机构编码过滤查询数据
		record.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
		List<Map<String, Object>> attendanceReportDetails = mapper.attendanceReportDetails(record);
		for (Map<String, Object> map : attendanceReportDetails) {
			if(null == map.get("status") || "0".equals(map.get("status"))){
				map.put("status", "未上报");
			}
			if("1".equals(map.get("status"))){
				map.put("status", "已上报");
			}
			if("2".equals(map.get("status"))){
				map.put("status", "审批通过");
			}
			if("3".equals(map.get("status"))){
				map.put("status", "审批不通过");
			}
			if("4".equals(map.get("status"))){
				map.put("status", "已撤销");
			}
		}
		return attendanceReportDetails;
	}

	@Override
	@Transactional(readOnly = false)
	public void batchDelete(String ids) {
		Assert.hasText(ids, "ids不能为空.");
		String[] idArray = ids.split(",");
		for (String id : idArray) {
			AttendanceReport record = new AttendanceReport();
			record.setId(id);
			record.setUpdateDate(new Date());
			record.setIsDeleted("Y");
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
			if (user != null) {
				record.setUpdateUser(user.getUsercode());
				record.setUpdateUserName(user.getUsername());
			}
			mapper.updateByPrimaryKeySelective(record);
		}
		
	}
	
}
