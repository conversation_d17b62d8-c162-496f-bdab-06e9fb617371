 package cn.trasen.oa.hrm.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import cn.trasen.BootComm.utils.MD5;
import cn.trasen.BootComm.utils.PasswordHash;
import cn.trasen.homs.bean.sso.ThpsDept;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.TreeModel;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.hrm.dao.EmployeeMapper;
import cn.trasen.oa.hrm.po.Message;
import cn.trasen.oa.hrm.po.ResponseJson;
import lombok.extern.slf4j.Slf4j;


@Configuration
@Slf4j
public class ThpsUserDBHelper {

	@Value("${tsSystemDriver}")
	String tsSystemDriver; // 驱动

	@Value("${tsSystemUrl}")
	String tsSystemUrl; // 地址

	@Value("${tsSystemUsername}")
	String tsSystemUsername; // 用户名

	@Value("${tsSystemPwd}")
	String tsSystemPwd; // 密码

	@Value("${subUser}")
	String subUser; //是否开启同步指定部门人员数据

	@Value("${parentDeptCode}")
	String parentDeptCode; //父节点

	@Value("${parentPDCode}")
	String parentPDCode; //父节点PID

	@Value("${ssoDbType}")
	String ssoDbType;
	
	@Autowired
	private EmployeeMapper employeeMapper;

	public List<ThpsUser> getThpsUser(){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<ThpsUser> list = new ArrayList<ThpsUser>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询员工信息
			StringBuilder sb = new StringBuilder();
			if("oracle".equals(ssoDbType)) {
				sb.append("select u.id,u.corpcode,u.usercode,u.username,u.deptcode,");
				sb.append("u.status,u.mobile_no,u.oldusercode,u.sex,u.wxuserid,u.hosp_code,u.hosp_name,d.deptname from thps.thps_user u");
				sb.append(" left join thps.thps_dept d on u.deptcode = d.deptcode");
			}
			if("mysql".equals(ssoDbType)) {
				sb.append("select u.id,u.corpcode,u.usercode,u.username,u.deptcode,");
				sb.append("u.status,u.mobile_no,u.oldusercode,u.sex,u.wxuserid,u.hosp_code,u.hosp_name,d.deptname from thps_user u");
				sb.append(" left join thps_dept d on u.deptcode = d.deptcode");
			}
			if("1".equals(subUser)) {
				if(StringUtils.isNotEmpty(parentDeptCode)) {
					sb.append(" where d.corpcode in (").append(parentDeptCode).append(")");
				}
			}

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					ThpsUser thpsUser = new ThpsUser();
					for (int i = 1; i <= userColumnCount; i++) {
						if ("id".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setId(String.valueOf(rs.getObject(i)));
						}
						if ("corpcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setCorpcode(String.valueOf(rs.getObject(i)));
						}
						if ("usercode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setUsercode(String.valueOf(rs.getObject(i)));
						}
						if ("username".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setUsername(String.valueOf(rs.getObject(i)));
						}
						if ("deptcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setDeptcode(String.valueOf(rs.getObject(i)));
						}
						if ("status".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setStatus(Integer.valueOf(String.valueOf(rs.getObject(i))));
						}
						if ("mobile_no".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setMobileNo(String.valueOf(rs.getObject(i)));
						}
						if ("oldusercode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setOldusercode(String.valueOf(rs.getObject(i)));
						}
						if ("sex".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setSex(String.valueOf(rs.getObject(i)));
						}
						if ("wxuserid".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setWxuserid(String.valueOf(rs.getObject(i)));
						}
						if ("hosp_code".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setHospCode(String.valueOf(rs.getObject(i)));
						}
						if ("hosp_name".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setHospName(String.valueOf(rs.getObject(i)));
						}
						if ("deptname".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsUser.setDeptname(String.valueOf(rs.getObject(i)));
						}
					}
					list.add(thpsUser);
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
				return list;
	}

	public List<ThpsDept> getThpsDept(){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<ThpsDept> list = new ArrayList<ThpsDept>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询部门信息
			StringBuilder sb = new StringBuilder();
			if("oracle".equals(ssoDbType)) {
				sb.append("select id,corpcode,deptcode,deptname,pdcode,deptlevel,status from thps.thps_dept where status=1");
			}
			if("mysql".equals(ssoDbType)) {
				sb.append("select id,corpcode,deptcode,deptname,pdcode,deptlevel,status from thps_dept where status=1 and corpcode='")
				.append(UserInfoHolder.getCurrentUserCorpCode()).append("'");
			}

			if("1".equals(subUser)) {
				sb.append(" and corpcode in (").append(parentDeptCode).append(")");
			}

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					ThpsDept thpsDept = new ThpsDept();
					for (int i = 1; i <= userColumnCount; i++) {
						if ("id".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setId(String.valueOf(rs.getObject(i)));
						}
						if ("corpcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setCorpcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptname".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptname(String.valueOf(rs.getObject(i)));
						}
						if ("pdcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setPdcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptlevel".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptlevel(String.valueOf(rs.getObject(i)));
						}
						if ("status".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setStatus(Integer.valueOf(String.valueOf(rs.getObject(i))));
						}
					}
					list.add(thpsDept);
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
				return list;
	}

	public List<String> getThpsDeptChildsId(String deptcode){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<String> list = new ArrayList<String>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询子部门id
			StringBuilder sb = new StringBuilder();
			if("oracle".equals(ssoDbType)) {
				sb.append("select DEPTCODE from THPS.thps_dept t start with DEPTCODE = '");
				sb.append(deptcode).append("' connect by prior DEPTCODE=PDCODE");
			}
			if("mysql".equals(ssoDbType)) {
				sb.append("select d.deptcode from thps_dept d where FIND_IN_SET(d.deptcode,(select getDeptChildLst('");
				sb.append(deptcode).append("') as deptcode))");
			}

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					for (int i = 1; i <= userColumnCount; i++) {
						if ("deptcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							list.add(String.valueOf(rs.getObject(i)));
						}
					}
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
			return list;
	}
	
	public List<String> getThpsOrgChildsId(String orgcode){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<String> list = new ArrayList<String>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询子机构
			StringBuilder sb = new StringBuilder();
			sb.append("select d.org_code from thps_org d where FIND_IN_SET(d.org_code,(select getOrgChildList('");
			sb.append(orgcode).append("') as org_code))").append(" and org_code != '").append(orgcode).append("'");

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					for (int i = 1; i <= userColumnCount; i++) {
						if ("org_code".equalsIgnoreCase(userMd.getColumnName(i))) {
							list.add(String.valueOf(rs.getObject(i)));
						}
					}
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
			return list;
	}


	public List<Map<String,String>> getRolePlatform(String roleName) {
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<Map<String,String>> list = new ArrayList<Map<String,String>>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询子部门id
			StringBuilder sb = new StringBuilder();
			sb.append("select ID,ROLE_CODE,ROLE_NAME from thps_role");
			sb.append(" where SYS_CODE = 'ts-platform' and ENABLED = 1");
			
			if(StringUtils.isNotBlank(roleName)){
				sb.append(" and ROLE_NAME like '%").append(roleName).append("%'");
			}
			
			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					Map<String,String> map = new HashMap<>();
					for (int i = 1; i <= userColumnCount; i++) {
						if ("ID".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("id",String.valueOf(rs.getObject(i)));
						}
						if ("ROLE_CODE".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("roleCode",String.valueOf(rs.getObject(i)));
						}
						if ("ROLE_NAME".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("roleName",String.valueOf(rs.getObject(i)));
						}
					}
					list.add(map);
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
			return list;
	}
	
	public List<Map<String,String>> getOrgLiaison(String username) {
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<Map<String,String>> list = new ArrayList<Map<String,String>>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			StringBuilder sb = new StringBuilder();
			sb.append("select t2.USER_ID as userid,t3.usercode,CONCAT(t4.org_name,'-',t3.username) as username from thps_role t1");
			sb.append(" LEFT JOIN thps_user_role t2 on t1.ID = t2.ROLE_ID");
			sb.append(" LEFT JOIN thps_user_org_map t5 on t2.USER_ID = t5.id");
			sb.append(" LEFT JOIN thps_user t3 on t3.id = t5.USER_ID");
			sb.append(" LEFT JOIN thps_org t4 on t4.org_code = t3.corpcode");
			sb.append(" where t1.ROLE_CODE = 'ORG_LIAISON' and t3.usercode is not null ORDER BY t4.org_id ");
			
			if(StringUtils.isNotBlank(username)){
				sb.append(" and t3.username like '%").append(username).append("%'");
			}
			
			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					Map<String,String> map = new HashMap<>();
					for (int i = 1; i <= userColumnCount; i++) {
						if ("userid".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("userid",String.valueOf(rs.getObject(i)));
						}
						if ("usercode".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("usercode",String.valueOf(rs.getObject(i)));
						}
						if ("username".equalsIgnoreCase(userMd.getColumnName(i))) {
							map.put("username",String.valueOf(rs.getObject(i)));
						}
					}
					list.add(map);
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
			return list;
	}


	public List<String> getRoleIdsByUserCode(String userCode) {
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ArrayList<String> list = new ArrayList<String>();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询子部门id
			StringBuilder sb = new StringBuilder();
			sb.append("select ROLE_ID from thps_user_role where USER_ID = '");
			sb.append(userCode).append("'");

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					for (int i = 1; i <= userColumnCount; i++) {
						if ("ROLE_ID".equalsIgnoreCase(userMd.getColumnName(i))) {
							list.add(String.valueOf(rs.getObject(i)));
						}
					}
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
			return list;
	}

	public ThpsDept getThpsDeptById(String id){
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		ThpsDept thpsDept = new ThpsDept();
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询部门信息
			StringBuilder sb = new StringBuilder();
			if("oracle".equals(ssoDbType)) {
				sb.append("select id,corpcode,deptcode,deptname,pdcode,deptlevel,status from thps.thps_dept where status=1");
				sb.append(" and id='").append(id).append("'");
			}
			if("mysql".equals(ssoDbType)) {
				sb.append("select id,corpcode,deptcode,deptname,pdcode,deptlevel,status from thps_dept where status=1");
				sb.append(" and id='").append(id).append("'");
			}

			try {
				ps = conn.prepareStatement(sb.toString());
				rs = ps.executeQuery();

				ResultSetMetaData userMd = rs.getMetaData();
				int userColumnCount = userMd.getColumnCount();
				while (rs.next()) {
					for (int i = 1; i <= userColumnCount; i++) {
						if ("id".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setId(String.valueOf(rs.getObject(i)));
						}
						if ("corpcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setCorpcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptname".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptname(String.valueOf(rs.getObject(i)));
						}
						if ("pdcode".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setPdcode(String.valueOf(rs.getObject(i)));
						}
						if ("deptlevel".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setDeptlevel(String.valueOf(rs.getObject(i)));
						}
						if ("status".equalsIgnoreCase(userMd.getColumnName(i))) {
							thpsDept.setStatus(Integer.valueOf(String.valueOf(rs.getObject(i))));
						}
					}
				}
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
				return thpsDept;
	}

	public List<TreeModel> getThpsDeptTree(){
		List<ThpsDept> deptList = getThpsDept();
		return getDeptTree(deptList);
	}


	private List<TreeModel> getDeptTree(List<ThpsDept> depts) {
	   	List<TreeModel> treelist = new ArrayList<TreeModel>();
        for (ThpsDept x : depts) {
            TreeModel treeNode = new TreeModel();
            if(StringUtils.isEmpty(parentPDCode)) {
            	parentPDCode = "0";
            }
            if (("".equals(x.getPdcode())) || (parentPDCode.equals(x.getPdcode()))) {
                treeNode.setId(x.getDeptcode());
                treeNode.setPid(x.getPdcode());
                treeNode.setName(x.getDeptname());
                treeNode.setIcon("");
                treeNode.setOpen(true);
                treeNode.setParent(true);
                List<TreeModel> child = getDeptChildTree(x.getDeptcode(),depts);
                if(null != child && child.size() > 0) {
                	treeNode.setChildren(child);
                }
                treelist.add(treeNode);
            }
        }
        return treelist;
    }

	private List<TreeModel> getDeptChildTree(String deptcode,List<ThpsDept> depts) {
        List<TreeModel> lists = new ArrayList<TreeModel>();
        for (ThpsDept a : depts) {
            TreeModel childNode = new TreeModel();

            if (deptcode != null && deptcode.equals(a.getPdcode())) {
                childNode.setId(a.getDeptcode());
                childNode.setPid(a.getPdcode());
                childNode.setName(a.getDeptname());
                childNode.setIcon("");
                childNode.setParent(true);
                List<TreeModel> child = getDeptChildTree(a.getDeptcode(),depts);
                if(null != child && child.size() > 0) {
                	childNode.setChildren(child);
                }
                lists.add(childNode);
            }
        }
        return lists;
    }
	
	public void clearWxuserid(String userCode) {
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询部门信息
			StringBuilder sb = new StringBuilder();
			sb.append("update thps_user set wxuserid = null");
			sb.append(" where usercode='").append(userCode).append("'");
			
			log.info("执行更新sql语句：" + sb.toString());
			try {
				ps = conn.prepareStatement(sb.toString());
				ps.executeUpdate(sb.toString());
				conn.commit();
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
	}
	
	public void syncRoleUserData(ResponseJson responseJson) {
		//HSB消息体
		Message message = responseJson.getMessage();
		
		//his消息体
		String content = message.getContent();
		
		//转JSON对象
		JSONArray jsonObjectList = JSON.parseArray(content);
		
		
		PreparedStatement ps = null;
		ResultSet rs = null;
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		
		
		if (conn != null) {
			try {
				StringBuilder sb = new StringBuilder();
				for (int i = 0; i < jsonObjectList.size(); i++) {
					JSONObject jsonobject = jsonObjectList.getJSONObject(i);
					
					StringBuilder querySb = new StringBuilder();
					querySb.append("select id from thps_user");
					querySb.append(" where usercode='").append(jsonobject.getString("usercode")).append("'");
					log.info("执行的查询SQL:" + querySb.toString());
					
					ps = conn.prepareStatement(querySb.toString());
					rs = ps.executeQuery();
					
					String userId = "";
					while (rs.next()) {
						userId = rs.getString("id");
					}
					
					if(StringUtils.isNotBlank(userId) && "OA".equals(jsonobject.getString("sysCode")) 
							&& "N".equals(jsonobject.getString("isDeleted"))) {
						sb.append("insert into thps_user_role(ID,USER_ID,ROLE_ID) values (");
						sb.append("'").append(String.valueOf(IdWork.id.nextId())).append("',");
						sb.append("'").append(userId).append("',");
						sb.append("'").append(jsonobject.getString("roleId")).append("'");
						sb.append(");");
					}
					
					if(StringUtils.isNotBlank(userId) && "OA".equals(jsonobject.getString("sysCode")) 
							&& "Y".equals(jsonobject.getString("isDeleted"))) {
						sb.append("delete from thps_user_role where user_id = '");
						sb.append(userId);
						sb.append("'").append(" and role_id = '").append(jsonobject.getString("roleId"));
						sb.append("';");
					}
				}
				
				log.info("执行的SQL:" + sb.toString());
				
				ps = conn.prepareStatement(sb.toString());
				ps.execute();
				conn.commit();
				closeAll(rs, ps, conn);
			} catch (SQLException e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
		
	}

	public void modifyUserPwd(ResponseJson responseJson) {
		//HSB消息体
		Message message = responseJson.getMessage();
		
		//his消息体
		String content = message.getContent();
		
		//转JSON对象
		JSONObject jsonObject =	JSONObject.parseObject(content);
		
		String usercode = jsonObject.getString("usercode");
		String passwordBase64 = jsonObject.getString("password");
		StringBuilder sb = new StringBuilder();
		
		try {
			String password = new String(Base64.getDecoder().decode(passwordBase64), "UTF-8");
			if (StringUtils.isNotBlank(password)) {
                String mdPassword = PasswordHash.createHash(password);//hash 加密
                String md5Password = MD5.string2MD5(password);//md5 加密
                sb.append("update thps_user set password='").append(mdPassword).append("'");
                sb.append(",oldpassword='").append(md5Password.toUpperCase()).append("'");
                sb.append(" where usercode='").append(usercode).append("'");
            }
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		
		log.info("执行的SQL:" + sb.toString());
		PreparedStatement ps = null;
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			try {
				ps = conn.prepareStatement(sb.toString());
				ps.execute();
				conn.commit();
				closeAll(null, ps, conn);
			} catch (SQLException e) {
				e.printStackTrace();
				closeAll(null, ps, conn);
			}
		}
				
	}
	
	
	public void modifyPwd(String usercode,String password) {
		
		StringBuilder sb = new StringBuilder();
		try {
			if (StringUtils.isNotBlank(password)) {
                String mdPassword = PasswordHash.createHash(password);//hash 加密
                String md5Password = MD5.string2MD5(password);//md5 加密
                sb.append("update thps_user set password='").append(mdPassword).append("'");
                sb.append(",oldpassword='").append(md5Password.toUpperCase()).append("'");
                sb.append(" where usercode='").append(usercode).append("'");
            }
		} catch (Exception e1) {
			e1.printStackTrace();
		}
		
		log.info("执行的SQL:" + sb.toString());
		PreparedStatement ps = null;
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			try {
				ps = conn.prepareStatement(sb.toString());
				ps.execute();
				conn.commit();
				closeAll(null, ps, conn);
			} catch (SQLException e) {
				e.printStackTrace();
				closeAll(null, ps, conn);
			}
		}
				
	}


	public void modifyUserStatus(ResponseJson responseJson) {
		//HSB消息体
		Message message = responseJson.getMessage();
		
		//his消息体
		String content = message.getContent();
		
		//转JSON对象
		JSONObject jsonObject =	JSONObject.parseObject(content);
		
		String usercode = jsonObject.getString("userCode");
		String status = jsonObject.getString("status");
		StringBuilder sb = new StringBuilder();
		sb.append("update thps_user set status='").append(status).append("'");
        sb.append(" where usercode='").append(usercode).append("'");
		
    	log.info("待执行的SQL语句：" + sb.toString());
		PreparedStatement ps = null;
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			try {
				ps = conn.prepareStatement(sb.toString());
				ps.execute();
				conn.commit();
				log.info("执行SQL语句成功");
				if("0".equals(status)) {
					status = "2";
				}
				employeeMapper.modifyEmployeeStatus(usercode,status);
				closeAll(null, ps, conn);
			} catch (SQLException e) {
				e.printStackTrace();
				log.info("执行SQL语句成功失败,失败原因" + e.getMessage());
				closeAll(null, ps, conn);
			}
		}
		
	}
	
	
	public void updateWxuserid(String userCode, String openId) {
		PreparedStatement ps = null; // 创建PreparedStatement执行对象
		ResultSet rs = null; // 创建ResultSet结果集对象
		Connection conn = getConnetion(tsSystemDriver, tsSystemUrl, tsSystemUsername, tsSystemPwd);
		if (conn != null) {
			// 查询部门信息
			StringBuilder sb = new StringBuilder();
			sb.append("update thps_user set wxuserid='").append(openId).append("'");
			sb.append(" where usercode='").append(userCode).append("'");
			
			log.info("执行更新sql语句：" + sb.toString());
			
			try {
				ps = conn.prepareStatement(sb.toString());
				ps.executeUpdate(sb.toString());
				conn.commit();
				closeAll(rs, ps, conn);
			} catch (Exception e) {
				e.printStackTrace();
				closeAll(rs, ps, conn);
			}
		}
		
	}

	/**
	 * @Description: 获得连接对象
	 * @param driver 数据库驱动
	 * @param url 数据库url
	 * @param username 账户名
	 * @param password 密码
	 * @return
	 */
	public Connection getConnetion(String driver, String url, String username, String password) {
		Connection conn = null;
		try {
			Class.forName(driver); // 映射Java驱动
			conn = DriverManager.getConnection(url, username, password); // 获得连接对象
			conn.setAutoCommit(false); // 关闭自动提交事务
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return conn;
	}

	/**
	 * @Description: 关闭所有资源
	 * @param rs
	 * @param ps
	 * @param conn
	 * @return
	 */
	public static void closeAll(ResultSet rs, PreparedStatement ps, Connection conn) {
		try {
			if (rs != null) {
				rs.close();
			}
			if (ps != null) {
				ps.close();
			}
			if (conn != null) {
				conn.close();
			}
		} catch (SQLException e) {
			e.printStackTrace();
		}
	}

}
