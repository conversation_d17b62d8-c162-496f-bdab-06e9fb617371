<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.civilAffairs.dao.CivilAffairsFieldMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.civilAffairs.model.CivilAffairsField">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="VARCHAR" property="id" />
    <result column="group_name" jdbcType="VARCHAR" property="groupName" />
    <result column="group_id" jdbcType="VARCHAR" property="groupId" />
    <result column="show_name" jdbcType="VARCHAR" property="showName" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="field_type" jdbcType="VARCHAR" property="fieldType" />
    <result column="dict_source" jdbcType="VARCHAR" property="dictSource" />
    <result column="field_length" jdbcType="INTEGER" property="fieldLength" />
    <result column="data_format" jdbcType="VARCHAR" property="dataFormat" />
    <result column="prompt_text" jdbcType="VARCHAR" property="promptText" />
    <result column="data_source" jdbcType="INTEGER" property="dataSource" />
    <result column="is_must" jdbcType="INTEGER" property="isMust" />
    <result column="is_multiple" jdbcType="INTEGER" property="isMultiple" />
    <result column="is_remove_duplicate" jdbcType="INTEGER" property="isRemoveDuplicate" />
    <result column="is_only" jdbcType="INTEGER" property="isOnly" />
    <result column="is_allow_deleted" jdbcType="INTEGER" property="isAllowDeleted" />
    <result column="is_hide" jdbcType="INTEGER" property="isHide" />
    <result column="is_disabled" jdbcType="INTEGER" property="isDisabled" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_user_name" jdbcType="VARCHAR" property="createUserName" />
    <result column="update_date" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_user_name" jdbcType="VARCHAR" property="updateUserName" />
    <result column="is_deleted" jdbcType="CHAR" property="isDeleted" />
    <result column="sso_org_code" jdbcType="VARCHAR" property="ssoOrgCode" />
  </resultMap>
  
  <select id="getFieldAndJurisdictionListByGroupid" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsField" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsField">
 	select s.* from civil_affairs_field s
		 where 1 =1  and s.is_deleted='N'
  <if test="groupId!=null and groupId!=''">
  	and s.group_id like concat('%',#{groupId},'%') 
  </if>
  <if test="isHide!=null">
  	and s.is_hide =#{isHide}
  </if>
    <if test="isDisabled!=null">
	  	and s.is_disabled = #{isDisabled}
	  </if>
	order by  s.seq DESC ,s.create_date asc 
  </select>
  
  
   <select id="getFielListByGroupid" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsField" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsField">
 	select * from civil_affairs_field
    where 1 =1  and is_deleted='N'
	  <if test="groupId!=null and groupId!=''">
	  	and group_id like concat('%',#{groupId},'%') 
	  </if>
	  <if test="isHide!=null">
	  	and is_hide =#{isHide}
	  </if>
	   <if test="isDisabled!=null">
	  	and is_disabled = #{isDisabled}
	  </if>
	order by  seq DESC ,create_date asc 
 
  </select>
  
  
	  <select id="getFieldsListByCondition" resultType="cn.trasen.oa.civilAffairs.model.CivilAffairsField" parameterType="cn.trasen.oa.civilAffairs.model.CivilAffairsField">
	  	
		  select * from civil_affairs_field s  where 1 =1  and s.is_deleted='N'
		  <if test="groupId!=null and groupId!=''">
		  	and s.group_id like concat('%',#{groupId},'%') 
		  </if>
		  <if test="isHide!=null">
		  	and s.is_hide = #{isHide}
		  </if>
		  <if test="isDisabled != null">
		  	and s.is_disabled = #{isDisabled}
		  </if>
		  <if test="groupIds != null and groupIds.size() > 0">
			and ( s.group_id in
			<foreach collection="groupIds" index="index" item="item" open="(" separator="," close=")">
		           #{item}
		       </foreach>
		       )
		</if>
		  order by  -s.seq DESC ,s.create_date asc 
	  </select>
</mapper>