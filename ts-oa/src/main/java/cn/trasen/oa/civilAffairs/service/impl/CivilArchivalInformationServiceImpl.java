package cn.trasen.oa.civilAffairs.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import com.google.common.collect.Maps;

import cn.trasen.homs.bean.base.DictItemResp;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.DictItemFeignService;
import cn.trasen.oa.civilAffairs.dao.CivilArchivalInformationMapper;
import cn.trasen.oa.civilAffairs.model.CivilArchivalInformation;
import cn.trasen.oa.civilAffairs.service.CivilArchivalInformationService;
import cn.trasen.oa.civilAffairs.util.VueTableEntity;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName CivilArchivalInformationServiceImpl
 * @Description TODO
 * @date 2024��1��25�� ����2:21:02
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class CivilArchivalInformationServiceImpl implements CivilArchivalInformationService {

	@Autowired
	private CivilArchivalInformationMapper mapper;
	
	@Autowired
	DictItemFeignService dictItemFeignService;

	@Transactional(readOnly = false)
	@Override
	public Integer save(CivilArchivalInformation record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(CivilArchivalInformation record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		CivilArchivalInformation record = new CivilArchivalInformation();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public CivilArchivalInformation selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<CivilArchivalInformation> getDataSetList(Page page, CivilArchivalInformation record) {
		Example example = new Example(CivilArchivalInformation.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		List<CivilArchivalInformation> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}

	@Override
	public DataSet<Map<String, Object>> getPageList(Page page, CivilArchivalInformation record) {
		// TODO Auto-generated method stub
		
		List<String> itemNameValueList = mapper.getItemNameValueList(UserInfoHolder.getCurrentUserCorpCode());
		record.setItemNameValue(itemNameValueList);//根据字典，循环出查询列
		List<Map<String, Object>> records = mapper.getPageList(page,record);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	@Override
	public String archivalNumberCheck(CivilArchivalInformation record) {
		// TODO Auto-generated method stub
		String message = "";
		if (record !=null && StringUtils.isNotBlank(record.getArchivalNumber())) {
			Example example = new Example(CivilArchivalInformation.class);
			Example.Criteria criteria = example.createCriteria();
			criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
			criteria.andEqualTo("archivalNumber", record.getArchivalNumber());
			if(StringUtils.isNotBlank(record.getId())){
				criteria.andNotEqualTo("id", record.getId());
			}
			List<CivilArchivalInformation> records = mapper.selectByExample(example);
			if (CollectionUtils.isNotEmpty(records)) {
				message = "档案编号:" + record.getArchivalNumber() + "已存在,不能重复添加";
			}
		}
		return message;
	}

	@Override
	public String selectArchivalNumberMax(String year) {
		// TODO Auto-generated method stub
		return mapper.selectArchivalNumberMax(year);
	}

	@Override
	public List<Map<String, Object>> getfilesTypeByPersonnelId(String personnelId) {
		// TODO Auto-generated method stub
		return  mapper.getfilesTypeByPersonnelId(personnelId, UserInfoHolder.getCurrentUserCorpCode());
	}

	@Override
	public List<VueTableEntity> selectCivilArchivalInformationHeader() {
		List<VueTableEntity> retVueTableEntity = new ArrayList<>();
		// TODO Auto-generated method stub
		//List<Map<String,Object>> listHeader = new ArrayList<>();
		//Map<String,Object> map = new HashMap<String, Object>();
		retVueTableEntity.add(new VueTableEntity("姓名","name",null,null));
		retVueTableEntity.add(new VueTableEntity("身份证号","identityNumber",null,null));
		retVueTableEntity.add(new VueTableEntity("编号","archivalNumber",null,null));
		retVueTableEntity.add(new VueTableEntity("柜号","cabinetNumber",null,null));
		retVueTableEntity.add(new VueTableEntity("盒号","boxNumber",null,null));
		
		Map<String, String> fileTypeMap = convertDictMap("civil_file_type");  //文件类别
		for (String key:fileTypeMap.keySet()){
			 //System.out.println("key= "+key+" and value= "+fileTypeMap.get(key));
			 retVueTableEntity.add(new VueTableEntity(fileTypeMap.get(key),key,null,null));
			 }
		
		retVueTableEntity.add(new VueTableEntity("最近修改人","updateUserName",null,null));
		retVueTableEntity.add(new VueTableEntity("最近修改时间","updateDate",null,null));
		
		
		/*Map<String, String> map = new TreeMap<>();
		map.put("name","姓名");
		map.put("identity_number","身份证号");
		map.put("archival_number","编号");
		map.put("cabinet_number","柜号");
		map.put("box_number","盒号");
		Map<String, String> postCategoryDictMap = convertDictMap("civil_file_type");  //文件类别
		map.putAll(postCategoryDictMap);
		map.put("update_user_name","最近修改人");
		map.put("update_date","最近修改时间");
		List listHeader = new ArrayList(map.entrySet());//map转list
*/		return retVueTableEntity;
	}
	
	
	
	private Map<String, String> convertDictMap(String dictType) {
		Assert.notNull(dictType, "dictType must not be null.");
		Map<String, String> map = Maps.newHashMap();
		List<DictItemResp> dictItemList = dictItemFeignService.getDictItemByTypeCode(dictType).getObject();
		if (CollectionUtils.isNotEmpty(dictItemList)) {
			for (DictItemResp d : dictItemList) {
				map.put(d.getItemNameValue(), d.getItemName());
			}
		}
		return map;
	}
}
