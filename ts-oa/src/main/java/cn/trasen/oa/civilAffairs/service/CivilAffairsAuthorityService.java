package cn.trasen.oa.civilAffairs.service;

import java.util.Map;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.oa.civilAffairs.model.CivilAffairsAuthority;

/**
 * @ClassName CivilAffairsAuthorityService
 * @Description TODO
 * @date 2023��12��18�� ����4:59:05
 * <AUTHOR>
 * @version 1.0
 */
public interface CivilAffairsAuthorityService {

	/**
	 * @Title save
	 * @Description 新增
	 * @param record
	 * @return Integer
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	Integer save(CivilAffairsAuthority record);

	/**
	 * @Title update
	 * @Description 修改
	 * @param record
	 * @return Integer
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	Integer update(CivilAffairsAuthority record);

	/**
	 * 
	 * @Title deleteById
	 * @Description 根据ID删除
	 * @param id
	 * @return Integer
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	Integer deleteById(String id);

	/**
	 * @Title selectById
	 * @Description 根据ID查询
	 * @return CivilAffairsAuthority
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	CivilAffairsAuthority selectById(String id);

	/**
	 * @Title getDataSetList
	 * @Description 分页查询
	 * @param page
	 * @param record
	 * @return DataSet<CivilAffairsAuthority>
	 * @date 2023��12��18�� ����4:59:05
	 * <AUTHOR>
	 */
	DataSet<CivilAffairsAuthority> getDataSetList(Page page, CivilAffairsAuthority record);
	
	Map<String,Object>  selectCivilAffairsAuthorityByUserId(CivilAffairsAuthority record);
}
