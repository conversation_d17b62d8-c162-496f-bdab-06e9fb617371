package cn.trasen.oa.document.controller;


import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import cn.hutool.core.io.FileUtil;
import cn.trasen.homs.core.bean.GlobalSetting;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdWork;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.StringUtil;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.base.GlobalSettingsFeignService;
import cn.trasen.oa.civilAffairs.model.CivilAdmissionInformation;
import cn.trasen.oa.document.dao.AttachmentMapper;
import cn.trasen.oa.document.model.Attachment;
import cn.trasen.oa.document.model.Document;
import cn.trasen.oa.document.service.AttachmentService;
import cn.trasen.oa.document.service.DocumentDownloadLogService;
import cn.trasen.oa.document.utils.HttpUtil;
import cn.trasen.oa.document.utils.ImageUtils;
import cn.trasen.oa.document.utils.ResultData;
import cn.trasen.oa.document.utils.ZipUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import sun.misc.BASE64Decoder;
import tk.mybatis.mapper.entity.Example;

@Api(tags = "文档管理API")
@RestController
public class AttachmentController {

    private static final Logger logger = LoggerFactory.getLogger(AttachmentController.class);

    @Autowired
    private AttachmentMapper attachmentMapper;
    
    @Autowired
    private AttachmentService attachmentService;
    
    @Autowired
    DocumentDownloadLogService documentDownloadLogService;
    
    @Autowired
    GlobalSettingsFeignService globalSettingsFeignService;

    @Value("${fileSystemPath}")
    String fileSystemPath;    //文件上传根目录
    
    @Value("${previewURL}")
    String previewURL; //预览插件预处理接口路径
    
    @Value("${filePathUrl}")
    String filePathUrl;//附件访问路径
    


    /**
     * @throws
     * @Title: fileUpload
     * @Description: 附件上传接口
     * @param: @param request
     * @param: @return
     * @return: PlatformResult<Object>
     * @author: YueC
     * @date: 2020年2月2日 下午2:16:18
     */
    @ApiOperation(value = "文件上传接口", notes = "文件上传接口")
    @ApiImplicitParams({
    	 @ApiImplicitParam(paramType = "query", name = "file", value = "附件流", required = false, dataType = "String"),
         @ApiImplicitParam(paramType = "query", name = "module", value = "模块名称", required = true, dataType = "String"),
         @ApiImplicitParam(paramType = "query", name = "fillupf", value = "富文本类型（1： layui原生   2：tinymce）", required = false, dataType = "String")
    })
    @PostMapping("/attachment/fileUpload")
    public Object fileUpload(MultipartHttpServletRequest request) {
        String moduleName = request.getParameter("module"); //模块名称
        String folderId = request.getParameter("folderId"); //文件夹id
        String fillupf = request.getParameter("fillupf"); 
        String token = UserInfoHolder.getToken();
        if(StringUtils.isEmpty(token)) {
        	token = request.getParameter("token");
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
        String folderName = simpleDateFormat.format(new Date());//文件夹名称  每天会产生一个新的文件夹
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> fillupfResult = new HashMap<>();
        //获取上传的文件流
        List<MultipartFile> files = request.getFiles("file");
        if (null != files && files.size() > 0) {
        	
        	PlatformResult<GlobalSetting> globalSettingResult = globalSettingsFeignService.getGlobalSetting("Y");
        	GlobalSetting globalSetting = globalSettingResult.getObject();
        	
        	Integer allowFileSize = globalSetting.getAllowFileSize();  //允许上传的最大文件大小
        	String allowFileExtension = globalSetting.getAllowFileExtension();   //允许上传的文件后缀名称
        	List<String> allowFileExtensionList = null;
        	if(StringUtils.isNotBlank(allowFileExtension)) {
        		String[] allowFileExtensions = allowFileExtension.split(",");
        		allowFileExtensionList = Arrays.asList(allowFileExtensions);
        	}
        	
        	long uploadSize = 0;
            //验证文件大小和格式
            for (MultipartFile multipartFile : files) {
                //过滤掉文件名非法字符
                String originalFilename = multipartFile.getOriginalFilename().replaceAll("[\\s\\\\/:\\*\\?\\\"<>\\|]", "");
                long size = multipartFile.getSize();
                
                uploadSize += size;
				
                String extensName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                if (CollectionUtils.isNotEmpty(allowFileExtensionList) && !allowFileExtensionList.contains(extensName.toLowerCase())) {
                    return PlatformResult.failure(originalFilename + "错误,不支持的文件类型:." + extensName + ",请重新上传！");
                }
                
                if (size > allowFileSize * 1024 * 1024) {
                    return PlatformResult.failure(originalFilename + "错误,文件最大上传大小为:" + allowFileSize + "M,请重新上传！");
                }
            }
            
            if("personal".equals(moduleName)) {
            	
            	Integer empUploadFileSize = attachmentMapper.selectEmpUploadFileSize(UserInfoHolder.getCurrentUserCode()); 
            	
				if(null != empUploadFileSize && empUploadFileSize > 0 ) { 
					
					Long totalSize = attachmentMapper.selectTotalSize(UserInfoHolder.getCurrentUserCode());
					
					if(null == totalSize) {
						totalSize = 0l;
					}
					
					long empsize = empUploadFileSize * 1024 * 1024; 
					
					if ((uploadSize + totalSize) > empsize) { 
					  return PlatformResult.failure("您的个人文档上传大小已达到限制，如有疑问请联系系统管理员！"); 
					} 
				}
            }

            List<Attachment> recordList = new ArrayList<>();
            for (MultipartFile file : files) {
                //过滤掉文件名非法字符
                String originalFilename = file.getOriginalFilename().replaceAll("[\\s\\\\/:\\*\\?\\\"<>\\|]", "");
                long size = file.getSize();
                //文件类型
                String contentType = file.getContentType();
                String mimeType = contentType.split("/")[0];
                //文件扩展名
                String extensName = originalFilename.substring(originalFilename.lastIndexOf(".") + 1);
                String isImage = "0";
                if (mimeType.toLowerCase().equals("image")) {
                    isImage = "1";
                }
                Attachment record = new Attachment();
                record.setId(String.valueOf(IdWork.id.nextId()));
                //文件实际路径
                String pathNames = fileSystemPath + File.separator + moduleName + File.separator + folderName;
                String realPath = pathNames + File.separator + record.getId() + "." + extensName;

                //文件假路径
                String fakePath = record.getId() +File.separator+ "attachView"+ File.separator + moduleName + File.separator + folderName + File.separator + originalFilename;
                FileUtil.mkdir(pathNames);
                Path path = Paths.get(realPath);
                //保存文件到指定目录
//                File dest = new File(realPath);
                //文件不存在则创建
//                if (!dest.getParentFile().exists()) {
//                    dest.getParentFile().mkdirs();
//                }
                try {
//                    file.transferTo(dest);
                	file.transferTo(path);
                } catch (IllegalStateException e1) {
                    e1.printStackTrace();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                //压缩图片大小
                if ("jpg".equalsIgnoreCase(extensName) ||
                        "png".equalsIgnoreCase(extensName) || "gif".equalsIgnoreCase(extensName)) {
                    try {
                        ImageUtils.pressImageByScale(realPath, realPath, (float) 1);
                    } catch (Exception e) {
                        e.printStackTrace();
                        System.out.println("压缩图片异常！" + e.getMessage());
                    }
                }
                //保存附件信息到数据库
                record.setModuleName(moduleName);
                record.setOriginalName(originalFilename);
                record.setFileName(record.getId() + "." + extensName);
                record.setIsImage(isImage);
                record.setIsCopy("0");
                record.setFileExtension(extensName);
                record.setFileSize(String.valueOf(size));
                record.setFilePath(fakePath);
                record.setRealPath(realPath);
                record.setFileStatus("1");
                record.setMimeType(mimeType);
                record.setContentType(contentType);
                record.setUpdateUser(UserInfoHolder.getCurrentUserCode());
                record.setUploadUserName(UserInfoHolder.getCurrentUserName());
                record.setUploadDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                record.setUploadDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                record.setCreateDate(new Date());
                
                if("personal".equals(moduleName)) {
                	if(StringUtils.isBlank(folderId)) {
                		record.setFolderId("999999999999999999");
                	}else {
                		record.setFolderId(folderId);
                	}
                }
               
                //lzh
                record.setIsDelete(Contants.IS_DELETED_FALSE);
                record.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
                record.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
                record.setCreateUser(UserInfoHolder.getCurrentUserCode());
                record.setCreateUserName(UserInfoHolder.getCurrentUserName());

                record.setUploadUser(UserInfoHolder.getCurrentUserCode());
                record.setUploadTime(new Date());
                recordList.add(record);
                Map<String, Object> resultMap = new HashMap<>();
                if (StringUtils.isNotBlank(fillupf) && "1".equals(fillupf)) {//富文本要求返回的格式
                    fillupfResult.put("code", 0);//0表示成功，1失败
                    fillupfResult.put("msg", "上传成功");//提示消息
                    Map<String, Object> data = new HashMap<>();
                    data.put("src", "/ts-document/attachment/" + fakePath);//图片url
                    data.put("title", record.getOriginalName());//图片名称，这个会显示在输入框里
                    fillupfResult.put("data", data);
                }else if(StringUtils.isNotBlank(fillupf) && "2".equals(fillupf)){//
                	fillupfResult.put("location", "/ts-document/attachment/" + fakePath);//图片url
                } else {
                    resultMap.put("fileId", record.getId()); //附件id
                    resultMap.put("fileSize", record.getFileSize());//附件大小
                    resultMap.put("filePath", fakePath);//附件路径
                    resultMap.put("fileRealName", record.getFileName());//附件真实名称
                    resultMap.put("fileName", record.getOriginalName());//附件名称
                    result.add(resultMap);
                }

            }
            attachmentService.batchInsert(recordList);
            
//            try {
//            	Base64 base64 = new Base64();
//                String pathUrl = filePathUrl + recordList.get(0).getId() + "?fullfilename=" + recordList.get(0).getFileName() + "&token=" + token;
//                String url = "";
//    			try {
//    				url = previewURL + "?url=" +  base64.encodeToString(pathUrl.getBytes("UTF-8"));
//    			} catch (UnsupportedEncodingException e) {
//    				e.printStackTrace();
//    			}
//                String preview = HttpUtil.getPreviewURL(url);
//                logger.info("====>>>>>>>调用预览插件预处理接口返回结果：" + preview);	
//            }catch(Exception e) {
//            	e.printStackTrace();
//            }
            
        }
       
        if (StringUtils.isNotBlank(fillupf)) {
            return fillupfResult;
        } else {
            return PlatformResult.success(result);
        }
        
        
    }
    
    /**
     * 
     * @Title: imageBase64Upload   
     * @Description: imageBase64图片上传
     * @param: @param request
     * @param: @return      
     * @return: Object  
     * @author: YueC
     * @date:   2020年4月29日 下午4:58:23    
     * @throws
     */
    @ApiOperation(value = "imageBase64图片上传接口", notes = "imageBase64图片上传接口")
    @ApiImplicitParams({
         @ApiImplicitParam(paramType = "query", name = "module", value = "模块名称", required = true, dataType = "String"),
         @ApiImplicitParam(paramType = "query", name = "imageBase64", value = "imageBase64字符串", required = true, dataType = "String")
    })
    @PostMapping("/attachment/imageBase64Upload")
	public Object imageBase64Upload(HttpServletRequest request,String imageBase64) {
    	String moduleName = request.getParameter("module"); //模块名称
    	String fillupf = request.getParameter("fillupf"); 
		Map<String, Object> result = new HashMap<String, Object>();
		try {
			String fileId = String.valueOf(IdWork.id.nextId());
			String fileName = fileId + ".png";
			String realPath = fileSystemPath + "/"+moduleName+"/" + fileName;
			
			if (StringUtil.isEmpty(imageBase64)) {
				return PlatformResult.failure("imageBase64数据为空，上传失败");
			} 
			
			File dest=new File(realPath);
			if (!dest.getParentFile().exists()) {
	           dest.getParentFile().mkdirs();
	        }
	 
			BASE64Decoder decoder = new BASE64Decoder();
			// Base64解码
			imageBase64 = imageBase64.replaceAll(" ","+");
			byte[] b = decoder.decodeBuffer(imageBase64);
			for (int i = 0; i < b.length; ++i) {
				if (b[i] < 0) {// 调整异常数据
					b[i] += 256;
				}
			}
			try (OutputStream out = new FileOutputStream(realPath);){
				out.write(b);
				out.flush();
			} catch (Exception e) {
				e.printStackTrace();
			}
			
			if(StringUtils.isBlank(fillupf)) {
				try {
					ImageUtils.pressImageBySize(realPath,realPath,120,60);
				}catch(Exception e) {
					e.printStackTrace();
				}
			}
			
			Attachment attachment = new Attachment();
			attachment.setId(fileId);
			attachment.setFileName(fileName);
			attachment.setOriginalName(UserInfoHolder.getCurrentUserCode() + ".png");
			attachment.setFileExtension("png");
			attachment.setFileSize("20");
			attachment.setFilePath(attachment.getId()+"/attachView/"+moduleName+"/" + UserInfoHolder.getCurrentUserCode() + ".png");
			attachment.setRealPath(realPath);
			attachment.setUpdateUser(UserInfoHolder.getCurrentUserCode());
			attachment.setUploadUserName(UserInfoHolder.getCurrentUserName());
			attachment.setUploadDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			attachment.setUploadDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
			attachment.setCreateDate(new Date());
			attachment.setIsDelete(Contants.IS_DELETED_FALSE);
			attachment.setCreateDept(UserInfoHolder.getCurrentUserInfo().getDeptcode());
			attachment.setCreateDeptName(UserInfoHolder.getCurrentUserInfo().getDeptname());
			attachment.setCreateUser(UserInfoHolder.getCurrentUserCode());
			attachment.setCreateUserName(UserInfoHolder.getCurrentUserName());
			attachment.setUploadUser(UserInfoHolder.getCurrentUserCode());
			attachment.setUploadTime(new Date());
			attachment.setSsoOrgCode(UserInfoHolder.getCurrentUserCorpCode());
			
			attachmentService.insert(attachment);
			
			if(StringUtils.isNotBlank(fillupf) && "2".equals(fillupf)) {
				result.put("location", "/ts-document/attachment/" + attachment.getFilePath());//图片url
			}else {
				result.put("fileId", attachment.getId()); //附件id
	            result.put("fileSize", attachment.getFileSize()); //附件大小
				result.put("filePath", attachment.getFilePath()); //附件路径
				result.put("fileName",attachment.getFileName()); //附件名称
			}
          
			return PlatformResult.success(result);
		} catch (Exception e) {
			e.printStackTrace();
			return PlatformResult.failure("服务端异常，异常信息:"+e.getMessage());
		}
	}

    /**
     * @throws
     * @Title: fileDown
     * @Description: 文件下载接口
     * @param: @param request
     * @param: @return
     * @return: PlatformResult<Object>
     * @author: YueC
     * @date: 2020年2月2日 下午2:16:18
     */
    @ApiOperation(value = "文件下载预览接口", notes = "文件下载预览接口")
    @GetMapping("/attachment/{fileId}/**/attachView/**/{folderName}/{fileName}")
    public void fileDown(HttpServletRequest request, HttpServletResponse response,
                         @PathVariable("fileId") String fileId, @PathVariable("folderName") String folderName,
                         @PathVariable("fileName") String fileName) {
        try {
            if (!fileName.isEmpty()) {
                //拿到下载链接
                String uri = URLDecoder.decode(request.getRequestURI(), "utf-8");
                //拿到文件名
                String originalName = uri.substring(uri.lastIndexOf("/") + 1);
                //拿到文件后缀
                String ext = originalName.substring(originalName.lastIndexOf("."));
                //获取真实路径

                //文件上传根目录
                //String fileSystemPath = redisUtil.getTypeAndKey("file", "fileSystemPath");
                String realPath = fileSystemPath + uri.substring(uri.indexOf(fileId) + fileId.length());
                //文件路径
                realPath = realPath.substring(0, realPath.lastIndexOf("/") + 1) + fileId + ext;
                realPath = realPath.replaceAll("/attachView", "");
                

                Attachment attachment = attachmentService.selectByPrimaryKey(fileId);
                if(null == attachment) {
                	attachment = attachmentService.selectCommFileById(fileId);
                }
                
                String userCode = UserInfoHolder.getCurrentUserCode();
                String userName = UserInfoHolder.getCurrentUserName();
                
                //科室文档保存下载记录的文档
                documentDownloadLogService.insert(attachment, userCode, userName);

                File file = new File(realPath);
                if (file.exists()) {
                    boolean isImage = true;
                    if (realPath.endsWith(".jpg") || realPath.endsWith(".JPG")) {
                        response.setContentType("image/jpg");
                    } else if (realPath.endsWith(".jpeg") || realPath.endsWith(".JPEG")) {
                        response.setContentType("image/jpeg");
                    } else if (realPath.endsWith(".png") || realPath.endsWith(".PNG")) {
                        response.setContentType("image/png");
                    } else if (realPath.endsWith(".gif") || realPath.endsWith(".GIF")) {
                        response.setContentType("image/gif");
                    } else if (realPath.endsWith(".bmp") || realPath.endsWith(".BMP")) {
                        response.setContentType("image/bmp");
                    } else {
                        isImage = false;
                    }
                    if (isImage) {
                        InputStream inputStream = new FileInputStream(file);
                        FileCopyUtils.copy(inputStream, response.getOutputStream());
                    } else {
                        String contentType = URLConnection.guessContentTypeFromName(file.getName());
                        if (contentType == null) {
                            contentType = "application/octet-stream";
                        }
                        response.setContentType(contentType);
                        String userAgent = request.getHeader("User-Agent").toLowerCase();
                        if (userAgent.contains("safari") && !userAgent.contains("chrome")) {
                            //处理safari的乱码问题
                            byte[] bytesName = fileName.getBytes("UTF-8");
                            fileName = new String(bytesName, "ISO-8859-1");
                            response.setHeader("content-disposition", "attachment;fileName=" + fileName);
                        } else {
                            response.setHeader("content-disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
                        }
                        response.setContentLength((int) file.length());
                        InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
                        FileCopyUtils.copy(inputStream, response.getOutputStream());
                    }
                } else {
                    ResultData.outputStreamWrite("文件不存在！", response);
                }
            }
        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }

    /**
     * @throws
     * @Title: download
     * @Description: 附件下载接口
     * @param: @param request
     * @param: @return
     * @return: PlatformResult<Object>
     * @author: YueC
     * @date: 2020年2月2日 下午2:16:18
     */
    @ApiOperation(value = "附件下载接口", notes = "附件下载接口")
    @GetMapping("/attachment/downloadFile/{fileId}")
    public void download(HttpServletResponse response,HttpServletRequest request, @PathVariable("fileId") String fileId,String fileName) {
        try {
            if (!fileId.isEmpty()) {
                if (fileId.indexOf(".") > 0) {
                    fileId = fileId.substring(0, fileId.lastIndexOf("."));
                }
                Attachment attachment = attachmentService.selectByPrimaryKey(fileId);
                if(null == attachment) {
                	attachment = attachmentService.selectCommFileById(fileId);
                }
                
                String userCode = request.getParameter("userCode");
                String userName = request.getParameter("userName");
                
                //科室文档保存下载记录的文档
                documentDownloadLogService.insert(attachment,userCode,userName);
                
                String realPath = attachment.getRealPath();
                File file = new File(realPath);
                if (!file.exists()) {
                	ResultData.outputStreamWrite("文件不存在！", response);
                    return;
                }

                String contentType = URLConnection.guessContentTypeFromName(file.getName());
                if (contentType == null) {
                    System.out.println("mimetype is not detectable, will take default");
                    contentType = "application/octet-stream";
                }
                response.setContentType(contentType);
                //response.setHeader("Content-Disposition", String.format("attachment; filename=\"" + new String(attachment.getOriginalname().replace("@dollar@","$").getBytes("gb2312"),"ISO8859-1" ) +"\""));
                String userAgent = request.getHeader("User-Agent").toLowerCase();

                //String fileName = attachment.getOriginalName();
                //个人文档修改重命名后  下载文件没带后缀名称
                
                if(StringUtils.isBlank(fileName)) {
                	 String[] name = new String[0];
                     if (attachment.getOriginalName().contains(".")) {
                         name = attachment.getOriginalName().split("\\.");
                     }
                     if (name.length > 1) {//存在
                         fileName = attachment.getOriginalName();
                     } else {
                         fileName = attachment.getOriginalName() + "." + attachment.getFileExtension();
                     }
                     
                     //解决微信端附件下载文件名过长导致文件无法下载的问题
                     String requestSource = request.getParameter("source");
                 	if("mobile".equals(requestSource)) {
                 		fileName = attachment.getFileName();
                 	}
                }
                
                if (userAgent.contains("safari") && !userAgent.contains("chrome")) {
                    //处理safari的乱码问题
                    byte[] bytesName = fileName.getBytes("UTF-8");
                    fileName = new String(bytesName, "ISO-8859-1");
                    response.setHeader("content-disposition", "attachment;fileName=" + fileName);
                } else {
                    response.setHeader("content-disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
                }
                response.setContentLength((int) file.length());
                InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
                FileCopyUtils.copy(inputStream, response.getOutputStream());
            }

        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }
    
    /**
     * 
     * @param response
     * @param request
     * @param fileId
     */
    @ApiOperation(value = "附件下载接口", notes = "附件下载接口")
    @GetMapping("/preview/attachment/downloadFile/{fileId}")
    public void previewDownload(HttpServletResponse response,
                         HttpServletRequest request, @PathVariable("fileId") String fileId) {
        try {
            if (!fileId.isEmpty()) {
                if (fileId.indexOf(".") > 0) {
                    fileId = fileId.substring(0, fileId.lastIndexOf("."));
                }
                Attachment attachment = attachmentService.selectByPrimaryKey(fileId);
                
                String realPath = attachment.getRealPath();
                File file = new File(realPath);
                if (!file.exists()) {
                	ResultData.outputStreamWrite("文件不存在！", response);
                    return;
                }

                String contentType = URLConnection.guessContentTypeFromName(file.getName());
                if (contentType == null) {
                    System.out.println("mimetype is not detectable, will take default");
                    contentType = "application/octet-stream";
                }
                response.setContentType(contentType);
                //response.setHeader("Content-Disposition", String.format("attachment; filename=\"" + new String(attachment.getOriginalname().replace("@dollar@","$").getBytes("gb2312"),"ISO8859-1" ) +"\""));
                String userAgent = request.getHeader("User-Agent").toLowerCase();

                //String fileName = attachment.getOriginalName();
                //个人文档修改重命名后  下载文件没带后缀名称
                String[] name = new String[0];
                String fileName = "";
                if (attachment.getOriginalName().contains(".")) {
                    name = attachment.getOriginalName().split("\\.");
                }
                if (name.length > 1) {//存在
                    fileName = attachment.getOriginalName();
                } else {
                    fileName = attachment.getOriginalName() + "." + attachment.getFileExtension();
                }
                
                //解决微信端附件下载文件名过长导致文件无法下载的问题
                String requestSource = request.getParameter("source");
            	if("mobile".equals(requestSource)) {
            		fileName = attachment.getFileName();
            	}
                
                if (userAgent.contains("safari") && !userAgent.contains("chrome")) {
                    //处理safari的乱码问题
                    byte[] bytesName = fileName.getBytes("UTF-8");
                    fileName = new String(bytesName, "ISO-8859-1");
                    response.setHeader("content-disposition", "attachment;fileName=" + fileName);
                } else {
                    response.setHeader("content-disposition", "attachment;fileName=" + URLEncoder.encode(fileName, "UTF-8"));
                }
                response.setContentLength((int) file.length());
                InputStream inputStream = new BufferedInputStream(new FileInputStream(file));
                FileCopyUtils.copy(inputStream, response.getOutputStream());
            }

        } catch (IOException ioe) {
            ioe.printStackTrace();
        }
    }

    /**
     * @throws
     * @Title: selectByIds
     * @Description: 查询附件信息接口
     * @param: @param idsStr
     * @param: @return
     * @return: PlatformResult<List < Attachment>>
     * @author: YueC
     * @date: 2020年2月14日 下午2:50:03
     */
    @ApiOperation(value = "查询附件信息接口", notes = "查询附件信息接口")
    @GetMapping("/attachment/selectByIds")
    public PlatformResult<List<Attachment>> selectByIds(String idsStr) {
        String[] idsArray = idsStr.split(",");
        try {
        	List<String> fileList = new ArrayList<>();
        	//处理旧数据附件兼容性问题
        	for (String string : idsArray) {
				if(string.contains("/")) {
					fileList.add(string.split("/")[0]);
				}else {
					fileList.add(string);
				}
			}
            List<Attachment> list = attachmentService.selectByIds(fileList.toArray(new String[fileList.size()]));
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }
    
    /**
     * 
     * @Title: selectInternalByIds
     * @Description: TODO(这里用一句话描述这个方法的作用)
     * @param @param idsStr
     * @param @return 参数
     * @return PlatformResult<List<Attachment>> 返回类型
     * 2021年9月8日
     * ADMIN
     * @throws
     */
    @GetMapping("/attachment/selectInternalByIds")
    public PlatformResult<List<Attachment>> selectInternalByIds(String idsStr) {
        String[] idsArray = idsStr.split(",");
        try {
        	List<String> fileList = new ArrayList<>();
        	//处理旧数据附件兼容性问题
        	for (String string : idsArray) {
				if(string.contains("/")) {
					fileList.add(string.split("/")[0]);
				}else {
					fileList.add(string);
				}
			}
            List<Attachment> list = attachmentService.selectByIds(fileList.toArray(new String[fileList.size()]));
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 获取我的文档列表
     * @Date: 2020/2/23 17:20
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.document.model.Attachment>
     **/
    @ApiOperation(value = "获取我的文档列表", notes = "获取我的文档列表")
    @PostMapping("/attachment/getMyAttachment")
    public DataSet<Attachment> getMyAttachment(HttpServletRequest request,Page page, Attachment record) {

        List<Attachment> list = attachmentService.getMyAttachment(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }
    
    @ApiOperation(value = "文档模板", notes = "文档模板")
    @PostMapping("/attachment/getAttachmentByModuleName")
    public DataSet<Attachment> getAttachmentByModuleName(HttpServletRequest request,Page page, Attachment record) {

        List<Attachment> list = attachmentService.getAttachmentByModuleName(page, record);
        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
    }

    /**
     * @Author: Lizhihuo
     * @Description: 微信端获取我的文档列表 (包含我的、分享给我的)
     * @Date: 2020/2/23 17:20
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.document.model.Attachment>
     **/
    @ApiOperation(value = "微信端获取我的文档列表(包含我的、分享给我的)", notes = "微信端获取我的文档列表(包含我的、分享给我的)")
    @PostMapping("/attachment/getWXMyAttachment")
    public DataSet<Attachment> getWXMyAttachment(Page page, Attachment record) {
       try {
	    	List<Attachment> list = attachmentService.getWXMyAttachment(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 获取分享给我的文档列表
     * @Date: 2020/2/23 17:20
     * @Param:
     * @return: DataSet<Attachment>
     **/
    @ApiOperation(value = "获取分享给我的文档列表", notes = "获取分享给我的文档列表")
    @PostMapping("/attachment/getShareAttachment")
    public DataSet<Attachment> getShareAttachment(Page page, Attachment record) {
    	try {
	        List<Attachment> list = attachmentService.getShareAttachment(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: zouj
     * @Description: 获取我分享的文档列表
     * @Date: 2025/6/24 17:20
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.document.model.Attachment>
     **/
    @ApiOperation(value = "获取我分享的文档列表", notes = "获取我分享的文档列表")
    @PostMapping("/attachment/getMyShareAttachment")
    public DataSet<Attachment> getMyShareAttachment(Page page, Attachment record) {
    	try {
	        List<Attachment> list = attachmentService.getMyShareAttachment(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }
    
    /**
     * 批量撤销分享
     * @param recordList
     * @return
	 * @date 2025-06-23 15:52:53
	 * <AUTHOR>
     */
    @ApiOperation(value = "批量撤销分享", notes = "批量撤销分享")
    @PostMapping("/attachment/bacthCancle")
    public PlatformResult<String> bacthCancle(@RequestBody List<Attachment> recordList) {
    	try {
    		attachmentService.bacthCancle(recordList);
    		return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【撤销分享】失败，失败原因：" + e.getMessage());
        }
    }
    
    /**
     * 批量撤销分享
     * @param recordList
     * @return
	 * @date 2025-08-19 15:52:53
	 * <AUTHOR>
     */
    @ApiOperation(value = "批量删除分享给我的", notes = "批量删除分享给我的")
    @PostMapping("/attachment/bacthDelete")
    public PlatformResult<String> bacthDelete(@RequestBody List<Attachment> recordList) {
    	try {
    		attachmentService.bacthDelete(recordList);
    		return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【删除】失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 获取回收站文档列表
     * @Date: 2020/2/23 17:20
     * @Param:
     * @return: cn.trasen.BootComm.model.DataSet<cn.trasen.document.model.Attachment>
     **/
    @ApiOperation(value = "获取回收站文档列表", notes = "获取回收站文档列表")
    @PostMapping("/attachment/getRecoveryAttachment")
    public DataSet<Attachment> getRecoveryAttachment(Page page, Attachment record) {
    	try {
	        List<Attachment> list = attachmentService.getRecoveryAttachment(page, record);
	        return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
	    }catch(Exception e) {
			e.printStackTrace();
			logger.error(e.getMessage(), e);
			return null;
		}
    }

    /**
     * @Author: Lizhihuo
     * @Description: 查询附件类型
     * @Date: 2020/2/24 9:59
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.util.List < cn.trasen.document.model.Attachment>>
     **/
    @ApiOperation(value = "查询附件类型", notes = "查询附件类型")
    @GetMapping("/attachment/getFileExtension")
    public PlatformResult<List<Map<String, Object>>> getFileExtension(String moduleName) {
        try {
            List<Map<String, Object>> list = attachmentService.getFileExtension(moduleName);
            return PlatformResult.success(list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure();
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description: 修改附件信息
     * @Date: 2020/2/24 11:11
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "修改附件信息", notes = "修改附件信息")
    @PostMapping("/attachment/update")
    public PlatformResult<String> update(@RequestBody Attachment record) {
        try {
            attachmentService.update(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    
    /**
     * @Author: Lizhihuo
     * @Description: 修改附件信息
     * @Date: 2020/2/24 11:11
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "修改附件信息", notes = "修改附件信息")
    @PostMapping("/attachment/bacthUpdate")
    public PlatformResult<String> bacthUpdate(@RequestBody List<Attachment> list) {
        try {
            attachmentService.bacthUpdate(list);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }

    /**
     * @Author: Lizhihuo
     * @Description: 分享我的文档
     * @Date: 2020/2/24 17:21
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "分享我的文档", notes = "分享我的文档")
    @PostMapping("/attachment/shareAttachmentDocument")
    public PlatformResult<String> shareAttachmentDocument(@RequestBody Attachment record) {
        try {
            attachmentService.shareAttachmentDocument(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return PlatformResult.failure();
    }
    
    @ApiOperation(value = "批量分享", notes = "批量分享")
    @PostMapping("/attachment/bacthShareDocument")
    public PlatformResult<String> bacthShareDocument(@RequestBody List<Attachment> record) {
        try {
        	attachmentService.bacthShareDocument(record);
            return PlatformResult.success(null,"分享成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【分享】失败，失败原因：" + e.getMessage());
        }
    }
    
    @ApiOperation(value = "目录分享", notes = "目录分享")
    @PostMapping("/attachment/shareChannel")
    public PlatformResult<String> shareChannel(@RequestBody Attachment record) {
        try {
        	attachmentService.shareChannel(record);
            return PlatformResult.success(null,"分享成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【分享】失败，失败原因：" + e.getMessage());
        }
    }
    
    @ApiOperation(value = "批量移动", notes = "批量移动")
    @PostMapping("/attachment/bacthMoveDocument")
    public PlatformResult<String> bacthMoveDocument(@RequestBody List<Attachment> record) {
        try {
        	attachmentService.bacthMoveDocument(record);
            return PlatformResult.success(null,"移动成功");
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【分享】失败，失败原因：" + e.getMessage());
        }
    }

    /**
     * @Author: Lizhihuo
     * @Description:删除附件信息
     * @Date: 2020/2/24 11:30
     * @Param:
     * @return: cn.trasen.BootComm.utils.PlatformResult<java.lang.String>
     **/
    @ApiOperation(value = "删除附件信息", notes = "删除附件信息")
    @PostMapping("/attachment/operationAttachment")
    public PlatformResult<String> operationAttachment(@RequestBody Attachment record) {
        try {
            attachmentService.operationAttachment(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure("【删除】失败，失败原因：" + e.getMessage());
        }
    }
    
    
    @ApiOperation(value = "批量下载", notes = "批量下载")
    @RequestMapping(value = "/attachment/batchDownloadByIds", method = {RequestMethod.GET})
    public void batchDownloadByIds(@RequestParam("ids") String ids, String isAll,  HttpServletRequest request, HttpServletResponse response) throws IOException {
        
    	List<Attachment> fileAttachmentList = new ArrayList<>();
    	
    	if("Y".equals(isAll)) {
    		fileAttachmentList = attachmentService.selectAllByIds(ids.split(","));
    	}else {
    		fileAttachmentList = attachmentService.selectByIds(ids.split(","));
    	}
    	
        
        if(fileAttachmentList.size() == 1){
        	download(response,request,fileAttachmentList.get(0).getId(),"");
        }else{
        	 OutputStream os = null;
        	try {
	        	List<File> files = new ArrayList<>();
	        	
	            int i=0;
	            for (Attachment fileAttachment : fileAttachmentList) {
	                File file = new File(fileAttachment.getRealPath());
	                i++;
	                if (file.exists()) {
	                    String newFileName = file.getParent() + File.separator + fileAttachment.getOriginalName();
	                    for (File f : files) {
	                        if (FileUtil.getName(f).equals(FileUtil.getName(newFileName))) {
	                             newFileName = file.getParent() + File.separator + FileUtil.getPrefix(newFileName)+"_"+i+"."+fileAttachment.getFileExtension();
	                            break;
	                        }
	                    }
	
	                    //创建新名字的抽象文件
	                    File newfile = new File(newFileName);
	                    FileUtils.copyFile(file, newfile);
	                    files.add(newfile);
	
	                }
	            }
	            
                //科室文档保存下载记录的文档
                documentDownloadLogService.insert(fileAttachmentList.get(0),null,null);
            
                byte[] excelBy = ZipUtil.toZip(files);

                files.forEach(f->{
                    if(!f.delete()){
                    	logger.info("文件删除失败");
                    }
                });

                String name = new String("附件.zip".getBytes("UTF-8"), "ISO8859-1");
                response.setContentType("application/zip");
                response.setCharacterEncoding("UTF-8");
                response.setHeader("Content-disposition", "attachment; filename=" + name);
                os = response.getOutputStream();
                os.write(excelBy);
                os.flush();
            }catch(Exception e){
            	e.printStackTrace();
            	logger.error(e.getMessage(), e);
            } finally {
                if (os != null) {
                    os.close();
                }
            }
        }
        
    }
    
    
	/**
	 * @Description 收藏到个人文档
	 * @param record
	 * @return PlatformResult<String>
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增收藏", notes = "新增收藏")
	@PostMapping("/attachment/saveCollect")
	public PlatformResult<String> saveCollect(@RequestBody Attachment record) {
		try {
				Example example = new Example(Attachment.class);
				Example.Criteria criteria = example.createCriteria();
				criteria.andEqualTo("isDelete", "N");
				criteria.andEqualTo("collectId",record.getCollectId());
				criteria.andEqualTo("uploadUser",UserInfoHolder.getCurrentUserCode());
				List<Attachment> records = attachmentMapper.selectByExample(example);
				if(CollectionUtils.isNotEmpty(records)) {
					return PlatformResult.failure("已收藏到个人文档、请勿重复收藏");
				}else {
					attachmentService.saveCollect(record);
					return PlatformResult.success();
				}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("【收藏】失败，失败原因：" + e.getMessage());
		}
	}
	
	@ApiOperation(value = "附件库列表查询", notes = "附件库列表查询")
    @GetMapping("/attachment/selectAllAttachmentList")
    public DataSet<Attachment> selectAllAttachmentList(Page page,Attachment attachment) {
        try {
            List<Attachment> list = attachmentService.selectAllAttachmentList(page,attachment);
            return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), list);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return null;
        }
    }
	
	@ApiOperation(value = "合同审查保存（益阳三需求）", notes = "合同审查保存（益阳三需求）")
	@PostMapping("/attachment/saveContractReview")
	public PlatformResult<String> saveContractReview(@RequestBody Attachment record) {
		try {
			attachmentService.saveContractReview(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("失败，失败原因：" + e.getMessage());
		}
	}
	
	@ApiOperation(value = "合同审查查询（益阳三需求）", notes = "合同审查查询（益阳三需求）")
	@PostMapping("/attachment/getContractReview")
	public PlatformResult<Map<String,String>> getContractReview(@RequestBody Attachment record) {
		try {
			return PlatformResult.success(attachmentService.getContractReview(record));
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure("失败，失败原因：" + e.getMessage());
		}
	}
}
