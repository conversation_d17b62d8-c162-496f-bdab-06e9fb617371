<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.trasen.oa.document.dao.DocumentAccessoryMapper">
  <resultMap id="BaseResultMap" type="cn.trasen.oa.document.model.DocumentAccessory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="DOC_ID" jdbcType="VARCHAR" property="docId" />
    <result column="ISIMAGE" jdbcType="DECIMAL" property="isimage" />
    <result column="TYPE" jdbcType="VARCHAR" property="type" />
    <result column="NAME" jdbcType="VARCHAR" property="name" />
    <result column="SAVE_NAME" jdbcType="VARCHAR" property="saveName" />
    <result column="DOMAIN_ID" jdbcType="VARCHAR" property="domainId" />
    <result column="FILE_SIZE" jdbcType="VARCHAR" property="fileSize" />

    <result column="CREATE_USER" jdbcType="VARCHAR" property="createUser" />
    <result column="CREATE_USER_NAME" jdbcType="VARCHAR" property="createUserName" />
    <result column="CREATE_DEPT" jdbcType="VARCHAR" property="createDept" />
    <result column="CREATE_DEPT_NAME" jdbcType="VARCHAR" property="createDeptName" />
    <result column="CREATE_DATE" jdbcType="TIMESTAMP" property="createDate" />
    <result column="UPDATE_USER" jdbcType="VARCHAR" property="updateUser" />
    <result column="UPDATE_USER_NAME" jdbcType="VARCHAR" property="updateUserName" />
    <result column="UPDATE_DATE" jdbcType="TIMESTAMP" property="updateDate" />
    <result column="IS_DELETED" jdbcType="DECIMAL" property="isDeleted" />
    <result column="ORG_CODE" jdbcType="VARCHAR" property="orgCode" />
    <result column="HOSP_CODE" jdbcType="VARCHAR" property="hospCode" />

  </resultMap>

  <!--根据文档Id查询附件信息-->
  <select id="selectDocumentAccessoryByDocId" resultMap="BaseResultMap" parameterType="String">
  	SELECT * FROM TOA_DOCUMENT_ACCESSORY
  	WHERE DOC_ID = #{docId} AND NAME IS NOT NULL
  </select>

  <!--批量增加附件信息-->
  <insert id="batchInsert" parameterType="java.util.List">
   INTO TOA_DOCUMENT_ACCESSORY (ID, DOC_ID, ISIMAGE,
      TYPE, NAME, SAVE_NAME,FILE_SIZE,CREATE_USER,CREATE_USER_NAME,CREATE_DEPT,
      CREATE_DEPT_NAME,CREATE_DATE,IS_DELETED,HOSP_CODE)
      VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.docId,jdbcType=VARCHAR}, #{item.isimage,jdbcType=DECIMAL},
      #{item.type,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.saveName,jdbcType=VARCHAR}
      ,#{item.fileSize,jdbcType=VARCHAR},#{item.createUser,jdbcType=VARCHAR},
      #{item.createUserName,jdbcType=VARCHAR},#{item.createDept,jdbcType=VARCHAR},
      #{item.createDeptName,jdbcType=VARCHAR},#{item.createDate,jdbcType=TIMESTAMP},
      #{item.isDeleted,jdbcType=DECIMAL},#{item.hospCode,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <!--根据文档ID删除-->
  <delete id="deleteByDocId" parameterType="java.lang.String">
  	DELETE FROM TOA_DOCUMENT_ACCESSORY WHERE DOC_ID = #{docId}
  </delete>

  <select id="selectIsDocByAccId" resultType="cn.trasen.oa.document.model.DocumentAccessory" parameterType="java.lang.String">
    select * from toa_document_accessory where 1=1 and id=#{id}
  </select>
  
  <select id="selectFileSize"  parameterType="java.lang.String" resultType="java.lang.Long">
  		SELECT
			sum( t1.FILE_SIZE ) 
		FROM
			toa_document_accessory t1
			LEFT JOIN toa_document t2 ON t1.DOC_ID = t2.id 
		WHERE
			t1.IS_DELETED = 'N' 
			AND t2.IS_DELETED = 'N' 
			AND t2.CHANNEL_ID = #{channelId}
  </select>

</mapper>