package cn.trasen.oa.contract.controller;

import cn.hutool.core.convert.NumberChineseFormatter;
import cn.trasen.BootComm.excel.ExportExcelUtil;
import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.BootComm.excel.utils.ImportExcelUtil;
import cn.trasen.oa.contract.bean.contract.*;
import cn.trasen.oa.contract.constant.ContractConst;
import cn.trasen.oa.contract.model.ContractFund;
import cn.trasen.oa.contract.model.ContractItem;
import cn.trasen.oa.contract.service.ContractFundService;
import cn.trasen.oa.contract.service.ContractItemService;
import cn.trasen.oa.contract.util.Comm;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.TemplateExportParams;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.contract.model.Contract;
import cn.trasen.oa.contract.service.ContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @ClassName ContractController
 * @Description TODO
 * @date 2025年5月16日 下午5:55:48
 */
@RestController
@Api(tags = "ContractController")
@Validated
public class ContractController {

    private transient static final Logger logger = LoggerFactory.getLogger(ContractController.class);

    @Autowired
    private ContractService contractService;

    @Autowired
    private ContractFundService contractFundService;

    @Autowired
    private ContractItemService contractItemService;

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveContract
     * @Description 新增
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/api/ctt/contract/save")
    public PlatformResult<String> saveContract(@Validated @RequestBody ContractInsertReq record) {
        try {
            contractService.insert(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title saveContract
     * @Description 新增
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "新增草稿", notes = "新增草稿")
    @PostMapping("/api/ctt/contract/save/draft")
    public PlatformResult<String> saveContractDraft(@RequestBody ContractInsertReq record) {
        try {
            // 设置草稿状态
            record.getContract().setStatus(ContractConst.STATUS_DRAFT);
            if (record.getContract().getId() == null) {
                contractService.insert(record);
            } else {
                contractService.edit(record);
            }

            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    @ApiOperation(value = "合同状态变更", notes = "合同状态变更")
    @PostMapping("/api/ctt/contract/statusChange/{id}")
    public PlatformResult<String> statusChange(@PathVariable String id, @RequestBody ContractStatusChangeReq record) {
        try {

            contractService.statusChange(id, record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param record
     * @return PlatformResult<String>
     * @Title updateContract
     * @Description 编辑
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "编辑", notes = "编辑")
    @PostMapping("/api/ctt/contract/update")
    public PlatformResult<String> updateContract(@Validated @RequestBody ContractInsertReq record) {
        try {
            if (ContractConst.STATUS_DRAFT.equals(record.getContract().getStatus())) {
                record.getContract().setStatus(ContractConst.STATUS_SIGNED);
            }
            contractService.edit(record);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param id
     * @return PlatformResult<Contract>
     * @Title selectContractById
     * @Description 根据ID查询
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/api/ctt/contract/{id}")
    public PlatformResult selectContractById(@PathVariable String id) {
        try {
            Contract record = contractService.selectById(id);
            contractService.dataFmt(record);

            // 查询合同标的物
            List<ContractFund> contractFundList = contractFundService.getListByContractId(id);
            // 查询合同付款信息
            List<ContractItem> contractItemList = contractItemService.getListByContractId(id);

            ContractInsertReq contractInsertReq = new ContractInsertReq();
            contractInsertReq.setContract(record);
            contractInsertReq.setContractFundList(contractFundList);
            contractInsertReq.setContractItemList(contractItemList);

            return PlatformResult.success(contractInsertReq);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }


    /**
     * @param id
     * @return PlatformResult<String>
     * @Title deleteContractById
     * @Description 根据ID删除
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "删除")
    @PostMapping("/api/ctt/contract/delete/{id}")
    public PlatformResult<String> deleteContractById(@PathVariable String id) {
        try {
            contractService.deleteById(id);
            return PlatformResult.success();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return PlatformResult.failure(e.getMessage());
        }
    }

    /**
     * @param page
     * @param record
     * @return DataSet<Contract>
     * @Title selectContractList
     * @Description 查询列表
     * @date 2025年5月16日 下午5:55:48
     * <AUTHOR>
     */
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping("/api/ctt/contract/list")
    public DataSet<Contract> selectContractList(Page page, Contract record) {
        return contractService.getDataSetList(page, record);
    }


    @ApiOperation(value = "导出", notes = "导出")
    @GetMapping(value = "/api/ctt/contract/export")
    public ResponseEntity<byte[]> export(Contract record) throws IOException {

        try {

            List<Contract> exportList = contractService.getListNoPage(record);

            TemplateExportParams params = new TemplateExportParams(ExportUtil.convertTemplatePath("template/contract/contractExportTpl.xlsx"));
            params.setColForEach(true);
            Map<String, Object> map = new HashMap<>();
            map.put("list", exportList);
            // 获取当前年月日 字符串
            Workbook workbook = ExcelExportUtil.exportExcel(params, map);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

            try {
                workbook.write(outputStream);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                outputStream.close();
                workbook.close();
            }

            byte[] contents = outputStream.toByteArray();
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            String encodedFilename = new String("合同列表.xlsx".getBytes("UTF-8"), "ISO8859-1");
            headers.setContentDispositionFormData("attachment", encodedFilename);
            headers.setContentLength(contents.length);

            return new ResponseEntity<>(contents, headers, HttpStatus.OK);

        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(), e);
            return null;
        }
    }


    @GetMapping(value = "/api/ctt/contract/item/tpl/{modelType}")
    @ApiOperation(value = "合同标的物模板", notes = "合同标的物模板")
    public void tpl(HttpServletResponse response, @PathVariable String modelType) {
        try {

            String filename = "";
            String template = "";

            switch (modelType) {
                case ContractConst.MODEL_TYPE_WZCGHT:
                    filename = "物资采购合同标的物导入模板.xlsx";
                    template = "template/contract/contractWZCGHTImportTpl.xlsx";
                    break;
                case ContractConst.MODEL_TYPE_SBCGHT:
                    filename = "设备采购合同标的物导入模板.xlsx";
                    template = "template/contract/contractSBCGHTImportTpl.xlsx";
                    break;
                case ContractConst.MODEL_TYPE_SBWBHT:
                    filename = "设备维保合同标的物导入模板.xlsx";
                    template = "template/contract/contractSBWBHTImportTpl.xlsx";
                    break;
            }

            ExportExcelUtil exportExcelUtil = new ExportExcelUtil();
            ClassPathResource resource = new ClassPathResource(template);
            exportExcelUtil.downloadExportExcel(filename, response, resource);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @ApiOperation(value = "导入", notes = "导入")
    @PostMapping(value = "/api/ctt/contract/item/import/{modelType}")
    public PlatformResult importExcel(@RequestParam("file") MultipartFile file, @PathVariable String modelType) {

        try {

            switch (modelType) {
                case ContractConst.MODEL_TYPE_WZCGHT:

                    List<WZCGItem> wzcgItemList = (List<WZCGItem>) ImportExcelUtil.getExcelDatas(file, WZCGItem.class);

                    if (!CollectionUtils.isEmpty(wzcgItemList)) {
                        wzcgItemList.forEach(item -> {
                            String quantity = String.valueOf(Comm.chineseToNumber(item.getQuantity()));

                            BigDecimal q = new BigDecimal(String.valueOf(Comm.chineseToNumber(item.getQuantity())));
                            BigDecimal p = Comm.chineseRMBToNumber(item.getUnitprice());

                            item.setQuantity(quantity);
                            item.setUnitprice(p.toString());

                            // 计算总价
                            item.setAmount(q.multiply(p).toString());
                        });
                    }

                    return PlatformResult.success(wzcgItemList);

                case ContractConst.MODEL_TYPE_SBCGHT:
                    List<SBCGItem> sbcgItemList = (List<SBCGItem>) ImportExcelUtil.getExcelDatas(file, SBCGItem.class);

                    if (!CollectionUtils.isEmpty(sbcgItemList)) {
                        sbcgItemList.forEach(item -> {
                            String quantity = String.valueOf(Comm.chineseToNumber(item.getQuantity()));

                            BigDecimal q = new BigDecimal(String.valueOf(Comm.chineseToNumber(item.getQuantity())));
                            BigDecimal p = Comm.chineseRMBToNumber(item.getUnitprice());

                            item.setQuantity(quantity);
                            item.setUnitprice(p.toString());

                            // 计算总价
                            item.setAmount(q.multiply(p).toString());
                        });
                    }

                    return PlatformResult.success(sbcgItemList);

                case ContractConst.MODEL_TYPE_SBWBHT:
                    List<SBWBItem> sbwbItemList = (List<SBWBItem>) ImportExcelUtil.getExcelDatas(file, SBWBItem.class);

                    if (!CollectionUtils.isEmpty(sbwbItemList)) {
                        sbwbItemList.forEach(item -> {
                            String quantity = String.valueOf(Comm.chineseToNumber(item.getQuantity()));

                            BigDecimal q = new BigDecimal(String.valueOf(Comm.chineseToNumber(item.getQuantity())));
                            BigDecimal p = Comm.chineseRMBToNumber(item.getUnitprice());

                            item.setQuantity(quantity);
                            item.setUnitprice(p.toString());

                            // 计算总价
                            item.setAmount(q.multiply(p).toString());
                        });
                    }

                    return PlatformResult.success(sbwbItemList);

            }


        } catch (Exception e) {
            e.printStackTrace();
            return PlatformResult.failure("导入失败:" + e.getMessage());
        }

        return PlatformResult.failure("导入失败:未知的合同标的物类型");
    }


    @ApiOperation(value = "合同登记页头统计数据", notes = "合同登记页头统计数据")
    @PostMapping("/api/ctt/contract/pageHeadData")
    public PlatformResult getPageHeadData(Contract record) {
        return PlatformResult.success(contractService.getPageHeadData(record));
    }


}
