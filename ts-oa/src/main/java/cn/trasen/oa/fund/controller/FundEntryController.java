package cn.trasen.oa.fund.controller;

import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.BootComm.excel.utils.ExportUtil;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.oa.fund.dao.FundBudgetMapper;
import cn.trasen.oa.fund.dao.FundEntryMapper;
import cn.trasen.oa.fund.model.FundBudget;
import cn.trasen.oa.fund.model.FundEntry;
import cn.trasen.oa.fund.service.FundEntryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName FundEntryController
 * @Description TODO
 * @date 2022��9��26�� ����5:14:13
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "经费录入")
public class FundEntryController {

	private transient static final Logger logger = LoggerFactory.getLogger(FundEntryController.class);

	@Autowired
	private FundEntryService fundEntryService;

	@Autowired
	private FundEntryMapper mapper;
	
	@Autowired
	private	FundBudgetMapper fundBudgetMapper;
	
	
	/**
	 * @Title saveFundEntry
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/fundEntry/save")
	public PlatformResult<String> saveFundEntry(@RequestBody FundEntry record) {
		try {
			fundEntryService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * @Title updateFundEntry
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "查阅", notes = "查阅")
	@PostMapping("/api/fundEntry/consult")
	public PlatformResult<String> consult(@RequestBody FundEntry record) {
		try {
			fundEntryService.consult(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * @Title updateFundEntry
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/fundEntry/update")
	public PlatformResult<String> updateFundEntry(@RequestBody FundEntry record) {
		try {
			fundEntryService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectFundEntryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/fundEntry/{id}")
	public PlatformResult<Map<String, Object>> selectFundEntryById(@PathVariable String id) {
		try {
			Map<String, Object> map = fundEntryService.selectById(id);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title selectFundEntryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "审批详情接口", notes = "审批详情接口")
	@GetMapping("/api/fundEntry/selectApprovalDetails")
	public PlatformResult<Map<String, Object>> selectApprovalDetails(HttpServletRequest request, HttpServletResponse response,FundEntry record) {
		try {
			Map<String, Object> map = fundEntryService.selectApprovalDetails(record);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title selectFundEntryById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "根据项目id获取项目配套经费等信息", notes = "根据项目id获取项目配套经费等信息")
	@GetMapping("/api/fundEntry/ByItemId/{id}")
	public PlatformResult<Map<String, Object>> selectByItemId(@PathVariable String id) {
		try {
			Map<String, Object> map = fundEntryService.selectByItemId(id);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title selectFundEntryById
	 * @Description 经费统计
	 * @param id
	 * @return PlatformResult<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "经费统计", notes = "经费统计")
	@GetMapping("/api/fundEntry/selectFundStatistics")
	public PlatformResult<Map<String, Object>> selectFundStatistics(HttpServletRequest request, HttpServletResponse response,FundEntry record) {
		try {
			Map<String, Object> map = fundEntryService.selectFundStatistics(record);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title selectByItemId
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<FundUse>
	 * @date 2022��9��30�� ����9:11:37
	 * <AUTHOR>
	 */
	@ApiOperation(value = "经费统计——点击项目获取预算明细", notes = "经费统计——点击项目获取预算明细")
	@GetMapping("/api/fundEntry/getStatisticsDetailed")
	public PlatformResult<Map<String, Object>> getStatisticsDetailed(FundEntry record) {
		try {
			Map<String, Object> map = fundEntryService.selectByItemId(record.getId());
			List<FundBudget> fundBudgetList = fundBudgetMapper.selectByItemId(record.getId());
			map.put("fundBudgetList", fundBudgetList);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	/**
	 * 
	 * @Title deleteFundEntryById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/fundEntry/delete/{id}")
	public PlatformResult<String> deleteFundEntryById(@PathVariable String id) {
		try {
			fundEntryService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectFundEntryList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/fundEntry/list")
	public DataSet<FundEntry> selectFundEntryList(Page page, FundEntry record) {
		return fundEntryService.getDataSetList(page, record);
	}
	
	
	/**
	 * @Title selectFundEntryList
	 * @Description 获取当前用户负责项目  index:1 配套经费,2经费使用,3绩效报销  
	 * @param page
	 * @param record
	 * @return DataSet<FundEntry>
	 * @date 2022��9��26�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "获取当前用户负责项目", notes = "获取当前用户负责项目")
	@GetMapping("/api/fundEntry/getByleaderUserlist")
	public DataSet<FundEntry> getByleaderUserlist(Page page, FundEntry record) {
		return fundEntryService.getByleaderUserlist(page, record);
	}
	
	/**
	 * 
	 * @Title selectFundPagePending
	 * @Description 首页待办统计接口
	 * @param id
	 * @return PlatformResult<FundEntry>
	 * @date 2022��11��21�� ����5:14:13
	 * <AUTHOR>
	 */
	@ApiOperation(value = "首页待办统计接口", notes = "首页待办统计接口")
	@GetMapping("/api/fundEntry/selectFundPagePending")
	public PlatformResult<Map<String, Object>> selectFundPagePending(FundEntry record) {
		try {
			Map<String, Object> map = fundEntryService.selectFundPagePending(record);
			return PlatformResult.success(map);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	
	/**
	*
	* @Title exportHealthComplaints
	* @Description 导出建议管理
	* @param page
	* @param record
	* @return {@link DataSet< WfBaseButtonInfo>}
	* @date 2020年08月3日 14:48
	* <AUTHOR>
	*/
	//建议管理导出
	@ApiOperation(value = "导出经费录入", notes = "导出经费录入")
	@GetMapping("/api/fundEntry/exportfundEntry")
	public void exportfundEntry(Page page, HttpServletRequest request, HttpServletResponse response,FundEntry record) {
		//page.setPageSize(Integer.MAX_VALUE);
	
	// 导出文件名称
	String name = "经费录入项目.xls";
	
	// 模板位置
	String templateUrl = "template/fundEntry.xls";
	// 导出数据列表
		try {
			ThpsUser user = UserInfoHolder.getCurrentUserInfo();
	        record.setUserCode(user.getUsercode());
			List<FundEntry> list = mapper.getDataSetList(page,record);
			if (CollectionUtils.isNotEmpty(list)) {
				//修改数据类型
				for(int i = 0 ; i < list.size() ; i ++){
					FundEntry fundEntry=  list.get(0);
					String	status = String.valueOf(fundEntry.getStatus());
					String	statusName ="";
						if(record !=null  && record.getIndex() == 1){
							if("0".equals(status)){
								statusName="待提交";
							}if("1".equals(status)){
								statusName="待查阅";
							}if("3".equals(status)){
								statusName="待录入";
							}
						}
				
					if(record !=null  && record.getIndex() == 2){
						statusName="处理中";
					}
					if(record !=null  && record.getIndex() == 3){
						statusName="已完成";
					}
					
					fundEntry.setStatusName(statusName);
					
					Date createDate = fundEntry.getCreateDate();
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
					Date createDateFormat = dateFormat.parse(dateFormat.format(createDate)); 
					fundEntry.setCreateDate(createDateFormat);
				}
			    
				ExportUtil.export(request, response, list, name, templateUrl);
			} else {
				ExportUtil.export(request, response, Collections.EMPTY_LIST, name, templateUrl);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
