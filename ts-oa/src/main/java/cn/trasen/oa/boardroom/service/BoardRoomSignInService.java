package cn.trasen.oa.boardroom.service;

import cn.trasen.oa.boardroom.bean.*;
import cn.trasen.oa.boardroom.bo.BoardRoomSigninListInBO;
import cn.trasen.oa.boardroom.bo.BoardRoomSigninListOutBO;
import cn.trasen.oa.boardroom.model.BoardroomSignin;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/10/15 16:33
 */
public interface BoardRoomSignInService {
    /**
    * 获取基础数据列表
    * @param boardroomSigninListReq
    * @return java.util.List<cn.trasen.oa.boardroom.model.BoardroomSignin>
    * <AUTHOR>
    * @date 2021/10/28 10:14
    */
    List<BoardroomSignin> getBaseList(BoardroomSigninListReq boardroomSigninListReq);

    /**
     * 获取数据
     * @param boardroomSigninReq
     * @return java.util.List<cn.trasen.oa.boardroom.model.BoardroomSignin>
     * <AUTHOR>
     * @date 2021/10/28 10:14
     */
    BoardroomSignRes get(BoardroomSigninReq boardroomSigninReq);

    /**
     * 获取列表
     * @param boardroomSigninListReq
     * @return [cn.trasen.oa.boardroom.bean.BoardroomSigninListReq]
     * <AUTHOR>
     * @date 2021/10/21 14:48
     */
    List<BoardRoomSigninListOutBO> list(BoardRoomSigninListInBO boardroomSigninListInBO);

    /**
    * 获取列表
    * @param boardroomSigninListReq
    * @return [cn.trasen.oa.boardroom.bean.BoardroomSigninListReq]
    * <AUTHOR>
    * @date 2021/10/21 14:48
    */
    List<BoardRoomSigninListRes> getList(BoardroomSigninListReq boardroomSigninListReq);

    /**
     * 保存参会人员
     *
     * @param boardRoomSignInSaveReqList
     * @return [java.util.List<cn.trasen.oa.boardroom.bean.BoardRoomSignInSaveReq>]
     * <AUTHOR>
     * @date 2021/10/15 16:39
     */
    @Transactional(rollbackFor = Exception.class)
    void save(List<BoardRoomSignInSaveReq> boardRoomSignInSaveReqList);

    /**
    * 批量修改会议室ID
    * @param applyId
    * @param meetingTimeId
    * @return [java.lang.String, java.lang.String]
    * <AUTHOR>
    * @date 2021/10/21 17:05
    */
    @Transactional(rollbackFor = Exception.class)
    void updateMeetingId(String applyId, String meetingTimeId);

    /**
     * 签到
     *
     * @param meetingId
     * @return [java.lang.String] 1签到成功 2 已经签到过
     * <AUTHOR>
     * @date 2021/10/21 10:35
     */
    @Transactional(rollbackFor = Exception.class)
    BoardroomSignRes signIn(String meetingId);

    /**
     * 签到
     *
     * @param meetingId invitee 1 邀请，0所有
     * @return [java.lang.String] 1签到成功 2 已经签到过 -1不在邀请范围内
     * <AUTHOR>
     * @date 2021/10/21 10:35
     */
    @Transactional(rollbackFor = Exception.class)
    BoardroomSignRes signIn(String meetingId, Integer invitee);

    /**
     * 签退
     *
     * @param meetingId
     * @return [java.lang.String] 1签到成功 2 已经签到过
     * <AUTHOR>
     * @date 2021/10/21 10:26
     */
    @Transactional(rollbackFor = Exception.class)
    BoardroomSignRes signOut(String meetingId);

    /**
     * 签退
     *
     * @param meetingId
     * @return [java.lang.String] 1签到成功 2 已经签到过
     * <AUTHOR>
     * @date 2021/10/21 10:26
     */
    @Transactional(rollbackFor = Exception.class)
    BoardroomSignRes signOut(String meetingId, Integer invitee);

    /**
    * 请假
    * @param boardRoomMeetingLeaveReq
    * @return [cn.trasen.oa.boardroom.bean.BoardRoomMeetingLeaveReq]
    * <AUTHOR>
    * @date 2021/10/21 11:35
    */
    @Transactional(rollbackFor = Exception.class)
    void leave(BoardRoomMeetingLeaveReq boardRoomMeetingLeaveReq);

    /**
     * 获取统计
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/21 16:21
     */
    List<BoardRoomMeetingSigninCountRes> getCount(List<String> meetingIdList);

    /**
     * 获取统计
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @date 2021/10/21 16:21
     */
    BoardroomSigninCountRes getCount(String meetingId);

    /**
     * 导出
     *
     * @param meetingId
     * @return [java.lang.String]
     * <AUTHOR>
     * @param signinStatus 
     * @date 2021/10/22 15:53
     */
    byte[] export(String meetingId, String signinStatus) throws IOException;
}
