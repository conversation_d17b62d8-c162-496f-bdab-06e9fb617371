package cn.trasen.oa.boardroom.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.trasen.homs.core.model.BaseBean;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * @description: 预订
 * @return:
 * @author: liyuan
 * @createTime: 2021/10/14 11:24
 */
@Table(name = "TOA_BOARDROOM_APPLY")
@Setter
@Getter
public class BoardroomApply  extends BaseBean {


    /**
     * 审核状态，默认0，1通过，2 不通过
     */
    @Column(name = "STATUS")
    @ApiModelProperty(value = "审核状态，默认0，1通过，-1 不通过，3 撤销")
    private String status;

    /**
     * 会议室ID
     */
    @Column(name = "BOARDROOM_ID")
    @ApiModelProperty(value = "会议室ID")
    private String boardroomId;

    /**
     * 会议主题
     */
    @Column(name = "MOTIF")
    @ApiModelProperty(value = "会议主题")
    private String motif;


    /**
     * 会议主题1显示2隐藏
     */
    @Column(name = "MOTIF_TYPE")
    @ApiModelProperty(value = "会议主题1显示2隐藏")
    private String motifType;


    /**
     * 人数
     */
    @Column(name = "CONTROL_NUMBER")
    @ApiModelProperty(value = "人数")
    private String controlNumber;
    /**
     * 会议期效
     */
    @Column(name = "MEETING_TERM")
    @ApiModelProperty(value = "会议期效")
    private String meetingTerm;


    /**
     * 主持人
     */
    @Column(name = "EMCEE")
    @ApiModelProperty(value = "主持人")
    private String emcee;

    /**
     * 主持人名字
     */
    @Column(name = "EMCEE_NAME")
    @ApiModelProperty(value = "主持人名字")
    private String emceeName;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Column(name = "START_TIME")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @Column(name = "END_TIME")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 预定时间
     */
    @Column(name = "DESTINE_DATE")
    @ApiModelProperty(value = "预定时间")
    private Date destineDate;

    /**
     * 预定人ID
     */
    @Column(name = "APPLY_EMP")
    @ApiModelProperty(value = "预定人ID")
    private String applyEmp;

    /**
     * 预定人姓名
     */
    @Column(name = "APPLY_EMPNAME")
    @ApiModelProperty(value = "预定人姓名")
    private String applyEmpname;

    /**
     * 预定部门ID
     */
    @Column(name = "APPLY_ORG")
    @ApiModelProperty(value = "预定部门ID")
    private String applyOrg;

    /**
     * 预定部门
     */
    @Column(name = "APPLY_ORGNAME")
    @ApiModelProperty(value = "预定部门")
    private String applyOrgname;

    /**
     * 短信
     */
    @Column(name = "MSG")
    @ApiModelProperty(value = "短信")
    private String msg;

    /**
     * 描述
     */
    @Column(name = "DEPICT")
    @ApiModelProperty(value = "描述")
    private String depict;

    /**
     * 会议记录人
     */
    @Column(name = "NOTEPERSON")
    @ApiModelProperty(value = "会议记录人")
    private String noteperson;

    /**
     * 文件数字
     */
    @Column(name = "FILE_NUMBER")
    @ApiModelProperty(value = "文件数字")
    private String fileNumber;

    /**
     * 会议类型ID
     */
    @Column(name = "APPTYPEID")
    @ApiModelProperty(value = "会议类型ID")
    private String appTypeId;



    /**
     * 是否同步
     */
    @Column(name = "IS_SYNCH")
    @ApiModelProperty(value = "是否同步")
    private String isSynch;


    /**
     * 联系电话
     */
    @Column(name = "LINKTELE_PHONE")
    @ApiModelProperty(value = "联系电话")
    private String linktelePhone;

    /**
     * 联系人
     */
    @Column(name = "LINKTELE_PERSON")
    @ApiModelProperty(value = "联系人")
    private String linktelePerson;

    /**
     * 申请日期
     */
    @Column(name = "APPLY_DATE")
    @ApiModelProperty(value = "申请日期")
    private Date applyDate;

    /**
     * 出席人数
     */
    @Column(name = "PERSON_NUM")
    @ApiModelProperty(value = "出席人数")
    private String personNum;

    /**
     * 会议记录人名
     */
    @Column(name = "NOTEPERSONNAME")
    @ApiModelProperty(value = "会议记录人名")
    private String notepersonname;

    /**
     * 备注
     */
    @Column(name = "REMARK")
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 地址
     */
    @Column(name = "ADDR")
    @ApiModelProperty(value = "地址")
    private String addr;

    /**
     * 流程ID
     */
    @Column(name = "EVENTID")
    @ApiModelProperty(value = "流程ID")
    private String eventid;



    /**
     * 机构编码
     */
    @Column(name = "ORG_CODE")
    @ApiModelProperty(value = "机构编码")
    private String orgCode;

    /**
     * 院区编码
     */
    @Column(name = "HOSP_CODE")
    @ApiModelProperty(value = "院区编码")
    private String hospCode;

    /**
     * 创建机构
     */
    @Column(name = "CREATE_DEPT")
    @ApiModelProperty(value = "创建机构")
    private String createDept;

    /**
     * 创建机构名称
     */
    @Column(name = "CREATE_DEPT_NAME")
    @ApiModelProperty(value = "创建机构名称")
    private String createDeptName;



    /**
     * 附件id
     */
    @Column(name = "ACCESSORY_ID")
    @ApiModelProperty(value = " 附件id")
    private String accessoryId;


    /**
     * 附件name
     */
    @Column(name = "ACCESSORY_NAME")
    @ApiModelProperty(value = " 附件name")
    private String accessoryName;

    /**
     * 附件name
     */
    @Column(name = "ACCESSORY_URL")
    @ApiModelProperty(value = " 附件url")
    private String accessoryUrl;
    
    /**
     * 1 刷新 2不刷新
     */
    @ApiModelProperty(value = "1 刷新 2不刷新")
    @Column(name = "auto_refresh_signin_qrcode_type")
    private Integer autoRefreshSignInQrCodeType;

    /**
     * 1 刷新 2不刷新
     */
    @ApiModelProperty(value = "刷新时间")
    @Column(name = "auto_refresh_signin_qrcode_date")
    private Integer autoRefreshSignInQrCodeDate;

    /**
     * 业务id
     */
    @Column(name = "BUSINESS_ID")
    private String businessId;

    /**
     * 多个会议室名字
     */
    @Column(name = "ROOM_NAMES")
    private String roomNames;
    
    
    /**
     * 需要的设备
     */
    @Column(name = "device")
    private String device;  


    private Integer repeatRate;

    /**
     * 重复结束时间
     */
    private Date repeatEndTime;


    private Integer signOutType;

    private Integer signOutAdvanceMinute;


    private Integer sendRemindType;

    private Integer sendRemindAdvanceMinute;


    /**
     * 审核处理时间
     */
    private Date checkTime;

    /**
     * 最后消息提醒时间
     */
    @Column(name = "send_info_last_time")
    Date sendInfoLastTime;
    
    @Column(name = "sso_org_code")
    private String ssoOrgCode;
    
    /**
     * 参会人员(手动输入)
     */
    @Column(name = "attend_employee_input")
    @ApiModelProperty(value = "参会人员(手动输入)")
    private String attendEmployeeInput;
}