package cn.trasen.oa.check.controller;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.oa.check.model.CheckOperationLogs;
import cn.trasen.oa.check.service.CheckOperationLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * @ClassName CheckOperationLogsController
 * @Description TODO
 * @date 2021��8��11�� ����4:46:21
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@Api(tags = "安全检查-操作日志")
public class CheckOperationLogsController {

	private transient static final Logger logger = LoggerFactory.getLogger(CheckOperationLogsController.class);

	@Autowired
	private CheckOperationLogsService checkOperationLogsService;

	/**
	 * @Title saveCheckOperationLogs
	 * @Description 新增
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��8��11�� ����4:46:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "新增", notes = "新增")
	@PostMapping("/api/CheckOperationLogs/save")
	public PlatformResult<String> saveCheckOperationLogs(@RequestBody CheckOperationLogs record) {
		try {
			checkOperationLogsService.save(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title updateCheckOperationLogs
	 * @Description 编辑
	 * @param record
	 * @return PlatformResult<String>
	 * @date 2021��8��11�� ����4:46:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "编辑", notes = "编辑")
	@PostMapping("/api/CheckOperationLogs/update")
	public PlatformResult<String> updateCheckOperationLogs(@RequestBody CheckOperationLogs record) {
		try {
			checkOperationLogsService.update(record);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * 
	 * @Title selectCheckOperationLogsById
	 * @Description 根据ID查询
	 * @param id
	 * @return PlatformResult<CheckOperationLogs>
	 * @date 2021��8��11�� ����4:46:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "详情", notes = "详情")
	@GetMapping("/api/CheckOperationLogs/{id}")
	public PlatformResult<CheckOperationLogs> selectCheckOperationLogsById(@PathVariable String id) {
		try {
			CheckOperationLogs record = checkOperationLogsService.selectById(id);
			return PlatformResult.success(record);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
	
	
	/**
	 * 
	 * @Title deleteCheckOperationLogsById
	 * @Description 根据ID删除
	 * @param id
	 * @return PlatformResult<String>
	 * @date 2021��8��11�� ����4:46:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "删除", notes = "删除")
	@PostMapping("/api/CheckOperationLogs/delete/{id}")
	public PlatformResult<String> deleteCheckOperationLogsById(@PathVariable String id) {
		try {
			checkOperationLogsService.deleteById(id);
			return PlatformResult.success();
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}

	/**
	 * @Title selectCheckOperationLogsList
	 * @Description 查询列表
	 * @param page
	 * @param record
	 * @return DataSet<CheckOperationLogs>
	 * @date 2021��8��11�� ����4:46:21
	 * <AUTHOR>
	 */
	@ApiOperation(value = "列表", notes = "列表")
	@GetMapping("/api/CheckOperationLogs/list")
	public DataSet<CheckOperationLogs> selectCheckOperationLogsList(Page page, CheckOperationLogs record) {
		return checkOperationLogsService.getDataSetList(page, record);
	}
	
	@ApiOperation(value = "根据发起检查id查询操作详情", notes = "根据发起检查id查询操作详情")
	@GetMapping("/api/getCheckOperationLogsByCheckId/{startCheckId}")
	public PlatformResult<List<CheckOperationLogs>> getCheckOperationLogsByCheckId(@PathVariable String startCheckId) {
		try {
			List<CheckOperationLogs> records = checkOperationLogsService.getCheckOperationLogsByCheckId(startCheckId);
			return PlatformResult.success(records);
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return PlatformResult.failure(e.getMessage());
		}
	}
}
