package cn.trasen.oa.gov.service;


import java.util.List;

import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.oa.gov.model.GovTemplateFile;

/**
 * @Description: 发文模板设置 Service
 * @Date: 2020/4/22 18:05
 * @Author: Lizh
 * @Company: 湖南创星
 */
public interface GovTemplateFileService {

    /**
     * <p> @Title: insert</p>
     * <p> @Description: 新增</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int insert(GovTemplateFile entity);

    /**
     * <p> @Title: update</p>
     * <p> @Description: 修改</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int update(GovTemplateFile entity);

    /**
     * <p> @Title: deleted</p>
     * <p> @Description: 删除</p>
     * <p> @Return: int</p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    int deleted(GovTemplateFile entity);

    /**
     * <p> @Title: getDataList</p>
     * <p> @Description: 列表</p>
     * <p> @Return: List<govSendDocnum></p>
     * <p> <AUTHOR>
     * <P> @Date: 2020年4月23日  下午1:49:22 </p>
     */
    List<GovTemplateFile> getDataList(Page page, GovTemplateFile entity);

    /**
     * @Author: Lizhihuo
     * @Description: 判断名称是否重复
     * @Date: 2020/5/27 15:46
     * @Param: 
     * @return: cn.trasen.gov.model.GovTemplateFile
     **/
    GovTemplateFile judgeFileNameIsRepeated(GovTemplateFile entity);
}
