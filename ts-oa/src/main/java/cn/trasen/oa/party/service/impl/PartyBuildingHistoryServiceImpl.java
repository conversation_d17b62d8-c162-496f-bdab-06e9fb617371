package cn.trasen.oa.party.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import cn.trasen.homs.bean.oa.NoticeReq;
import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.contants.Contants;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.identifier.IdGeneraterUtils;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.properties.AppConfigProperties;
import cn.trasen.homs.core.utils.UserInfoHolder;
import cn.trasen.homs.feign.oa.InformationFeignService;
import cn.trasen.oa.party.dao.PartyBuildingBranchMapper;
import cn.trasen.oa.party.dao.PartyBuildingBranchMemberMapper;
import cn.trasen.oa.party.dao.PartyBuildingHistoryMapper;
import cn.trasen.oa.party.dao.PartyBuildingManageMapper;
import cn.trasen.oa.party.model.PartyBuildingBranch;
import cn.trasen.oa.party.model.PartyBuildingBranchMember;
import cn.trasen.oa.party.model.PartyBuildingHistory;
import cn.trasen.oa.party.model.PartyBuildingManage;
import cn.trasen.oa.party.service.PartyBuildingBranchMemberService;
import cn.trasen.oa.party.service.PartyBuildingHistoryService;
import cn.trasen.oa.party.service.PartyBuildingManageService;
import tk.mybatis.mapper.entity.Example;

/**
 * @ClassName PartyBuildingHistoryServiceImpl
 * @Description TODO
 * @date 2023��7��7�� ����3:07:00
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class, readOnly = true)
public class PartyBuildingHistoryServiceImpl implements PartyBuildingHistoryService {

	@Autowired
	private PartyBuildingHistoryMapper mapper;
	
	@Autowired
	private PartyBuildingBranchMemberService  partyBuildingBranchMemberService;
	
	@Autowired
	private PartyBuildingBranchMemberMapper  branchMemberMapper;
	
	@Autowired
	private PartyBuildingManageMapper  partyBuildingManageMapper;

	@Autowired
	private PartyBuildingManageService partyBuildingManageService;
	
	@Autowired
	private PartyBuildingBranchMapper partyBuildingBranchMapper;
	
	@Autowired
	AppConfigProperties appConfigProperties;
	
	@Autowired
    private InformationFeignService informationFeignClient;

	SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");//可以方便地修改日期格式
	
	@Transactional(readOnly = false)
	@Override
	public Integer save(PartyBuildingHistory record) {
		record.setId(IdGeneraterUtils.nextId());
		record.setCreateDate(new Date());
		record.setUpdateDate(new Date());
		record.setIsDeleted("N");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setCreateUser(user.getUsercode());
			record.setCreateUserName(user.getUsername());
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//新增该成员为历史党员，删除原支部该成员
	/*	PartyBuildingBranchMember branchMember = new PartyBuildingBranchMember();
		branchMember.setBranchId(record.getHistoryBranchId());
		branchMember.setMemberCode(record.getHistoryUserCode());
		branchMember.setIsDeleted("Y");//删除该支部历史党员
		partyBuildingBranchMemberService.updateByBranchMemberCode(branchMember);*///有可能因为多次操作，支部已经变化
		PartyBuildingBranchMember  branchMember= branchMemberMapper.selectByCode(record.getHistoryUserCode());//查询该成员支部信息、然后更新
		if (null != branchMember){
		branchMember.setIsDeleted("Y");//删除该支部历史党员
		branchMemberMapper.updateByPrimaryKeySelective(branchMember);
		}
		String reduceStatusContent ="";
		if(null != record.getReduceStatus()){
			if(record.getReduceStatus() == 3){
				reduceStatusContent ="已死亡";
			}else if(record.getReduceStatus() == 4){
				reduceStatusContent ="已停止党籍";
			}else if(record.getReduceStatus() == 5){
				reduceStatusContent ="已出党";
			}else if(record.getReduceStatus() == 6){
				reduceStatusContent ="其他";
			}
		}
		String content = record.getHistoryUserName()+dateFormat.format(record.getDepartureDate())+"已被"+reduceStatusContent+"请知悉！";//消息
	    String subject = "党员管理—历史党员提醒";
	    String url =appConfigProperties.getWxDomain() + "";//微信端地址
	    if(StringUtils.isNotBlank(record.getHistoryBranchId())){
	    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getHistoryBranchId());
	    	if(null != partyBuildingBranch){
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
	    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
	    		}if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
	    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
	    		}
	    	}
	    }
	    
		PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(record.getHistoryUserCode());//查询党员管理是否有该成员，更新成员支部信息
		if(null !=partyBuildingManage){
			partyBuildingManage.setBranchId("");//是否置空
			partyBuildingManage.setOrganizationId("");//是否置空
			partyBuildingManage.setType(record.getType());
			partyBuildingManage.setManageStatus(6);
			partyBuildingManage.setPartyBuildingStatus(record.getReduceStatus());//党籍状态，历史方式
			partyBuildingManage.setIsHistory(1);
			partyBuildingManageService.update(partyBuildingManage);
		}
		return mapper.insertSelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer update(PartyBuildingHistory record) {
		record.setUpdateDate(new Date());
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		//恢复历史党员，原支部添加该成员
		/*PartyBuildingBranchMember branchMember = new PartyBuildingBranchMember();
		branchMember.setBranchId(record.getHistoryBranchId());
		branchMember.setMemberCode(record.getHistoryUserCode());
		branchMember.setIsDeleted("N");//恢复该支部历史党员
		partyBuildingBranchMemberService.updateByBranchMemberCode(branchMember);*/
		PartyBuildingBranchMember  branchMember= branchMemberMapper.selectByCode(record.getHistoryUserCode());//查询该成员支部信息、然后更新
		if (null != branchMember){
		branchMember.setBranchId(record.getHistoryBranchId());
		branchMemberMapper.updateByPrimaryKeySelective(branchMember);
		}else{
		PartyBuildingBranchMember branchMemberrecord = new PartyBuildingBranchMember();
		branchMemberrecord.setMemberCode(record.getHistoryUserCode());
		branchMemberrecord.setMemberName(record.getHistoryUserName());
		branchMemberrecord.setBranchId(record.getHistoryBranchId());
		partyBuildingBranchMemberService.save(branchMemberrecord);
		}
		
		PartyBuildingManage partyBuildingManage = partyBuildingManageMapper.selectByUserCode(record.getHistoryUserCode());//查询党员管理是否有该成员，更新成员支部信息
		if(null !=partyBuildingManage){
			partyBuildingManage.setBranchId(record.getHistoryBranchId());
			PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getHistoryBranchId());
			if(partyBuildingBranch != null){
				partyBuildingManage.setOrganizationId(partyBuildingBranch.getOrganizationId());
			}
			partyBuildingManage.setType(record.getType());
			partyBuildingManage.setManageStatus(6);
			partyBuildingManage.setPartyBuildingStatus(record.getReduceStatus());//党籍状态，历史方式
			if(null != record.getHistoryStatus() && record.getHistoryStatus()==1){
			 partyBuildingManage.setIsHistory(0);//恢复了，是否清空历史党员标识
			}else{
			 partyBuildingManage.setIsHistory(1);
			}
			partyBuildingManageService.update(partyBuildingManage);
		}
		
		
		String content = record.getHistoryUserName()+dateFormat.format(record.getRecoveryDate())+"已恢复党组织，请知悉！";//恢复消息
	    String subject = "党员管理—历史党员恢复提醒";
	    String url =appConfigProperties.getWxDomain() + "";//微信端地址
	    if(StringUtils.isNotBlank(record.getHistoryBranchId())){
	    	PartyBuildingBranch partyBuildingBranch = partyBuildingBranchMapper.selectBybranchId(record.getHistoryBranchId());
	    	if(null != partyBuildingBranch){
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getSecretary())){
	    			sendMessage(content, partyBuildingBranch.getSecretary(), url, subject,"3");//给支部书记发送信息
	    		}
	    		if(StringUtils.isNotBlank(partyBuildingBranch.getTeamSecretaryCode())){
	    			sendMessage(content, partyBuildingBranch.getTeamSecretaryCode(), url, subject,"3");//给班子总书记发送信息
	    		}
	    	}
	    }
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Transactional(readOnly = false)
	@Override
	public Integer deleteById(String id) {
		Assert.hasText(id, "ID不能为空.");
		PartyBuildingHistory record = new PartyBuildingHistory();
		record.setId(id);
		record.setUpdateDate(new Date());
		record.setIsDeleted("Y");
		ThpsUser user = UserInfoHolder.getCurrentUserInfo();
		if (user != null) {
			record.setUpdateUser(user.getUsercode());
			record.setUpdateUserName(user.getUsername());
		}
		
		return mapper.updateByPrimaryKeySelective(record);
	}

	@Override
	public PartyBuildingHistory selectById(String id) {
		Assert.hasText(id, "ID不能为空.");
		return mapper.selectByPrimaryKey(id);
	}

	@Override
	public DataSet<PartyBuildingHistory> getDataSetList(Page page, PartyBuildingHistory record) {
		Example example = new Example(PartyBuildingHistory.class);
		Example.Criteria criteria = example.createCriteria();
		criteria.andEqualTo(Contants.IS_DELETED_FIELD, "N");
		if(StringUtils.isNotBlank(record.getHistoryUserName())){
			criteria.andLike("historyUserName", "%" + record.getHistoryUserName() + "%");
		}
		if(record.getHistoryStatus() != null){
			criteria.andEqualTo("historyStatus",record.getHistoryStatus());
		}
		if(record.getType() != null){
			criteria.andEqualTo("type",record.getType());
		}
		if(record.getReduceStatus() != null){
			criteria.andEqualTo("reduceStatus",record.getReduceStatus());
		}
		if(StringUtils.isNotBlank(record.getDepartureStartDate()) && StringUtils.isNotBlank(record.getDepartureEndDate())){
			criteria.andBetween("departureDate", record.getDepartureStartDate(), record.getDepartureEndDate());
		}
		if(StringUtils.isNotBlank(record.getRecoveryStartDate()) && StringUtils.isNotBlank(record.getRecoveryEndDate())){
			criteria.andBetween("recoveryDate", record.getRecoveryStartDate(), record.getRecoveryEndDate());
		}
		 boolean isInfoAdmin = UserInfoHolder.getRight("PARTY_BUILDING");//党建管理员
	        if(isInfoAdmin){
	        }else{
	        	criteria.andEqualTo("createUser",UserInfoHolder.getCurrentUserCode());
	        }
		List<PartyBuildingHistory> records = mapper.selectByExampleAndRowBounds(example, page);
		return new DataSet<>(page.getPageNo(), page.getPageSize(), page.getTotalPages(), page.getTotalCount(), records);
	}
	
	
	 private void sendMessage(String content,String receiver,String url,String subject,String noticeType) {
	        NoticeReq notice = NoticeReq.builder()
	                .content(content)
	                .noticeType(noticeType)
	                .receiver(receiver)  //接收人
	                .sender(UserInfoHolder.getCurrentUserCode())//发送人
	                .senderName(UserInfoHolder.getCurrentUserName())//发送人name
	                .subject(subject)
	                .url(url)
	                .wxSendType("1")
	                .toUrl("/ts-web-political/party-member-management/history-member").source("智慧党建")
	                .build();
	         informationFeignClient.sendNotice(notice);
	    }
}
