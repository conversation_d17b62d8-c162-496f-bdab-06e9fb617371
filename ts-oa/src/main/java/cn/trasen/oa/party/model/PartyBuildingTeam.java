package cn.trasen.oa.party.model;

import io.swagger.annotations.*;

import java.util.Date;
import java.util.List;

import javax.persistence.*;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.*;

@Table(name = "toa_party_building_team")
@Setter
@Getter
public class PartyBuildingTeam {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @Id
    private String id;

    /**
     * 党组织id
     */
    @Column(name = "organization_id")
    @ApiModelProperty(value = "党组织id")
    private String organizationId;

    /**
     * 当选日期
     */
    @Column(name = "team_date")
    @ApiModelProperty(value = "当选日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date teamDate;

    /**
     * 任期年限
     */
    @Column(name = "team_life")
    @ApiModelProperty(value = "任期年限")
    private String teamLife;

    /**
     * 期满日期
     */
    @Column(name = "team_expire")
    @ApiModelProperty(value = "期满日期")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date teamExpire;
    
    @Column(name = "team_secretary")
    @ApiModelProperty(value = "书记")
    private String teamSecretary;
    
    
    @Column(name = "team_deputy_secretary")
    @ApiModelProperty(value = "副书记")
    private String teamDeputySecretary;
    
    @Column(name = "team_secretary_code")
    @ApiModelProperty(value = "书记code")
    private String teamSecretaryCode;
    
    @Column(name = "team_deputy_secretary_code")
    @ApiModelProperty(value = "副书记code")
    private String teamDeputySecretaryCode;
    
    @Column(name = "team_numbers")
    @ApiModelProperty(value = "班子成员数")
    private Integer teamNmbers;

    /**
     * 备注
     */
    @Column(name = "team_remark")
    @ApiModelProperty(value = "备注")
    private String teamRemark;

    /**
     * 附件
     */
    @Column(name = "team_files")
    @ApiModelProperty(value = "附件")
    private String teamFiles;

    /**
     * 创建时间
     */
    @Column(name = "create_date")
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value = "创建人")
    private String createUser;

    /**
     * 创建人名称
     */
    @Column(name = "create_user_name")
    @ApiModelProperty(value = "创建人名称")
    private String createUserName;

    /**
     * 更新人
     */
    @Column(name = "update_user")
    @ApiModelProperty(value = "更新人")
    private String updateUser;

    /**
     * 更新人名称
     */
    @Column(name = "update_user_name")
    @ApiModelProperty(value = "更新人名称")
    private String updateUserName;

    /**
     * 更新时间
     */
    @Column(name = "update_date")
    @ApiModelProperty(value = "更新时间")
    private Date updateDate;

    /**
     * 删除标示
     */
    @Column(name = "is_deleted")
    @ApiModelProperty(value = "删除标示")
    private String isDeleted;
    
    /**
     * 是否超期换届 1超期,0正常
     */
    @Column(name = "expired_term")
    @ApiModelProperty(value = "是否超期换届")
    private Integer expiredTerm;
    
    @Transient
    @ApiModelProperty(value = "班子成员")
    List<PartyBuildingTeamUser> teamUserList;
    
    @Transient
    @ApiModelProperty(value = "班子届数")
    private String teamNo;
    
    @Transient
    @ApiModelProperty(value = "开始时间（查询用）")
    private String teamStartDate;
    
    @Transient
    @ApiModelProperty(value = "结束时间（查询用）")
    private String teamEndDate;
    
    @Transient
    @ApiModelProperty(value = "党组织全称")
    private String organizationName;
    
    @Transient
    @ApiModelProperty(value = "党组织简称")
    private String organizationShortName;
}