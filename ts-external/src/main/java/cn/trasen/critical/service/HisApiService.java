package cn.trasen.critical.service;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.web.bind.annotation.RequestBody;

import cn.trasen.critical.model.OrderitemRecord;
import cn.trasen.homs.core.feature.orm.mybatis.Page;
import cn.trasen.homs.core.model.DataSet;
import cn.trasen.homs.core.utils.PlatformResult;
import cn.trasen.po.ParamsVo;
import cn.trasen.po.ResponseJson;

public interface HisApiService {

	PlatformResult<List<Map<String,String>> > getPatientInfo(String type,String number);
	
	PlatformResult<String> updatePatientInfo(HttpServletRequest request);
	
	PlatformResult<List<Map<String,Object>> > queryRegPool(String beginDate,String endDate);
	
	PlatformResult<List<Map<String,Object>>> getStatementRecord(String id);
	
	PlatformResult<String> updateStatementRecordStatus(HttpServletRequest request);
	
	PlatformResult<List<Map<String,Object>>> getInpDepositsParmList(String id);
	
	PlatformResult<String> updateAdvancePaymentStatus(HttpServletRequest request);
	
	PlatformResult<String> receiveCriticalValue(@RequestBody ResponseJson responseJson);
	
	PlatformResult<List<Map<String,String>>> queryNurseInpatientOrder(HttpServletRequest request,String inpatientId);
	
	PlatformResult<List<Map<String,String>>> queryInPatientNurseFeeSpeci(HttpServletRequest request,String inpatientId,String groupId);
	
	PlatformResult<String> reversalCost(HttpServletRequest request);
	
	DataSet<Map<String,Object>> queryOrderItemList(Page page, String keyword);
	
	DataSet<Map<String,Object>> querySample(Page page, String keyword);
	
	PlatformResult<String> completeTask(HttpServletRequest request);
	
	 Map<String,Object> getOrderitemRecordList(@RequestBody(required=false) OrderitemRecord orderitemRecord);
	 
	 DataSet<Map<String,Object>> QueryDrgRoomManageDrug(Page page, ParamsVo paramsVo);
	 
	 DataSet<Map<String,Object>> QueryBasicsDrgDictionary(Page page, ParamsVo paramsVo);
	 
	 List<Map<String,Object>> queryDrgRoomDismantling(String deptIds,List<String> drugIdsList);
	 
	 PlatformResult<String> SaveDeptApplyBill(HttpServletRequest request);
	 
	 Map<String,String> queryMember(String userCode,String userId);
	 
	 PlatformResult<List<Map<String,String>> > getQueryInPatient(String number);

	 DataSet<Map<String, Object>> queryInPatientOrder(Page page, String keyword);

	 PlatformResult<String> reverseInPatientFeeByGroupId(HttpServletRequest request);
}
