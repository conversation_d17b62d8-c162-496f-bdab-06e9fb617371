appconfig:
  login: http://127.0.0.1/login.html
  whiteUrlList:
      - "/static/"
      - "/swagger"
      - "/springfox-swagger-ui"
      - "/v2/api-docs"
      - "/static/"
      - "/favicon.ico"
      - "/doc.html"      
      - "/webjars"     
      - "/swagger-ui.html"      
      - "/swagger-resources"
      - "/hisApi/getOrderitemRecordList"   
      - "/hisApi/receiveCriticalValue"
      - "/csjkHrp/"
      - "/csefWz/"
      - "/satisfaction/satisfactionDegree"
      - "/emrApi/"
      - "/hisApi/"
      - "/hisApi/updateStatementRecordStatus"
feign:
   okhttp:
      enabled: false
   httpclient:
      enabled: true
      max-connections: 1000 #最大连接数
      max-connections-per-route: 100 #每个url的连接数
   sentinel:
      enabled: true
   client:
      config:
         default: #default默认所有服务的超时时间
            connect-timeout: 10000
            read-timeout: 20000
ribbon:
   NFLoadBalancerRuleClassName: com.netflix.loadbalancer.ZoneAvoidanceRule #配置规则 随机
   ConnectTimeout: 500 #请求连接超时时间
   ReadTimeout: 1000 #请求处理的超时时间
   OkToRetryOnAllOperations: true #对所有请求都进行重试
   MaxAutoRetriesNextServer: 2 #切换实例的重试次数
   MaxAutoRetries: 1 #对当前实例的重试次数
   
logging:
 config: classpath:logback-custom.xml
 
mybatis:
  config-location: classpath:mybatis-config.xml
  mapper-locations: classpath*:/mapper/*Mapper.xml

server:
  port: 9009
  servlet:
    context-path: /ts-external
  jetty: 
    workerName: jetty-server 
    tmp-directory: /home/<USER>/data/tmp/directory
    
registry:
# type: nacos
 type: eureka
 #Eureka注册中心配置 
eureka:
 client:
  service-url:
   defaultZone: http://**************:8761/eureka
   register-with-eureka: true
   fetch-registry: true
   enabled: true
 instance:
  ip-address: ${spring.cloud.client.ip-address}
  instance-id: ${spring.cloud.client.ip-address}:${server.port}
  prefer-ip-address: true

spring:
  application:
    name: ts-external
  cloud:
      nacos:
         config:
            server-addr: 127.0.0.1:8848 #配置中心地址
            file-extension: yml #指定yaml格式的配置
            auto-refresh: false # 是否启用动态刷新配置
            encode: utf-8 # 编码
            enabled: false
         discovery:
            server-addr: 127.0.0.1:8848 #服务注册中心地址
            enabled: false
      sentinel:
         transport:
            dashboard: **************:8718
  datasource:
  #driverClassName: com.kingbase8.Driver
   names: deanquery
   type: com.alibaba.druid.pool.DruidDataSource
   driver-class-name: com.mysql.cj.jdbc.Driver
   url: ***********************************************************************************************************************************************************************************
   username: root
   password: 123456
   druid:
    initialSize: 1
    min-idle: 1
    max-active: 20
    keep-alive: true
    max-wait: 60000
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 60000
    validation-query: select 1 
    validation-query-timeout: 1
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
    connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    use-global-data-source-stat: true
    remove-abandoned: true
    remove-abandoned-timeout: 1800
    pool-prepared-statements: true
    max-open-prepared-statements: 50
    #aop-patterns: cn.trasen.*
    filters: stat,slf4j
    stat-view-servlet:
     enabled: true
     login-username: xtbg
     login-password: 123456@Xtbg
    web-stat-filter: #web监控
     enabled: true
     url-pattern: /*
     exclusions: '*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*'
    filter:
     stat: #sql监控
      slow-sql-millis: 1000
      log-slow-sql: true
      enabled: true
      db-type: mysql
     wall: #防火墙
      enabled: false
      db-type: mysql
     config:
        drop-table-allow: false
    
  devtools:
    restart:
      additional-paths: src/main/java
      enabled: true
  http:
    multipart:
      max-file-size: 150MB
      max-request-size: 150MB
  redis:
    database: 0
    host: **************
    jedis:
      pool:
        max-active: 8
        max-idle: 8
        max-wait: -1ms
        min-idle: 0
    password: 123456
    port: 6379
    timeout: 5000
springfox:
  base-package: cn.trasen
  description: ts-information Interface Manager
  enable: true
  service-url: http://localhost:9005/ts-config
  title: ts-information Interface Manager
  version: 2.0

his:
  request:
    trasenToken: l5gftv5l49
    hospCode: CSSDESHFLY01
    orgCode: CSSDESHFLY
    url: http://************:10010/service/call
    queryOrderItemUrl: http://***************:9101/OuterBaseDataService/QueryOrderItem
    querySampleUrl: http://*************:9101/OuterBaseDataService/QuerySample
    queryBasicsDrgDictionaryUrl: http://************:9001/ts-platform-work/http/OuterBaseDataService/QueryDrgRoomManageDrug
    saveDrugRoomApplyBillUrl: http://************:9001/ts-platform-work/http/OuterDrugService/SaveDeptApplyBill
    queryDrgRoomDismantlingUrl: http://*************:8888/ts-platform-work/http/OuterDrugService/QueryDrgRoomDismantling
    queryMemberUrl: http://*************:8888/ts-platform-work/http/jcpt/OuterBaseDataService/QueryMember
    handleCriticalValue: http://*************:9001/OuterClinicService/QueryInPatientOrder
    reverseInPatientFeeByGroupId: http://*************:9001/OuterClinicService/ReverseInPatientFeeByGroupId
    queryInPatientOrder: 
    version: 2
    appid: ********
    key: 77edfe90e1753996a7ed29c44b5f4231f614ca94
    
borrowUrl: http://192.168.210.135:8282     
    
isRemindLeader: 1
remindLeaderTime: -300
wx:
  loginUrl: http://testmoa.trasen.cn/trasen-wxoa/

ssoDbType: mysql
tsSystemDriver: com.mysql.jdbc.Driver
tsSystemUrl: ********************************************************************************************************************************************************************************
tsSystemUsername: root
tsSystemPwd: 123456

notify-url-header: http://127.0.0.1:9088

fileSystemPath: /home/<USER>/data/attachment

csjk:
  request:
    url: http://172.16.22.172:8069/jsonrpc

wxOrDingtalk: 0

csefWzReturn:
  request:
    url: http://192.168.74.167:8096/device-cloud/biz/oa/flowReturn

csefWzComplete:
  request:
    url: http://192.168.74.167:8096/device-cloud/biz/oa/flowComplete
form-external-ssoUrl: 
#多数据源、八医院门户首页等查询
deanquery:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************
    username: root
    password: 123456
