package cn.trasen.ams.common.interceptor;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 改进版 MySQL SQL拦截器 - 自动添加机构代码条件
 * 解决了原版本在复杂SQL场景下的健壮性问题
 * 
 * 主要改进：
 * 1. 更准确的SQL解析，支持复杂子查询、JOIN、UNION
 * 2. 更高效的字符串处理，避免正则表达式性能问题
 * 3. 更安全的括号匹配，考虑字符串和注释
 * 4. 缓存机制提升性能
 *
 * <AUTHOR> (improved)
 * @date 2025/9/4
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class ImprovedSsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(ImprovedSsoOrgCodeInterceptor.class);

    // SQL解析结果缓存，提升性能
    private static final Map<String, SqlParseResult> PARSE_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;

    // 需要排除的表
    private static final Set<String> EXCLUDE_TABLES = new HashSet<>(Arrays.asList("d_category22"));

    // 需要添加sso_org_code条件的表前缀
    private static final Set<String> INCLUDE_TABLE_PREFIXES = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;
    private boolean enableCache = true;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            String modifiedSql = processSql(originalSql);

            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.debug("SQL拦截器执行完成，耗时: {}ms", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("SQL拦截器执行异常", e);
            throw e;
        }
    }

    /**
     * 处理SQL语句
     */
    private String processSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || user.getCorpcode() == null || user.getCorpcode().trim().isEmpty()) {
                if (enableLogging) {
                    log.warn("无法获取用户信息或机构代码为空");
                }
                return sql;
            }

            // 使用缓存提升性能
            String cacheKey = sql.hashCode() + "_" + user.getCorpcode();
            if (enableCache && PARSE_CACHE.containsKey(cacheKey)) {
                SqlParseResult cached = PARSE_CACHE.get(cacheKey);
                if (cached.isModified()) {
                    return cached.getModifiedSql();
                } else {
                    return sql;
                }
            }

            SqlParseResult result = analyzeAndModifySql(sql, user.getCorpcode());
            
            // 缓存结果
            if (enableCache && PARSE_CACHE.size() < MAX_CACHE_SIZE) {
                PARSE_CACHE.put(cacheKey, result);
            }

            return result.isModified() ? result.getModifiedSql() : sql;

        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            return sql;
        }
    }

    /**
     * 分析并修改SQL
     */
    private SqlParseResult analyzeAndModifySql(String sql, String orgCode) {
        String trimmedSql = sql.trim();
        
        // 只处理SELECT语句
        if (!trimmedSql.toUpperCase().startsWith("SELECT")) {
            return new SqlParseResult(sql, false);
        }

        // 检查是否已包含机构代码条件
        if (hasOrgCodeCondition(sql)) {
            if (enableLogging) {
                log.debug("SQL中已包含sso_org_code条件，跳过修改");
            }
            return new SqlParseResult(sql, false);
        }

        // 处理UNION查询
        if (containsUnion(sql)) {
            return handleUnionQuery(sql, orgCode);
        }

        // 处理普通SELECT查询
        return handleSelectQuery(sql, orgCode);
    }

    /**
     * 检查是否包含机构代码条件 - 改进版本，避免正则表达式
     */
    private boolean hasOrgCodeCondition(String sql) {
        String upperSql = sql.toUpperCase();
        SqlTokenizer tokenizer = new SqlTokenizer(upperSql);
        
        while (tokenizer.hasNext()) {
            String token = tokenizer.next();
            if ("SSO_ORG_CODE".equals(token)) {
                // 检查下一个token是否是操作符
                if (tokenizer.hasNext()) {
                    String nextToken = tokenizer.next();
                    if (isComparisonOperator(nextToken) || "LIKE".equals(nextToken) || "IS".equals(nextToken)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 检查是否包含UNION
     */
    private boolean containsUnion(String sql) {
        return sql.toUpperCase().contains(" UNION ");
    }

    /**
     * 处理UNION查询
     */
    private SqlParseResult handleUnionQuery(String sql, String orgCode) {
        // 分割UNION的各个部分
        String[] parts = sql.split("(?i)\\s+UNION\\s+(ALL\\s+)?");
        StringBuilder result = new StringBuilder();
        boolean modified = false;

        for (int i = 0; i < parts.length; i++) {
            if (i > 0) {
                result.append(" UNION ");
                if (sql.toUpperCase().contains("UNION ALL")) {
                    result.append("ALL ");
                }
            }

            SqlParseResult partResult = handleSelectQuery(parts[i].trim(), orgCode);
            result.append(partResult.getModifiedSql());
            
            if (partResult.isModified()) {
                modified = true;
            }
        }

        return new SqlParseResult(result.toString(), modified);
    }

    /**
     * 处理SELECT查询
     */
    private SqlParseResult handleSelectQuery(String sql, String orgCode) {
        SqlParser parser = new SqlParser(sql);
        
        try {
            String mainTable = parser.getMainTable();
            String tableAlias = parser.getTableAlias();
            
            if (mainTable == null || !shouldAddOrgCodeCondition(mainTable)) {
                return new SqlParseResult(sql, false);
            }

            String modifiedSql = addOrgCodeCondition(sql, mainTable, tableAlias, orgCode);
            
            if (enableLogging && !sql.equals(modifiedSql)) {
                log.info("SQL修改完成 - 主表: {}, 机构代码: {}", mainTable, orgCode);
            }

            return new SqlParseResult(modifiedSql, true);
            
        } catch (Exception e) {
            log.warn("解析SELECT语句失败: {}", e.getMessage());
            return new SqlParseResult(sql, false);
        }
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }

        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 添加机构代码条件
     */
    private String addOrgCodeCondition(String sql, String mainTable, String tableAlias, String orgCode) {
        String fieldName = buildOrgCodeField(mainTable, tableAlias);
        String condition = fieldName + " = '" + orgCode + "'";

        SqlParser parser = new SqlParser(sql);
        int wherePosition = parser.findMainWherePosition();

        if (wherePosition == -1) {
            // 没有WHERE子句，添加新的WHERE
            int insertPosition = parser.findWhereInsertPosition();
            return sql.substring(0, insertPosition) + " WHERE " + condition + sql.substring(insertPosition);
        } else {
            // 已有WHERE子句，添加AND条件
            return sql.substring(0, wherePosition + 5) + " " + condition + " AND" + sql.substring(wherePosition + 5);
        }
    }

    /**
     * 构建机构代码字段名
     */
    private String buildOrgCodeField(String mainTable, String tableAlias) {
        String pureTableName = getPureTableName(mainTable);
        
        if (tableAlias != null && !tableAlias.equals(pureTableName)) {
            return tableAlias + ".sso_org_code";
        }
        
        return pureTableName + ".sso_org_code";
    }

    /**
     * 获取纯表名
     */
    private static String getPureTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return tableName;
        }

        String pureTableName = tableName;

        if (tableName.contains(".")) {
            pureTableName = tableName.split("\\.")[1];
        }

        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 检查是否为比较操作符
     */
    private boolean isComparisonOperator(String token) {
        return "=".equals(token) || ">".equals(token) || "<".equals(token) || 
               ">=".equals(token) || "<=".equals(token) || "!=".equals(token) || "<>".equals(token);
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();
            
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, modifiedSql);
            
            if (enableLogging) {
                log.debug("SQL修改成功");
            }
            
        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }

            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }

            String cacheProp = properties.getProperty("enableCache");
            if (cacheProp != null) {
                this.enableCache = Boolean.parseBoolean(cacheProp);
            }

            log.info("改进版机构拦截器配置 - SQL修改: {}, 日志: {}, 缓存: {}", 
                    enableSqlModification, enableLogging, enableCache);
        }
    }

    /**
     * SQL解析结果
     */
    private static class SqlParseResult {
        private final String modifiedSql;
        private final boolean modified;

        public SqlParseResult(String modifiedSql, boolean modified) {
            this.modifiedSql = modifiedSql;
            this.modified = modified;
        }

        public String getModifiedSql() {
            return modifiedSql;
        }

        public boolean isModified() {
            return modified;
        }
    }

    /**
     * SQL词法分析器 - 更准确地解析SQL token
     */
    private static class SqlTokenizer {
        private final String sql;
        private int position;
        private final int length;

        public SqlTokenizer(String sql) {
            this.sql = sql;
            this.position = 0;
            this.length = sql.length();
        }

        public boolean hasNext() {
            skipWhitespace();
            return position < length;
        }

        public String next() {
            skipWhitespace();
            if (position >= length) {
                return null;
            }

            // 处理字符串字面量
            if (sql.charAt(position) == '\'' || sql.charAt(position) == '"') {
                return readString();
            }

            // 处理标识符或关键字
            if (Character.isLetter(sql.charAt(position)) || sql.charAt(position) == '_' || sql.charAt(position) == '`') {
                return readIdentifier();
            }

            // 处理操作符
            return readOperator();
        }

        private void skipWhitespace() {
            while (position < length && Character.isWhitespace(sql.charAt(position))) {
                position++;
            }
        }

        private String readString() {
            char quote = sql.charAt(position);
            int start = position++;

            while (position < length) {
                if (sql.charAt(position) == quote) {
                    position++;
                    break;
                } else if (sql.charAt(position) == '\\') {
                    position += 2; // 跳过转义字符
                } else {
                    position++;
                }
            }

            return sql.substring(start, position);
        }

        private String readIdentifier() {
            int start = position;

            if (sql.charAt(position) == '`') {
                position++;
                while (position < length && sql.charAt(position) != '`') {
                    position++;
                }
                if (position < length) position++; // 跳过结束的`
            } else {
                while (position < length &&
                       (Character.isLetterOrDigit(sql.charAt(position)) ||
                        sql.charAt(position) == '_' ||
                        sql.charAt(position) == '.')) {
                    position++;
                }
            }

            return sql.substring(start, position);
        }

        private String readOperator() {
            int start = position;
            char c = sql.charAt(position++);

            // 处理多字符操作符
            if (position < length) {
                char next = sql.charAt(position);
                if ((c == '<' && (next == '=' || next == '>')) ||
                    (c == '>' && next == '=') ||
                    (c == '!' && next == '=')) {
                    position++;
                }
            }

            return sql.substring(start, position);
        }
    }

    /**
     * SQL解析器 - 更准确地解析SQL结构
     */
    private static class SqlParser {
        private final String sql;
        private final String upperSql;

        public SqlParser(String sql) {
            this.sql = sql;
            this.upperSql = sql.toUpperCase();
        }

        public String getMainTable() {
            int fromIndex = findMainFromPosition();
            if (fromIndex == -1) {
                return null;
            }

            String afterFrom = sql.substring(fromIndex + 4).trim();
            String[] parts = afterFrom.split("\\s+", 3);

            return parts.length > 0 ? parts[0] : null;
        }

        public String getTableAlias() {
            int fromIndex = findMainFromPosition();
            if (fromIndex == -1) {
                return null;
            }

            String afterFrom = sql.substring(fromIndex + 4).trim();
            String[] parts = afterFrom.split("\\s+", 3);

            if (parts.length >= 2) {
                String secondPart = parts[1];
                if ("AS".equalsIgnoreCase(secondPart) && parts.length >= 3) {
                    return parts[2];
                } else if (!isSqlKeyword(secondPart)) {
                    return secondPart;
                }
            }

            return null;
        }

        public int findMainWherePosition() {
            int position = 0;
            while (true) {
                int whereIndex = upperSql.indexOf(" WHERE ", position);
                if (whereIndex == -1) {
                    break;
                }

                if (!isInSubquery(whereIndex)) {
                    return whereIndex;
                }

                position = whereIndex + 7;
            }
            return -1;
        }

        public int findWhereInsertPosition() {
            // 找到FROM子句结束位置
            int fromIndex = findMainFromPosition();
            if (fromIndex == -1) {
                return sql.length();
            }

            String afterFrom = sql.substring(fromIndex);
            String[] keywords = {" GROUP BY ", " ORDER BY ", " HAVING ", " LIMIT ", " UNION "};

            int minIndex = Integer.MAX_VALUE;
            for (String keyword : keywords) {
                int index = afterFrom.toUpperCase().indexOf(keyword);
                if (index != -1 && index < minIndex) {
                    minIndex = index;
                }
            }

            return minIndex == Integer.MAX_VALUE ? sql.length() : fromIndex + minIndex;
        }

        private int findMainFromPosition() {
            int position = 0;
            int lastFromIndex = -1;

            while (true) {
                int fromIndex = upperSql.indexOf(" FROM ", position);
                if (fromIndex == -1) {
                    fromIndex = upperSql.indexOf("FROM ", position);
                }

                if (fromIndex == -1) {
                    break;
                }

                if (!isInSubquery(fromIndex)) {
                    lastFromIndex = fromIndex;
                }

                position = fromIndex + 5;
            }

            return lastFromIndex;
        }

        private boolean isInSubquery(int position) {
            int leftParens = 0;
            int rightParens = 0;
            boolean inString = false;
            char stringChar = 0;

            for (int i = 0; i < position; i++) {
                char c = sql.charAt(i);

                if (!inString) {
                    if (c == '\'' || c == '"') {
                        inString = true;
                        stringChar = c;
                    } else if (c == '(') {
                        leftParens++;
                    } else if (c == ')') {
                        rightParens++;
                    }
                } else {
                    if (c == stringChar && (i == 0 || sql.charAt(i - 1) != '\\')) {
                        inString = false;
                    }
                }
            }

            return leftParens > rightParens;
        }

        private boolean isSqlKeyword(String word) {
            String[] keywords = {"WHERE", "GROUP", "ORDER", "HAVING", "LIMIT", "UNION", "JOIN", "LEFT", "RIGHT", "INNER", "OUTER"};
            return Arrays.stream(keywords).anyMatch(keyword -> keyword.equalsIgnoreCase(word));
        }
    }
}
