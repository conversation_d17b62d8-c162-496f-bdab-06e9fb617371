package cn.trasen.ams.common.interceptor;

import cn.trasen.homs.core.bean.ThpsUser;
import cn.trasen.homs.core.utils.UserInfoHolder;
import org.apache.ibatis.executor.statement.StatementHandler;
import org.apache.ibatis.plugin.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.Connection;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 修复版 MySQL SQL拦截器 - 自动添加机构代码条件
 * 
 * 主要修复：
 * 1. 修复子查询判断逻辑，正确处理字符串和注释
 * 2. 完善UNION查询支持
 * 3. 优化性能，添加缓存机制
 * 4. 增强错误处理和日志记录
 *
 * <AUTHOR> (fixed version)
 * @date 2025/9/4
 */
@Intercepts({
        @Signature(type = StatementHandler.class, method = "prepare", args = {Connection.class, Integer.class})
})
public class FixedSsoOrgCodeInterceptor implements Interceptor {

    private static final Logger log = LoggerFactory.getLogger(FixedSsoOrgCodeInterceptor.class);

    // SQL解析结果缓存
    private static final Map<String, CachedSqlResult> PARSE_CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;
    private static final long CACHE_EXPIRE_TIME = 30 * 60 * 1000; // 30分钟

    // 需要排除的表
    private static final Set<String> EXCLUDE_TABLES = new HashSet<>(Arrays.asList("d_category22"));

    // 需要添加sso_org_code条件的表前缀
    private static final Set<String> INCLUDE_TABLE_PREFIXES = new HashSet<>(Arrays.asList(
            "d_", "m_", "call_", "civil_", "comm_", "cust_", "dept_", "device_", "di_", "dp_",
            "emp_", "gov_", "hr_", "hrms_", "importdata_", "jc_", "kq_", "med_", "new_",
            "political_", "satisfaction_", "scheduling_", "sms_", "t_", "tbl_", "thr_",
            "toa_", "user_", "wf_", "ws_", "zdy_", "zp_", "zt_", "ts_", "c_", "thps_"
    ));

    // 配置属性
    private boolean enableSqlModification = true;
    private boolean enableLogging = true;
    private boolean enableCache = true;

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        long startTime = System.currentTimeMillis();

        try {
            StatementHandler statementHandler = (StatementHandler) invocation.getTarget();
            String originalSql = statementHandler.getBoundSql().getSql();

            if (enableLogging) {
                log.debug("原始SQL: {}", originalSql);
            }

            String modifiedSql = processSql(originalSql);

            if (!originalSql.equals(modifiedSql) && enableSqlModification) {
                applySqlModification(statementHandler, modifiedSql);
            }

            Object result = invocation.proceed();

            if (enableLogging) {
                long executionTime = System.currentTimeMillis() - startTime;
                log.debug("SQL拦截器执行完成，耗时: {}ms", executionTime);
            }

            return result;

        } catch (Exception e) {
            log.error("SQL拦截器执行异常", e);
            throw e;
        }
    }

    /**
     * 处理SQL语句
     */
    private String processSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        try {
            ThpsUser user = UserInfoHolder.getCurrentUserInfo();
            if (user == null || user.getCorpcode() == null || user.getCorpcode().trim().isEmpty()) {
                if (enableLogging) {
                    log.warn("无法获取用户信息或机构代码为空");
                }
                return sql;
            }

            // 使用缓存提升性能
            String cacheKey = generateCacheKey(sql, user.getCorpcode());
            if (enableCache) {
                CachedSqlResult cached = PARSE_CACHE.get(cacheKey);
                if (cached != null && !cached.isExpired()) {
                    return cached.getModifiedSql();
                }
            }

            String modifiedSql = analyzeAndModifySql(sql, user.getCorpcode());
            
            // 缓存结果
            if (enableCache && PARSE_CACHE.size() < MAX_CACHE_SIZE) {
                PARSE_CACHE.put(cacheKey, new CachedSqlResult(modifiedSql));
            }

            return modifiedSql;

        } catch (Exception e) {
            log.error("处理SQL时发生异常", e);
            return sql;
        }
    }

    /**
     * 分析并修改SQL
     */
    private String analyzeAndModifySql(String sql, String orgCode) {
        String trimmedSql = sql.trim();
        
        // 只处理SELECT语句
        if (!trimmedSql.toUpperCase().startsWith("SELECT")) {
            return sql;
        }

        // 检查是否已包含机构代码条件
        if (hasOrgCodeCondition(sql)) {
            if (enableLogging) {
                log.debug("SQL中已包含sso_org_code条件，跳过修改");
            }
            return sql;
        }

        // 处理UNION查询
        if (containsUnion(sql)) {
            return handleUnionQuery(sql, orgCode);
        }

        // 处理普通SELECT查询
        return handleSelectQuery(sql, orgCode);
    }

    /**
     * 检查是否包含机构代码条件 - 修复版本
     */
    private boolean hasOrgCodeCondition(String sql) {
        SqlTokenizer tokenizer = new SqlTokenizer(sql.toUpperCase());
        
        while (tokenizer.hasNext()) {
            String token = tokenizer.next();
            
            // 跳过字符串字面量
            if (token.startsWith("'") || token.startsWith("\"")) {
                continue;
            }
            
            if ("SSO_ORG_CODE".equals(token) || token.endsWith(".SSO_ORG_CODE")) {
                // 检查下一个token是否是操作符
                if (tokenizer.hasNext()) {
                    String nextToken = tokenizer.next();
                    if (isComparisonOperator(nextToken) || "LIKE".equals(nextToken) || "IS".equals(nextToken)) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 检查是否包含UNION
     */
    private boolean containsUnion(String sql) {
        // 使用更精确的检查，避免字符串中的UNION
        SqlTokenizer tokenizer = new SqlTokenizer(sql.toUpperCase());
        while (tokenizer.hasNext()) {
            String token = tokenizer.next();
            if ("UNION".equals(token)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 处理UNION查询 - 修复版本
     */
    private String handleUnionQuery(String sql, String orgCode) {
        List<String> unionParts = splitUnionQuery(sql);
        StringBuilder result = new StringBuilder();
        boolean modified = false;

        for (int i = 0; i < unionParts.size(); i++) {
            if (i > 0) {
                result.append(" UNION ");
                // 检查是否是UNION ALL
                if (sql.toUpperCase().contains("UNION ALL")) {
                    result.append("ALL ");
                }
            }

            String part = unionParts.get(i).trim();
            String modifiedPart = handleSelectQuery(part, orgCode);
            result.append(modifiedPart);
            
            if (!part.equals(modifiedPart)) {
                modified = true;
            }
        }

        if (modified && enableLogging) {
            log.info("UNION查询修改完成，机构代码: {}", orgCode);
        }

        return result.toString();
    }

    /**
     * 分割UNION查询 - 考虑括号和字符串
     */
    private List<String> splitUnionQuery(String sql) {
        List<String> parts = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        
        SqlTokenizer tokenizer = new SqlTokenizer(sql);
        boolean inParens = false;
        int parenLevel = 0;
        
        while (tokenizer.hasNext()) {
            String token = tokenizer.next();
            
            if (token.equals("(")) {
                parenLevel++;
                inParens = true;
            } else if (token.equals(")")) {
                parenLevel--;
                if (parenLevel == 0) {
                    inParens = false;
                }
            } else if ("UNION".equals(token.toUpperCase()) && !inParens) {
                parts.add(current.toString().trim());
                current = new StringBuilder();
                
                // 跳过可能的ALL关键字
                if (tokenizer.hasNext()) {
                    String nextToken = tokenizer.next();
                    if (!"ALL".equals(nextToken.toUpperCase())) {
                        current.append(nextToken).append(" ");
                    }
                }
                continue;
            }
            
            current.append(token).append(" ");
        }
        
        if (current.length() > 0) {
            parts.add(current.toString().trim());
        }
        
        return parts;
    }

    /**
     * 处理SELECT查询
     */
    private String handleSelectQuery(String sql, String orgCode) {
        try {
            SqlParser parser = new SqlParser(sql);
            
            String mainTable = parser.getMainTable();
            String tableAlias = parser.getTableAlias();
            
            if (mainTable == null || !shouldAddOrgCodeCondition(mainTable)) {
                return sql;
            }

            String modifiedSql = addOrgCodeCondition(sql, mainTable, tableAlias, orgCode);
            
            if (enableLogging && !sql.equals(modifiedSql)) {
                log.info("SQL修改完成 - 主表: {}, 机构代码: {}", mainTable, orgCode);
            }

            return modifiedSql;
            
        } catch (Exception e) {
            log.warn("解析SELECT语句失败: {}", e.getMessage());
            return sql;
        }
    }

    /**
     * 判断是否需要添加机构代码条件
     */
    private boolean shouldAddOrgCodeCondition(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return false;
        }

        String pureTableName = getPureTableName(tableName);

        if (EXCLUDE_TABLES.contains(pureTableName.toLowerCase())) {
            return false;
        }

        return INCLUDE_TABLE_PREFIXES.stream()
                .anyMatch(prefix -> pureTableName.toLowerCase().startsWith(prefix.toLowerCase()));
    }

    /**
     * 添加机构代码条件
     */
    private String addOrgCodeCondition(String sql, String mainTable, String tableAlias, String orgCode) {
        String fieldName = buildOrgCodeField(mainTable, tableAlias);
        String condition = fieldName + " = '" + orgCode + "'";

        SqlParser parser = new SqlParser(sql);
        int wherePosition = parser.findMainWherePosition();

        if (wherePosition == -1) {
            // 没有WHERE子句，添加新的WHERE
            int insertPosition = parser.findWhereInsertPosition();
            return sql.substring(0, insertPosition) + " WHERE " + condition + sql.substring(insertPosition);
        } else {
            // 已有WHERE子句，添加AND条件
            return sql.substring(0, wherePosition + 5) + " " + condition + " AND" + sql.substring(wherePosition + 5);
        }
    }

    /**
     * 构建机构代码字段名
     */
    private String buildOrgCodeField(String mainTable, String tableAlias) {
        String pureTableName = getPureTableName(mainTable);
        
        if (tableAlias != null && !tableAlias.equals(pureTableName)) {
            return tableAlias + ".sso_org_code";
        }
        
        return pureTableName + ".sso_org_code";
    }

    /**
     * 获取纯表名
     */
    private static String getPureTableName(String tableName) {
        if (tableName == null || tableName.trim().isEmpty()) {
            return tableName;
        }

        String pureTableName = tableName;

        if (tableName.contains(".")) {
            pureTableName = tableName.split("\\.")[1];
        }

        if (pureTableName.startsWith("`") && pureTableName.endsWith("`")) {
            pureTableName = pureTableName.substring(1, pureTableName.length() - 1);
        }

        return pureTableName;
    }

    /**
     * 检查是否为比较操作符
     */
    private boolean isComparisonOperator(String token) {
        return "=".equals(token) || ">".equals(token) || "<".equals(token) || 
               ">=".equals(token) || "<=".equals(token) || "!=".equals(token) || "<>".equals(token);
    }

    /**
     * 生成缓存键
     */
    private String generateCacheKey(String sql, String orgCode) {
        return sql.hashCode() + "_" + orgCode;
    }

    /**
     * 应用SQL修改
     */
    private void applySqlModification(StatementHandler statementHandler, String modifiedSql) {
        try {
            Object boundSql = statementHandler.getBoundSql();
            
            java.lang.reflect.Field sqlField = boundSql.getClass().getDeclaredField("sql");
            sqlField.setAccessible(true);
            sqlField.set(boundSql, modifiedSql);
            
            if (enableLogging) {
                log.debug("SQL修改成功");
            }
            
        } catch (Exception e) {
            log.error("应用SQL修改失败", e);
        }
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        if (properties != null) {
            String sqlModificationProp = properties.getProperty("enableSqlModification");
            if (sqlModificationProp != null) {
                this.enableSqlModification = Boolean.parseBoolean(sqlModificationProp);
            }

            String loggingProp = properties.getProperty("enableLogging");
            if (loggingProp != null) {
                this.enableLogging = Boolean.parseBoolean(loggingProp);
            }

            String cacheProp = properties.getProperty("enableCache");
            if (cacheProp != null) {
                this.enableCache = Boolean.parseBoolean(cacheProp);
            }

            log.info("修复版机构拦截器配置 - SQL修改: {}, 日志: {}, 缓存: {}", 
                    enableSqlModification, enableLogging, enableCache);
        }
    }

    /**
     * 缓存结果类
     */
    private static class CachedSqlResult {
        private final String modifiedSql;
        private final long timestamp;

        public CachedSqlResult(String modifiedSql) {
            this.modifiedSql = modifiedSql;
            this.timestamp = System.currentTimeMillis();
        }

        public String getModifiedSql() {
            return modifiedSql;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - timestamp > CACHE_EXPIRE_TIME;
        }
    }
}
