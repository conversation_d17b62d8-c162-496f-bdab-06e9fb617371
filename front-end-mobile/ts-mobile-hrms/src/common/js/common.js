export default {
  //获取路由及参数
  getCurPage: function() {
    let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
    let curRoute = routes[routes.length - 1].route; //获取当前页面路由
    //在微信小程序或是app中，通过curPage.options；如果是H5，则需要curPage.$route.query（H5中的curPage.options为undefined，所以刚好就不需要条件编译了）
    let curParam = routes[routes.length - 1].options; //获取路由参数
    // 拼接参数
    let param = '';
    if (curParam) {
      for (let key in curParam) {
        param += `${key}=${curParam[key]}&`;
      }
      param = `?${param.substring(0, param.length - 1)}`;
    }
    return `/${curRoute}${param}`;
  }
};
