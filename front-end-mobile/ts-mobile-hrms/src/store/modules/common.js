import common from '@/common/js/common';
export default {
  namespaced: true,
  state: () => ({
    baseHost: '',
    token: '', //用户token
    userInfo: {
      empDeptId: '',
      empId: '',
      phone: ''
    },
    globalSetting: {},
    systemCustomCode: {}
  }),
  actions: {
    /**@desc 去登录**/
    goToLogin({ state }) {
      const getCurrentPagePath = location.href;
      let hrefPath = '',
        toastIcon = '',
        toastTitle = '';
      const isMustAccountLogin = state.globalSetting.accountLogin;
      if (isMustAccountLogin) {
        hrefPath = `./login?returnURL=${getCurrentPagePath}`;
        toastIcon = 'none';
        toastTitle = '登录失效，请重新登录';
      } else {
        hrefPath = `/ts-information/cp/weixin/wxOAuth2Url?url=${getCurrentPagePath}`;
        toastIcon = 'loading';
        toastTitle = '登录失效，正在重新登录';
      }
      uni.showToast({
        title: toastTitle,
        icon: toastIcon,
        duration: 2000,
        complete: function() {
          setTimeout(function() {
            location.href = hrefPath;
          });
        }
      });
    }
  },
  mutations: {
    /**@desc 更新数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    setData(state, { label, value }) {
      state[label] = value;
    }
  }
};
