import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  getMedicalCriticalValueList(params) {
    return request.get(`${apiConfig.hrms()}/api/medCrisisValue/list`, {
      params
    });
  },
  handleMedCrisisValue(data) {
    return request.post(
      `${apiConfig.hrms()}/api/medCrisisValue/handleMedCrisisValue`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
