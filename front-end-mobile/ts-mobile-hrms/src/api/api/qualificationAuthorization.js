import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  //获取用户是否为医务管理人员
  getIsMedicalManagement() {
    return request.get(`${apiConfig.hrms()}/api/doctorRole/getIsAdmin`);
  },
  getMyDocRoleDetails() {
    return request.get(
      `${apiConfig.hrms()}/api/doctorRole/getMyDocRoleDetails`
    );
  },
  getMyQuaAuthItemDetlDetails(params) {
    return request.get(
      `${apiConfig.hrms()}/api/doctorRole/getMyQuaAuthItemDetlDetails`,
      {
        params
      }
    );
  },
  getMyLaunchWorkflowList(params) {
    return request.get(
      `${apiConfig.workflow()}/workflow/instance/getMyLaunchWorkflowList`,
      {
        params
      }
    );
  },
  doctorRoleStatistics(data) {
    return request.post(`${apiConfig.hrms()}/api/doctorRole/statistics`, data);
  },
  doctorRoleStatisticsList(params) {
    return request.get(`${apiConfig.hrms()}/api/doctorRole/statisticsList`, {
      params
    });
  }
};
