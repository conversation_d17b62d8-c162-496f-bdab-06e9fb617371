import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 我的排班列表**/
  getMeSchedulingList(data) {
    return request.post(
      `${apiConfig.hrms()}/api/scheduleRecord/getPersonMedScheduleRecord`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 科室排班列表**/
  getOrgSchedulingList(params) {
    return request.get(
      `${apiConfig.hrms()}/api/scheduleRecord/getScheduleRecordList`,
      {
        params
      }
    );
  },
  /**@desc 班次数据**/
  getScheduleClassesList(data) {
    return request.post(
      `${apiConfig.hrms()}/api/scheduleClasses/getScheduleClassesList`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 班次数据**/
  statisticsOrgScheduleRecordM(data) {
    return request.post(
      `${apiConfig.hrms()}/api/scheduleRecord/statisticsScheduleRecordM`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 科室完成率**/
  orgfinishRateScheduleRecordM(data) {
    return request.post(
      `${apiConfig.hrms()}/api/scheduleRecord/finishRateScheduleRecordM`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 科室未排班人员**/
  orgUnfinishScheduleDareM(data) {
    return request.post(
      `${apiConfig.hrms()}/api/scheduleRecord/unfinishScheduleDareM`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
