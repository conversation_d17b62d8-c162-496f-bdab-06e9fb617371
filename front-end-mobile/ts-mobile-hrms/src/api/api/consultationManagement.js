import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 会诊资质列表**/
  getconsultQuaList(datas) {
    return request.get(`${apiConfig.hrms()}/api/consultQua/list`, {
      params: datas
    });
  },

  /**@desc 申请已签/申请未签 */
  getApplyUnsignedOrSignedList(datas) {
    return request.get(
      `${apiConfig.hrms()}/api/consultApply/getApplyUnsignedOrSignedList`,
      {
        params: datas
      }
    );
  },

  /**@desc 会诊已签/会诊未签 */
  getConsultUnsignedOrSignedList(datas) {
    return request.get(
      `${apiConfig.hrms()}/api/consultApply/getConsultUnsignedOrSignedList`,
      {
        params: datas
      }
    );
  },

  /**@desc 我会诊的 */
  getMySignedList(datas) {
    return request.get(`${apiConfig.hrms()}/api/consultApply/getMySignedList`, {
      params: datas
    });
  },

  /**@desc 查授权人 */
  getZcrRole(datas) {
    return request.get(`${apiConfig.hrms()}/api/consultApply/getZcrRole`, {
      params: datas
    });
  },

  /**@desc 确认授权 */
  confirmZcrRole(data) {
    return request.post(
      `${apiConfig.hrms()}/api/consultApply/confirmZcrRole`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 签到二维码 */
  consultApplySignInQrCode(id) {
    return request.get(
      `${apiConfig.hrms()}/api/consultApply/signInQrCode/${id}`
    );
  },

  /**@desc 申请单详情 */
  consultApplyDetail(id) {
    return request.get(`${apiConfig.hrms()}/api/consultApply/${id}`);
  },

  /**@desc 申请单签到详情列表 */
  consultApplyDetailsList(datas) {
    return request.get(`${apiConfig.hrms()}/api/consultApplyDetails/list`, {
      params: datas
    });
  },

  /**@desc 临时补签 */
  confirmSignRepair(data) {
    return request.post(
      `${apiConfig.hrms()}/api/consultApply/confirmSignRepair`,
      data,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
