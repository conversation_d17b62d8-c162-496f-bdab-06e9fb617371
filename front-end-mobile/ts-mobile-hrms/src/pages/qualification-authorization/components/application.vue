<template>
  <view class="application-container">
    <view class="application-content">
      <view class="applicant-info">
        <view class="info-row">
          <text>姓名：{{ docRoleDetail.employeeName }}</text>
          <text>性别：{{ docRoleDetail.empSex }}</text>
        </view>
        <view class="info-row">
          <text>年龄：{{ docRoleDetail.empAge }}</text>
          <text>职称：{{ docRoleDetail.technical || '--' }}</text>
        </view>
      </view>
      <u-tabs
        :list="tabList"
        :is-scroll="false"
        :current="curaTab"
        bg-color="transparent"
        @change="onTabTap"
      ></u-tabs>
      <swiper
        :current="curaTab"
        class="swiper-box"
        :duration="300"
        @change="handleTabChange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in tabList"
          :key="index"
        >
          <mescroll
            :ref="'mescroll' + index"
            :mescrollIndex="index"
            :down="item.downOption"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <view class="authorization-list" v-if="index == 0">
              <view
                class="authorization-item"
                v-for="(row, authorizationIndex) in item['list']"
                :key="index + '-' + authorizationIndex"
              >
                <view class="item-title">
                  {{ row.name }}
                  <text class="item-status">{{ row.value }}</text>
                </view>
                <view class="item-info">
                  <view class="item-permission" v-if="row.name == '手术权限'">
                    <text
                      class="item-permission-detail"
                      @tap="handleCheckPermissionDetail()"
                    >
                      权限明细
                    </text>
                  </view>
                  <template v-else>
                    <view class="info-field item-time" v-if="row.appyTime">
                      申请时间：{{ $dayjs(row.appyTime).format('YYYY-MM-DD') }}
                    </view>
                    <view class="info-field item-node" v-if="row.endTime">
                      生效时间：{{ $dayjs(row.endTime).format('YYYY-MM-DD') }}
                    </view>
                  </template>
                </view>
              </view>
            </view>
            <view class="application-list" v-else-if="index == 1">
              <view
                class="application-item"
                v-for="(row, applicationIndex) in item['list']"
                :key="index + '-' + applicationIndex"
                @tap="handleCheckWorkflowDetail(row)"
              >
                <view class="item-title">
                  {{ row.workflowName }}
                  <text
                    class="item-status"
                    :style="workflowStatus[row.status].style"
                  >
                    {{ workflowStatus[row.status].label }}
                  </text>
                </view>
                <view class="item-info">
                  <view class="info-field item-time">
                    申请日期：{{ $dayjs(row.createDate).format('YYYY-MM-DD') }}
                  </view>
                  <view class="info-field item-node">
                    {{
                      row.status === 2 || row.status === 0
                        ? ''
                        : '当前节点：' + row.currentStepName
                    }}
                  </view>
                </view>
              </view>
            </view>
          </mescroll>
        </swiper-item>
      </swiper>
    </view>
    <view class="bottom-btn">
      <button class="btn-item uni-bg-blue" @tap="handleToApply">
        申请授权
      </button>
    </view>
    <PermissionDetail ref="permissionDetail" />
  </view>
</template>

<script>
const workflowStatus = {
  1: {
    label: '审核中',
    style: {
      color: '#005abc'
    }
  },
  2: {
    label: '审核通过',
    style: {
      color: '#30a15b'
    }
  },
  3: {
    label: '已退回',
    style: {
      color: '#ccc'
    }
  }
};
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import PermissionDetail from './permission-detail.vue';
export default {
  components: {
    mescroll,
    PermissionDetail
  },
  data() {
    return {
      docRoleDetail: this.$store.state.common.userInfo,
      curaTab: 0,
      tabList: [
        {
          name: '已授权',
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          name: '已申请',
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      showPermissionDetail: false,
      workflowStatus
    };
  },
  mounted() {
    let tabIndex = uni.getStorageSync('tabIndex');
    if (tabIndex != null && tabIndex !== '') {
      this.curaTab = tabIndex;
      uni.removeStorageSync('tabIndex');
    }
    this.$refs[`mescroll${this.curaTab}`][0].downCallback();
  },
  methods: {
    onTabTap(index) {
      this.curaTab = index;
    },
    async handleTabChange(e) {
      let index = e.target.current || e.detail.current;
      this.curaTab = index;
      if (!this.tabList[index]['isInit']) {
        this.tabList[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
    },

    async getListData(page, successCallback, errorCallback, keywords, index) {
      if (index == 0) {
        await this.ajax.getMyDocRoleDetails().then(async res => {
          let rows = res.object || [];
          await successCallback(rows, res.object?.length || 0);
        });
      } else {
        await this.ajax
          .getMyLaunchWorkflowList({
            isMobile: true,
            handleStatus: 9,
            pageSize: page.size,
            pageNo: page.num,
            sidx: 'inst.CREATE_DATE',
            sord: 'desc',
            workflowLabel: 'yw'
          })
          .then(async res => {
            let rows = res.rows;
            await successCallback(rows, res.totalCount);
          })
          .catch(() => {
            errorCallback();
          });
      }
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['total'] = totalCount;
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabList[index]['list'] = [];
    },
    handleCheckPermissionDetail() {
      this.$refs.permissionDetail.show({
        employeeNo: this.docRoleDetail.empCode
      });
    },
    handleCheckWorkflowDetail(row) {
      uni.setStorageSync('workflow_info', row);
      uni.setStorageSync('tabIndex', this.curaTab);
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/workflow/my-workflow-detail?name=checkDetail`
      });
    },
    handleToApply() {
      uni.setStorageSync('tabIndex', this.curaTab);
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/ts-mobile-oa/pages/workflow/workflow-list?workflowLabel=yw`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.application-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}
.application-content {
  flex: 1;
  margin: 0 16rpx;
  display: flex;
  flex-direction: column;
}
.applicant-info {
  background: #fff;
  border-radius: 4px;
  padding: 16rpx;
}
.applicant-info .info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 28rpx;
}
.swiper-box {
  flex: 1;
  .swiper-item {
    flex: 1;
    flex-direction: row;
  }
}
.authorization-item,
.application-item {
  padding: 16rpx;
  background: #fff;
  position: relative;
  &:not(:last-child)::after {
    content: ' ';
    position: absolute;
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 16rpx;
    right: 0;
  }
}
.authorization-item .item-title,
.application-item .item-title {
  color: #333;
  font-weight: 700;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.authorization-item .item-status,
.application-item .item-status {
  font-weight: normal;
  color: $theme-color; //#30a15b;
  font-size: 30rpx;
}
.authorization-item .info-field,
.application-item .info-field {
  font-size: 28rpx;
  color: #666;
  flex: 1;
  &:nth-child(even) {
    text-align: right;
  }
}
.application-item .item-info {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.authorization-item .item-permission {
  flex: 1;
  text-align: right;
}
.authorization-item .item-permission-detail {
  color: $theme-color;
  font-size: 24rpx;
}
.authorization-item .item-node {
  text-align: left !important;
}
.bottom-btn {
  background-color: #fff;
  box-shadow: 0 1px 6px #ddd;
  padding: 8rpx 16rpx;
  box-sizing: border-box;
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: center;
}
.bottom-btn .btn-item {
  flex: 1;
  box-sizing: border-box;
  text-align: center;
  font-size: 32rpx;
  position: relative;
  border-radius: 4px;
  margin: 0;
}
.bottom-btn .btn-item:not(:last-child) {
  margin-right: 16rpx;
}
</style>
