<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="资质授权管理"></page-head>
    <view class="subsection-wrap">
      <view class="subsection-list">
        <view
          v-for="subsectionItem in subsectionList"
          :key="subsectionItem.id"
          @click="curSubsection = subsectionItem.id"
          class="subsection-item"
          :class="{
            'is-active': curSubsection == subsectionItem.id
          }"
        >
          {{ subsectionItem.name }}
        </view>
      </view>
    </view>
    <Application v-show="curSubsection == 'application'" />
    <AuthorizationOverview v-show="curSubsection == 'authorizationOverview'" />
  </view>
</template>

<script>
import Application from './components/application.vue';
import AuthorizationOverview from './components/authorization-overview.vue';
export default {
  components: {
    Application,
    AuthorizationOverview
  },
  data() {
    return {
      subsectionList: [
        {
          name: '资质授权',
          id: 'application'
        },
        {
          name: '授权总览',
          id: 'authorizationOverview'
        }
      ],
      curSubsection: 'application'
    };
  },
  methods: {
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}
.subsection-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 16rpx;
}
.subsection-list {
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid $theme-color;
  border-radius: 4px;
  background: #fff;
  overflow: hidden;
}
.subsection-wrap .subsection-item {
  line-height: 1;
  padding: 16rpx 30rpx;
  color: $theme-color;
  font-size: 28rpx;
}
.subsection-wrap .subsection-item.is-active {
  background: $theme-color;
  color: #fff;
}
</style>
