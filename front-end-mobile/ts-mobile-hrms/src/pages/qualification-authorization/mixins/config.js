export default {
  data() {
    return {
      localPermissionList: [
        {
          label: '手术权限',
          key: 'ssqxCount',
          index: '0',
          ssoOrgCode: ''
        },
        {
          label: '普通处方权',
          key: 'ptcfqCount',
          index: '1',
          ssoOrgCode: ''
        },
        {
          label: '抗菌药物处方权',
          key: 'kjywcfqCount',
          radioList: [
            {
              label: '无',
              value: '0'
            },
            {
              label: '非限制使用',
              value: '1'
            },
            {
              label: '限制使用',
              value: '2'
            },
            {
              label: '特殊使用',
              value: '3'
            }
          ],
          index: '2',
          ssoOrgCode: ''
        },
        {
          label: '中药饮片权',
          key: 'zyypqCount',
          index: '3',
          ssoOrgCode: ''
        },
        {
          label: '特殊级抗菌药物会诊权',
          key: 'tsjkjywhzqCount',
          index: '4',
          ssoOrgCode: ''
        },
        {
          label: '麻醉药品处方权',
          key: 'mzypcfqCount',
          index: '5',
          ssoOrgCode: ''
        },
        {
          label: '抗肿瘤药物处方权',
          key: 'kzlywcfqCount',
          radioList: [
            {
              label: '无',
              value: '0'
            },
            {
              label: '普通级',
              value: '1'
            },
            {
              label: '限制级',
              value: '2'
            }
          ],
          index: '6',
          ssoOrgCode: ''
        },
        {
          label: '精神药品处方权',
          key: 'jsypcfqCount',
          radioList: [
            {
              label: '无',
              value: '0'
            },
            {
              label: '精一',
              value: '1'
            },
            {
              label: '精二',
              value: '2'
            },
            {
              label: '精一和精二',
              value: '3'
            }
          ],
          index: '7',
          ssoOrgCode: ''
        },
        {
          label: '毒性药品处方权',
          key: 'dxypcfqCount',
          index: '8',
          ssoOrgCode: ''
        },
        {
          label: '协定处方权',
          key: 'xdcfqCount',
          index: '13',
          ssoOrgCode: ''
        },
        {
          label: '输血权',
          key: 'sxqxCount',
          index: '17',
          ssoOrgCode: ''
        },
        {
          label: '会诊权',
          key: 'hzqxCount',
          index: '18',
          ssoOrgCode: ''
        },
        {
          label: '放射性药品处方权',
          key: 'fsxypcfqCount',
          index: '9',
          ssoOrgCode: 'hnsrmyy'
        },
        {
          label: '终止妊娠药品处方权',
          key: 'zzrsypcfqCount',
          index: '10',
          ssoOrgCode: 'hnsrmyy'
        },
        {
          label: '处方调剂权',
          key: 'cftjqCount',
          radioList: [
            {
              label: '普通药品',
              value: '普通药品'
            },
            {
              label: '麻醉精神药品',
              value: '麻醉精神药品'
            },
            {
              label: '抗菌药物',
              value: '抗菌药物'
            },
            {
              label: '抗肿瘤药物',
              value: '抗肿瘤药物'
            },
            {
              label: '放射性药品',
              value: '放射性药品'
            },
            {
              label: '中药饮片',
              value: '中药饮片'
            }
          ],
          index: '11',
          ssoOrgCode: 'hnsrmyy'
        },
        {
          label: '临床药师技术权',
          key: 'lcysjsqCount',
          radioList: [
            {
              label: '处方审核',
              value: '处方审核'
            },
            {
              label: '药学门诊',
              value: '药学门诊'
            },
            {
              label: '药学会诊',
              value: '药学会诊'
            },
            {
              label: '药学巡诊',
              value: '药学巡诊'
            },
            {
              label: '药学监测',
              value: '药学监测'
            }
          ],
          index: '12',
          ssoOrgCode: 'hnsrmyy'
        },
        {
          label: '高风险操作权限',
          key: 'gfxczqxCount',
          index: '21',
          radioList: [
            {
              label: '一类医疗技术项目',
              value: 21
            },
            {
              label: '二类医疗技术项目',
              value: 22
            },
            {
              label: '三类医疗技术项目',
              value: 23
            }
          ],
          ssoOrgCode: ''
        }
      ]
    };
  },
  computed: {
    permissionList() {
      let orgCode = this.$store.state.common.globalSetting.ssoOrgCode;
      return this.localPermissionList.filter(
        e => e.ssoOrgCode == '' || e.ssoOrgCode == orgCode
      );
    }
  }
};
