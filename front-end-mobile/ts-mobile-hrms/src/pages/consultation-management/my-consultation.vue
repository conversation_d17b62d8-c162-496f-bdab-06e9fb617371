<template>
  <view class="ts-content" v-if="showContent">
    <page-head :title="title" @clickLeft="returnBack"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          cancelButton="none"
          @confirm="search"
          placeholder="搜索科室/门诊号/住院号/患者姓名"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <list-item
          v-for="(item, index) in consultationList"
          :key="index"
          :dataSource="item"
          :colmun="colmun"
          :footBtnList="[]"
          @itemClick="toDetail"
        >
          <template #title>
            <text class="title bold">{{ item.patnName }}</text>
            <text class="title"
              >&nbsp;{{ item.patnSex }}&nbsp;{{ item.patnAge }}&nbsp;{{
                item.patnNo
              }}</text
            >
          </template>
        </list-item>
      </mescroll>
    </view>
    <search-drawer
      ref="searchDrawer"
      :searchList="searchList"
      @search="searchByForm"
    />
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import listItem from './components/list-item.vue';
import searchDrawer from './components/search-drawer.vue';
export default {
  components: {
    Mescroll,
    listItem,
    searchDrawer
  },
  data() {
    return {
      showContent: false,
      keywords: '',
      queryMap: {},
      title: '',
      consultationList: [],
      searchList: [
        {
          label: '全院大会诊',
          prop: 'quaType',
          childNodeList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '普通会诊',
              value: '普通会诊'
            },
            {
              label: '急会诊',
              value: '急会诊'
            },
            {
              label: '抗生素会诊',
              value: '抗生素会诊'
            }
          ]
        }
      ],
      colmun: [
        {
          label: '会诊类别',
          key: 'consultTypeConsultClass'
        },
        // {
        //   label: '会诊级别',
        //   key: 'consultType'
        // },
        {
          label: '会诊时间',
          key: 'consultDate'
        },
        {
          label: '会诊地点',
          key: 'consultAddress'
        },
        {
          label: '会诊目的',
          key: 'consultPurpose'
        }
      ],
      status: null
    };
  },
  onLoad(opt) {
    this.showContent = true;
    this.title = '我会诊的';
    this.$nextTick(() => {
      this.$refs.mescroll.downCallback();
    });
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    searchByForm(searchForm) {
      this.queryMap = searchForm;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.$refs.searchDrawer.show();
    },
    async getListData(page, successCallback, errorCallback) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        sidx: 'create_date',
        searchKey: this.keywords,
        sord: 'desc'
      };
      for (let key in this.queryMap) {
        query[key] = this.queryMap[key];
      }
      await this.ajax
        .getMySignedList(query)
        .then(async res => {
          let rows = res.rows.map(e => {
            return {
              ...e,
              status: e.consultStatus == 1 ? '已会诊' : '未会诊',
              statusClass: e.consultStatus == 1 ? 'green' : 'red',
              consultTypeConsultClass: `${e.consultType}-${e.consultClass}`
            };
          });
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      this.consultationList = this.consultationList.concat(rows);
    },
    datasInit() {
      this.consultationList = [];
    },
    //返回上一层
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .title {
    margin-left: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 28rpx;
    line-height: 60rpx;
    color: #666;
    &.bold {
      font-size: 30rpx;
      color: #000;
      font-weight: bold;
      margin-left: 0rpx;
    }
  }
}
</style>
