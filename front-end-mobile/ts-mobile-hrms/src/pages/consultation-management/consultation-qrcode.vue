<template>
  <view class="ts-content" v-if="showContent">
    <page-head title="签到二维码" @clickLeft="returnBack"></page-head>
    <view class="sign">
      <view class="title">{{ dataSoruce.consultClass }}签到二维码</view>
      <view class="value">{{ dataSoruce.patnName }}</view>
      <view class="value">
        {{ dataSoruce.patnSex }}&nbsp;{{ dataSoruce.patnAge }}&nbsp;
        {{ dataSoruce.patnNo }}
      </view>
      <image v-if="image" :src="image" class="iamge"></image>
      <view class="value yellow">会诊时间：{{ dataSoruce.consultDate }}</view>
      <view class="value yellow"
        >会诊地点：{{ dataSoruce.consultAddress }}</view
      >
    </view>
    <view class="mescroll-title">
      <view class="value">签到记录</view>
      <view class="value green" @click="refresh">签到刷新</view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="signItem">
          <view class="flex1 width20">姓名</view>
          <view class="flex1 width20">科室</view>
          <view class="flex1 width40">时间</view>
          <view class="flex1 right width20">状态</view>
        </view>
        <view class="signItem" v-for="item in signList" :key="item.id">
          <view class="flex1 width20">{{ item.consultDoctorName }}</view>
          <view class="flex1 width20">{{ item.inviteOrgName }}</view>
          <view class="flex1 width40">{{ item.signDate | formatTime }}</view>
          <view class="flex1 right width20">{{
            item.signStatus == '1' ? '已签到' : '未签到'
          }}</view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: { Mescroll },
  data() {
    return {
      showContent: false,
      queryMap: {},
      signList: [],
      value: '',
      dataSoruce: null,
      applyId: '',
      image: ''
    };
  },
  async onLoad(opt) {
    if (opt.applyId) {
      this.applyId = opt.applyId;
      await this.getApplyData(opt.applyId);
    }
    this.showContent = true;
    this.refresh();
  },
  filters: {
    formatTime(time) {
      if (!time) return '无';
      return time.split(':')[0] + ':' + time.split(':')[2];
    }
  },
  methods: {
    refresh() {
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    async getApplyData(id) {
      let res = await this.ajax.consultApplyDetail(id);
      this.dataSoruce = res.object || {};
      let img = await this.ajax.consultApplySignInQrCode(id);
      this.image = `data:image/png;base64,${img}`;
    },
    async getListData(page, successCallback, errorCallback) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        applyId: this.applyId,
        sidx: 'sign_org',
        sord: 'desc'
      };
      await this.ajax
        .consultApplyDetailsList(query)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      this.signList = this.signList.concat(rows);
    },
    datasInit() {
      this.signList = [];
    },
    //返回上一层
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ {
    .sign {
      height: 40%;
      background: #fff;
      margin: 20rpx 20rpx 0 20rpx;
      border-radius: 16rpx;
      padding: 10rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      .title {
        font-weight: 700;
        font-size: 36rpx;
        color: #000;
      }
      .value {
        color: #666;
        &.yellow {
          color: $u-type-warning;
          font-weight: bold;
        }
      }
      .iamge {
        width: 50%;
      }
    }
    .mescroll-title {
      display: flex;
      justify-content: space-between;
      padding: 10rpx;
      margin: 10rpx 20rpx 0 10rpx;
      border-radius: 16rpx 16rpx 0 0;
      background: #fff;
      .value {
        font-weight: bold;
        &.green {
          color: $u-type-success;
        }
      }
    }
    .mescroll-content {
      flex: 1;
      position: relative;
      background: #fff;
      margin: 0 20rpx 20rpx 10rpx;
      border-radius: 0 0 16rpx 16rpx;
      padding: 10rpx;
      .signItem {
        display: flex;
        justify-content: space-around;
        line-height: 40rpx;
        margin: 8rpx 0;
        padding: 10rpx 14rpx;
        .flex1 {
          text-align: left;
          font-size: 26rpx;
          &.right {
            text-align: right;
          }
          &.width20 {
            width: 20%;
          }
          &.width40 {
            width: 40%;
          }
        }
      }
    }
  }
}
</style>
