<template>
  <view class="ts-content" v-if="showContent">
    <page-head :title="title" @clickLeft="returnBack"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          cancelButton="none"
          @confirm="search"
          placeholder="搜索科室/门诊号/住院号/患者姓名"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <list-item
          v-for="(item, index) in consultationList"
          :key="index"
          :dataSource="item"
          :colmun="colmun"
          :footBtnList="getFooterBtn(item)"
        >
          <template #title>
            <text class="title bold">{{ item.patnName }}</text>
            <text class="title"
              >&nbsp;{{ item.patnSex }}&nbsp;{{ item.patnAge }}&nbsp;{{
                item.patnNo
              }}</text
            >
          </template>
        </list-item>
      </mescroll>
    </view>
    <search-drawer
      ref="searchDrawer"
      :searchList="searchList"
      @search="searchByForm"
    />
    <!-- 授权人 -->
    <uni-popup ref="popup" type="allBottom">
      <view class="popup-container">
        <view class="popup-action-container">
          <view class="cancel-btn" @tap="handleCancelSelect('popup')"
            >取消</view
          >
          <view class="popup-title">
            <text>选择授权人</text>
            <text class="fault">医政科主任指定人员组织签到</text>
          </view>
          <view class="submit-btn" @tap="handleSubmitSelect('popup')"
            >确定</view
          >
        </view>

        <view class="search-container">
          <u-search
            v-model="pickerSearch"
            placeholder="输入姓名搜索"
            @clear="searchPopup('popup')"
            @search="searchPopup('popup')"
            @custom="searchPopup('popup')"
          ></u-search>
        </view>

        <view class="mescroll-content">
          <mescroll
            ref="mescrollPopup"
            @getDatas="getDatasPopup"
            @setDatas="setDatasPopup"
            @datasInit="datasInitPopup"
          >
            <view
              class="contact_item"
              v-for="item in radioUserList"
              :key="item.id"
              @click="singleColumn('popup', item)"
            >
              <text class="contact_item_text"
                >{{ item.employee_name }}-{{ item.orgName }}</text
              >
              <view
                class="contact_item-icon"
                v-if="activeRadioIndex == item.employee_no"
              >
                <uni-icons type="checkmarkempty" color="#005BAC" size="44" />
              </view>
            </view>
          </mescroll>
        </view>
      </view>
    </uni-popup>
    <!-- 补签人员列表 -->
    <uni-popup ref="popupCheckBox" type="allBottom">
      <view class="popup-container">
        <view class="popup-action-container">
          <view class="cancel-btn" @tap="handleCancelSelect('popupCheckBox')"
            >取消</view
          >
          <view class="popup-title">
            <text>补签人员列表</text>
            <text class="fault">临时情况需在会诊申请成功后,增加会诊人员</text>
          </view>
          <view class="submit-btn" @tap="handleSubmitSelect('popupCheckBox')"
            >确定</view
          >
        </view>

        <view class="search-container">
          <u-search
            v-model="pickerSearch1"
            placeholder="输入姓名搜索"
            @clear="searchPopup('popupCheckBox')"
            @search="searchPopup('popupCheckBox')"
            @custom="searchPopup('popupCheckBox')"
          ></u-search>
        </view>

        <view class="mescroll-content">
          <mescroll
            ref="mescrollCheckBox"
            @getDatas="getDatasCheckBox"
            @setDatas="setDatasCheckBox"
            @datasInit="datasInitCheckBox"
          >
            <view
              class="contact_item"
              v-for="item in checkUserList"
              :key="item.id"
              @click="singleColumn('popupCheckBox', item)"
            >
              <text class="contact_item_text"
                >{{ item.appluUserName }}-{{ item.orgName }}</text
              >
              <view
                class="contact_item-icon"
                v-if="activeCheckIndex.some(e => e.id == item.id)"
              >
                <uni-icons type="checkmarkempty" color="#005BAC" size="44" />
              </view>
            </view>
          </mescroll>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import listItem from './components/list-item.vue';
import searchDrawer from './components/search-drawer.vue';
export default {
  components: {
    Mescroll,
    listItem,
    searchDrawer
  },
  data() {
    return {
      showContent: false,
      keywords: '',
      queryMap: {},
      title: '',
      consultationList: [],
      searchList: [
        {
          label: '全院大会诊',
          prop: 'quaType',
          childNodeList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '普通会诊',
              value: '普通会诊'
            },
            {
              label: '急会诊',
              value: '急会诊'
            },
            {
              label: '抗生素会诊',
              value: '抗生素会诊'
            }
          ]
        }
      ],
      colmun: [
        {
          label: '会诊类别',
          key: 'consultTypeConsultClass'
        },
        // {
        //   label: '会诊级别',
        //   key: 'consultType'
        // },
        {
          label: '会诊时间',
          key: 'consultDate'
        },
        {
          label: '会诊地点',
          key: 'consultAddress'
        },
        {
          label: '会诊目的',
          key: 'consultPurpose'
        }
      ],
      status: null,
      pickerSearch: '',
      radioUserList: [],
      activeRadioIndex: '',
      activeRow: {},
      pickerSearch1: '',
      checkUserList: [],
      activeCheckIndex: []
    };
  },
  async onLoad(opt) {
    this.showContent = true;
    this.status = opt.status;
    this.title = '申请未签';
    if (this.status == 1) {
      this.title = '申请已签';
    }
    await this.getZcrRole();
    this.$nextTick(() => {
      this.$refs.mescroll.downCallback();
    });
  },
  computed: {
    userInfo() {
      return this.$store.state.common.userInfo;
    }
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    getFooterBtn(row) {
      let btnList = [];
      let isMaster =
        row.doctorAttr == '主持人' || this.userInfo.empCode == 'admin';
      if (this.status == 0 && isMaster) {
        btnList = [
          {
            btnName: '签到二维码',
            event: this.handleQrCode
          },
          {
            btnName: '临时补签',
            event: this.handleCheckBox,
            btnClass: 'primary'
          },
          {
            btnName: '签到授权',
            event: this.handleRadio
          }
        ];
      }
      if (this.status == 1 && isMaster) {
        btnList = [
          {
            btnName: '签到二维码',
            event: this.handleQrCode
          },
          {
            btnName: '临时补签',
            event: this.handleCheckBox,
            btnClass: 'primary'
          }
        ];
      }
      return btnList;
    },
    searchByForm(searchForm) {
      this.queryMap = searchForm;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.$refs.searchDrawer.show();
    },
    // 主列表
    async getListData(page, successCallback, errorCallback) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        consultStatus: this.status,
        searchKey: this.keywords,
        sidx: 'create_date',
        sord: 'desc'
      };
      for (let key in this.queryMap) {
        query[key] = this.queryMap[key];
      }
      await this.ajax
        .getApplyUnsignedOrSignedList(query)
        .then(async res => {
          let rows = res.rows.map(e => {
            return {
              ...e,
              status: this.status == 1 ? '已会诊' : '未会诊',
              statusClass: this.status == 1 ? 'green' : 'red',
              consultTypeConsultClass: `${e.consultType}-${e.consultClass}`
            };
          });
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      this.consultationList = this.consultationList.concat(rows);
    },
    datasInit() {
      this.consultationList = [];
    },
    // 签到授权
    handleRadio(item) {
      this.activeRow = item;
      this.$refs.popup.open();
    },
    // 临时补签
    async handleCheckBox(item) {
      this.activeRow = item;
      this.$refs.popupCheckBox.open();
    },
    // 签到二维码
    handleQrCode(item) {
      let url = `/pages/consultation-management/consultation-qrcode?applyId=${item.id}`;
      uni.navigateTo({ url });
    },
    searchPopup(ref) {
      this.$nextTick(() => {
        if (ref == 'popup') {
          this.$refs.mescrollPopup?.downCallback();
        } else {
          this.$refs.mescrollCheckBox?.downCallback();
        }
      });
    },
    async getZcrRole() {
      let res = await this.ajax.getZcrRole();
      this.radioUserList = res.object || [];
    },
    // 签到授权
    getDatasPopup(page, successCallback, errorCallback) {
      if (this.pickerSearch) {
        let list = this.radioUserList.filter(
          e => e.employee_name.indexOf(this.pickerSearch) > -1
        );
        successCallback(list);
      } else {
        successCallback(this.radioUserList);
      }
    },
    setDatasPopup(rows) {
      this.radioUserList = this.radioUserList.concat(rows);
    },
    datasInitPopup() {
      this.radioUserList = [];
    },
    // 取消
    handleCancelSelect(ref) {
      if (ref == 'popup') {
        this.pickerSearch = '';
        this.activeRadioIndex = '';
        this.$refs.popup.close();
      } else {
        this.pickerSearch1 = '';
        this.activeCheckIndex = [];
        this.$refs.popupCheckBox.close();
      }
      this.activeRow = {};
    },
    // 确定
    async handleSubmitSelect(ref) {
      if (ref == 'popup') {
        let emp = this.radioUserList.find(
          e => e.employee_no == this.activeRadioIndex
        );
        if (emp) {
          let res = await this.ajax.confirmZcrRole({
            applyId: this.activeRow.id,
            consultDoctor: emp.employee_no,
            consultDoctorName: emp.employee_name,
            signOrg: emp.org_id,
            signOrgName: emp.orgName,
            consultDoctorLevel: emp.technical || ''
          });
          if (res.success) {
            uni.showToast({
              title: '签到授权设置成功！',
              icon: 'none'
            });
            this.handleCancelSelect(ref);
            this.$refs.mescroll.downCallback();
          } else {
            uni.showToast({
              title: '签到授权设置失败！',
              icon: 'none'
            });
          }
        }
      } else {
        let sumitData = this.activeCheckIndex.map(e => {
          return {
            consultDoctor: e.applyUser,
            consultDoctorName: e.appluUserName,
            signOrg: e.organizationId,
            signOrgName: e.orgName,
            consultDoctorLevel: e.technical,
            applyId: this.activeRow.id
          };
        });
        let res = await this.ajax.confirmSignRepair(sumitData);
        if (res.success) {
          uni.showToast({
            title: '临时补签设置成功',
            icon: 'none'
          });
          this.handleCancelSelect(ref);
          this.$refs.mescroll.downCallback();
        } else {
          uni.showToast({
            title: res.message || '临时补签设置失败',
            icon: 'none'
          });
        }
      }
    },
    // 选中
    singleColumn(ref, item) {
      if (ref == 'popup') {
        this.activeRadioIndex = item.employee_no;
      } else {
        if (this.activeCheckIndex.some(e => e.id == item.id)) {
          let index = this.activeCheckIndex.findIndex(e => e.id == item.id);
          this.activeCheckIndex.splice(index, 1);
        } else {
          this.activeCheckIndex.push(item);
        }
      }
    },
    async getDatasCheckBox(page, successCallback, errorCallback) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        searchKey: this.pickerSearch1,
        quaType: this.activeRow.consultClass,
        status: '1',
        sidx: 'create_date',
        sord: 'desc'
      };
      await this.ajax
        .getconsultQuaList(query)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setDatasCheckBox(rows) {
      this.checkUserList = this.checkUserList.concat(rows);
    },
    datasInitCheckBox() {
      this.checkUserList = [];
    },
    //返回上一层
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .title {
    margin-left: 10rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 28rpx;
    line-height: 60rpx;
    color: #666;
    &.bold {
      font-size: 30rpx;
      color: #000;
      font-weight: bold;
      margin-left: 0rpx;
    }
  }
  .contact_item {
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    color: #333333;
    position: relative;
    background: #fff;
    // display: flex;
    // align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      &.conten_ite_flex {
        display: flex;
        align-items: flex-end;
        justify-content: center;
      }
      .contact_item_text-wrap {
        flex: 1;
        .contact_item_text-info {
          font-size: 28rpx;
          color: #333333;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .contact_item_text-info-text {
          flex: 1;
        }
      }
      .contact_item_text-icon {
        font-size: 28rpx;
        color: #999;
        margin-left: 40rpx;
      }
    }
    .contact_item-icon {
      line-height: 1;
      position: absolute;
      right: 10rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }
  .popup-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .search-container {
      padding: 8px;
      background-color: #fff;
      margin: 8px 0;
    }
    .mescroll-content {
      flex: 1;
      position: relative;
    }
  }
  .popup-action-container {
    display: flex;
    overflow: hidden;
    align-items: center;
    background-color: #fff;
    padding: 8px 16px;
    box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
    .popup-title {
      flex: 1;
      font-weight: bold;
      text-align: center;
      display: flex;
      flex-direction: column;
      .fault {
        font-size: 24rpx;
        color: #999;
      }
    }
    .cancel-btn {
      color: #999;
    }
    .submit-btn {
      color: #005bac;
    }
    .confirm-btn {
      color: $theme-color;
    }
  }
}
</style>
