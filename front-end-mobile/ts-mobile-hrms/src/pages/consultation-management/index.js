export default {
  data() {
    return {
      tabIndex: 0,
      tabBars: [
        {
          title: '我的会诊',
          total: null,
          menus: [
            {
              icon: 'oa-icon oa-icon-xinxiguanli',
              iconColor: '#ef7f78',
              menuname: '会诊未签',
              totalKey: '',
              url: '/pages/consultation-management/consultation-list?status=0'
            },
            {
              icon: 'oa-icon oa-icon-shouwendengji',
              iconColor: '#7bb872',
              menuname: '会诊已签',
              totalKey: '',
              url: '/pages/consultation-management/consultation-list?status=1'
            },
            {
              icon: 'oa-icon oa-icon-wodegongdan',
              iconColor: '#2d76eb',
              menuname: '我会诊的',
              totalKey: '',
              url: '/pages/consultation-management/my-consultation'
            }
          ]
        },
        {
          title: '我的申请',
          total: null,
          menus: [
            {
              icon: 'oa-icon oa-icon-shouwendengji',
              iconColor: '#2d76eb',
              menuname: '我申请的',
              totalKey: '',
              url: `workflowNo-applyCode`
            },
            {
              icon: 'oa-icon oa-icon-xinxiguanli',
              iconColor: '#ef7f78',
              menuname: '申请未签',
              totalKey: '',
              url: '/pages/consultation-management/consultApplyList?status=0'
            },
            {
              icon: 'oa-icon oa-icon-shouwendengji',
              iconColor: '#7bb872',
              menuname: '申请已签',
              totalKey: '',
              url: '/pages/consultation-management/consultApplyList?status=1'
            }
          ]
        },
        {
          title: '会诊资质',
          total: null,
          menus: [
            {
              icon: 'oa-icon oa-icon-shouwendengji',
              iconColor: '#2d76eb',
              menuname: '我申请的',
              totalKey: '',
              url: 'workflowNo-quaCode'
            },
            {
              icon: 'oa-icon oa-icon-zhishiguanli',
              iconColor: '#44bec8',
              menuname: '资质一览',
              totalKey: '',
              url:
                '/pages/consultation-management/qualification/qualification-allList'
            }
          ]
        }
      ]
    };
  },
  onLoad(opt) {
    if (opt.index != undefined) this.tabIndex = opt.index;
  },
  computed: {
    quaCode() {
      return (
        this.$store.state.common.systemCustomCode.CONSULATION_QUA || 'L_90134'
      );
    },
    applyCode() {
      return (
        this.$store.state.common.systemCustomCode.CONSULATION_APPLY || 'L_90135'
      );
    }
  },
  methods: {
    //tab点解切换
    ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let _self = this,
        index = e.target.current || e.detail.current;
      await _self.switchTab(Number(index));
    },
    async getWorkflowDefinition(code) {
      let res = await this.ajax.getWorkflowDefinition(code);
      return res.object || {};
    },
    async jumpPage(url, code = '') {
      // if (url.indexOf('?') > -1) {
      //   url = `${url}&tabIndex=${this.tabIndex}`;
      // } else {
      //   url = `${url}?tabIndex=${this.tabIndex}`;
      // }
      if (url.indexOf('ts-mobile') > -1) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: url
        });
      } else if (url != '') {
        if (url.indexOf('workflowNo') > -1) {
          let code = url.split('-')[1];
          url = `/ts-mobile-oa/pages/workflow/my-workflow-list?index=0&workflowNo=${this[code]}`;
          this.$parentTypeFun({
            type: 'jumpPage',
            path: url
          });
        } else {
          uni.navigateTo({ url });
        }
      } else {
        let workFlow = await this.getWorkflowDefinition(code);
        url = `/ts-mobile-oa/pages/workflow/init-custom-workflow?wfDefinitionId=${workFlow.wfDefinitionId}&workflowNo=${workFlow.workflowNo}&formId=${workFlow.formId}`;
        this.$parentTypeFun({
          type: 'jumpPage',
          path: url
        });
      }
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    },
    async switchTab(index) {
      let _self = this;
      _self.tabIndex = index;
    }
  }
};
