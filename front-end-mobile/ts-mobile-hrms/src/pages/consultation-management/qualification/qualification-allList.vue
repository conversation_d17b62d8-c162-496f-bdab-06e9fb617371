<template>
  <view class="ts-content" v-if="showContent">
    <page-head title="资质一览" @clickLeft="returnBack"></page-head>
    <view class="search-box">
      <view class="search-form">
        <uni-search-bar
          radius="100"
          bgColor="#FFFFFF"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          cancelButton="none"
          @confirm="search"
          placeholder="搜索科室/姓名"
        ></uni-search-bar>
      </view>
      <view class="search-sift" @tap="changeDrawer">
        <text class="search-sift-icon oa-icon oa-icon-shaixuan"></text>
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :down="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="list_item" v-for="item in consultationList" :key="item.id">
          <view class="item_title">
            <view class="title_left">
              <text class="title bold">{{ item.appluUserName }}</text>
              <text class="title"
                >{{ item.technical }}&nbsp;全院大会诊{{ item.quaType }}</text
              >
            </view>
            <view class="title_right red">{{
              item.updateDate | formatDate
            }}</view>
          </view>
          <view class="item_content">
            <view class="content_item">
              <view class="item_l">
                <text class="label">归属科室：</text>
                <text class="key">{{ item.orgName }}</text>
              </view>
            </view>
            <view class="content_item">
              <view class="item_l">
                <text class="label">工号：</text>
                <text class="key">{{ item.applyUser }}</text>
              </view>
            </view>
            <view class="content-status">已授权</view>
          </view>
        </view>
      </mescroll>
    </view>
    <search-drawer
      ref="searchDrawer"
      :searchList="searchList"
      @search="searchByForm"
    />
  </view>
</template>

<script>
import Mescroll from '@/components/mescroll-swiper/mescroll.vue';
import searchDrawer from '../components/search-drawer.vue';
export default {
  components: {
    Mescroll,
    searchDrawer
  },
  data() {
    return {
      showContent: false,
      keywords: '',
      queryMap: {},
      title: '',
      consultationList: [],
      searchList: [
        {
          label: '会诊权限类型',
          prop: 'quaType',
          childNodeList: [
            {
              label: '全部',
              value: ''
            },
            {
              label: '普通会诊',
              value: '普通会诊'
            },
            {
              label: '急会诊',
              value: '急会诊'
            },
            {
              label: '抗生素会诊',
              value: '抗生素会诊'
            }
          ]
        }
      ]
    };
  },
  onLoad(opt) {
    this.showContent = true;
    this.title = opt.title || '资质一览';
    this.$nextTick(() => {
      this.$refs.mescroll.downCallback();
    });
  },
  filters: {
    formatDate(time) {
      let current = new Date(time),
        cY = current.getFullYear(),
        cM = current.getMonth() + 1,
        cD = current.getDate(),
        currentStr = `${cY}-${cM}-${cD}`;
      return currentStr;
    }
  },
  methods: {
    //搜索
    search(res) {
      this.keywords = res.value;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    searchByForm(searchForm) {
      this.queryMap = searchForm;
      this.$nextTick(() => {
        this.$refs.mescroll.downCallback();
      });
    },
    //分类抽屉切换
    changeDrawer() {
      this.$refs.searchDrawer.show();
    },
    async getListData(page, successCallback, errorCallback) {
      let query = {
        pageSize: page.size,
        pageNo: page.num,
        searchKey: this.keywords,
        status: '1',
        sidx: 'create_date',
        sord: 'desc'
      };
      for (let key in this.queryMap) {
        query[key] = this.queryMap[key];
      }
      await this.ajax
        .getconsultQuaList(query)
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      this.consultationList = this.consultationList.concat(rows);
    },
    datasInit() {
      this.consultationList = [];
    },
    //返回上一层
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  /deep/ .uni-navbar__header-container {
    padding-right: 88rpx;
  }
  /deep/ .uni-navbar__header-btns-right {
    position: absolute;
    right: 0;
  }
  .search-box {
    display: flex;
    align-items: center;
    background-color: #eeeeee;
    .search-form {
      flex: 1;
    }
    .search-sift {
      font-size: 28rpx;
      color: #666666;
      padding-right: 16rpx;
      .search-sift-icon {
        font-size: 36rpx !important;
        color: #666666;
      }
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .list_item {
    padding: 20rpx 0 12rpx 0;
    background: #ffffff;
    margin-top: 20rpx;
    position: relative;
    .item_title {
      display: flex;
      justify-content: space-between;
      height: 60rpx;
      line-height: 60rpx;
      padding: 0 30rpx;
      .title_left {
        width: calc(100% - 150rpx);
        display: flex;
        .title {
          margin-left: 10rpx;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 28rpx;
          line-height: 60rpx;
          color: #666;
          &.bold {
            font-size: 30rpx;
            color: #000;
            font-weight: bold;
            margin-left: 0rpx;
          }
        }
      }
      .title_right {
        font-size: 28rpx;
        line-height: 60rpx;
        padding: 0 10rpx;
      }
    }
    .item_content {
      padding: 0 30rpx 14rpx 30rpx;
      .content_item {
        display: flex;
        justify-content: space-between;
        .item_l {
          display: flex;
          flex-wrap: wrap;
        }
        .label {
          font-size: 28rpx;
          color: #666;
        }
        .key {
          font-size: 28rpx;
          color: #666;
          .fiel_list {
            .file_item {
              font-size: 28rpx;
              color: #0079fe;
            }
          }
        }
        .item_r {
          font-size: 28rpx;
        }
      }
    }
    .content-status {
      position: absolute;
      bottom: 30rpx;
      right: 10px;
      border: 1px solid #4bd863;
      color: #4bd863;
      padding: 0 10rpx;
      border-radius: 10rpx;
    }
  }
}
</style>
