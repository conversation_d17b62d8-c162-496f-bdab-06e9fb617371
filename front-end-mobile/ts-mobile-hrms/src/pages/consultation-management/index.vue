<template>
  <div class="ts-content">
    <page-head @clickLeft="returnBack" title="医师会诊"></page-head>
    <work-card>
      <menu-list>
        <menu-list-item
          icon="main-icon main-icon-jingfeiluru"
          icon-color="#7bb872"
          title="会诊申请"
          @click="jumpPage('', applyCode)"
        ></menu-list-item>
        <menu-list-item
          icon="main-icon main-icon-faqihuodong"
          icon-color="#7bb872"
          title="资质申请"
          @click="jumpPage('', quaCode)"
        ></menu-list-item>
      </menu-list>
    </work-card>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.title }}</text>
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <work-card>
          <menu-list>
            <menu-list-item
              v-for="row in item.menus"
              :key="row.id"
              :icon="row.icon"
              :icon-color="row.iconColor"
              :title="row.menuname"
              @click="jumpPage(row.url)"
            ></menu-list-item>
          </menu-list>
        </work-card>
      </swiper-item>
    </swiper>
  </div>
</template>

<script>
import workCard from './components/work-card.vue';
import menuList from './components/menu-list.vue';
import menuListItem from './components/menu-list-item.vue';
import index from './index.js';
export default {
  mixins: [index],
  components: {
    workCard,
    menuList,
    menuListItem
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    margin-top: 16rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        color: #f59a23;
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
}
</style>
