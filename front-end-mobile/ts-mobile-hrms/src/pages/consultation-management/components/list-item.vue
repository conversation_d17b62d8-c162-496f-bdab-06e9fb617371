<template>
  <view class="list_item" @click="itemClick">
    <view class="item_title">
      <view class="title_left">
        <slot name="title"></slot>
        <!-- <view class="title">患者姓名</view>
        <view class="title">性别 年龄 门诊号/住院号</view> -->
      </view>
      <view class="title_right" :class="dataSource.statusClass">{{
        dataSource.status
      }}</view>
    </view>
    <view class="item_content">
      <view class="content_item" v-for="(item, index) in colmun" :key="index">
        <view class="item_l">
          <text class="label">{{ item.label }}：</text>
          <text class="key">
            {{ dataSource[item.key] }}
          </text>
        </view>
      </view>
    </view>
    <view class="item_footer" v-if="footBtnList.length > 0">
      <view
        class="footer_btn"
        :class="item.btnClass || 'defaultBtn'"
        v-for="(item, index) in footBtnList"
        :key="index"
        @click.stop="() => item.event(dataSource)"
      >
        {{ item.btnName }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    dataSource: {
      type: Object,
      default: () => {}
    },
    colmun: {
      type: Array,
      default: () => []
    },
    footBtnList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {};
  },
  methods: {
    itemClick() {
      this.$emit('itemClick', this.dataSource);
    }
  }
};
</script>
<style lang="scss" scoped>
.list_item {
  padding: 20rpx 0 12rpx 0;
  background: #ffffff;
  margin-top: 20rpx;
  .item_title {
    display: flex;
    justify-content: space-between;
    height: 60rpx;
    line-height: 60rpx;
    padding: 0 30rpx;
    .title_left {
      width: calc(100% - 120rpx);
      display: flex;
      .item_status {
        color: #ffffff;
        border-radius: 20rpx;
        font-size: 24rpx;
        min-width: 120rpx;
        text-align: center;
        line-height: 40rpx;
      }
      .title {
        line-height: 40rpx;
        margin-left: 10rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .red {
        background: #ff3b30;
      }
      .yellow {
        background: #fe9400;
      }
      .green {
        background: #4bd863;
      }
    }
    .title_right {
      font-size: 28rpx;
      border: 2rpx solid #eee;
      border-radius: 8rpx;
      line-height: 60rpx;
      padding: 0 10rpx;
      &.red {
        color: #f23232;
        border-color: #f23232;
      }
    }
  }
  .item_content {
    padding: 0 30rpx 14rpx 30rpx;
    .content_item {
      display: flex;
      justify-content: space-between;
      .item_l {
        display: flex;
        flex-wrap: wrap;
      }
      .label {
        font-size: 28rpx;
      }
      .key {
        font-size: 28rpx;
        color: #999999;
        .fiel_list {
          .file_item {
            font-size: 28rpx;
            color: #0079fe;
          }
        }
      }
      .orange {
        color: #fe9400;
      }
      .red {
        color: #ff3b30;
      }
      .green {
        color: #4bd863;
      }
      .item_r {
        font-size: 28rpx;
      }
    }
  }
  .item_footer {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row-reverse;
    border-top: 1rpx solid #bbbbbb;
    padding: 12rpx 30rpx 0 30rpx;
    .footer_btn {
      height: 54rpx;
      line-height: 54rpx;
      font-size: 28rpx;
      padding: 0 30rpx;
      border-radius: 27rpx;
      margin-left: 12rpx;
    }
    .defaultBtn {
      color: #0079fe;
      background: #cce4ff;
    }
    .primary {
      background: #0079fe;
      color: #fff;
    }
    .greyBtn {
      color: #999999;
      border: 2rpx solid #999999;
    }
  }
}
</style>
