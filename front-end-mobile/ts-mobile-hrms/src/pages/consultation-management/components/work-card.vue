<template>
  <div class="work-card">
    <p class="card-title" v-if="title">{{ title }}</p>
    <slot></slot>
  </div>
</template>

<script>
/**
 * work-card
 * @description 工作台数据卡片
 * @property {String} title 主标题
 */
export default {
  name: 'work-card',
  props: {
    title: {
      type: String,
      default: ''
    }
  }
};
</script>

<style scoped>
.work-card {
  background-color: #ffffff;
  margin-top: 10px;
  padding-top: 20rpx;
}
.card-title {
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  margin: 0;
  font-weight: bold;
}
.work-card:last-child {
  margin-bottom: 10px;
}
</style>
