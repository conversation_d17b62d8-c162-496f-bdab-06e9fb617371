<template>
  <div class="menu-list-item" @click="onclick">
    <base-icon :class="icon" :color="iconColor" :size="iconSize"></base-icon>
    <p
      class="item-title"
      :style="{ color: titleColor, 'font-size': `${titleSize}px` }"
    >
      {{ title }}
    </p>
  </div>
</template>

<script>
import baseIcon from '@/components/base-icon/index.vue';
export default {
  name: 'menu-list-item',
  props: {
    icon: {
      type: String,
      default: ''
    },
    iconColor: {
      type: String,
      default: ''
    },
    iconSize: {
      type: String,
      default: '32'
    },
    title: {
      type: String,
      default: ''
    },
    titleColor: {
      type: String,
      default: '#333'
    },
    titleSize: {
      type: String,
      default: '12'
    }
  },
  components: {
    baseIcon
  },
  methods: {
    onclick() {
      this.$emit('click');
    }
  }
};
</script>

<style lang="scss" scoped>
.menu-list-item {
  text-align: center;
  width: 20%;
  margin-bottom: 16px;
  line-height: 1;
}
.item-icon {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}
.item-title {
  margin-top: 4px;
  margin-bottom: 0;
  font-size: 14px;
  color: $text-color;
}
</style>
