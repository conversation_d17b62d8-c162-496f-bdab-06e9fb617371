<template>
  <view class="container">
    <view class="head-container">
      <view class="select-date-header">
        <view class="select-date-container">
          <u-icon name="arrow-left" color="#666" @tap="changeDate(-1)"></u-icon>
          <view class="current-month-btn" @tap="changeDate(0)">
            {{ completionTimeRange }}
          </view>
          <u-icon name="arrow-right" color="#666" @tap="changeDate(1)"></u-icon>
        </view>
        <text class="month-title"></text>
      </view>

      <view class="switch-mode">
        <view class="switch-label">
          按周
        </view>
        <switch
          :checked="switchValue"
          :color="'#215390'"
          style="transform: scale(0.7);"
          @change="e => tabChange(e.detail.value ? 1 : 0)"
        />
        <view class="switch-label">
          按月
        </view>
      </view>
    </view>

    <view class="completion-rate">
      <view class="header-cols">
        <view class="org-col">科室/完成率</view>
        <view class="name-col">姓名/工号</view>
        <view class="date-col">未排班日期</view>
      </view>
      <view class="data-rows">
        <view
          class="data-row"
          v-for="(item, index) in orgfinishRateScheduleRateData"
          :key="index"
        >
          <view
            class="org-cell"
            :class="{ 'active-org': item.organization_id === searchOrgId }"
            @tap="checkOrg(item.organization_id)"
          >
            <view class="ellipsis">{{ item.name }}</view>
            <view>{{ item.finishRate }}</view>
          </view>
          <view class="name-cell">
            <view class="ellipsis">
              {{
                unCompletionData[index]
                  ? unCompletionData[index].employee_name
                  : ''
              }}
            </view>
            <view class="ellipsis">
              {{
                unCompletionData[index]
                  ? unCompletionData[index].employee_no
                  : ''
              }}
            </view>
          </view>
          <view class="date-cell ">
            <view class="ellipsis">
              {{
                unCompletionData[index]
                  ? unCompletionData[index].missingDays
                  : ''
              }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      tabList: [{ name: '按周' }, { name: '按月' }],
      currentTab: 0,
      switchValue: false,

      isBtnCurrentActive: true,

      completionTimeRange: '',
      startDate: this.$dayjs(new Date()).startOf('week'),

      endDate: this.$dayjs(new Date()).endOf('week'),

      orgfinishRateScheduleRateData: [],
      unCompletionData: [],
      searchOrgId: ''
    };
  },
  created() {
    this.completionTimeRange =
      this.startDate.format('M月D日') + '-' + this.endDate.format('M月D日');

    this.handleGetAxiosData();
  },
  methods: {
    async handleGetAxiosData() {
      try {
        uni.showLoading({
          title: '加载中...',
          mask: true,
          duration: 5000
        });

        await this.handleGetOrgfinishValue();
        await this.handleGetUnfinishOrgScheduleDareM();
      } catch (error) {
      } finally {
        uni.hideLoading();
      }
    },

    tabChange(value) {
      this.currentTab = value;
      let currentDate = this.$dayjs(new Date());
      if (this.currentTab == 0) {
        this.startDate = currentDate.startOf('week');
        this.endDate = currentDate.endOf('week');
        this.completionTimeRange =
          this.startDate.format('M月D日') + '-' + this.endDate.format('M月D日');
      }

      if (this.currentTab == 1) {
        this.startDate = currentDate.startOf('month');
        this.endDate = currentDate.endOf('month');
        this.completionTimeRange = currentDate.format('YYYY年M月');
      }
      this.isBtnCurrentActive = true;
      this.handleGetAxiosData();
    },

    changeDate(offset) {
      if (offset === 0) {
        return this.tabChange(this.currentTab);
      }
      const nowData = this.$dayjs(new Date());
      const unit = this.currentTab === 0 ? 'week' : 'month';

      this.startDate = this.startDate.add(offset, unit);
      this.endDate = this.endDate.add(offset, unit);

      this.completionTimeRange =
        this.currentTab === 0
          ? `${this.startDate.format('M月D日')}-${this.endDate.format(
              'M月D日'
            )}`
          : this.startDate.format('YYYY年M月');

      // 检查是否为当前周期
      this.isBtnCurrentActive =
        nowData.startOf(unit).format('YYYY-MM-DD') ===
        this.startDate.format('YYYY-MM-DD');

      this.handleGetAxiosData();
    },

    async handleGetOrgfinishValue() {
      let res = await this.ajax.orgfinishRateScheduleRecordM({
        // archivesType: 'yw',
        copyType: this.currentTab + 1,
        startDate: this.startDate.format('YYYY-MM-DD'),
        endDate: this.endDate.format('YYYY-MM-DD')
      });
      this.orgfinishRateScheduleRateData = res.object || [];
      this.searchOrgId = this.orgfinishRateScheduleRateData[0].organization_id;
    },

    async handleGetUnfinishOrgScheduleDareM() {
      let res = await this.ajax.orgUnfinishScheduleDareM({
        // archivesType: 'yw',
        orgId: this.searchOrgId,
        startDate: this.startDate.format('YYYY-MM-DD'),
        endDate: this.endDate.format('YYYY-MM-DD')
      });
      let datas = res.object || [];
      this.unCompletionData = datas;
    },

    async checkOrg(orgId) {
      try {
        this.searchOrgId = orgId;
        uni.showLoading({
          title: '加载中...',
          mask: true,
          duration: 5000
        });
        await this.handleGetUnfinishOrgScheduleDareM();
      } catch (error) {
      } finally {
        uni.hideLoading();
      }
    }
  }
};
</script>
<style lang="scss" scoped>
// 通用样式
%flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

%flex-column {
  display: flex;
  flex-direction: column;
}

%border-right {
  border-right: 1px solid #eee;
}

%border-bottom {
  border-bottom: 1px solid #eee;
}

%common-cell {
  font-size: 32rpx;
  font-weight: bold;
  @extend %flex-center;
  @extend %border-right;
}

%fontSize32rpx {
  font-size: 32rpx;
}

%fontSize28rpx {
  font-size: 28rpx;
}

.container {
  height: 100%;
  @extend %flex-column;

  .ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    line-height: 32rpx;
    font-size: 28rpx;
  }

  .head-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16rpx;
    background-color: #fff;
    margin-bottom: 16rpx;

    .switch-mode {
      display: flex;
      align-items: center;
      background-color: #fff;
      overflow: hidden;
      .switch-label {
        @extend %fontSize32rpx;
      }
      .u-switch {
        margin: 0 8rpx;
      }
    }

    .select-date-header {
      @extend %flex-center;
      background: #fff;

      .select-date-container {
        display: flex;
        align-items: center;
        gap: 16rpx;
        .current-month-btn {
          width: 280rpx;
          color: #333;
          text-align: center;
          transition: all 0.2s ease;
          @extend %fontSize32rpx;
        }
      }
    }
  }

  .completion-rate {
    flex: 1;
    @extend %flex-column;

    .header-cols {
      display: flex;
      flex-direction: row;
      justify-items: center;
      align-items: center;
      color: #fff;
      color: #333;
      background: #fff;
      @extend %border-bottom;
      @extend %fontSize28rpx;

      .org-col {
        width: 30%;
        padding: 16rpx 0;
        @extend %fontSize28rpx;
        @extend %common-cell;
      }

      .name-col {
        width: 25%;
        padding: 16rpx 0;
        @extend %common-cell;
        @extend %fontSize28rpx;
      }

      .date-col {
        flex: 1;
        padding: 16rpx 0;
        border-right: none;
        @extend %common-cell;
        @extend %fontSize28rpx;
      }
    }
  }

  .data-rows {
    background-color: white;
    .data-row {
      display: flex;
      @extend %border-bottom;
      &:nth-child(odd) {
        background: #f5f5f5;
      }
      .org-cell {
        width: 30%;
        padding: 8rpx;

        @extend %flex-column;
        justify-content: space-between;
        @extend %border-right;
        overflow: hidden;
        box-sizing: border-box;
        view {
          @extend %fontSize28rpx;
        }
        &.active-org {
          color: $theme-color;
          font-weight: bold;
        }
      }
      .name-cell {
        width: 25%;
        text-align: center;
        padding: 8rpx;
        @extend %border-right;
        @extend %flex-column;
        justify-content: space-between;
        view {
          @extend %fontSize28rpx;
        }
      }

      .date-cell {
        flex: 1;
        text-align: left;
        padding: 8rpx;
        line-height: 1;
        overflow: hidden;
        justify-content: center;
        @extend %flex-column;
        @extend %fontSize28rpx;
      }
    }
  }
}
</style>
