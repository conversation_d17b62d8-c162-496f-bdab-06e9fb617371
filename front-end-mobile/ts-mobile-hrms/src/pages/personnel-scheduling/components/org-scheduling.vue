<template>
  <view class="container">
    <!-- 筛选条件 -->
    <view class="filter-bar">
      <!-- 科室筛选 -->
      <view class="department" @tap="showDeptSelect">
        <view class="dept-text"> 科室：{{ datas.orgName }}</view>
        <u-icon name="arrow-down" size="20" color="#666" />
      </view>
      <!-- 班次 -->
      <!-- <view class="department" @tap="showScheduleClasses">
        班次：{{ classesName }}
        <uni-icons type="bottom" size="14" color="#606266" />
      </view> -->
      <input
        class="search"
        placeholder="请输入姓名/工号"
        v-model="searchKey"
        @input="handleSearchInput"
      />
    </view>

    <!-- 排班表格 -->
    <scroll-view scroll-y="true" class="schedule-table">
      <view class="select-date-header">
        <view class="select-date-container">
          <u-icon name="arrow-left" color="#666" @tap="changeWeek(-1)"></u-icon>
          <view class="current-month-btn" @tap="changeWeek(0)">
            {{ isBtnCurrentWeek ? '本周' : weekStartDate + '-' + weekEndDate }}
          </view>
          <u-icon name="arrow-right" color="#666" @tap="changeWeek(1)"></u-icon>
        </view>
        <text class="month-title"></text>
      </view>

      <view class="table-header">
        <view class="name-col">姓名</view>
        <view class="day-col" v-for="(day, index) in weekData" :key="index">
          {{ day.week }}
          <br />
          <text class="day-text">{{ day.date }}</text>
        </view>
      </view>

      <view class="table-body" v-if="staffList.length">
        <view class="staff-row" v-for="staff in staffList" :key="staff.id">
          <view class="name-col" @tap="showPhonePopup(staff)">
            <text class="employee-name">{{ staff.employeeName }}</text>
            <!-- <text class="dept-name">({{ staff.orgName }})</text> -->
          </view>
          <view
            v-for="(w, index) in weekData"
            :key="index"
            :class="{
              'data-cols': true,
              red: w.day === $dayjs().format('YYYY-MM-DD')
            }"
          >
            {{ staff.dateList[w.day] }}
          </view>
        </view>
      </view>
      <view class="no-data" v-else>
        <image
          src="@/assets/img/no-data.png"
          mode="aspectFit"
          class="no-data-img"
        />
        <text>暂无数据</text>
      </view>
    </scroll-view>

    <view class="stats-panel">
      <view class="block-text">班次信息</view>
      <view class="chart-container" v-if="this.chartData.series[0].data.length">
        <qiun-data-charts
          type="ring"
          :chartData="chartData"
          :opts="opts"
          :loadingType="1"
          canvasId="proportion-outpatient-inpatient-income-mz-charts"
          :canvas2d="true"
          background="white"
          :animation="true"
          :ontouch="true"
        />
      </view>
      <view class="no-data" v-else>
        <image
          src="@/assets/img/no-data.png"
          mode="aspectFit"
          class="no-data-img"
        />
        <text>暂无数据</text>
      </view>
    </view>

    <u-select
      title="班次"
      v-model="show"
      mode="single-column"
      :list="scheduleClassesData"
      @confirm="confirmScheduleClasses"
      labelName="classesName"
      valueName="id"
    />

    <u-popup
      class="call-phone-popup"
      v-model="phonePopupShow"
      mode="center"
      border-radius="8"
      closeable
    >
      <view class="popup-body">
        <view class="popup-title">联系电话</view>
        <view class="popup-content">
          <text class="employee-name">
            <text>{{ employeeData.employeeName }}:</text>
            <text>{{ employeeData.orgName }}</text>
          </text>
          <text class="phone-number">{{ employeeData.phoneNumber }}</text>
        </view>

        <view class="call-btn" @tap="callPhone">
          <uni-icons color="#fff" type="phone-filled" size="38" />
          拨打电话
        </view>
      </view>
    </u-popup>

    <PopupRadioSelectDept
      ref="PopupRadioSelectDept"
      v-model="datas.orgId"
      @chooseItem="handleChooseItem"
    />
  </view>
</template>

<script>
import QiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import PopupRadioSelectDept from '@/components/popup-radio-select-dept/popup-radio-select-dept.vue';

export default {
  components: {
    QiunDataCharts,
    PopupRadioSelectDept
  },
  data() {
    return {
      searchTimer: null,
      isBtnCurrentWeek: true,
      staffList: [], //科室员工排班数据
      currentDate: this.$dayjs(new Date()), //当前日期
      weekStartDate: this.$dayjs(new Date())
        .startOf('week')
        .format('M月D日'),
      weekEndDate: this.$dayjs(new Date())
        .endOf('week')
        .format('M月D日'),

      datas: {
        orgId: '',
        orgName: ''
      },

      classesName: '全部', //班次名称
      classesId: '',

      searchKey: '',

      show: false, //是否显示班次选择
      scheduleClassesData: [], //班次数据

      archivesType: '', //档案类型 用于区分是否医务系统

      phonePopupShow: false,
      employeeData: {},

      chartData: {
        series: [
          {
            data: []
          }
        ]
      },
      opts: {
        rotate: false,
        width: '1000',
        rotateLock: false,
        color: ['#F56C6C', '#409EFF', '#67C23A', '#E6A23C', '#c0c0c0'],
        title: { name: '', fontSize: 16 },
        subtitle: { name: '', fontSize: 12 },
        dataLabel: true,
        fontColor: '#333',
        fontSize: 14,
        enableMarkLine: true,
        padding: [20, 40, 20, 40],
        legend: {
          show: false
        },
        markLine: {
          labelText: 'name',
          lineColor: 'red'
        },
        extra: {
          ring: {
            center: ['45%', '50%'],
            ringWidth: 40, // 环形图宽度
            activeOpacity: 0.5,
            activeRadius: 10,
            offsetAngle: 0,
            labelWidth: 15,
            border: true,
            borderWidth: 3,
            borderColor: '#FFFFFF'
          }
        }
      }
    };
  },
  computed: {
    weekData() {
      const weeks = [];
      const arr = ['日', '一', '二', '三', '四', '五', '六'];
      let startDate = this.currentDate.startOf('week');
      for (let i = 0; i < 7; i++) {
        weeks.push({
          date: startDate.add(i, 'day').format('MM-DD'),
          day: startDate.add(i, 'day').format('YYYY-MM-DD'),
          week: arr[startDate.add(i, 'day').day()],
          weekIndx: startDate.add(i, 'day').day(),
          isCurrent:
            startDate.add(i, 'day').format('YYYY-MM-DD') ===
            this.$dayjs().format('YYYY-MM-DD')
        });
      }
      return weeks;
    }
  },
  created() {
    this.archivesType = this.$route.query.archivesType;

    this.getScheduleClassesList();
    this.datas.orgName = this.$storeInfo().common.userInfo.orgName;
    this.datas.orgId = this.$storeInfo().common.userInfo.orgId;
    this.handleGetAxiosData();
  },
  watch: {
    $route: {
      handler: function(to, from) {
        // if (to.path.indexOf('pages/personnel-scheduling/index') > -1) {
        //   this.archivesType = this.$route.query.archivesType;
        //   this.$nextTick(() => {
        //     this.handleGetAxiosData();
        //   });
        // }
      },
      immediate: true
    }
  },
  methods: {
    async handleGetAxiosData() {
      try {
        uni.showLoading({
          title: '加载中...',
          mask: true,
          duration: 5000
        });

        await Promise.all([
          this.handleTryOrgSchedulingList(),
          this.handleTrystatisticsOrgSchedule()
        ]);
      } catch (error) {
      } finally {
        uni.hideLoading();
      }
    },
    //显示科室下人员排班情况
    async handleTryOrgSchedulingList() {
      let res = await this.ajax.getOrgSchedulingList({
        archivesType: this.archivesType,
        classesIds: this.classesId,
        orgIds: !this.searchKey ? this.datas.orgId : '',
        isAll: 'Y',
        searchKey: this.searchKey,
        pageNo: 1,
        pageSize: 9999,
        startDate: this.currentDate.startOf('week').format('YYYY-MM-DD'),
        endDate: this.currentDate.endOf('week').format('YYYY-MM-DD')
      });
      const orgSchedulingData = res.rows || [];
      orgSchedulingData.forEach(item => {
        if (!item.scheduleRecords) return;
        const dateMap = new Map();
        item.scheduleRecords.forEach(record => {
          if (!dateMap.has(record.scheduleDate)) {
            dateMap.set(record.scheduleDate, []);
          }
          dateMap.get(record.scheduleDate).push(record);
        });

        item.dateList = Object.fromEntries(
          Array.from(dateMap.entries()).map(([date, records]) => [
            date,
            records.map(r => r.classesName).join(',')
          ])
        );
      });
      this.staffList = orgSchedulingData;
    },
    async handleTrystatisticsOrgSchedule() {
      let res = await this.ajax.statisticsOrgScheduleRecordM({
        archivesType: this.archivesType,
        orgId: this.datas.orgId,
        startDate: this.currentDate.startOf('week').format('YYYY-MM-DD'),
        endDate: this.currentDate.endOf('week').format('YYYY-MM-DD')
      });
      let datas = res.object || [];
      let statsData = [];
      let color = [];
      datas.forEach(function(d) {
        statsData.push({
          name: d.classesName,
          value: Number(d.classesDays),
          labelText: `${d.classesName}：${Number(d.classesDays)}次`
        });
        color.push(d.classesColor);
      });
      this.chartData.series[0].data = statsData;
      this.opts.color = color;
    },

    //选择部门
    showDeptSelect() {
      this.$refs.PopupRadioSelectDept.show();
    },
    handleChooseItem({ id, name }) {
      this.datas.orgName = name;
      this.datas.orgId = id;
      this.handleGetAxiosData();
    },

    //选择班次
    showScheduleClasses() {
      this.show = true;
    },
    confirmScheduleClasses(e) {
      this.classesName = e[0].label;
      this.classesId = e[0].value;
      this.handleGetAxiosData();
    },

    //切换周
    changeWeek(offset) {
      if (offset == 0) {
        this.currentDate = this.$dayjs(new Date());
      } else {
        this.currentDate = this.currentDate.add(offset, 'week');
      }

      this.isBtnCurrentWeek =
        this.$dayjs(new Date()).format('YYYY-MM-DD') ===
        this.currentDate.format('YYYY-MM-DD');
      this.weekStartDate = this.currentDate.startOf('week').format('M月D日');
      this.weekEndDate = this.currentDate.endOf('week').format('M月D日');
      this.handleGetAxiosData();
    },
    //获取班次列表
    async getScheduleClassesList() {
      let res = await this.ajax.getScheduleClassesList({ classesStatus: 1 });
      let data = res.object || [];
      this.scheduleClassesData = [{ id: '', classesName: '全部' }];
      this.scheduleClassesData.push(...data);
    },
    showPhonePopup(data) {
      if (!data.phoneNumber) {
        uni.showToast({
          title: '暂无联系方式!',
          icon: 'none'
        });
        return;
      }

      this.employeeData = data;
      this.phonePopupShow = true;
    },
    callPhone() {
      window.location.href = 'tel:' + this.employeeData.phoneNumber;
    },

    // 添加防抖搜索方法
    handleSearchInput() {
      if (this.searchTimer) {
        clearTimeout(this.searchTimer);
      }
      this.searchTimer = setTimeout(() => {
        this.handleGetAxiosData();
      }, 400);
    }
  }
};
</script>

<style lang="scss" scoped>
%fontSize32rpx {
  font-size: 32rpx;
}
%fontSize28rpx {
  font-size: 28rpx;
}
%fontSize24rpx {
  font-size: 24rpx;
}

.container {
  display: flex;
  flex-direction: column;
  .red {
    color: red;
  }
}

.select-date-header {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #fff;
  padding: 16rpx;
  margin-bottom: 16rpx;
  .select-date-container {
    display: flex;
    align-items: center;
    gap: 16rpx;
    .current-month-btn {
      text-align: center;
      width: 280rpx;
      color: #333;
      @extend %fontSize32rpx;
      border-radius: 32rpx;
      transition: all 0.2s ease;
    }
  }
}

.filter-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 16rpx 0;
  background: #fff;
  border-radius: 12rpx;
  .department {
    flex: 1;
    border-radius: 8rpx;
    padding: 4rpx 12rpx;
    margin-right: 16rpx;
    background: #eee;
    color: #606266;
    height: 72rpx;

    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    overflow: hidden;
    @extend %fontSize28rpx;
    .dept-text {
      overflow: hidden;
      @extend %fontSize28rpx;
      height: auto;
      line-height: 18px;
      color: #333;
    }
    &:active {
      background-color: #e6f2ff;
      border-color: $theme-color;
      color: $theme-color;
      .uni-icon {
        color: $theme-color !important;
      }
    }
  }

  .search {
    flex: 1;
    background: #f0f0f0;
    height: 72rpx;
    padding: 16rpx;
    border-radius: 8rpx;
    box-sizing: border-box;
    @extend %fontSize28rpx;
  }
}

.schedule-table {
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  .table-header {
    display: flex;
    color: #333;
    background: #fff;
    .name-col {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 120rpx;
      flex-shrink: 0;
      font-weight: bold;
      border-right: 1px solid #eee;
      border-bottom: 1px solid #eee;
      @extend %fontSize28rpx;
    }
    .day-col {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      @extend %fontSize28rpx;
      color: $theme-color;
      font-weight: bold;
      border-bottom: 1px solid #eee;
      &:not(:last-child) {
        border-right: 1px solid #eee;
      }
      .day-text {
        @extend %fontSize24rpx;
        color: #333;
        font-weight: normal;
      }
    }
  }
  .table-body {
    background: #fff;
    .staff-row {
      display: flex;
      align-items: center;
      .name-col {
        width: 120rpx;
        text-align: center;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        border-right: 1px solid #eee;
        border-bottom: 1px solid #eee;
        padding: 8rpx 0;
        min-height: 32px;
        .employee-name {
          @extend %fontSize24rpx;
          color: #333;
        }
        .dept-name {
          @extend %fontSize24rpx;
          color: #333;
        }
      }
      .data-cols {
        line-height: 36rpx;
        flex: 1;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 64rpx;
        @extend %fontSize24rpx;
        border-bottom: 1px solid #eee;
        &:not(:last-child) {
          border-right: 1px solid #eee;
        }
      }
      &:nth-child(odd) {
        background: #f5f5f5;
      }
    }
  }
}

.stats-panel {
  padding: 16rpx;
  background: white;

  .block-text {
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 12rpx;
      height: 34rpx;
      background: $theme-color;
      border-radius: 25%;
      margin-right: 10rpx;
      transform: translateY(-2rpx);
    }
  }
  .chart-container {
    height: 600rpx;
    margin-bottom: 40rpx;
  }
}

.call-phone-popup {
  .u-icon__icon {
    color: #fff !important;
  }
  .popup-body {
    width: 600rpx;
    height: 280rpx;
    .popup-title {
      @extend %fontSize32rpx;
      height: 88rpx;
      line-height: 88rpx;
      background: $theme-color;
      color: #fff;
      text-align: center;
    }
    .u-icon {
      top: 0rpx;
      right: 0rpx;
    }
    .popup-content {
      padding: 20rpx 16rpx;
      @extend %fontSize32rpx;
      display: flex;
      align-items: center;

      .employee-name {
        color: #535050;
      }

      .phone-number {
        color: $theme-color;
        margin-left: 16rpx;
      }
    }

    .call-btn {
      width: 220rpx;
      color: #fff;
      @extend %fontSize32rpx;
      padding: 4rpx 0rpx;
      border: 4rpx solid $theme-color;
      background-color: $theme-color;
      border-radius: 16rpx;
      margin: 0 auto;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.no-data {
  background: #fff;
  color: #999;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  @extend %fontSize28rpx;
  .no-data-img {
    width: 320rpx;
    height: 120rpx;
  }
}
</style>
