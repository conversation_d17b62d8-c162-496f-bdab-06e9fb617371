<template>
  <view class="ts-container">
    <page-head @clickLeft="returnBack" title="排班管理"></page-head>
    <view class="subsection-box">
      <u-subsection
        mode="subsection"
        activeColor="#005BAC"
        :list="tabList"
        :current="activeTab"
        @change="sectionChange"
      ></u-subsection>
    </view>
    <view class="swiper-box">
      <me-scheduling ref="meScheduling" v-if="activeTab === 0" />
      <org-scheduling ref="orgScheduling" v-if="activeTab === 1" />
      <scheduling-completion-rate
        ref="schedulingCompletionRate"
        v-if="activeTab === 2"
      />
    </view>
  </view>
</template>

<script>
import meScheduling from './components/me-scheduling.vue';
import orgScheduling from './components/org-scheduling.vue';
import schedulingCompletionRate from './components/scheduling-completion-rate.vue';
export default {
  components: {
    meScheduling,
    orgScheduling,
    schedulingCompletionRate
  },
  data() {
    return {
      tabList: [
        { name: '我的排班' },
        { name: '科室排班信息' },
        { name: '排班完成度' }
      ],
      activeTab: 0
    };
  },
  methods: {
    sectionChange(index) {
      this.activeTab = index;
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.subsection-box {
  padding: 16rpx 16rpx 0;
  background: #fff;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
  overflow: hidden;
}
.scroll-container {
  height: 100%;
}
</style>
