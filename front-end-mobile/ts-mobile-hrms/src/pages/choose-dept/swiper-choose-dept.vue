<template>
  <view class="ts-container">
    <u-navbar title="人员选择" title-bold></u-navbar>
    <view class="search-container">
      <u-search
        v-model="keywords"
        :show-action="false"
        @search="search"
        @clear="clear"
      ></u-search>
      <view class="choose-dept-num">已选({{ chooseDeptNum }})</view>
    </view>
    <view class="choose-dept">
      <base-tabs-swiper
        v-if="type == 'all'"
        ref="tabs"
        fontSize="28"
        :list="list"
        :current="current"
        :is-scroll="false"
      ></base-tabs-swiper>
      <swiper
        :current="tabIndex"
        class="swiper-box"
        :duration="300"
        @change="ontabchange"
      >
        <swiper-item
          class="swiper-item"
          v-for="(item, index) in list"
          :key="index"
        >
          <mescroll
            v-if="item.mode == 'scoll'"
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <template #default>
              <dept-list
                :type="chooseType"
                :list="item.list"
                :value="value"
              ></dept-list>
            </template>
          </mescroll>
          <dept-list
            v-else-if="item.mode == 'notScoll'"
            :type="chooseType"
            :list="item.list"
            :value="value"
          ></dept-list>
        </swiper-item>
      </swiper>
    </view>
  </view>
</template>

<script>
const allList = [
  {
    name: '组织架构',
    mode: 'scoll',
    list: []
  },
  {
    name: '虚拟群组',
    mode: 'notScoll',
    list: []
  },
  {
    name: '个人群组',
    mode: 'notScoll',
    list: []
  }
];
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import deptList from './components/dept-list.vue';
export default {
  name: 'choose-dept',
  components: {
    mescroll,
    deptList
  },
  data() {
    return {
      tabIndex: 0,
      keywords: '',
      type: '',
      value: '',
      list: [],
      chooseDeptList: [],
      chooseType: '',
      api: '',
      params: {}
    };
  },
  computed: {
    chooseDeptNum() {
      return this.chooseDeptList.length;
    }
  },
  onLoad(opt) {
    this.chooseType = opt.chooseType;
    if (opt.getListType == 'scollSearch') {
      let searchApi = JSON.parse(uni.getStorageSync('searchApi'));
      this.api = searchApi.api;
      this.params = searchApi.params;
      (this.type = 'part'),
        (this.list = [
          {
            name: '处理人',
            mode: 'scoll',
            list: []
          }
        ]);
    } else {
      this.list = allList;
    }
  },
  methods: {
    search() {},
    clear() {},
    getListData(page, successCallback, errorCallback, index) {
      let api = '';
      if (this.type == 'all') {
        api = list[index].api;
      } else {
        api = this.api;
      }
      this.ajax
        .getDeptList(api, {
          ...this.params,
          pageNo: page.num,
          pageSize: page.size
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.list[index]['list'] = this.list[index]['list'].concat(rows);
    },
    datasInit() {}
  }
};
</script>

<style scoped>
.search-container {
  padding: 20rpx 30rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
}
.choose-dept-num {
  font-size: 28rpx;
  margin-left: 30rpx;
}
</style>
