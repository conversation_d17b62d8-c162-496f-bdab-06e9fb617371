<template>
  <view>
    <person-list-item-radio
      v-if="type == 'radio'"
      :list="list"
      @change="radioChange"
    ></person-list-item-radio>
    <person-list-item-checkbox
      v-else
      :list="list"
      @change="checkboxChange"
    ></person-list-item-checkbox>
  </view>
</template>

<script>
import personListItemRadio from './person-list-item-radio.vue';
import personListItemCheckbox from './person-list-item-checkbox.vue';
export default {
  name: 'person-list',
  components: {
    personListItemRadio,
    personListItemCheckbox
  },
  props: {
    type: {
      type: String,
      default: 'checkbox'
    },
    list: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  methods: {
    radioChange(val) {
      this.$emit('change', val);
    },
    checkboxChange(val) {
      this.$emit('change', val);
    }
  }
};
</script>

<style lang="scss" scoped></style>
