export function apiPerson(list, pageNum = 0, pageSize = 15, keyword) {
  return new Promise((resolute, reject) => {
    //延时一秒,模拟联网
    setTimeout(() => {
      try {
        let data = {
          pageCount: 0, // 总页数
          pageNo: pageNum, //当前页数
          pageSize: pageSize, //每页数量
          rows: [], // 数据列表
          totalCount: 0 // 总数量
        };

        // 符合关键词的记录
        let keywordList = [];
        if (!keyword || keyword == '全部') {
          // 搜索全部商品
          keywordList = list;
        } else {
          // 关键词搜索
          for (let i = 0; i < list.length; i++) {
            let item = list[i];
            if (item.name.indexOf(keyword) !== -1) {
              keywordList.push(item);
            }
          }
        }

        // 分页
        for (let i = (pageNum - 1) * pageSize; i < pageNum * pageSize; i++) {
          if (i >= keywordList.length) break;
          data.rows.push(keywordList[i]);
        }

        // 汇总数据
        data.totalCount = keywordList.length;
        data.pageCount = Math.ceil(data.totalCount / pageSize);

        resolute(data);
      } catch (e) {
        //模拟接口请求失败
        reject(e);
      }
    }, 10);
  });
}
