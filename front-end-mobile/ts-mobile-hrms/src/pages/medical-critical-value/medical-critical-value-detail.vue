<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" :title="title"></page-head>
    <view class="content-box">
      <view class="patient-box">
        <view class="item-top">
          <view class="item-patient-box">
            <view class="item-bed-number" v-if="detailData.visitType == 2">
              {{ detailData.bedNo }}床
            </view>
            <view class="item-visit-type" v-else>门诊</view>
            <img
              v-if="detailData.sexName.includes('女')"
              class="item-img"
              :src="require(`@/assets/img/woman.png`)"
              alt=""
            />
            <img
              v-else
              class="item-img"
              :src="require(`@/assets/img/man.png`)"
              alt=""
            />
            <view class="item-patient-info">{{ detailData.visitNo }}</view>
            <view class="item-patient-info">{{ detailData.name }}</view>
            <view class="item-patient-info">{{ detailData.age }}</view>
          </view>
          <u-icon
            name="phone-fill"
            :color="detailData.tel ? '#2979ff' : '#999'"
            size="36"
            @click="detailData.tel ? handleCallPhone() : null"
          ></u-icon>
        </view>
        <view class="item-info">
          <view class="item-project-info">
            {{ detailData.requestDeptName }}
          </view>
          <view class="item-project-info item-project-value">
            {{ detailData.diagnosisName }}
          </view>
        </view>
      </view>
      <view
        class="detail-info"
        v-for="(item, index) in checkDetailList"
        :key="index"
      >
        <view class="label">{{ item.label }}</view>
        <view
          class="value"
          :style="item.style"
          @click="item.clickable ? item.clickCallback(item.prop) : null"
        >
          {{
            item.format
              ? item.format(detailData[item.prop])
              : detailData[item.prop]
          }}
        </view>
      </view>
      <view class="medical-critical-info">
        <view class="label">危急值内容：</view>
        <view class="value">{{ detailData.panicValue }} </view>
      </view>
      <template v-if="type == 'check'">
        <view
          class="detail-info"
          v-for="(item, itemIndex) in handleDetailList"
          :key="`d_${itemIndex}`"
        >
          <view class="label">
            <view
              v-if="item.required"
              class="required oa-icon oa-icon-asterisks"
            ></view>
            {{ item.label }}
          </view>
          <view class="value">{{ detailData[item.prop] }}</view>
        </view>
      </template>
    </view>

    <view class="handle-measures-box" v-if="type == 'handle' && showHandling">
      <view class="label">
        <view class="required oa-icon oa-icon-asterisks"></view>
        处理措施
      </view>
      <u-input
        class="value"
        v-model="handleMeasuresText"
        type="textarea"
        :border="true"
        height="40"
        :auto-height="false"
        :maxlength="200"
        :clearable="false"
      />
      <view style="padding: 16rpx">
        <u-button type="primary" @click="handleSubmitMeasures">提交</u-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '',
      type: '',
      detailData: {},
      checkDetailList: [
        {
          label: '检查报告编号',
          prop: 'reportNo',
          style: {
            color: '#005bac'
          },
          clickable: true,
          clickCallback: () => {
            this.ajax
              .getTestRptItemRequestNew({
                repNo: this.detailData.reportNo
              })
              .then(res => {
                if (res.length && res[0].fileLink) {
                  uni.navigateTo({
                    url: `/pages/medical-critical-value/pdf-view?reportNo=${this.detailData.reportNo}`
                  });
                } else {
                  uni.showToast({
                    title: '暂无报告单文件信息',
                    icon: 'none'
                  });
                }
              })
              .catch(err => {
                console.log(err);
              });
          }
        },
        {
          label: '检查项目',
          prop: 'itemName'
        },
        {
          label: '责任医生',
          prop: 'bedDoctorName'
        },
        {
          label: '上报科室',
          prop: 'signDeptName'
        },
        {
          label: '上报人',
          prop: 'signUserName'
        },
        {
          label: '上报时间',
          prop: 'reportDate',
          format: e => {
            return this.$dayjs(e).format('YYYY-MM-DD HH:mm');
          }
        }
      ],
      handleDetailList: [
        {
          label: '处理措施',
          prop: 'processDesc',
          required: true
        },
        {
          label: '处理医生',
          prop: 'processUserName'
        },
        {
          label: '处理时间',
          prop: 'processDate'
        }
      ],
      handleMeasuresText: '',
      showHandling: false
    };
  },
  onLoad() {
    this.getCriticalValueConfiguration();
    this.detailData = JSON.parse(uni.getStorageSync('checkDetails'));
    this.type = this.detailData.index == 2 ? 'handle' : 'check';
    this.title = '危急值' + (this.type == 'handle' ? '处理' : '查看');
  },
  methods: {
    async getCriticalValueConfiguration() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'CUSTOM_CODE'
      });
      if (!res.object) {
        this.showHandling = false;
      } else {
        let showHandlingConfig = res.object.find(
          e => e.itemCode == 'DISP_HANDLING_CRITICAL_VALUE'
        );
        this.showHandling =
          showHandlingConfig == undefined ||
          showHandlingConfig.itemNameValue == 1
            ? true
            : false;
      }
    },
    handleCallPhone() {
      uni.makePhoneCall({
        phoneNumber: this.detailData.tel
      });
    },
    handleSubmitMeasures() {
      if (this.handleMeasuresText.length == 0) {
        uni.showToast({
          title: '请输入处理措施',
          icon: 'none'
        });
        return;
      }
      this.ajax
        .handleMedCrisisValue({
          id: this.detailData.id,
          processDesc: this.handleMeasuresText
        })
        .then(res => {
          uni.showToast({
            title: '处理成功',
            icon: 'none'
          });
          setTimeout(() => {
            this.returnBack();
          }, 1000);
        });
    },
    returnBack() {
      uni.removeStorageSync('checkDetails');
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  background: #fff;
}
.patient-box {
  padding: 16rpx;
  background: #e9f0ff;
}
.item-top,
.item-info,
.item-patient-box {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 16rpx;
}
.item-patient-box {
  flex: 1;
  margin-bottom: 0;
}
.item-bed-number {
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
}
.item-visit-type {
  font-size: 28rpx;
  color: $uni-color-success;
  border: 1px solid $uni-color-success;
  background: rgba($uni-color-success, 0.1);
  padding: 0 16rpx;
  border-radius: 28rpx;
}
.item-img {
  width: 50rpx;
  padding-left: 16rpx;
}
.item-patient-info,
.item-project-info {
  color: #333;
  padding-right: 16rpx;
  line-height: 1;
  &:not(:first-child) {
    padding: 0 16rpx;
  }
  &:not(:last-child) {
    border-right: 1px solid #bbb;
  }
}
.item-project-info {
  font-size: 28rpx;
  color: #666;
}
.item-project-value {
  flex: 1;
  color: $uni-color-error;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
.item-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.item-reporter-info {
  color: #999;
  font-size: 28rpx;
}
.detail-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #fff;
}
.label {
  font-size: 28rpx;
  position: relative;
  padding: 20rpx;
}
.value {
  font-size: 28rpx;
  padding: 20rpx;
}
.medical-critical-info {
  padding: 16rpx;
  background: #e9f0ff;
  .label {
    font-size: 32rpx;
    padding: 0;
  }
  .value {
    color: $uni-color-error;
    font-size: 32rpx;
    padding: 0;
  }
}
.handle-measures-box {
  background: #fff;
  .label {
    padding-bottom: 16rpx;
  }
  .value {
    margin: 0 20rpx;
  }
}
.required {
  color: red;
  position: absolute;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  left: 0;
  font-size: 12px;
}
/deep/.value .u-input__textarea {
  height: 120rpx !important;
}
</style>
