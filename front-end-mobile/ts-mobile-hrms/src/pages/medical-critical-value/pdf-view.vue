<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="检查报告信息"></page-head>
    <web-view :src="src" style="flex: 1;margin-top: 44px;"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      src: '',
      viewerUrl: window.__POWERED_BY_QIANKUN__
        ? `${window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__}static/pdfjs/web/viewer.html`
        : '/static/pdfjs/web/viewer.html'
    };
  },
  onLoad(opt) {
    this.reportNo = opt.reportNo;
    this.getPdfBase64();
  },
  methods: {
    returnBack() {
      uni.navigateBack();
    },
    getPdfBase64() {
      this.ajax
        .getTestRptItemRequestNew({
          repNo: this.reportNo
        })
        .then(res => {
          let fileType = '1';
          let fileLink = res[0].fileLink;
          if (fileLink.startsWith('ftp')) {
            fileType = '2';
          }
          this.ajax
            .getBase64Pdf({
              filePath: fileLink,
              fileType
            })
            .then(res1 => {
              this.base64ToBlob(res1);
            });
        });
    },
    base64ToBlob(url) {
      let str = atob(url);
      let length = str.length;
      const u8arr = new Uint8Array(length);
      while (length--) {
        u8arr[length] = str.charCodeAt(length);
      }
      const blob = new Blob([u8arr], {
        type: 'application/pdf'
      });
      const urlObject = window.URL || window.webkitURL || window;
      let pdfUrl = urlObject.createObjectURL(blob);
      this.src = this.viewerUrl + '?file=' + encodeURI(pdfUrl);
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
