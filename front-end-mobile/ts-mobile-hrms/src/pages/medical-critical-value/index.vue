<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="危急值管理"></page-head>
    <base-tabs-swiper
      ref="tabSwiper"
      class="tab-swiper-box"
      :list="tabList"
      badgeType="text"
      :current="currentTab"
      :is-scroll="false"
      @change="handleChangeTab"
    ></base-tabs-swiper>
    <swiper
      class="swiper-box"
      :current="currentTab"
      :duration="300"
      @change="handleChangeSwiper"
    >
      <swiper-item
        class="swiper-item"
        v-for="(item, index) in tabList"
        :key="index"
      >
        <view class="date-box" v-if="index == 1">
          <text
            class="current-month-btn"
            :class="{ active: isBtnCurrentMonth }"
            @tap="handleChangeCurrentMonth"
          >
            本月
          </text>
          <view class="select-date-box" @tap="handleShowSelectMonth">
            <view class="select-month">
              {{ $dayjs(selectedMonth).format('YYYY年M月') }}
            </view>
            <u-icon name="arrow-down" color="#666"></u-icon>
          </view>
        </view>
        <view class="scroll-box">
          <mescroll
            :ref="`mescroll${index}`"
            :mescrollIndex="index"
            @getDatas="getListData"
            @setDatas="setListData"
            @datasInit="datasInit"
          >
            <view
              class="medical-critical-item"
              v-for="valueItem in item.list"
              :key="valueItem.id"
              @click="handleCheckDetail(valueItem)"
            >
              <view class="item-top">
                <view class="item-patient-box">
                  <view class="item-bed-number" v-if="valueItem.visitType == 2">
                    {{ valueItem.bedNo }}床
                  </view>
                  <view class="item-visit-type" v-else>门诊</view>
                  <img
                    v-if="valueItem.sexName.includes('女')"
                    class="item-img"
                    :src="require(`@/assets/img/woman.png`)"
                    alt=""
                  />
                  <img
                    v-else
                    class="item-img"
                    :src="require(`@/assets/img/man.png`)"
                    alt=""
                  />
                  <view class="item-patient-info">{{ valueItem.visitNo }}</view>
                  <view class="item-patient-info">{{ valueItem.name }}</view>
                  <view class="item-patient-info">{{ valueItem.age }}</view>
                </view>
                <view
                  class="item-tag"
                  :class="{ 'tag-warning': item.index == 2 }"
                >
                  {{ item.index == 2 ? '处理' : '查看' }}
                </view>
              </view>
              <view class="item-info">
                <view class="item-project-info">{{ valueItem.itemName }}</view>
                <view class="item-project-info item-project-value">
                  {{ valueItem.panicValue }}
                </view>
              </view>
              <view class="item-bottom">
                <view class="item-reporter-info">
                  上报人：{{ valueItem.signUserName }}
                </view>
                <view class="item-reporter-info">
                  上报时间：{{
                    $dayjs(valueItem.reportDate).format('YYYY-MM-DD HH:mm')
                  }}
                </view>
              </view>
            </view>
          </mescroll>
        </view>
      </swiper-item>
    </swiper>
    <date-picker
      ref="selectMonth"
      mode="date"
      startDate="2000-01-01"
      endDate="2100-12-31"
      :disabled-after="true"
      :value="selectedMonth"
      fields="month"
      @confirm="handleConfirmSelectMonth"
    />
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import DatePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    mescroll,
    DatePicker
  },
  data() {
    return {
      tabList: [
        {
          name: '待处理',
          index: 2,
          count: 0,
          list: []
        },
        {
          name: '已处理',
          index: 3,
          count: 0,
          list: []
        }
      ],
      currentTab: 0,
      currentMonth: this.$dayjs().format('YYYY-MM'), // 不变的当前月份
      selectedMonth: this.$dayjs().format('YYYY-MM'), //页面选择的当前月份
      isBtnCurrentMonth: true
    };
  },
  methods: {
    handleChangeTab(index) {
      this.currentTab = index;
    },
    async handleChangeSwiper(e) {
      let index = e.target.current || e.detail.current;
      this.currentTab = index;
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      this.ajax
        .getMedicalCriticalValueList({
          sidx: 't1.status,t1.report_date',
          sord: 'desc',
          pageSize: page.size,
          pageNo: page.num,
          index: this.tabList[index]['index'],
          startDate:
            index == 0
              ? ''
              : this.$dayjs(this.selectedMonth)
                  .startOf('month')
                  .format('YYYY-MM-DD'),
          endDate:
            index == 0
              ? ''
              : this.$dayjs(this.selectedMonth)
                  .endOf('month')
                  .format('YYYY-MM-DD')
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['count'] = totalCount;
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
    },
    datasInit(keywords, index) {
      this.tabList[index]['list'] = [];
    },
    handleCheckDetail(valueItem) {
      uni.setStorageSync('checkDetails', JSON.stringify(valueItem));
      uni.navigateTo({
        url: `/pages/medical-critical-value/medical-critical-value-detail`
      });
    },
    handleChangeCurrentMonth() {
      this.selectedMonth = this.currentMonth;
      this.isBtnCurrentMonth = true;
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    handleShowSelectMonth() {
      this.$refs.selectMonth.show();
    },
    handleConfirmSelectMonth(res) {
      this.isBtnCurrentMonth = this.currentMonth === res.result;
      this.selectedMonth = res.result;
      this.$refs[`mescroll${this.currentTab}`][0].downCallback();
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  background: #fff;
}
.tab-swiper-box {
  border-bottom: 1px solid #eee;
}
.swiper-box {
  flex: 1;
  .swiper-item {
    flex: 1;
    flex-direction: column;
    display: flex;
  }
}
.scroll-box {
  flex: 1;
  position: relative;
}
.date-box {
  position: relative;
  text-align: center;
  padding: 16rpx;
  .current-month-btn {
    position: absolute;
    left: 16rpx;
    border: 1px solid #999;
    color: #999;
    background: #eee;
    border-radius: 32rpx;
    font-size: 28rpx;
    padding: 0 16rpx;
    &.active {
      color: $theme-color;
      border-color: $theme-color;
      background: rgba($theme-color, 0.1);
    }
  }
  .select-date-box {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.medical-critical-item {
  padding: 16rpx;
  position: relative;
  &:not(:last-child)::after {
    content: ' ';
    position: absolute;
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 16rpx;
    right: 0;
  }
  .item-top,
  .item-info,
  .item-patient-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 16rpx;
  }
  .item-patient-box {
    flex: 1;
    margin-bottom: 0;
  }
  .item-bed-number {
    color: #333;
    font-size: 36rpx;
    font-weight: bold;
  }
  .item-visit-type {
    font-size: 28rpx;
    color: $uni-color-success;
    border: 1px solid $uni-color-success;
    background: rgba($uni-color-success, 0.1);
    padding: 0 16rpx;
    border-radius: 28rpx;
  }
  .item-img {
    width: 50rpx;
    padding-left: 16rpx;
  }
  .item-patient-info,
  .item-project-info {
    color: #333;
    padding-right: 16rpx;
    line-height: 1;
    &:not(:first-child) {
      padding: 0 16rpx;
    }
    &:not(:last-child) {
      border-right: 1px solid #bbb;
    }
  }
  .item-project-info {
    font-size: 28rpx;
    color: #666;
  }
  .item-project-value {
    flex: 1;
    color: $uni-color-error;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
  .item-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .item-reporter-info {
    color: #999;
    font-size: 28rpx;
  }
  .item-tag {
    font-size: 32rpx;
    color: $theme-color;
    font-weight: bold;
  }
  .tag-warning {
    color: $uni-color-error;
  }
}
</style>
