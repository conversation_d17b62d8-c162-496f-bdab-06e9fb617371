<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="申请详情"></page-head>
    <view class="application-detail-box">
      <view class="detail-box" v-for="(item, index) in detailList" :key="index">
        <view class="detail-box-title">
          {{ item.title }}
        </view>
        <view class="detail-field-box">
          <view
            class="detail-field"
            v-for="(fieldItem, fieldIndex) in item.fieldList"
            :key="`${index}-${fieldIndex}`"
          >
            <view class="detail-field-label" v-if="fieldItem.label">
              {{ fieldItem.label }}
            </view>
            <view
              class="detail-field-text"
              :style="fieldItem.style ? fieldItem.style : {}"
            >
              {{
                fieldItem.format
                  ? fieldItem.format(detailData)
                  : detailData[fieldItem.prop]
              }}
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      detailList: [
        {
          title: '申请信息',
          fieldList: [
            {
              label: '申请人',
              prop: 'applyUserName'
            },
            {
              label: '科室',
              prop: 'applyDeptName'
            },
            {
              label: '申请任职科室',
              prop: 'tenureDeptName'
            },
            {
              label: '申请类型',
              prop: 'applyType'
            },
            {
              label: '授权类型',
              prop: 'authorizeType'
            },
            {
              label: '任期时间',
              prop: 'tenureDate',
              format: data => {
                return `${data.tenureStartDate}至${data.tenureEndDate}`;
              }
            }
          ]
        },
        {
          title: '本人小结',
          fieldList: [
            {
              label: '',
              prop: 'summary',
              style: {
                minHeight: '300rpx',
                background: '#fff'
              }
            }
          ]
        },
        {
          title: '职责描述',
          fieldList: [
            {
              label: '',
              prop: 'remark',
              style: {
                minHeight: '300rpx',
                background: '#fff'
              }
            }
          ]
        }
      ],
      detailData: {}
    };
  },
  onLoad() {
    this.detailData = JSON.parse(uni.getStorageSync('checkDetails'));
  },
  methods: {
    returnBack() {
      uni.removeStorageSync('checkDetails');
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.application-detail-box {
  background: #fff;
  .detail-box {
    border-bottom: 1px solid #eee;
  }
  .detail-box-title {
    color: #333;
    position: relative;
    padding: 8rpx 16rpx;
    padding-left: 32rpx;
    font-weight: bold;
    &::after {
      content: '';
      position: absolute;
      top: 16rpx;
      bottom: 16rpx;
      left: 16rpx;
      border: 2px solid $theme-color;
    }
  }
  .detail-field-box {
    .detail-field {
      display: flex;
      justify-content: space-between;
      align-items: center;
      &:nth-child(odd) {
        background: #f5f5f5;
      }
      .detail-field-label {
        padding: 8rpx 16rpx;
        font-size: 28rpx;
      }
      .detail-field-text {
        flex: 1;
        text-align: right;
        padding: 8rpx 16rpx;
        font-size: 28rpx;
      }
    }
  }
}
</style>
