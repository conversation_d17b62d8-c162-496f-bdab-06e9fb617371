<template>
  <view class="duty-details">
    <view class="head-container">
      <view class="select-date-header">
        <u-icon name="arrow-left" color="#666" @tap="changeDate(-1)"></u-icon>
        <view class="current-date-btn" @tap="changeDate(0)">
          {{ isBtnCurrentDate ? '今天' : currentDate }}
        </view>
        <u-icon name="arrow-right" color="#666" @tap="changeDate(1)"></u-icon>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentDate: this.$dayjs().format('YYYY-MM-DD'),
      isBtnCurrentDate: true
    };
  },
  methods: {
    changeDate(offset) {
      if (offset === 0) {
        this.currentDate = this.$dayjs().format('YYYY-MM-DD');
        this.isBtnCurrentDate = true;
      } else {
        const newDate = this.$dayjs(this.currentDate).add(offset, 'day');
        this.currentDate = newDate.format('YYYY-MM-DD');
        this.isBtnCurrentDate =
          this.currentDate === this.$dayjs().format('YYYY-MM-DD');
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.head-container {
  background: #fff;
  padding: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.select-date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.current-date-btn {
  width: 280rpx;
  color: #333;
  text-align: center;
  transition: all 0.2s ease;
  font-size: 32rpx;
}
</style>
