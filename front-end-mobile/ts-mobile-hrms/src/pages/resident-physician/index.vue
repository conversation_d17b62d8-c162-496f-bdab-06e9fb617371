<template>
  <view class="ts-container">
    <page-head @clickLeft="returnBack" title="三级医师"></page-head>
    <view class="subsection-box">
      <u-subsection
        mode="subsection"
        activeColor="#005BAC"
        :list="tabList"
        :current="activeTab"
        @change="sectionChange"
      ></u-subsection>
    </view>
    <view class="swiper-box">
      <!-- <duty-details v-if="activeTab == 0" /> -->
      <hospitalization-application :authorizeType="tabList[activeTab].type" />
    </view>
  </view>
</template>

<script>
// import DutyDetails from './components/duty-details.vue';
import HospitalizationApplication from './components/hospitalization-application.vue';
export default {
  components: {
    HospitalizationApplication
    // DutyDetails
  },
  data() {
    return {
      tabList: [
        // { name: '值班详情' },
        { name: '总住院申请', type: '住院医师资质' },
        { name: '二级医师', type: '二级医师资质' },
        { name: '三级医师', type: '三级医师资质' }
      ],
      activeTab: 0
    };
  },
  methods: {
    sectionChange(index) {
      this.activeTab = index;
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}
.subsection-box {
  padding: 16rpx 16rpx 0;
  background: #fff;
}
.swiper-box {
  flex: 1;
}
.swiper-item {
  height: 100%;
  overflow: hidden;
}
.scroll-container {
  height: 100%;
}
</style>
