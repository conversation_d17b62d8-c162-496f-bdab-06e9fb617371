<template>
  <view class="ts-content">
    <page-head
      title="科室选择"
      @clickLeft="returnBack"
      rightText="清空"
      @clickRight="clearSelection"
    ></page-head>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :searchInput="true"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <!-- 数据列表 -->
        <view class="contact-list">
          <view
            class="contact-item"
            v-for="item in dataList"
            :key="item.id"
            @tap="chooseItem(item)"
          >
            <uni-icons
              :type="
                item.choose
                  ? 'checkbox-filled'
                  : item.disabled
                  ? 'smallcircle-filled'
                  : 'circle'
              "
              :color="item.disabled ? '#eee' : item.choose ? '#005BAC' : '#aaa'"
              size="48"
              style="line-height: 1;"
            />
            <view
              class="info"
              :style="{ color: item.disabled ? '#aaa' : '#333' }"
            >
              <text>{{ item.name }}</text>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
    <view class="button-groups">
      <button
        class="button-item selected"
        type="default"
        :disabled="btnDisabled"
        @click="togglePopup('allBottom', 'popup')"
      >
        已选 {{ selectNum }}
      </button>
      <button class="button-item" type="primary" @click="confirmBtn">
        确定
      </button>
    </view>
    <uni-popup ref="showpopup" :type="popupType">
      <page-head
        title="科室选择"
        :isleft="false"
        right-text="确定"
        @clickRight="popupConfirm"
      ></page-head>
      <view v-if="popupSearchInput" class="search">
        <uni-search-bar
          radius="100"
          placeholder="搜索"
          searchBgColor="#eeeeee"
          borderColor="transparent"
          bgColor="#FFFFFF"
          cancelButton="none"
          @confirm="popupSearch"
        />
      </view>
      <view class="popup-content">
        <!-- 数据列表 -->
        <view class="contact-list" v-if="resource.length > 0">
          <view
            class="contact-item"
            v-for="(item, index) in resource"
            :key="item.id"
          >
            <view class="info">
              <text>{{ item.name }}</text>
            </view>
            <uni-icons
              @tap="deletItem(item, index)"
              type="close"
              color="#aaa"
              size="48"
            />
          </view>
        </view>
        <view
          v-else-if="resource.length == 0 && selectedList.length == 0"
          class="nothing"
        >
          暂无数据
        </view>
        <view v-else class="nothing">没有找到相关科室</view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
//导入vuex的mapState值和mapMutations方法
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import uniSearchBar from '@/components/uni-search-bar/uni-search-bar.vue';
export default {
  components: {
    mescroll,
    uniPopup,
    uniSearchBar
  },
  data() {
    return {
      dataList: [], //数据列表
      selectedList: [], //已选数组
      disabledList: [], //禁用数组
      selectNum: null, //已选数量
      btnDisabled: true, //按钮是否禁用
      popupType: '', //弹出层类型
      popupSearchInput: true, //弹出层是否显示搜索框
      resource: [], //搜索数据
      checkType: ''
    };
  },
  computed: {
    ...mapState(['token'])
  },
  watch: {
    selectedList(newVal, oldVal) {
      if (newVal.length == 0) {
        this.selectNum = null;
        this.btnDisabled = true;
      } else {
        this.btnDisabled = false;
        this.selectNum = newVal.length;
      }
    }
  },
  onLoad(opt) {
    let dept_list = uni.getStorageSync('dept_list');
    if (dept_list != '[]') {
      this.selectedList = JSON.parse(dept_list);
      this.selectNum = this.selectedList.length;
    }
    let disabled_dept_list = uni.getStorageSync('disabled_dept_list');
    if (disabled_dept_list && disabled_dept_list != '[]') {
      this.disabledList = JSON.parse(disabled_dept_list);
    }
    this.checkType = opt.checkType;
  },
  methods: {
    //获取部门列表
    async getListData(page, successCallback, errorCallback, keywords) {
      let _self = this;
      // await _self.ajax.getDeptList({
      //   token: _self.token,
      //   rows: page.size,
      //   page: page.num,
      //   deptname: keywords,
      //   sidx: 'deptcode',
      //   sord: 'asc'
      // });
      await _self.ajax
        .getAllOrgList({
          pageSize: page.size,
          pageNo: page.num,
          name: keywords
        })
        .then(res => {
          let rows = res.rows;
          let rowsArr = [];
          rows.forEach(item => {
            let isChoose = _self.selectedList.some(one => {
              return one.id == item.organizationId;
            });
            let isDisabled = _self.disabledList.some(one => {
              return one.id == item.organizationId;
            });
            // rowsArr.push({
            //   name: item.deptname,
            //   id: item.deptcode,
            //   choose: isChoose,
            //   disabled: isDisabled
            // });
            rowsArr.push({
              name: item.name,
              id: item.organizationId,
              choose: isChoose,
              disabled: isDisabled
            });
          });
          successCallback(rowsArr);
        })
        .catch(() => {
          errorCallback();
        });
    },
    //设置数据
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    //搜索时清空数据
    datasInit(keywords) {
      this.dataList = [];
    },
    chooseItem(item) {
      let _self = this;
      //判断是否已禁掉
      let disabled = _self.chooseFilter(item.id, _self.disabledList);
      if (disabled) return false;
      //使用过滤器判断该选项是否已选中
      let choose = _self.chooseFilter(item.id, _self.selectedList);
      if (_self.checkType == 'radio') {
        _self.selectedList = [];
        if (!choose) {
          _self.dataList.forEach(row => {
            row.choose = false;
          });
          _self.selectedList.push(item);
          _self.$set(item, 'choose', true);
        } else {
          _self.$delete(item, 'choose');
        }
      } else if (_self.checkType == 'checkBox') {
        // 多选模式
        if (!choose) {
          _self.selectedList.push(item);
          _self.$set(item, 'choose', true);
        } else {
          let newSelectedList = _self.selectedList.filter(i => i.id != item.id);
          _self.selectedList = newSelectedList;
          _self.$delete(item, 'choose');
        }
      }
    },
    chooseFilter(value, list) {
      return list.some(one => one.id == value);
    },
    confirmBtn() {
      let _self = this;
      uni.$emit('deptlist', _self.selectedList);
      uni.navigateBack({
        delta: 1
      });
    },
    togglePopup(type, open) {
      let _self = this;
      _self.popupType = type;
      _self.$nextTick(() => {
        _self.resource = _self.selectedList;
        _self.$refs[`show${open}`].open();
      });
    },
    deletItem(item, i) {
      this.resource.splice(i, 1);
      for (let j = 0; j < this.selectedList.length; j++) {
        if (this.selectedList[j]['id'] == item.id) {
          this.selectedList.splice(j, 1);
          break;
        }
      }
      for (let j = 0; j < this.dataList.length; j++) {
        if (this.dataList[j]['id'] == item.id) {
          this.dataList[j]['choose'] = false;
          break;
        }
      }
    },
    clearSelection() {
      this.resource = [];
      this.selectedList = [];
      this.dataList.map(item => {
        item.choose = false;
      });
    },
    popupSearch(res) {
      let temp = [];
      let _self = this;
      _self.selectedList.forEach(e => {
        if (e.name.indexOf(res.value) > -1) {
          temp.push(e);
        }
      });
      _self.resource = temp;
    },
    popupConfirm() {
      this.$refs['showpopup'].close();
    },
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>
<style lang="scss" scoped>
.ts-content {
  display: flex;
  height: 100%;
  flex-direction: column;
  overflow: hidden;
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .button-groups {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 10px;
    box-sizing: border-box;
    background-color: #ffffff;
    box-shadow: 0 1px 6px #cccccc;
    z-index: 10;
    .button-item {
      font-size: 28rpx;
      margin: 0 20rpx;
      width: 160rpx;
      &:first-child::after {
        border: 1px solid #005bac;
      }
      &::after {
        width: 100%;
        height: 100%;
        transform: none;
        border-radius: 10rpx;
      }
    }
    .button-item.selected {
      color: #005bac;
      background-color: #ffffff;
    }
  }
  .popup-content {
    position: absolute;
    top: 96px;
    bottom: 0;
    left: 0;
    right: 0;
  }
  .contact-list {
    height: 100%;
    overflow: scroll;
    .contact-item {
      padding: 20rpx;
      display: flex;
      align-items: center;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 48rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .info {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        padding-left: 10rpx;
      }
      .iconImg {
        width: 80rpx;
        height: 80rpx;
        margin: 0 20rpx;
        border-radius: 100%;
        color: #ffffff;
        text-align: center;
        line-height: 2.8;
      }
      .sexMan {
        background-color: $sexman-color;
      }
      .sexWoman {
        background-color: $sexwoman-color;
      }
    }
  }
  .nothing {
    color: #999999;
    text-align: center;
  }
}
</style>
