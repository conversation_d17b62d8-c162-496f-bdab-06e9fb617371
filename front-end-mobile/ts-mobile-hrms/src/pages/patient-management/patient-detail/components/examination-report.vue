<template>
  <view class="examination-report-box">
    <mescroll
      ref="mescroll"
      :searchInput="false"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <uni-collapse accordion>
        <uni-collapse-item
          v-for="(item, index) in examinationReportReportList"
          :key="index"
        >
          <template v-slot:title>
            <view>
              <view>报告时间：{{ item.reportDate }}</view>
              <view>检查项目：{{ item.examTypeLargeName }}</view>
              <view>检查部位：{{ item.examBodyName }}</view>
            </view>
          </template>
          <view class="collapse-contact-item">
            <view class="field-text">诊断医生：{{ item.reportUserName }}</view>
            <view class="field-text">诊断内容：{{ item.reportResult }}</view>
            <view class="field-text">建议：{{ item.reportRemarks }}</view>
            <!-- <view class="field-text jump-button">
              <text @click="handleCheckDetail(item)">查看详情</text>
            </view> -->
          </view>
        </uni-collapse-item>
      </uni-collapse>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      examinationReportReportList: []
    };
  },
  created() {},
  methods: {
    async getListData(page, successCallback, errorCallback) {
      await this.ajax
        .getExamRptRequestNew({
          pageSize: page.size,
          pageNo: page.num,
          patnId: this.patientInfo.patnId,
          patnNo: this.patientInfo.inPatientNo
        })
        .then(res => {
          let rows = res;
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.examinationReportReportList = this.examinationReportReportList.concat(
        rows
      );
    },
    datasInit() {
      this.examinationReportReportList = [];
    },
    handleCheckDetail(item) {
      uni.openDocument({
        filePath: item.fileLink,
        showMenu: true,
        success: function(res) {
          console.log('打开文档成功');
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.examination-report-box {
  height: 100%;
  position: relative;
}
/deep/ {
  .uni-collapse-cell--disabled,
  .uni-collapse-cell--open,
  .uni-collapse-cell__title:active {
    background: #fff !important;
  }
  .uni-collapse-cell--hide,
  .uni-collapse-cell__title {
    height: auto;
  }
  .uni-collapse-cell__title {
    padding: 16rpx;
  }
  .uni-collapse-cell__title-text {
    font-size: 32rpx;
  }
}
.collapse-contact-item {
  padding: 16rpx;
  background: #f5f5f5;
  border-top: #e5e5e5;

  .field-text {
    font-size: 28rpx;
    margin-bottom: 8rpx;
    color: #333;
  }
  .jump-button {
    text-align: right;
    color: $theme-color;
  }
}
</style>
