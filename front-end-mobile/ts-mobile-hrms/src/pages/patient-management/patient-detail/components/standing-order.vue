<template>
  <view class="standing-order-box">
    <mescroll
      ref="mescroll"
      :searchInput="false"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <uni-collapse accordion>
        <uni-collapse-item
          v-for="item in standingOrderList"
          :key="item.orderRecordId"
        >
          <template v-slot:title>
            <view class="collapse-item">
              <view class="collapse-item-title">
                <view class="title-text">{{ item.orderName }}</view>
                <view
                  class="status-text"
                  :style="{ background: item.status.color }"
                >
                  {{ item.status.name }}
                </view>
              </view>
              <view class="collapse-item-sub-text">
                <view class="order-time-text">
                  {{ item.orderBDate ? item.orderBDate.replace('T', ' ') : '' }}
                </view>
                <view class="dosage-text">
                  {{ item.dosage || '-' }}/{{ item.dosageUnitName || '-' }}/{{
                    item.usageName || '-'
                  }}/{{ item.frequencyName || '-' }}/{{ item.dropsper || '-' }}
                </view>
              </view>
            </view>
          </template>
          <view class="collapse-item-content">
            <view class="content-field">
              首/末次
              <text class="field-text">
                {{ item.firstTimes || '-' }}/{{ item.termialTimes || '-' }}
              </text>
            </view>
            <view class="content-field">
              开(停)嘱医生
              <text class="field-text"
                >{{ item.orderDocName || '-' }}/{{
                  item.orderEDocName || '-'
                }}</text
              >
            </view>
            <view class="content-field">
              停嘱时间
              <text class="field-text">
                {{ item.orderEDate ? item.orderEDate.replace('T', ' ') : '无' }}
              </text>
            </view>
          </view>
        </uni-collapse-item>
      </uni-collapse>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import commonMixin from '../mixins/common-mixin';
export default {
  components: {
    mescroll
  },
  mixins: [commonMixin],
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      standingOrderList: []
    };
  },
  methods: {
    async getListData(page, successCallback, errorCallback) {
      await this.ajax
        .getQueryInPatientOrder({
          pageSize: page.size,
          pageNo: page.num,
          mngTypes: 0,
          hospCode: this.patientInfo.hospCode,
          sortOrder: 2,
          patnId: this.patientInfo.patnId
        })
        .then(res => {
          let rows = res.rows.map(row => {
            row.status = this.statusColor[row.statusFlag];
            return row;
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.standingOrderList = this.standingOrderList.concat(rows);
    },
    datasInit() {
      this.standingOrderList = [];
    }
  }
};
</script>

<style lang="scss" scoped>
.standing-order-box {
  height: 100%;
  position: relative;
}

/deep/ {
  .uni-collapse-cell--disabled,
  .uni-collapse-cell--open {
    background: #fff !important;
  }
  .uni-collapse-cell--hide,
  .uni-collapse-cell__title {
    height: auto;
  }
  .uni-collapse-cell__title {
    padding: 16rpx;
  }
  .uni-collapse-cell__title-text {
    font-size: 32rpx;
  }
}
.collapse-item {
  flex: 1;
}
.collapse-item-title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
}
.status-text {
  font-size: 26rpx;
  color: #fff;
  padding: 8rpx;
  border-radius: 4px;
  line-height: 1;
}
.collapse-item-content {
  background: #f5f5f5;
  border-top: #e5e5e5;
  padding: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.content-field {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 28rpx;
  color: #666;
}
.field-text {
  color: #333;
}
.order-time-text,
.dosage-text {
  line-height: 1;
  font-size: 28rpx;
  color: #666;
  margin-right: 60rpx;
  display: inline-block;
}
</style>
