<template>
  <view class="laboratory-test-report-box">
    <mescroll
      ref="mescroll"
      :searchInput="false"
      :page="{ num: 0, size: 1000 }"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <view
        class="laboratory-test-report-item"
        :class="{ crisis: item.crisisFlag == 1 }"
        v-for="(item, index) in laboratoryTestReportList"
        :key="index"
        @click="handleCheckDetail(item)"
      >
        <view class="test-text">
          {{ item.orderName }}
        </view>
        <view class="test-time-text"> 检验时间：{{ item.testDate }} </view>
        <template v-if="item.crisisFlag == 1">
          <view class="crisis-tag"></view>
          <u-icon
            class="crisis-icon"
            color="#fff"
            size="40"
            name="info-circle-fill"
          ></u-icon>
        </template>
      </view>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      laboratoryTestReportList: []
    };
  },
  created() {},
  methods: {
    async getListData(page, successCallback, errorCallback) {
      await this.ajax
        .getTestRptItemRequestNew({
          pageSize: page.size,
          pageNo: page.num,
          patnId: this.patientInfo.patnId
        })
        .then(res => {
          let rows = res.map(row => {
            return row;
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.laboratoryTestReportList = this.laboratoryTestReportList.concat(
        rows
      );
    },
    datasInit() {
      this.laboratoryTestReportList = [];
    },
    handleCheckDetail(item) {
      uni.navigateTo({
        url: `/pages/patient-management/patient-detail/laboratory-test-report-detail?reportNo=${item.reportNo}&orderName=${item.orderName}&reportDate=${item.reportDate}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.laboratory-test-report-box {
  height: 100%;
  position: relative;
}

.laboratory-test-report-item {
  position: relative;
  padding: 16rpx;
  background: #fff;
  &.crisis {
    padding-right: 136rpx;
  }
  .crisis-tag {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-left: 120rpx solid transparent;
    border-top: 80rpx solid $uni-color-warning;
  }
  .crisis-icon {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    z-index: 999;
    transform: rotate(180deg);
  }
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 0;
    right: 0;
  }
}
.test-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}
.test-time-text {
  font-size: 28rpx;
  color: #666;
}
</style>
