<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="文件信息"></page-head>
    <web-view :src="src" style="flex: 1;margin-top: 44px;"></web-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      src: '',
      viewerUrl: window.__POWERED_BY_QIANKUN__
        ? `${window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__}static/pdfjs/web/viewer.html`
        : '/static/pdfjs/web/viewer.html',
      patientInfo: {}
    };
  },
  onLoad(opt) {
    this.patientInfo = {
      patnId: opt.patnId || '',
      emrId: opt.emrId || '',
      hospCode: opt.hospCode || '',
      templateFiletype: opt.templateFiletype || ''
    };
    this.getPdfBase64();
  },
  methods: {
    returnBack() {
      uni.removeStorageSync('patientInfo');
      uni.navigateBack();
    },
    getPdfBase64() {
      this.ajax
        .getQueryInpEmrFileInfoPdfStream({
          patnId: this.patientInfo.patnId,
          emrId: this.patientInfo.emrId,
          hospCode: this.patientInfo.hospCode,
          templateFiletype: this.patientInfo.templateFiletype
        })
        .then(async res => {
          if (!res[0]?.BASESTR) {
            uni.showToast({ title: '文件数据为空', icon: 'none' });
            return;
          }
          const pdfUrl = res[0].BASESTR;
          this.base64ToBlob(pdfUrl);
        });
    },
    base64ToBlob(url) {
      let str = atob(url);
      let length = str.length;
      const u8arr = new Uint8Array(length);
      while (length--) {
        u8arr[length] = str.charCodeAt(length);
      }
      const blob = new Blob([u8arr], {
        type: 'application/pdf'
      });
      const urlObject = window.URL || window.webkitURL || window;
      let pdfUrl = urlObject.createObjectURL(blob);
      this.src = this.viewerUrl + '?file=' + encodeURI(pdfUrl);
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>
