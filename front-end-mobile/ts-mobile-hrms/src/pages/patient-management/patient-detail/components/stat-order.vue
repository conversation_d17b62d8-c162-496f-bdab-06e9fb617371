<template>
  <view class="stat-order-box">
    <view class="search-box">
      <text>日期选择：</text>
      <text class="time-item" @tap="showPicker">
        {{ searchData.beginDate }}
      </text>
      <text class="time-divider">-</text>
      <text class="time-item" @tap="showPicker">
        {{ searchData.endDate }}
      </text>
      <text class="search-button" @click="handleSearch">搜索</text>
    </view>
    <view class="stat-order-list-box">
      <mescroll
        ref="mescroll"
        :searchInput="false"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <uni-collapse accordion>
          <uni-collapse-item
            v-for="item in statOrderList"
            :key="item.orderRecordId"
          >
            <template v-slot:title>
              <view class="collapse-item">
                <view class="collapse-item-title">
                  <view class="title-text">{{ item.orderName }}</view>
                  <view
                    class="status-text"
                    :style="{ background: item.status.color }"
                  >
                    {{ item.status.name }}
                  </view>
                </view>
                <view class="collapse-item-sub-text">
                  <view class="order-time-text">
                    {{
                      item.orderBDate ? item.orderBDate.replace('T', ' ') : ''
                    }}
                  </view>
                  <view class="dosage-text">
                    {{ item.dosage || '-' }}/{{ item.dosageUnitName || '-' }}/{{
                      item.usageName || '-'
                    }}/{{ item.frequencyName || '-' }}/{{
                      item.dropsper || '-'
                    }}
                  </view>
                </view>
              </view>
            </template>
            <view class="collapse-item-content">
              <view class="content-field">
                首/末次
                <text class="field-text">
                  {{ item.firstTimes || '-' }}/{{ item.termialTimes || '-' }}
                </text>
              </view>
              <view class="content-field">
                总量/总单位
                <text class="field-text">
                  {{ item.amount || '-' }}/{{ item.amountUnitName || '-' }}
                </text>
              </view>
              <view class="content-field">
                开嘱医生
                {{ item.orderDocName || '-' }}
              </view>
            </view>
          </uni-collapse-item>
        </uni-collapse>
      </mescroll>
    </view>
    <date-picker
      startDate="2000-01-01"
      :value="agentTimeArr"
      endDate="2100-12-31"
      pickerZindex="999"
      mode="range"
      :current="true"
      @confirm="onConfirm"
      @cancel="onCancel"
      ref="dataRange"
    ></date-picker>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
import DatePicker from '@/components/picker/date-picker.vue';
import commonMixin from '../mixins/common-mixin';
export default {
  components: {
    mescroll,
    DatePicker
  },
  mixins: [commonMixin],
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      searchData: {
        beginDate: this.$dayjs().format('YYYY-MM-DD'),
        endDate: this.$dayjs().format('YYYY-MM-DD')
      },
      agentTimeArr: [],
      statOrderList: []
    };
  },
  methods: {
    async getListData(page, successCallback, errorCallback) {
      await this.ajax
        .getQueryInPatientOrder({
          pageSize: page.size,
          pageNo: page.num,
          mngTypes: 1,
          startOrderBDate: this.searchData.beginDate,
          endOrderBDate: this.searchData.endDate,
          hospCode: this.patientInfo.hospCode,
          sortOrder: 2,
          patnId: this.patientInfo.patnId
        })
        .then(res => {
          let rows = res.rows.map(row => {
            row.status = this.statusColor[row.statusFlag];
            return row;
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.statOrderList = this.statOrderList.concat(rows);
    },
    datasInit() {
      this.statOrderList = [];
    },
    //显示时间弹出层
    showPicker() {
      this.$refs.dataRange.show();
    },
    //时间选择确认
    onConfirm(res) {
      this.agentTimeArr = [
        `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`,
        `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`
      ];
      this.searchData.beginDate = `${res.obj.fyear}-${res.obj.fmonth}-${res.obj.fday}`;
      this.searchData.endDate = `${res.obj.tyear}-${res.obj.tmonth}-${res.obj.tday}`;
    },
    //时间取消
    onCancel() {},
    handleSearch() {
      this.datasInit();
      this.$nextTick(() => {
        this.$refs['mescroll'].downCallback();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.stat-order-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.search-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 16rpx;
  background: #fff;
  border-bottom: 1px solid #eee;
}
.search-box .time-item {
  border-radius: 4px;
  line-height: 70rpx;
  height: 70rpx;
  flex: 1;
  text-align: center;
  background: #ffffff;
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 28rpx;
  border: 1px solid #ddd;
  box-sizing: border-box;
  color: #333333;
}
.time-divider {
  margin: 0 16rpx;
}
.search-box .search-button {
  color: #ffffff;
  border-color: $theme-color;
  background-color: $theme-color;
  font-size: 28rpx;
  padding: 8rpx 16rpx;
  border-radius: 4px;
  margin-left: 16rpx;
}
.stat-order-list-box {
  position: relative;
  flex: 1;
}
/deep/ {
  .uni-collapse-cell--disabled,
  .uni-collapse-cell--open {
    background: #fff !important;
  }
  .uni-collapse-cell--hide,
  .uni-collapse-cell__title {
    height: auto;
  }
  .uni-collapse-cell__title {
    padding: 16rpx;
  }
  .uni-collapse-cell__title-text {
    font-size: 32rpx;
  }
}
.collapse-item {
  flex: 1;
}
.collapse-item-title {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}
.title-text {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
  flex: 1;
}
.status-text {
  font-size: 26rpx;
  color: #fff;
  padding: 8rpx;
  border-radius: 4px;
  line-height: 1;
}
.collapse-item-content {
  background: #f5f5f5;
  border-top: #e5e5e5;
  padding: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.content-field {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  font-size: 28rpx;
  color: #666;
}
.field-text {
  color: #333;
}
.order-time-text,
.dosage-text {
  line-height: 1;
  font-size: 28rpx;
  color: #666;
  margin-right: 60rpx;
  display: inline-block;
}
</style>
