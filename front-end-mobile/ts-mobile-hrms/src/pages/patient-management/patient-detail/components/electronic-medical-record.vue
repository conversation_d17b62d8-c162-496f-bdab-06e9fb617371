<template>
  <view class="electronic-medical-record-box">
    <mescroll
      ref="mescroll"
      :searchInput="false"
      :page="{ num: 0, size: 1000 }"
      @getDatas="getListData"
      @setDatas="setListData"
      @datasInit="datasInit"
    >
      <uni-collapse accordion>
        <uni-collapse-item
          v-for="item in electronicMedicalRecordList"
          :title="item.MR_TITLE"
          :key="item.MR_TYPE"
        >
          <view
            class="collapse-contact-item"
            v-for="row in item['children']"
            :key="row.EMR_ID"
            @tap="previewFile(row)"
          >
            {{ row.MR_TITLE + '-' + row.MR_TITLEDATE }}
          </view>
        </uni-collapse-item>
      </uni-collapse>
    </mescroll>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  props: {
    patientInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      electronicMedicalRecordList: []
    };
  },
  onLoad() {},
  methods: {
    previewFile(row) {
      uni.navigateTo({
        url: `/pages/patient-management/patient-detail/components/pdf-view?patnId=${this.patientInfo.patnId}&emrId=${row.EMR_ID}&hospCode=${this.patientInfo.hospCode}&templateFiletype=${row.TEMPLATE_FILETYPE}`
      });
    },
    // 去重
    uniqueByProp(arr, prop) {
      const map = new Map();
      return arr.reduce((acc, item) => {
        if (!map.has(item[prop])) {
          map.set(item[prop], true);
          acc.push(item);
        }
        return acc;
      }, []);
    },
    async getListData(page, successCallback, errorCallback) {
      await this.ajax
        .getQueryInpEmrFileInfo({
          hospCode: this.patientInfo.hospCode,
          patnId: this.patientInfo.patnId
        })
        .then(res => {
          let parentList = this.uniqueByProp(res, 'MR_TYPE');
          this.defaultOpeneds = parentList;
          let rows = parentList.map(e => {
            let children = res
              .filter(i => i.MR_TYPE == e.MR_TYPE)
              .map(e => {
                return {
                  ...e,
                  MR_TITLEDATE: e.MR_TITLEDATE.replace('T', ' ')
                };
              });
            return {
              MR_TYPE: e.MR_TYPE,
              MR_TITLE: e.MR_TITLE,
              children,
              num: children.length
            };
          });
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.electronicMedicalRecordList = this.electronicMedicalRecordList.concat(
        rows
      );
    },
    datasInit() {
      this.electronicMedicalRecordList = [];
    },
    handleCheckDetail(item) {
      uni.navigateTo({
        url: `/pages/patient-management/patient-detail/laboratory-test-report-detail?reportNo=${item.reportNo}&orderName=${item.orderName}&reportDate=${item.reportDate}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
/deep/ {
  .uni-collapse-cell--disabled,
  .uni-collapse-cell--open,
  .uni-collapse-cell__title:active {
    background: #fff !important;
  }
  .uni-collapse-cell--hide,
  .uni-collapse-cell__title {
    height: auto;
  }
  .uni-collapse-cell__title {
    padding: 16rpx;
  }
}
.collapse-contact-item {
  padding: 16rpx;
  display: flex;
  font-size: 28rpx;
  align-items: center;
  position: relative;
  background-color: #f5f5f5;
  &:not(:last-child)::after {
    position: absolute;
    bottom: 0;
    left: 16rpx;
    right: 0;
    height: 1px;
    background-color: #eee;
    content: '';
  }
}
.collapse_contact_item {
  padding-left: 60rpx;
  &::after {
    left: 60rpx;
  }
}
</style>
