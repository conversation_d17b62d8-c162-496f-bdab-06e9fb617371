<template>
  <view class="laboratory-test-report-detail-box">
    <page-head @clickLeft="returnBack" title="检验报告"></page-head>
    <view class="report-title">{{ reportInfo.orderName }}报告单</view>
    <view class="report-date">报告日期:{{ reportInfo.reportDate }}</view>
    <view class="report-table">
      <view class="report-table-tr">
        <view class="report-table-th w35">项目名称</view>
        <view class="report-table-th w18">结果</view>
        <view class="report-table-th w19">单位</view>
        <view class="report-table-th w28">参考值</view>
      </view>
      <view
        class="report-table-tr"
        v-for="item in reportDetail"
        :key="item.resultId"
      >
        <view class="report-table-td w35">{{ item.itemName }}</view>
        <view class="report-table-td w18" :style="{ color: item.color }"
          >{{ item.result }}
          <u-icon
            v-if="item.resultTag"
            :name="item.resultTag"
            :color="item.color"
            size="28"
          ></u-icon>
        </view>
        <view class="report-table-td w19">{{ item.unit || '-' }}</view>
        <view class="report-table-td w28">{{ item.referenceRange }}</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      reportInfo: {},
      reportDetail: []
    };
  },
  onLoad(opt) {
    this.reportInfo = opt;
    this.getTestRptItemDetailNew({
      repNo: opt.reportNo
    });
  },
  methods: {
    getTestRptItemDetailNew(data) {
      this.ajax.getTestRptItemDetailNew(data).then(res => {
        this.reportDetail = res.map(i => {
          switch (i.resultFlag) {
            case '1':
              i.resultTag = 'arrow-upward';
              i.color = '#fa3534';
              break;
            case '2':
              i.resultTag = 'arrow-downward';
              i.color = '#19be6b';
              break;
            case '6':
              i.color = '#fa3534';
              break;
            default:
              i.color = '#333';
          }
          return i;
        });
      });
    },
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.laboratory-test-report-detail-box {
  .report-title {
    font-size: 40rpx;
    color: #000;
    text-align: center;
    padding: 0 16rpx;
    margin: 8rpx 0;
  }
  .report-date {
    font-size: 28rpx;
    color: #666;
    text-align: center;
    padding: 0 16rpx;
    margin: 8rpx 0;
  }
  .report-table {
    width: 100%;
  }
  .report-table-tr {
    padding: 8rpx 16rpx;
    &:nth-child(odd) {
      background: #fff;
    }
  }
  .report-table-th,
  .report-table-td {
    display: inline-block;
    color: #000;
  }
  .report-table-th {
    color: #999;
  }
  .w19 {
    width: 19%;
  }
  .w18 {
    width: 18%;
  }
  .w28 {
    width: 28%;
  }
  .w35 {
    width: 35%;
  }
}
</style>
