<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="我的患者"></page-head>
    <view class="content-box">
      <view
        class="search-box"
        :style="{ height: isExpanded ? contentHeight + 'px' : '0' }"
      >
        <view class="search-content">
          <search-screen
            ref="screen"
            :deptSearchType="deptSearchType"
            :formList="searchFormList"
            :formData.sync="searchData"
            @change="handleSearchDataChange"
          ></search-screen>
          <view class="position-icon">
            <!-- <view class="icon-item" @click="handleCheckPatientOverview">
              <u-icon name="order" color="#666" size="36"></u-icon>
              患者一览
            </view> -->
            <view class="icon-item toggle-close" @click="toggleContent">
              <u-icon name="eye-off" color="#666" size="36"></u-icon>
              隐藏
            </view>
          </view>
        </view>
      </view>
      <view
        class="toggle-open-button"
        @click="toggleContent"
        v-if="!isExpanded"
      >
        展开筛选条件
        <u-icon
          :custom-style="{ transform: 'rotate(90deg)' }"
          name="arrow-right-double"
          color="#aaa"
          size="32"
        ></u-icon>
      </view>
      <u-divider
        half-width="100%"
        color="#ccc"
        border-color="#ccc"
        fontSize="32"
        margin-top="8"
        margin-bottom="8"
      >
        当前选择了{{ patienTotalCount }}位患者
      </u-divider>
      <view class="patient-list-box">
        <mescroll
          ref="mescroll"
          :searchInput="false"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view
            class="patient-list-item"
            v-for="(item, index) in patientList"
            :key="index"
            @click="handleCheckDetail(item)"
          >
            <view class="item-left">
              <img
                v-if="item.sex == 2"
                class="item-img"
                :src="require(`@/assets/img/woman.png`)"
                alt=""
              />
              <img
                v-else
                class="item-img"
                :src="require(`@/assets/img/man.png`)"
                alt=""
              />
              <view class="item-index">{{ item.bedNo }}</view>
            </view>
            <view class="item-right">
              <view class="info-row">
                <view class="info-field item-name"> {{ item.name }}</view>
                <view class="info-field"> {{ item.sexName }}</view>
                <view class="info-field"> {{ item.age }}</view>
                <view
                  class="info-field"
                  v-for="(statusItem, index) in item.statusList"
                  :key="index"
                  :style="{ color: statusItem.color }"
                >
                  {{ statusItem.name }}</view
                >
                <view class="info-field"> {{ item.orderTendLevelName }}</view>
                <view class="info-field"> {{ item.medicTypeName }}</view>
              </view>
              <view class="info-row">
                <view class="info-field"> {{ item.inPatientNo }}</view>
                <view class="info-field">诊断：{{ item.inDiagnosisName }}</view>
              </view>
              <view class="info-row">
                <view class="info-field item-bed-doctor">
                  管床医生：{{ item.bedDocName }}
                </view>
                <view class="info-field">
                  入院时间：{{ $dayjs(item.inDate).format('YYYY-MM-DD') }}
                </view>
              </view>
            </view>
          </view>
        </mescroll>
      </view>
    </view>
  </view>
</template>

<script>
import SearchScreen from './components/search-screen.vue';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

import searchMixin from './mixins/search-mixin';
import commonMixin from './mixins/common-mixin';
export default {
  components: {
    mescroll,
    SearchScreen
  },
  mixins: [searchMixin, commonMixin],
  data() {
    return {
      deptSearchType: '',
      isShow: true,
      isExpanded: false,
      contentHeight: 0,
      buttonHeight: 0,
      patienTotalCount: 0,
      patientList: []
    };
  },
  async onLoad() {
    await this.getDeptSearchType();
    this.$nextTick(() => {
      uni
        .createSelectorQuery()
        .in(this)
        .select('.search-content')
        .boundingClientRect(res => {
          this.contentHeight = res.height;
        })
        .exec();
      uni
        .createSelectorQuery()
        .in(this)
        .select('.toggle-open-button')
        .boundingClientRect(res => {
          this.buttonHeight = res.height;
        })
        .exec();
    });
  },
  methods: {
    async getDeptSearchType() {
      let res = await this.ajax.getDictItemByTypeCode({
        typeCode: 'CUSTOM_CODE'
      });
      if (!res.object) {
        this.deptSearchType = '';
      } else {
        let deptSearchTypeConfig = res.object.find(
          e => e.itemCode == 'HRMS_MY_PATIENT_DEPT'
        );
        this.deptSearchType = deptSearchTypeConfig?.itemNameValue ?? '';
      }
    },
    toggleContent() {
      this.isExpanded = !this.isExpanded;
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      let { deptIds, bedDocId, orderTendLevel, wzStatus } = this.searchData;
      await this.ajax
        .getQueryInPatient({
          pageSize: page.size,
          pageNo: page.num,
          deptIds,
          bedDocId,
          statuss: '3,4',
          orderTendLevel,
          wzStatus
        })
        .then(res => {
          let rows = res.rows.map(row => {
            let colorList = [];
            Object.keys(this.typeColor).forEach(i => {
              if (row[this.typeColor[i].key] == '1')
                colorList.push(this.typeColor[i]);
            });
            row.statusList = colorList;
            return row;
          });
          successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount) {
      this.patientList = this.patientList.concat(rows);
      this.patienTotalCount = totalCount;
    },
    datasInit() {
      this.patientList = [];
    },
    handleCheckDetail(item) {
      uni.setStorageSync('patientInfo', item);
      uni.navigateTo({
        url: `/pages/patient-management/patient-detail/index`
      });
    },
    handleCheckPatientOverview() {
      uni.navigateTo({
        url: `/pages/patient-management/patient-list/patient-overview?deptName=${this.searchData.curDept.name}&deptId=${this.searchData.curDept.id}`
      });
    },
    //返回上一层
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
}
.content-box {
  background: #fff;
  padding: 16rpx;
  flex: 1;
  display: flex;
  flex-direction: column;
}
.search-box {
  border-radius: 4px;
  transition: all 0.3s;
  background-color: #efefef;
  overflow: hidden;
}
.search-content {
  padding-top: 16rpx;
  position: relative;
}
.position-icon {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  color: #666;
  display: flex;
  justify-content: center;
  align-items: center;

  .icon-item {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    font-size: 28rpx;
    &:not(:last-child) {
      margin-right: 16rpx;
    }
  }
}
.toggle-open-button {
  text-align: center;
  color: #aaa;
  padding: 16rpx;
  z-index: 10;
  background: #efefef;
  border-radius: 4px;
  margin-bottom: 10px;
}
.patient-list-box {
  flex: 1;
  position: relative;
}
.patient-list-item {
  padding: 16rpx 0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  &:not(:last-child)::after {
    content: ' ';
    position: absolute;
    bottom: 0;
    height: 1px;
    background-color: #eee;
    left: 16rpx;
    right: 0;
  }
}

.patient-list-item .item-left {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.patient-list-item .item-index {
  font-size: 40rpx;
  font-weight: bold;
  color: $theme-color;
  line-height: 1;
  margin-top: 16rpx;
}
.patient-list-item .item-right {
  flex: 1;
  margin-left: 16rpx;
}
.patient-list-item .info-field {
  display: inline-block;
  margin-right: 16rpx;
  font-size: 28rpx;
  color: #666;
}
.patient-list-item .item-name {
  font-size: 32rpx;
  color: #333;
}
.patient-list-item .item-bed-doctor {
  color: $theme-color;
}
.patient-list-item .item-img {
  width: 120rpx;
}
</style>
