export default {
  data() {
    return {
      searchFormList: [
        {
          title: '当前科室',
          type: 'select',
          mode: 'dept',
          chooseType: 'radio',
          prop: 'deptIds',
          propVal: 'curDept',
          required: true
        },
        {
          title: '患者范围',
          type: 'select',
          mode: 'radio',
          chooseType: 'radio',
          prop: 'bedDocId',
          optionList: [
            {
              name: '我的患者',
              value: this.$store.state.common.userInfo.empCode
            },
            {
              name: '全科患者',
              value: ''
            }
          ]
        },
        {
          title: '护理级别',
          type: 'select',
          mode: 'checkbox',
          chooseType: 'checkbox',
          prop: 'orderTendLevel',
          optionList: [
            {
              name: '特级护理',
              checked: false,
              value: '4'
            },
            {
              name: 'I级护理',
              checked: false,
              value: '1'
            },
            {
              name: 'II级护理',
              checked: false,
              value: '2'
            },
            {
              name: 'III级护理',
              checked: false,
              value: '3'
            }
          ]
        },
        {
          title: '危重患者',
          type: 'select',
          mode: 'checkbox',
          chooseType: 'checkbox',
          prop: 'wzStatus',
          optionList: [
            {
              name: '病危',
              checked: false,
              value: '1'
            },
            {
              name: '病重',
              checked: false,
              value: '2'
            }
          ]
        }
        // {
        //   title: '医保范围',
        //   type: 'select',
        //   mode: 'checkbox',
        //   chooseType: 'checkbox',
        //   prop: 'workStatusValue',
        //   optionList: [
        //     {
        //       name: '自费',
        //       checked: false,
        //       value: '1'
        //     },
        //     {
        //       name: '医保',
        //       checked: false,
        //       value: '2'
        //     }
        //   ]
        // },
        // {
        //   title: '患者排序',
        //   type: 'select',
        //   mode: 'radio',
        //   chooseType: 'radio',
        //   prop: 'faultEmergency',
        //   optionList: [
        //     {
        //       name: '按床位正',
        //       value: ''
        //     },
        //     {
        //       name: '按床位反',
        //       value: '1'
        //     },
        //     {
        //       name: '按入院正',
        //       value: '2'
        //     },
        //     {
        //       name: '按入院正',
        //       value: '3'
        //     }
        //   ]
        // }
      ],
      searchData: {
        curDept: {
          id: this.$store.state.common.userInfo.empDeptId,
          name: this.$store.state.common.userInfo.empDeptName
        },
        deptIds: this.$store.state.common.userInfo.empDeptId,
        bedDocId: this.$store.state.common.userInfo.empCode,
        orderTendLevel: '',
        wzStatus: ''
      }
    };
  },
  methods: {
    handleSearchDataChange() {
      this.$refs.mescroll.downCallback();
    }
  }
};
