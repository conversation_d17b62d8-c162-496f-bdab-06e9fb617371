<template>
  <view>
    <u-popup
      v-model="show"
      mode="bottom"
      height="800"
      :safe-area-inset-bottom="true"
    >
      <view class="select_multiple_container">
        <view class="u-select__header">
          <view
            @click="handleCancel"
            class="u-select__header__confirm u-select__header__btn"
          >
            取消
          </view>
          <view class="u-select__header__title">{{ title }}</view>
          <view
            @click="handleOk"
            style="color: rgb(41, 121, 255);"
            class="u-select__header__confirm u-select__header__btn"
          >
            确定
          </view>
        </view>
        <view class="select_multiple_list">
          <view
            class="select_multiple_list_item"
            v-for="(item, idx) in list"
            :key="idx"
            @click="handleSelect(item)"
          >
            <view :class="['label', isActive(item) ? 'active' : '']">
              {{ item.label }}
            </view>
            <image
              v-if="isActive(item)"
              class="icon_16"
              src="@/assets/img/icon_xuanzhong.png"
            ></image>
          </view>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      title: '',
      list: [],
      selected: []
    };
  },
  methods: {
    open(arg) {
      let _arg = {
        title: '',
        list: [],
        selected: []
      };
      _arg = Object.assign(_arg, arg);
      this.title = _arg.title;
      this.list = _arg.list;
      this.show = true;
      this.selected = this.list.filter(e => {
        return _arg.selected.some(i => {
          return i === e.value;
        });
      });
    },
    handleSelect(row) {
      const idx = this.selected.findIndex(e => {
        return e.value === row.value;
      });
      if (idx === -1) {
        this.selected.push(row);
      } else {
        this.selected.splice(idx, 1);
      }
    },
    isActive(row) {
      return this.selected.find(e => {
        return e.value === row.value;
      })
        ? true
        : false;
    },
    handleCancel() {
      this.show = false;
      this.title = '';
      this.list = [];
      this.selected = [];
    },
    handleOk() {
      this.$emit('ok', this.selected);
      this.handleCancel();
    }
  }
};
</script>
<style lang="scss" scoped>
.select_multiple_container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .select_multiple_list {
    flex: 1;
    overflow-y: auto;
    padding: 0 32rpx;
    .select_multiple_list_item {
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      .label {
        flex: 1;
        font-size: 28rpx;
        font-weight: 400;
        color: #333333;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        margin-right: 32rpx;
      }
      .active {
        color: #005bac;
      }
    }
    .select_multiple_list_item + .select_multiple_list_item {
      border-top: 2rpx solid #eee;
    }
  }
}
.u-select__header {
  height: 80rpx;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 40rpx;
  border-bottom: 2rpx solid #f4f4f4;
}
</style>
