<template>
  <i
    class="main-icon"
    :class="icon"
    :style="{ color: color, 'font-size': `${size}px` }"
    @click="onClick"
  ></i>
</template>

<script>
export default {
  name: 'base-icon',
  props: {
    icon: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: '#333333'
    },
    size: {
      type: [Number, String],
      default: 16
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
};
</script>

<style scoped></style>
