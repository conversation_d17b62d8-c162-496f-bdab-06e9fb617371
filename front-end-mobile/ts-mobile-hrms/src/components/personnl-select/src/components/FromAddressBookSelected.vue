<template>
  <u-popup
    mode="right"
    v-model="myDialog"
    :safe-area-inset-bottom="true"
    height="100%"
    width="100%"
    @close="myDialogClose"
  >
    <u-navbar title="已选" title-bold :customBack="myDialogClose">
      <text class="navbar-right" slot="right" @click="addressBookSelectConfirm"
        >确定</text
      >
    </u-navbar>
    <view class="from-address-book-selected">
      <personnl-item-info
        v-for="item in selectData"
        :key="item.empId"
        :person="item"
        :selectData="selectData"
      />
    </view>
  </u-popup>
</template>

<script>
import PersonnlItemInfo from './PersonnlItemInfo';
export default {
  model: {
    prop: 'value',
    event: 'change'
  },
  components: {
    PersonnlItemInfo
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) this.myDialog = val;
      },
      immediate: true
    }
  },
  data: () => ({
    myDialog: false,
    paddingSelectData: []
  }),
  created() {
    this.paddingSelectData = this.$_.cloneDeep(this.selectData);
  },
  methods: {
    myDialogClose() {
      this.selectData.splice(0);
      this.selectData = [...this.paddingSelectData];

      this.myDialog = false;
      this.$emit('change', false);
    },
    addressBookSelectConfirm() {
      // 获取到已选数据 取消选中的empId
      let cancelEmpIds = this.selectData
        .filter(item => !item.checked)
        .map(item => item.empId);

      // 循环 剔除 取消选中的数据
      while (cancelEmpIds.length) {
        let empId = cancelEmpIds[cancelEmpIds.length - 1];
        uni.$emit('delSelectAddressBookItem', empId);
        cancelEmpIds.pop();
      }

      this.myDialog = false;
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.from-address-book-selected {
  padding: 32rpx 0;
}
.navbar-right {
  padding: 0 $uni-spacing-row-lg;
  font-size: $uni-font-size-base;
}
</style>
