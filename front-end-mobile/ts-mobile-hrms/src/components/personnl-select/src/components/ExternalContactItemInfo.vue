<template>
  <view class="person-list-item-info" @click="toggle(person)">
    <view
      class="checkbox__icon-wrap"
      :readOnly="readOnly"
      :class="(selectDataIds.indexOf(person.id) != -1) | iconClass"
    >
      <u-icon
        class="checkbox__icon-wrap__icon"
        name="checkbox-mark"
        :size="iconSize"
        :color="(selectDataIds.indexOf(person.id) != -1) | iconColor"
      />
    </view>
    <view class="left">
      <view
        class="person-head-image"
        :class="person.linkmanSex | sexClassFilter"
      >
        {{ person.linkmanName | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view class="person-name">{{ person.linkmanName }}</view>
      <view class="person-description">
        {{ person.linkmanDepart }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    /**
     * @Description: listType默认为default，已选页面为 selected 点击逻辑不一样
     * <AUTHOR>
     * @date 2022/1/20
     */
    listType: {
      type: String,
      default: () => 'default'
    },
    person: {
      type: Object,
      default: () => ({})
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    readOnly: {
      type: Boolean,
      default: () => false
    },
    selectData: {
      type: Array,
      default: () => []
    }
  },
  filters: {
    sexClassFilter(val) {
      return val === '0' ? 'sex-man' : 'sex-woman';
    },
    firstNameFilter(val) {
      return val.substring(val.length - 2);
    },
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'checkbox__icon-wrap--checked' : '';
    }
  },
  watch: {
    person: {
      handler(val) {
        val.avatar = (val.avatar && val.avatar + '/80x80.png') || null;
      },
      immediate: true
    }
  },
  computed: {
    selectDataIds() {
      return this.selectData.map(item => item.id);
    }
  },
  methods: {
    toggle(e) {
      if (this.readOnly) {
        return;
      }

      switch (this.listType) {
        case 'default':
          const index = this.selectDataIds.indexOf(e.id);
          if (index !== -1) {
            this.selectData.splice(index, 1);
          } else {
            uni.$emit('pushSelectAddressBookItem', e);
          }
          break;
        case 'selected':
          break;
      }
      e.checked = !e.checked;
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.person-list-item-info {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  .checkbox__icon-wrap {
    color: $uni-text-content-color;
    @include vue-flex;
    flex: none;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 38rpx;
    height: 38rpx;
    color: transparent;
    text-align: center;
    transition-property: color, border-color, background-color;
    font-size: $uni-icon-size-base;
    border: 1px solid $uni-text-color-disable;
    border-radius: 4px;
    transition-duration: 0.2s;
  }
  .checkbox__icon-wrap--checked {
    border-color: $u-type-primary;
    background-color: $u-type-primary;
  }
  .left {
    margin: 0 24rpx;
    .person-head-image {
      width: $uni-img-size-lg;
      height: $uni-img-size-lg;
      border-radius: 50%;
      background-color: $u-bg-color;
      text-align: center;
      line-height: $uni-img-size-lg;
      font-size: $uni-font-size-base;
      color: $uni-text-color-inverse;
      &.sex-man {
        background-color: $sexman-color;
      }
      &.sex-woman {
        background-color: $sexwoman-color;
      }
    }
  }
  .right {
    flex: 1;
    .person-name {
      font-size: $uni-font-size-base;
      color: $u-main-color;
    }
    .person-description {
      color: $u-content-color;
      font-size: $uni-font-size-sm;
    }
  }
}
</style>
