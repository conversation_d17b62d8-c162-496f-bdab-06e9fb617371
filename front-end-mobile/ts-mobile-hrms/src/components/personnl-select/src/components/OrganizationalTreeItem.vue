<script>
import IconWarpCheck from './IconWrapCheck';
import renderTree from '../Mixins/renderOrganizationalTree';
export default {
  name: 'OrganizationalTreeItem',
  components: {
    IconWarpCheck
  },
  mixins: [renderTree],
  props: {
    treeList: {
      type: Array,
      default: () => []
    },
    selectList: {
      type: Array,
      default: () => []
    },
    /**@desc 树层级 */
    showLevel: {
      type: Number,
      default: 2
    }
  },
  mounted() {
    if (this.selectList && this.selectList.length > 0) {
      // echo  回显
      this.selectList.forEach(item => {
        let itemElement = document.getElementById(`TreeItem${item}`);
        if (itemElement) {
          let level = Number(itemElement.getAttribute('level') - 1);
          if (level > 1) {
            while (level) {
              itemElement
                .getElementsByClassName('tree-item-content')[0]
                .classList.remove('content-close');

              itemElement.parentNode.classList.remove('view-close');
              itemElement.parentNode.childNodes.forEach(item => {
                const type = item.getAttribute('type');
                if (type === 'view') {
                  const parent = item.getAttribute('parent');
                  if (parent === null) {
                    item.classList.remove('view-close');
                  }

                  item
                    .getElementsByClassName('tree-item-content')[0]
                    .classList.remove('content-close');
                } else {
                  item.classList.remove('content-close');
                }
              });
              itemElement = itemElement.parentNode;
              level--;
            }
          }
        }
      });
    }
  },
  methods: {
    clickTreeItem(data) {
      // 点击项为最父级
      if (data.parent) {
        // 选中数组中存在parentId清空全选
        //         不存在 则子节点全部选中
        // 每次点击全选时 其清空数组 避免重复选中子级别（先选择一个子节点 后全选）
        const index = this.selectList.indexOf(data.id);
        this.selectList.splice(0);
        if (index === -1) {
          this.changeParentAllHandle(this.treeList);
        }
      } else {
        this.clickTreeChildrenHandle(data);
      }
    },
    // 全选递归push Data
    changeParentAllHandle(arr) {
      arr.forEach(item => {
        this.selectList.push(item.id);
        if (item.children && item.children.length > 0)
          this.changeParentAllHandle(item.children);
      });
    },
    clickTreeChildrenHandle(data) {
      const index = this.selectList.indexOf(data.id);
      // 是否存在children
      if (data.children) {
        // 如果id存在 则递归 删除子级id
        this.setChildrenSelectHandle([data], 'del');
        if (index === -1) {
          this.setChildrenSelectHandle([data], 'add');
        }
      } else {
        if (index !== -1) {
          this.selectList.splice(index, 1);
        } else {
          this.selectList.push(data.id);
        }
      }
    },
    // 操作子级 push Or del id
    setChildrenSelectHandle(arr, type) {
      for (let i = 0; i < arr.length; i++) {
        const item = arr[i];

        if (type === 'add') {
          this.selectList.push(item.id);
        } else {
          const index = this.selectList.indexOf(item.id);
          if (index !== -1) this.selectList.splice(index, 1);
        }

        if (item.children && item.children.length > 0)
          this.setChildrenSelectHandle(item.children, type);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.tree-view {
  .icon-warp-check {
    margin-right: 32rpx;
  }
  .tree-parent {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding-left: 32rpx;
    transition: all 0.5s;
    .text-font {
      line-height: 80rpx;
      flex: 1;
      font-size: 32rpx;
      color: #333333;
      font-weight: 700;
    }
    &.active {
      background: rgba(82, 96, 255, 0.2);
    }
  }
  .tree-item-view {
    height: auto;
    overflow: hidden;
    &.view-close {
      height: 80rpx;
    }
    .tree-item-content {
      height: 80rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      transition: all 0.5s;
      opacity: 1;
      &.content-close {
        height: 0;
        opacity: 0;
      }
      &.active {
        background: rgba(82, 96, 255, 0.2);
      }
      .tree-item-info {
        display: flex;
        align-items: center;
        height: 100%;
        flex: 1;

        .item-switch-open {
          width: 16rpx;
          height: 16rpx;
          border-top: 4rpx solid #eee;
          border-left: 4rpx solid #eee;
          transform: rotate(225deg);
          transition: all 0.7s;
          &.item-switch-close {
            transform: rotate(45deg);
          }
          &.no-switch {
            visibility: hidden;
          }
        }
        img {
          margin: 0 16rpx;
          width: 32 rpx;
          height: 32 rpx;
        }
        .tree-item-text {
          display: flex;
          align-items: center;
          font-size: 28rpx;
          color: #333333;
        }
      }
    }
  }
}
</style>
