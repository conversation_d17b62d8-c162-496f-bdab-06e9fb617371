<template>
  <view class="personnl-select">
    <u-popup
      mode="bottom"
      v-model="fromDialog"
      :safe-area-inset-bottom="true"
      height="300"
      @close="fromDialogClose"
    >
      <div class="content">
        <view class="operate" @click="fromNavigateToSelectHandle(0)"
          >从通讯录选择</view
        >
        <view class="operate" @click="fromNavigateToSelectHandle(1)"
          >从组织架构选择</view
        >
        <view class="transition"></view>
        <view class="operate" @click="fromDialogCancel">取消</view>
      </div>
    </u-popup>

    <u-popup
      mode="bottom"
      v-model="typeSelectDialog"
      :safe-area-inset-bottom="true"
      height="100%"
      @close="typeSelectDialogClose"
    >
      <u-navbar title="OA系统" title-bold :customBack="typeSelectBackHandle">
        <text class="navbar-right" slot="right" @click="confirm">确定</text>
      </u-navbar>
      <select-from-address-book
        v-if="!selectFormType"
        :selectData="selectList"
        v-bind="$attrs"
      />
      <select-from-organizational
        v-else
        ref="SelectOrganizational"
        :selectList="selectList"
      />
    </u-popup>
  </view>
</template>

<script>
import SelectFromAddressBook from './SelectFromAddressBook';
import SelectFromOrganizational from './SelectFromOrganizational';
export default {
  name: 'personnl-select',
  components: {
    SelectFromAddressBook,
    SelectFromOrganizational
  },
  model: {
    event: 'change',
    prop: 'modelValue'
  },
  props: {
    modelValue: {
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: () => ''
    },
    personnlSelectArr: {
      type: Array,
      default: () => []
    }
  },
  data: () => ({
    fromDialog: false,
    typeSelectDialog: false,
    selectFormType: undefined,
    selectList: []
  }),
  watch: {
    modelValue: {
      handler(newValue) {
        if (newValue) {
          if (this.value.length) {
            // 如果value 有值 则为修改 根据数据type 直接打开对应列表
            this.typeSelectDialog = true;
            this.selectList = this.value;
            this.selectFormType = this.type === 'personnl' ? 0 : 1;
          } else {
            // 如果value 没值 则为新增 打开选择类型dialog
            this.fromDialog = true;
            this.selectList = [];
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  methods: {
    fromNavigateToSelectHandle(index) {
      this.typeSelectDialog = true;
      this.selectFormType = index;
    },
    confirm() {
      switch (this.selectFormType) {
        case 0:
          uni.$emit('personSelectSetValue', {
            data: this.selectList,
            type: this.selectFormType,
            name: 'empName',
            id: 'empId'
          });
          break;
        case 1:
          this.$refs.SelectOrganizational.organizationalConfirm();
          uni.$emit('personSelectSetValue', {
            data: this.$refs.SelectOrganizational.organizationalConfirmList,
            type: this.selectFormType,
            name: 'name',
            id: 'id'
          });
          break;
      }
      this.typeSelectDialog = false;
      this.fromDialog = false;
      this.close();
    },
    typeSelectBackHandle() {
      this.typeSelectDialog = false;
    },
    fromDialogCancel() {
      this.fromDialog = false;
    },
    fromDialogClose() {
      this.close();
    },
    typeSelectDialogClose() {
      if (this.value.length) this.close();
    },
    close() {
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
.personnl-select {
  width: 100%;
  height: 100vh;
  .navbar-right {
    padding: 0 $uni-spacing-row-lg;
    font-size: $uni-font-size-base;
  }
  .content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    text-align: center;
    > .operate {
      width: 100%;
      height: 98rpx;
      line-height: 98rpx;
    }
    .transition {
      height: 16rpx;
      background: #f4f4f4;
    }
  }
}
</style>
