<template>
  <view class="organizational-tree">
    <view class="organizational-tree-input">
      <u-search
        v-model="keywords"
        :show-action="false"
        placeholder="输入部门搜索"
        @search="searchHandle"
        @clear="clearHandle"
      ></u-search>
    </view>
    <organizational-tree-item
      class="organizational-tree-item"
      v-if="treeList.length"
      :treeList="treeList"
      :selectList="selectList"
    />
  </view>
</template>

<script>
import OrganizationalTreeItem from './components/OrganizationalTreeItem';
export default {
  components: {
    OrganizationalTreeItem
  },
  props: {
    selectList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeList: [],
      keywords: '',
      organizationalConfirmList: []
    };
  },
  async created() {
    const result = await this.ajax.getTree();
    const { statusCode, success, object } = result;
    if (statusCode === 200 && success) {
      this.treeList = this.$_.cloneDeep(object);
    }
  },
  methods: {
    searchHandle() {},
    clearHandle() {},
    organizationalConfirm() {
      this.organizationalConfirmList = [];
      for (let i = 0; i < this.selectList.length; i++) {
        const itemId = this.selectList[i];
        this.splitSelectDataHandle(this.treeList, itemId);
      }
    },
    splitSelectDataHandle(arr, id) {
      for (let j = 0; j < arr.length; j++) {
        const treeItem = arr[j];
        if (treeItem.id === id) {
          this.organizationalConfirmList.push(treeItem);
        } else if (treeItem.children && treeItem.children.length > 0) {
          this.splitSelectDataHandle(treeItem.children, id);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.organizational-tree {
  height: calc(100% - 88rpx);
  display: flex;
  flex-direction: column;
  .organizational-tree-input {
    padding: 16rpx 32rpx;
    border-radius: 12rpx;
    box-shadow: 0px 1px 0px 0px #f4f4f4;
  }
  .organizational-tree-item {
    padding: 32rpx 0;
    height: 100vh;
    overflow-y: auto;
    box-sizing: border-box;
    transition: all 0.5s;
    &.close {
      height: 112rpx;
    }
  }
}
</style>
