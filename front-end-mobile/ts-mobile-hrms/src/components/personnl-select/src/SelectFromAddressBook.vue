<template>
  <view class="from-address-book">
    <view class="address-book-box">
      <view class="search-container">
        <u-search
          v-model="keywords"
          :disabled="selectAllState"
          :show-action="false"
          placeholder="输入姓名、部门搜索"
          @search="search"
          @clear="clear"
        ></u-search>
        <view class="choose-person-num" @click="changePopopShow">
          已选({{ choosePersonNum }})
        </view>
      </view>
      <view class="personnl-select-tabs">
        <u-tabs
          activeColor="#005bac"
          barWidth="60"
          :list="tabList"
          :current="tabValue"
          :scrollable="false"
          @change="tabsClickHandle"
        ></u-tabs>
      </view>
      <view class="Hospital-staff-switch" v-if="HospitalStaffSwitch">
        <text>全院人员</text>
        <u-switch v-model="selectAllState"></u-switch>
      </view>
      <view class="personnl-type-content">
        <select-contact :selectData="selectData" v-show="tabValue === 0" />
        <select-group-members
          :selectData="selectData"
          v-show="tabValue === 1"
          groupType="0"
        />
        <select-group-members
          :selectData="selectData"
          v-show="tabValue === 2"
          groupType="1"
        />
        <!--外部联系人-->
        <!--<select-external-contact-->
        <!--  :selectData="selectData"-->
        <!--  v-show="tabValue === 3"-->
        <!--/>-->
      </view>
    </view>
    <from-address-book-selected
      :selectData="selectData"
      v-model="selectedDialog"
    />
  </view>
</template>

<script>
import SelectContact from './components/SelectContact';
import SelectGroupMembers from './components/SelectGroupMembers';
import FromAddressBookSelected from './components/FromAddressBookSelected';
// import SelectExternalContact from './components/SelectExternalContact';
import { mapState } from 'vuex';
export default {
  name: 'AddressBook',
  components: {
    SelectContact,
    SelectGroupMembers,
    FromAddressBookSelected
    // SelectExternalContact,
  },
  computed: {
    ...mapState({
      choosePersonNum: state => state.personnlSelect.choosePersonNum
    }),
    selectAllState: {
      get() {
        return this.$store.state.personnlSelect.selectAllState;
      },
      set(val) {
        this.changePersonnlSelectsetData('selectAllState', val);

        // 全院人员 为true  清空选中数组 已选人员
        if (val) {
          this.changePersonnlSelectsetData('choosePersonNum', 0);
          this.selectData.splice(0);
          this.keywords = '';
        }
      }
    },
    // 全院人员按钮权限
    HospitalStaffSwitch() {
      return (
        this.tabValue === 0 &&
        this.$attrs.personnlSelectParams.allPersonnlSwitch
      );
    }
  },
  props: {
    selectData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    selectData: {
      handler(val) {
        this.changePersonnlSelectsetData('choosePersonNum', val.length);
      }
    },
    deep: true,
    immediate: true
  },
  created() {
    // 控制外部联系人 权限管理
    if (!this.$attrs.personnlSelectParams.outSideContactTabs) {
      const index = this.tabList.map(item => item.name).indexOf('外部联系人');

      if (index !== -1) {
        this.tabList.splice(index, 1);
      }
    }

    uni.$on('pushSelectAddressBookItem', data => {
      this.selectData.push(data);
    });
    uni.$on('delSelectAddressBookItem', empId => {
      const empIds = this.selectData.map(item => item.empId);
      const index = empIds.findIndex(item => item === empId);
      this.selectData.splice(index, 1);
    });
  },
  data: () => ({
    keywords: '',
    tabList: [
      {
        name: '联系人'
      },
      {
        name: '系统群组'
      },
      {
        name: '个人群组'
      }
      // {
      //   name: '外部联系人'
      // }
    ],
    tabValue: 0,
    selectedDialog: false
  }),
  methods: {
    search() {},
    clear() {},
    changePopopShow() {
      this.selectedDialog = true;
    },
    tabsClickHandle(val) {
      this.tabValue = val;
    },
    changePersonnlSelectsetData(label, value) {
      this.$store.commit('personnlSelect/setData', { label, value });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.from-address-book {
  height: calc(100% - 44px);
  .address-book-box {
    height: 100%;
    @include vue-flex(column);
    .search-container {
      padding: $uni-spacing-row-sm $uni-spacing-row-lg;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      margin-bottom: $uni-spacing-col-base;
    }
    .choose-person-num {
      font-size: $uni-font-size-base;
      margin-left: $uni-spacing-row-lg;
    }
    .personnl-select-tabs {
      text-align: center;
    }
    .Hospital-staff-switch {
      background: #f4f6f9;
      padding: 32rpx 24rpx 16rpx;
      @include vue-row-flex(flex-start);
      text {
        font-size: 28rpx;
        color: #333333;
        margin-right: 16rpx;
      }
    }
    .personnl-type-content {
      background: #f4f6f9;
      position: relative;
      flex: 1;
      width: 100%;
    }
  }
}
</style>
