<template>
  <u-popup
    v-model="privateVisible"
    height="800"
    :safe-area-inset-bottom="true"
    mode="bottom"
    class="ts_time_range_picker"
    @open="handleOpen"
    @close="handleClose"
  >
    <view class="range_picker_container">
      <view class="u-select__header">
        <view
          @click="handleCancel"
          class="u-select__header__confirm u-select__header__btn"
        >
          取消
        </view>
        <view class="u-select__header__title"> </view>
        <view
          @click="handleOk"
          style="color: rgb(41, 121, 255);"
          class="u-select__header__confirm u-select__header__btn"
        >
          确定
        </view>
      </view>
      <view class="range_picker_title_box">
        <view
          :class="[
            'range_picker_title_text',
            currentTap == 'start' ? 'range_picker_active' : ''
          ]"
          data-tap="start"
          @tap.stop="clickTap"
        >
          {{ startVal }}
        </view>
        <view class="range_picker_title_lable">至</view>
        <view
          :class="[
            'range_picker_title_text',
            currentTap == 'end' ? 'range_picker_active' : ''
          ]"
          data-tap="end"
          @tap.stop="clickTap"
        >
          {{ endVal }}
        </view>
      </view>
      <view class="content">
        <picker-view
          :indicator-style="indicatorStyle"
          :value="rangeValue"
          @change="bindChange"
          class="picker-view"
        >
          <picker-view-column v-for="rangeItem in range" :key="rangeItem.type">
            <view
              class="item"
              v-for="(item, index) in rangeItem.list"
              :key="`${rangeItem.type}:${index}`"
            >
              {{ item }}{{ getName(rangeItem.type) }}
            </view>
          </picker-view-column>
        </picker-view>
      </view>
    </view>
  </u-popup>
</template>

<script>
var dayjs = require('dayjs');
const dateFormat = [
  { type: 'year', value: 2021 },
  { type: 'month', value: 1 },
  { type: 'day', value: 1 },
  { type: 'hour', value: 1 },
  { type: 'minute', value: 1 },
  { type: 'second', value: 1 }
];
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    format: {
      type: String,
      default: 'HH:mm'
    },
    defaultValue: {
      type: Array,
      default: () => []
    },
    rangeDate: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      privateVisible: this.value,
      currentTap: 'start',
      timesValue: [],
      rangeValue: [],
      title: 'picker-view',
      range: [],
      indicatorStyle: `height: ${Math.round(
        uni.getSystemInfoSync().screenWidth / (750 / 100)
      )}px;`,
      fileList: []
    };
  },
  created() {},
  methods: {
    dayjs,
    formatValue(date) {
      let _date = date;
      switch (this.format) {
        case 'YYYY-MM-DD':
        case 'YYYY/MM/DD':
          _date = dayjs(`${date} 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'YYYY-MM':
          _date = dayjs(`${date}-01 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'YYYY/MM':
          _date = dayjs(`${date}/01 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'MM-DD':
          _date = dayjs(`2021-${date} 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'MM/DD':
          _date = dayjs(`2021/${date} 00:00:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'HH:mm:ss':
          _date = dayjs(`2021-01-01 ${date}`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'HH:mm':
          _date = dayjs(`2021-01-01 ${date}:00`).format('YYYY-MM-DD HH:mm:ss');
          break;
        case 'mm:ss':
          _date = dayjs(`2021-01-01 00:${date}`).format('YYYY-MM-DD HH:mm:ss');
          break;
        default:
          break;
      }
      return _date;
    },
    clickTap(e) {
      let data = e.currentTarget.dataset;
      if (data.tap == this.currentTap) return;
      this.currentTap = data.tap;
      if (this.currentTap === 'start') {
        this.rangeValue = this.getRangeValue(this.timesValue[0]);
      } else {
        this.rangeValue = this.getRangeValue(this.timesValue[1]);
      }
    },
    initData() {
      // 年
      this.fileList = [];
      if (this.isType('YYYY')) {
        let startYear = parseInt(dayjs(this._rangeDate[0]).format('YYYY'));
        let endYear = parseInt(dayjs(this._rangeDate[1]).format('YYYY'));
        let years = [];
        for (let i = startYear; i <= endYear; i++) {
          years.push(i);
        }
        this.fileList.push('year');
        this.range.push({ type: 'year', list: years });
      }
      //   月
      if (this.isType('MM')) {
        let months = [];
        for (let i = 1; i <= 12; i++) {
          months.push(this.formatDateNum(i));
        }
        this.fileList.push('month');
        this.range.push({ type: 'month', list: months });
      }
      //   日
      if (this.isType('DD')) {
        let days = [];
        for (let i = 1; i <= 31; i++) {
          days.push(this.formatDateNum(i));
        }
        this.fileList.push('day');
        this.range.push({ type: 'day', list: days });
      }
      //   时
      if (this.isType('HH')) {
        let hours = [];
        for (let i = 0; i < 24; i++) {
          hours.push(this.formatDateNum(i));
        }
        this.fileList.push('hour');
        this.range.push({ type: 'hour', list: hours });
      }
      //   分
      if (this.isType('mm')) {
        let minutes = [];
        for (let i = 0; i < 60; i++) {
          minutes.push(this.formatDateNum(i));
        }
        this.fileList.push('minute');
        this.range.push({ type: 'minute', list: minutes });
      }
      //   秒
      if (this.isType('ss')) {
        let seconds = [];
        for (let i = 0; i < 60; i++) {
          seconds.push(this.formatDateNum(i));
        }
        this.fileList.push('second');
        this.range.push({ type: 'second', list: seconds });
      }
    },
    isType(str) {
      return this.format.indexOf(str) != -1 ? true : false;
    },
    bindChange: function(e) {
      const val = e.detail.value;
      let date = [];
      for (let i = 0; i < dateFormat.length; i++) {
        const dateFormatItem = dateFormat[i];
        const _idx = this.fileList.findIndex(file => {
          return file === dateFormatItem.type;
        });
        if (_idx !== -1) {
          const data = this.range.find(j => {
            return j.type === dateFormatItem.type;
          });
          date.push(parseInt(data.list[val[_idx]]));
        } else {
          date.push(dateFormatItem.value);
        }
      }
      const _dateFormat = dayjs(
        `${date.slice(0, 3).join('-')} ${date.slice(3).join(':')}`
      ).format('YYYY-MM-DD HH:mm:ss');
      // this.timesValue[this.currentTap === 'start' ? 0 : 1] = _dateFormat;
      this.$set(
        this.timesValue,
        this.currentTap === 'start' ? 0 : 1,
        _dateFormat
      );
    },
    getName(type) {
      let str = '';
      switch (type) {
        case 'year':
          str = '年';
          break;
        case 'month':
          str = '月';
          break;
        case 'day':
          str = '日';
          break;
        default:
          break;
      }
      return str;
    },

    /**
     * 日期自动补0
     * @param {String | Number} num 日期
     * @return {String}
     */
    formatDateNum(num) {
      return String(num).replace(/(^\d{1}$)/, '0$1');
    },
    getRangeValue(test) {
      if (!test) return [];
      let rangeValue = [];
      let date, time;
      let list = test.split(' ');
      date = list[0];
      time = list[1];
      const dateLiist = date.split('-');
      const timeList = time.split(':');
      for (const key in this.range) {
        const item = this.range[key];
        let e;
        switch (item.type) {
          case 'year':
            e = dateLiist[0];
            break;
          case 'month':
            e = dateLiist[1];
            break;
          case 'day':
            e = dateLiist[2];
            break;
          case 'hour':
            e = timeList[0];
            break;
          case 'minute':
            e = timeList[1];
            break;
          case 'second':
            e = timeList[2];
            break;
          default:
            break;
        }

        rangeValue.push(
          item.list.findIndex(i => {
            return i == e;
          })
        );
      }
      return rangeValue;
    },
    handleClose() {
      // this.handleCancel()
      this.currentTap = 'start';
      this.timesValue = [];
      this.rangeValue = [];
      this.range = [];
      this.fileList = [];
    },
    handleOpen() {
      if (
        this.defaultValue &&
        Object.prototype.toString.call(this.defaultValue) == '[object Array]' &&
        this.defaultValue.length === 2
      ) {
        this.timesValue = this.defaultValue.map(e => {
          return this.formatValue(e);
        });
      } else {
        this.timesValue = [
          dayjs().format('YYYY-MM-DD HH:mm:ss'),
          dayjs().format('YYYY-MM-DD HH:mm:ss')
        ];
      }

      this._rangeDate =
        this.rangeDate.length > 0
          ? this.rangeDate
          : ['2010-01-01', '2100-12-31'];
      this.initData();
      this.rangeValue = this.getRangeValue(this.timesValue[0]);
    },
    handleCancel() {
      this.privateVisible = false;
      this.currentTap = 'start';
      this.timesValue = [];
      this.rangeValue = [];
      this.range = [];
      this.fileList = [];
    },
    handleOk() {
      const vm = this;
      let _emitData = vm.timesValue.map(e => {
        return dayjs(e).format(vm.format);
      });
      vm.$emit('confirm', _emitData);
      vm.handleCancel();
    }
  },
  computed: {
    startVal() {
      return this.timesValue.length > 0
        ? dayjs(this.timesValue[0]).format(this.format)
        : '';
    },
    endVal() {
      return this.timesValue.length === 2
        ? dayjs(this.timesValue[1]).format(this.format)
        : '';
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.privateVisible = newValue;
      },
      deep: true,
      immediate: true
    },
    privateVisible(newValue) {
      if (!newValue) this.privateVisible = false;
      this.$emit('input', newValue);
    }
  }
};
</script>
<style lang="scss" scoped>
.ts_time_range_picker {
  .range_picker_container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .u-select__header {
      height: 80rpx;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 40rpx;
    }
    .range_picker_title_box {
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      padding: 16rpx 40rpx;
      .range_picker_title_text {
        flex: 1;
        border-bottom: 6rpx solid #eee;
        padding: 24rpx 0rpx;
        font-size: 32rpx;
        line-height: 32rpx;
        font-weight: normal;
        color: #333333;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        text-align: center;
      }
      .range_picker_active {
        border-color: #1989fa;
        color: #1989fa;
      }
      .range_picker_title_lable {
        width: 60rpx;
        max-width: 60rpx;
        min-width: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        box-sizing: border-box;
        color: #333;
      }
    }
    .content {
      flex: 1;
      width: 100%;
      padding: 20rpx;
      .picker-view {
        height: 100%;
      }
      .item {
        height: 100rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
    }
  }
}
</style>
