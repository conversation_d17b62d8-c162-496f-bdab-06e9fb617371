<template>
  <u-popup
    v-model="deptPopupShow"
    class="popup-radio-select-dept"
    mode="center"
    width="100%"
    height="100%"
  >
    <view class="popup-body">
      <view class="head">
        <view class="title">选择科室</view>
        <view class="close" @click="deptPopupShow = false">
          <u-icon name="close" size="24" />
        </view>
      </view>
      <view class="mescroll-content">
        <mescroll
          ref="mescroll"
          :searchInput="true"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <!-- 数据列表 -->
          <view class="contact-list">
            <view
              class="contact-item"
              v-for="item in dataList"
              :key="item.id"
              @tap="chooseItem(item)"
            >
              {{ item.name }}
              <view class="radio__icon-wrap" v-if="item.id == selectedDeptId">
                <u-icon
                  class="radio__icon-wrap__icon"
                  name="checkbox-mark"
                  :size="28"
                />
              </view>
            </view>
          </view>
        </mescroll>
      </view>
    </view>
  </u-popup>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';

export default {
  props: {
    selectedDeptId: {
      type: String,
      default: ''
    },
    pramas: {
      type: Object,
      default: () => {}
    }
  },
  components: {
    mescroll
  },
  data() {
    return {
      deptPopupShow: false,
      dataList: []
    };
  },
  methods: {
    show() {
      this.deptPopupShow = true;
    },
    async getListData(page, successCallback, errorCallback, keywords) {
      let _self = this;
      await _self.ajax
        .getAllOrgList({
          pageSize: 50,
          pageNo: page.num,
          name: keywords,
          ...this.pramas
        })
        .then(res => {
          let rows = res.rows;
          let rowsArr = [];
          rows.forEach(item => {
            rowsArr.push({
              name: item.name,
              id: item.organizationId
            });
          });
          successCallback(rowsArr);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(row) {
      this.dataList = this.dataList.concat(row);
    },
    datasInit(keywords) {
      this.dataList = [];
    },
    handleSearch() {
      this.$refs['mescroll'].downCallback();
    },
    chooseItem(item) {
      this.$emit('chooseItem', item);
      this.deptPopupShow = false;
    }
  }
};
</script>

<style lang="scss" scoped>
.popup-body {
  width: 100%;
  height: 100%;
  background: #f7f8f8;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 75rpx;
    padding: 0 12rpx;
    background-color: $u-type-primary;
    .title {
      font-size: 32rpx;
      color: #fff;
    }
    .close {
      width: 40rpx;
      height: 40rpx;
      border-radius: 100%;
      background-color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: $u-type-primary;
    }
  }
  .search {
    padding: 16rpx;
    background: #fff;
  }
  .mescroll-content {
    flex: 1;
    position: relative;
    .contact-list {
      height: 100%;
      overflow: scroll;
      .contact-item {
        padding: 12rpx;
        display: flex;
        align-items: center;
        background-color: #ffffff;
        border-bottom: 2rpx solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .radio__icon-wrap__icon {
          color: $u-type-primary;
        }
      }
    }
  }
}
</style>
