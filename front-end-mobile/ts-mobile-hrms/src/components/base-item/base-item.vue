<template>
  <view
    class="base_item"
    :style="{ height: extraText ? '120rpx' : '80rpx' }"
    @click="handleClick"
  >
    <view class="base_item_head">
      <slot name="left">
        <view v-if="leftText" class="left" :style="leftStyle">
          {{ leftText }}
        </view>
      </slot>
      <slot name="right">
        <view v-if="rightText" class="right" :style="rightStyle">
          {{ rightText }}
        </view>
      </slot>
    </view>
    <slot name="extra">
      <view class="extra_box">
        {{ extraText }}
      </view>
    </slot>
  </view>
</template>

<script>
export default {
  props: {
    leftText: {
      type: [String, Number],
      default: ''
    },
    rightText: {
      type: [String, Number],
      default: ''
    },
    leftStyle: {
      type: [String, Object],
      default: () => {}
    },
    rightStyle: {
      type: [String, Object],
      default: () => {}
    },
    extraText: {
      type: [String, Number],
      default: ''
    }
  },
  methods: {
    handleClick() {
      this.$emit('click');
    }
  }
};
</script>
<style lang="scss" scoped>
.base_item {
  padding: 0 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .base_item_head {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      width: 200rpx;
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
    .right {
      flex: 1;
      font-size: 28rpx;
      font-weight: 400;
      color: #333333;
      text-align: right;
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
    }
  }
  .extra_box {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    font-size: 28rpx;
    font-weight: 400;
    color: #999999;
  }
}
</style>
<style>
.base_item + .base_item {
  border-top: 2rpx solid #eee;
}
</style>
