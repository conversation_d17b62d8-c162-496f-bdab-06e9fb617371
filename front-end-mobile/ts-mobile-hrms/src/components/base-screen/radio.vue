<template>
  <view class="ts_radio">
    <view
      v-for="optionItem in option"
      :key="optionItem.value"
      :class="[
        'screen_item_option_item',
        optionItem.disabled ? 'disabled' : '',
        myValue === optionItem.value ? 'actived' : ''
      ]"
      @click="handleClick(optionItem)"
    >
      {{ optionItem.name }}
    </view>
  </view>
</template>

<script>
export default {
  props: {
    option: {
      type: Array,
      default: () => []
    },
    value: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      myValue: this.value
    };
  },
  methods: {
    handleClick(row) {
      if (row.value === this.myValue) {
        this.myValue = '';
      } else {
        this.myValue = row.value;
      }
    }
  },
  watch: {
    value: {
      handler(newValue) {
        this.myValue = newValue;
      },
      deep: true,
      immediate: true
    },
    myValue(newValue) {
      if (!newValue) this.myValue = '';
      this.$emit('input', newValue);
    }
  }
};
</script>
<style lang="scss" scoped>
.ts_radio {
  display: grid;
  grid-row-gap: 16rpx;
  grid-column-gap: 48rpx;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(auto-fill, 48rpx);
  .screen_item_option_item {
    border-radius: 6rpx;
    border: 2rpx solid #eee;
    font-size: 24rpx;
    font-weight: 400;
    color: #999;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .disabled {
    background: #dddddd;
    border: 1px solid #dddddd;
    color: #999999;
    cursor: not-allowed;
  }
  .actived {
    border: 1px solid #005bac;

    color: #005bac;
  }
}
</style>
