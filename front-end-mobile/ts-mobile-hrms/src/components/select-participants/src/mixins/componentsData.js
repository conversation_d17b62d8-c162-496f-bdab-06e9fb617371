export default {
  filters: {
    sexClassFilter(val) {
      return val === '0' ? 'sex-man' : 'sex-woman';
    },
    firstNameFilter(val) {
      return val.substring(val.length - 2);
    },
    iconColor(val) {
      return val ? '#ffffff' : 'transparent';
    },
    iconClass(val) {
      return val ? 'checkbox__icon-wrap--checked' : '';
    }
  },
  computed: {
    keyId() {
      return this.$attrs.dataKey.id;
    },
    keyName() {
      return this.$attrs.dataKey.name;
    },
    keyType() {
      return this.$attrs.dataKey.type;
    }
  }
};
