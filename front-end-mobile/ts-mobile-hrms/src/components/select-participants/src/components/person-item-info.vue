<template>
  <view class="person-list-item-info" @click="toggle(person)">
    <view class="checkbox__icon-wrap" :class="isChecked | iconClass">
      <u-icon
        class="checkbox__icon-wrap__icon"
        name="checkbox-mark"
        size="28"
        :color="isChecked | iconColor"
      />
    </view>
    <view class="left">
      <img
        class="person-head-image"
        v-if="person.avatar"
        :src="person.avatar"
      />
      <view
        v-else
        class="person-head-image"
        :class="person.gender | sexClassFilter"
      >
        {{ person.empName | firstNameFilter }}
      </view>
    </view>
    <view class="right">
      <view class="person-name">{{ person.empName }}</view>
      <view class="person-description">
        {{ person.orgName }}
      </view>
    </view>
  </view>
</template>

<script>
import componentsData from '../mixins/componentsData';
export default {
  mixins: [componentsData],
  props: {
    // 人员信息
    person: {
      type: Object,
      default: () => ({})
    },
    // 是否只读
    readOnly: {
      type: <PERSON>olean,
      default: () => false
    },
    // 选中数据
    selectData: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    selectDataIds() {
      return this.selectData.map(item => item[this.keyId]);
    },
    isChecked() {
      return this.selectDataIds.includes(this.person[this.keyId]);
    }
  },
  watch: {
    isChecked: {
      handler(val) {
        if (this.person.groupId) {
          // 发送事件  如果子级全部取消选中 则夫级 取消选中
          this.$emit('selectItemFormGroup', this.person);
        }
      },
      immediate: true
    }
  },
  methods: {
    toggle(e) {
      if (this.$attrs.selectAllState) {
        return;
      }
      const index = this.selectDataIds.indexOf(e[this.keyId]);

      if (index !== -1) {
        this.selectData.splice(index, 1);
      } else {
        this.selectData.push(e);
      }
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.person-list-item-info {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  .checkbox__icon-wrap {
    color: $uni-text-content-color;
    @include vue-flex;
    flex: none;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    width: 38rpx;
    height: 38rpx;
    color: transparent;
    text-align: center;
    transition-property: color, border-color, background-color;
    font-size: $uni-icon-size-base;
    border: 1px solid $uni-text-color-disable;
    border-radius: 4px;
    transition-duration: 0.2s;
  }
  .checkbox__icon-wrap--checked {
    border-color: $u-type-primary;
    background-color: $u-type-primary;
  }
  .left {
    margin: 0 24rpx;
    .person-head-image {
      width: $uni-img-size-lg;
      height: $uni-img-size-lg;
      border-radius: 50%;
      background-color: $u-bg-color;
      text-align: center;
      line-height: $uni-img-size-lg;
      font-size: $uni-font-size-base;
      color: $uni-text-color-inverse;
      &.sex-man {
        background-color: $sexman-color;
      }
      &.sex-woman {
        background-color: $sexwoman-color;
      }
    }
  }
  .right {
    flex: 1;
    .person-name {
      font-size: $uni-font-size-base;
      color: $u-main-color;
    }
    .person-description {
      color: $u-content-color;
      font-size: $uni-font-size-sm;
    }
  }
}
</style>
