<template>
  <u-popup
    class="select-participants-box"
    mode="bottom"
    v-model="dialog"
    :safe-area-inset-bottom="true"
    height="100%"
    @close="close"
  >
    <u-navbar title="OA系统" title-bold :customBack="backHandle">
      <text class="navbar-right" slot="right" @click="confirm">确定</text>
    </u-navbar>
    <view class="address-book-box">
      <view class="search-container">
        <u-search
          v-model="name"
          :show-action="false"
          placeholder="输入姓名、部门搜索"
          :disabled="selectAllState"
          @input="input"
          @clear="clear"
        ></u-search>
        <view class="choose-person-num" @click="handleShowSelected">
          已选({{ selectedNum }})
        </view>
      </view>
      <!--search view-->
      <search-result-view
        v-bind="$attrs"
        v-show="showSearchView"
        :searchData="searchData"
        :selectData="selectData"
      />
      <view class="select-participants-tabs" v-show="!showSearchView">
        <u-tabs
          activeColor="#005bac"
          barWidth="60"
          :list="tabList"
          :current="tabValue"
          :scrollable="false"
          @change="handleTabsChange"
        ></u-tabs>
      </view>
      <view class="participants-type-content" v-show="!showSearchView">
        <tabs-contacts
          ref="tabsContacts"
          v-show="tabValue === 0"
          :selectData="selectData"
          :selectAllState="selectAllState"
          v-bind="$attrs"
          @changeSelectAllState="changeSelectAllState"
        />
        <tabs-group
          v-show="tabValue === 1"
          groupType="0"
          :selectData="selectData"
          :selectAllState="selectAllState"
          v-bind="$attrs"
        />
        <tabs-group
          v-show="tabValue === 2"
          groupType="1"
          :selectData="selectData"
          :selectAllState="selectAllState"
          v-bind="$attrs"
        />
      </view>
    </view>

    <selected-dialog
      v-if="selectedDialog"
      v-model="selectedDialog"
      :selectData="selectData"
      v-bind="$attrs"
    />
  </u-popup>
</template>

<script>
import debounce from 'lodash.debounce';
import TabsContacts from './components/tabs-contacts';
import TabsGroup from './components/tabs-group';
import SelectedDialog from './components/selected-dialog';
import SearchResultView from './components/search-result-view';
import componentsData from './mixins/componentsData';
export default {
  name: 'SelectParticipants',
  mixins: [componentsData],
  components: {
    TabsContacts,
    TabsGroup,
    SelectedDialog,
    SearchResultView
  },
  model: {
    event: 'change',
    prop: 'dialog'
  },
  props: {
    dialog: {
      type: Boolean,
      default: () => false
    },
    value: {
      type: [Array, Boolean],
      default: () => false
    }
  },
  data: () => ({
    name: '', // search 输入框
    searchData: [], // 搜索列表
    tabValue: 0, // tabs value
    tabList: [{ name: '联系人' }, { name: '系统群组' }, { name: '个人群组' }],
    selectData: [], // 选中数据
    selectedDialog: false, // 已选列表
    selectAllState: false
  }),
  computed: {
    selectedNum() {
      return this.selectData.length;
    },
    showSearchView() {
      return this.name.trim() && this.name.trim().length > 0;
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val && val instanceof Array) {
          this.selectData = val;
        } else if (val && typeof val === 'boolean') {
          this.selectData = [];
          this.selectAllState = true;
        }
      },
      immediate: true
    },
    selectAllState: {
      handler(val) {
        if (val) {
          this.selectData.splice(0);
        }
      },
      immediate: true
    }
  },
  methods: {
    input: debounce(async function() {
      if (this.name === '') {
        return false;
      } else {
        const { object } = await this.ajax.getOrgEmp({
          name: this.name,
          dataKey: this.$attrs.dataKey
        });

        this.searchData = [...object.orgList, ...object.empList];
      }
    }, 666),
    confirm() {
      if (this.selectAllState) {
        uni.$emit('selectParticipantsConfirm', {
          data: this.selectData,
          all: true
        });
      } else {
        uni.$emit('selectParticipantsConfirm', {
          data: this.selectData,
          all: false
        });
      }

      this.$emit('change', false);
    },
    changeSelectAllState() {
      this.selectAllState = !this.selectAllState;
    },
    clear() {
      this.name = '';
    },
    handleShowSelected() {
      if (this.selectAllState) {
        return false;
      }

      this.selectedDialog = true;
    },
    handleTabsChange(val) {
      this.tabValue = val;
    },
    backHandle() {
      this.$emit('change', false);
    },
    close() {
      this.$emit('change', false);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
.select-participants-box {
  height: 100%;
  .navbar-right {
    padding: 0 $uni-spacing-row-lg;
    font-size: $uni-font-size-base;
  }

  .address-book-box {
    height: calc(100% - 88rpx);
    @include vue-flex(column);
    .search-container {
      padding: $uni-spacing-row-sm $uni-spacing-row-lg;
      background-color: #ffffff;
      display: flex;
      align-items: center;
      margin-bottom: $uni-spacing-col-base;
    }
    .choose-person-num {
      font-size: $uni-font-size-base;
      margin-left: $uni-spacing-row-lg;
    }
    .select-participants-tabs {
      text-align: center;
    }
    .participants-type-content {
      background: #fff;
      position: relative;
      flex: 1;
      width: 100%;
    }
  }
}
</style>
