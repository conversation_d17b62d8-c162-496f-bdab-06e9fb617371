<template name="w-picker">
  <view
    class="w-picker"
    :style="{ 'z-index': pickerZindex }"
    :key="createKey"
    :data-key="createKey"
  >
    <view
      class="mask"
      v-if="visible"
      :class="{ visible: visible }"
      @tap="onCancel('mask')"
      @touchmove.stop.prevent
      catchtouchmove="true"
    ></view>
    <view class="w-picker-cnt" v-if="visible" :class="{ visible: visible }">
      <view
        class="w-picker-header"
        @touchmove.stop.prevent
        catchtouchmove="true"
      >
        <text class="w-picker-btn" @tap.stop.prevent="onCancel('cancelBtn')">
          {{ cancelText }}
        </text>
        <slot></slot>
        <text
          class="w-picker-btn"
          :style="{ color: themeColor }"
          @tap.stop.prevent="pickerConfirm"
        >
          {{ confirmText }}
        </text>
      </view>
      <!-- 日期范围 -->
      <range-picker
        v-if="mode == 'range'"
        class="w-picker-wrapper"
        :startDate="startDate"
        :endDate="endDate"
        :value="value"
        :item-height="itemHeight"
        :current="current"
        @change="handlerChange"
        @touchstart="touchStart"
        @touchend="touchEnd"
      >
      </range-picker>
      <!-- 日期 -->
      <date-picker
        v-if="mode == 'date'"
        class="w-picker-wrapper"
        :startDate="startDate"
        :endDate="endDate"
        :value="value"
        :fields="fields"
        :item-height="itemHeight"
        :current="current"
        :disabled-after="disabledAfter"
        @change="handlerChange"
        @touchstart="touchStart"
        @touchend="touchEnd"
      >
      </date-picker>
      <time-picker
        v-if="mode == 'time'"
        class="w-picker-wrapper"
        :value="value"
        :item-height="itemHeight"
        :current="current"
        :disabled-after="disabledAfter"
        :second="second"
        @change="handlerChange"
        @touchstart="touchStart"
        @touchend="touchEnd"
      >
      </time-picker>
    </view>
  </view>
</template>

<script>
import datePicker from './datePicker/date-picker.vue';
import rangePicker from './datePicker/range-picker.vue';
import timePicker from './datePicker/time-picker.vue';
export default {
  name: 'DatePicker',
  components: {
    datePicker,
    rangePicker,
    timePicker
  },
  props: {
    mode: {
      type: String,
      default: 'date'
    },
    value: {
      //默认值
      type: [String, Array, Number],
      default: ''
    },
    current: {
      //是否默认显示当前时间，如果是，传的默认值将失效
      type: Boolean,
      default: false
    },
    themeColor: {
      //确认按钮主题颜色
      type: String,
      default: '#005BAC'
    },
    fields: {
      //日期颗粒度:year、month、day、hour、minute、second
      type: String,
      default: 'date'
    },
    disabledAfter: {
      //是否禁用当前之后的日期
      type: Boolean,
      default: false
    },
    second: {
      //time-picker是否显示秒
      type: Boolean,
      default: true
    },
    options: {
      //selector,region数据源
      type: [Array, Object],
      default() {
        return [];
      }
    },
    defaultProps: {
      //selector,linkagle字段转换配置
      type: Object,
      default() {
        return {
          label: 'label',
          value: 'value',
          children: 'children'
        };
      }
    },
    defaultType: {
      type: String,
      default: 'label'
    },
    hideArea: {
      //mode=region时，是否隐藏区县列
      type: Boolean,
      default: false
    },
    level: {
      //多级联动层级，表示几级联动,区间2-4;
      type: [Number, String],
      default: 2
    },
    timeout: {
      //是否开启点击延迟,当快速滚动 还没有滚动完毕点击关闭时得到的值是不准确的
      type: Boolean,
      default: false
    },
    expand: {
      //mode=shortterm 默认往后拓展天数
      type: [Number, String],
      default: 30
    },
    startDate: {
      type: String,
      default: '1920-01-01'
    },
    endDate: {
      type: String,
      default: '2100-12-31'
    },
    pickerZindex: {
      type: [Number, String],
      default: 888
    },
    /**@desc 左侧取消按钮文字内容 */
    cancelText: {
      type: String,
      default: '取消'
    },
    /**@desc 右侧确认按钮文字内容 */
    confirmText: {
      type: String,
      default: '确定'
    }
  },
  created() {
    this.createKey = Math.random() * 1000;
  },
  data() {
    return {
      itemHeight: `height: 88rpx;`,
      visible: false,
      result: {},
      confirmFlag: true
    };
  },
  methods: {
    touchStart() {
      if (this.timeout) {
        this.confirmFlag = false;
      }
    },
    touchEnd() {
      if (this.timeout) {
        setTimeout(() => {
          this.confirmFlag = true;
        }, 500);
      }
    },
    handlerChange(res) {
      let _this = this;
      this.result = { ...res };
    },
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    },
    onCancel(type = 'cancelBtn') {
      this.visible = false;
      this.$emit('cancel', type);
    },
    pickerConfirm() {
      if (!this.confirmFlag) {
        return;
      }
      this.$emit('confirm', this.result);
      this.visible = false;
    }
  }
};
</script>

<style lang="scss">
.w-picker-item {
  text-align: center;
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 30rpx;
}
.w-picker {
  z-index: 888;
  .mask {
    position: fixed;
    z-index: 1000;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.4);
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
  }
  .mask.visible {
    visibility: visible;
    opacity: 1;
  }
  .w-picker-cnt {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    transition: all 0.3s ease;
    transform: translateY(100%);
    z-index: 3000;
    background-color: #fff;
  }
  .w-picker-cnt.visible {
    transform: translateY(0);
  }
  .w-picker-header {
    display: flex;
    align-items: center;
    padding: 0 30rpx;
    height: 88rpx;
    background-color: #fff;
    position: relative;
    text-align: center;
    font-size: 32rpx;
    justify-content: space-between;
    border-bottom: solid 1px #eee;
    .w-picker-btn {
      font-size: 30rpx;
      color: #333333;
    }
  }

  .w-picker-hd:after {
    content: ' ';
    position: absolute;
    left: 0;
    bottom: 0;
    right: 0;
    height: 1px;
    border-bottom: 1px solid #e5e5e5;
    color: #e5e5e5;
    transform-origin: 0 100%;
    transform: scaleY(0.5);
  }
}
</style>
