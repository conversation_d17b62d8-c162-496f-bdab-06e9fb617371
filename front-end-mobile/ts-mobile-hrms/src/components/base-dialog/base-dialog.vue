<template>
  <u-popup v-model="visible" mode="center" width="560rpx" border-radius="16">
    <view class="base_dialog_container">
      <view class="base_dialog_container_head">
        <view class="base_dialog_container_head_title" v-if="title">
          {{ title }}
        </view>
        <u-input
          type="textarea"
          v-model="text"
          :placeholder="config.placeholder"
          v-if="config.showInput"
          trim
          style="background:#F4F4F4;margin-bottom:40rpx;padding:0 16rpx"
        ></u-input>
        <view class="base_dialog_container_head_content" v-else-if="content">
          {{ content }}
        </view>
      </view>
      <view class="base_dialog_container_footer">
        <view
          class="base_dialog_container_footer_btn"
          style="color: #999999;"
          @click="handleCancel"
        >
          取消
        </view>
        <view class="line"></view>
        <view
          class="base_dialog_container_footer_btn"
          style="color:#005bac"
          @click="handleSave"
        >
          确定
        </view>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  data() {
    return {
      visible: false,
      title: '标题',
      content: '告知当前状态，信息和解决方法',
      text: '',
      config: {}
    };
  },
  methods: {
    show(config) {
      let _config = {
        title: '',
        content: '',
        showInput: false,
        placeholder: '请输入'
      };
      _config = Object.assign(_config, config);
      this.config = _config;
      this.title = _config.title;
      this.content = _config.content;
      this.visible = true;
    },
    handleCancel() {
      this.visible = false;
      this.text = '';
    },
    handleSave() {
      this.visible = false;
      this.config.save(this.text);
      this.text = '';
    }
  }
};
</script>
<style lang="scss" scoped>
.base_dialog_container {
  padding-top: 40rpx;
  .base_dialog_container_head {
    padding: 0 24rpx;
    border-bottom: 2rpx solid #eee;
    .base_dialog_container_head_title {
      font-size: 36rpx;
      font-weight: 500;
      color: #333333;
      text-align: center;
      margin-bottom: 8rpx;
    }
    .base_dialog_container_head_content {
      font-size: 30rpx;
      font-weight: 400;
      color: #333333;
      text-align: center;
      margin-bottom: 32rpx;
    }
  }
  .base_dialog_container_footer {
    padding: 0 24rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    .base_dialog_container_footer_btn {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      text-align: center;
      font-size: 36rpx;
      font-weight: 400;
    }
    .line {
      height: 100%;
      width: 2rpx;
      background: #e5e5e5;
    }
  }
}
</style>
