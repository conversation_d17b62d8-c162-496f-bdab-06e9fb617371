# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Pajenn a-raok
previous_label=A-raok
next.title=Pajenn war-lerc'h
next_label=War-lerc'h

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Pajenn
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=eus {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} war {{pagesCount}})

zoom_out.title=Zoum bihanaat
zoom_out_label=Zoum bihanaat
zoom_in.title=Zoum brasaat
zoom_in_label=Zoum brasaat
zoom.title=Zoum
presentation_mode.title=Trec'haoliÃ± etrezek ar mod kinnigadenn
presentation_mode_label=Mod kinnigadenn
open_file.title=DigeriÃ± ur restr
open_file_label=DigeriÃ± ur restr
print.title=MoullaÃ±
print_label=MoullaÃ±
download.title=PellgargaÃ±
download_label=PellgargaÃ±
bookmark.title=Gwel bremanel (eilaÃ± pe zigeriÃ± e-barzh ur prenestr nevez)
bookmark_label=Gwel bremanel

# Secondary toolbar and context menu
tools.title=OstilhoÃ¹
tools_label=OstilhoÃ¹
first_page.title=Mont d'ar bajenn gentaÃ±
first_page_label=Mont d'ar bajenn gentaÃ±
last_page.title=Mont d'ar bajenn diwezhaÃ±
last_page_label=Mont d'ar bajenn diwezhaÃ±
page_rotate_cw.title=C'hwelaÃ± gant roud ar bizied
page_rotate_cw_label=C'hwelaÃ± gant roud ar bizied
page_rotate_ccw.title=C'hwelaÃ± gant roud gin ar bizied
page_rotate_ccw_label=C'hwelaÃ± gant roud gin ar bizied

cursor_text_select_tool.title=Gweredekaat an ostilh diuzaÃ± testenn
cursor_text_select_tool_label=Ostilh diuzaÃ± testenn
cursor_hand_tool.title=Gweredekaat an ostilh dorn
cursor_hand_tool_label=Ostilh dorn

scroll_vertical.title=ArveraÃ± an dibunaÃ± a-blom
scroll_vertical_label=DibunaÃ± a-serzh
scroll_horizontal.title=ArveraÃ± an dibunaÃ± a-blaen
scroll_horizontal_label=DibunaÃ± a-blaen
scroll_wrapped.title=ArveraÃ± an dibunaÃ± paket
scroll_wrapped_label=DibunaÃ± paket

spread_none.title=Chom hep stagaÃ± ar skignadurioÃ¹
spread_none_label=Skignadenn ebet
spread_odd.title=Lakaat ar pajennadoÃ¹ en ur gregiÃ± gant ar pajennoÃ¹ ampar
spread_odd_label=PajennoÃ¹ ampar
spread_even.title=Lakaat ar pajennadoÃ¹ en ur gregiÃ± gant ar pajennoÃ¹ par
spread_even_label=PajennoÃ¹ par

# Document properties dialog box
document_properties.title=PerzhioÃ¹ an teulâ¦
document_properties_label=PerzhioÃ¹ an teulâ¦
document_properties_file_name=Anv restr:
document_properties_file_size=Ment ar restr:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} Ke ({{size_b}} eizhbit)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} Me ({{size_b}} eizhbit)
document_properties_title=Titl:
document_properties_author=Aozer:
document_properties_subject=Danvez:
document_properties_keywords=GerioÃ¹-alc'hwez:
document_properties_creation_date=Deiziad krouiÃ±:
document_properties_modification_date=Deiziad kemmaÃ±:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Krouer:
document_properties_producer=Kenderc'her PDF:
document_properties_version=Handelv PDF:
document_properties_page_count=Niver a bajennoÃ¹:
document_properties_page_size=Ment ar bajenn:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=poltred
document_properties_page_size_orientation_landscape=gweledva
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Lizher
document_properties_page_size_name_legal=Lezennel
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Gwel Web Herrek:
document_properties_linearized_yes=Ya
document_properties_linearized_no=Ket
document_properties_close=SerriÃ±

print_progress_message=O prientiÃ± an teul evit moullaÃ±...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=NullaÃ±

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Diskouez/kuzhat ar varrenn gostez
toggle_sidebar_notification2.title=Trec'haoliÃ± ar varrenn-gostez (ur steuÃ±v pe stagadennoÃ¹ a zo en teul)
toggle_sidebar_label=Diskouez/kuzhat ar varrenn gostez
document_outline.title=Diskouez steuÃ±v an teul (daouglikit evit brasaat/bihanaat an holl elfennoÃ¹)
document_outline_label=SinedoÃ¹ an teuliad
attachments.title=Diskouez ar c'henstagadurioÃ¹
attachments_label=KenstagadurioÃ¹
layers.title=Diskouez ar gwiskadoÃ¹ (daou-glikaÃ± evit adderaouekaat an holl gwiskadoÃ¹ d'o stad dre ziouer)
layers_label=GwiskadoÃ¹
thumbs.title=Diskouez ar melvennoÃ¹
thumbs_label=MelvennoÃ¹
findbar.title=Klask e-barzh an teuliad
findbar_label=Klask

additional_layers=GwiskadoÃ¹ ouzhpenn
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Pajenn {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Pajenn {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Melvenn ar bajenn {{page}}

# Find panel button title and messages
find_input.title=Klask
find_input.placeholder=Klask e-barzh an teuliad
find_previous.title=Kavout an tamm frazenn kent o klotaÃ± ganti
find_previous_label=Kent
find_next.title=Kavout an tamm frazenn war-lerc'h o klotaÃ± ganti
find_next_label=War-lerc'h
find_highlight=UsskediÃ± pep tra
find_match_case_label=Teurel evezh ouzh ar pennlizherennoÃ¹
find_entire_word_label=GerioÃ¹ a-bezh
find_reached_top=Tizhet eo bet derou ar bajenn, kenderc'hel diouzh an diaz
find_reached_bottom=Tizhet eo bet dibenn ar bajenn, kenderc'hel diouzh ar c'hrec'h
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=Klotadenn {{current}} war {{total}}
find_match_count[two]=Klotadenn {{current}} war {{total}}
find_match_count[few]=Klotadenn {{current}} war {{total}}
find_match_count[many]=Klotadenn {{current}} war {{total}}
find_match_count[other]=Klotadenn {{current}} war {{total}}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_match_count_limit[one]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_match_count_limit[two]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_match_count_limit[few]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_match_count_limit[many]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_match_count_limit[other]=Muioc'h eget {{limit}} a glotadennoÃ¹
find_not_found=N'haller ket kavout ar frazenn

# Error panel labels
error_more_info=Muioc'h a ditouroÃ¹
error_less_info=Nebeutoc'h a ditouroÃ¹
error_close=SerriÃ±
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js handelv {{version}} (kempunadur: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Kemennadenn: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Torn: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Restr: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Linenn: {{line}}
rendering_error=Degouezhet ez eus bet ur fazi e-pad skrammaÃ± ar bajennad.

# Predefined zoom values
page_scale_width=Led ar bajenn
page_scale_fit=Pajenn a-bezh
page_scale_auto=Zoum emgefreek
page_scale_actual=Ment wir
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=O kargaÃ±â¦
loading_error=Degouezhet ez eus bet ur fazi e-pad kargaÃ± ar PDF.
invalid_file_error=Restr PDF didalvoudek pe kontronet.
missing_file_error=Restr PDF o vankout.
unexpected_response_error=Respont dic'hortoz a-berzh an dafariad

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} NotennaÃ±]
password_label=Enankit ar ger-tremen evit digeriÃ± ar restr PDF-maÃ±.
password_invalid=Ger-tremen didalvoudek. Klaskit en-dro mar plij.
password_ok=Mat eo
password_cancel=NullaÃ±

printing_not_supported=Kemenn: N'eo ket skoret penn-da-benn ar moullaÃ± gant ar merdeer-maÃ±.
printing_not_ready=Kemenn: N'hall ket bezaÃ± moullet ar restr PDF rak n'eo ket karget penn-da-benn.
web_fonts_disabled=Diweredekaet eo an nodrezhoÃ¹ web: n'haller ket arveraÃ± an nodrezhoÃ¹ PDF enframmet.
