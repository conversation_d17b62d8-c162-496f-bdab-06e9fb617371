# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Ð¡Ð°Ò³Ð¸ÑÐ°Ð¸ ÒÐ°Ð±Ð»Ó£
previous_label=ÒÐ°Ð±Ð»Ó£
next.title=Ð¡Ð°Ò³Ð¸ÑÐ°Ð¸ Ð½Ð°Ð²Ð±Ð°ÑÓ£
next_label=ÐÐ°Ð²Ð±Ð°ÑÓ£

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ð¡Ð°Ò³Ð¸ÑÐ°
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Ð°Ð· {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} Ð°Ð· {{pagesCount}})

zoom_out.title=Ð¥ÑÑÐ´ ÐºÐ°ÑÐ´Ð°Ð½
zoom_out_label=Ð¥ÑÑÐ´ ÐºÐ°ÑÐ´Ð°Ð½
zoom_in.title=ÐÐ°Ð»Ð¾Ð½ ÐºÐ°ÑÐ´Ð°Ð½
zoom_in_label=ÐÐ°Ð»Ð¾Ð½ ÐºÐ°ÑÐ´Ð°Ð½
zoom.title=Ð¢Ð°Ð½Ð·Ð¸Ð¼Ð¸ Ð°Ð½Ð´Ð¾Ð·Ð°
presentation_mode.title=ÐÑÐ·Ð°ÑÐ¸Ñ Ð±Ð° ÑÐµÒ·Ð°Ð¸ ÑÐ°ÒÐ´Ð¸Ð¼
presentation_mode_label=Ð ÐµÒ·Ð°Ð¸ ÑÐ°ÒÐ´Ð¸Ð¼
open_file.title=ÐÑÑÐ¾Ð´Ð°Ð½Ð¸ ÑÐ°Ð¹Ð»
open_file_label=ÐÑÑÐ¾Ð´Ð°Ð½
print.title=Ð§Ð¾Ð¿ ÐºÐ°ÑÐ´Ð°Ð½
print_label=Ð§Ð¾Ð¿ ÐºÐ°ÑÐ´Ð°Ð½
download.title=ÐÐ¾ÑÐ³Ð¸ÑÓ£ ÐºÐ°ÑÐ´Ð°Ð½
download_label=ÐÐ¾ÑÐ³Ð¸ÑÓ£ ÐºÐ°ÑÐ´Ð°Ð½
bookmark.title=ÐÐ°Ð¼ÑÐ´Ð¸ Ò·Ð¾ÑÓ£ (Ð½ÑÑÑÐ° Ð±Ð°ÑÐ´Ð¾ÑÑÐ°Ð½ Ñ ÐºÑÑÐ¾Ð´Ð°Ð½ Ð´Ð°Ñ ÑÐ°Ð²Ð·Ð°Ð½Ð°Ð¸ Ð½Ð°Ð²)
bookmark_label=ÐÐ°Ð¼ÑÐ´Ð¸ Ò·Ð¾ÑÓ£

# Secondary toolbar and context menu
tools.title=ÐÐ±Ð·Ð¾ÑÒ³Ð¾
tools_label=ÐÐ±Ð·Ð¾ÑÒ³Ð¾
first_page.title=ÐÐ° ÑÐ°Ò³Ð¸ÑÐ°Ð¸ Ð°Ð²Ð²Ð°Ð» Ð³ÑÐ·Ð°ÑÐµÐ´
first_page_label=ÐÐ° ÑÐ°Ò³Ð¸ÑÐ°Ð¸ Ð°Ð²Ð²Ð°Ð» Ð³ÑÐ·Ð°ÑÐµÐ´
last_page.title=ÐÐ° ÑÐ°Ò³Ð¸ÑÐ°Ð¸ Ð¾ÑÐ¸ÑÐ¸Ð½ Ð³ÑÐ·Ð°ÑÐµÐ´
last_page_label=ÐÐ° ÑÐ°Ò³Ð¸ÑÐ°Ð¸ Ð¾ÑÐ¸ÑÐ¸Ð½ Ð³ÑÐ·Ð°ÑÐµÐ´
page_rotate_cw.title=ÐÐ° ÑÐ°Ð¼ÑÐ¸ Ò³Ð°ÑÐ°ÐºÐ°ÑÐ¸ Ð°ÒÑÐ°Ð±Ð°ÐºÐ¸ ÑÐ¾Ð°Ñ Ð´Ð°Ð²Ñ Ð·Ð°Ð´Ð°Ð½
page_rotate_cw_label=ÐÐ° ÑÐ°Ð¼ÑÐ¸ Ò³Ð°ÑÐ°ÐºÐ°ÑÐ¸ Ð°ÒÑÐ°Ð±Ð°ÐºÐ¸ ÑÐ¾Ð°Ñ Ð´Ð°Ð²Ñ Ð·Ð°Ð´Ð°Ð½
page_rotate_ccw.title=ÐÐ° Ð¼ÑÒÐ¾Ð±Ð¸Ð»Ð¸ ÑÐ°Ð¼ÑÐ¸ Ò³Ð°ÑÐ°ÐºÐ°ÑÐ¸ Ð°ÒÑÐ°Ð±Ð°ÐºÐ¸ ÑÐ¾Ð°Ñ Ð´Ð°Ð²Ñ Ð·Ð°Ð´Ð°Ð½
page_rotate_ccw_label=ÐÐ° Ð¼ÑÒÐ¾Ð±Ð¸Ð»Ð¸ ÑÐ°Ð¼ÑÐ¸ Ò³Ð°ÑÐ°ÐºÐ°ÑÐ¸ Ð°ÒÑÐ°Ð±Ð°ÐºÐ¸ ÑÐ¾Ð°Ñ Ð´Ð°Ð²Ñ Ð·Ð°Ð´Ð°Ð½

cursor_text_select_tool.title=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Â«ÐÐ±Ð·Ð¾ÑÐ¸ Ð¸Ð½ÑÐ¸ÑÐ¾Ð±Ð¸ Ð¼Ð°ÑÐ½Â»
cursor_text_select_tool_label=ÐÐ±Ð·Ð¾ÑÐ¸ Ð¸Ð½ÑÐ¸ÑÐ¾Ð±Ð¸ Ð¼Ð°ÑÐ½
cursor_hand_tool.title=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Â«ÐÐ±Ð·Ð¾ÑÐ¸ Ð´Ð°ÑÑÂ»
cursor_hand_tool_label=ÐÐ±Ð·Ð¾ÑÐ¸ Ð´Ð°ÑÑ

scroll_page.title=ÐÑÑÐ¸ÑÐ¾Ð´Ð°Ð¸ Ð²Ð°ÑÐ°ÒÐ·Ð°Ð½Ó£
scroll_page_label=ÐÐ°ÑÐ°ÒÐ·Ð°Ð½Ó£
scroll_vertical.title=ÐÑÑÐ¸ÑÐ¾Ð´Ð°Ð¸ Ð²Ð°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ Ð°Ð¼ÑÐ´Ó£
scroll_vertical_label=ÐÐ°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ Ð°Ð¼ÑÐ´Ó£
scroll_horizontal.title=ÐÑÑÐ¸ÑÐ¾Ð´Ð°Ð¸ Ð²Ð°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ ÑÑÑÒÓ£
scroll_horizontal_label=ÐÐ°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ ÑÑÑÒÓ£
scroll_wrapped.title=ÐÑÑÐ¸ÑÐ¾Ð´Ð°Ð¸ Ð²Ð°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ Ð¼Ð¸ÒÑÑÐ±Ð°Ð½Ð´Ó£
scroll_wrapped_label=ÐÐ°ÑÐ°ÒÐ·Ð°Ð½Ð¸Ð¸ Ð¼Ð¸ÒÑÑÐ±Ð°Ð½Ð´Ó£

spread_none.title=ÐÑÑÑÐ°ÑÐ¸ÑÐ¸ ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾ Ð¸ÑÑÐ¸ÑÐ¾Ð´Ð° Ð±ÑÑÐ´Ð° Ð½Ð°ÑÐ°Ð²Ð°Ð´
spread_none_label=ÐÐµ Ð³ÑÑÑÑÑÐ´Ð°Ð½Ð¸ ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾
spread_odd.title=ÐÑÑÑÐ°ÑÐ¸ÑÐ¸ ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾ Ð°Ð· ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾ Ð±Ð¾ ÑÐ°ÒÐ°Ð¼Ò³Ð¾Ð¸ ÑÐ¾Ò Ð¾ÒÐ¾Ð· ÐºÐ°ÑÐ´Ð° Ð¼ÐµÑÐ°Ð²Ð°Ð´
spread_odd_label=Ð¡Ð°Ò³Ð¸ÑÐ°Ò³Ð¾Ð¸ ÑÐ¾Ò Ð°Ð· ÑÐ°ÑÐ°ÑÐ¸ ÑÐ°Ð¿
spread_even.title=ÐÑÑÑÐ°ÑÐ¸ÑÐ¸ ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾ Ð°Ð· ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾ Ð±Ð¾ ÑÐ°ÒÐ°Ð¼Ò³Ð¾Ð¸ Ò·ÑÑÑ Ð¾ÒÐ¾Ð· ÐºÐ°ÑÐ´Ð° Ð¼ÐµÑÐ°Ð²Ð°Ð´
spread_even_label=Ð¡Ð°Ò³Ð¸ÑÐ°Ò³Ð¾Ð¸ Ò·ÑÑÑ Ð°Ð· ÑÐ°ÑÐ°ÑÐ¸ ÑÐ°Ð¿

# Document properties dialog box
document_properties.title=Ð¥ÑÑÑÑÐ¸ÑÑÒ³Ð¾Ð¸ Ò³ÑÒ·Ò·Ð°Ñâ¦
document_properties_label=Ð¥ÑÑÑÑÐ¸ÑÑÒ³Ð¾Ð¸ Ò³ÑÒ·Ò·Ð°Ñâ¦
document_properties_file_name=ÐÐ¾Ð¼Ð¸ ÑÐ°Ð¹Ð»:
document_properties_file_size=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ ÑÐ°Ð¹Ð»:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} Ð±Ð°Ð¹Ñ)
document_properties_title=Ð¡Ð°ÑÐ»Ð°Ð²Ò³Ð°:
document_properties_author=ÐÑÐ°Ð»Ð»Ð¸Ñ:
document_properties_subject=ÐÐ°Ð²Ð·ÑÑ:
document_properties_keywords=ÐÐ°Ð»Ð¸Ð¼Ð°Ò³Ð¾Ð¸ ÐºÐ°Ð»Ð¸Ð´Ó£:
document_properties_creation_date=Ð¡Ð°Ð½Ð°Ð¸ ÑÒ·Ð¾Ð´:
document_properties_modification_date=Ð¡Ð°Ð½Ð°Ð¸ ÑÐ°ÒÐ¹Ð¸ÑÐ¾Ñ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Ð­Ò·Ð¾Ð´ÐºÑÐ½Ð°Ð½Ð´Ð°:
document_properties_producer=Ð¢Ð°Ò³Ð¸ÑÐºÑÐ½Ð°Ð½Ð´Ð°Ð¸ PDF:
document_properties_version=ÐÐµÑÑÐ¸ÑÐ¸ PDF:
document_properties_page_count=Ð¨ÑÐ¼Ð¾ÑÐ°Ð¸ ÑÐ°Ò³Ð¸ÑÐ°Ò³Ð¾:
document_properties_page_size=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ ÑÐ°Ò³Ð¸ÑÐ°:
document_properties_page_size_unit_inches=Ð´ÑÐ¹Ð¼
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=Ð°Ð¼ÑÐ´Ó£
document_properties_page_size_orientation_landscape=ÑÑÑÒÓ£
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÐÐ°ÐºÑÑÐ±
document_properties_page_size_name_legal=Ò²ÑÒÑÒÓ£
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=ÐÐ°Ð¼Ð¾Ð¸ÑÐ¸ ÑÐµÐ· Ð´Ð°Ñ ÐÐ½ÑÐµÑÐ½ÐµÑ:
document_properties_linearized_yes=Ò²Ð°
document_properties_linearized_no=ÐÐµ
document_properties_close=ÐÓ¯ÑÐ¸Ð´Ð°Ð½

print_progress_message=ÐÐ¼Ð¾Ð´Ð°ÑÐ¾Ð·Ð¸Ð¸ Ò³ÑÒ·Ò·Ð°Ñ Ð±Ð°ÑÐ¾Ð¸ ÑÐ¾Ð¿â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÐÐµÐºÐ¾Ñ ÐºÐ°ÑÐ´Ð°Ð½

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ð½Ð°Ð²Ð¾ÑÐ¸ Ò·Ð¾Ð½Ð¸Ð±Ó£
toggle_sidebar_notification2.title=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ð½Ð°Ð²Ð¾ÑÐ¸ Ò·Ð¾Ð½Ð¸Ð±Ó£ (Ò³ÑÒ·Ò·Ð°Ñ Ð´Ð¾ÑÐ¾Ð¸ ÑÐ¾ÑÑÐ¾Ñ/Ð·Ð°Ð¼Ð¸Ð¼Ð°Ò³Ð¾/ÒÐ°Ð±Ð°ÑÒ³Ð¾ Ð¼ÐµÐ±Ð¾ÑÐ°Ð´)
toggle_sidebar_label=Ð¤Ð°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ð½Ð°Ð²Ð¾ÑÐ¸ Ò·Ð¾Ð½Ð¸Ð±Ó£
document_outline.title=ÐÐ°Ð¼Ð¾Ð¸Ñ Ð´Ð¾Ð´Ð°Ð½Ð¸ ÑÐ¾ÑÑÐ¾ÑÐ¸ Ò³ÑÒ·Ò·Ð°Ñ (Ð±Ð°ÑÐ¾Ð¸ Ð±Ð°ÑÐºÑÑÐ¾Ð´Ð°Ð½/Ð¿ÐµÒ·Ð¾Ð½Ð´Ð°Ð½Ð¸ Ò³Ð°Ð¼Ð°Ð¸ ÑÐ½ÑÑÑÒ³Ð¾ Ð´ÑÐ±Ð¾ÑÐ° Ð·ÐµÑ ÐºÑÐ½ÐµÐ´)
document_outline_label=Ð¡Ð¾ÑÑÐ¾ÑÐ¸ Ò³ÑÒ·Ò·Ð°Ñ
attachments.title=ÐÐ°Ð¼Ð¾Ð¸Ñ Ð´Ð¾Ð´Ð°Ð½Ð¸ Ð·Ð°Ð¼Ð¸Ð¼Ð°Ò³Ð¾
attachments_label=ÐÐ°Ð¼Ð¸Ð¼Ð°Ò³Ð¾
layers.title=ÐÐ°Ð¼Ð¾Ð¸Ñ Ð´Ð¾Ð´Ð°Ð½Ð¸ ÒÐ°Ð±Ð°ÑÒ³Ð¾ (Ð±Ð°ÑÐ¾Ð¸ Ð±Ð°ÑÒÐ°ÑÐ¾Ñ ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ò³Ð°Ð¼Ð°Ð¸ ÒÐ°Ð±Ð°ÑÒ³Ð¾ Ð±Ð° Ð²Ð°Ð·ÑÐ¸ÑÑÐ¸ Ð¿ÐµÑÑÐ°ÑÐ· Ð´ÑÐ±Ð¾ÑÐ° Ð·ÐµÑ ÐºÑÐ½ÐµÐ´)
layers_label=ÒÐ°Ð±Ð°ÑÒ³Ð¾
thumbs.title=ÐÐ°Ð¼Ð¾Ð¸Ñ Ð´Ð¾Ð´Ð°Ð½Ð¸ ÑÐ°ÑÐ²Ð¸ÑÑÐ°Ò³Ð¾
thumbs_label=Ð¢Ð°ÑÐ²Ð¸ÑÑÐ°Ò³Ð¾
current_outline_item.title=ÐÑÑÐ°Ð½Ð¸ ÑÐ½ÑÑÑÐ¸ ÑÐ¾ÑÑÐ¾ÑÐ¸ Ò·Ð¾ÑÓ£
current_outline_item_label=Ð£Ð½ÑÑÑÐ¸ ÑÐ¾ÑÑÐ¾ÑÐ¸ Ò·Ð¾ÑÓ£
findbar.title=ÐÑÑÐ°Ð½ Ð´Ð°Ñ Ò³ÑÒ·Ò·Ð°Ñ
findbar_label=ÐÑÑÐ°Ð½

additional_layers=ÒÐ°Ð±Ð°ÑÒ³Ð¾Ð¸ Ð¸Ð»Ð¾Ð²Ð°Ð³Ó£
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=Ð¡Ð°Ò³Ð¸ÑÐ°Ð¸ {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡Ð°Ò³Ð¸ÑÐ°Ð¸ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ð¢Ð°ÑÐ²Ð¸ÑÑÐ°Ð¸ ÑÐ°Ò³Ð¸ÑÐ°Ð¸ {{page}}

# Find panel button title and messages
find_input.title=ÐÑÑÐ°Ð½
find_input.placeholder=ÐÑÑÐ°Ð½ Ð´Ð°Ñ Ò³ÑÒ·Ò·Ð°Ñâ¦
find_previous.title=Ò¶ÑÑÑÑÒ·Ó¯Ð¸ Ð¼Ð°Ð²ÑÐ¸Ð´Ð¸ ÒÐ°Ð±Ð»Ð¸Ð¸ Ð¸Ð±Ð¾ÑÐ°Ð¸ Ð¿ÐµÑÐ½Ð¸Ò³Ð¾Ð´ÑÑÐ´Ð°
find_previous_label=ÒÐ°Ð±Ð»Ó£
find_next.title=Ò¶ÑÑÑÑÒ·Ó¯Ð¸ Ð¼Ð°Ð²ÑÐ¸Ð´Ð¸ Ð½Ð°Ð²Ð±Ð°ÑÐ¸Ð¸ Ð¸Ð±Ð¾ÑÐ°Ð¸ Ð¿ÐµÑÐ½Ð¸Ò³Ð¾Ð´ÑÑÐ´Ð°
find_next_label=ÐÐ°Ð²Ð±Ð°ÑÓ£
find_highlight=Ò²Ð°Ð¼Ð°Ð°ÑÑÐ¾ Ð±Ð¾ ÑÐ°Ð½Ð³ Ò·ÑÐ´Ð¾ ÐºÐ°ÑÐ´Ð°Ð½
find_match_case_label=ÐÐ¾ Ð´Ð°ÑÐ½Ð°Ð·Ð°ÑÐ´Ð¾ÑÑÐ¸ Ò³Ð°ÑÑÒ³Ð¾Ð¸ ÑÑÑÐ´Ñ ÐºÐ°Ð»Ð¾Ð½
find_match_diacritics_label=ÐÐ¾ Ð´Ð°ÑÐ½Ð°Ð·Ð°ÑÐ´Ð¾ÑÑÐ¸ Ð°Ð»Ð¾Ð¼Ð°ÑÒ³Ð¾Ð¸ Ð´Ð¸Ð°ÐºÑÐ¸ÑÐ¸ÐºÓ£
find_entire_word_label=ÐÐ°Ð»Ð¸Ð¼Ð°Ò³Ð¾Ð¸ Ð¿ÑÑÑÐ°
find_reached_top=ÐÐ° Ð±Ð¾Ð»Ð¾Ð¸ Ò³ÑÒ·Ò·Ð°Ñ ÑÐ°ÑÐ¸Ð´, Ð°Ð· Ð¿Ð¾ÑÐ½ Ð¸Ð´Ð¾Ð¼Ð° ÑÑÑ
find_reached_bottom=ÐÐ° Ð¿Ð¾ÑÐ½Ð¸ Ò³ÑÒ·Ò·Ð°Ñ ÑÐ°ÑÐ¸Ð´, Ð°Ð· Ð±Ð¾Ð»Ð¾ Ð¸Ð´Ð¾Ð¼Ð° ÑÑÑ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Ð°Ð· {{total}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count[two]={{current}} Ð°Ð· {{total}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count[few]={{current}} Ð°Ð· {{total}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count[many]={{current}} Ð°Ð· {{total}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count[other]={{current}} Ð°Ð· {{total}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[one]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[two]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[few]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[many]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_match_count_limit[other]=ÐÐ¸ÑÐ´Ð° Ð°Ð· {{limit}} Ð¼ÑÐ²Ð¾ÑÐ¸ÒÐ°Ñ
find_not_found=ÐÐ±Ð¾ÑÐ° ÑÑÑ Ð½Ð°ÑÑÐ´

# Error panel labels
error_more_info=ÐÐ°ÑÐ»ÑÐ¼Ð¾ÑÐ¸ Ð±ÐµÑÑÐ°Ñ
error_less_info=ÐÐ°ÑÐ»ÑÐ¼Ð¾ÑÐ¸ ÐºÐ°Ð¼ÑÐ°Ñ
error_close=ÐÓ¯ÑÐ¸Ð´Ð°Ð½
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (ÑÐ¾ÑÑ: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÐÐ°ÑÐ¼: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ÐÐ°ÑÑÐ°: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ð¡Ð°ÑÑ: {{line}}
rendering_error=Ò²Ð°Ð½Ð³Ð¾Ð¼Ð¸ ÑÐ°ÐºÐ»ÑÐ¾Ð·Ð¸Ð¸ ÑÐ°Ò³Ð¸ÑÐ° ÑÐ°ÑÐ¾ Ð±Ð° Ð¼Ð¸ÑÐ½ Ð¾Ð¼Ð°Ð´.

# Predefined zoom values
page_scale_width=ÐÐ· ÑÓ¯Ð¸ Ð¿Ð°Ò³Ð½Ð¾Ð¸ ÑÐ°Ò³Ð¸ÑÐ°
page_scale_fit=ÐÐ· ÑÓ¯Ð¸ Ð°Ð½Ð´Ð¾Ð·Ð°Ð¸ ÑÐ°Ò³Ð¸ÑÐ°
page_scale_auto=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ ÑÑÐ´ÐºÐ¾Ñ
page_scale_actual=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ Ð²Ð¾ÒÐµÓ£
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading=ÐÐ¾Ñ ÑÑÐ´Ð° Ð¸ÑÑÐ¾Ð´Ð°Ð°ÑÑâ¦
loading_error=Ò²Ð°Ð½Ð³Ð¾Ð¼Ð¸ Ð±Ð¾ÑÐºÑÐ½Ð¸Ð¸ PDF ÑÐ°ÑÐ¾ Ð±Ð° Ð¼Ð¸ÑÐ½ Ð¾Ð¼Ð°Ð´.
invalid_file_error=Ð¤Ð°Ð¹Ð»Ð¸ PDF Ð½Ð¾Ð´ÑÑÑÑÑ Ñ Ð²Ð°Ð¹ÑÐ¾Ð½ÑÑÐ´Ð° Ð¼ÐµÐ±Ð¾ÑÐ°Ð´.
missing_file_error=Ð¤Ð°Ð¹Ð»Ð¸ PDF ÒÐ¾Ð¸Ð± Ð°ÑÑ.
unexpected_response_error=Ò¶Ð°Ð²Ð¾Ð±Ð¸ Ð½Ð¾Ð³Ð°Ò³Ð¾Ð½ Ð°Ð· ÑÐµÑÐ²ÐµÑ.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Ò²Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÓ£ - {{type}}]
password_label=ÐÐ°ÑÐ¾Ð¸ ÐºÑÑÐ¾Ð´Ð°Ð½Ð¸ Ð¸Ð½ ÑÐ°Ð¹Ð»Ð¸ PDF Ð½Ð¸Ò³Ð¾Ð½Ð²Ð¾Ð¶Ð°ÑÐ¾ Ð²Ð¾ÑÐ¸Ð´ ÐºÑÐ½ÐµÐ´.
password_invalid=ÐÐ¸Ò³Ð¾Ð½Ð²Ð¾Ð¶Ð°Ð¸ Ð½Ð¾Ð´ÑÑÑÑÑ. ÐÑÑÑÐ°Ð½, Ð°Ð· Ð½Ð°Ð² ÐºÓ¯ÑÐ¸Ñ ÐºÑÐ½ÐµÐ´.
password_ok=Ð¥Ð£Ð
password_cancel=ÐÐµÐºÐ¾Ñ ÐºÐ°ÑÐ´Ð°Ð½

printing_not_supported=ÐÐ¸ÒÒÐ°Ñ: Ð§Ð¾Ð¿ÐºÑÐ½Ó£ Ð°Ð· ÑÐ°ÑÐ°ÑÐ¸ Ð¸Ð½ Ð±ÑÐ°ÑÐ·ÐµÑ Ð±Ð° ÑÐ°Ð²ÑÐ¸ Ð¿ÑÑÑÐ° Ð´Ð°ÑÑÐ³Ð¸ÑÓ£ Ð½Ð°Ð¼ÐµÑÐ°Ð²Ð°Ð´.
printing_not_ready=ÐÐ¸ÒÒÐ°Ñ: Ð¤Ð°Ð¹Ð»Ð¸ PDF Ð±Ð°ÑÐ¾Ð¸ ÑÐ¾Ð¿ÐºÑÐ½Ó£ Ð¿ÑÑÑÐ° Ð±Ð¾Ñ ÐºÐ°ÑÐ´Ð° Ð½Ð°ÑÑÐ´.
web_fonts_disabled=Ð¨ÑÐ¸ÑÑÒ³Ð¾Ð¸ Ð¸Ð½ÑÐµÑÐ½ÐµÑÓ£ ÒÐ°Ð¹ÑÐ¸ÑÐ°ÑÐ¾Ð»Ð°Ð½Ð´: Ð¸ÑÑÐ¸ÑÐ¾Ð´Ð°Ð¸ ÑÑÐ¸ÑÑÒ³Ð¾Ð¸ Ð´Ð°ÑÑÐ½ÑÐ¾ÑÑÐ¸ PDF ÒÐ°Ð¹ÑÐ¸Ð¸Ð¼ÐºÐ¾Ð½ Ð°ÑÑ.

# Editor
editor_none.title=ÒÐ°Ð¹ÑÐ¸ÑÐ°ÑÐ¾Ð» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ ÑÐ°Ò³ÑÐ¸ÑÐ¸ Ò³Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÓ£
editor_none_label=ÒÐ°Ð¹ÑÐ¸ÑÐ°ÑÐ» ÐºÐ°ÑÐ´Ð°Ð½Ð¸ ÑÐ°Ò³ÑÐ¸ÑÐ¸ Ð¼Ð°ÑÐ½
editor_free_text.title=ÐÐ»Ð¾Ð²Ð° ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ò³Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÐ¸Ð¸ Â«FreeTextÂ»
editor_free_text_label=Ò²Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÐ¸Ð¸ Â«FreeTextÂ»
editor_ink.title=ÐÐ»Ð¾Ð²Ð° ÐºÐ°ÑÐ´Ð°Ð½Ð¸ Ò³Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÐ¸Ð¸ Ð´Ð°ÑÑÐ½Ð°Ð²Ð¸Ñ
editor_ink_label=Ò²Ð¾ÑÐ¸ÑÐ½Ð°Ð²Ð¸ÑÐ¸Ð¸ Ð´Ð°ÑÑÐ½Ð°Ð²Ð¸Ñ

freetext_default_content=Ð¯Ð³Ð¾Ð½ Ð¼Ð°ÑÐ½ÑÐ¾ Ð²Ð¾ÑÐ¸Ð´ Ð½Ð°Ð¼Ð¾ÐµÐ´â¦

free_text_default_content=ÐÐ°ÑÐ½ÑÐ¾ Ð²Ð¾ÑÐ¸Ð´ Ð½Ð°Ð¼Ð¾ÐµÐ´â¦

# Editor Parameters
editor_free_text_font_color=Ð Ð°Ð½Ð³Ð¸ Ò³ÑÑÑÑ
editor_free_text_font_size=ÐÐ½Ð´Ð¾Ð·Ð°Ð¸ Ò³ÑÑÑÑ
editor_ink_line_color=Ð Ð°Ð½Ð³Ð¸ ÑÐ°ÑÑ
editor_ink_line_thickness=ÒÐ°ÑÑÐ¸Ð¸ ÑÐ°ÑÑ

# Editor Parameters
editor_free_text_color=Ð Ð°Ð½Ð³
editor_free_text_size=ÐÐ½Ð´Ð¾Ð·Ð°
editor_ink_color=Ð Ð°Ð½Ð³
editor_ink_thickness=ÒÐ°ÑÑÓ£
editor_ink_opacity=Ð¨Ð°ÑÑÐ¾ÑÓ£

# Editor aria
editor_free_text_aria_label=ÐÑÒ³Ð°ÑÑÐ¸ÑÐ¸ Â«FreeTextÂ»
editor_ink_aria_label=ÐÑÒ³Ð°ÑÑÐ¸ÑÐ¸ ÑÐ°Ð½Ð³
editor_ink_canvas_aria_label=Ð¢Ð°ÑÐ²Ð¸ÑÐ¸ ÑÒ·Ð¾Ð´ÐºÐ°ÑÐ´Ð°Ð¸ ÐºÐ¾ÑÐ±Ð°Ñ
