# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=à¤ªà¤¿à¤à¤²à¤¾ à¤ªà¥à¤·à¥à¤ 
previous_label=à¤ªà¤¿à¤à¤²à¤¾
next.title=à¤à¤à¤²à¤¾ à¤ªà¥à¤·à¥à¤ 
next_label=à¤à¤à¥

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=à¤ªà¥à¤·à¥à¤ :
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}} à¤à¤¾
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} of {{pagesCount}})

zoom_out.title=\u0020à¤à¥à¤à¤¾ à¤à¤°à¥à¤
zoom_out_label=\u0020à¤à¥à¤à¤¾ à¤à¤°à¥à¤
zoom_in.title=à¤¬à¤¡à¤¼à¤¾ à¤à¤°à¥à¤
zoom_in_label=à¤¬à¤¡à¤¼à¤¾ à¤à¤°à¥à¤
zoom.title=à¤¬à¤¡à¤¼à¤¾-à¤à¥à¤à¤¾ à¤à¤°à¥à¤
presentation_mode.title=à¤ªà¥à¤°à¤¸à¥à¤¤à¥à¤¤à¤¿ à¤à¤µà¤¸à¥à¤¥à¤¾ à¤®à¥à¤ à¤à¤¾à¤à¤
presentation_mode_label=\u0020à¤ªà¥à¤°à¤¸à¥à¤¤à¥à¤¤à¤¿ à¤à¤µà¤¸à¥à¤¥à¤¾
open_file.title=à¤«à¤¼à¤¾à¤à¤² à¤à¥à¤²à¥à¤
open_file_label=\u0020à¤à¥à¤²à¥à¤
print.title=à¤à¤¾à¤ªà¥à¤
print_label=\u0020à¤à¤¾à¤ªà¥à¤
download.title=à¤¡à¤¾à¤à¤¨à¤²à¥à¤¡
download_label=à¤¡à¤¾à¤à¤¨à¤²à¥à¤¡
bookmark.title=à¤®à¥à¤à¥à¤¦à¤¾ à¤¦à¥à¤¶à¥à¤¯ (à¤¨à¤ à¤µà¤¿à¤à¤¡à¥ à¤®à¥à¤ à¤¨à¤à¤¼à¤² à¤²à¥à¤ à¤¯à¤¾ à¤à¥à¤²à¥à¤)
bookmark_label=\u0020à¤®à¥à¤à¥à¤¦à¤¾ à¤¦à¥à¤¶à¥à¤¯

# Secondary toolbar and context menu
tools.title=à¤à¤à¤¼à¤¾à¤°
tools_label=à¤à¤à¤¼à¤¾à¤°
first_page.title=à¤ªà¥à¤°à¤¥à¤® à¤ªà¥à¤·à¥à¤  à¤ªà¤° à¤à¤¾à¤à¤
first_page_label=à¤ªà¥à¤°à¤¥à¤® à¤ªà¥à¤·à¥à¤  à¤ªà¤° à¤à¤¾à¤à¤
last_page.title=à¤à¤à¤¤à¤¿à¤® à¤ªà¥à¤·à¥à¤  à¤ªà¤° à¤à¤¾à¤à¤
last_page_label=\u0020à¤à¤à¤¤à¤¿à¤® à¤ªà¥à¤·à¥à¤  à¤ªà¤° à¤à¤¾à¤à¤
page_rotate_cw.title=à¤à¤¡à¤¼à¥ à¤à¥ à¤¦à¤¿à¤¶à¤¾ à¤®à¥à¤ à¤à¥à¤®à¤¾à¤à¤
page_rotate_cw_label=à¤à¤¡à¤¼à¥ à¤à¥ à¤¦à¤¿à¤¶à¤¾ à¤®à¥à¤ à¤à¥à¤®à¤¾à¤à¤
page_rotate_ccw.title=à¤à¤¡à¤¼à¥ à¤à¥ à¤¦à¤¿à¤¶à¤¾ à¤¸à¥ à¤à¤²à¥à¤à¤¾ à¤à¥à¤®à¤¾à¤à¤
page_rotate_ccw_label=\u0020à¤à¤¡à¤¼à¥ à¤à¥ à¤¦à¤¿à¤¶à¤¾ à¤¸à¥ à¤à¤²à¥à¤à¤¾ à¤à¥à¤®à¤¾à¤à¤

cursor_text_select_tool.title=à¤ªà¤¾à¤  à¤à¤¯à¤¨ à¤à¤ªà¤à¤°à¤£ à¤¸à¤à¥à¤·à¤® à¤à¤°à¥à¤
cursor_text_select_tool_label=à¤ªà¤¾à¤  à¤à¤¯à¤¨ à¤à¤ªà¤à¤°à¤£
cursor_hand_tool.title=à¤¹à¤¸à¥à¤¤ à¤à¤ªà¤à¤°à¤£ à¤¸à¤à¥à¤·à¤® à¤à¤°à¥à¤
cursor_hand_tool_label=à¤¹à¤¸à¥à¤¤ à¤à¤ªà¤à¤°à¤£

scroll_vertical.title=à¤²à¤à¤¬à¤µà¤¤ à¤¸à¥à¤à¥à¤°à¥à¤²à¤¿à¤à¤ à¤à¤¾ à¤à¤ªà¤¯à¥à¤ à¤à¤°à¥à¤
scroll_vertical_label=à¤²à¤à¤¬à¤µà¤¤ à¤¸à¥à¤à¥à¤°à¥à¤²à¤¿à¤à¤
scroll_horizontal.title=à¤à¥à¤·à¤¿à¤¤à¤¿à¤à¤¿à¤¯ à¤¸à¥à¤à¥à¤°à¥à¤²à¤¿à¤à¤ à¤à¤¾ à¤à¤ªà¤¯à¥à¤ à¤à¤°à¥à¤
scroll_horizontal_label=à¤à¥à¤·à¤¿à¤¤à¤¿à¤à¤¿à¤¯ à¤¸à¥à¤à¥à¤°à¥à¤²à¤¿à¤à¤
scroll_wrapped.title=à¤µà¥à¤°à¤¾à¤ªà¥à¤ªà¥à¤¡ à¤¸à¥à¤à¥à¤°à¥à¤²à¤¿à¤à¤ à¤à¤¾ à¤à¤ªà¤¯à¥à¤ à¤à¤°à¥à¤

spread_none_label=à¤à¥à¤ à¤¸à¥à¤ªà¥à¤°à¥à¤¡ à¤à¤ªà¤²à¤¬à¥à¤§ à¤¨à¤¹à¥à¤
spread_odd.title=à¤µà¤¿à¤·à¤®-à¤à¥à¤°à¤®à¤¾à¤à¤à¤¿à¤¤ à¤ªà¥à¤·à¥à¤ à¥à¤ à¤¸à¥ à¤ªà¥à¤°à¤¾à¤°à¤à¤­ à¤¹à¥à¤¨à¥ à¤µà¤¾à¤²à¥ à¤ªà¥à¤·à¥à¤  à¤¸à¥à¤ªà¥à¤°à¥à¤¡ à¤®à¥à¤ à¤¶à¤¾à¤®à¤¿à¤² à¤¹à¥à¤
spread_odd_label=à¤µà¤¿à¤·à¤® à¤«à¥à¤²à¤¾à¤µ

# Document properties dialog box
document_properties.title=à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤µà¤¿à¤¶à¥à¤·à¤¤à¤¾...
document_properties_label=à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤µà¤¿à¤¶à¥à¤·à¤¤à¤¾...
document_properties_file_name=à¤«à¤¼à¤¾à¤à¤² à¤¨à¤¾à¤®:
document_properties_file_size=à¤«à¤¾à¤à¤² à¤à¤à¤¾à¤°à¤
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} bytes)
document_properties_title=à¤¶à¥à¤°à¥à¤·à¤:
document_properties_author=à¤²à¥à¤à¤à¤
document_properties_subject=à¤µà¤¿à¤·à¤¯:
document_properties_keywords=à¤à¥à¤à¤à¥-à¤¶à¤¬à¥à¤¦:
document_properties_creation_date=à¤¨à¤¿à¤°à¥à¤®à¤¾à¤£ à¤¦à¤¿à¤¨à¤¾à¤à¤:
document_properties_modification_date=à¤¸à¤à¤¶à¥à¤§à¤¨ à¤¦à¤¿à¤¨à¤¾à¤à¤:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=à¤¨à¤¿à¤°à¥à¤®à¤¾à¤¤à¤¾:
document_properties_producer=PDF à¤à¤¤à¥à¤ªà¤¾à¤¦à¤:
document_properties_version=PDF à¤¸à¤à¤¸à¥à¤à¤°à¤£:
document_properties_page_count=à¤ªà¥à¤·à¥à¤  à¤à¤¿à¤¨à¤¤à¥:
document_properties_page_size=à¤ªà¥à¤·à¥à¤  à¤à¤à¤¾à¤°:
document_properties_page_size_unit_inches=à¤à¤à¤
document_properties_page_size_unit_millimeters=à¤®à¤¿à¤®à¥
document_properties_page_size_orientation_portrait=à¤ªà¥à¤°à¥à¤à¥à¤°à¥à¤
document_properties_page_size_orientation_landscape=à¤²à¥à¤à¤¡à¤¸à¥à¤à¥à¤ª
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=à¤ªà¤¤à¥à¤°
document_properties_page_size_name_legal=à¥à¤¾à¤¨à¥à¤¨à¥
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=à¤¤à¥à¤µà¥à¤° à¤µà¥à¤¬ à¤µà¥à¤¯à¥:
document_properties_linearized_yes=à¤¹à¤¾à¤
document_properties_linearized_no=à¤¨à¤¹à¥à¤
document_properties_close=à¤¬à¤à¤¦ à¤à¤°à¥à¤

print_progress_message=à¤à¤ªà¤¾à¤ à¤à¥ à¤²à¤¿à¤ à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤à¥ à¤¤à¥à¤¯à¤¾à¤° à¤à¤¿à¤¯à¤¾ à¤à¤¾ à¤°à¤¹à¤¾ à¤¹à¥...
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=à¤°à¤¦à¥à¤¦ à¤à¤°à¥à¤

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=\u0020à¤¸à¥à¤²à¤¾à¤à¤¡à¤° à¤à¥à¤à¤² à¤à¤°à¥à¤
toggle_sidebar_label=à¤¸à¥à¤²à¤¾à¤à¤¡à¤° à¤à¥à¤à¤² à¤à¤°à¥à¤
document_outline.title=à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤à¥ à¤°à¥à¤ªà¤°à¥à¤à¤¾ à¤¦à¤¿à¤à¤¾à¤à¤ (à¤¸à¤¾à¤°à¥ à¤µà¤¸à¥à¤¤à¥à¤à¤ à¤à¥ à¤«à¤²à¤¨à¥ à¤à¤¥à¤µà¤¾ à¤¸à¤®à¥à¤à¤¨à¥ à¤à¥ à¤²à¤¿à¤ à¤¦à¥ à¤¬à¤¾à¤° à¤à¥à¤²à¤¿à¤ à¤à¤°à¥à¤)
document_outline_label=à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤à¤à¤à¤²à¤¾à¤à¤¨
attachments.title=à¤¸à¤à¤²à¤à¥à¤¨à¤ à¤¦à¤¿à¤à¤¾à¤¯à¥à¤
attachments_label=à¤¸à¤à¤²à¤à¥à¤¨à¤
thumbs.title=à¤²à¤à¥à¤à¤µà¤¿à¤¯à¤¾à¤ à¤¦à¤¿à¤à¤¾à¤à¤
thumbs_label=à¤²à¤à¥ à¤à¤µà¤¿
findbar.title=\u0020à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤®à¥à¤ à¤¢à¥à¤à¤¢à¤¼à¥à¤
findbar_label=à¤¢à¥à¤à¤¢à¥à¤

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=à¤ªà¥à¤·à¥à¤  {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=à¤ªà¥à¤·à¥à¤  {{page}} à¤à¥ à¤²à¤à¥-à¤à¤µà¤¿

# Find panel button title and messages
find_input.title=à¤¢à¥à¤à¤¢à¥à¤
find_input.placeholder=à¤¦à¤¸à¥à¤¤à¤¾à¤µà¥à¤à¤¼ à¤®à¥à¤ à¤à¥à¤à¥à¤...
find_previous.title=à¤µà¤¾à¤à¥à¤¯à¤¾à¤à¤¶ à¤à¥ à¤ªà¤¿à¤à¤²à¥ à¤à¤ªà¤¸à¥à¤¥à¤¿à¤¤à¤¿ à¤¢à¥à¤à¤¢à¤¼à¥à¤
find_previous_label=à¤ªà¤¿à¤à¤²à¤¾
find_next.title=à¤µà¤¾à¤à¥à¤¯à¤¾à¤à¤¶ à¤à¥ à¤à¤à¤²à¥ à¤à¤ªà¤¸à¥à¤¥à¤¿à¤¤à¤¿ à¤¢à¥à¤à¤¢à¤¼à¥à¤
find_next_label=à¤à¤à¤²à¤¾
find_highlight=\u0020à¤¸à¤­à¥ à¤à¤²à¥à¤à¤¿à¤¤ à¤à¤°à¥à¤
find_match_case_label=à¤®à¤¿à¤²à¤¾à¤¨ à¤¸à¥à¤¥à¤¿à¤¤à¤¿
find_entire_word_label=à¤¸à¤à¤ªà¥à¤°à¥à¤£ à¤¶à¤¬à¥à¤¦
find_reached_top=à¤ªà¥à¤·à¥à¤  à¤à¥ à¤à¤ªà¤° à¤ªà¤¹à¥à¤à¤ à¤à¤¯à¤¾, à¤¨à¥à¤à¥ à¤¸à¥ à¤à¤¾à¤°à¥ à¤°à¤à¥à¤
find_reached_bottom=à¤ªà¥à¤·à¥à¤  à¤à¥ à¤¨à¥à¤à¥ à¤®à¥à¤ à¤à¤¾ à¤ªà¤¹à¥à¤à¤à¤¾, à¤à¤ªà¤° à¤¸à¥ à¤à¤¾à¤°à¥
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{total}} à¤®à¥à¤ {{current}} à¤®à¥à¤²
find_match_count[two]={{total}} à¤®à¥à¤ {{current}} à¤®à¥à¤²
find_match_count[few]={{total}} à¤®à¥à¤ {{current}} à¤®à¥à¤²
find_match_count[many]={{total}} à¤®à¥à¤ {{current}} à¤®à¥à¤²
find_match_count[other]={{total}} à¤®à¥à¤ {{current}} à¤®à¥à¤²
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_match_count_limit[one]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_match_count_limit[two]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_match_count_limit[few]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_match_count_limit[many]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_match_count_limit[other]={{limit}} à¤¸à¥ à¤à¤§à¤¿à¤ à¤®à¥à¤²
find_not_found=à¤µà¤¾à¤à¥à¤¯à¤¾à¤à¤¶ à¤¨à¤¹à¥à¤ à¤®à¤¿à¤²à¤¾

# Error panel labels
error_more_info=à¤à¤§à¤¿à¤ à¤¸à¥à¤à¤¨à¤¾
error_less_info=à¤à¤® à¤¸à¥à¤à¤¨à¤¾
error_close=à¤¬à¤à¤¦ à¤à¤°à¥à¤
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=\u0020à¤¸à¤à¤¦à¥à¤¶: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=à¤¸à¥à¤à¥à¤: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=à¤«à¤¼à¤¾à¤à¤²: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=à¤ªà¤à¤à¥à¤¤à¤¿: {{line}}
rendering_error=à¤ªà¥à¤·à¥à¤  à¤°à¥à¤à¤¡à¤°à¤¿à¤à¤ à¤à¥ à¤¦à¥à¤°à¤¾à¤¨ à¤¤à¥à¤°à¥à¤à¤¿ à¤à¤.

# Predefined zoom values
page_scale_width=\u0020à¤ªà¥à¤·à¥à¤  à¤à¥à¤¡à¤¼à¤¾à¤
page_scale_fit=à¤ªà¥à¤·à¥à¤  à¤«à¤¿à¤
page_scale_auto=à¤¸à¥à¤µà¤à¤¾à¤²à¤¿à¤¤ à¤à¥à¤®
page_scale_actual=à¤µà¤¾à¤¸à¥à¤¤à¤µà¤¿à¤ à¤à¤à¤¾à¤°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

loading_error=PDF à¤²à¥à¤¡ à¤à¤°à¤¤à¥ à¤¸à¤®à¤¯ à¤à¤ à¤¤à¥à¤°à¥à¤à¤¿ à¤¹à¥à¤.
invalid_file_error=à¤à¤®à¤¾à¤¨à¥à¤¯ à¤¯à¤¾ à¤­à¥à¤°à¤·à¥à¤ PDF à¤«à¤¼à¤¾à¤à¤².
missing_file_error=\u0020à¤à¤¨à¥à¤ªà¤¸à¥à¤¥à¤¿à¤¤ PDF à¤«à¤¼à¤¾à¤à¤².
unexpected_response_error=à¤à¤ªà¥à¤°à¤¤à¥à¤¯à¤¾à¤¶à¤¿à¤¤ à¤¸à¤°à¥à¤µà¤° à¤ªà¥à¤°à¤¤à¤¿à¤à¥à¤°à¤¿à¤¯à¤¾.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=\u0020[{{type}} Annotation]
password_label=à¤à¤¸ PDF à¤«à¤¼à¤¾à¤à¤² à¤à¥ à¤à¥à¤²à¤¨à¥ à¤à¥ à¤²à¤¿à¤ à¤à¥à¤ªà¤¯à¤¾ à¤à¥à¤à¤¶à¤¬à¥à¤¦ à¤­à¤°à¥à¤.
password_invalid=à¤à¤µà¥à¤§ à¤à¥à¤à¤¶à¤¬à¥à¤¦, à¤à¥à¤ªà¤¯à¤¾ à¤«à¤¿à¤° à¤à¥à¤¶à¤¿à¤¶ à¤à¤°à¥à¤.
password_ok=OK
password_cancel=à¤°à¤¦à¥à¤¦ à¤à¤°à¥à¤

printing_not_supported=à¤à¥à¤¤à¤¾à¤µà¤¨à¥: à¤à¤¸ à¤¬à¥à¤°à¤¾à¤à¤à¤¼à¤° à¤ªà¤° à¤à¤ªà¤¾à¤ à¤ªà¥à¤°à¥ à¤¤à¤°à¤¹ à¤¸à¥ à¤¸à¤®à¤°à¥à¤¥à¤¿à¤¤ à¤¨à¤¹à¥à¤ à¤¹à¥.
printing_not_ready=à¤à¥à¤¤à¤¾à¤µà¤¨à¥: PDF à¤à¤ªà¤¾à¤ à¤à¥ à¤²à¤¿à¤ à¤ªà¥à¤°à¥ à¤¤à¤°à¤¹ à¤¸à¥ à¤²à¥à¤¡ à¤¨à¤¹à¥à¤ à¤¹à¥.
web_fonts_disabled=à¤µà¥à¤¬ à¤«à¥à¤¨à¥à¤à¥à¤¸ à¤¨à¤¿à¤·à¥à¤à¥à¤°à¤¿à¤¯ à¤¹à¥à¤: à¤à¤à¤¤à¤à¤¸à¥à¤¥à¤¾à¤ªà¤¿à¤¤ PDF à¤«à¥à¤¨à¥à¤à¤¸ à¤à¥ à¤à¤ªà¤¯à¥à¤ à¤®à¥à¤ à¤à¤¸à¤®à¤°à¥à¤¥.
