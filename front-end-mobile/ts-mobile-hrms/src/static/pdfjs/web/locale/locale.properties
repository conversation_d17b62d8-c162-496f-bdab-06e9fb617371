[ach]
@import url(ach/viewer.properties)

[af]
@import url(af/viewer.properties)

[an]
@import url(an/viewer.properties)

[ar]
@import url(ar/viewer.properties)

[ast]
@import url(ast/viewer.properties)

[az]
@import url(az/viewer.properties)

[be]
@import url(be/viewer.properties)

[bg]
@import url(bg/viewer.properties)

[bn]
@import url(bn/viewer.properties)

[bo]
@import url(bo/viewer.properties)

[br]
@import url(br/viewer.properties)

[brx]
@import url(brx/viewer.properties)

[bs]
@import url(bs/viewer.properties)

[ca]
@import url(ca/viewer.properties)

[cak]
@import url(cak/viewer.properties)

[ckb]
@import url(ckb/viewer.properties)

[cs]
@import url(cs/viewer.properties)

[cy]
@import url(cy/viewer.properties)

[da]
@import url(da/viewer.properties)

[de]
@import url(de/viewer.properties)

[dsb]
@import url(dsb/viewer.properties)

[el]
@import url(el/viewer.properties)

[en-CA]
@import url(en-CA/viewer.properties)

[en-GB]
@import url(en-GB/viewer.properties)

[en-US]
@import url(en-US/viewer.properties)

[eo]
@import url(eo/viewer.properties)

[es-AR]
@import url(es-AR/viewer.properties)

[es-CL]
@import url(es-CL/viewer.properties)

[es-ES]
@import url(es-ES/viewer.properties)

[es-MX]
@import url(es-MX/viewer.properties)

[et]
@import url(et/viewer.properties)

[eu]
@import url(eu/viewer.properties)

[fa]
@import url(fa/viewer.properties)

[ff]
@import url(ff/viewer.properties)

[fi]
@import url(fi/viewer.properties)

[fr]
@import url(fr/viewer.properties)

[fy-NL]
@import url(fy-NL/viewer.properties)

[ga-IE]
@import url(ga-IE/viewer.properties)

[gd]
@import url(gd/viewer.properties)

[gl]
@import url(gl/viewer.properties)

[gn]
@import url(gn/viewer.properties)

[gu-IN]
@import url(gu-IN/viewer.properties)

[he]
@import url(he/viewer.properties)

[hi-IN]
@import url(hi-IN/viewer.properties)

[hr]
@import url(hr/viewer.properties)

[hsb]
@import url(hsb/viewer.properties)

[hu]
@import url(hu/viewer.properties)

[hy-AM]
@import url(hy-AM/viewer.properties)

[hye]
@import url(hye/viewer.properties)

[ia]
@import url(ia/viewer.properties)

[id]
@import url(id/viewer.properties)

[is]
@import url(is/viewer.properties)

[it]
@import url(it/viewer.properties)

[ja]
@import url(ja/viewer.properties)

[ka]
@import url(ka/viewer.properties)

[kab]
@import url(kab/viewer.properties)

[kk]
@import url(kk/viewer.properties)

[km]
@import url(km/viewer.properties)

[kn]
@import url(kn/viewer.properties)

[ko]
@import url(ko/viewer.properties)

[lij]
@import url(lij/viewer.properties)

[lo]
@import url(lo/viewer.properties)

[lt]
@import url(lt/viewer.properties)

[ltg]
@import url(ltg/viewer.properties)

[lv]
@import url(lv/viewer.properties)

[meh]
@import url(meh/viewer.properties)

[mk]
@import url(mk/viewer.properties)

[mr]
@import url(mr/viewer.properties)

[ms]
@import url(ms/viewer.properties)

[my]
@import url(my/viewer.properties)

[nb-NO]
@import url(nb-NO/viewer.properties)

[ne-NP]
@import url(ne-NP/viewer.properties)

[nl]
@import url(nl/viewer.properties)

[nn-NO]
@import url(nn-NO/viewer.properties)

[oc]
@import url(oc/viewer.properties)

[pa-IN]
@import url(pa-IN/viewer.properties)

[pl]
@import url(pl/viewer.properties)

[pt-BR]
@import url(pt-BR/viewer.properties)

[pt-PT]
@import url(pt-PT/viewer.properties)

[rm]
@import url(rm/viewer.properties)

[ro]
@import url(ro/viewer.properties)

[ru]
@import url(ru/viewer.properties)

[sat]
@import url(sat/viewer.properties)

[sc]
@import url(sc/viewer.properties)

[scn]
@import url(scn/viewer.properties)

[sco]
@import url(sco/viewer.properties)

[si]
@import url(si/viewer.properties)

[sk]
@import url(sk/viewer.properties)

[sl]
@import url(sl/viewer.properties)

[son]
@import url(son/viewer.properties)

[sq]
@import url(sq/viewer.properties)

[sr]
@import url(sr/viewer.properties)

[sv-SE]
@import url(sv-SE/viewer.properties)

[szl]
@import url(szl/viewer.properties)

[ta]
@import url(ta/viewer.properties)

[te]
@import url(te/viewer.properties)

[tg]
@import url(tg/viewer.properties)

[th]
@import url(th/viewer.properties)

[tl]
@import url(tl/viewer.properties)

[tr]
@import url(tr/viewer.properties)

[trs]
@import url(trs/viewer.properties)

[uk]
@import url(uk/viewer.properties)

[ur]
@import url(ur/viewer.properties)

[uz]
@import url(uz/viewer.properties)

[vi]
@import url(vi/viewer.properties)

[wo]
@import url(wo/viewer.properties)

[xh]
@import url(xh/viewer.properties)

[zh-CN]
@import url(zh-CN/viewer.properties)

[zh-TW]
@import url(zh-TW/viewer.properties)

