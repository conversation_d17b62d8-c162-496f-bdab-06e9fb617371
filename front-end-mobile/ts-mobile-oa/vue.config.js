const { name } = require('./package.json');

process.env.VUE_APP_PORT = 6051; //设置端口号

process.env.VUE_APP_NAME = name; //项目名称 很重要 要写

process.env.VUE_APP_CONTAINER = `qiankun-uniapp${uuid(16, 16)}`;
process.env.VUE_APP_DOMPARENT = {};
module.exports = {
  chainWebpack: config => {
    config.output.filename('[name].[hash].js').end();
    config.plugins.delete('preload');
    config.plugins.delete('prefetch');
    config.resolve.symlinks(false);
    config.module
      .rule('images')
      .use('url-loader')
      .loader('url-loader')
      .options({})
      .end();
    config.module
      .rule('fonts')
      .test(/.(ttf|otf|eot|woff|woff2)$/)
      .use('url-loader')
      .loader('url-loader')
      .tap(options => {
        options = {
          // limit: 10000,
          name: '/static/fonts/[name].[ext]'
        };
        return options;
      })
      .end();
  },

  configureWebpack: {
    output: {
      library: `${name}`,
      libraryTarget: 'umd',
      jsonpFunction: `webpackJsonp_${name}`
    }
  },
  devServer: {
    port: process.env.VUE_APP_PORT,
    headers: {
      'Access-Control-Allow-Origin': '*' //开发模式下给微前端服务使用
    },
    disableHostCheck: true,
    historyApiFallback: true,
    host: '0.0.0.0', //用于找不到界面就返回默认首页
    proxy: {
      '/dpc': {
        target: 'http://*************:9078', //目标接口域名

        //"target" : "http://yuechang.vaiwan.com", //目标接口域名
        changeOrigin: true, //是否跨域
        secure: false, // 设置支持https协议的代理
        pathRewrite: {
          '^/dpc': ''
        }
      }
    },
    https: false
  }
};

function uuid(len, radix) {
  var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split(
    ''
  );
  var uuid = [],
    i;
  radix = radix || chars.length;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
  } else {
    // rfc4122, version 4 form
    var r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
    uuid[14] = '4';

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }

  return uuid.join('');
}
