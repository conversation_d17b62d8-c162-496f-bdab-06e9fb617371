<template>
  <u-popup
    v-model="show"
    :closeable="false"
    mode="bottom"
    :safe-area-inset-bottom="true"
    height="50%"
  >
    <view class="action-content">
      <view class="quit-btn" @click="show = false">取消</view>
      <view class="input-content">
        <u-search
          shape="square"
          v-model="searchInput"
          :show-action="false"
          placeholder="输入关键字搜索"
          @input="handleSearch"
          @clear="handleSearchClear"
        ></u-search>
      </view>

      <view class="search-btn" @click="handleSearchSub">确定</view>
    </view>
    <view class="list-content">
      <view
        v-for="(item, index) of renderOptionList"
        :key="index"
        class="list-item"
        @click="handleSelectClick(item)"
      >
        <view>
          {{ item.name }}
        </view>
        <u-icon v-if="selectItem.id == item.id" name="checkmark"></u-icon>
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  data() {
    return {
      show: false,
      renderOptionList: [],
      localOptionList: [],
      callback: null,

      searchInput: '',
      selectItem: {}
    };
  },
  methods: {
    open(data = {}) {
      this.show = true;
      this.renderOptionList = data.optionList || [];
      this.localOptionList = data.optionList || [];
      this.callback = data.callback || null;

      if (data.defaultVal) {
        const find =
          this.localOptionList.find(item => item.id === data.defaultVal) || {};
        this.selectItem = find || {};
      } else {
        this.selectItem = {};
      }
    },
    handleSearch() {
      if (this.searchInput && this.searchInput.length > 0) {
        this.renderOptionList = this.localOptionList.filter(item => {
          const label = item.name.split('-')[0];
          return label.indexOf(this.searchInput) !== -1;
        });
      } else {
        this.renderOptionList = this.localOptionList;
      }
    },
    handleSearchClear() {
      this.renderOptionList = this.localOptionList;
    },
    handleSearchSub() {
      if (this.callback) {
        this.callback(this.selectItem);
        this.show = false;
        return;
      }
    },
    handleSelectClick(item) {
      this.selectItem = item;
    }
  }
};
</script>

<style lang="scss" scoped>
.input-content,
.action-content {
  display: flex;
  align-items: center;
}
.action-content {
  position: fixed;
  top: 0;
  z-index: 9;
  background-color: #fff;
  width: 100vw;
  box-shadow: 0px 1px 0px 0px #dddddd;

  .quit-btn {
    color: $u-tips-color;
    font-size: 32rpx;
    line-height: 44px;
    margin: 0 16px;
  }
  .input-content {
    flex: 1;
    background-color: #f8f8f8;
    height: 60rpx;
    border-radius: 6px;
    .u-icon {
      color: $u-tips-color;
      margin: 0 8px;
    }
  }
  .search-btn {
    color: $u-type-primary;
    font-size: 32rpx;
    line-height: 44px;
    margin: 0 16px;
  }
}
.list-content {
  padding-top: 44px;
  .list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 16px;
    font-size: 32rpx;
    color: $u-main-color;
    line-height: 44rpx;
    height: 44px;
    border-bottom: 1px solid #e6e6e6;
    .u-icon {
      color: $u-type-primary;
    }
  }
}
</style>
