<template>
  <view class="process-history-list">
    <view
      v-for="(item, index) in processHistoryList"
      :key="index"
      :class="item.isCurrent ? 'current-process' : ''"
    >
      <view class="process-history-item">
        <view class="process-history-item_info">
          <view
            class="process-history-item_name"
            v-if="item.wfStepName != null"
          >
            {{ item.wfStepName }}--抄送
          </view>
          <view class="process-history-item_name" v-else>
            抄送
          </view>
          <view class="process-history-item_info-top">
            <text class="process-history-item_operation-user">
              操作人：{{ item.createUserName }}
            </text>
          </view>
          <view class="process-history-item_info-top">
            <text class="process-history-item_operation-user">
              抄送人：{{ item.copytoUserName }}
              <span
                class="read-status"
                :class="{ 'is-readed': item.readStatus == 1 }"
              >
                （{{ item.readStatus == 1 ? '已读' : '未读' }}）
              </span>
            </text>
          </view>
          <view class="process-history-item_info-top">
            <text class="process-history-item_operation-user">
              抄送时间：{{ item.createDate }}
            </text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * 表单流程信息
 * @description 业务组件
 * @property {Array} processHistoryList 流程数组
 */
export default {
  name: 'DataProcessHistory',
  props: {
    processHistoryList: {
      type: Array
    }
  }
};
</script>

<style scoped lang="scss">
.process-history-list {
  list-style-type: none;
  border-left: 4rpx dashed #ddd;
  padding: 0px;
  .current-process {
    &::after {
      background: #005bac !important;
    }
    .process-history-item_name,
    .process-history-item_operation-user,
    .process-history-item_content {
      color: #005bac !important;
    }
  }
  .process-history-item {
    position: relative;
    &::after {
      content: '';
      position: absolute;
      top: 10rpx;
      left: 0;
      transform: translateX(-18rpx);
      width: 30rpx;
      height: 30rpx;
      border-radius: 100%;
      background: #ddd;
    }
    .process-history-item_info {
      margin: 20rpx 0 20rpx 30rpx;
    }
    .process-history-item_assent {
      color: #3aad73;
      font-size: 28rpx;
    }
    .process-history-item_dissent {
      color: #f59a23;
      font-size: 28rpx;
    }
    .process-history-item_name,
    .process-history-item_operation-user {
      font-size: 28rpx;
      color: #666;
      padding-right: 20rpx;
    }
    .process-history-item_name {
      font-weight: bold;
    }
    .process-history-item_time {
      font-size: 24rpx;
      color: #999;
      float: right;
    }
    .process-history-item_content {
      font-size: 28rpx;
      color: #666;
    }
  }
}
.read-status {
  color: #f56c6c;
  &.is-readed {
    color: #67c23a;
  }
}
</style>
