<template xlang="wxml">
  <view class="tki-tree">
    <scroll-view class="tki-tree-view" :scroll-y="true">
      <block v-for="(item, index) in treeList" :key="index">
        <view
          class="tki-tree-item"
          :style="[
            {
              paddingLeft: item.rank * 15 + 'px',
              zIndex: item.rank * -1 + 50
            }
          ]"
          :class="{
            show: item.show,
            last: item.lastRank,
            showchild: item.showChild,
            open: item.open
          }"
        >
          <view class="tki-tree-label" @tap.stop="_treeItemTap(item, index)">
            <image
              class="tki-tree-icon"
              :src="
                item.lastRank
                  ? lastIcon
                  : item.showChild
                  ? currentIcon
                  : defaultIcon
              "
            ></image>
            {{ item.name }}
          </view>
          <view
            class="tki-tree-check"
            @tap.stop="_treeItemSelect(item, index)"
            v-if="selectParent ? true : item.lastRank"
          >
            <!-- <view class="tki-tree-check-yes" v-if="item.checked" :class="{'radio':!multiple}" :style="{'border-color':confirmColor}">
							<view class="tki-tree-check-yes-b" :style="{'background-color':confirmColor}"></view>
						</view>
						<view class="tki-tree-check-no" v-else :class="{'radio':!multiple}" :style="{'border-color':confirmColor}"></view> -->
            <uni-icons
              :type="item.checked ? 'checkbox-filled' : 'circle'"
              :color="item.checked ? '#005BAC' : '#aaa'"
              size="48"
            />
            <image
              class="iconImg"
              style="width: 40px;height: 40px;"
              v-if="item.empHeadImg ? true : false"
              :src="$config.BASE_HOST + item.empHeadImg"
              mode="aspectFill"
            ></image>
            <view
              v-else
              class="iconImg"
              :class="item.empSex == 1 ? 'sexMan' : 'sexWoman'"
            >
              {{ item.empFirstName }}
            </view>
            <view class="userInfo">
              <text>{{ item.empName }}</text>
              <text
                >{{ item.empDeptName }}&nbsp;&nbsp;{{ item.empDutyName }}</text
              >
            </view>
          </view>
        </view>
      </block>
    </scroll-view>
  </view>
</template>

<script>
export default {
  name: 'tki-tree',
  props: {
    range: {
      type: Array,
      default: function() {
        return [];
      }
    },
    idKey: {
      type: String,
      default: 'id'
    },
    rangeKey: {
      type: String,
      default: 'name'
    },
    title: {
      type: String,
      default: ''
    },
    multiple: {
      // 是否可以多选
      type: Boolean,
      default: false
      // default: true
    },
    selectParent: {
      //是否可以选父级
      type: Boolean,
      default: false
    },
    foldAll: {
      //折叠时关闭所有已经打开的子集，再次打开时需要一级一级打开
      type: Boolean,
      default: false
    },
    currentIcon: {
      // 展开时候的ic
      type: String,
      default: ''
    },
    defaultIcon: {
      // 折叠时候的ic
      type: String,
      default: ''
    },
    lastIcon: {
      // 没有子集的ic
      type: String,
      default: ''
    }
  },
  data() {
    return {
      treeList: [],
      selectIndex: -1
    };
  },
  computed: {},
  methods: {
    //扁平化树结构
    _renderTreeList(list = [], rank = 0, parentId = [], parents = []) {
      let _self = this;
      list.forEach(item => {
        _self.treeList.push({
          id: item[_self.idKey],
          name: item[_self.rangeKey],
          source: item,
          parentId, // 父级id数组
          parents, // 父级id数组
          rank, // 层级
          showChild: false, //子级是否显示
          open: false, //是否打开
          show: rank === 0, // 自身是否显示
          hideArr: [],
          orChecked: item.checked ? item.checked : false,
          checked: item.checked ? item.checked : false
        });
        if (Array.isArray(item.children) && item.children.length > 0) {
          let parentid = [...parentId],
            parentArr = [...parents],
            childrenid = [...childrenid];
          delete parentArr.children;
          parentid.push(item[_self.idKey]);
          parentArr.push({
            [_self.idKey]: item[_self.idKey],
            [_self.rangeKey]: item[_self.rangeKey]
          });
          _self._renderTreeList(item.children, rank + 1, parentid, parentArr);
        } else {
          _self.treeList[_self.treeList.length - 1].lastRank = true;
        }
      });
    },
    // 处理默认选择
    _defaultSelect() {
      let _self = this;
      _self.treeList.forEach((v, i) => {
        if (v.checked) {
          _self.treeList.forEach((v2, i2) => {
            if (v.parentId.toString().indexOf(v2.parentId.toString()) >= 0) {
              v2.show = true;
              if (v.parentId.includes(v2.id)) {
                v2.showChild = true;
                v2.open = true;
              }
            }
          });
        }
      });
    },
    // 点击
    _treeItemTap(item, index) {
      if (item.lastRank === true) {
        //点击最后一级时触发事件
        this.treeList[index].checked = !this.treeList[index].checked;
        this._fixMultiple(index);
        return;
      }
      let list = this.treeList;
      let id = item.id;
      item.showChild = !item.showChild;
      item.open = item.showChild ? true : !item.open;
      list.forEach((childItem, i) => {
        if (item.showChild === false) {
          //隐藏所有子级
          if (!childItem.parentId.includes(id)) {
            return;
          }
          if (!this.foldAll) {
            if (childItem.lastRank !== true && !childItem.open) {
              childItem.showChild = false;
            }
            // 为隐藏的内容添加一个标记
            if (childItem.show) {
              childItem.hideArr[item.rank] = id;
            }
          } else {
            if (childItem.lastRank !== true) {
              childItem.showChild = false;
            }
          }
          childItem.show = false;
        } else {
          // 打开子集
          if (childItem.parentId[childItem.parentId.length - 1] === id) {
            childItem.show = true;
          }
          // 打开被隐藏的子集
          if (childItem.parentId.includes(id) && !this.foldAll) {
            if (childItem.hideArr[item.rank] === id) {
              childItem.show = true;
              if (childItem.open && childItem.showChild) {
                childItem.showChild = true;
              } else {
                childItem.showChild = false;
              }
              childItem.hideArr[item.rank] = null;
            }
          }
        }
      });
    },
    _treeItemSelect(item, index) {
      this.treeList[index].checked = !this.treeList[index].checked;
      this._fixMultiple(index);
    },
    // 处理单选多选
    _fixMultiple(index) {
      if (!this.multiple) {
        // 如果是单选
        this.treeList.forEach((v, i) => {
          if (i != index) {
            this.treeList[i].checked = false;
          } else {
            this.treeList[i].checked = true;
          }
        });
      }
    },
    // 重置数据
    _reTreeList() {
      this.treeList.forEach((v, i) => {
        this.treeList[i].checked = v.orChecked;
      });
    },
    _initTree(range = this.range) {
      this.treeList = [];
      this._renderTreeList(range);
      this.$nextTick(() => {
        this._defaultSelect(range);
      });
    }
  },
  watch: {
    range(list) {
      this._initTree(list);
    }
    // multiple() {
    // 	if (this.range.length) {
    // 		this._reTreeList();
    // 	}
    // },
    // selectParent() {
    // 	if (this.range.length) {
    // 		this._reTreeList();
    // 	}
    // },
  }
  // mounted() {
  // 	this._initTree();
  // }
};
</script>

<style scoped>
.tki-tree-view {
  height: 100%;
  overflow: hidden;
}
.tki-tree-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 26rpx;
  color: #757575;
  line-height: 1;
  height: 0;
  opacity: 0;
  transition: 0.2s;
  position: relative;
  overflow: hidden;
}
.tki-tree-item.show {
  height: 80rpx;
  opacity: 1;
}
.tki-tree-item.showchild:before {
  transform: rotate(90deg);
}
.tki-tree-item.last:before {
  opacity: 0;
}
.tki-tree-icon {
  width: 26rpx;
  height: 26rpx;
  margin-right: 8rpx;
}
.tki-tree-label {
  flex: 1;
  display: flex;
  align-items: center;
  height: 100%;
  line-height: 1.2;
}
.tki-tree-check {
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.tki-tree-check-yes,
.tki-tree-check-no {
  width: 20px;
  height: 20px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  border-top-width: 1rpx;
  border-left-width: 1rpx;
  border-bottom-width: 1rpx;
  border-right-width: 1rpx;
  border-style: solid;
  border-color: #07bb07;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
}
.tki-tree-check-yes-b {
  width: 12px;
  height: 12px;
  border-top-left-radius: 20%;
  border-top-right-radius: 20%;
  border-bottom-right-radius: 20%;
  border-bottom-left-radius: 20%;
  background-color: #07bb07;
}
.tki-tree-check .radio {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.tki-tree-check .radio .tki-tree-check-yes-b {
  border-top-left-radius: 50%;
  border-top-right-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.hover-c {
  opacity: 0.6;
}
</style>
