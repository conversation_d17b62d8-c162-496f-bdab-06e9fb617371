<template>
  <view
    :class="{
      'uni-collapse-cell--disabled': disabled,
      'uni-collapse-cell--notdisabled': !disabled,
      'uni-collapse-cell--open': isOpen,
      'uni-collapse-cell--hide': !isOpen
    }"
    class="uni-collapse-cell"
  >
    <view class="uni-collapse-cell__title" @click="onClick">
      <image v-if="thumb" :src="thumb" class="uni-collapse-cell__title-img" />
      <text
        v-if="customIcon"
        class="oa-icon"
        :class="customIcon"
        :style="{ color: customIconColor }"
      ></text>
      <text
        class="uni-collapse-cell__title-text"
        :style="collapseTextStyle"
        v-if="title"
      >
        {{ title }}
      </text>
      <view
        lass="uni-collapse-cell__title-text"
        :style="collapseTextStyle"
        v-if="title == ''"
      >
        <slot name="titletext" />
      </view>
      <view class="uni-collapse-cell__title-subtext">
        <slot name="subtext" />
      </view>
      <!-- #ifdef MP-ALIPAY -->
      <view
        :class="{
          'uni-collapse-cell__title-arrow-active': isOpen,
          'uni-collapse-cell--animation': showAnimation === true
        }"
        class="uni-collapse-cell__title-arrow"
      >
        <uni-icons color="#bbb" size="40" type="arrowdown" />
      </view>
      <!-- #endif -->
      <!-- #ifndef MP-ALIPAY -->
      <uni-icons
        :class="{
          'uni-collapse-cell__title-arrow-active': isOpen,
          'uni-collapse-cell--animation': showAnimation === true
        }"
        class="uni-collapse-cell__title-arrow"
        color="#bbb"
        size="40"
        type="arrowdown"
      />
      <!-- #endif -->
    </view>
    <view
      :class="{ 'uni-collapse-cell__content--hide': !isOpen }"
      class="uni-collapse-cell__content"
    >
      <view
        :class="{ 'uni-collapse-cell--animation': showAnimation === true }"
        class="uni-collapse-cell__wrapper"
        :style="{
          transform: isOpen ? 'translateY(0)' : 'translateY(-50%)',
          '-webkit-transform': isOpen ? 'translateY(0)' : 'translateY(-50%)'
        }"
      >
        <slot />
      </view>
    </view>
  </view>
</template>

<script>
import uniIcons from '../uni-icons/uni-icons.vue';
/**
 * CollapseItem 折叠面板子组件
 * @description 折叠面板子组件
 * @property {String} title 标题文字
 * @property {String} thumb 标题左侧缩略图
 * @property {Boolean} disabled = [true|false] 是否展开面板
 * @property {Boolean} showAnimation = [true|false] 开启动画
 */
export default {
  name: 'UniCollapseItem',
  components: {
    uniIcons
  },
  props: {
    title: {
      // 列表标题
      type: String,
      default: ''
    },
    name: {
      // 唯一标识符
      type: [Number, String],
      default: 0
    },
    disabled: {
      // 是否禁用
      type: Boolean,
      default: false
    },
    showAnimation: {
      // 是否显示动画
      type: Boolean,
      default: false
    },
    open: {
      // 是否展开
      type: Boolean,
      default: false
    },
    thumb: {
      // 缩略图
      type: String,
      default: ''
    },
    customIcon: {
      // 自定义图标
      type: String,
      default: ''
    },
    customIconColor: {
      // 自定义图标
      type: String,
      default: ''
    },
    collapseTextStyle: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  data() {
    return {
      isOpen: false
    };
  },
  watch: {
    open(val) {
      this.isOpen = val;
    }
  },
  inject: ['collapse'],
  created() {
    this.isOpen = this.open;
    this.nameSync = this.name ? this.name : this.collapse.childrens.length;
    this.collapse.childrens.push(this);
    if (String(this.collapse.accordion) === 'true') {
      if (this.isOpen) {
        let lastEl = this.collapse.childrens[
          this.collapse.childrens.length - 2
        ];
        if (lastEl) {
          this.collapse.childrens[
            this.collapse.childrens.length - 2
          ].isOpen = false;
        }
      }
    }
  },
  methods: {
    onClick() {
      if (this.disabled) {
        return;
      }
      if (String(this.collapse.accordion) === 'true') {
        this.collapse.childrens.forEach(vm => {
          if (vm === this) {
            return;
          }
          vm.isOpen = false;
        });
      }
      this.isOpen = !this.isOpen;
      this.collapse.onChange && this.collapse.onChange();
      this.$forceUpdate();
    }
  }
};
</script>

<style scoped>
.uni-collapse-cell {
  flex-direction: column;
  border-color: #e5e5e5;
  border-bottom-width: 1px;
  border-bottom-style: solid;
}

.uni-collapse-cell--hover {
  background-color: #f1f1f1;
}

.uni-collapse-cell--open {
  background-color: #f1f1f1;
}

.uni-collapse-cell--disabled {
  background-color: #f1f1f1;
  /* opacity: 0.3;
 */
}

.uni-collapse-cell--hide {
  height: 48px;
}

.uni-collapse-cell--animation {
  /* transition: transform 0.3s ease;
 */
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}

.uni-collapse-cell__title {
  padding: 12px 12px;
  position: relative;
  /* #ifndef APP-NVUE */
  display: flex;
  width: 100%;
  box-sizing: border-box;
  /* #endif */
  height: 48px;
  line-height: 24px;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.uni-collapse-cell__title:active {
  background-color: #f1f1f1;
}

.uni-collapse-cell__title-img {
  height: 52rpx;
  width: 52rpx;
  margin-right: 10px;
}
.oa-icon {
  margin-right: 10px;
  font-size: 52rpx;
}

.uni-collapse-cell__title-arrow {
  width: 20px;
  height: 20px;
  transform: rotate(0deg);
  transform-origin: center center;
}

.uni-collapse-cell__title-arrow-active {
  transform: rotate(180deg);
}

.uni-collapse-cell__title-text {
  flex: 1;
  font-size: 28rpx;
  /* #ifndef APP-NVUE */
  white-space: nowrap;
  color: inherit;
  /* #endif */
  /* #ifdef APP-NVUE */
  lines: 1;
  /* #endif */
  overflow: hidden;
  text-overflow: ellipsis;
}

.uni-collapse-cell__content {
  overflow: hidden;
}

.uni-collapse-cell__wrapper {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: column;
}

.uni-collapse-cell__content--hide {
  height: 0px;
  line-height: 0px;
}
</style>
