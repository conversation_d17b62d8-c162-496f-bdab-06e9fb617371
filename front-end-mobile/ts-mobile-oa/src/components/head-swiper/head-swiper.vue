<template>
  <view ref="tabHeader" class="swiper_head">
    <view
      v-for="(tab, index) in tabs"
      :key="tab[valueKey]"
      class="uni-tab-item"
      @click="handleTabClick(tab, index)"
      :class="value == tab[valueKey] ? 'uni-tab-item-title-active' : ''"
    >
      <text
        :class="{
          'uni-tab-item-title': true,
          [tab.className]: !!tab.className
        }"
      >
        {{ tab[labelKey] }}
      </text>
    </view>
    <view ref="activeLine" class="text-line"></view>
  </view>
</template>

<script>
export default {
  name: 'head-swiper',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {},
    tabs: {
      type: Array,
      default: () => []
    },
    valueKey: {
      type: String,
      default: () => 'value'
    },
    labelKey: {
      type: String,
      default: () => 'label'
    }
  },
  methods: {
    handleComputeDomStyle(index) {
      let header = this.$refs.tabHeader.$el,
        { offsetWidth: parentWidth } = header,
        activeNode = header.querySelector(
          `.uni-tab-item:nth-child(${index + 1})`
        ),
        offsetLeft = activeNode.offsetLeft,
        line = this.$refs.activeLine.$el,
        left = activeNode.offsetWidth / 2 + offsetLeft - line.offsetWidth / 2,
        minOffsetLeft = parentWidth / 2 - activeNode.offsetWidth;
      line.style.transform = `translate(${left}px)`;

      if (offsetLeft + activeNode.offsetWidth / 2 > minOffsetLeft) {
        header.scrollTo({
          left: offsetLeft + activeNode.offsetWidth / 2 - parentWidth / 2,
          behavior: 'smooth'
        });
      }
    },
    handleTabClick(tab, index) {
      this.handleComputeDomStyle(index);
      this.switchTab(tab, Number(index));
    },
    async switchTab(tab, index) {
      if (this.tabIndex === index) {
        return;
      }
      this.$emit('input', tab[this.valueKey]);
      this.$emit('change', tab, index);
    }
  },
  watch: {
    tabs: {
      handler(val = []) {
        if (val.length && !this.value) {
          this.$nextTick(() => this.handleTabClick(val[0], 0));
        }
      },
      deep: true,
      immediate: true
    },
    value(val) {
      let index = this.tabs.findIndex(item => item[this.valueKey] == val);
      index >= 0 && this.handleComputeDomStyle(index);
    }
  }
};
</script>

<style lang="scss" scoped>
.swiper_head {
  display: flex;
  background-color: #ffffff;
  overflow-y: hidden;
  overflow-x: auto;
  line-height: 0;
  position: relative;
  .uni-tab-item {
    display: inline-block;
    flex-wrap: nowrap;
    flex: 1;
    height: 42px;
    line-height: 42px;
    font-size: 14px;
    box-sizing: border-box;
    text-align: center;
    padding: 0 8px;
    .uni-tab-item-title,
    .uni-tab-item-num {
      color: #666;
      height: 100%;
      font-size: 14px;
      flex-wrap: nowrap;
      box-sizing: border-box;
      /* #ifndef APP-PLUS */
      white-space: nowrap;
      /* #endif */
    }
    &.uni-tab-item-title-active .uni-tab-item-title {
      color: $theme-color;
    }
  }
  .text-line {
    position: absolute;
    bottom: 0;
    border-radius: 100px;
    width: 30px;
    transition-duration: 300ms;
    height: 2px;
    background-color: $theme-color;
  }
}
</style>
