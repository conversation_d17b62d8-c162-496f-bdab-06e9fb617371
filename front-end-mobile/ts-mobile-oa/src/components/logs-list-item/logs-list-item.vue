<template>
  <view class="logs-item">
    <view
      class="logs-item-content"
      :class="isFirstChild && showCurrent ? 'log-list-item--first' : ''"
    >
      <view class="logs-item-content-text">{{ node }}</view>
      <view class="logs-item-content-text">{{ content }}</view>
      <view class="logs-item-content-text">{{ remark }}</view>
      <slot></slot>
    </view>
  </view>
</template>

<script>
/**
 * 表单流程信息
 * @description 业务组件
 * @property {Array} logsListItem 流程数组
 */
export default {
  name: 'LogsListItem',
  props: {
    node: {
      type: String,
      default: ''
    },
    content: {
      type: String,
      default: ''
    },
    showCurrent: {
      type: [<PERSON><PERSON><PERSON>, String],
      default: false
    },
    remark: {
      type: String,
      default: ''
    }
  },
  inject: ['list'],
  data() {
    return {
      isFirstChild: false
    };
  },
  mounted() {
    if (!this.list.firstChildAppend) {
      this.list.firstChildAppend = true;
      this.isFirstChild = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.logs-item {
  position: relative;
  &::after {
    content: '';
    position: absolute;
    top: 10rpx;
    left: 0;
    transform: translateX(-18rpx);
    width: 30rpx;
    height: 30rpx;
    border-radius: 100%;
    background: #ddd;
  }
}
.logs-item-content {
  margin: 20rpx 0 20rpx 30rpx;
}
.log-list-item--first .logs-item-content-text {
  color: $theme-color;
}
.logs-item-content-text {
  font-size: 28rpx;
  color: #333;
}
</style>
