<template>
  <view class="form-file-container">
    <view class="file-title">
      <view class="file-title-text">{{ title }}</view>
      <view class="file-add-icon" @click="addFiles">
        <text :class="addIcon" :style="addIconStyle"></text>
        <slot name="addIcon"></slot>
      </view>
    </view>
    <view class="file-content">
      <slot />
    </view>
  </view>
</template>

<script>
import { chooseImage, chooseFile } from '../../common/js/uploadImg.js';
/**
 * FormFile 附件组件
 * @description 表单附件
 * @property {String} title 标题
 * @property {String}  addIcon 添加图标class
 * @property {String} addIconStyle 添加图标样式
 */
export default {
  name: 'FormFile',
  emits: ['click'],
  props: {
    title: {
      type: String,
      default: ''
    },
    addIcon: {
      type: String,
      default: 'oa-icon oa-icon-tupiantianjia'
    },
    addIconStyle: {
      type: String,
      default: ''
    },
    uploadPath: {
      type: String,
      default: ''
    },
    uploadParam: {
      type: Object,
      default: function() {
        return {};
      }
    }
  },
  data() {
    return {
      paramStrArr: []
    };
  },
  created() {
    var paramStrArr = [];
    for (var i in this.uploadParam) {
      this.paramStrArr.push(`${i}=${this.uploadParam[i]}`);
    }
  },
  methods: {
    addFiles() {
      let _self = this,
        paramStr = _self.paramStrArr.join('&');
      chooseFile({
        limitNum: 9, //数量
        uploadFileUrl: `${_self.$config.BASE_HOST}/${_self.uploadPath}&${paramStr}`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let datas = JSON.parse(res);
          if (datas.statusCode == 200) this.$emit('click', datas.object);
          else _self.$common.toast(datas.message);
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.file-title {
  display: flex;
  align-items: center;
  width: 100%;
}
.file-title-text {
  font-size: 32rpx;
  flex: 1;
}
.file-add-icon {
  font-size: 56rpx;
  color: #bbb;
  line-height: 56rpx;
}
</style>
