<template>
  <view class="logs-list">
    <slot></slot>
  </view>
</template>
<script>
/**
 * 操作日志列表组件
 * @description 业务组件
 */
export default {
  name: 'LogsList',
  provide() {
    return {
      list: this
    };
  },
  created() {
    this.firstChildAppend = false;
  }
};
</script>

<style scoped lang="scss">
.logs-list {
  list-style-type: none;
  border-left: 4rpx dashed #ddd;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  background-color: $uni-bg-color;
  position: relative;
  flex-direction: column;
}
</style>
