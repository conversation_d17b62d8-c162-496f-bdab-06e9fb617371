<template>
  <view class="uni-grid-wrap">
    <view
      :id="elId"
      ref="uni-grid"
      class="uni-grid"
      :class="{ 'uni-grid--border': showBorder }"
      :style="{
        'border-left-style': 'solid',
        'border-left-color': borderColor,
        'border-left-width': showBorder && flexWrap != 'nowrap' ? '1px' : 0,
        'flex-wrap': flexWrap
      }"
    >
      <slot />
    </view>
  </view>
</template>

<script>
// #ifdef APP-NVUE
const dom = uni.requireNativePlugin('dom');
// #endif

/**
 * Grid 宫格
 * @description 宫格组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=27
 * @property {Number} column 每列显示个数
 * @property {String} borderColor 边框颜色
 * @property {Boolean} showBorder 是否显示边框
 * @property {Boolean} square 是否方形显示
 * @property {Boolean} Boolean 点击背景是否高亮
 * @event {Function} change 点击 grid 触发，e={detail:{index:0}}，index 为当前点击 gird 下标
 */
export default {
  name: 'UniGrid',
  props: {
    // 每列显示个数
    column: {
      type: Number,
      default: 3
    },
    // 是否显示边框
    showBorder: {
      type: Boolean,
      default: true
    },
    // 边框颜色
    borderColor: {
      type: String,
      default: '#e5e5e5'
    },
    // 是否正方形显示,默认为 true
    square: {
      type: Boolean,
      default: true
    },
    highlight: {
      type: Boolean,
      default: true
    },
    //自定义宽度
    tWidth: {
      type: String,
      default: ''
    },
    tHeight: {
      type: String,
      default: ''
    },
    //
    flexWrap: {
      type: String,
      default: 'wrap'
    }
  },
  provide() {
    return {
      grid: this
    };
  },
  data() {
    const elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`;
    return {
      elId,
      width: 0,
      height: 0
    };
  },
  created() {
    this.children = [];
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      setTimeout(() => {
        this._getSize((width, height) => {
          this.children.forEach((item, index) => {
            item.width = width;
            item.height = height;
          });
        });
      }, 50);
    },
    change(e) {
      this.$emit('change', e);
    },
    _getSize(fn) {
      // #ifndef APP-NVUE
      uni
        .createSelectorQuery()
        .in(this)
        .select(`#${this.elId}`)
        .boundingClientRect()
        .exec(ret => {
          this.width = this.tWidth
            ? this.tWidth
            : parseInt((ret[0].width - 1) / this.column) + 'px';
          if (this.square) {
            this.height = this.width;
          } else if (!this.square && this.tHeight) {
            this.height = this.tHeight;
          } else {
            this.height = '';
          }
          fn(this.width, this.height);
        });
      // #endif
      // #ifdef APP-NVUE
      dom.getComponentRect(this.$refs['uni-grid'], ret => {
        this.width = parseInt((ret.size.width - 1) / this.column) + 'px';
        fn(this.width);
      });
      // #endif
    }
  }
};
</script>

<style scoped>
.uni-grid-wrap {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  flex-direction: column;
  /* #ifdef H5 */
  width: 100%;
  /* #endif */
}

.uni-grid {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  /* flex: 1;
 */
  flex-direction: row;
  flex-wrap: wrap;
}

.uni-grid--border {
  border-left-color: #e5e5e5;
  border-left-style: solid;
  border-left-width: 1px;
}
</style>
