<template>
  <uni-popup
    class="data-popup-select-box"
    type="bottom"
    popup-choice="radio"
    top-height="45%"
    ref="popup"
  >
    <view v-if="title" class="data-popup-header" :style="titleStyle">
      {{ title }}
    </view>
    <view class="scroll-list">
      <data-check
        mode="list"
        iconPosition="right"
        :multiple="multiple"
        v-model="selectValue"
        :checkList="selectList"
        selectedColor="#005BAC"
        :fontSize="fontSize"
        @change="onChange"
      ></data-check>
    </view>
  </uni-popup>
</template>

<script>
export default {
  name: 'data-popup-select',
  props: {
    title: {
      type: String,
      default: ''
    },
    titleStyle: {
      type: Object,
      default() {
        return {
          color: '#333',
          fontSize: '32rpx'
        };
      }
    },
    multiple: {
      type: Boolean,
      default: false
    },
    value: {
      type: [Array, String, Number],
      default() {
        return '';
      }
    },
    fontSize: {
      type: [Number, String],
      default: '16'
    },
    selectList: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      selectValue: null
    };
  },
  watch: {
    value: {
      async handler(val) {
        this.selectValue = val;
      }
    }
  },
  methods: {
    open() {
      this.$refs['popup'].open();
    },
    onChange(e) {
      this.$emit('change', e);
      this.$refs['popup'].close();
    }
  }
};
</script>

<style lang="scss" scoped>
.data-popup-title {
  height: 80rpx;
}
.data-popup-header {
  background: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  height: 80rpx;
  line-height: 80rpx;
  box-sizing: border-box;
  border-bottom: 1px solid #eee;
}
.scroll-list {
  max-height: 800rpx;
  overflow: auto;
  background: #fff;
}
</style>
