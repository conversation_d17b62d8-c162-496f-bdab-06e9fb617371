<template>
  <u-popup v-model="visible" mode="bottom">
    <view class="bottom_menu">
      <view
        class="bottom_menu_item"
        v-for="(action, idx) in actions"
        :key="idx"
        @click="handleClick(action.emitName)"
      >
        {{ action.label }}
      </view>

      <view class="bottom_menu_item cancel_box" @click="handleClick('cancel')">
        取消
      </view>
    </view>
  </u-popup>
</template>

<script>
export default {
  props: {
    actions: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      row: {}
    };
  },
  methods: {
    show(row) {
      this.row = row;
      this.visible = true;
    },
    handleClick(emitName) {
      this.visible = false;
      this.$emit(emitName, this.row);
    }
  }
};
</script>
<style lang="scss" scoped>
.bottom_menu {
  background: #f8f8f8;
  .bottom_menu_item {
    background: #fff;
    height: 108rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32rpx;
    font-weight: 400;
    color: #333333;
  }
  .bottom_menu_item + .bottom_menu_item {
    border-top: 2rpx solid #eee;
  }
  .cancel_box {
    margin-top: 16rpx;
    height: 112rpx;
    font-size: 36rpx;
    font-weight: 400;
    color: #333333;
    border: 0 !important;
  }
}
</style>
