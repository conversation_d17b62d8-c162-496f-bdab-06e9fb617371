export default {
  data() {
    return {
      processPopup: {
        tap: '',
        value: null,
        params: { status: 9, sord: 'desc', sidx: 'create_date' },
        apiFunc: this.ajax.getMyLaunchWorkflowList
      }
    };
  },
  methods: {
    chooseProcess(e) {
      let data = e.currentTarget.dataset;
      this.processPopup.tap = data.tap;
      this.processPopup.value = this.choiceValue[data.tap].map(item => {
        return item.wfInstanceId;
      });
      this.processPopup.params.wfDefinitionIds = this.formKeyTemplate[
        data.keyId
      ].relationWorkflowId;
      this.$refs.processPopupSelect.open();
    },
    processPopupOnChange(e) {
      this.$set(
        this.itemData,
        this.processPopup.tap,
        e.data.length ? JSON.stringify(e.data) : ''
      );
      this.$set(this.itemValue, this.processPopup.tap, e.data);
      this.$set(this.choiceData, this.processPopup.tap, e.data);
      this.$set(this.choiceValue, this.processPopup.tap, e.data);
    },
    deleteProcess(tap, id) {
      this.itemValue[tap] = this.itemValue[tap].filter(
        item => item.wfInstanceId != id
      );
      this.choiceData[tap] = this.choiceData[tap].filter(
        item => item.wfInstanceId != id
      );
      this.choiceValue[tap] = this.choiceValue[tap].filter(
        item => item.wfInstanceId != id
      );
      this.itemData[tap] = this.itemValue[tap].length
        ? JSON.stringify(this.itemValue[tap])
        : '';
    },
    checkProcessDetail(data) {
      let pagePramas = {
        isMobile: true,
        wfInstId: data.wfInstanceId,
        name: 'checkDetail'
      };
      uni.navigateTo({
        url: `/pages/workflow/my-workflow-detail?${this.$common.convertObj(
          pagePramas
        )}`
      });
    }
  }
};
