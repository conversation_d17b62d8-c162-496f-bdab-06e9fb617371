import { mapState } from 'vuex';

import { chooseImage, chooseFile } from '@/common/js/uploadImg.js';
import graceChecker from '@/common/js/graceChecker.js';
import convertCapital from '@/common/js/changeNumToChinese.js';
import formAutoCalculat from '@/common/js/formAutoCalculat.js';
import Base64 from '@/common/js/base64.min.js';

import process from './process';
export default {
  name: 'FormInput',
  mixins: [process],
  computed: {
    ...mapState(['empcode'])
  },
  props: {
    currentStepNo: {
      type: String,
      default: 'start'
    },
    formTemplate: {
      type: Array,
      default() {
        return [];
      }
    },
    formId: {
      type: String,
      default() {
        return '';
      }
    },
    formDatas: {
      type: Object,
      default() {
        return {};
      }
    },
    fileOpt: {
      type: Object,
      default() {
        return {};
      }
    },
    btnType: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      showContent: false,
      commonWordsList: [],
      commentVal: '',
      commentValTap: '',
      commentFileRequired: false,
      personInfo: {},
      datePickerVal: null, // 时间选择器数据
      picker: {
        tap: '', //点击的元素
        mode: 'date', //日期选择的类型
        fields: 'day', //日期选择的颗粒度
        value: '', //已选值
        list: '', //选项
        popupType: '', //弹出层弹出方式
        popupChoice: '', //弹出层选择方式
        type: '', //字段类型
        showSearch: false //是否显示搜索框
      },
      itemData: {}, //每一项的数据
      itemValue: {}, //每一项的数据
      choiceData: {}, //每个选择项的数据
      choiceValue: {}, //每个选择项的数据
      dataRequired: {}, //每个选择项是否必填
      preChoiceData: [], //点击确定前多选值
      preItemData: [], //多选
      formKeyTemplate: {},
      interwork: [], //互通请求接口
      interworkIndex: 0,
      interworkValObj: {},
      //互通 修改病人信息
      interworkSickType: [
        {
          text: '住院号',
          value: 'zyh'
        },
        {
          text: '身份证号',
          value: 'sfzh'
        },
        {
          text: '诊疗卡号',
          value: 'zlkh'
        }
      ],
      interworkSickSelectedType: 'zyh',
      interworkSickVal: '',
      interworkSickChange: {
        name: '',
        sexText: '',
        phone: '',
        idcard: ''
      },
      sickSexList: {
        1: '男',
        2: '女',
        9: '未知'
      },
      orderTypeList: {
        1: '西药',
        2: '中成药',
        3: '中草药',
        4: '治疗',
        5: '医技',
        6: '手术',
        7: '说明',
        8: '护理',
        9: '其他',
        10: '物资',
        101: '材料'
      },
      interworkTestVal: {
        queryOrder: {
          name: '医嘱项目',
          text: '',
          value: {}
        },
        querySample: {
          name: '标本',
          text: '',
          value: {}
        }
      },
      pickerSearch: '',
      scrollStyle: '',
      leaveStatisticsDataList: [],
      columns: [],
      noData: false,
      childFormCountList: [],
      fieldSerialNumberRoleId: '',
      consultationCol: [
        {
          remark: '邀请科室',
          optionList: [
            { id: '1', text: '测试1' },
            { id: '2', text: '测试2' }
          ],
          isNull: 1,
          promptText: '邀请科室'
        }
      ]
    };
  },
  async created() {
    this.getMyOfficaldiction();
    await this.getPersonInfo();
  },
  filters: {
    pickerValueFilter(value) {
      let arr = [];
      if (value) {
        if (Array.isArray(value)) {
          arr = value.map(item => {
            return item['text'];
          });
        } else {
          arr.push(value['text']);
        }
      }
      return arr.join('、');
    },
    sickKeyFilter(value) {
      let key = '';
      switch (value) {
        case 'name':
          key = '姓名';
          break;
        case 'sex':
          key = '性别';
          break;
        case 'phone':
          key = '电话';
          break;
        case 'idcard':
          key = '身份证';
          break;
      }
      return key;
    },
    formatDate(dateVal) {
      let date = new Date(dateVal),
        YY = date.getFullYear(),
        MM = String(date.getMonth() + 1).replace(/(^\d{1}$)/, '0$1'),
        DD = String(date.getDate()).replace(/(^\d{1}$)/, '0$1'),
        time = `${YY}年${MM}月${DD}日`;
      return time;
    }
  },
  watch: {
    pickerSearch(newVal) {
      let searchKey = 'text';
      if (this.picker.type === 'inPatientOrder') {
        searchKey = 'orderName';
      }
      this.$set(
        this.picker,
        'list',
        this.interwork[this.interworkIndex].list.filter(i => {
          return i[searchKey].indexOf(newVal) != -1;
        })
      );
    }
  },
  methods: {
    //获取用户信息
    async getPersonInfo() {
      await this.ajax
        .getPersonInfoByPrimaryKey({
          userCode: this.empcode
        })
        .then(async res => {
          let data = res.object;
          this.personInfo = data;
          await this.init();
        });
    },
    //初始化数据
    async init() {
      let _self = this;
      let calculationList = [];
      await Promise.all(
        _self.formTemplate.map(async (el, indexNum, arr) => {
          // 存储需要计算的子表单统计字段信息
          if (el.fieldType == 'childFormCount') {
            this.childFormCountList.push(el);
          }
          if (el.showName) {
            let data_val = await _self.initvalue(el, indexNum, arr);
            el.placeholderContent = data_val.placeholderContent.replace(
              /\t/g,
              ''
            );
            if (data_val.fieldType) {
              el.fieldType = data_val.fieldType;
            }
            if (data_val.options) {
              el.optionList = data_val.options;
            }
            _self.$set(
              _self.itemData,
              el.fieldName,
              Array.isArray(data_val.itemData) ? '' : data_val.itemData
            );
            _self.$set(_self.itemValue, el.fieldName, data_val.itemValue);
            _self.$set(
              _self.choiceData,
              el.fieldName,
              data_val.fieldType == 'childForm'
                ? JSON.parse(JSON.stringify(data_val.choiceValue))
                : data_val.choiceValue
            );
            _self.$set(
              _self.choiceValue,
              el.fieldName,
              data_val.fieldType == 'childForm'
                ? JSON.parse(JSON.stringify(data_val.choiceValue))
                : data_val.choiceValue
            );
            if (data_val.isMust != '' || data_val.checkType) {
              _self.$set(_self.dataRequired, el.fieldName, {
                filedKey: el.fieldName,
                required: data_val.isMust == 'Y' && el.isReadonly != 'Y',
                checkType: data_val.checkType ? data_val.checkType : '',
                checkRule: data_val.checkRule || '',
                errorMsg: data_val.placeholderContent.replace(/\t/g, ''),
                otherErrorMsg: data_val.errorMsg
              });
            }
            if (data_val.dataType) el.dataType = data_val.dataType;
            if (data_val.interworkVal != undefined)
              _self.$set(
                _self.interworkValObj,
                el.fieldName,
                data_val.interworkVal
              );
            if (data_val.searchApi) el.searchApi = data_val.searchApi;
            if (data_val.searchParams) el.searchParams = data_val.searchParams;
            if (data_val.fileList) el.fileList = data_val.fileList;
            //dataSource: 1-手动录入、 2-常用字段 、3-公式计算
            if (el.dataSource == 3) {
              calculationList.push(el.keyId);
            }
            _self.$set(_self.formKeyTemplate, el.keyId, el);
          }
        })
      );
      calculationList.map(item => {
        let el = _self.formKeyTemplate[item];
        let func = formAutoCalculat.parseCalRules(
          el.calculationRole,
          _self.formKeyTemplate,
          _self.itemData
        );
        if (func.triggerFieldKeyId.length) {
          func.triggerFieldKeyId.map(i => {
            let fieldParams = _self.formKeyTemplate[i];
            fieldParams.inputChangeFiled = {
              calculationRole: el.calculationRole,
              fieldName: el.fieldName,
              keyId: el.keyId
            };
            _self.$set(_self.formKeyTemplate, i, fieldParams);
          });
        }
      });
      _self.showContent = true;
    },
    async initvalue(el, index, arr) {
      let _self = this,
        init_val,
        el_data = {};
      if (
        JSON.stringify(_self.formDatas) === '{}' ||
        _self.formDatas[el.fieldName] === '' ||
        _self.formDatas[el.fieldName] === null
      ) {
        init_val = el.defaultValue || '';
      } else {
        init_val = _self.formDatas[el.fieldName] || '';
      }
      el_data.checkType = 'string';
      el_data.isMust = el.isMust;
      el_data.placeholderContent = el.promptText
        ? el.promptText
        : '请输入' + el.showName.replace(/\t/g, '');
      if (el.fieldType == 'input' || el.fieldType == 'textarea') {
        if (el.dataSource == 2 && el.sourceField == 'nowDate') {
          el.fieldType = 'date';
          el_data.dataType = 'day';
          init_val = _self.$common.getDate('date').timeStr;
        } else if (el.dataSource == 2 && el.sourceField == 'loginName') {
          init_val = _self.personInfo.empName || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginCarno') {
          init_val = _self.personInfo.carNo || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginCode') {
          init_val = _self.personInfo.empCode || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginOrg') {
          init_val = _self.personInfo.ssoOrgName || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginDept') {
          init_val =
            _self.personInfo.organizationParttimeName ||
            _self.personInfo.empDeptName ||
            '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginPhone') {
          el.fieldType = 'number';
          el_data.checkType = 'phone';
          el_data.errorMsg = `${el.showName}格式不正确`;
          init_val = _self.personInfo.empPhone || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginEmail') {
          el_data.checkType = 'email';
          el_data.errorMsg = `${el.showName}格式不正确`;
          init_val = _self.personInfo.empEmail || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginDuty') {
          init_val = _self.personInfo.empDutyName || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginBirth') {
          el.fieldType = 'date';
          el_data.dataType = 'day';
          init_val = _self.personInfo.empBirth || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginAge') {
          el.fieldType = 'number';
          el_data.checkType = 'positiveInt';
          el_data.errorMsg = `${el.showName}只能为正整数`;
          init_val = _self.personInfo.empAge || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginSex') {
          init_val =
            _self.personInfo.empSex == 0
              ? '男'
              : _self.personInfo.empSex == 1
              ? '女'
              : '';
        } else if (el.dataSource == 2 && el.sourceField == 'annualLeave') {
          el.fieldType = 'number';
          el_data.checkType = 'nonnegativeNumber';
          el_data.errorMsg = `${el.showName}只能为非负数`;
          init_val = _self.personInfo.yearDays || '';
        } else if (el.dataSource == 2 && el.sourceField == 'annualLeave_h') {
          el.fieldType = 'number';
          el_data.checkType = 'nonnegativeNumber';
          el_data.errorMsg = `${el.showName}只能为非负数`;
          init_val = _self.personInfo.yearNumber || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginIDCard') {
          el_data.checkType = 'idcard';
          el_data.errorMsg = `${el.showName}格式不正确`;
          init_val = _self.personInfo.empIdcard || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginPost') {
          init_val = _self.personInfo.postName || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginPostType') {
          init_val = _self.personInfo.postType || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginEntryDate') {
          el.fieldType = 'date';
          el_data.dataType = 'day';
          init_val = _self.personInfo.entryDate || '';
        } else if (el.dataSource == 2 && el.sourceField == 'positiveTime') {
          el.fieldType = 'date';
          el_data.dataType = 'day';
          init_val = _self.personInfo.positiveTime || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginPosttitle') {
          init_val = _self.personInfo.entryDate || '';
        } else if (
          el.dataSource == 2 &&
          el.sourceField == 'loginOrgAttributes'
        ) {
          init_val = _self.personInfo.orgAttributes || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginJob') {
          init_val = _self.personInfo.jobAttributes || '';
        } else if (el.dataSource == 2 && el.sourceField == 'loginWorkDate') {
          init_val = _self.personInfo.workStartDate || '';
        }
        if (el.fieldType != 'date') {
          el_data.itemData = el_data.choiceValue = el_data.itemValue = init_val;
        } else {
          el_data.itemData = el_data.itemValue = init_val;
          el_data.choiceValue = {
            value: init_val,
            text: init_val
          };
        }
      } else if (el.fieldType == 'number') {
        el_data.itemData = el_data.itemValue = el_data.choiceValue = init_val;
        el_data.checkType = 'number';
        if (el.isMakeBigger) {
          el_data.checkType = 'string';
        }
      } else if (el.fieldType == 'date') {
        if (el.dataFormat == 'yyyy-MM') {
          el_data.dataType = 'month';
        } else if (el.dataFormat == 'yyyy-MM-dd') {
          el_data.dataType = 'day';
        } else if (el.dataFormat == 'yyyy-MM-dd HH:mm') {
          el_data.dataType = 'minute';
        } else if (el.dataFormat == 'yyyy-MM-dd HH:mm:ss') {
          el_data.dataType = 'second';
        }
        el_data.itemData = el_data.itemValue = init_val;
        el_data.choiceValue = {
          value: init_val,
          text: init_val
        };
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请选择' + el.showName.replace(/\t/g, '');
      } else if (
        el.fieldType == 'radio' ||
        el.fieldType == 'select' ||
        el.fieldType == 'checkbox' ||
        el.fieldType == 'workorderSetting'
      ) {
        if (el.dataSource == 4) {
          //数据字典
          el_data.options = await _self.getDataDictionary(el.dictSource);
          let list = init_val.split(',');
          el_data.choiceValue = list.map(one => {
            return {
              value: one,
              text: one
            };
          });
        } else if (el.dataSource == 7) {
          el_data.options = await _self.getSelectOptionList(
            el.interfaceServices
          );
          el_data.choiceValue = [];
          let list = init_val.split(',');
          list.map(one => {
            let index = el_data.options.findIndex(i => {
              return i.value == one;
            });
            if (index != -1) {
              el_data.choiceValue.push({
                value: el_data.options[index].value,
                text: el_data.options[index].text
              });
            }
          });
        } else if (el.dataSource == 6) {
          //关联流程数据
          let dataList = await _self.getDataList(el.relationWorkflowId),
            //获取关联字段
            fieldList = await _self.getFindByFieldId(el.keyId);
          //选项值拼接
          el_data.options = dataList.map(dataItem => {
            let optionText = fieldList
                .map(fieldItem => {
                  return dataItem[fieldItem.valueFieldname];
                })
                .join('——'),
              optionVal = fieldList.map(fieldItem => {
                return {
                  keyFieldname: fieldItem['keyFieldname'],
                  valueFieldname: dataItem[fieldItem.valueFieldname]
                };
              });
            return {
              relationValue: optionVal,
              value: dataItem.ID,
              text: optionText
            };
          });
          let relationVal = null;
          dataList.map(item => {
            if (init_val == item.ID) {
              relationVal = item;
            }
          });
          if (relationVal) {
            let relationValText = fieldList
              .map(fieldItem => {
                return relationVal[fieldItem.valueFieldname];
              })
              .join('——');
            el_data.choiceValue = [
              {
                value: relationValText,
                text: relationValText
              }
            ];
          } else {
            el_data.choiceValue = [];
          }
        } else if (el.dataSource == 8) {
          //工单处理科室
          let optionList = await _self.getWorkOrderDatas(
            '/omMeau/meauPermissionsList'
          );
          let relationParamsArr = arr.filter(i => {
            return i.dataSource == 9;
          });
          //选项值拼接
          el_data.options = optionList.map(dataItem => {
            if (init_val == dataItem.deptId) {
              el_data.choiceValue = {
                value: dataItem.deptId,
                text: dataItem.deptName
              };
            }
            return {
              relationParams: JSON.parse(JSON.stringify(relationParamsArr)),
              value: dataItem.deptId,
              text: dataItem.deptName
            };
          });
        } else if (el.dataSource == 9) {
          //工单处理科室关联字段
          el_data.options = [];
          el_data.searchParams = arr.filter(i => {
            return i.dataSource == 8;
          });
          el_data.searchApi = '/faultType/getFaultTypeAllList/1';
          let searchParamsVal =
            _self.formDatas[el_data.searchParams[0]['fieldName']];
          if (searchParamsVal) {
            let optionList = await _self.getWorkOrderDatas(
              `${el_data.searchApi}/${searchParamsVal}`
            );
            optionList.map(one => {
              if (init_val == one.id) {
                el_data.choiceValue = {
                  value: one.id,
                  text: one.name
                };
              }
            });
          }
        } else {
          let options = el.optionValue ? el.optionValue.split(',') : [];
          el_data.options = options.map(i => {
            return {
              value: i,
              text: i
            };
          });
          let list = init_val.split(',');
          el_data.choiceValue = list.map(one => {
            return {
              value: one,
              text: one
            };
          });
        }
        el_data.itemData = el_data.itemValue = init_val;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请选择' + el.showName.replace(/\t/g, '');
      } else if (el.fieldType == 'fileTemplate') {
        let list = await _self.getFiles(el.fileTemplate);
        el_data.fileList = list.map(one => {
          one.fileRealName = one.fileName;
          one.fileName = one.originalName;
          one.fileType = one.fileExtension;
          return one;
        });
      } else if (el.fieldType == 'file') {
        let list = [];
        if (init_val && JSON.stringify(_self.fileOpt) != '{}') {
          list = _self.fileOpt[el.fieldName];
        }
        let fileIdArr = list.map(one => {
          one.fileRealName = one.fileName;
          one.fileName = one.originalName;
          one.fileId = one.id;
          return one.id;
        });
        el_data.itemData = fileIdArr.join(',');
        el_data.itemValue = el_data.choiceValue = list;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请上传' + el.showName.replace(/\t/g, '');
      } else if (el.fieldType == 'deptChose') {
        let deptList = [];
        if (init_val) {
          let dept = init_val.split('--'),
            deptNameArr = dept[0].split(','),
            deptIdArr = dept[1].split(',');
          deptList = deptNameArr.map((one, index) => {
            return {
              choose: true,
              id: deptIdArr[index],
              name: one
            };
          });
        }
        el_data.itemData = init_val;
        el_data.itemValue = el_data.choiceValue = deptList;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请选择' + el.showName.replace(/\t/g, '');
      } else if (el.fieldType == 'personChose') {
        el_data.itemData = init_val;
        let personList = [];
        if (init_val) {
          _self.getEmpByUserCode(el.fieldName, init_val.split('--')[1]);
        }
        el_data.itemValue = el_data.choiceValue = personList;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请选择' + el.showName.replace(/\t/g, '');
      } else if (el.fieldType == 'processChoose') {
        let processList = [];
        if (init_val) {
          processList = JSON.parse(init_val);
        }
        el_data.itemData = init_val;
        el_data.itemValue = el_data.choiceValue = processList;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请选择' + el.showName.replace(/\t/g, '');
      } else if (el.fieldType == 'table') {
        el_data.itemData = '';
        let columns = el.defaultVal.split(';'),
          obj = {};
        columns.forEach(item => {
          obj[item] = '';
        });
        el_data.itemValue = el_data.choiceValue = init_val
          ? JSON.parse(init_val)
          : [obj];
      } else if (el.fieldType == 'serialNumber') {
        let serialNumber =
          init_val && this.btnType != 'startAgain'
            ? init_val
            : _self.getSerialNumber(el.fieldName);
        el_data.itemData = el_data.choiceValue = el_data.itemValue = serialNumber;
      } else if (el.fieldType == 'interworkSick') {
        el_data.itemData = el_data.itemValue = init_val;
        el_data.choiceValue = init_val ? JSON.parse(init_val) : [];
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : '请输入病人信息';
      } else if (
        el.fieldType == 'interworkSettle' ||
        el.fieldType == 'interworkTest'
      ) {
        el_data.itemData = el_data.itemValue = init_val;
        el_data.choiceValue = init_val ? JSON.parse(init_val) : {};
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : `请选择${el.showName}`;
      } else if (el.fieldType == 'inPatientOrder') {
        el_data.itemValue = [];
        el_data.itemData = JSON.stringify([]);
        el_data.placeholderContent = '请输入住院号且选择医嘱耗材';
      } else if (
        el.fieldType == 'interworkCom' ||
        el.fieldType == 'interworkPay' ||
        el.fieldType == 'interworkHosPro'
      ) {
        el_data.itemData = init_val;
        let payData = init_val ? JSON.parse(init_val) : [],
          itemDataArr = payData.map(i => {
            return i.id;
          });
        el_data.itemValue = itemDataArr.join(',');
        el_data.choiceValue = payData;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : `请选择${el.showName}`;
      } else if (
        el.fieldType == 'interworkHosInfo' ||
        el.fieldType == 'operationItem'
      ) {
        el_data.itemData = init_val;
        el_data.placeholderContent = el.promptText
          ? el.promptText
          : `请选择${el.showName}`;
      } else if (el.fieldType == 'comment') {
        if (el.isReadonly != 'Y') {
          _self.commentValTap = el.fieldName;
          if (el.isMustCommentFile) {
            _self.commentFileRequired = true;
            el_data.errorMsg = `请上传${el.showName.replace(/\t/g, '')}附件`;
          }
        }
        el_data.itemData = '';
        _self.commonWordsList.map(i => {
          if (i?.isDefaultValue == 1) {
            el_data.itemData = i.offName;
          }
        });
        el_data.itemValue = [];
        el_data.choiceValue = [];
      } else if (el.fieldType == 'signature') {
        el_data.itemData = el_data.itemValue =
          _self.personInfo.signatureImgName || _self.personInfo.empName;
      } else if (el.fieldType == 'childForm') {
        let itemData = {};
        el.childFormDetail.fields.map(item => {
          if (item.fieldType == 'EXPRESSION') {
            itemData[item.fieldName] = '';
          } else {
            itemData[item.fieldName] = item.defaultValue || '';
          }
        });
        if (this.formDatas[el.fieldName]) {
          el_data.choiceValue = init_val;
        } else {
          el_data.choiceValue = [itemData];
        }
        el_data.itemData = [itemData];
      } else if (el.fieldType == 'leaveStatistics') {
        this.getLeaveStatistics();
      } else if (el.fieldType == 'consultation') {
        init_val = init_val ? JSON.parse(init_val) : [];
        if (init_val.length == 0) {
          init_val = [
            { id: '', deptId: '', deptName: '', typeId: '', typeName: '' }
          ];
        }
        el_data.itemData = JSON.stringify(init_val);
      }
      return el_data;
    },
    async getLeaveStatistics() {
      let res = await this.ajax.getleaveStatisticsTableHeadCols({
        isWorkflow: 'Y'
      });
      let tableHeadList = [];
      let tableData = [];
      let year = new Date().getFullYear();
      if (res.success && res.object) {
        res.object.push({
          label: '合计',
          name: 'total',
          hidden: false
        });
        tableHeadList = res.object;
      } else {
        uni.showToast({
          title: `未查询到请假表头信息`,
          icon: 'none'
        });
        return;
      }
      let resData = await this.ajax.getleaveStatisticsDataList({
        isWorkflow: 'Y',
        startLeaveMonth: year + '-01',
        endLeaveMonth: year + '-12',
        employeeCode: this.personInfo.empCode
      });
      tableData = resData.rows || [];
      tableData.forEach(e => {
        let total = 0;
        for (let key in e) {
          if (typeof e[key] == 'number') {
            total += e[key];
          }
        }
        e.total = total;
      });
      let noData = false;
      tableHeadList.forEach(item => {
        if (!item.hidden) {
          let canShow = false;
          tableData.forEach(e => {
            let vPd = e[item.name];
            if (
              (typeof vPd == 'number' && vPd > 0) ||
              (vPd instanceof Array && vPd.length)
            ) {
              canShow = true;
              noData = true;

              if (item.name === 'kxnj') {
                e[item.name] = (vPd || [])
                  .map(
                    ({ year, remainingDays: d }) =>
                      `${year}年<text class="remainingDays">${d}</text>天`
                  )
                  .join('<br>');

                item.widthSet = '90px';
              }
            }
          });
          if (!canShow) {
            item.hidden = true;
          }
        }
      });
      this.columns = tableHeadList
        .filter(e => !e.hidden)
        .map(e => {
          return {
            title: e.label,
            key: e.name,
            width: e.widthSet || '80px'
          };
        });
      this.leaveStatisticsDataList = tableData;
      this.noData = noData;
    },
    //获取流水号
    getSerialNumber(fieldName) {
      let _self = this;
      _self.ajax
        .getCalculationSerialNo({
          templateTd: _self.formId
        })
        .then(res => {
          _self.$set(_self.itemData, fieldName, res.object[fieldName]);
          this.fieldSerialNumberRoleId = res.object.fieldSerialNumberRoleId;
        });
    },
    //获取常用语
    getMyOfficaldiction() {
      let _self = this;
      _self.ajax.getMyOfficaldiction().then(res => {
        _self.commonWordsList = res.object.map(item => {
          return {
            value: item.offName,
            text: item.offName
          };
        });
      });
    },
    //获取人员信息
    getEmpByUserCode(fieldName, userCodeStr) {
      let _self = this;
      _self.ajax
        .getEmpByUserCode({
          userCodeStr: userCodeStr
        })
        .then(res => {
          let personList = res.object.map(one => {
            return {
              id: one.empCode,
              name: one.empName,
              empFirstName: one.empName.substring(one.empName.length - 2),
              empHeadImg: one.empHeadImg,
              empDeptName: one.empDeptName,
              empDutyName: one.empDutyName,
              empSex: one.empSex,
              choose: true
            };
          });
          _self.$set(_self.itemValue, fieldName, personList);
          _self.$set(_self.choiceValue, fieldName, personList);
          _self.$set(_self.choiceData, fieldName, personList);
        });
    },
    //获取数据字典
    async getDataDictionary(dictSource) {
      let _self = this,
        options = null;
      await _self.ajax
        .getDictItemByTypeCode({
          typeCode: dictSource
        })
        .then(res => {
          options = res.object.map(item => {
            return {
              text: item.itemName,
              value: item.itemName
            };
          });
        });
      return options;
    },
    //通过接口获取下拉选项
    async getSelectOptionList(api) {
      let _self = this,
        options = null;
      await _self.ajax.getSelectOptionList(api).then(res => {
        options = res.object || [];
        options = options.map(item => {
          return {
            text: item.itemName,
            value: item.itemValue
          };
        });
      });
      return options;
    },
    //获取流程表单数据
    async getFindByFieldId(keyId) {
      let _self = this,
        relation = null;
      await _self.ajax.getDatasByFieldId(keyId).then(res => {
        relation = res.object.relation ? JSON.parse(res.object.relation) : [];
      });
      return relation;
    },
    //根据流程id获取自己发起已完结的流程流程数据
    async getDataList(workflowId) {
      let _self = this,
        datas = [];
      await _self.ajax.getMyselfDataListByWorkflowId(workflowId).then(res => {
        datas = res.object;
      });
      return datas;
    },
    //获取工单相关数据
    async getWorkOrderDatas(api) {
      let _self = this,
        datas = [];
      await _self.ajax.getWorkOrderDatas(api).then(res => {
        datas = res.object;
      });
      return datas;
    },
    //显示时间弹出层
    showPicker(e) {
      let data = e.currentTarget.dataset;
      delete this.picker.modeType;
      delete this.picker.modeOption;
      Object.keys(data).map(key => {
        this.$set(this.picker, key, data[key]);
      });
      if (this.picker.modeType == 'childForm') {
        let { index, fieldName, isReadOnly } = this.picker.modeOption;
        this.datePickerVal = this.choiceData[data.tap][index][fieldName];
        if (isReadOnly == 1) {
          return;
        }
      } else {
        this.datePickerVal = this.itemValue[data.tap];
      }
      this.$set(this.picker, 'type', 'date');
      this.$nextTick(() => {
        this.$refs[data.ref].show();
      });
    },
    //时间选择确认
    onConfirm(res) {
      if (this.picker.modeType == 'childForm') {
        let { index, fieldName } = this.picker.modeOption;
        this.$set(
          this.choiceData[this.picker.tap][index],
          fieldName,
          res.result
        );
        this.handleChildFormItemDataChange(
          this.choiceData[this.picker.tap][index],
          this.picker.modeOption,
          this.formTemplate[this.picker.tempIndex],
          index
        );
      } else {
        this.$set(this.itemData, this.picker.tap, res.result);
        this.$set(this.itemValue, this.picker.tap, res.result);
        this.$set(this.choiceData, this.picker.tap, {
          value: res.value,
          text: res.result
        });
        this.$set(this.choiceValue, this.picker.tap, {
          value: res.value,
          text: res.result
        });
        let setting = this.formTemplate.find(
          item => item.fieldName == this.picker.tap
        );
        if (setting && setting.inputChangeFiled != undefined) {
          this.calculatBlur(
            setting.inputChangeFiled.keyId,
            setting.inputChangeFiled.fieldName,
            setting.inputChangeFiled.calculationRole
          );
        }
      }
    },
    selectSickType(value) {
      this.interworkSickSelectedType = value;
    },
    //获取互通数据（病人信息）
    async getInterworkSick() {
      let _self = this;
      await _self.ajax
        .getHisPatientInfo({
          type: _self.interworkSickSelectedType,
          number: _self.interworkSickVal
        })
        .then(res => {
          if (res.object.length == 0) {
            uni.showToast({
              title: `未查询到相关数据`,
              icon: 'none'
            });
            return;
          } else {
            _self.interworkIndex++;
            let list = res.object.map(i => {
              let obj = JSON.parse(JSON.stringify(i));
              obj.value = i.id;
              if (i.sex == 1) {
                obj.sexText = '男';
              } else if (i.sex == 2) {
                obj.sexText = '女';
              } else {
                obj.sexText = '未知';
              }
              return obj;
            });
            let interworkObj = { list: list };
            _self.$set(
              _self.interwork,
              _self.interworkIndex,
              JSON.parse(JSON.stringify(interworkObj))
            );
            _self.$set(_self.picker, 'list', list);
            _self.$set(_self.picker, 'popupChoice', 'radio');
          }
        });
    },
    //显示弹出层
    async showPopup(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        list = [],
        choiceType = data.popupChoice;
      if (data.modeOption && data.modeOption.isReadOnly == 1) {
        return false;
      }
      if (data.popupChoice == 'interworkSick') {
        list = [];
        _self.interworkIndex = 0;
        choiceType = 'radio';
        _self.$set(_self.picker, 'tap', data.tap);
        _self.$set(_self.picker, 'popupType', data.popupType);
        _self.$set(_self.picker, 'type', data.popupChoice);
      } else if (data.popupChoice == 'interworkTest') {
        list = [];
        _self.interworkIndex = 0;
        choiceType = 'radio';
        _self.$set(_self.picker, 'tap', data.tap);
        _self.$set(_self.picker, 'popupType', data.popupType);
        _self.$set(_self.picker, 'type', data.popupChoice);
        _self.$set(_self.interwork, _self.interworkIndex, { list: [] });
      } else if (
        data.popupChoice == 'interworkSettle' ||
        data.popupChoice == 'interworkPay' ||
        data.popupChoice == 'interworkHosPro'
      ) {
        if (data.interval === '' || data.interval === undefined) {
          uni.showToast({
            title: `请输入住院号`,
            icon: 'none'
          });
          return;
        }
        _self.interworkIndex = 0;
        list = await _self.getInpatientInfo({ name: data.interval });
        if (list.length == 0) {
          uni.showToast({
            title: `未查询到相关数据`,
            icon: 'none'
          });
          return;
        }
        choiceType = 'radio';
        _self.$set(_self.interwork, _self.interworkIndex, {
          list: JSON.parse(JSON.stringify(list))
        });
      } else if (data.popupChoice == 'inPatientOrder') {
        if (data.interval === '' || data.interval === undefined) {
          uni.showToast({
            title: `请输入住院号`,
            icon: 'none'
          });
          return;
        }
        _self.interworkIndex = 0;
        list = await _self.consumableDetails(data.interval);
        if (list.length == 0) {
          uni.showToast({
            title: `未查询到相关数据`,
            icon: 'none'
          });
          return;
        }
        choiceType = 'checkBox';
        _self.$set(_self.picker, 'showSearch', true);
        _self.$set(_self, 'pickerSearch', '');
        _self.$set(_self.interwork, _self.interworkIndex, {
          list: JSON.parse(JSON.stringify(list))
        });
      } else if (data.popupChoice == 'commonWords') {
        choiceType = 'radio';
        list = _self.commonWordsList;
      } else {
        if (data.dataSource == 9) {
          if (data.searchParams && data.searchParams.length > 0) {
            let param = _self.itemData[data.searchParams[0].fieldName];
            if (!param) {
              uni.showToast({
                icon: 'none',
                title: '请先选择处理科室'
              });
              return false;
            }
            let optionList = await _self.getWorkOrderDatas(
              `${data.searchApi}/${param}`
            );
            list = optionList.map(item => {
              return {
                text: item.name,
                value: item.id
              };
            });
          }
        } else {
          list = data.options;
        }
      }
      delete _self.picker.modeType;
      delete _self.picker.modeOption;
      _self.$set(_self.picker, 'tap', data.tap);
      _self.$set(_self.picker, 'popupType', data.popupType);
      _self.$set(_self.picker, 'list', list);
      _self.$set(_self.picker, 'popupChoice', choiceType);
      _self.$set(_self.picker, 'type', data.popupChoice);
      _self.$set(_self.picker, 'modeType', data.modeType);
      _self.$set(_self.picker, 'modeOption', data.modeOption);
      _self.$set(_self.picker, 'tempIndex', data.tempIndex);
      if (
        data.popupChoice != 'interworkCom' &&
        data.popupChoice != 'interworkSick' &&
        data.popupChoice != 'interworkSettle' &&
        data.popupChoice != 'inPatientOrder'
      ) {
        _self.$set(
          _self.itemValue,
          _self.picker.tap,
          _self.itemData[_self.picker.tap]
        );
        _self.$set(
          _self.choiceValue,
          _self.picker.tap,
          data.modeType == 'childForm'
            ? JSON.parse(JSON.stringify(_self.choiceData[_self.picker.tap]))
            : _self.choiceData[_self.picker.tap]
        );
      }
      if (data.popupChoice == 'interworkPay') {
        let itemDataVal =
            _self.itemData[_self.picker.tap] != ''
              ? JSON.parse(_self.itemData[_self.picker.tap])
              : [],
          itemDataIdArr = itemDataVal.map(i => {
            return i.id;
          });
        _self.$set(_self.itemValue, _self.picker.tap, itemDataIdArr.join(','));
      }
      if (data.popupChoice == 'inPatientOrder') {
        _self.$set(
          _self.itemValue,
          _self.picker.tap,
          JSON.parse(_self.itemData[_self.picker.tap])
        );
      }
      _self.$nextTick(() => {
        _self.$refs[data.ref].open();
      });
    },
    //获取附件
    async getFiles(fileIds) {
      let list = [],
        _self = this;
      await _self.ajax
        .getFiles({
          idsStr: fileIds
        })
        .then(res => {
          list = res.object;
        });
      return list;
    },
    //获取互通数据（电子病历）
    async getInterworkComData(data) {
      let list = [],
        _self = this;
      await _self.$api
        .request({
          method: `${_self.interwork[_self.interworkIndex]['method']}`, //必须大写
          url: `${_self.interwork[_self.interworkIndex]['url']}`,
          data: data,
          custom: {
            showLoading: true
          }
        })
        .then(res => {
          list = res.object.map(item => {
            return {
              value: item.id,
              text: item.name
            };
          });
        });

      return list;
    },
    //获取互通数据（住院信息）
    async getInpatientInfo(data) {
      let list = [],
        _self = this;
      await _self.ajax.getEmrPatientInfo(data).then(res => {
        list = res.object.map(item => {
          return { ...item, ...{ value: item.id, text: item.name } };
        });
      });
      return list;
    },
    //获取互通数据（结算信息）
    async getStatementRecord(data) {
      let list = [],
        _self = this;
      await _self.ajax.getHisStatementRecord(data).then(res => {
        res.object.forEach(item => {
          item.show = 'false';
        });
        list = JSON.parse(JSON.stringify(res.object));
      });
      return list;
    },
    //获取互通数据（预交金信息）
    async getInpDepositsParm(data, column) {
      let list = [],
        columnObj = {},
        _self = this;
      await _self.ajax.getHisInpDepositsParmList(data).then(res => {
        for (var key in column) {
          if (key != 'id' && key != 'text') columnObj[key] = column[key];
        }
        list = res.object.map(item => {
          item.show = 'false';
          item.value = item.id;
          return { ...columnObj, ...item };
        });
      });
      return list;
    },
    //获取互通数据（医嘱明细）
    async queryNurseInpatientOrder(data) {
      let list = [],
        _self = this;
      await _self.ajax.getHisQueryNurseInpatientOrder(data).then(res => {
        list = res.object.map(item => {
          return {
            ...item,
            ...{ value: item.inpatientId, text: item.Nurse_ORDER_CONTEXT }
          };
        });
      });
      return list;
    },
    //获取互通数据（住院费用明细）
    async queryInPatientNurseFeeSpeci(data) {
      let list = [],
        _self = this;
      await _self.ajax.getHisQueryInPatientNurseFeeSpeci(data).then(res => {
        list = res.object.map(item => {
          return {
            ...item,
            ...{ value: item.ID, text: item.SHOW_ORDER_NAME }
          };
        });
      });
      return list;
    },
    //获取互通数据（医嘱项目）
    async queryOrderItemList() {
      let list = [],
        _self = this;
      await _self.ajax
        .getHisQueryOrderItemList({
          pageNo: 1,
          pageSize: 10000
        })
        .then(res => {
          list = res.rows.map(item => {
            return { ...item, ...{ value: item.id, text: item.name } };
          });
        })
        .catch(() => {});
      return list;
    },
    //获取互通数据（标本）
    async querySample() {
      let list = [],
        _self = this;
      await _self.ajax
        .getHisQuerySample({
          pageNo: 1,
          pageSize: 10000
        })
        .then(res => {
          list = res.rows.map(item => {
            return { ...item, ...{ value: item.code, text: item.name } };
          });
        })
        .catch(() => {});
      return list;
    },
    //获取互通数据（耗材明细）
    async consumableDetails(keyword) {
      let list = [],
        _self = this;

      await _self.$api
        .request({
          url: `/ts-external/hisApi/queryInPatientOrder?keyword=${keyword}`,
          method: 'POST',
          contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
          data: {
            pageNo: 1,
            pageSize: 10000,
            sidx: 'create_date',
            sord: 'desc'
          },
          showLoading: true,
          hideLoading: true,
          isPagination: true
        })
        .then(res => {
          list = res.rows || [];
        })
        .catch(() => {});
      return list;
    },
    async selectQueryOrder() {
      let _self = this;
      _self.interworkIndex++;
      _self.$set(_self.picker, 'value', 'queryOrder');
      let queryOrderItemList = await _self.queryOrderItemList();
      _self.$set(_self.interwork, _self.interworkIndex, {
        list: JSON.parse(JSON.stringify(queryOrderItemList))
      });
      _self.$set(
        _self.picker,
        'list',
        JSON.parse(JSON.stringify(queryOrderItemList))
      );
      _self.$set(_self.picker, 'showSearch', true);
      _self.$set(_self.picker, 'popupChoice', 'radio');
      _self.scrollStyle = 'height: 800rpx';
    },
    async selectQuerySample() {
      let _self = this;
      _self.interworkIndex++;
      _self.$set(_self.picker, 'value', 'querySample');
      let querySampleItemList = await _self.querySample();
      _self.$set(_self.interwork, _self.interworkIndex, {
        list: JSON.parse(JSON.stringify(querySampleItemList))
      });
      _self.$set(
        _self.picker,
        'list',
        JSON.parse(JSON.stringify(querySampleItemList))
      );
      _self.$set(_self.picker, 'showSearch', true);
      _self.$set(_self.picker, 'popupChoice', 'radio');
      _self.scrollStyle = 'height: 800rpx';
    },
    sickRadioChange(e) {
      this.interworkSickChange.sexText = e.target.value;
    },
    //单选
    async singleColumn(ref, column) {
      let _self = this;
      if (_self.picker.type == 'interworkSick') {
        _self.interworkIndex++;
        let interworkSickList = [],
          interworkSickData = column;
        _self.$set(_self.interwork, _self.interworkIndex, {
          list: JSON.parse(JSON.stringify(interworkSickData))
        });
        _self.$set(
          _self.picker,
          'list',
          JSON.parse(JSON.stringify(interworkSickList))
        );
        _self.$set(_self.picker, 'value', column.name);
        return;
      } else if (_self.picker.type == 'interworkSettle') {
        if (_self.interworkIndex == 0) {
          let statementData = { id: column.value },
            statementList = await _self.getStatementRecord(statementData);
          _self.interworkIndex++;
          _self.$set(_self.interwork, _self.interworkIndex, {
            list: JSON.parse(JSON.stringify(statementList))
          });
          _self.$set(
            _self.picker,
            'list',
            JSON.parse(JSON.stringify(statementList))
          );
          _self.$set(_self.picker, 'value', column.text);
          return;
        } else {
          let columnObj = JSON.parse(JSON.stringify(column));
          columnObj.zy = _self.picker.value;
          _self.$set(
            _self.itemData,
            _self.picker.tap,
            JSON.stringify(columnObj)
          );
          _self.$set(_self.choiceData, _self.picker.tap, columnObj);
          _self.$set(_self.itemValue, _self.picker.tap, '');
          _self.$set(_self.choiceValue, _self.picker.tap, []);
          _self.$nextTick(() => {
            _self.$refs[ref].close();
          });
          return;
        }
      } else if (_self.picker.type == 'interworkPay') {
        if (_self.interworkIndex == 0) {
          let depositsParmData = { id: column.value },
            depositsParmList = await _self.getInpDepositsParm(
              depositsParmData,
              column
            );
          _self.interworkIndex++;
          _self.itemValue[_self.picker.tap] = '';
          _self.choiceValue[_self.picker.tap] = [];
          _self.$set(_self.interwork, _self.interworkIndex, {
            list: JSON.parse(JSON.stringify(depositsParmList))
          });
          _self.$set(
            _self.picker,
            'list',
            JSON.parse(JSON.stringify(depositsParmList))
          );
          _self.$set(_self.picker, 'value', column.text);
          _self.$set(_self.picker, 'popupChoice', 'checkBox');
          return;
        }
      } else if (_self.picker.type == 'interworkHosPro') {
        if (_self.interworkIndex == 0) {
          let depositsParmData = { inpatientId: column.inpatientId },
            depositsParmList = await _self.queryNurseInpatientOrder(
              depositsParmData
            );
          _self.interworkIndex++;
          _self.itemValue[_self.picker.tap] = '';
          _self.choiceValue[_self.picker.tap] = [];
          _self.$set(_self.interwork, _self.interworkIndex, {
            list: JSON.parse(JSON.stringify(depositsParmList))
          });
          _self.$set(
            _self.picker,
            'list',
            JSON.parse(JSON.stringify(depositsParmList))
          );
          _self.$set(_self.picker, 'value', column.text);
          _self.$set(_self.picker, 'popupChoice', 'radio');
          return;
        } else if (_self.interworkIndex == 1) {
          let depositsParmData = {
              inpatientId: column.inpatientId,
              groupId: column.groupId
            },
            depositsParmList = await _self.queryInPatientNurseFeeSpeci(
              depositsParmData,
              column
            );
          _self.interworkIndex++;
          _self.itemValue[_self.picker.tap] = '';
          _self.choiceValue[_self.picker.tap] = [];
          _self.$set(_self.interwork, _self.interworkIndex, {
            list: JSON.parse(JSON.stringify(depositsParmList))
          });
          _self.$set(
            _self.picker,
            'list',
            JSON.parse(JSON.stringify(depositsParmList))
          );
          _self.$set(_self.picker, 'subValue', column.text);
          _self.$set(_self.picker, 'popupChoice', 'checkBox');
          return;
        }
      } else if (_self.picker.type == 'commonWords') {
        _self.$set(_self.itemData, _self.picker.tap, column.value);
        _self.$nextTick(() => {
          _self.$refs[ref].close();
        });
        return;
      } else if (_self.picker.type == 'interworkTest') {
        _self.$set(
          _self.interworkTestVal[_self.picker.value],
          'text',
          column.text
        );
        _self.$set(_self.interworkTestVal[_self.picker.value], 'value', column);
        _self.interworkIndex--;
        _self.$set(_self.picker, 'showSearch', false);
        _self.$set(_self.picker, 'list', []);
        _self.scrollStyle = '';
        return;
      } else if (
        column.relationParams != null &&
        column.relationParams != undefined &&
        _self.itemData[_self.picker.tap] != column.value
      ) {
        column.relationParams.map(item => {
          _self.$set(_self.itemData, item['fieldName'], '');
          _self.$set(_self.itemValue, item['fieldName'], '');
          _self.$set(_self.choiceData, item['fieldName'], []);
          _self.$set(_self.choiceValue, item['fieldName'], []);
        });
      } else if (
        column.relationValue != null &&
        column.relationValue != undefined
      ) {
        let relation = column.relationValue;
        if (Object.prototype.toString.call(relation) == '[object Array]') {
          relation.map(item => {
            _self.$set(
              _self.itemData,
              item['keyFieldname'],
              item['valueFieldname']
            );
            _self.$set(
              _self.itemValue,
              item['keyFieldname'],
              item['valueFieldname']
            );
            let fieldType = '',
              choiceData = null;
            for (var i in _self.formKeyTemplate) {
              if (
                _self.formKeyTemplate[i]['fieldName'] == item['keyFieldname']
              ) {
                fieldType = _self.formKeyTemplate[i]['fieldType'];
              }
            }
            if (fieldType == 'radio' || fieldType == 'select') {
              choiceData = [
                {
                  value: item['valueFieldname'],
                  text: item['valueFieldname']
                }
              ];
            } else if (fieldType == 'date') {
              choiceData = {
                value: item['valueFieldname'],
                text: item['valueFieldname']
              };
            } else {
              choiceData = item['valueFieldname'];
            }
            _self.$set(_self.choiceData, item['keyFieldname'], choiceData);
            _self.$set(_self.choiceValue, item['keyFieldname'], choiceData);
          });
        }
      }
      if (_self.picker.modeType == 'childForm') {
        let { index, fieldName } = _self.picker.modeOption;
        _self.$set(
          _self.choiceData[_self.picker.tap][index],
          fieldName,
          column.value
        );
        _self.$set(
          _self.choiceValue[_self.picker.tap][index],
          fieldName,
          column.value
        );
        _self.handleChildFormItemDataChange(
          _self.choiceData[_self.picker.tap][index],
          _self.picker.modeOption,
          _self.formTemplate[_self.picker.tempIndex],
          index
        );
      } else {
        _self.$set(_self.itemData, _self.picker.tap, column.value);
        _self.$set(_self.itemValue, _self.picker.tap, column.value);
        _self.$set(_self.choiceData, _self.picker.tap, [
          {
            value: column.value,
            text: column.text
          }
        ]);
        _self.$set(_self.choiceValue, _self.picker.tap, [
          {
            value: column.value,
            text: column.text
          }
        ]);
      }
      _self.$nextTick(() => {
        _self.$refs[ref].close();
        _self.scrollStyle = '';
      });
    },
    //多选
    multipleColumn(ref, column) {
      let _self = this,
        preData = [],
        preValue = [];
      if (_self.picker.type === 'inPatientOrder') {
        if (_self.itemValue[_self.picker.tap].length > 0) {
          preData = [..._self.itemValue[_self.picker.tap]];
        }

        let ids = preData.map(item => item.orderItemId) || [];
        if (ids.includes(column.orderItemId)) {
          let index = ids.findIndex(item => item === column.orderItemId);
          preData.splice(index, 1);
        } else {
          preData.push(column);
        }
        _self.$set(_self.itemValue, _self.picker.tap, preData);
        return;
      }
      if (_self.picker.modeType == 'childForm') {
        let { index, fieldName } = _self.picker.modeOption,
          c_data = _self.choiceData[_self.picker.tap][index][fieldName],
          c_value = _self.choiceValue[_self.picker.tap][index][fieldName];
        preData = [...(c_value ? c_value.split(',') : [])];
        _self.preItemData = c_data;
        _self.preChoiceData = c_data;
        if (preData.includes(column.value)) {
          preData.splice(
            preData.findIndex(item => item === column.value),
            1
          );
        } else {
          preData.push(column.value);
        }
        _self.$set(
          _self.choiceValue[_self.picker.tap][index],
          fieldName,
          preData.join(',')
        );
      } else {
        //获取表单中展示的(弹出层展开前)数据
        preData = [
          ...(_self.itemValue[_self.picker.tap] != ''
            ? _self.itemValue[_self.picker.tap].split(',')
            : [])
        ];
        preValue = [...(_self.choiceValue[_self.picker.tap] || [])];
        _self.preItemData = _self.itemData[_self.picker.tap];
        _self.preChoiceData = _self.choiceData[_self.picker.tap];
        if (preData.includes(column.value)) {
          preData.splice(
            preData.findIndex(item => item === column.value),
            1
          );
          preValue.splice(
            preValue.findIndex(item => item.value === column.value),
            1
          );
        } else {
          preData.push(column.value);
          preValue.push(column);
        }
        _self.$set(_self.itemValue, _self.picker.tap, preData.join(','));
        _self.$set(_self.choiceValue, _self.picker.tap, preValue);
      }
    },
    //多选确定按钮
    async confirmBtn(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        itemValue = _self.itemValue[_self.picker.tap],
        choiceValue = _self.choiceValue[_self.picker.tap];
      if (_self.picker.type == 'interworkPay') {
        itemValue = JSON.stringify(choiceValue);
      } else if (_self.picker.type == 'interworkHosPro') {
        itemValue = JSON.stringify(choiceValue);
      } else if (_self.picker.type == 'inPatientOrder') {
        _self.$set(
          _self.itemData,
          _self.picker.tap,
          itemValue.length ? JSON.stringify(itemValue) : ''
        );
        _self.preItemData = itemValue;
        _self.$set(_self.itemValue, _self.picker.tap, []);
        _self.$nextTick(() => {
          _self.$refs[data.ref].close();
          _self.scrollStyle = '';
        });
        return;
      }
      if (_self.picker.modeType == 'childForm') {
        let { index, fieldName } = _self.picker.modeOption;
        itemValue = choiceValue[index][fieldName];
        _self.$set(
          _self.choiceData[_self.picker.tap][index],
          fieldName,
          itemValue
        );
        _self.handleChildFormItemDataChange(
          _self.choiceData[_self.picker.tap][index],
          _self.picker.modeOption,
          _self.formTemplate[_self.picker.tempIndex],
          index
        );
      } else {
        if (itemValue) {
          _self.$set(_self.itemData, _self.picker.tap, itemValue);
          _self.$set(_self.choiceData, _self.picker.tap, choiceValue);
          _self.preItemData = itemValue;
          _self.preChoiceData = [...choiceValue];
          _self.$set(_self.itemValue, _self.picker.tap, '');
          _self.$set(_self.choiceValue, _self.picker.tap, []);
        } else {
          uni.showToast({
            icon: 'none',
            title: `请先选择`
          });
          return;
        }
      }

      _self.$nextTick(() => {
        _self.$refs[data.ref].close();
        _self.scrollStyle = '';
      });
    },
    //医嘱添加
    addInterworkTest() {
      for (var key in this.interworkTestVal) {
        if (this.interworkTestVal[key].text == '') {
          uni.showToast({
            icon: 'none',
            title: `请选择${this.interworkTestVal[key].name}`
          });
          return false;
        }
      }
      let queryOrderItem = {
          id: this.interworkTestVal.queryOrder.value.id,
          name: this.interworkTestVal.queryOrder.value.name,
          price: this.interworkTestVal.queryOrder.value.price,
          defaultDeptId: this.interworkTestVal.queryOrder.value.defaultDeptId,
          defaultDeptName: this.interworkTestVal.queryOrder.value
            .defaultDeptName
        },
        querySampleItem = {
          sampleCode: this.interworkTestVal.querySample.value.code,
          sampleName: this.interworkTestVal.querySample.value.name
        };
      this.interwork[this.interworkIndex]['list'].push({
        ...queryOrderItem,
        ...querySampleItem
      });
      this.$set(this.interworkTestVal.queryOrder, 'value', {});
      this.$set(this.interworkTestVal.queryOrder, 'text', '');
      this.$set(this.interworkTestVal.querySample, 'value', {});
      this.$set(this.interworkTestVal.querySample, 'text', '');
    },
    deleteInterworkTest(index) {
      this.interwork[this.interworkIndex]['list'].splice(index, 1);
    },
    confirmInterworkTest() {
      if (this.interwork[this.interworkIndex]['list']?.length) {
        this.$set(
          this.itemData,
          this.picker.tap,
          JSON.stringify(this.interwork[this.interworkIndex]['list'])
        );
        this.$set(
          this.choiceData,
          this.picker.tap,
          this.interwork[this.interworkIndex]['list']
        );
      } else {
        uni.showToast({
          title: `请添加医嘱信息`,
          icon: 'none'
        });
        return;
      }

      this.$nextTick(() => {
        this.$refs['popup'].close();
        this.maskConfirm();
      });
    },
    cancelInterworkTest() {
      this.$refs['popup'].close();
      this.maskConfirm();
    },
    //修改病人信息确定点击事件
    confirmSickBtn() {
      let _self = this,
        isChange = false,
        itemValue = '',
        choiceValue = {};
      for (let i in _self.interworkSickChange) {
        if (_self.interworkSickChange[i] != '') {
          isChange = true;
          choiceValue[i] = _self.interworkSickChange[i];
        }
      }
      if (isChange) {
        let before = _self.interwork[_self.interworkIndex].list,
          beforeSick = {
            id: before.id,
            name: before.name,
            sex: before.sex,
            phone: before.phone,
            idcard: before.idcard
          },
          afterSick = {
            id: before.id,
            name: _self.interworkSickChange.name,
            sex: _self.interworkSickChange.sexText
              ? _self.interworkSickChange.sexText == '男'
                ? '1'
                : _self.interworkSickChange.sexText == '女'
                ? '2'
                : '9'
              : '',
            phone: _self.interworkSickChange.phone,
            idcard: _self.interworkSickChange.idcard
          },
          sickValue = {
            before: beforeSick,
            after: afterSick
          };
        _self.$set(_self.itemData, _self.picker.tap, JSON.stringify(sickValue));
        _self.$set(_self.choiceData, _self.picker.tap, sickValue);
        _self.$nextTick(() => {
          _self.$refs['popup'].close();
          _self.interworkSickVal = '';
          _self.$set(_self.interworkSickChange, 'name', '');
          _self.$set(_self.interworkSickChange, 'sexText', '');
          _self.$set(_self.interworkSickChange, 'phone', '');
          _self.$set(_self.interworkSickChange, 'idcard', '');
        });
      } else {
        uni.showToast({
          title: `至少修改一项内容`,
          icon: 'none'
        });
      }
    },
    //弹出遮罩层点击事件
    maskConfirm(popupChoice) {
      let _self = this;
      if (popupChoice == 'checkBox' && this.picker.modeType != 'childForm') {
        _self.$set(_self.itemValue, _self.picker.tap, '');
        _self.$set(_self.choiceValue, _self.picker.tap, []);
        _self.$set(_self.itemData, _self.picker.tap, _self.preItemData);
        _self.$set(_self.choiceData, _self.picker.tap, _self.preChoiceData);
      }
      this.$set(this.interworkTestVal.queryOrder, 'value', {});
      this.$set(this.interworkTestVal.queryOrder, 'text', '');
      this.$set(this.interworkTestVal.querySample, 'value', {});
      this.$set(this.interworkTestVal.querySample, 'text', '');
      _self.interwork = [];
      _self.interworkIndex = 0;
      _self.scrollStyle = '';
      _self.$set(_self.picker, 'showSearch', false);
    },
    changeCollapse(columnIndex, columnType) {
      this.$set(
        this.picker['list'][columnIndex],
        'show',
        columnType == 'true' ? 'false' : 'true'
      );
    },
    async returnInterwork(tap, index) {
      if (index) this.interworkIndex = index;
      else this.interworkIndex--;
      this.$set(
        this.picker,
        'list',
        this.interwork[this.interworkIndex]['list']
      );
      this.$set(
        this.picker,
        'popupChoice',
        this.interwork[this.interworkIndex]['multiselect']
          ? 'checkBox'
          : 'radio'
      );
    },
    //选择人员
    choosePerson(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        personArr = _self.choiceValue[data.tap] || [];
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', function(res) {
        let arrName = [],
          arrId = [];
        res.forEach(item => {
          arrName.push(item.name);
          arrId.push(item.id);
        });
        _self.$set(
          _self.itemData,
          data.tap,
          arrName.join(',') + '--' + arrId.join(',')
        );
        _self.$set(_self.itemValue, data.tap, res);
        _self.$set(_self.choiceData, data.tap, res);
        _self.$set(_self.choiceValue, data.tap, res);
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(personArr));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空人员
    emptyPerson(tap) {
      let _self = this;
      _self.$set(_self.itemData, tap, '');
      _self.$set(_self.itemValue, tap, []);
      _self.$set(_self.choiceData, tap, []);
      _self.$set(_self.choiceValue, tap, []);
    },
    //选择部门
    chooseDept(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        personArr = _self.choiceValue[data.tap] || [];
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('deptlist', function(res) {
        let arrName = [],
          arrId = [];
        res.forEach(item => {
          arrName.push(item.name);
          arrId.push(item.id);
        });
        _self.$set(
          _self.itemData,
          data.tap,
          arrName.join(',') + '--' + arrId.join(',')
        );
        _self.$set(_self.itemValue, data.tap, res);
        _self.$set(_self.choiceData, data.tap, res);
        _self.$set(_self.choiceValue, data.tap, res);
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('deptlist');
      });
      uni.setStorageSync('dept_list', JSON.stringify(personArr));
      uni.navigateTo({
        url: '/pages/selectDept/select-dept?checkType=checkBox'
      });
    },
    //删除所选部门
    deleteDept(tap, id) {
      let _self = this;
      _self.itemValue[tap] = _self.itemValue[tap].filter(item => item.id != id);
      _self.choiceData[tap] = _self.choiceData[tap].filter(
        item => item.id != id
      );
      _self.choiceValue[tap] = _self.choiceValue[tap].filter(
        item => item.id != id
      );
      let itemDataStr = _self.choiceValue[tap].map(item => {
        return item.name;
      });
      _self.itemData[tap] = itemDataStr.join(',');
    },
    //上传图片
    addFile(tap, index) {
      let _self = this;
      chooseFile({
        limitNum: 9, //数量
        uploadFileUrl: `${_self.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=hrm`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res),
            fileList = resVal.object.map(i => {
              let nameList = i.fileName.split('.');
              return {
                fileRealName: i.fileRealName,
                fileName: i.fileName,
                fileSize: i.fileSize,
                fileUrl: i.filePath,
                fileId: i.fileId,
                fileType:
                  nameList.length > 1 ? nameList[nameList.length - 1] : ''
              };
            }),
            list = _self.itemData[tap] ? _self.itemData[tap].split(',') : [];
          if (_self.formTemplate[index].fieldType != 'comment') {
            _self.itemData[tap] = list.concat(fileList[0].fileId).join(',');
          }
          _self.itemValue[tap] = _self.itemValue[tap].concat(fileList);
          _self.choiceData[tap] = _self.choiceData[tap].concat(fileList);
          _self.choiceValue[tap] = _self.choiceValue[tap].concat(fileList);
        }
      });
    },
    //删除图片
    deletFile(tap, id) {
      let _self = this,
        list = _self.itemData[tap].split(',');
      _self.itemData[tap] = list.filter(item => item != id).join(',');
      _self.itemValue[tap] = _self.itemValue[tap].filter(
        item => item.fileId != id
      );
      _self.choiceData[tap] = _self.choiceData[tap].filter(
        item => item.fileId != id
      );
      _self.choiceValue[tap] = _self.choiceValue[tap].filter(
        item => item.fileId != id
      );
    },
    // 子表单添加一行
    addChildFormRow(data) {
      let newObj = {},
        fieldName = data.fieldName;
      data.childFormColumns.map(item => {
        let { defaultValue = '' } = item;
        newObj[item.fieldName] =
          item.fieldType == 'EXPRESSION' ? '' : defaultValue;
      });
      this.choiceData[fieldName].push(newObj);
    },
    /**
     * @desc 子表单数据改变
     * @param {object} rowData 本行数据
     * @param {object} col 本列配置
     * @param {object} item 本子表单配置字
     */
    handleChildFormItemDataChange(rowData, col, item, index) {
      if (col.fieldType == 'NUMBER') {
        let { pointLength = '', fieldName } = col,
          value = rowData[fieldName];

        let newVal = parseFloat(value);
        if (isNaN(newVal)) {
          value = '';
        } else if (value) {
          if (newVal == 0 || !pointLength) {
            value = parseFloat(value);
          } else {
            value = newVal.toFixed(Number(pointLength));
          }
        }
        this.choiceData[item.fieldName][index][fieldName] = value || '';
      }
      // 公式计算
      let expressionList = item.childFormColumns.filter(
        item => item.fieldType == 'EXPRESSION'
      );
      if (expressionList.length) {
        expressionList.map(setting => {
          let { funStr, relatedAttrs = [] } = setting.formula || {};
          if (!relatedAttrs.includes(col.fieldName)) {
            return;
          }
          let value = funStr(rowData);
          this.$set(rowData, setting.fieldName, value);

          let setInput = document.querySelector(
            `#expressionInputIndex${index}Id${setting.id} input`
          );
          setInput.value = value;
          const event = new Event('input', {
            bubbles: true,
            cancelable: true
          });
          setInput.dispatchEvent(event);
        });
      }
    },
    /**
     * @desc 通过 BusinessId 作为key值的文件上传
     * @param {object} rowData 行数据，或者说要修改的数据
     * @param {string} key 关键词
     * @param {object} prop 配置
     */
    uploadByBusinessId(rowData, key, prop) {
      let businessId = rowData[key] || this.$common.createUUID();
      chooseFile({
        limitNum: 9, //数量
        uploadFileUrl:
          `${_self.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=oa&businessId=` +
          businessId, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res),
            fileList = resVal.object.map(i => {
              let nameList = i.fileName.split('.');
              return {
                fileRealName: i.fileRealName,
                fileName: i.fileName,
                fileSize: i.fileSize,
                fileUrl: i.filePath,
                fileId: i.fileId,
                fileType:
                  nameList.length > 1 ? nameList[nameList.length - 1] : ''
              };
            });
        }
      });
    },
    //表格插入一行
    addRow(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        obj = _self.choiceData[data.tap][0],
        newObj = {};
      for (let i in obj) {
        newObj[i] = '';
      }
      _self.choiceData[data.tap].push(newObj);
    },
    //表格删除一行
    deletRow(e) {
      let _self = this,
        data = e.currentTarget.dataset;
      _self.choiceData[data.tap].splice(Number(data.index), 1);
      if (this.childFormCountList.length == 0) return;
      this.childConutFormFun(data, data.tap);
    },
    handleChildFormInput(e, index, col, name) {
      let value = e.target.value,
        { fieldName, fieldType, pointLength = '' } = col;
      if (fieldType === 'NUMBER' || fieldType === 'EXPRESSION') {
        // let reg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/;
        // value = (value.match(reg) || [])[0];
        let isNegative = value.indexOf('-') == 0;
        let newVal =
          (isNegative ? '-' : '') +
          value
            .replace(/[^\d\.]/g, '')
            .replace('.', '$#$')
            .replace(/\./g, '')
            .replace('$#$', '.');
        if (pointLength) {
          let matchList = newVal.match(/\d+/g) || [];
          if (matchList.length > 1 && matchList[1].length >= pointLength) {
            matchList[1] = matchList[1].slice(0, pointLength);
            value = matchList.join('.');
            if (isNegative) {
              value = '-' + value;
            }
          }
        }
      }
      this.$nextTick(() => {
        this.choiceData[name][index][fieldName] = value || '';
        if (this.childFormCountList.length == 0) return;
        this.childConutFormFun(col, name);
      });
    },
    childConutFormFun(col, name) {
      let list = this.childFormCountList.filter(e => e.tableId == col.tableId);
      list.forEach(e => {
        let sum = this.choiceData[name].reduce(
          (total, i) => total + Number(i[e.sumField] * 10000),
          0
        );
        this.$set(this.itemData, e.fieldName, sum / 10000);
      });
    },
    changeItemData(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        value = e.target.value;
      _self.choiceData[data.tap][data.index][data.key] = value;
      _self.itemData[data.tap] = JSON.stringify(_self.choiceData[data.tap]);
    },
    showLowerNumber(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        value = _self.formTemplate[data.index]['lowerNumber'];
      if (data.makeBigger == 'Y') _self.itemData[data.tap] = value;
    },
    replaceInput(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        value = e.target.value;
      value = value.replace(/[^(\-?\d+\.?\d+)]/g, '');
      let n = value.split('.')[1] ? true : false;
      if (n) {
        let valArr = value.split('.');
        value = valArr[0] + '.' + valArr[1];
      }
      setTimeout(() => {
        _self.itemData[data.tap] = value;
        let changeFiled = data.inputChangeFiled;
        if (changeFiled && changeFiled != undefined) {
          _self.calculatBlur(
            changeFiled.keyId,
            changeFiled.fieldName,
            changeFiled.calculationRole
          );
        }
      }, 0);
    },
    //数字转换
    changNumber(e) {
      let _self = this,
        data = e.currentTarget.dataset,
        value,
        //去除空格和千分位，
        valueStr = e.target.value.replace(/\s+/g, '').replace(/,/g, ''),
        lowerValue = '',
        isNegativeNum,
        reg = /^-?\d+(?:\.\d+)?$/;
      let list = /(-?\d+)(\.\d+)?/.exec(valueStr);
      if (list) {
        valueStr = list[0];
      } else {
        valueStr = '';
      }
      //判断是否为纯数字，只有纯数值才做处理（包含负数和小数）
      if (reg.test(valueStr)) {
        if (data.max != '' && Number(valueStr) > Number(data.max)) {
          valueStr = data.max;
          uni.showToast({
            title: `不能超过最大值，最大值为${data.max}`,
            icon: 'none'
          });
        } else if (data.min != '' && Number(valueStr) < Number(data.min)) {
          valueStr = data.min;
          uni.showToast({
            title: `不能小于最小值，最小值为${data.min}`,
            icon: 'none'
          });
        }
        isNegativeNum = valueStr.indexOf('-') != -1 ? true : false;
        //去除负数符号-（不直接取绝对值，避免任意去掉整数位前面的0）
        valueStr = isNegativeNum ? valueStr.substring(1) : valueStr;
        let absValue = Math.abs(Number(valueStr)),
          absValueStr,
          absLowerValueStr;
        //数值需要做处理才会使用绝对值（除去小数位数不限制和小数位数为0）
        if (data.treatmentMethod == 'floor') {
          //向下取整
          absValueStr = Math.floor(absValue).toString();
        } else if (data.treatmentMethod == 'ceil') {
          //向上取整
          absValueStr = Math.ceil(absValue).toString();
        } else {
          //四舍五入
          //获取字符串被小数点分割后的第二串字符串长度
          let decimalDigit =
            data.decimalDigit != ''
              ? Number(data.decimalDigit)
              : valueStr.split('.')[1]
              ? valueStr.split('.')[1].length
              : 0;
          absValue =
            Math.round(absValue * Math.pow(10, decimalDigit)) /
            Math.pow(10, decimalDigit);
          absValueStr = absValue.toString();
          let pointNum = absValueStr.indexOf('.');
          if (pointNum < 0 && decimalDigit > 0) {
            absValueStr += '.';
          }
          for (
            var i = absValueStr.length - absValueStr.indexOf('.');
            i <= decimalDigit;
            i++
          ) {
            absValueStr += '0';
          }
        }
        if (data.makeBigger == 'Y') {
          absLowerValueStr = absValueStr;
          //转中文大写
          value = convertCapital(absValueStr);
        } else if (data.thousandth == 'Y') {
          //千分位
          let pos_decimal = absValueStr.indexOf('.');
          if (pos_decimal < 0) {
            value = absValueStr.replace(/[1-9]\d{0,2}(?=(\d{3})+$)/g, '$&,');
          } else {
            value =
              absValueStr
                .substring(0, pos_decimal)
                .replace(/[1-9]\d{0,2}(?=(\d{3})+$)/g, '$&,') +
              absValueStr.substr(pos_decimal);
          }
        } else {
          value = absValueStr;
        }
        if (data.makeBigger == 'Y') {
          _self.itemData[data.tap] = isNegativeNum
            ? `负${value}`
            : value.toString();
          lowerValue = isNegativeNum
            ? `-${absLowerValueStr}`
            : absLowerValueStr;
        } else {
          _self.itemData[data.tap] = isNegativeNum
            ? `-${value}`
            : value.toString();
        }
      } else {
        _self.itemData[data.tap] = valueStr;
      }
      _self.formTemplate[data.index]['lowerNumber'] = lowerValue;
    },

    //自动计算
    calculatBlur(keyId, tap, calRole) {
      let func = formAutoCalculat.parseCalRules(
          calRole,
          this.formKeyTemplate,
          this.itemData
        ),
        val = func.fun.bind(formAutoCalculat)();
      this.itemData[tap] = String(val);
      let changeFiled = this.formKeyTemplate[keyId].inputChangeFiled;
      if (changeFiled != undefined) {
        this.calculatBlur(
          changeFiled.keyId,
          changeFiled.fieldName,
          changeFiled.calculationRole
        );
      }
    },
    //验证表单
    checkFrom(opt) {
      let commonData = {},
        rule = Object.values(this.dataRequired),
        checkRes = graceChecker.check(this.itemData, rule);
      if (!checkRes) {
        this.handleCheckErr();
        return;
      }
      if (
        this.commentFileRequired &&
        !this.choiceData[this.commentValTap]?.length
      ) {
        graceChecker.error = this.dataRequired[
          this.commentValTap
        ].otherErrorMsg;
        this.handleCheckErr();
        return;
      }
      if (
        checkRes &&
        this.formTemplate.some(item => item.fieldType == 'childForm')
      ) {
        //校验子表单中是否有 必填字段未填
        let childFormTempList = this.formTemplate.filter(
            item => item.fieldType == 'childForm'
          ),
          childTableData = [],
          errorIndex = childFormTempList.findIndex(temp => {
            let requiredColumns = temp.childFormDetail.fields
                .filter(
                  item =>
                    item.isNull == 1 && item.pcShow == 1 && item.isReadOnly != 1
                )
                .map(item => item.fieldName),
              tableId = temp.childFormDetail.id,
              childTableList = this.choiceValue[temp.fieldName] || [],
              check = false;
            childTableList.map(data => {
              if (requiredColumns.some(key => !data[key])) {
                check = true;
              }
            });

            // 格式化子表单储存数据
            childTableData.push(
              JSON.stringify({
                tableId,
                fieldName: temp.fieldName,
                childTableList
              })
            );
            delete this.itemData[temp.fieldName];
            return check;
          });
        if (errorIndex >= 0) {
          graceChecker.error = '子表单必填字段不能为空';
          this.handleCheckErr();
          return;
        } else {
          commonData.childTableData = childTableData;
        }
      }
      if (
        checkRes &&
        this.formTemplate.some(item => item.fieldType == 'essentialDrugDic')
      ) {
        let validateTemplateList = this.formTemplate.filter(
            item => item.fieldType == 'essentialDrugDic'
          ),
          check = '';
        validateTemplateList.some(setting => {
          if (this.itemData[setting.fieldName]) {
            let dataList = JSON.parse(this.itemData[setting.fieldName]) || [];
            if (!dataList.length) {
              check = '基本药品字典必填，请至少选择一条数据';
            } else if (
              dataList.some(
                item =>
                  item.num == null || item.num == undefined || item.num == ''
              )
            ) {
              check = '基本药品字典所选药品数量必填';
            }
          }
        });
        if (check) {
          graceChecker.error = check;
          this.handleCheckErr();
          return;
        }
      }
      if (
        checkRes &&
        this.formTemplate.some(item => item.fieldType == 'medicalSupplieDic')
      ) {
        let validateTemplateList2 = this.formTemplate.filter(
            item => item.fieldType == 'medicalSupplieDic'
          ),
          check = '';
        validateTemplateList2.some(setting => {
          if (this.itemData[setting.fieldName]) {
            let dataList = JSON.parse(this.itemData[setting.fieldName]) || [];
            if (!dataList.length) {
              check = setting.showName + '必填，请至少选择一条数据';
            } else {
              let requiredField = [
                {
                  prop: 'chemicalName',
                  lable: '品名'
                },
                {
                  prop: 'spec',
                  lable: '规格'
                },
                {
                  prop: 'unitName',
                  lable: '单位'
                },
                {
                  prop: 'factName',
                  lable: '生产厂家'
                },
                {
                  prop: 'supplierName',
                  lable: '供货单位'
                },
                {
                  prop: 'num',
                  lable: '采购数量'
                },
                {
                  prop: 'buyPrice',
                  lable: '预算单价'
                },
                {
                  prop: 'total',
                  lable: '预算金额'
                },
                {
                  prop: 'purchaseType',
                  lable: '采购方式'
                }
              ];
              let nullField = requiredField.find(fieldItem => {
                return dataList.some(
                  item =>
                    item[fieldItem.prop] == '' ||
                    item[fieldItem.prop] == null ||
                    item[fieldItem.prop] == undefined
                );
              });
              if (nullField) {
                check =
                  '所选' + setting.fieldName + '的' + nullField.lable + '必填';
              }
            }
          }
        });
        if (check) {
          graceChecker.error = check;
          this.handleCheckErr();
          return;
        }
      }
      if (
        checkRes &&
        this.formTemplate.some(item => item.fieldType == 'consultation')
      ) {
        let validateTemplateList3 = this.formTemplate.filter(
            item => item.fieldType == 'consultation'
          ),
          check = '';
        validateTemplateList3.some(setting => {
          if (this.itemData[setting.fieldName]) {
            let dataList = JSON.parse(this.itemData[setting.fieldName]) || [];
            if (!dataList.length) {
              check = setting.showName + '必填，请至少添加一条数据';
            } else {
              let requiredField = [
                {
                  prop: 'deptId',
                  lable: '邀请会诊科室'
                },
                {
                  prop: 'typeId',
                  lable: '邀请会诊级别'
                }
              ];
              let nullField = requiredField.find(fieldItem => {
                return dataList.some(
                  item =>
                    item[fieldItem.prop] == '' ||
                    item[fieldItem.prop] == null ||
                    item[fieldItem.prop] == undefined
                );
              });
              if (nullField) {
                check = setting.showName + '的' + nullField.lable + '必填';
              }
            }
          }
        });
        if (check) {
          graceChecker.error = check;
          this.handleCheckErr();
          return;
        }
      }

      if (this.commentValTap) {
        commonData = Object.assign(commonData, {
          remark: this.itemData[this.commentValTap],
          approvalFiled: this.commentValTap,
          taskFileList: this.choiceData[this.commentValTap]
        });
      }
      if (checkRes && this.fieldSerialNumberRoleId != '') {
        this.itemData.fieldSerialNumberRoleId = this.fieldSerialNumberRoleId;
      } else {
        this.itemData.fieldSerialNumberRoleId = '';
      }
      this.$emit('afterCheck', checkRes, this.itemData, commonData, opt);
    },

    handleCheckErr() {
      uni.showToast({ title: graceChecker.error, icon: 'none' });
      this.$emit('afterCheck', false);
    },
    //预览文件
    previewFile(id, fileName, file, fileList = []) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (!fileList.length) {
        fileList = [file];
      }
      uni.setStorageSync('fileList', fileList);
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    connectionFile(id, fileName) {
      this.ajax.saveCollect({ collectId: id }).then(res => {
        uni.showToast({
          icon: 'none',
          title: res.message || '收藏成功,已收藏到个人文档'
        });
      });
    }
  }
};
