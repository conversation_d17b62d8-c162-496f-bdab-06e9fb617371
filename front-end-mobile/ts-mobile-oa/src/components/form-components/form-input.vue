<template>
  <view class="form_content" v-if="showContent">
    <view class="form">
      <view
        class="row_tab"
        v-for="(item, index) in formTemplate"
        :key="item.fieldName"
      >
        <!-- DHWB(单行文本) -->
        <view class="form_row dis_flex" v-if="item.fieldType == 'input'">
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value row_value_input" v-if="item.dataSource == 3">
            <input
              class="row_value_input_text"
              :field-name="item.fieldName"
              :disabled="item.isReadonly == 'Y'"
              v-model="itemData[item.fieldName]"
              @blur="
                calculatBlur(item.keyId, item.fieldName, item.calculationRole)
              "
              :placeholder="item.placeholderContent"
            />
          </view>
          <view class="row_value row_value_input" v-else>
            <input
              class="row_value_input_text"
              :field-name="item.fieldName"
              v-model="itemData[item.fieldName]"
              :disabled="item.isReadonly == 'Y'"
              :placeholder="item.placeholderContent"
              @input="
                () =>
                  item.inputChangeFiled != undefined &&
                  calculatBlur(
                    item.inputChangeFiled.keyId,
                    item.inputChangeFiled.fieldName,
                    item.inputChangeFiled.calculationRole
                  )
              "
            />
          </view>
        </view>
        <!-- DHWB(数字) -->
        <view class="form_row dis_flex" v-if="item.fieldType == 'number'">
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value row_value_input">
            <input
              class="row_value_input_text"
              :field-name="item.fieldName"
              v-if="item.dataSource == 3"
              :disabled="item.isReadonly == 'Y'"
              :placeholder="item.placeholderContent"
              v-model="itemData[item.fieldName]"
              @blur="
                calculatBlur(item.keyId, item.fieldName, item.calculationRole)
              "
            />
            <input
              v-else
              class="row_value_input_text"
              :field-name="item.fieldName"
              type="text"
              v-model="itemData[item.fieldName]"
              :disabled="item.isReadonly == 'Y'"
              @blur="changNumber"
              @input="replaceInput"
              @focus="showLowerNumber"
              :data-index="index"
              :data-tap="item.fieldName"
              :data-thousandth="item.isThousandth"
              :data-make-bigger="item.isMakeBigger"
              :data-treatment-method="item.treatmentMethod"
              :data-decimal-digit="item.decimalDigit"
              :data-min="item.min"
              :data-max="item.max"
              :data-input-change-filed="item.inputChangeFiled"
              :placeholder="item.placeholderContent"
            />
          </view>
        </view>
        <!-- WBY(文本域)-->
        <view class="form_row" v-else-if="item.fieldType == 'textarea'">
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value row_value_textarea">
            <textarea
              class="row_value_textarea_text"
              :field-name="item.fieldName"
              v-if="item.dataSource == 3"
              v-model="itemData[item.fieldName]"
              maxlength="-1"
              @blur="
                calculatBlur(item.keyId, item.fieldName, item.calculationRole)
              "
              :disabled="item.isReadonly == 'Y'"
              :placeholder="item.placeholderContent"
            />
            <textarea
              class="row_value_textarea_text"
              :field-name="item.fieldName"
              v-else
              v-model="itemData[item.fieldName]"
              maxlength="-1"
              :disabled="item.isReadonly == 'Y'"
              :placeholder="item.placeholderContent"
              @input="
                () =>
                  item.inputChangeFiled != undefined &&
                  calculatBlur(
                    item.inputChangeFiled.keyId,
                    item.inputChangeFiled.fieldName,
                    item.inputChangeFiled.calculationRole
                  )
              "
            />
          </view>
        </view>
        <!-- XLK(下拉框) 、DXK(单选框)-->
        <view
          class="form_row dis_flex"
          v-else-if="
            item.fieldType == 'select' ||
              item.fieldType == 'radio' ||
              item.fieldType == 'workorderSetting'
          "
        >
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view
            v-if="item.isReadonly != 'Y'"
            class="row_value row_value_input"
            :data-tap="item.fieldName"
            data-ref="popup"
            :data-data-source="item.dataSource"
            data-popup-type="bottom"
            data-popup-choice="radio"
            :data-options="item.optionList"
            :data-search-params="item.searchParams || []"
            :data-search-api="item.searchApi || ''"
            @click="showPopup"
          >
            <input
              class="row_value_input_text row_value-select"
              :field-name="item.fieldName"
              :disabled="true"
              :placeholder="item.placeholderContent"
              :value="choiceData[item.fieldName] | pickerValueFilter"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
          <view v-else class="row_value row_value_input">
            <input
              class="row_value_input_text"
              :field-name="item.fieldName"
              :disabled="true"
              :placeholder="item.placeholderContent"
              :value="choiceData[item.fieldName] | pickerValueFilter"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <!-- FXK(复选框) -->
        <view
          class="form_row dis_flex"
          v-else-if="item.fieldType == 'checkbox'"
        >
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view
            v-if="item.isReadonly != 'Y'"
            class="row_value row_value_input"
            :data-tap="item.fieldName"
            data-ref="popup"
            data-popup-type="bottom"
            data-popup-choice="checkBox"
            :data-options="item.optionList"
            @click="showPopup"
          >
            <view
              v-if="
                choiceData[item.fieldName] &&
                  choiceData[item.fieldName].length > 0
              "
              class="choice_list"
            >
              <text
                class="choice_item"
                v-for="(t, i) in choiceData[item.fieldName]"
                :key="t.text + '_' + i"
              >
                {{ t.text }}
              </text>
            </view>
            <input
              v-else
              class="row_value_input_text row_value-select"
              :disabled="true"
              :placeholder="item.placeholderContent"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
          <view v-else class="row_value row_value_input">
            <view
              v-if="
                choiceData[item.fieldName] &&
                  choiceData[item.fieldName].length > 0
              "
              class="choice_list"
            >
              <text
                class="choice_item"
                v-for="(t, i) in choiceData[item.fieldName]"
                :key="t.text + '_' + i"
              >
                {{ t.text }}
              </text>
            </view>
            <input
              v-else
              class="row_value_input_text row_value-select"
              :disabled="true"
              :placeholder="item.placeholderContent"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <!-- RQ(日期) -->
        <view class="form_row dis_flex" v-else-if="item.fieldType == 'date'">
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view
            v-if="item.isReadonly != 'Y'"
            class="row_value row_value_input"
            :data-tap="item.fieldName"
            data-ref="date"
            data-mode="date"
            :data-fields="item.dataType ? item.dataType : 'day'"
            :data-value="itemData[item.fieldName]"
            @click="showPicker"
          >
            <input
              class="row_value_input_text row_value-select"
              :field-name="item.fieldName"
              :disabled="true"
              type="text"
              :value="choiceData[item.fieldName] | pickerValueFilter"
              :placeholder="item.placeholderContent"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
          <view v-else class="row_value row_value_input">
            <input
              class="row_value_input_text row_value-select"
              :field-name="item.fieldName"
              :disabled="true"
              type="text"
              :value="choiceData[item.fieldName] | pickerValueFilter"
              :placeholder="item.placeholderContent"
            />
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
            />
          </view>
        </view>
        <!-- WJMB(文件模板) -->
        <view
          class="form_row dis_flex"
          v-else-if="item.fieldType == 'fileTemplate'"
        >
          <view class="row_lable">
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value fileTemplate">
            <view class="fiel_list">
              <view
                class="file_item"
                v-for="t in item.fileList"
                :key="t.id"
                @click="previewFile(t.id, t.fileName, item.fileList)"
              >
                <text
                  class="oa-icon"
                  :class="`oa-icon-${$oaModule.formatFileType(t.fileType)}`"
                ></text>
                <text class="file_name">{{ t.fileName }}</text>
              </view>
            </view>
          </view>
        </view>
        <view class="form_row" v-else-if="item.fieldType == 'remark'">
          <!-- <view class="row_lable" style="width: 100%;">
            <text class="row_lable_text">{{ item.showName }}</text>
          </view> -->
          <view
            class="row_value remark"
            :class="{
              'red-text': item.isRed,
              'bold-text': item.isBold
            }"
          >
            <rich-text
              :nodes="item.remark.replace(/(\r\n)|(\n)/g, '<br>')"
            ></rich-text>
          </view>
        </view>
        <!-- FJSC(附件上传) -->
        <view class="form_row" v-else-if="item.fieldType == 'file'">
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
            <view
              class="file_add_icon oa-icon oa-icon-tupiantianjia"
              v-if="item.isReadonly != 'Y'"
              @click="addFile(item.fieldName, index)"
            ></view>
          </view>
          <view class="row_value file_list">
            <view
              class="file_item"
              v-for="t in choiceData[item.fieldName]"
              :key="t.fileId"
            >
              <view
                class="file_item_info"
                @click="
                  previewFile(
                    t.fileId,
                    t.fileRealName,
                    choiceData[item.fieldName]
                  )
                "
              >
                <view
                  class="oa-icon"
                  :class="'oa-icon-' + $oaModule.formatFileType(t.fileType)"
                ></view>
                <view class="file_item_name">
                  <text class="file_name">{{ t.fileName }}</text>
                  <text class="file_size">{{
                    t.fileSize | fileSizeFilter
                  }}</text>
                </view>
              </view>
              <text
                class="oa-icon oa-icon-xiazai delete_file"
                @click.stop="downloadFile(t.fileId, t.fileRealName)"
              ></text>
              <text
                class="oa-icon oa-icon-guanbi delete_file"
                @click.stop="deletFile(item.fieldName, t.fileId)"
              ></text>
              <text
                class="oa-icon oa-icon-wodeshoucang delete_file"
                @click.stop="connectionFile(t.fileId, t.fileRealName)"
              ></text>
            </view>
          </view>
        </view>
        <!-- CHOOSEDEPT(部门选择) -->
        <view class="form_row" v-else-if="item.fieldType == 'deptChose'">
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
            <text
              class="add_icon oa-icon oa-icon-tianjiachaosong"
              v-if="item.isReadonly != 'Y'"
              :data-tap="item.fieldName"
              @click="chooseDept"
            ></text>
          </view>
          <view class="row_value row_value_dept">
            <view
              v-if="
                choiceData[item.fieldName] &&
                  choiceData[item.fieldName].length > 0
              "
              class="choice_list"
            >
              <view
                class="choice_item"
                v-for="t in choiceData[item.fieldName]"
                :key="t.id"
              >
                <text
                  class="choice_item_text"
                  :data-tap="item.fieldName"
                  @click="chooseDept"
                >
                  {{ t.name }}
                </text>
                <uni-icons
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#005BAC"
                  type="closeempty"
                  @click="deleteDept(item.fieldName, t.id)"
                />
              </view>
            </view>
          </view>
        </view>
        <!-- CHOOSEUSER(人员选择) -->
        <view class="form_row" v-else-if="item.fieldType == 'personChose'">
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
            <text
              class="add_icon oa-icon oa-icon-tianjiachaosong"
              v-if="item.isReadonly != 'Y'"
              :data-tap="item.fieldName"
              @click="choosePerson"
            ></text>
          </view>
          <view class="row_value row_value_personal">
            <text
              class="personNameStr"
              :data-tap="item.fieldName"
              @click="choosePerson"
              >{{ choiceData[item.fieldName] | personFilter }}</text
            >
            <uni-icons
              v-if="
                itemValue[item.fieldName] &&
                  itemValue[item.fieldName].length > 0
              "
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="closeempty"
              @click="emptyPerson(item.fieldName)"
            />
          </view>
        </view>
        <!-- CHOOSEPROCESS(流程选择) -->
        <view class="form_row" v-else-if="item.fieldType == 'processChoose'">
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
            <text
              class="add_icon oa-icon oa-icon-tianjiachaosong"
              v-if="item.isReadonly != 'Y'"
              :data-tap="item.fieldName"
              :data-key-id="item.keyId"
              @click="chooseProcess"
            ></text>
          </view>
          <view class="row_value row_value_process">
            <view
              v-if="
                choiceData[item.fieldName] &&
                  choiceData[item.fieldName].length > 0
              "
              class="choice_list"
            >
              <view
                class="choice_item"
                v-for="t in choiceData[item.fieldName]"
                :key="t.wfInstanceId"
              >
                <text class="choice_item_text" @click="checkProcessDetail(t)">
                  {{ `${t.workflowName} - ${t.createDate}` }}
                </text>
                <uni-icons
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#005BAC"
                  type="closeempty"
                  @click="deleteProcess(item.fieldName, t.wfInstanceId)"
                />
              </view>
            </view>
          </view>
        </view>
        <!-- NQBG(内嵌表格) -->
        <view class="form_row" v-else-if="item.fieldType == 'table'">
          <view class="table_title">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text>{{ item.showName }}</text>
          </view>
          <view class="table">
            <view
              class="row_group"
              v-for="(t, i) in choiceData[item.fieldName]"
              :key="i"
            >
              <view class="table_item_title">
                <text>第{{ i + 1 }}组</text>
                <text
                  class="delet_meeting_room_btn"
                  v-if="i === 0"
                  :data-tap="item.fieldName"
                  @click="addRow"
                  >添加</text
                >
                <text
                  class="delet_meeting_room_btn"
                  v-else
                  :data-tap="item.fieldName"
                  :data-index="i"
                  @click="deletRow"
                  >删除</text
                >
              </view>
              <view
                class="table_item dis_flex"
                v-for="(col, cKey, cIndex) in choiceData[item.fieldName][i]"
                :key="cIndex"
              >
                <view class="row_lable">
                  <text class="row_lable_text">{{ cKey }}</text>
                </view>
                <view class="row_value row_value_input">
                  <input
                    class="row_value_input_text"
                    placeholder="请输入"
                    :value="col"
                    :data-tap="item.fieldName"
                    :data-index="i"
                    :data-key="cKey"
                    @input="changeItemData"
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
        <!--COMMENT(审批意见)-->
        <view
          class="form_row"
          v-else-if="item.fieldType == 'comment' && item.isReadonly != 'Y'"
        >
          <view class="row_lable" style="width: 100%;">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
            <view
              class="common_words"
              data-ref="popup"
              data-popup-type="bottom"
              data-popup-choice="commonWords"
              :data-tap="item.fieldName"
              @tap="showPopup"
            >
              常用语
              <uni-icons type="arrowup" color="#005BAC" size="30" />
            </view>
          </view>
          <view class="row_value row_value_textarea">
            <textarea
              class="row_value_textarea_text"
              v-model="itemData[item.fieldName]"
              maxlength="-1"
              :placeholder="item.placeholderContent"
            />
          </view>
          <view>
            <view
              class="row_lable"
              style="width: 100%;"
              v-if="item.isReadonly != 'Y' && !item.isHideCommentFile"
            >
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isReadonly != 'Y' && item.isMustCommentFile"
              ></text>
              <text class="row_lable_text">上传附件</text>
              <view
                class="file_add_icon oa-icon oa-icon-tupiantianjia"
                @tap="addFile(item.fieldName, index)"
              ></view>
            </view>
            <view class="row_value file_list">
              <view
                class="file_item"
                v-for="t in choiceData[item.fieldName]"
                :key="t.fileId"
              >
                <view
                  class="file_item_info"
                  @click="
                    previewFile(
                      t.fileId,
                      t.fileRealName,
                      t,
                      choiceData[item.fieldName]
                    )
                  "
                >
                  <view
                    class="oa-icon"
                    :class="'oa-icon-' + $oaModule.formatFileType(t.fileType)"
                  ></view>
                  <view class="file_item_name">
                    <text class="file_name">{{ t.fileName }}</text>
                    <text class="file_size">{{
                      t.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                </view>
                <text
                  class="oa-icon oa-icon-xiazai delete_file"
                  @click.stop="downloadFile(t.fileId, t.fileRealName)"
                ></text>
                <text
                  class="oa-icon oa-icon-guanbi delete_file"
                  @click.stop="deletFile(item.fieldName, t.fileId)"
                ></text>
                <text
                  class="oa-icon oa-icon-wodeshoucang delete_file"
                  @click.stop="connectionFile(t.fileId, t.fileRealName)"
                ></text>
              </view>
            </view>
          </view>
        </view>
        <!-- (签名) -->
        <view
          class="form_row dis_flex"
          v-else-if="item.fieldType == 'signature'"
        >
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view
            class="row_value"
            :class="{
              row_value_image: itemData[item.fieldName].includes(
                '/ts-basics-bottom/fileAttachment/downloadFile'
              )
            }"
          >
            <image
              v-if="
                itemData[item.fieldName].includes(
                  '/ts-basics-bottom/fileAttachment/downloadFile'
                )
              "
              :src="
                itemData[item.fieldName]
                  ? $config.BASE_HOST + itemData[item.fieldName]
                  : ''
              "
              mode="aspectFit"
            ></image>
            <template v-else>{{ itemData[item.fieldName] }}</template>
          </view>
        </view>
        <!-- CHILDFORM(子表单) -->
        <view class="form_row" v-else-if="item.fieldType == 'childForm'">
          <view class="table_title">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text>{{ item.showName }}</text>
          </view>
          <view class="table">
            <view
              class="row_group"
              v-for="(val, i) in choiceData[item.fieldName]"
              :key="i"
            >
              <view class="table_item_title">
                <text>第{{ i + 1 }}组</text>
                <view
                  v-if="
                    currentStepNo == 'start' ||
                      (currentStepNo != 'start' &&
                        item.childFormDetail &&
                        JSON.stringify(item.childFormDetail) != '{}' &&
                        item.childFormDetail.editLine == '1')
                  "
                >
                  <text
                    class="delet_meeting_room_btn"
                    v-if="choiceData[item.fieldName].length > 1"
                    :data-tap="item.fieldName"
                    :data-table-id="item.tableId"
                    :data-index="i"
                    @click="deletRow"
                  >
                    删除
                  </text>
                  <text
                    class="delet_meeting_room_btn"
                    v-if="i === choiceData[item.fieldName].length - 1"
                    @click="addChildFormRow(item)"
                  >
                    添加
                  </text>
                </view>
              </view>
              <view
                class="table_item child-form-item"
                v-for="(col, cIndex) of item.childFormColumns || []"
                :key="cIndex"
                :class="{
                  dis_flex: !['FILE', 'TEXTAREA'].includes(col.fieldType),
                  disabled: col.isReadOnly
                }"
              >
                <view class="row_lable">
                  <text class="row_lable_text">
                    <text
                      class="required_red oa-icon oa-icon-asterisks"
                      v-if="col.isNull == 1"
                    ></text>
                    {{ col.remark }}
                  </text>
                </view>
                <view
                  v-if="['VARCHAR', 'NUMBER'].includes(col.fieldType)"
                  class="row_value row_value_input"
                >
                  <input
                    class="row_value_input_text"
                    :placeholder="`请输入${col.promptText || ''}`"
                    v-model="val[col.fieldName]"
                    :disabled="col.isReadOnly == 1"
                    @input="
                      handleChildFormInput($event, i, col, item.fieldName)
                    "
                    @blur="handleChildFormItemDataChange(val, col, item, i)"
                  />
                </view>
                <view
                  v-else-if="col.fieldType == 'TEXTAREA'"
                  class="row_value row_value_textarea"
                >
                  <textarea
                    class="row_value_textarea_text"
                    v-model="val[col.fieldName]"
                    :placeholder="`请输入${col.promptText || ''}`"
                    :disabled="col.isReadOnly == 1"
                    maxlength="-1"
                    @input="
                      handleChildFormInput($event, i, col, item.fieldName)
                    "
                    @blur="handleChildFormItemDataChange(val, col, item, i)"
                  />
                </view>
                <!-- 子表单 时间选择框 -->
                <view
                  v-else-if="col.fieldType == 'DATEPICKER'"
                  class="row_value row_value_input"
                  :data-tap="item.fieldName"
                  data-ref="date"
                  data-mode="date"
                  data-fields="day"
                  data-mode-type="childForm"
                  :data-temp-index="index"
                  :data-mode-option="{
                    index: i,
                    ...col
                  }"
                  :data-value="val[col.fieldName]"
                  @click="showPicker"
                >
                  <input
                    class="row_value_input_text"
                    disabled
                    type="text"
                    :value="val[col.fieldName]"
                    :placeholder="`请选择${col.promptText || ''}`"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
                <!-- 子表单 单选选择 -->
                <view
                  v-else-if="col.fieldType == 'SELECT'"
                  class="row_value row_value_input"
                  :data-tap="item.fieldName"
                  data-ref="popup"
                  data-popup-type="bottom"
                  data-popup-choice="radio"
                  :data-temp-index="index"
                  :data-options="col.optionList"
                  data-mode-type="childForm"
                  :data-mode-option="{
                    index: i,
                    ...col
                  }"
                  @click="showPopup"
                >
                  <input
                    class="row_value_input_text row_value-select"
                    :disabled="true"
                    :placeholder="`请选择${col.promptText || ''}`"
                    :value="val[col.fieldName]"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
                <!-- 子表单 多选选择 -->
                <view
                  v-else-if="col.fieldType == 'MULTIPLESELECT'"
                  class="row_value row_value_input"
                  :data-tap="item.fieldName"
                  data-ref="popup"
                  data-popup-type="bottom"
                  data-popup-choice="checkBox"
                  :data-temp-index="index"
                  :data-options="col.optionList"
                  data-mode-type="childForm"
                  :data-mode-option="{
                    index: i,
                    ...col
                  }"
                  @click="showPopup"
                >
                  <input
                    class="row_value_input_text row_value-select"
                    :disabled="true"
                    :placeholder="`请选择${col.promptText || ''}`"
                    :value="val[col.fieldName]"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
                <!-- 子表单 附件 -->
                <view v-else-if="col.fieldType == 'FILE'" class="row_value">
                  <form-upload
                    v-model="val[col.fieldName]"
                    :disabled="col.isReadOnly == 1"
                  />
                </view>
                <!-- 子表单 公式计算 -->
                <view
                  v-else-if="col.fieldType == 'EXPRESSION'"
                  class="row_value row_value_input"
                >
                  <input
                    :id="'expressionInputIndex' + i + 'Id' + col.id"
                    class="row_value_input_text"
                    :disabled="true"
                    :value="val[col.fieldName]"
                    :placeholder="`请输入${col.promptText || ''}`"
                    @input="
                      handleChildFormInput($event, i, col, item.fieldName)
                    "
                  />
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- childFormCount(子表单字段统计) -->
        <view
          class="form_row dis_flex"
          v-if="item.fieldType == 'childFormCount'"
        >
          <view class="row_lable">
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value">{{
            formDatas[item.fieldName] || itemData[item.fieldName] || 0
          }}</view>
        </view>
        <!-- 请假统计 -->
        <view class="form_row" v-if="item.fieldType == 'leaveStatistics'">
          <view class="row_lable_table">
            <uni-table
              v-if="noData"
              ref="leaveStatisticsTable"
              titleTextAlign="center"
              textAlign="center"
              :tableData="leaveStatisticsDataList"
              :columns="columns"
              :stickSide="false"
              style="width: auto;"
              :border="true"
            ></uni-table>
            <view v-else>暂无请假数据</view>
          </view>
        </view>
        <!-- 数据字典 -->
        <view class="form_row dis_flex" v-if="item.fieldType == 'serialNumber'">
          <view class="row_lable">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text class="row_lable_text">{{ item.showName }}</text>
          </view>
          <view class="row_value row_value_input">
            <input
              class="row_value_input_text"
              :field-name="item.fieldName"
              v-model="itemData[item.fieldName]"
              disabled="true"
              id="serialNumber"
              :placeholder="item.placeholderContent"
            />
          </view>
        </view>
        <!-- operationItem(手术项目) -->
        <view class="form_row" v-if="item.fieldType == 'operationItem'">
          <operation-item-select
            :label="item.showName"
            v-model="itemData[item.fieldName]"
            :required="item.isMust"
          ></operation-item-select>
        </view>
        <!-- 互通组件 取消电子病例审签-->
        <view class="form_row" v-if="item.fieldType == 'interworkCom'">
          <case-histroy-select
            :label="item.showName"
            v-model="itemData[item.fieldName]"
            :required="item.isMust"
            :interworkface="item.interworkface"
          ></case-histroy-select>
        </view>
        <!-- 互通组件 修改病人信息-->
        <view class="form_row" v-if="item.fieldType == 'interworkSick'">
          <view class="dis_flex">
            <view class="row_lable">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view
              class="row_value row_value_input"
              :data-tap="item.fieldName"
              data-ref="popup"
              data-popup-type="bottom"
              data-popup-choice="interworkSick"
              @click="showPopup"
            >
              <input
                class="row_value_input_text row_value-select"
                :disabled="true"
                v-model="interworkValObj[item.fieldName]"
                :placeholder="
                  item.promptText ? item.promptText : '请查询' + item.showName
                "
              />
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#bbb"
                type="arrowright"
              />
            </view>
          </view>
          <view v-if="choiceData[item.fieldName]" class="choice_list">
            <view class="choice_item block-item">
              <view
                v-for="(val, key) in choiceData[item.fieldName]['after']"
                :key="key"
              >
                <view class="text" v-if="val && key != 'id'">
                  【{{ key | sickKeyFilter }}】修改前：{{
                    choiceData[item.fieldName]['before'][key]
                  }}; 修改后：{{ val }}
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 互通组件 取消结算-->
        <view class="form_row" v-if="item.fieldType == 'interworkSettle'">
          <view class="dis_flex">
            <view class="row_lable">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
              <button
                class="mini-btn row_value_input-btn"
                type="default"
                size="mini"
                :data-tap="item.fieldName"
                data-ref="popup"
                data-popup-type="bottom"
                data-popup-choice="interworkSettle"
                :data-interval="interworkValObj[item.fieldName]"
                :data-name="item.showName"
                @click="showPopup"
              >
                查询
              </button>
            </view>
          </view>
          <view
            v-if="
              JSON.stringify(choiceData[item.fieldName]) != '{}' &&
                JSON.stringify(choiceData[item.fieldName]) != '[]'
            "
            class="choice_list"
          >
            <view class="choice_item block-item">
              <view class="text">{{ choiceData[item.fieldName].zy }}</view>
              <view class="text">
                {{
                  choiceData[item.fieldName].finishDate | formatDate
                }}结算,应退{{ choiceData[item.fieldName].recedeFee }}元
              </view>
            </view>
          </view>
        </view>
        <!-- 互通组件 检验申请 -->
        <view class="form_row" v-if="item.fieldType == 'interworkTest'">
          <view class="dis_flex">
            <view class="row_lable">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view
              class="row_value row_value_input"
              :data-tap="item.fieldName"
              data-ref="popup"
              data-popup-type="bottom"
              data-popup-choice="interworkTest"
              @click="showPopup"
            >
              <input
                class="row_value_input_text row_value-select"
                :disabled="true"
                v-model="interworkValObj[item.fieldName]"
                :placeholder="
                  item.promptText ? item.promptText : '请查询' + item.showName
                "
              />
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#bbb"
                type="arrowright"
              />
            </view>
          </view>
          <view
            v-if="
              choiceData[item.fieldName] &&
                choiceData[item.fieldName].length > 0
            "
            class="choice_list"
          >
            <view
              class="choice_item block-item interwork-test-item"
              v-for="(testItem, key) in choiceData[item.fieldName]"
              :key="key"
            >
              <view class="interwork-test-item-main">
                <view class="interwork-test-item-main-text">
                  {{ `医嘱项目：${testItem.name}` }}
                </view>
                <view class="interwork-test-item-main-text">
                  {{ `标本：${testItem.sampleName}` }}
                </view>
              </view>
              <view class="interwork-test-item-right">
                <view class="interwork-test-item-right-text">{{
                  `单价：${testItem.price}`
                }}</view>
              </view>
            </view>
          </view>
        </view>
        <!-- 互通组件 退预交金-->
        <view class="form_row" v-if="item.fieldType == 'interworkPay'">
          <view class="dis_flex">
            <view class="row_lable">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
              <button
                class="mini-btn row_value_input-btn"
                type="default"
                size="mini"
                :data-tap="item.fieldName"
                data-ref="popup"
                data-popup-type="bottom"
                data-popup-choice="interworkPay"
                :data-interval="interworkValObj[item.fieldName]"
                :data-name="item.showName"
                @click="showPopup"
              >
                查询
              </button>
            </view>
          </view>
          <view
            v-if="
              choiceData[item.fieldName] &&
                choiceData[item.fieldName].length > 0
            "
            class="choice_list"
          >
            <view
              class="choice_item block-item"
              v-for="(t, i) in choiceData[item.fieldName]"
              :key="i"
            >
              <view class="text">{{ t.name }}</view>
              <view class="text">
                {{ t.arriveDate | formatDate }} {{ t.payModeName }}
                {{ t.payValues }}元
              </view>
            </view>
          </view>
        </view>
        <!-- 互通组件 修改医嘱-->
        <view class="form_row" v-if="item.fieldType == 'interworkHosPro'">
          <view class="dis_flex">
            <view class="row_lable">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view
              v-if="item.isReadonly != 'Y'"
              class="row_value row_value_input"
            >
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
              <button
                class="mini-btn row_value_input-btn"
                type="default"
                size="mini"
                :data-tap="item.fieldName"
                data-ref="popup"
                data-popup-type="bottom"
                data-popup-choice="interworkHosPro"
                :data-interval="interworkValObj[item.fieldName]"
                :data-name="item.showName"
                @click="showPopup"
              >
                查询
              </button>
            </view>
            <view v-else class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
            </view>
          </view>
          <view
            v-if="
              choiceData[item.fieldName] &&
                choiceData[item.fieldName].length > 0
            "
            class="choice_list"
          >
            <view
              class="choice_item block-item"
              v-for="(t, i) in choiceData[item.fieldName]"
              :key="i"
            >
              <view class="text">{{ t.SHOW_ORDER_NAME }}</view>
              <view class="text"> 医嘱日期：{{ t.BOOK_DATE }} </view>
              <view class="text">
                数量：{{ t.SHOW_QTY }} 单价：{{ t.RETAIL_PRICE }} 金额：{{
                  t.RETAIL_VALUE
                }}
              </view>
            </view>
          </view>
        </view>
        <!-- 互通组件 医嘱耗材明细-->
        <view class="form_row" v-if="item.fieldType == 'inPatientOrder'">
          <view class="dis_flex">
            <view class="row_lable w260">
              <text
                class="required_red oa-icon oa-icon-asterisks"
                v-if="item.isMust"
              ></text>
              <text class="row_lable_text">{{ item.showName }}</text>
            </view>
            <view
              v-if="item.isReadonly != 'Y'"
              class="row_value row_value_input"
            >
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
              <button
                class="mini-btn row_value_input-btn"
                type="default"
                size="mini"
                :data-tap="item.fieldName"
                data-ref="popup"
                data-popup-type="bottom"
                data-popup-choice="inPatientOrder"
                :data-interval="interworkValObj[item.fieldName]"
                :data-name="item.showName"
                @click="showPopup"
              >
                查询
              </button>
            </view>
            <view v-else class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="interworkValObj[item.fieldName]"
                placeholder="请输入住院号"
              />
            </view>
          </view>
          <view
            v-if="JSON.parse(itemData[item.fieldName].length) > 0"
            class="choice_list"
          >
            <view
              class="choice_item block-item"
              v-for="(t, i) in JSON.parse(itemData[item.fieldName])"
              :key="i"
            >
              <view class="text"> 医嘱内容：{{ t.orderName }} </view>
              <view
                class="text"
                style="display: flex;justify-content: space-between;"
              >
                <span>规格：{{ t.spec }}</span>
                <span>单位：{{ t.dosageUnitName }}</span>
              </view>
            </view>
          </view>
        </view>
        <!--互通组件 基本药物字典 -->
        <view class="form_row" v-if="item.fieldType == 'essentialDrugDic'">
          <view class="table_title">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text>{{ item.showName }}</text>
          </view>
          <EssentialDrugDicSelect
            v-model="itemData[item.fieldName]"
            :disabled="item.isReadonly == 'Y'"
          />
        </view>
        <!--互通组件 医疗物品字典 -->
        <view class="form_row" v-if="item.fieldType == 'medicalSupplieDic'">
          <view class="table_title">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text>{{ item.showName }}</text>
          </view>
          <MedicalSupplieDicSelect
            v-model="itemData[item.fieldName]"
            :disabled="item.isReadonly == 'Y'"
          />
        </view>
        <!--互通组件 病人住院信息 -->
        <view class="form_row" v-if="item.fieldType == 'interworkHosInfo'">
          <patient-hos-info-select
            :label="item.showName"
            v-model="itemData[item.fieldName]"
            :required="item.isMust"
          ></patient-hos-info-select>
        </view>
        <!--互通组件 邀请会诊对象 -->
        <view class="form_row" v-if="item.fieldType == 'consultation'">
          <view class="table_title">
            <text
              class="required_red oa-icon oa-icon-asterisks"
              v-if="item.isMust"
            ></text>
            <text>{{ item.showName }}</text>
          </view>
          <view class="table">
            <consultation-item-select
              :label="item.showName"
              v-model="itemData[item.fieldName]"
              :currentStepNo="currentStepNo"
            />
            <!-- <view
              class="row_group"
              v-for="(val, i) in itemData[item.fieldName]"
              :key="i"
            >
              <view class="table_item_title">
                <text>第{{ i + 1 }}组</text>
                <view v-if="currentStepNo == 'start'">
                  <text
                    class="delet_meeting_room_btn"
                    v-if="itemData[item.fieldName].length > 1"
                    :data-tap="item.fieldName"
                    :data-index="i"
                    @click="deletConsultationRow"
                  >
                    删除
                  </text>
                  <text
                    class="delet_meeting_room_btn"
                    v-if="i === itemData[item.fieldName].length - 1"
                    @click="addConsultationRow(item)"
                  >
                    添加
                  </text>
                </view>
              </view>
              <view
                class="table_item child-form-item"
                v-for="(col, cIndex) of consultationCol || []"
                :key="cIndex"
                :class="{
                  dis_flex: true,
                  disabled: col.isReadOnly
                }"
              >
                <view class="row_lable">
                  <text class="row_lable_text">
                    <text
                      class="required_red oa-icon oa-icon-asterisks"
                      v-if="col.isNull == 1"
                    ></text>
                    {{ col.remark }}
                  </text>
                </view>
                <view
                  class="row_value row_value_input"
                  :data-tap="item.fieldName"
                  data-ref="popup"
                  data-popup-type="bottom"
                  data-popup-choice="radio"
                  :data-temp-index="index"
                  :data-options="col.optionList"
                  data-mode-type="childForm"
                  :data-mode-option="{
                    index: i,
                    ...col
                  }"
                  @click="showPopup"
                >
                  <input
                    class="row_value_input_text row_value-select"
                    :disabled="true"
                    :placeholder="`请选择${col.promptText || ''}`"
                    :value="val[col.fieldName]"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
              </view>
            </view> -->
          </view>
        </view>
      </view>
    </view>
    <date-picker
      ref="date"
      endDate="2100-12-31 23:59"
      :mode="picker.mode"
      @confirm="onConfirm"
      :fields="picker.fields"
      :value="datePickerVal"
    ></date-picker>
    <uni-popup
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      ref="popup"
      @maskclick="maskConfirm"
    >
      <view class="pop_header" v-if="picker.type == 'interworkTest'">
        <view class="pop_header-text">
          <text
            v-if="interworkIndex == 0"
            class="interwork-test-header-button"
            @click="cancelInterworkTest"
            >取消</text
          >
          <text class="pop_header-title interwork-test-title">
            {{
              `医嘱信息${
                interworkIndex == 0
                  ? ''
                  : picker.value == 'queryOrder'
                  ? '-医嘱项目'
                  : '-标本'
              }`
            }}
          </text>
          <text
            v-if="interworkIndex == 0"
            class="interwork-test-header-button"
            @click="confirmInterworkTest"
            >保存</text
          >
        </view>
        <view class="pop-wrap" v-if="interworkIndex == 0">
          <view class="pop-row dis_flex">
            <view class="pop-row_lable">医嘱项目</view>
            <view
              class="pop-row_value pop-row_value_input"
              @click.stop="selectQueryOrder"
            >
              <input
                class="pop-row_value_input-text"
                readonly
                type="text"
                v-model="interworkTestVal.queryOrder.text"
                placeholder="请选择医嘱项目"
              />
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#bbb"
                type="arrowright"
              />
            </view>
          </view>
          <view class="pop-row dis_flex">
            <view class="pop-row_lable">标本</view>
            <view
              class="pop-row_value pop-row_value_input"
              @click.stop="selectQuerySample"
            >
              <input
                class="pop-row_value_input-text"
                readonly
                type="text"
                v-model="interworkTestVal.querySample.text"
                placeholder="请选择标本"
              />
              <uni-icons
                :size="30"
                class="uni-icon-wrapper"
                color="#bbb"
                type="arrowright"
              />
            </view>
          </view>
          <view class="interwork-test-add-button" @click="addInterworkTest"
            >添加</view
          >
          <view
            class="interwork-test-list"
            v-if="
              interworkIndex == 0 &&
                interwork.length > 0 &&
                interwork[0].list.length > 0
            "
          >
            <view
              class="interwork-test-item"
              v-for="(testItem, key) in interwork[interworkIndex].list"
              :key="key"
            >
              <view class="interwork-test-item-main">
                <view class="interwork-test-item-main-text">
                  {{ `医嘱项目：${testItem.name}` }}
                </view>
                <view class="interwork-test-item-main-text">
                  {{ `标本${testItem.sampleName}` }}
                </view>
              </view>
              <view class="interwork-test-item-right">
                <view class="interwork-test-item-right-text">
                  {{ `单价：${testItem.price}` }}
                </view>
                <view
                  class="interwork-test-delete-button"
                  @click="deleteInterworkTest(key)"
                  >删除</view
                >
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="pop_header" v-else-if="picker.type == 'interworkSick'">
        <view class="pop_header-title">{{
          interworkIndex == 2 ? '修改基本信息' : '基本信息查询'
        }}</view>
        <view class="pop-wrap" v-if="interworkIndex == 0">
          <view class="pop-row dis_flex">
            <view class="pop-row_lable">查询类型</view>
            <view class="pop-row_value pop-row_value_list">
              <text
                class="pop-row_value_list-item"
                :class="
                  item.value == interworkSickSelectedType ? 'active-item' : ''
                "
                v-for="item in interworkSickType"
                :key="item.value"
                @click="selectSickType(item.value)"
                >{{ item.text }}</text
              >
            </view>
          </view>
          <view class="pop-row dis_flex">
            <view class="pop-row_lable">查询内容</view>
            <view class="pop-row_value pop-row_value_input">
              <input
                class="pop-row_value_input-text"
                type="text"
                v-model="interworkSickVal"
                placeholder="请输入查询内容"
              />
              <button
                class="mini-btn pop-row_value_input-btn"
                type="default"
                size="mini"
                @click="getInterworkSick('interworkSick', interworkSickVal)"
              >
                查询
              </button>
            </view>
          </view>
        </view>
        <view class="pop-wrap" v-if="interworkIndex == 2">
          <view class="pop_tips">
            <view class="pop_tips-text">{{ picker.value }}</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
              @click="returnInterwork(picker.tap)"
            />
          </view>
          <view class="pop_lable-text">修改基本信息：</view>
          <view class="sick-row">
            <view class="sick-row_lable"
              >姓名：{{ _self.interwork[_self.interworkIndex].list.name }}</view
            >
            <view class="sick-row_value sick-row_value_input">
              <input
                class="sick-row_value_input-text"
                type="text"
                v-model="interworkSickChange.name"
                placeholder="如需修改请输入"
              />
            </view>
          </view>
          <view class="sick-row">
            <view class="sick-row_lable">
              性别：{{ _self.interwork[_self.interworkIndex].list.sexText }}
              <text style="color: #F0AD4E;font-size: 24rpx;">(暂不能修改)</text>
            </view>
            <view class="sick-row_value sick-row_value_input">
              <radio-group @change="sickRadioChange">
                <label
                  class="radio"
                  v-for="(val, key) in sickSexList"
                  :key="key"
                >
                  <radio
                    :value="val"
                    :disabled="true"
                    :checked="
                      key == _self.interwork[_self.interworkIndex].list.sex
                    "
                  />
                  {{ val }}
                </label>
              </radio-group>
            </view>
          </view>
          <view class="sick-row">
            <view class="sick-row_lable"
              >联系电话：{{
                _self.interwork[_self.interworkIndex].list.phone
              }}</view
            >
            <view class="sick-row_value sick-row_value_input">
              <input
                class="sick-row_value_input-text"
                type="text"
                v-model="interworkSickChange.phone"
                placeholder="如需修改请输入"
              />
            </view>
          </view>
          <view class="sick-row">
            <!-- <view class="sick-row_lable">
							身份证号码：{{_self.interwork[_self.interworkIndex].list.idcard}}
							<text style="color: #F0AD4E;font-size: 24rpx;">(暂不能修改)</text>
						</view> -->
            <view class="sick-row_lable">
              身份证号码：{{
                _self.interwork[_self.interworkIndex].list.idcard
              }}
            </view>
            <view class="sick-row_value sick-row_value_input">
              <input
                class="sick-row_value_input-text"
                type="text"
                v-model="interworkSickChange.idcard"
                placeholder="如需修改请输入"
              />
            </view>
          </view>
          <view class="button_groups">
            <button
              class="button_item"
              type="primary"
              data-ref="popup"
              @click="confirmSickBtn"
            >
              确定
            </button>
          </view>
        </view>
      </view>
      <view class="pop_header" v-else-if="picker.type == 'interworkSettle'">
        <view class="pop_header-title">查询结算信息</view>
        <view v-if="interworkIndex != 0">
          <view class="pop_tips">
            <view class="pop_tips-text">{{ picker.value }}</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
              @click="returnInterwork(picker.tap)"
            />
          </view>
          <view class="pop_lable-text">选择结算记录：</view>
        </view>
      </view>
      <view class="pop_header" v-else-if="picker.type == 'interworkPay'">
        <view class="pop_header-title">查询预交金信息</view>
        <view v-if="interworkIndex != 0">
          <view class="pop_tips">
            <view class="pop_tips-text">{{ picker.value }}</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
              @click="returnInterwork(picker.tap)"
            />
          </view>
          <view class="pop_lable-text">选择预交金记录：</view>
        </view>
      </view>
      <view class="pop_header" v-else-if="picker.type == 'interworkHosPro'">
        <view class="pop_header-title">查询项目信息</view>
        <view v-if="interworkIndex != 0">
          <view class="pop_tips">
            <view class="pop_tips-text">{{ picker.value }}</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
              @click="returnInterwork(picker.tap, 0)"
            />
          </view>
          <view class="pop_tips" v-if="interworkIndex == 2">
            <view class="pop_tips-text">{{ picker.subValue }}</view>
            <uni-icons
              :size="30"
              class="uni-icon-wrapper"
              color="#bbb"
              type="arrowright"
              @click="returnInterwork(picker.tap, 1)"
            />
          </view>
          <view class="pop_lable-text" v-if="interworkIndex == 1">
            选择医嘱记录：
          </view>
          <view class="pop_lable-text" v-else-if="interworkIndex == 2">
            选择费用明细：
          </view>
        </view>
      </view>
      <view class="pop_header" v-else-if="picker.type == 'inPatientOrder'">
        <view class="pop_header-title">耗材明细</view>
        <view class="pop_header-action" data-ref="popup" @click="confirmBtn">
          提交
        </view>
        <view class="pop_tips">
          <view class="pop_tips-text tal orderName">
            医嘱内容
          </view>
          <view class="pop_tips-text tal">
            规格
          </view>
          <view class="pop_tips-text tar">
            单位
          </view>
        </view>
      </view>
      <view class="pop_input" v-if="picker.showSearch">
        <input
          class="pop_input-text"
          type="text"
          v-model="pickerSearch"
          :placeholder="`输入关键字搜索`"
        />
      </view>
      <view
        v-if="picker.popupChoice == 'radio'"
        class="contact_list scroll_list"
        :style="scrollStyle"
      >
        <view
          class="contact_item"
          v-for="(item, index) in picker.list"
          :key="item.value"
          @click="singleColumn('popup', item)"
        >
          <view class="contact_item_text" v-if="picker.type == 'interworkSick'">
            <view>{{ item.name }}({{ item.sexText }})</view>
            <view>联系电话：{{ item.phone }}</view>
            <view>身份证号码：{{ item.idcard }}</view>
          </view>
          <view
            class="contact_item_text conten_ite_flex"
            v-else-if="picker.type == 'interworkSettle' && interworkIndex != 0"
          >
            <view class="contact_item_text-wrap">
              <view class="contact_item_text-info"
                >{{ item.patientName }}({{ item.patientIccard }})</view
              >
              <view class="contact_item_text-info"
                >结算日期：{{ item.finishDate | formatDate }}</view
              >
              <view class="contact_item_text-info">
                <text>总费用：{{ item.totalCost }}元</text>
                <text>应退：{{ item.recedeFee }}元</text>
              </view>
              <view v-show="item.show == 'true'">
                <view class="contact_item_text-info">
                  <text>诊疗卡号：{{ item.inpatientNo }}</text>
                  <text>住院状态：{{ item.status }}</text>
                </view>
                <view class="contact_item_text-info"
                  >入院日期：{{ item.inDate | formatDate }}</view
                >
                <view class="contact_item_text-info"
                  >出院日期：{{ item.outDate | formatDate }}</view
                >
                <view class="contact_item_text-info"
                  >入院病室：{{ item.inDeptName }}</view
                >
                <view class="contact_item_text-info"
                  >当前病室：{{ item.deptName }}</view
                >
                <view class="contact_item_text-info">
                  <text>床号：{{ item.bedNo }}</text>
                  <text>结算方式：{{ item.recordType }}</text>
                </view>
                <view class="contact_item_text-info"
                  >预交金：{{ item.deposits }}元</view
                >
                <view class="contact_item_text-info">
                  <text>医保支付：{{ item.otherPayments }}元</text>
                  <text>自付金额：{{ item.actualPey }}元</text>
                </view>
                <view class="contact_item_text-info">
                  <text>暂存：{{ item.saveFee }}元</text>
                  <text>欠费：{{ item.lackFee }}元</text>
                </view>
              </view>
            </view>
            <view
              class="contact_item_text-icon"
              @click.stop="changeCollapse(index, item.show)"
            >
              {{ item.show == 'true' ? '收起' : '展开' }}
              <uni-icons
                :type="item.show == 'true' ? 'arrowup' : 'arrowdown'"
                color="#005BAC"
                size="28"
              />
            </view>
          </view>
          <view
            class="contact_item_text"
            v-else-if="picker.type == 'interworkHosPro' && interworkIndex != 0"
          >
            <view class="contact_item_text-info"
              >【{{ orderTypeList[item.ORDER_TYPE] }}】{{
                item.Nurse_ORDER_CONTEXT
              }}</view
            >
            <view class="contact_item_text-info"
              >录入：{{
                `${item.DEPT_NAME}-${item.ORDER_DOCNAME} ${item.Nurse_DAY} ${item.Nurse_TIME}`
              }}</view
            >
          </view>
          <text class="contact_item_text" v-else>{{ item.text }}</text>
          <view class="contact_item-icon">
            <uni-icons
              v-if="
                itemValue[picker.tap] == item.value ||
                  (picker.modeType == 'childForm' &&
                    choiceData[picker.tap][picker.modeOption.index][
                      picker.modeOption.fieldName
                    ] == item.value)
              "
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
      </view>
      <view
        v-else-if="picker.popupChoice == 'checkBox'"
        class="contact_list scroll_list"
        :style="scrollStyle"
      >
        <view
          class="contact_item"
          v-for="(item, index) in picker.list"
          :key="item.value"
          @click="multipleColumn('popup', item)"
        >
          <view
            class="contact_item_text conten_ite_flex"
            v-if="picker.type == 'interworkPay' && interworkIndex != 0"
          >
            <view class="contact_item_text-wrap">
              <view class="contact_item_text-info">
                {{ item.patientName }}({{ item.patientIccard }})
              </view>
              <view class="contact_item_text-info">
                到账日期：{{ item.arriveDate | formatDate }}
              </view>
              <view class="contact_item_text-info">
                <text>{{ item.payModeName }}</text>
                <text>费用：{{ item.payValues }}元</text>
              </view>
              <view v-show="item.show == 'true'">
                <view class="contact_item_text-info">
                  <text>诊疗卡号：{{ item.inpatientNo }}</text>
                  <text>住院状态：{{ item.status }}</text>
                </view>
                <view class="contact_item_text-info">
                  入院日期：{{ item.inDate | formatDate }}
                </view>
                <view class="contact_item_text-info">
                  出院日期：{{ item.outDate | formatDate }}
                </view>
                <view class="contact_item_text-info">
                  入院病室：{{ item.inDeptName }}
                </view>
                <view class="contact_item_text-info">
                  <text>当前病室：{{ item.deptName }}</text>
                  <text>床号：{{ item.bedNo }}</text>
                </view>
              </view>
            </view>
            <view
              class="contact_item_text-icon"
              @click.stop="changeCollapse(index, item.show)"
            >
              {{ item.show == 'true' ? '收起' : '展开' }}
              <uni-icons
                :type="item.show == 'true' ? 'arrowup' : 'arrowdown'"
                color="#999"
                size="28"
              />
            </view>
          </view>
          <view
            class="contact_item_text"
            v-if="picker.type == 'interworkHosPro'"
          >
            <view class="contact_item_text-wrap">
              <view class="contact_item_text-info">{{
                item.SHOW_ORDER_NAME
              }}</view>
              <view class="contact_item_text-info"
                >医嘱日期：{{ item.BOOK_DATE }}</view
              >
              <view class="contact_item_text-info">
                <text class="contact_item_text-info-text"
                  >数量：{{ item.SHOW_QTY }}</text
                >
                <text class="contact_item_text-info-text"
                  >单价：{{ item.RETAIL_PRICE }}</text
                >
                <text class="contact_item_text-info-text"
                  >总金额：{{ item.RETAIL_VALUE }}</text
                >
              </view>
            </view>
          </view>
          <view
            v-if="picker.type == 'inPatientOrder'"
            class="contact_item_text"
          >
            <view
              class="contact_item_text-wrap"
              :class="{
                active: itemValue[picker.tap]
                  .map(map => map.orderItemId)
                  .includes(item.orderItemId)
              }"
            >
              <view class="contact_item_text-info">
                <view class="orderName">{{ item.orderName }} </view>
                <view class="spec">{{ item.spec }}</view>
                <view>{{ item.dosageUnitName }}</view>
              </view>
            </view>
          </view>
          <text class="contact_item_text" v-else>{{ item.text }}</text>
          <view class="contact_item-icon">
            <uni-icons
              v-if="
                (picker.type != 'inPatientOrder' &&
                  itemValue[picker.tap] != '' &&
                  itemValue[picker.tap].split(',').includes(item.value)) ||
                  (picker.modeType == 'childForm' &&
                    choiceValue[picker.tap][picker.modeOption.index][
                      picker.modeOption.fieldName
                    ]
                      .split(',')
                      .includes(item.value))
              "
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </view>
        <view class="button_groups">
          <button
            class="button_item"
            type="primary"
            data-ref="popup"
            @click="confirmBtn"
          >
            确定
          </button>
        </view>
      </view>
    </uni-popup>
    <process-popup-select
      ref="processPopupSelect"
      :value="processPopup.value"
      :params="processPopup.params"
      :apiFunction="processPopup.apiFunc"
      @change="processPopupOnChange"
    />
  </view>
</template>

<script>
import UniPopup from '@/components/uni-popup/uni-popup.vue';
import DatePicker from '@/components/picker/date-picker.vue';
import ProcessPopupSelect from './components/process-popup-select.vue';
import formUpload from './components/form-upload.vue';
import EssentialDrugDicSelect from './components/essential-drug-dic-select.vue';
import MedicalSupplieDicSelect from './components/medical-supplie-dic-select.vue';
import caseHistroySelect from './components/case-histroy-select.vue';
import patientHosInfoSelect from './components/patient-hos-info-select.vue';
import operationItemSelect from './components/operation-item-select.vue';
import consultationItemSelect from './components/consultation-item-select.vue';
import FromInput from './mixins/formInput';
export default {
  mixins: [FromInput],
  components: {
    DatePicker,
    UniPopup,
    ProcessPopupSelect,
    formUpload,
    EssentialDrugDicSelect,
    MedicalSupplieDicSelect,
    caseHistroySelect,
    patientHosInfoSelect,
    operationItemSelect,
    consultationItemSelect
  }
};
</script>

<style lang="scss" scoped>
.form_content {
  height: 100%;
  width: 100%;
  overflow-y: auto;
}
.dis_flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.form {
  margin-bottom: 30rpx;
  .row_tab {
    &:last-child {
      .form_row::after {
        height: 0;
      }
    }
    .form_row {
      position: relative;
      background-color: #ffffff;
      overflow: hidden;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      .row_group {
        position: relative;
        padding-top: 20rpx;
        &:first-child {
          padding-top: 0;
        }
        &::after {
          position: absolute;
          content: '';
          bottom: 0;
          left: 40rpx;
          right: 0;
          transform: scaleY(-0.5);
          height: 1px;
          background-color: #eee;
        }
      }
      .row_title {
        color: #333;
        margin: 30rpx 30rpx 20rpx;
      }
      .row_lable_table {
        padding: 20rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        color: #333;
        font-size: 28rpx;
        overflow-x: auto;
      }
      .row_lable {
        width: 240rpx;
        padding: 20rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        color: #333;
        font-size: 28rpx;
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add_icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        .file_add_icon {
          padding: 0 30rpx;
          font-size: 56rpx;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          color: #bbb;
          //width: auto;
        }
        .common_words {
          padding: 0 30rpx;
          font-size: 28rpx;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          color: #005bac;
        }
        & ~ .row_value {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          padding: 20rpx 30rpx;
          padding-left: 0;
          box-sizing: border-box;
          text-align: right;
        }
        & ~ .row_value_input {
          display: flex;
          justify-content: center;
          align-items: center;
          .row_value_input_text {
            text-align: right;
            flex: 1;
            font-size: 28rpx;
            color: #333;
          }
          .row_value-select {
            pointer-events: none;
          }
          .row_value_input-btn {
            background-color: #005bac;
            color: #fff;
            padding: 0 20rpx;
            margin-left: 20rpx;
          }
        }
        & ~ .row_value_textarea {
          width: 100%;
          padding-left: 30rpx;
          padding-top: 0;
          text-align: left;
          .row_value_textarea_text {
            width: 100%;
            min-height: 160rpx;
            font-size: 28rpx;
            color: #333;
          }
          .textarea-placeholder {
            color: #bbb;
          }
        }
        & ~ .row_value_personal,
        & ~ .row_value_dept,
        & ~ .row_value_process {
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          text-align: left;
          padding: 0 30rpx 20rpx;
        }
        & ~ .row_value_personal .personNameStr {
          flex: 1;
        }
        & ~ .row_value_image image {
          width: 100%;
          height: 100rpx;
        }
      }
      .file_list {
        .file_item {
          text-decoration: none;
          font-size: 28rpx;
          color: #333333;
          margin: 10rpx 20rpx 20rpx;
          padding: 6rpx 20rpx;
          border: 1px solid #eeeeee;
          border-radius: 5px;
          margin-bottom: 20rpx;
          display: flex;
          align-items: center;
          .file_item_info {
            text-decoration: none;
            flex: 1;
            text-align: left;
            display: flex;
            align-items: center;
            .file_item_name {
              flex: 1;
              margin: 0 20rpx;
            }
          }
          .oa-icon {
            font-size: 40rpx;
          }
          .delete_file {
            color: #005bac;
            margin-left: 8px;
          }
          .file_name {
            font-size: 28rpx;
            color: #333333;
          }
          .file_size {
            color: #999999;
            font-size: 24rpx;
            margin-left: 20rpx;
          }
        }
      }
      .table_title {
        padding: 20rpx 30rpx;
        position: relative;
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
      }
      .table_item_title {
        color: #999;
        font-size: 26rpx;
        padding: 0 40rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .delet_meeting_room_btn {
          color: #005bac;
          font-size: 26rpx;
          & + .delet_meeting_room_btn {
            margin-left: 4px;
          }
        }
      }
      .table_item {
        margin-left: 20rpx;
        position: relative;
      }
    }
  }
}
.fileTemplate {
  .fiel_list {
    .file_item {
      text-decoration: none;
      color: #005bac;
      font-size: 28rpx;
      background-color: #fff;
      display: flex;
      align-items: center;
      .oa-icon {
        font-size: 40rpx;
        margin-right: 10rpx;
      }
    }
  }
}
.remark {
  text-align: left !important;
  padding: 20rpx 30rpx !important;
}
.remark.red-text {
  color: $uni-color-error !important;
}
.remark.bold-text {
  font-weight: bold !important;
}

.scroll_list {
  max-height: 800rpx;
  overflow: auto;
}
.contact_list {
  background-color: #ffffff;
  .contact_item {
    padding: 20rpx 30rpx;
    font-size: 28rpx;
    color: #333333;
    position: relative;
    // display: flex;
    // align-items: center;
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 30rpx;
      bottom: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_text {
      flex: 1;
      font-size: 28rpx;
      color: #333333;
      &.conten_ite_flex {
        display: flex;
        align-items: flex-end;
        justify-content: center;
      }
      .contact_item_text-wrap {
        flex: 1;
        .contact_item_text-info {
          font-size: 28rpx;
          color: #333333;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .contact_item_text-info-text {
          flex: 1;
        }
      }
      .contact_item_text-icon {
        font-size: 28rpx;
        color: #999;
        margin-left: 40rpx;
      }
    }
    .contact_item-icon {
      line-height: 1;
      position: absolute;
      right: 10rpx;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
.button_groups {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 10px;
  box-sizing: border-box;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #cccccc;
  z-index: 10;
  .button_item {
    font-size: 28rpx;
    margin: 0 20rpx;
    width: 160rpx;
  }
}
.choice_list {
  flex: 1;
  .choice_item {
    color: #005bac;
    background-color: #e5edff;
    font-size: 28rpx;
    padding: 0 16rpx;
    margin-left: 10rpx;
    margin-bottom: 10rpx;
    border-radius: 20rpx;
    display: inline-block;
    .choice_item_text {
      padding-right: 10rpx;
      font-size: 28rpx;
    }
    .text {
      font-size: 28rpx;
    }
  }
  .block-item {
    display: block;
    margin: 0 30rpx 20rpx;
  }
}
.pop_header {
  background-color: #ffffff;
  .pop_header-title {
    text-align: center;
    font-size: 32rpx;
    color: #333333;
    padding: 10rpx 0;
  }
  .pop_tips {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10rpx 30rpx;
    .pop_tips-text {
      flex: 1;
      font-size: 28rpx;
      color: #666666;
    }
  }
  .pop_lable-text {
    padding: 10rpx 30rpx 0;
    font-size: 28rpx;
    color: #666666;
    // border-bottom: 1px solid #eee;
  }
}
.pop-wrap {
  width: 100%;
  .pop-row {
    width: 100%;
    position: relative;
    background-color: #ffffff;
    overflow: hidden;
    .pop-row_lable {
      width: 120px;
      padding: 11px 15px;
      box-sizing: border-box;
      position: relative;
      color: #333;
    }
    .pop-row_value {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      font-size: 16px;
      color: #666;
      padding: 11px 15px;
      padding-left: 0;
      box-sizing: border-box;
      text-align: right;
      .pop-row_value_input-text {
        flex: 1;
        font-size: 28rpx;
      }
      .pop-row_value_input-btn {
        background-color: #005bac;
        color: #fff;
        padding: 0 20rpx;
        margin-left: 20rpx;
        font-size: 28rpx;
      }
    }
    .pop-row_value_list {
      display: block;
      .pop-row_value_list-item {
        color: #333333;
        margin-left: 20rpx;
        &.active-item {
          color: #005bac;
        }
      }
    }
  }
  .sick-row {
    width: 100%;
    position: relative;
    background-color: #ffffff;
    overflow: hidden;
    &::after {
      content: '';
      left: 15px;
      right: 0;
      bottom: 0;
      height: 1px;
      transform: translate(0.5);
      background-color: #eee;
      position: absolute;
    }
    .sick-row_lable {
      padding: 11px 15px 5px;
      box-sizing: border-box;
      position: relative;
      color: #333;
      font-size: 28rpx;
    }
    .sick-row_value {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      font-size: 28rpx;
      color: #666;
      padding: 0 15px 11px;
      box-sizing: border-box;
      .sick-row_value_input-text {
        flex: 1;
        font-size: 28rpx;
      }
      .sick-row_value_input-btn {
        background-color: #005bac;
        color: #fff;
        padding: 0 20rpx;
        margin-left: 20rpx;
        font-size: 28rpx;
      }
    }
  }
  .interwork-test-add-button {
    color: #005bac;
    text-align: center;
    font-size: 28rpx;
    padding-bottom: 10rpx;
  }
}
.pop_input {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  padding: 0 30rpx 16rpx;
  position: relative;
  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    transform: scaleY(0.5);
    content: '';
    background-color: #eeeeee;
  }
}
.interwork-test-list {
  border-top: 1px solid #eee;
  overflow: auto;
  max-height: 600rpx;
}
.interwork-test-item {
  margin: 20rpx 30rpx;
  background-color: #f1f1f1;
  padding: 20rpx;
  display: flex !important;
  font-size: 28rpx;
  border-radius: 4px;
  align-items: center;
}
.interwork-test-item-main {
  flex: 1;
  min-width: 1px;
}
.interwork-test-item-main-text {
  font-size: 28rpx;
  color: #005bac;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.interwork-test-item-right {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}
.interwork-test-item-right-text {
  font-size: 28rpx;
  color: #005bac;
}
.interwork-test-delete-button {
  color: #005bac;
  font-size: 24rpx;
}
.pop_header-text {
  display: flex;
}
.interwork-test-title {
  flex: 1;
}
.interwork-test-header-button {
  padding: 10rpx;
}
</style>
