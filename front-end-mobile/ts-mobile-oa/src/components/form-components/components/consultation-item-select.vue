<template>
  <view class="consultation-item-select">
    <view class="row_group" v-for="(val, i) in selectedDataList" :key="i">
      <view class="table_item_title">
        <text>第{{ i + 1 }}组</text>
        <view v-if="currentStepNo == 'start'">
          <text
            class="delet_meeting_room_btn"
            v-if="selectedDataList.length > 1"
            :data-index="i"
            @click="handleDeleteSelectedItem(i)"
          >
            删除
          </text>
          <text
            class="delet_meeting_room_btn"
            v-if="i === selectedDataList.length - 1"
            @click="handleAddSelectedItem()"
          >
            添加
          </text>
        </view>
      </view>
      <view
        class="table_item child-form-item"
        :class="{
          dis_flex: true,
          disabled: currentStepNo == 'start'
        }"
      >
        <view class="row_lable">
          <text class="row_lable_text">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            邀请会诊科室
          </text>
        </view>
        <view class="row_value row_value_input" @click="showPopupDept(i)">
          <input
            class="row_value_input_text row_value-select"
            :disabled="true"
            :placeholder="`请选择邀请会诊科室`"
            :value="val.deptName"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view
        class="table_item child-form-item"
        :class="{
          dis_flex: true,
          disabled: currentStepNo == 'start'
        }"
      >
        <view class="row_lable">
          <text class="row_lable_text">
            <text class="required_red oa-icon oa-icon-asterisks"></text>
            邀请会诊级别
          </text>
        </view>
        <view class="row_value row_value_input" @click="showPopupType(i)">
          <input
            class="row_value_input_text row_value-select"
            :disabled="true"
            :placeholder="`请选择邀请会诊级别`"
            :value="val.typeName"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
    </view>
    <uni-popup ref="popup" type="allBottom">
      <view class="popup-container">
        <view class="popup-action-container">
          <view class="cancel-btn" @tap="handleCancelSelect">取消</view>
          <view class="popup-title">会诊科室</view>
        </view>

        <view class="search-container">
          <u-search
            v-model="pickerSearch"
            placeholder="输入科室名称搜索"
            @clear="search"
            @search="search"
            @custom="search"
          ></u-search>
        </view>

        <view class="mescroll-content">
          <mescroll
            ref="mescroll"
            @getDatas="getDatas"
            @setDatas="setDatas"
            @datasInit="datasInit"
          >
            <view
              class="contact_item"
              v-for="item in deptList"
              :key="item.id"
              @click="singleColumn('popup', item)"
            >
              <text class="contact_item_text">{{ item.name }}</text>
              <view
                class="contact_item-icon"
                v-if="
                  index != null && selectedDataList[index].deptId == item.id
                "
              >
                <uni-icons type="checkmarkempty" color="#005BAC" size="44" />
              </view>
            </view>
          </mescroll>
        </view>
      </view>
    </uni-popup>
    <uni-popup ref="popupType" type="bottom">
      <view class="contact_list scroll_list" :style="scrollStyle">
        <view
          class="contact_item"
          v-for="item in typeList"
          :key="item.id"
          @click="singleColumn('popupType', item)"
        >
          <text class="contact_item_text">{{ item.name }}</text>
          <view
            class="contact_item-icon"
            v-if="index != null && selectedDataList[index].typeId == item.id"
          >
            <uni-icons type="checkmarkempty" color="#005BAC" size="44" />
          </view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: { mescroll },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: () => ''
    },
    currentStepNo: {
      type: String,
      default: 'start'
    }
  },
  data() {
    return {
      selectedDataList: [],
      deptList: [],
      typeList: [],
      scrollStyle: '400rpx',
      col: [
        {
          text: '邀请科室',
          filed: 'deptId'
        },
        {
          text: '邀请会诊级别',
          filed: 'typeId'
        }
      ],
      pickerSearch: '',
      index: null
    };
  },
  created() {
    this.ajax.getConsultQuaOrgInfo().then(res => {
      if (res.success && res.object) {
        this.deptList = res.object.map(e => {
          return {
            id: e.organizationId,
            name: e.orgName
          };
        });
      }
    });
    this.ajax.getDictItemByTypeCode({ typeCode: 'cslt_lv' }).then(res => {
      if (res.success && res.object) {
        this.typeList = res.object.map(e => {
          return {
            id: e.itemCode,
            name: e.itemName
          };
        });
      }
    });
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.selectedDataList = JSON.parse(val);
        } else {
          this.selectedDataList = [];
        }
      },
      immediate: true,
      deep: true
    },
    selectedDataList: {
      handler(val = []) {
        if (!val.length) {
          this.$emit('input', null);
        } else {
          let selectArr = val.map(item => ({
            deptId: item.deptId,
            deptName: item.deptName,
            typeId: item.typeId,
            typeName: item.typeName
          }));
          this.$emit('input', JSON.stringify(selectArr));
        }
      },
      deep: true
    }
  },
  methods: {
    handleDeleteSelectedItem(index) {
      this.selectedDataList.splice(index, 1);
    },
    handleAddSelectedItem() {
      this.selectedDataList.push({
        deptId: '',
        deptName: '',
        typeId: '',
        typeName: ''
      });
    },
    showPopupDept(index) {
      if (this.currentStepNo != 'start') return;
      this.index = index;
      this.$refs.popup.open();
      this.$nextTick(() => {
        this.$refs.mescroll?.downCallback();
      });
    },
    showPopupType(index) {
      if (this.currentStepNo != 'start') return;
      this.index = index;
      this.$refs.popupType.open();
    },
    search() {
      this.$nextTick(() => {
        this.$refs.mescroll?.downCallback();
      });
    },
    getDatas(page, successCallback, errorCallback) {
      if (this.pickerSearch) {
        let list = this.deptList.filter(e => e.indexOf(this.pickerSearch) > -1);
        successCallback(list);
      } else {
        successCallback(this.deptList);
      }
    },
    setDatas(rows) {
      this.deptList = this.deptList.concat(rows);
    },
    datasInit() {
      this.deptList = [];
    },
    handleCancelSelect() {
      this.$refs.popup.close();
    },
    singleColumn(ref, item) {
      let _self = this;
      if (ref == 'popupType') {
        this.selectedDataList[this.index].typeId = item.id;
        this.selectedDataList[this.index].typeName = item.name;
      } else {
        this.selectedDataList[this.index].deptId = item.id;
        this.selectedDataList[this.index].deptName = item.name;
      }
      _self.$nextTick(() => {
        _self.$refs[ref].close();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dis_flex {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.row_group {
  position: relative;
  padding-top: 20rpx;
  &:first-child {
    padding-top: 0;
  }
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 40rpx;
    right: 0;
    transform: scaleY(-0.5);
    height: 1px;
    background-color: #eee;
  }
}
.row_title {
  color: #333;
  margin: 30rpx 30rpx 20rpx;
}
.row_lable_table {
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  position: relative;
  color: #333;
  font-size: 28rpx;
  overflow-x: auto;
}
.row_lable {
  width: 240rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  position: relative;
  color: #333;
  font-size: 28rpx;
  .required_red {
    color: #f00;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 4rpx;
    font-size: 24rpx;
  }
  .add_icon {
    font-size: 40rpx;
    padding: 0 30rpx;
    line-height: 1;
    color: #005bac;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
  }
  .file_add_icon {
    padding: 0 30rpx;
    font-size: 56rpx;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #bbb;
    //width: auto;
  }
  .common_words {
    padding: 0 30rpx;
    font-size: 28rpx;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    color: #005bac;
  }
  & ~ .row_value {
    flex: 1;
    font-size: 28rpx;
    color: #666;
    padding: 20rpx 30rpx;
    padding-left: 0;
    box-sizing: border-box;
    text-align: right;
  }
  & ~ .row_value_input {
    display: flex;
    justify-content: center;
    align-items: center;
    .row_value_input_text {
      text-align: right;
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
    .row_value-select {
      pointer-events: none;
    }
    .row_value_input-btn {
      background-color: #005bac;
      color: #fff;
      padding: 0 20rpx;
      margin-left: 20rpx;
    }
  }
  & ~ .row_value_textarea {
    width: 100%;
    padding-left: 30rpx;
    padding-top: 0;
    text-align: left;
    .row_value_textarea_text {
      width: 100%;
      min-height: 160rpx;
      font-size: 28rpx;
      color: #333;
    }
    .textarea-placeholder {
      color: #bbb;
    }
  }
  & ~ .row_value_personal,
  & ~ .row_value_dept,
  & ~ .row_value_process {
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;
    text-align: left;
    padding: 0 30rpx 20rpx;
  }
  & ~ .row_value_personal .personNameStr {
    flex: 1;
  }
  & ~ .row_value_image image {
    width: 100%;
    height: 100rpx;
  }
}
.file_list {
  .file_item {
    text-decoration: none;
    font-size: 28rpx;
    color: #333333;
    margin: 10rpx 20rpx 20rpx;
    padding: 6rpx 20rpx;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    .file_item_info {
      text-decoration: none;
      flex: 1;
      text-align: left;
      display: flex;
      align-items: center;
      .file_item_name {
        flex: 1;
        margin: 0 20rpx;
      }
    }
    .oa-icon {
      font-size: 40rpx;
    }
    .delete_file {
      color: #005bac;
      margin-left: 8px;
    }
    .file_name {
      font-size: 28rpx;
      color: #333333;
    }
    .file_size {
      color: #999999;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }
}
.table_title {
  padding: 20rpx 30rpx;
  position: relative;
  .required_red {
    color: #f00;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 4rpx;
    font-size: 24rpx;
  }
}
.table_item_title {
  color: #999;
  font-size: 26rpx;
  padding: 0 40rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .delet_meeting_room_btn {
    color: #005bac;
    font-size: 26rpx;
    & + .delet_meeting_room_btn {
      margin-left: 4px;
    }
  }
}
.table_item {
  margin-left: 20rpx;
  position: relative;
}
.pop_input {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  padding: 0 30rpx 16rpx;
  position: relative;
  &::after {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    transform: scaleY(0.5);
    content: '';
    background-color: #eeeeee;
  }
}
.scroll_list {
  max-height: 800rpx;
  overflow: auto;
}
.contact_list {
  background-color: #ffffff;
}
.contact_item {
  padding: 20rpx 30rpx;
  font-size: 28rpx;
  color: #333333;
  position: relative;
  background: #fff;
  // display: flex;
  // align-items: center;
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 30rpx;
    bottom: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &:last-child::after {
    height: 0;
  }
  .contact_item_text {
    flex: 1;
    font-size: 28rpx;
    color: #333333;
    &.conten_ite_flex {
      display: flex;
      align-items: flex-end;
      justify-content: center;
    }
    .contact_item_text-wrap {
      flex: 1;
      .contact_item_text-info {
        font-size: 28rpx;
        color: #333333;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .contact_item_text-info-text {
        flex: 1;
      }
    }
    .contact_item_text-icon {
      font-size: 28rpx;
      color: #999;
      margin-left: 40rpx;
    }
  }
  .contact_item-icon {
    line-height: 1;
    position: absolute;
    right: 10rpx;
    top: 50%;
    transform: translateY(-50%);
  }
}
.popup-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .search-container {
    padding: 8px;
    background-color: #fff;
    margin: 8px 0;
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
}
.popup-action-container {
  display: flex;
  overflow: hidden;
  align-items: center;
  background-color: #fff;
  padding: 8px 16px;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  .popup-title {
    flex: 1;
    font-weight: bold;
    text-align: center;
  }
  .cancel-btn {
    color: #999;
  }
  .confirm-btn {
    color: $theme-color;
  }
}
</style>
