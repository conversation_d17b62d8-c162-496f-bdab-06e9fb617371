<template>
  <view class="essential-drug-select-container">
    <view
      v-if="!disabled"
      class="file_add_icon oa-icon oa-icon-tupiantianjia"
      @click="handleOpenDrugDic"
    ></view>

    <view v-if="disabled" style="overflow: auto;">
      <uni-table
        ref="ntable"
        titleTextAlign="center"
        textAlign="center"
        :tableData="selectedDataList"
        :columns="columns"
        :stickSide="false"
        style="width: 800px;"
      ></uni-table>
    </view>
    <uni-swipe-action v-else>
      <uni-swipe-action-item
        v-for="(item, index) in selectedDataList"
        :key="item.drugId"
        :options="[
          { text: '删除', style: { backgroundColor: 'rgb(255,58,49)' } }
        ]"
        @click="handleDeleteSelectedItem(item, index)"
      >
        <view style="flex: 1;padding: 8px 16px;">
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">品名</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.chemicalName"
                placeholder="请输入品名"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">规格</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.spec"
                placeholder="请输入规格"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">单位</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.unitName"
                placeholder="请输入单位"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">生产厂商</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.factName"
                placeholder="请输入生产厂商"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">供货单位</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.supplierName"
                placeholder="请输入供货单位"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">采购数量</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.num"
                placeholder="请输入采购数量"
                :disabled="disabled"
                @input="handleInputNumber($event, index)"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">预算单价</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.buyPrice"
                placeholder="请输入预算单价"
                :disabled="disabled"
                @input="handleInputPrice($event, index)"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">预算金额</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.total"
                placeholder="请输入预算金额"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row" style="display: flex;">
            <view class="row_label">
              <text class="required_red oa-icon oa-icon-asterisks"></text>
              <text class="row_lable_text">采购方式</text>
            </view>
            <view class="row_value row_value_input">
              <input
                class="row_value_input_text"
                v-model="item.purchaseType"
                placeholder="请输入采购方式"
                :disabled="disabled"
              />
            </view>
          </view>
          <view class="form_row">
            <view class="row_label">
              <text class="row_lable_text">编码及备注</text>
            </view>
            <view class="row_value row_value_textarea">
              <textarea
                class="row_value_textarea_text"
                v-model="item.memo"
                :disabled="disabled"
                placeholder="请输入备注"
                maxlength="-1"
                @input="handleInputMemo($event, index)"
              />
            </view>
          </view>
        </view>
      </uni-swipe-action-item>
    </uni-swipe-action>

    <uni-popup ref="popup" type="allBottom">
      <view class="popup-container">
        <view class="popup-action-container">
          <view class="cancel-btn" @tap="handleCancelSelect">取消</view>
          <view class="popup-title">基本药品字典</view>
          <view class="confirm-btn" @tap="handleConfirmSelect">确定</view>
        </view>

        <view class="search-container">
          <u-search
            v-model="keyword"
            placeholder="请输入关键词"
            @clear="refresh"
            @search="refresh"
            @custom="refresh"
          ></u-search>
        </view>

        <view class="mescroll-content">
          <mescroll
            ref="mescroll"
            @getDatas="getDrugDicDataList"
            @setDatas="setDrugDicDatas"
            @datasInit="initDrugDicData"
          >
            <u-checkbox
              v-for="(item, index) in tableDataList"
              v-model="item.checked"
              :key="index"
              :name="item.drugId"
              class="ess-drug-item-checkbox"
              @change="handleSelectedDataChange"
            >
              <view class="ess-drug-title">{{ item.chemicalName }}</view>
              <view class="ess-drug-info">
                <view>规格：{{ item.spec }}</view>
                <view>单位：{{ item.unitName }}</view>
              </view>
              <view class="ess-drug-info">
                <view>进货价：{{ item.buyPrice }}</view>
              </view>
              <view class="ess-drug-info">生产厂商：{{ item.factName }} </view>
              <view class="ess-drug-info" v-if="item.supplierName">
                供货单位：{{ item.supplierName }}
              </view>
            </u-checkbox>
          </mescroll>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    value: {
      type: String,
      default: () => ''
    },
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      form: {},
      keyword: '',
      selectedDataList: [],
      popupSelectedList: [],

      tableDataList: [],
      columns: [
        {
          title: '品名',
          key: 'chemicalName'
        },
        {
          title: '规格',
          key: 'spec'
        },
        {
          title: '单位',
          key: 'unitName'
        },
        {
          title: '生产厂家',
          key: 'factName'
        },
        {
          title: '供货单位',
          key: 'supplierName'
        },
        {
          title: '采购数量',
          key: 'num'
        },
        {
          title: '预算单价',
          key: 'buyPrice'
        },
        {
          title: '预算金额',
          key: 'total'
        },
        {
          title: '采购方式',
          key: 'purchaseType'
        },
        {
          title: '备注',
          key: 'memo'
        }
      ]
    };
  },
  methods: {
    handleDeleteSelectedItem(item, index) {
      this.selectedDataList.splice(index, 1);
      this.$emit('input', JSON.stringify(this.selectedDataList));
    },
    handleOpenDrugDic() {
      this.form = {};
      this.refresh();
      this.keyword = '';
      this.popupSelectedList = JSON.parse(
        JSON.stringify(this.selectedDataList)
      );
      this.$refs.popup.open();
    },
    refresh() {
      this.$nextTick(() => {
        this.$refs.mescroll?.downCallback();
      });
    },
    handleConfirmSelect() {
      this.$refs.popup.close();
      this.tableDataList = [];
      this.form = {};
      this.$emit('input', JSON.stringify(this.popupSelectedList));
    },
    handleCancelSelect() {
      this.$refs.popup.close();
      this.tableDataList = [];
      this.form = {};
      if (this.value) {
        this.selectedDataList = JSON.parse(this.value);
      } else {
        this.selectedDataList = [];
      }
    },
    getDrugDicDataList(page, successCallback, errorCallback) {
      this.ajax
        .QueryBasicsDrgDictionary({
          pageNo: page.num,
          pageSize: page.size,
          keyword: this.keyword
        })
        .then(res => {
          let rows = res.rows.map(item => ({
            ...item,
            checked: this.popupSelectedList.some(
              drug => drug.drugId == item.drugId
            )
          }));
          successCallback(rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setDrugDicDatas(rows) {
      this.tableDataList = this.tableDataList.concat(rows);
    },
    initDrugDicData() {
      this.tableDataList = [];
    },
    handleSelectedDataChange({ value, name }) {
      let selectedNodes = [];
      if (value) {
        selectedNodes = this.tableDataList.filter(item => item.drugId === name);
      } else {
        let findIndex = this.popupSelectedList.findIndex(
          item => item.drugId === name
        );
        if (findIndex !== -1) {
          this.popupSelectedList.splice(findIndex, 1);
        }
      }
      selectedNodes.length > 0 && this.popupSelectedList.push(...selectedNodes);
    },
    handleInputNumber(e, index) {
      let value = e.target.value,
        reg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/;
      value = (value.match(reg) || [])[0];
      value && (value = parseFloat(value));

      this.$nextTick(() => {
        this.$set(this.selectedDataList[index], 'num', value);
        let buyPriceVal = this.selectedDataList[index].buyPrice;
        if (buyPriceVal && value) {
          let totalVal = this.addMultiplyPrecision(Number(buyPriceVal), value);
          this.$set(this.selectedDataList[index], 'total', totalVal);
        }
      });
    },
    handleInputPrice(e, index) {
      let value = e.target.value,
        reg = /^(([1-9]{1}\d*)|(0{1}))((\.\d{1,2}|\.))?/;
      value = (value.match(reg) || [])[0];
      value && (value = parseFloat(value));

      this.$nextTick(() => {
        this.$set(this.selectedDataList[index], 'buyPrice', value);
        let numVal = this.selectedDataList[index].num;
        if (numVal && value) {
          let totalVal = this.addMultiplyPrecision(Number(numVal), value);
          this.$set(this.selectedDataList[index], 'total', totalVal);
        }
      });
    },
    //乘法精度处理
    addMultiplyPrecision(num1, num2) {
      var p1 = 0;
      var p2 = 0;
      if (num1.toString().split('.').length > 1) {
        p1 = num1.toString().split('.')[1].length;
      }
      if (num2.toString().split('.').length > 1) {
        p2 = num2.toString().split('.')[1].length;
      }
      var p = p1 + p2;
      var n1 = num1 * Math.pow(10, p1);
      var n2 = num2 * Math.pow(10, p2);
      return (n1 * n2) / Math.pow(10, p);
    },
    handleInputMemo(event, index) {
      let value = event.detail.value;
      value = value.replace(/\s+/g, '');
      this.$nextTick(() => {
        this.$set(this.selectedDataList[index], 'memo', value);
      });
    }
  },
  watch: {
    value: {
      handler(val) {
        if (val) {
          this.selectedDataList = JSON.parse(val);
        } else {
          this.selectedDataList = [];
        }
      },
      immediate: true,
      deep: true
    },
    selectedDataList: {
      handler(val = []) {
        if (!val.length) {
          this.$emit('input', null);
        } else {
          this.$emit('input', JSON.stringify(val));
        }
      },
      deep: true
    }
  }
};
</script>

<style lang="scss" scoped>
.essential-drug-select-container {
  // position: relative;
  ::v-deep {
    .search-container {
      .form {
        .form-container {
          margin-top: 0;
          .form-box {
            padding: 0px;
            .u-form-item {
              padding-top: 0;
            }

            .u-border-bottom:after {
              border-bottom-width: 0px;
            }
          }
        }
      }
    }
  }
}
.file_add_icon {
  padding: 0 30rpx;
  font-size: 56rpx;
  position: absolute;
  top: 0;
  right: 0;
  color: #bbb;
}

.popup-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;

  .search-container {
    padding: 8px;
    background-color: #fff;
    margin: 8px 0;
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
}
.popup-action-container {
  display: flex;
  overflow: hidden;
  align-items: center;
  background-color: #fff;
  padding: 8px 16px;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  .popup-title {
    flex: 1;
    font-weight: bold;
    text-align: center;
  }
  .cancel-btn {
    color: #999;
  }
  .confirm-btn {
    color: $theme-color;
  }
}

.ess-drug-item-checkbox {
  align-items: baseline;
  padding: 8px;
  background-color: #fff;
  width: 100% !important;
  /deep/ .u-checkbox__label {
    flex: 1;
  }
  &:not(:first-child) {
    border-top: 1px solid #eee;
  }
}
.ess-drug-title {
  font-weight: bold;
}
.ess-drug-info {
  color: #999;
  font-size: 14px;
  display: flex;
  > view {
    flex: 1;
    font: inherit;
  }
}
.form_row {
  position: relative;
  background-color: #ffffff;
  overflow: hidden;
  &::after {
    position: absolute;
    content: '';
    bottom: 0;
    left: 30rpx;
    right: 0;
    transform: scaleY(-0.5);
    height: 1px;
    background-color: #eee;
  }
  .row_title {
    color: #333;
    margin: 30rpx 30rpx 20rpx;
  }
  .row_label {
    width: 240rpx;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    position: relative;
    color: #333;
    font-size: 28rpx;
    .required_red {
      color: #f00;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 4rpx;
      font-size: 24rpx;
    }
    & ~ .row_value {
      flex: 1;
      font-size: 28rpx;
      color: #666;
      padding: 22rpx 30rpx;
      padding-left: 0;
      box-sizing: border-box;
      text-align: right;
    }
    & ~ .row_value_input {
      display: flex;
      justify-content: center;
      align-items: center;
      .row_value_input_text {
        text-align: right;
        flex: 1;
        font-size: 28rpx;
        color: #333;
      }
      .row_value_input-btn {
        background-color: #005bac;
        color: #fff;
        padding: 0 20rpx;
        margin-left: 20rpx;
      }
    }
    & ~ .row_value_textarea {
      width: 100%;
      padding-left: 30rpx;
      padding-top: 0;
      text-align: left;
      .row_value_textarea_text {
        width: 100%;
        min-height: 160rpx;
        font-size: 28rpx;
        color: #333;
      }
      .textarea-placeholder {
        color: #bbb;
      }
    }
  }
}
</style>
