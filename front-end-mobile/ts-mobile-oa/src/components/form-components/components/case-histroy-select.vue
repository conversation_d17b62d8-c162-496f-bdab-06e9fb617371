<template>
  <view class="patient-hos-info-select-box">
    <view class="input-box">
      <view class="label">
        <text
          class="is-required oa-icon oa-icon-asterisks"
          v-if="required"
        ></text>
        {{ label }}
      </view>
      <view class="input-content">
        <input class="input" v-model="inpatientNo" placeholder="请输入住院号" />
        <button
          class="search-btn"
          type="default"
          size="mini"
          @click="handleSearch"
        >
          查询
        </button>
      </view>
    </view>
    <view class="data-box" v-if="choiceVal.length">
      <view class="data-item" v-for="(t, i) in choiceVal" :key="t.id + '_' + i">
        {{ t.name }}
      </view>
    </view>
    <uni-popup
      class="popper-box"
      type="bottom"
      ref="popper"
      @maskclick="handleCancel"
    >
      <view class="popper-header">
        <view
          class="popper-header_btn popper-header_btn-cancel"
          @touchmove.stop=""
          @tap.stop="handleCancel"
        >
          取消
        </view>
        <view class="popper-header_title">查询病历文书</view>
        <view
          class="popper-header_btn popper-header_btn-confirm"
          @touchmove.stop=""
          @tap.stop="handleConfirm"
        >
          确定
        </view>
      </view>
      <template v-if="level != 0">
        <view class="popper-tips">
          <uni-icons
            class="popper-tips_btn"
            :size="30"
            color="#bbb"
            type="arrowthinleft"
            @click="handleReturn()"
          />
          <view class="popper-tips_text">
            {{
              interwork[level]['parent'] ? interwork[level]['parent'].name : ''
            }}
          </view>
        </view>
        <view class="popper-label">选择病历文书：</view>
      </template>
      <view class="popper-body">
        <view
          class="popper-body_item"
          :class="{ 'is-selected': checkIsSelected(item) }"
          v-for="item in options"
          :key="item.id"
          @click="handleClick(item)"
        >
          {{ item.name }}
        </view>
        <view
          class="popper-body_empty"
          v-if="emptyText && (loading || options.length === 0)"
        >
          {{ emptyText }}
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
export default {
  name: 'case-histroy-select',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    label: {
      type: String,
      default: () => ''
    },
    value: {
      type: String,
      default: () => ''
    },
    required: {
      type: String,
      default: () => ''
    },
    interworkface: {
      type: String,
      default: () => 'HIS'
    }
  },
  data() {
    return {
      visible: false,
      loading: false,
      inpatientNo: '',
      interwork: this.$config[this.interworkface.toUpperCase()] || [],
      choiceVal: [],
      currentVal: [],
      level: 0
    };
  },
  computed: {
    options() {
      return this.interwork[this.level]?.list || [];
    },
    emptyText() {
      if (this.loading) {
        return '加载中';
      } else {
        return this.options.length === 0 ? '无数据' : null;
      }
    }
  },
  watch: {
    value: {
      handler(val) {
        this.choiceVal = val ? JSON.parse(val) : [];
      },
      immediate: true,
      deep: true
    },
    visible(val) {
      if (!val) {
        this.$refs.popper.close();
        this.level = 0;
        this.currentVal = [];
        this.interwork.map(item => {
          delete item.list;
          delete item.parent;
        });
      }
    }
  },
  methods: {
    async handleSearch() {
      if (!this.inpatientNo) {
        uni.showToast({
          title: `请输入住院号`,
          icon: 'none'
        });
        return;
      }
      this.loading = true;
      let list = await this.getInterworkComData({
        name: this.inpatientNo
      });
      this.loading = false;
      if (list.length == 0) {
        uni.showToast({
          title: `未查询到相关数据`,
          icon: 'none'
        });
        return;
      }
      this.$refs.popper.open();
      this.visible = true;
      this.$set(this.interwork[this.level], 'list', list);
    },
    //获取互通数据（电子病历）
    async getInterworkComData(data) {
      let list = [];
      await this.$api
        .request({
          method: `${this.interwork[this.level]['method']}`, //必须大写
          url: `${this.interwork[this.level]['url']}`,
          data: data
        })
        .then(res => {
          list = res.object.map(item => {
            return {
              id: item.id,
              name: item.name
            };
          });
        })
        .catch(e => {
          this.loading = false;
        });
      return list;
    },
    checkIsSelected(column) {
      return this.currentVal.some(item => item.id == column.id);
    },
    async handleClick(column) {
      if (this.level < this.interwork.length - 1) {
        this.loading = true;
        this.level++;
        let list = await this.getInterworkComData({
          id: column.value
        });
        this.loading = false;
        this.$set(this.interwork[this.level], 'list', list);
        this.$set(this.interwork[this.level], 'parent', column);
      } else {
        let multiple = this.interwork[this.level]['multiselect'];
        if (multiple) {
          this.currentVal.push(column);
        } else {
          this.currentVal = [column];
        }
      }
    },
    handleReturn() {
      this.currentVal = [];
      delete this.interwork[this.level].list;
      delete this.interwork[this.level].parent;
      this.level--;
    },
    handleCancel() {
      this.visible = false;
    },
    handleConfirm() {
      if (this.currentVal && this.currentVal.length) {
        this.$emit('input', JSON.stringify(this.currentVal));
        this.visible = false;
      } else {
        uni.showToast({
          title: `请选择病历文书`,
          icon: 'none'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.input-box {
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.input-box .label {
  width: 240rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 28rpx;
  position: relative;
}
.is-required {
  color: #f00;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 4rpx;
  font-size: 24rpx;
}
.input-content {
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 30rpx;
  padding-left: 0;
  box-sizing: border-box;
  text-align: right;
}
.input-content .input {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #333;
}
.input-content .search-btn {
  background-color: #005bac;
  color: #fff;
  padding: 0 20rpx;
  margin-left: 20rpx;
}
.data-box .data-item {
  color: #005bac;
  background-color: #e5edff;
  font-size: 28rpx;
  padding: 0 16rpx;
  border-radius: 20rpx;
  margin: 0 30rpx 20rpx;
}
.popper-box .popper-header {
  background: #fff;
  display: flex;
  text-align: center;
  border-bottom: 1px solid #eee;
}
.popper-box .popper-header .popper-header_btn {
  padding: 10rpx 20rpx;
  font-size: 30rpx;
}
.popper-box .popper-header .popper-header_btn.popper-header_btn-cancel {
  color: #666;
}
.popper-box .popper-header .popper-header_btn.popper-header_btn-confirm {
  color: #005bac;
}
.popper-box .popper-header .popper-header_title {
  font-weight: bold;
  flex: 1;
  color: #333;
  font-size: 30rpx;
}
.popper-body {
  background-color: #ffffff;
  max-height: 400px;
  overflow: auto;
}
.popper-body_item {
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333333;
  position: relative;
  &::after {
    position: absolute;
    z-index: 10;
    right: 0;
    left: 20rpx;
    bottom: 0;
    height: 1px;
    content: '';
    -webkit-transform: scaleY(0.5);
    transform: scaleY(0.5);
    background-color: #eeeeee;
  }
  &:last-child::after {
    height: 0;
  }
}
.popper-tips {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  .popper-tips_btn {
    padding: 0 5px;
  }
  .popper-tips_text {
    padding: 10rpx 0;
    flex: 1;
    font-size: 28rpx;
    color: #666666;
  }
}
.popper-label {
  padding: 10rpx 20rpx 0;
  font-size: 28rpx;
  color: #666666;
  background: #fff;
}
.popper-body_empty {
  text-align: center;
  font-size: 28rpx;
  padding: 40rpx 0;
  color: #999;
}
</style>
