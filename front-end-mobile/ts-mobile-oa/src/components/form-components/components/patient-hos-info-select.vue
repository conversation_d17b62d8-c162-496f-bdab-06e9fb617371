<template>
  <view class="patient-hos-info-select-box">
    <view class="input-box">
      <view class="label">
        <text
          class="is-required oa-icon oa-icon-asterisks"
          v-if="required"
        ></text>
        {{ label }}
      </view>
      <view class="input-content">
        <input class="input" v-model="inpatientNo" placeholder="请输入住院号" />
        <button
          class="search-btn"
          type="default"
          size="mini"
          @click="handleClick"
        >
          查询
        </button>
      </view>
    </view>
    <view class="data-box" v-if="inpatientInfo">
      <view class="data-info">
        <text>姓名：{{ inpatientInfo.name }}</text>
        <text>性别：{{ inpatientInfo.sexName || '' }}</text>
        <text>年龄：{{ inpatientInfo.age || '' }}</text>
      </view>
      <view class="data-info">
        住院号： {{ inpatientInfo.inPatientNo || '' }}
      </view>
      <view class="data-info">
        身份证号： {{ inpatientInfo.idCard || '' }}
      </view>
      <view class="data-info">
        联系电话： {{ inpatientInfo.phone || '' }}
      </view>
      <view class="data-info">
        入院时间： {{ inpatientInfo.phone || '' }}
      </view>
      <view class="data-info">
        <text>当前科室：{{ inpatientInfo.deptName || '' }}</text>
        <text>床号：{{ inpatientInfo.bedNo || '' }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'patient-hos-info-select',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    label: {
      type: String,
      default: () => ''
    },
    value: {
      type: String,
      default: () => ''
    },
    required: {
      type: String,
      default: () => ''
    }
  },
  data() {
    return {
      inpatientNo: '',
      inpatientInfo: null
    };
  },
  watch: {
    value: {
      handler(val) {
        this.inpatientInfo = val ? JSON.parse(val) : null;
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleClick() {
      if (!this.inpatientNo) {
        uni.showToast({
          title: `请输入住院号`,
          icon: 'none'
        });
        return;
      }
      this.ajax
        .getHisInPatientInfo({
          number: this.inpatientNo
        })
        .then(res => {
          if (res.object && res.object.length) {
            this.$emit('input', JSON.stringify(res.object[0]));
          } else {
            this.$emit('input', null);
            uni.showToast({
              title: `未查询到相关数据`,
              icon: 'none'
            });
          }
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.input-box {
  background-color: #ffffff;
  overflow: hidden;
  display: flex;
  align-items: center;
}
.input-box .label {
  width: 240rpx;
  padding: 20rpx 30rpx;
  box-sizing: border-box;
  color: #333;
  font-size: 28rpx;
  position: relative;
}
.is-required {
  color: #f00;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  left: 4rpx;
  font-size: 24rpx;
}
.input-content {
  display: flex;
  align-items: center;
  flex: 1;
  font-size: 28rpx;
  color: #666;
  padding: 20rpx 30rpx;
  padding-left: 0;
  box-sizing: border-box;
  text-align: right;
}
.input-content .input {
  flex: 1;
  text-align: right;
  font-size: 28rpx;
  color: #333;
}
.input-content .search-btn {
  background-color: #005bac;
  color: #fff;
  padding: 0 20rpx;
  margin-left: 20rpx;
}
.data-box {
  color: #005bac;
  background-color: #e5edff;
  font-size: 28rpx;
  padding: 0 16rpx;
  margin: 0 15px 10px;
  border-radius: 20rpx;
}
.data-box .data-info {
  font-size: 28rpx;
  display: flex;
  justify-content: space-between;
}
</style>
