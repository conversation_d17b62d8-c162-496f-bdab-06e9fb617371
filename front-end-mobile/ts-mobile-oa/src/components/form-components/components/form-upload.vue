<template>
  <view>
    <view
      v-if="!disabled"
      class="file_add_icon oa-icon oa-icon-tupiantianjia"
      @click="handleUpload"
    ></view>
    <view class="row_value file_list">
      <view class="file_item" v-for="file of fileList" :key="file.id">
        <view class="file_item_info" @click="handlePreviewFile(file)">
          <view
            class="oa-icon"
            :class="'oa-icon-' + $oaModule.formatFileType(file.fileType)"
          ></view>
          <view class="file_item_name">
            <text class="file_name">{{ file.fileName }}</text>
            <text class="file_size">{{ file.fileSize | fileSizeFilter }}</text>
          </view>
        </view>
        <text
          class="oa-icon oa-icon-xiazai delete_file"
          @click.stop="handleDownloadFile(file)"
        ></text>
        <text
          v-if="!disabled"
          class="oa-icon oa-icon-guanbi delete_file"
          @click.stop="handleDelete(file)"
        ></text>
      </view>
    </view>
  </view>
</template>

<script>
import { chooseImage } from '@/common/js/uploadImg.js';
import Base64 from '@/common/js/base64.min.js';

export default {
  model: {
    prop: 'businessId',
    event: 'input'
  },
  props: {
    businessId: {},
    disabled: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      fileList: []
    };
  },
  methods: {
    /**@desc 获取已上传文件 */
    getUploadedFiles() {
      this.ajax
        .getFileAttachmentByBusinessId({
          businessId: this.businessId
        })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.fileList = res.object.map(file => {
            let {
              id,
              originalName,
              fileName,
              realPath,
              fileSize,
              fileExtension
            } = file;
            return {
              id,
              originalName,
              fileName,
              fileUrl: realPath,
              fileSize,
              fileType: fileExtension
            };
          });
        });
    },
    /**@desc 通过 BusinessId 作为key值的文件上传 */
    handleUpload() {
      let businessId = this.businessId || this.$common.createUUID();
      chooseImage({
        limitNum: 9, //数量
        uploadFileUrl:
          `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/upload?moduleName=oa&businessId=` +
          businessId, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res),
            fileList = resVal.object.map(i => {
              return {
                id: i.fileId,
                originalName: i.fileRealName,
                fileName: i.fileName,
                fileUrl: i.filePath,
                fileSize: i.fileSize,
                fileType: i.fileExtension
              };
            });

          this.fileList = this.fileList.concat(fileList);
          !this.businessId && this.$emit('input', businessId);
        }
      });
    },
    /**@desc 删除文件 */
    handleDelete(file) {
      this.ajax
        .deletFileById({
          fileid: file.id
        })
        .then(res => {
          if (!res.success) {
            uni.showToast({
              icon: 'none',
              title: res.message || '删除失败'
            });
            return;
          }
          uni.showToast({
            icon: 'none',
            title: '删除成功'
          });
          let index = this.fileList.findIndex(item => item.id == file.id);
          if (index >= 0) {
            this.fileList.splice(index, 1);
          }
          !this.fileList.length && this.$emit('input', null);
        });
    },
    /**@desc 下载文件 */
    handleDownloadFile({ id, fileName }) {
      let filePath = `${this.$config.BASE_HOST}/ts-basics-bottom/fileAttachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    /**@desc 文件预览 */
    handlePreviewFile(file) {
      let filePath =
          // location.origin +
          'http://**************:9088' +
          file.fileUrl +
          '?fullfilename=' +
          file.fileId +
          '.' +
          (file.fileType || file.fileName.split('.')[1]) +
          '&source=mobile',
        fileList = JSON.parse(JSON.stringify(this.fileList));
      if (!fileList.length) {
        fileList = [file];
      }
      uni.setStorageSync('fileList', fileList);
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    }
  },
  watch: {
    businessId: {
      handler(val) {
        val && this.getUploadedFiles();
      },
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.file_add_icon {
  padding: 0 30rpx;
  font-size: 56rpx;
  position: absolute;
  top: 0;
  right: 0;
  color: #bbb;
}
.file_list {
  .file_item {
    text-decoration: none;
    font-size: 28rpx;
    color: #333333;
    margin: 10rpx 20rpx 20rpx;
    padding: 6rpx 20rpx;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    .file_item_info {
      text-decoration: none;
      flex: 1;
      text-align: left;
      display: flex;
      align-items: center;
      .file_item_name {
        flex: 1;
        margin: 0 20rpx;
      }
    }
    .oa-icon {
      font-size: 40rpx;
    }
    .delete_file {
      color: #005bac;
      margin-left: 8px;
    }
    .file_name {
      font-size: 28rpx;
      color: #333333;
    }
    .file_size {
      color: #999999;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }
}
</style>
