<template>
  <uni-popup type="allBottom" ref="processPopup" @maskclick="maskConfirm">
    <view class="process-popup-select-box">
      <view class="popup-header" :style="titleStyle">
        <view class="popup-header-title" :style="titleStyle">{{ title }}</view>
        <view
          class="popup-header-right"
          :style="rightTextStyle"
          @click="handleRightClick"
        >
          {{ rightText }}
        </view>
      </view>
      <view class="mescroll-content">
        <mescroll
          ref="mescroll"
          :page="scrollPage"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <data-check
            mode="list"
            iconPosition="right"
            :multiple="multiple"
            v-model="selectValue"
            :checkList="list"
            selectedColor="#005BAC"
            :fontSize="fontSize"
            :map="{
              text: 'workflowName',
              value: 'wfInstanceId'
            }"
          >
            <template #content="data">
              <view class="process-info" :style="data.info.styleIconText">
                <text class="process-info__date">
                  {{ data.info.createDate }}
                </text>
                <text
                  class="process-info__status"
                  :class="
                    data.info.status == '1' ? 'is-handling' : 'is-completed'
                  "
                >
                  【{{ data.info.status == '1' ? '在办' : '办结' }}】
                </text>
              </view>
            </template>
          </data-check>
        </mescroll>
      </view></view
    >
  </uni-popup>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  name: 'process-popup-select',
  components: {
    mescroll
  },
  props: {
    title: {
      type: String,
      default: '流程选择'
    },
    titleStyle: {
      type: Object,
      default: () => {}
    },
    rightText: {
      type: String,
      default: '确定'
    },
    rightTextStyle: {
      type: Object,
      default: () => {}
    },
    multiple: {
      type: Boolean,
      default: true
    },
    value: {
      type: [Array, String, Number],
      default: () => ''
    },
    fontSize: {
      type: [Number, String],
      default: '16'
    },
    params: {
      type: Object,
      defalut: () => {}
    },
    apiFunction: {
      type: Function
    }
  },
  data() {
    return {
      selectValue: null,
      scrollPage: {
        num: 0,
        size: 10000
      },
      list: [],
      selectList: []
    };
  },
  watch: {
    value: {
      async handler(val) {
        this.selectValue = val;
      }
    }
  },
  methods: {
    getListData(page, successCallback, errorCallback) {
      this.apiFunction({
        ...this.params,
        pageNo: page.num,
        pageSize: page.size
      })
        .then(res => {
          successCallback(res.rows);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows) {
      this.list = this.list.concat(rows);
    },
    datasInit() {
      this.list = [];
    },
    open() {
      this.$refs['processPopup'].open();
    },
    handleRightClick() {
      let selectList = this.list.filter(item => {
        return this.selectValue.includes(item.wfInstanceId);
      });
      this.$emit('change', {
        value: this.selectValue,
        data: selectList
      });
      this.$refs['processPopup'].close();
    }
  }
};
</script>

<style lang="scss" scoped>
.process-popup-select-box {
  height: 100%;
  display: flex;
  flex-direction: column;
}
.popup-header {
  height: 44px;
  line-height: 44px;
  font-size: 16px;
  background-color: #fff;
  box-shadow: 0 1px 6px #cccccc;
  width: 100%;
  position: relative;
  z-index: 999;
}
.popup-header-title {
  width: 100%;
  height: 100%;
  font-weight: bold;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: center;
}
.popup-header-right {
  position: absolute;
  color: $theme-color;
  top: 0;
  right: 0;
  box-sizing: border-box;
  height: 100%;
  padding: 0 10px;
  display: flex;
  align-items: center;
}
.mescroll-content {
  flex: 1;
  position: relative;
}
.process-info {
  font-size: 14px !important;
  color: #666;
}
.process-info__status {
  margin-left: 20px;
}
.is-handling {
  color: $theme-color;
}
.is-completed {
  color: #666;
}
</style>
