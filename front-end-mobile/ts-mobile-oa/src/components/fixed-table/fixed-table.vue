<template>
  <view
    class="fixed-table"
    :style="{ height: height + 'px' }"
    :class="{
      fixedheader: fixed,
      fixedFirst: firstFixed,
      stripedTable: stripe
    }"
  >
    <table cellspacing="0" border="0" cellpadding="0">
      <thead>
        <tr>
          <th
            v-for="(item, index) in header"
            :class="{ border: border }"
            :width="item.width"
            :key="index"
          >
            <view
              class="item"
              :class="[item.align, item.overflow ? 'overflowtext' : '']"
              >{{ item.title }}</view
            >
          </th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in data" :key="index" @click="clickRow(item)">
          <td
            v-for="(v, idx) in header"
            :class="[border, item.className ? item.className : '']"
            :width="v.width"
            :key="idx"
            @click="v.click(item[v.prop])"
          >
            <view
              class="item"
              :class="[v.align, v.overflow ? 'overflowtext' : '']"
            >
              <image
                v-if="item[v.prop] && v.phoneIcon"
                class="phone_img"
                src="../../static/img/phone.png"
                mode="aspectFit"
              />
              {{
                (v['formatter'] && v['formatter'](item[v.prop])) || item[v.prop]
              }}
            </view>
          </td>
        </tr>
      </tbody>
    </table>
  </view>
</template>
<script>
export default {
  props: {
    // 列表数据
    data: {
      type: Array,
      default: () => [],
      require: true
    },
    // 表头列表
    header: {
      type: Array,
      default: () => [],
      require: true
    },
    height: {
      type: Number,
      default: 400
    },
    border: {
      type: Boolean,
      default: true
    },
    fixed: {
      type: Boolean,
      default: false
    },
    firstFixed: {
      type: Boolean,
      default: false
    },
    stripe: {
      type: Boolean,
      default: false
    },
    showActions: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    clickRow(item) {
      // this.$emit('row-click', item);
    }
  }
};
</script>
<style lang="scss">
.fixed-table {
  overflow: auto;
  max-height: 100vh; /* 设置固定高度 */
  .phone_img {
    width: 18px;
    height: 18px;
    margin-right: 4px;
  }
  &.stripedTable {
    table {
      tbody {
        tr {
          &:nth-child(odd) {
            td {
              background: #f5f5f5;
            }
          }
        }
      }
    }
  }
  td,
  th {
    /* 设置td,th宽度高度 */
    padding: 5px 12px;
    background: #fff;
  }

  &.fixedheader {
    table {
      table-layout: fixed;
      width: 100%; /* 固定宽度 */
    }
  }
  td,
  th {
    border-bottom: 1px solid #ebeef5;
    view.item {
      text-align: center;
      display: flex;
      align-items: center;
      &.left {
        text-align: left;
      }
      &.center {
        text-align: center;
      }
      &.right {
        text-align: right;
      }
      &.overflowtext {
        -o-text-overflow: -o-ellipsis-lastline;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }
  }
  &.fixedFirst {
    table {
      table-layout: fixed;
      width: 100%; /* 固定宽度 */
      td:first-child,
      th:first-child {
        position: sticky;
        left: 0; /* 首行永远固定在左侧 */
        z-index: 2;
      }
      th:first-child {
        z-index: 3;
      }
    }
  }
  &.fixedheader {
    th:first-child {
      z-index: 1;
    }
    thead tr th {
      position: sticky;
      top: 0; /* 列首永远固定在头部  */
      background: #fff;
    }
  }
  .border {
    border-right: 1px solid #ebeef5;
  }
}
</style>
