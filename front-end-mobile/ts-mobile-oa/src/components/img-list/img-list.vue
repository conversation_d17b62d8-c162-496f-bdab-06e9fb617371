<template>
  <view>
    <!-- #ifndef APP-NVUE -->
    <view class="img-list">
      <slot />
    </view>
    <!-- #endif -->
    <!-- #ifdef APP-NVUE -->
    <list
      class="img-list"
      :enableBackToTop="enableBackToTop"
      loadmoreoffset="15"
      :scroll-y="scrollY"
      @loadmore="loadMore"
    >
      <slot />
    </list>
    <!-- #endif -->
  </view>
</template>

<script>
/**
 * imgList 图片列表
 * @description 列表组件
 */
export default {
  name: 'ImgList',
  'mp-weixin': {
    options: {
      multipleSlots: false
    }
  },
  props: {
    enableBackToTop: {
      type: [Boolean, String],
      default: false
    },
    scrollY: {
      type: [Boolean, String],
      default: false
    }
  },
  provide() {
    return {
      list: this
    };
  },
  created() {
    this.firstChildAppend = false;
  },
  methods: {
    loadMore(e) {
      this.$emit('scrolltolower');
    }
  }
};
</script>
<style lang="scss" scoped>
.img-list {
  width: 100%;
  background-color: $uni-bg-color;
  overflow-x: scroll;
  display: -webkit-box;
}
</style>
