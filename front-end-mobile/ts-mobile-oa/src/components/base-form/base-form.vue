<template>
  <view class="form">
    <view class="form-container">
      <u-form
        class="form-box"
        ref="uForm"
        :model="form"
        :error-type="errorType"
      >
        <u-form-item
          v-for="(item, index) in formList"
          :key="index"
          :required="item.required"
          :label-width="item.labelWidth || $formLabelWidth"
          :label-align="item.labelAlign"
          :label-position="
            item.labelPosition
              ? item.labelPosition
              : labelPositionFilter(
                  item.type,
                  $formInputLabelPosition,
                  $formTextareaLabelPosition
                )
          "
          :label="item.title"
          :prop="item.prop"
        >
          <template #left>
            <text
              v-if="item.labelSlot"
              :class="item.labelSlotClass"
              :style="item.labelSlotStyle"
              @click="labelSlotClick(item)"
            >
              {{ item.labelSlot }}
            </text>
          </template>
          <template
            #right
            v-if="item.type == 'radio' || item.type == 'rate' || item.rightSlot"
          >
            <u-radio-group
              v-if="item.type == 'radio'"
              v-model="form[item.prop]"
              @change="radioGroupChange($event, item)"
              :wrap="item.radioCheckWrap"
              :disabled="item.disabled"
            >
              <u-radio
                shape="circle"
                v-for="(radioItem, radioIndex) in item.radioList"
                :key="radioIndex"
                :name="radioItem.value"
              >
                {{ radioItem.label }}
              </u-radio>
            </u-radio-group>
            <u-rate
              v-else-if="item.type == 'rate'"
              :count="item.count"
              v-model="form[item.prop]"
              :inactive-color="item.inactiveColor"
              :active-color="item.activeColor"
              :size="item.size"
              :active-icon="item.activeIcon"
              :inactive-icon="item.inactiveIcon"
              :custom-prefix="item.customPrefix"
            >
            </u-rate>
            <text
              v-if="item.rightSlot"
              :class="item.rightSlotClass"
              :style="item.rightSlotStyle"
              @click="rightSlotClick(item)"
            >
              <slot :name="item.rightSlot">{{ form[item.prop] }}</slot>
            </text>
          </template>
          <template
            #default
            v-if="
              item.type == 'file' ||
                item.type == 'select' ||
                item.type == 'text' ||
                item.type == 'number' ||
                item.type == 'switch' ||
                item.type == 'textarea'
            "
          >
            <view
              v-if="item.type == 'file' && item.propVal"
              class="flex-column"
            >
              <view class="icon_box" v-if="item.disabled != true">
                <form
                  class="addBox oa-icon oa-icon-plus-circle"
                  ref="fileinput"
                  @click.stop="uploadedFile(item)"
                ></form>
              </view>
              <view
                class="file_list"
                v-if="form[item.propVal] && form[item.propVal].length"
              >
                <view
                  class="file_item"
                  v-for="t in form[item.propVal]"
                  :key="t.fileId"
                >
                  <view
                    class="file_item_info"
                    @click="previewFile(t.fileId, t.fileRealName)"
                  >
                    <view
                      class="oa-icon"
                      :class="
                        'oa-icon-' + $oaModule.formatFileType(t.fileExtension)
                      "
                    ></view>
                    <view class="file_item_name">
                      <text class="file_name">{{
                        t.fileRealName || t.originalName
                      }}</text>
                      <text class="file_size">{{
                        t.fileSize | fileSizeFilter
                      }}</text>
                    </view>
                  </view>
                  <text
                    class="oa-icon oa-icon-xiazai delete_file"
                    style="margin-right: 10rpx;"
                    v-if="
                      item.moduleName == 'document' ||
                        item.moduleName == 'personal'
                    "
                    @click.stop="downloadFile(t.fileId, t.fileRealName)"
                  ></text>
                  <text
                    v-if="item.disabled != true"
                    class="oa-icon oa-icon-guanbi delete_file"
                    @click.stop="deletFile(t.fileId, item)"
                  ></text>
                </view>
              </view>
            </view>
            <view v-else-if="item.type == 'switch'" class="switch_box">
              <u-switch
                size="34"
                v-model="formData[item.prop]"
                @change="switchChange($event, item.prop)"
              ></u-switch
              ><text v-if="item.switchLabel" class="switch_label">
                {{ item.switchLabel }}
              </text>
            </view>
            <u-input
              v-else
              :class="inputClass(item)"
              :key="item.props"
              :border="$formInputBorder"
              :height="item.height"
              :type="item.type"
              :placeholder="item.placeholder"
              :disabled="item.disabled"
              :input-align="
                item.inputAlign
                  ? item.inputAlign
                  : inputAlignFilter(
                      item.type,
                      item.labelPosition,
                      $formInputAlign,
                      $formTextareaAlign
                    )
              "
              :maxlength="item.maxlength"
              v-model="form[item.prop]"
              trim
              @blur="item.blurCallback ? item.blurCallback($event, item) : ''"
              @input="item.callback ? changeInputVal($event, item) : ''"
              @click="
                item.type == 'select' && item.mode == 'select'
                  ? changeSelectShow(item, index)
                  : item.type == 'select' && item.mode == 'time'
                  ? changePickerShow(item)
                  : item.type == 'select' && item.mode == 'person'
                  ? choosePerson(item)
                  : item.type == 'select' && item.mode == 'dept'
                  ? chooseDept(item)
                  : item.type == 'select' && item.mode == 'callBack'
                  ? handleCallBack(item)
                  : item.type == 'select' && item.mode == 'range-picker'
                  ? chooseRangePicker(item)
                  : ''
              "
            ></u-input>
          </template>
        </u-form-item>
      </u-form>
      <view class="button-box" v-if="showSubmitButton">
        <u-button type="primary" @click="submit">{{ submitTitle }}</u-button>
      </view>
    </view>
    <u-select
      mode="single-column"
      :list="selctAllObj[clickProp]"
      v-model="selectShow"
      :default-value="selectDefaultValue"
      @confirm="selectConfirm"
    ></u-select>
    <u-picker
      :mode="clickMode"
      v-model="pickerShow"
      :params="clickParams"
      @confirm="pickerConfirm"
    ></u-picker>

    <!-- 时间区间 -->
    <base-time-range-picker
      v-model="rangePickerShow"
      :defaultValue="rangePickerValue"
      :format="rangePickerFormat"
      :rangeDate="rangePickerRangeDate"
      @confirm="timeRangePickerConfirm"
    ></base-time-range-picker>
  </view>
</template>

<script>
import { chooseImage, chooseFile } from '@/common/js/uploadImg.js';
import Base64 from '@/common/js/base64.min.js';
import BaseTimeRangePicker from '@/components/base-time-range-picker/index.vue';
export default {
  name: 'base-form',
  components: {
    BaseTimeRangePicker
  },
  props: {
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    submitTitle: {
      type: String,
      default: '提交'
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    showSubmitButton: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      selectDefaultValue: [],
      errorType: ['toast'],
      selctAllObj: {},
      clickProp: null,
      clickPropVal: null,
      relationProp: [],
      clickMode: null,
      clickParams: null,
      clickField: null,
      selectShow: false,
      pickerShow: false,
      personLabel: {},
      deptLabel: {},
      form: this.formData,

      rangePickerShow: false,
      rangePickerValue: [],
      rangePickerFormat: 'YYYY-MM-DD HH:mm:ss',
      rangePickerRangeDate: []
    };
  },
  watch: {
    formList: {
      handler(newVal = [], oldVal = []) {
        this.init(newVal, oldVal);
      },
      immediate: true,
      deep: true
    },
    form(newVal) {
      this.$emit('update:formData', newVal);
    }
  },
  mounted() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    labelPositionFilter(type, inputLable, textareaLable) {
      if (type == 'textarea' || type == 'file') {
        return textareaLable;
      } else {
        return inputLable;
      }
    },
    inputAlignFilter(type, labelPosition, inputAlign, textareaAlign) {
      if (type == 'textarea') {
        return textareaAlign;
      } else {
        if (labelPosition == 'top') {
          return 'left';
        }
        return inputAlign;
      }
    },
    //初始化选择项、校验规则
    init(newFormList, oldFormList) {
      let selctAllObj = {},
        personLabel = {},
        deptLabel = {};

      newFormList.forEach(item => {
        let prop = item.prop;
        if (item.optionList) {
          selctAllObj[prop] = item.optionList;
        }

        if (item.type == 'select' && item.mode == 'person') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'person' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            personLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            personLabel[prop] = [];
          }
        } else if (item.type == 'select' && item.mode == 'dept') {
          let oldItemIndex = oldFormList.findIndex(
            oldItem =>
              oldItem.type == 'select' &&
              oldItem.mode == 'dept' &&
              oldItem.prop == prop
          );
          if (oldItemIndex >= 0) {
            deptLabel[prop] = JSON.parse(
              JSON.stringify(this.personLabel[prop] || [])
            );
          } else {
            deptLabel[prop] = [];
          }
        }
      });
      this.selctAllObj = JSON.parse(JSON.stringify(selctAllObj));
      this.personLabel = JSON.parse(JSON.stringify(personLabel));
      this.deptLabel = JSON.parse(JSON.stringify(deptLabel));

      //抛出初始化完成事件，用以解决初始化数据清空问题, 或者初始化赋值问题
      this.$emit('init-finished');
    },
    //单选
    radioGroupChange(e, item) {
      this.$set(this.form, item.prop, e);
      if (item.callback) {
        item.callback(e);
      }
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.form, item.prop, item.callback(e, item));
      });
    },
    //打开列选择器
    async changeSelectShow(e) {
      if (e.disabled) return;
      if (e.type === 'select' && e.mode === 'select' && this.form[e.propVal]) {
        this.selectDefaultValue = [
          e.optionList.findIndex(fl => fl.value === this.form[e.propVal])
        ];
      }
      this.relationProp = e.relationProp || [];

      if (e.searchParams && e.searchParams.length > 0) {
        let param = [];
        for (var i = 0; i < e.searchParams.length; i++) {
          if (!this.form[e.searchParams[i].value]) {
            this.$u.toast(e.searchParams[i].message);
            return false;
          }
          param.push(
            `${e.searchParams[i].name}=${this.form[e.searchParams[i].value]}`
          );
        }
        let optionList = await this.getDatas(
          `${e.searchApi}?${param.join('&')}`
        );
        this.$set(this.selctAllObj, e.prop, optionList);
      }
      this.$nextTick(() => {
        this.selectShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (this.form[this.clickPropVal] !== e[0].value) {
        this.$set(this.form, this.clickProp, e[0].label);
        this.$set(this.form, this.clickPropVal, e[0].value);
        this.selectDefaultValue = [];
        if (this.relationProp.length > 0) {
          for (var i = 0; i < this.relationProp.length; i++) {
            this.$set(this.form, this.relationProp[i].prop, '');
            if (this.relationProp[i].propVal) {
              this.$set(this.form, this.relationProp[i].propVal, '');
            }
          }
        }
      }
    },
    //打开选择器
    changePickerShow(e) {
      if (e.disabled) return;
      this.pickerShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.clickParams = e.params;
      this.clickField = e.field;
    },
    async getDatas(api) {
      let datas = await this.ajax.getDatas(api);
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    //时间确认事件
    pickerConfirm(e) {
      if (this.clickMode == 'time') {
        this.$set(
          this.form,
          this.clickProp,
          this.timePickerConfirm(e, this.clickField)
        );
      }
    },
    //格式化时间
    timePickerConfirm(e, field = 'yy-MM-dd') {
      if (field == 'YY') {
        return `${e.year}年`;
      } else if (field == 'yy-MM') {
        return `${e.year}-${e.month}`;
      } else if (field == 'yy-MM-dd') {
        return `${e.year}-${e.month}-${e.day}`;
      } else if (field == 'HH:mm') {
        return `${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm:ss') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
      }
    },
    //选择人员
    choosePerson(item) {
      let personList = this.personLabel[item.prop] || [];
      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.name,
        personInfoProp: item.personInfoProp
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < (item.searchParams || []).length; i++) {
          if (
            !this.form[item.searchParams[i].value] &&
            item.searchParams[i].message
          ) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        personPageParams = { ...personPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        this.$set(this.personLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        } else {
          let personNameList = [],
            personIdList = [],
            personName =
              (item.personInfoProp && item.personInfoProp.name) || 'name',
            psersonId =
              (item.personInfoProp && item.personInfoProp.key) || 'userId';

          data.map(i => {
            personNameList.push(i[personName]);
            personIdList.push(i[psersonId]);
          });
          this.$set(this.form, item.prop, personNameList.join(','));
          this.$set(this.form, item.propVal, personIdList.join(','));
        }
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
        this.$forceUpdate();
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&searchType=${item.searchType ||
          'workSheetPost'}`
      });
    },
    chooseDept(item) {
      if (item.disabled == true) {
        return;
      }
      let deptList = this.deptLabel[item.prop] || [];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenDept', data => {
        this.$set(this.deptLabel, item.prop, data);
        if (item.changeCallback) {
          item.changeCallback(data, item);
        }
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.id);
        });
        this.$set(this.form, item.prop, deptName.join(','));
        this.$set(this.form, item.propVal, deptId.join(','));
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenDept');
        this.$forceUpdate();
      });

      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${
          item.chooseType
        }&getListMode=${item.getListMode}&mode=${item.selectMode || 'scroll'}`
      });
    },
    handleCallBack(item) {
      this.$emit(item.callBackName, item);
    },
    // 打开时间区间选择器
    chooseRangePicker(e) {
      if (e.disabled == true) {
        return;
      }
      this.$emit('open-range-picker-before', e);
      this.rangePickerValue = this.formData[e.propVal];
      this.rangePickerFormat = e.format || this.rangePickerFormat;
      this.rangePickerRangeDate = e.rangeDate || this.rangePickerRangeDate;
      this.$nextTick(() => {
        this.rangePickerShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    // 时间区间选择器回调
    timeRangePickerConfirm(e) {
      if (e) {
        this.$set(this.formData, this.clickProp, e.join('-'));
        this.$set(this.formData, this.clickPropVal, e);
        this.$emit('handld-rang-date', e);
      }
    },
    switchChange(val, field) {
      this.$emit('switchChange', { field, value: val });
    },
    inputClass(item) {
      let classList = [];
      if (item.type == 'select') {
        classList.push('select-input-contanier');
      }
      return classList.concat(item.class);
    },
    //上传文件
    uploadedFile(item) {
      if (item.disabled) return;
      let _self = this;
      if (!item.moduleName) {
        chooseFile({
          limitNum: item.limitNum || 9, //数量
          uploadFileUrl: `${
            _self.$config.BASE_HOST
          }/ts-basics-bottom/fileAttachment/upload?moduleName=oa&businessId=${
            this.form[item.prop]
          }`,
          fileKeyName: 'file', //参数
          showLoading: true,
          hideLoading: true,
          loadingTitle: '上传中...',
          success: resVal => {
            let res = JSON.parse(resVal);
            if (res.success) {
              this.form[item.propVal].push({
                ...res.object[0],
                realPath: res.object[0].filePath,
                url: res.object[0].filePath,
                uid: res.object[0].fileId,
                name: res.object[0].fileRealName
              });
            } else {
              uni.showToast({
                title: '上传失败',
                icon: 'none'
              });
            }
          }
        });
      } else {
        chooseFile({
          limitNum: item.limitNum || 9, //数量
          uploadFileUrl: `${_self.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=${item.moduleName}`,
          fileKeyName: 'file', //参数
          showLoading: true,
          hideLoading: true,
          loadingTitle: '上传中...',
          success: resVal => {
            let res = JSON.parse(resVal);
            if (res.success) {
              let name = res.object[0].fileRealName;
              this.form[item.propVal].push({
                ...res.object[0],
                fileRealName: res.object[0].fileName,
                fileName: name,
                realPath: res.object[0].filePath,
                url: res.object[0].filePath,
                uid: res.object[0].fileId,
                name: res.object[0].fileName
              });
            } else {
              uni.showToast({
                title: '上传失败',
                icon: 'none'
              });
            }
          }
        });
      }
    },
    // 下载附件
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    //删除附件
    async deletFile(fileId, item) {
      if (item.moduleName == 'document' || item.moduleName == 'personal') {
        let idx = this.form[item.propVal].findIndex(e => {
          return e.fileId === fileId;
        });
        this.form[item.propVal].splice(idx, 1);
        this.$forceUpdate();
      } else {
        let idx = this.form[item.propVal].findIndex(e => {
            return e.fileId === fileId;
          }),
          deleteFile = this.form[item.propVal][idx] || {};
        const res = await this.ajax
          .deleteFileId({ fileid: deleteFile.fileId })
          .then(res => {
            if (!res.success) {
              uni.showToast({
                title: '删除失败!',
                icon: 'none'
              });
              return;
            }
            uni.showToast({
              title: '删除成功!',
              icon: 'none'
            });
            this.form[item.propVal].splice(idx, 1);
          });
      }
    },
    previewFile(id, fileName) {
      let _self = this,
        filePath = `${_self.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    //label右侧控件点击事件
    labelSlotClick(e) {
      if (e.labelSlotCallback) {
        e.labelSlotCallback(e);
      }
    },
    rightSlotClick(e) {
      if (e.labelSlotCallback) {
        e.rightSlotClick(e);
      }
    },
    validate() {
      let validVal = false;
      this.$refs.uForm.validate(valid => {
        validVal = valid;
      });
      return validVal;
    },
    //提交表单
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          this.$emit('submit', valid);
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: $uni-spacing-col-base;
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
.switch_box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  .switch_label {
    font-size: 28rpx;
    font-weight: 400;
    color: #333;
    margin-left: 16rpx;
  }
}
/deep/ .flex-column {
  width: 100%;
  height: 100%;
  .icon_box {
    width: 100%;
    height: 100%;
    position: relative;
    text-align: right;
    .addBox {
      font-size: 36rpx;
      right: 0;
      top: 0;
    }
  }
}
/deep/ .u-radio__icon-wrap--disabled--checked {
  color: #2979ff !important;
  background-color: #2979ff !important;
  border-color: #2979ff !important;
}
/deep/ .u-radio__icon-wrap--disabled {
  background: none;
  border-color: none;
}
.file_list {
  .file_item {
    text-decoration: none;
    font-size: 32rpx;
    color: #333333;
    margin: 10rpx 20rpx 20rpx;
    padding: 6rpx 20rpx;
    border: 1px solid #eeeeee;
    border-radius: 5px;
    margin-bottom: 20rpx;
    display: flex;
    align-items: center;
    .file_item_info {
      text-decoration: none;
      flex: 1;
      text-align: left;
      display: flex;
      align-items: center;
      .file_item_name {
        flex: 1;
        margin: 0 20rpx;
      }
    }
    .oa-icon {
      font-size: 40rpx;
      color: $theme-color;
    }
    .file_name {
      font-size: 28rpx;
      color: #333333;
    }
    .file_size {
      color: #999999;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }
}
/deep/ .select-input-contanier .uni-input-input {
  pointer-events: none;
}
</style>
