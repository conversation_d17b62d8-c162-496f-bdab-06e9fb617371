<template>
  <view class="img-item">
    <image
      :src="imgSrc"
      mode="aspectFill"
      :style="imgStyle"
      @click="onClick"
    ></image>
    <slot></slot>
  </view>
</template>

<script>
/**
 * imgList 图片列表
 * @description 列表组件
 */
export default {
  name: 'ImgListItem',
  props: {
    imgSrc: {
      type: String,
      default: ''
    },
    imgStyle: {
      type: String,
      default: ''
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    }
  }
};
</script>
<style lang="scss" scoped>
.img-item {
  width: 136rpx;
  height: 136rpx;
  margin: 10px 5px;
  border-radius: 4rpx;
  overflow: hidden;
  position: relative;
}
.img-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
}
</style>
