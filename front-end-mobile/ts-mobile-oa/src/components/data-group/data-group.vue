<template>
  <view
    class="uni-group"
    :class="['uni-group--' + mode, margin ? 'group-margin' : '']"
    :style="{ marginTop: `${marginTop}px` }"
  >
    <view
      v-if="title"
      class="uni-group__title"
      :style="{ 'background-color': titleBgColor }"
    >
      <view class="uni-group__title-text">{{ title }}</view>
      <slot name="title"></slot>
    </view>
    <view
      class="uni-group__content"
      :class="{ 'group-conent-padding': border }"
    >
      <slot />
    </view>
  </view>
</template>

<script>
/**
 * Group 分组
 * @description 表单字段分组
 * @property {String} title 主标题
 * @property {Number} marginTop 分组间隔
 * @property {Number} mode 模式
 */
export default {
  name: 'DataGroup',
  emits: ['click'],
  props: {
    title: {
      type: String,
      default: ''
    },
    marginTop: {
      type: [Number, String],
      default: 10
    },
    titleBgColor: {
      type: String,
      default: '#FFFFFF'
    },
    mode: {
      type: String,
      default: 'default'
    }
  },
  data() {
    return {
      margin: false,
      border: false
    };
  },
  created() {
    this.form = this.getForm();
    if (this.form) {
      this.margin = true;
      this.border = this.form.border;
    }
  },
  methods: {
    /**
     * 获取父元素实例
     */
    getForm() {
      let parent = this.$parent;
      let parentName = parent.$options.name;
      while (parentName !== 'uniForms') {
        parent = parent.$parent;
        if (!parent) return false;
        parentName = parent.$options.name;
      }
      return parent;
    },
    onClick() {
      this.$emit('click');
    }
  }
};
</script>
<style lang="scss" scoped>
$list-item-pd: $uni-spacing-col-base $uni-spacing-row-lg;
.uni-group {
  background: #fff;
  margin-top: 10px;
  // border: 1px red solid;
}

.group-margin {
  // margin: 0 -15px;
}

.uni-group__title {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  align-items: center;
  background-color: $uni-bg-color-grey;
  font-weight: normal;
  color: $uni-text-color;
}

.uni-group__content {
  padding: 0 15px;
  border-top: 1px solid #eee;
}

.group-conent-padding {
  padding: 0 15px;
}

.uni-group__title-text {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  font-weight: bold;
  padding: $list-item-pd;
}

.distraction {
  flex-direction: row;
  align-items: center;
}

.uni-group--card {
  margin: 10px;
  border-radius: 5px;
  overflow: hidden;
  box-shadow: 0 0 5px 1px rgba($color: #000000, $alpha: 0.08);
}
</style>
