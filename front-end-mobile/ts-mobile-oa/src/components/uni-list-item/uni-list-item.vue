<template>
  <!-- #ifdef APP-NVUE -->
  <cell>
    <!-- #endif -->
    <view
      :class="disabled ? 'uni-list-item--disabled' : ''"
      :hover-class="disabled || showSwitch ? '' : 'uni-list-item--hover'"
      class="uni-list-item"
      @click="onClick"
      :style="listItemStyle"
    >
      <view
        class="uni-list-item__container"
        :class="{ 'uni-list-item--first': isFirstChild }"
        :style="listItemStyle"
      >
        <view>
          <view v-if="thumb" class="uni-list-item__icon">
            <text :class="thumb" class="oa-icon" :style="iconStyleStr"></text>
          </view>
          <view v-else-if="showExtraIcon" class="uni-list-item__icon">
            <uni-icons
              :color="extraIcon.color"
              :size="extraIcon.size"
              :type="extraIcon.type"
              class="uni-icon-wrapper"
            />
          </view>
          <slot name="left"></slot>
        </view>
        <view class="uni-list-item__content">
          <view class="uni-list-item__content-title__container">
            <text class="uni-list-item__content-title" :style="titleStyle">{{
              title
            }}</text>
            <text v-if="number" class="uni-list-item__content-number">{{
              number
            }}</text>
          </view>
          <text v-if="note" class="uni-list-item__content-note">{{
            note
          }}</text>
          <slot />
        </view>
        <view class="uni-list-item__extra">
          <text
            v-if="rightText"
            class="uni-list-item__extra-text uni-list-item__extra-main-text"
            >{{ rightText }}</text
          >
          <text v-if="tipText" class="uni-list-item__extra-text">{{
            tipText
          }}</text>
          <uni-badge v-if="showBadge" :type="badgeType" :text="badgeText" />
          <switch
            class="uni-list-item__extra_switch"
            v-if="showSwitch"
            :disabled="disabled"
            :checked="switchChecked"
            @click.stop
            @change="onSwitchChange"
          />
          <slot name="right"></slot>
          <uni-icons
            v-if="showArrow"
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
    </view>
    <!-- #ifdef APP-NVUE -->
  </cell>
  <!-- #endif -->
</template>

<script>
import uniIcons from '../uni-icons/uni-icons.vue';
import uniBadge from '../uni-badge/uni-badge.vue';

/**
 * ListItem 列表子组件
 * @description 列表子组件
 * @tutorial https://ext.dcloud.net.cn/plugin?id=24
 * @property {String} title 标题
 * @property {String} note 描述
 * @property {String} thumb 左侧缩略图，若thumb有值，则不会显示扩展图标
 * @property {String} number 统计数字
 * @property {String} badgeText 数字角标内容
 * @property {String} badgeType 数字角标类型，参考[uni-icons](https://ext.dcloud.net.cn/plugin?id=21)
 * @property {String} rightText 右侧文字内容
 * @property {String} tipText 右侧提示文字内容
 * @property {Boolean} disabled = [true|false]是否禁用
 * @property {Boolean} showArrow = [true|false] 是否显示箭头图标
 * @property {Boolean} showBadge = [true|false] 是否显示数字角标
 * @property {Boolean} showSwitch = [true|false] 是否显示Switch
 * @property {Boolean} switchChecked = [true|false] Switch是否被选中
 * @property {Boolean} showExtraIcon = [true|false] 左侧是否显示扩展图标
 * @property {Boolean} scrollY = [true|false] 允许纵向滚动，需要显式的设置其宽高
 * @property {Object} extraIcon 扩展图标参数，格式为 {color: '#4cd964',size: '22',type: 'spinner'}
 * @event {Function} click 点击 uniListItem 触发事件
 * @event {Function} switchChange 点击切换 Switch 时触发
 */
export default {
  name: 'UniListItem',
  components: {
    uniIcons,
    uniBadge
  },
  props: {
    title: {
      type: String,
      default: ''
    }, // 列表标题
    titleStyle: {
      type: String,
      default: ''
    },
    note: {
      type: String,
      default: ''
    }, // 列表描述
    disabled: {
      // 是否禁用
      type: [Boolean, String],
      default: false
    },
    showArrow: {
      // 是否显示箭头
      type: [Boolean, String],
      default: true
    },
    showBadge: {
      // 是否显示数字角标
      type: [Boolean, String],
      default: false
    },
    showSwitch: {
      // 是否显示Switch
      type: [Boolean, String],
      default: false
    },
    switchChecked: {
      // Switch是否被选中
      type: [Boolean, String],
      default: false
    },
    badgeText: {
      // badge内容
      type: String,
      default: ''
    },
    badgeType: {
      // badge类型
      type: String,
      default: 'success'
    },
    rightText: {
      // 右侧文字内容
      type: String,
      default: ''
    },
    tipText: {
      // 右侧提示文字内容
      type: String,
      default: ''
    },
    thumb: {
      // 缩略图
      type: String,
      default: ''
    },
    showExtraIcon: {
      // 是否显示扩展图标
      type: [Boolean, String],
      default: false
    },
    extraIcon: {
      type: Object,
      default() {
        return {
          type: 'contact',
          color: '#333333',
          size: 44
        };
      }
    },
    number: {
      type: [Number, String],
      default: ''
    },
    iconStyleStr: {
      type: String,
      default: ''
    },
    listItemStyle: {
      type: String,
      default: ''
    }
  },
  inject: ['list'],
  data() {
    return {
      isFirstChild: false
    };
  },
  mounted() {
    if (!this.list.firstChildAppend) {
      this.list.firstChildAppend = true;
      this.isFirstChild = true;
    }
  },
  methods: {
    onClick() {
      this.$emit('click');
    },
    onSwitchChange(e) {
      this.$emit('switchChange', e.detail.value);
    }
  }
};
</script>

<style lang="scss" scoped>
$list-item-pd: $uni-spacing-col-base $uni-spacing-row-lg;

.uni-list-item {
  font-size: $uni-font-size-base;
  position: relative;
  flex-direction: column;
  justify-content: space-between;
  padding-left: $uni-spacing-row-lg;
}

.uni-list-item--disabled {
  opacity: 0.3;
}

.uni-list-item--hover {
  background-color: $uni-bg-color-hover;
}

.uni-list-item__container {
  position: relative;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  padding: $list-item-pd;
  padding-left: 0;
  flex: 1;
  position: relative;
  justify-content: space-between;
  align-items: center;
  /* #ifdef APP-PLUS */
  border-top-color: $uni-border-color;
  border-top-style: solid;
  border-top-width: 0.5px;
  /* #endif */
}

.uni-list-item--first {
  border-top-width: 0px;
}

/* #ifndef APP-NVUE */
.uni-list-item__container:after {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 1px;
  content: '';
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
  background-color: $uni-border-color;
}

.uni-list-item--first:after {
  height: 0px;
}
/* #endif */

.uni-list-item__content {
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex: 1;
  overflow: hidden;
  flex-direction: column;
  color: #333333;
}
.uni-list-item__content-title__container {
  line-height: 1.3;
}
.uni-list-item__content-title {
  font-size: $uni-font-size-base;
  color: #333333;
  overflow: hidden;
}

.uni-list-item__content-number {
  color: #666666;
  font-size: $uni-font-size-base;
  overflow: hidden;
  padding-left: 10rpx;
}

.uni-list-item__content-note {
  color: $uni-text-color-grey;
  font-size: $uni-font-size-sm;
  overflow: hidden;
}

.uni-list-item__extra {
  // width: 25%;
  /* #ifndef APP-NVUE */
  display: flex;
  /* #endif */
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.uni-list-item__icon {
  margin-right: 18rpx;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  line-height: 1;
}

.uni-list-item__icon-img {
  height: $uni-img-size-base;
  width: $uni-img-size-base;
}
.uni-list-item__extra_switch {
  margin-left: 4px;
}
.uni-list-item__extra-text {
  color: $uni-text-color-grey;
  font-size: $uni-font-size-sm;
}
.uni-list-item__extra-main-text {
  color: #666666;
  font-size: $uni-font-size-base;
}
.oa-icon-pdf {
  color: #a33639;
}
.oa-icon-gif,
.oa-icon-jpg,
.oa-icon-jpeg,
.oa-icon-png,
.oa-icon-img,
.oa-icon-video {
  color: #ea9518;
}
.oa-icon-html,
.oa-icon-txt {
  color: #0071c5;
}
.oa-icon-xls,
.oa-icon-xlsx {
  color: #207245;
}
.oa-icon-ppt,
.oa-icon-pptx {
  color: #d24625;
}
.oa-icon-doc,
.oa-icon-docx {
  color: #2a5699;
}
.oa-icon-rar,
.oa-icon-zip {
  color: #733781;
}
.oa-icon-wenjianjia {
  color: #ffd641;
}
.oa-icon-unfile {
  color: #999;
}
</style>
