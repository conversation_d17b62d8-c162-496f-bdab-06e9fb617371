import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc 获取文件**/
  getFileAttachmentByBusinessId(datas) {
    return request.post(
      `${apiConfig.basics()}/fileAttachment/getFileAttachmentByBusinessId`,
      datas
    );
  },
  /**@desc 删除文件**/
  deletFileById(datas) {
    return request.post(
      `${apiConfig.basics()}/fileAttachment/deleteFileId`,
      datas
    );
  },
  /**@desc 删除文件**/
  deleteFileId(params) {
    return request.get(`${apiConfig.basics()}/fileAttachment/deleteFileId`, {
      params
    });
  },
  /**@desc 上传文件**/
  imageBase64Upload(datas) {
    return request.post(
      `${apiConfig.basics()}/fileAttachment/attachment/imageBase64Upload?moduleName=oa`,
      datas
    );
  },
  /**@desc 获取公文列表**/
  getMySendfileList(datas) {
    return request.post(
      `${apiConfig.document()}/govSendfile/getMySendfileList`,
      datas
    );
  },
  /**@desc 公文操作**/
  confirmMySendFile(datas) {
    return request.post(
      `${apiConfig.document()}/govSendfile/confirmMySendFile`,
      datas
    );
  },
  /**@desc 获取个人文档**/
  getWXMyAttachment(datas) {
    return request.post(
      `${apiConfig.document()}/attachment/getWXMyAttachment`,
      datas
    );
  },
  /**@desc 获取个人文档 分享给我的**/
  getShareAttachment(datas) {
    return request.post(
      `${apiConfig.document()}/attachment/getShareAttachment`,
      datas
    );
  },
  /**@desc 获取个人文档 我的分享**/
  getMyShareAttachment(datas) {
    return request.post(
      `${apiConfig.document()}/attachment/getMyShareAttachment`,
      datas
    );
  },
  /**@desc 重命名**/
  editFileName(datas) {
    return request.post(`${apiConfig.document()}/attachment/update`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  /**@desc 删除文件**/
  deleteFile(datas) {
    return request.post(
      `${apiConfig.document()}/attachment/operationAttachment`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取文件统计数据**/
  getDocumentNumber(datas) {
    return request.get(`${apiConfig.oa()}/document/getDocumentNumber`, {
      params: datas
    });
  },
  /**@desc 获取科室文档**/
  getFolderList(datas) {
    return request.post(
      `${apiConfig.document()}/document/getWXDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 新的接口
  /**@desc 获取科室文档 文档库**/
  getListColumn(datas) {
    return request.post(`${apiConfig.oa()}/document/listColumn`, datas);
  },

  // 分享
  getShareListColumn(datas) {
    return request.post(
      `${apiConfig.oa()}/document/listColumn?shareData=Y`,
      datas
    );
  },

  // 分享给我的、我分享的文档列表
  getShareDocumentList(params) {
    return request.get(`${apiConfig.oa()}/api/documentShare/pageList`, {
      params
    });
  },

  // 归档
  getListFiled(datas) {
    return request.post(`${apiConfig.oa()}/document/listFiled`, datas);
  },

  // 回收站
  getListRecovery(datas) {
    return request.post(`${apiConfig.oa()}/document/listRecovery`, datas);
  },

  /**@desc 科室文档视图**/
  getDocumentChannelView(datas) {
    return request.post(
      `${apiConfig.oa()}/documentChannel/getDocumentView`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 科室文档 文档操作**/
  documentBatchOperate(datas) {
    return request.post(`${apiConfig.oa()}/document/bacthOperate`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 科室文档 分享**/
  DocumentbacthShareDocument(datas) {
    return request.post(
      `${apiConfig.oa()}/document/bacthShareDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  /**@desc 科室文档 文档新建**/
  documentSave(datas) {
    return request.post(`${apiConfig.oa()}/document/save`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 科室文档 文档更新**/
  documentUpdate(datas) {
    return request.post(`${apiConfig.oa()}/document/update`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 科室文档 科室列表**/
  documentChannelList(datas) {
    return request.post(`${apiConfig.oa()}/documentChannel/list`, datas);
  },

  /**@desc 获取文件统计数据**/
  selectDocumentById(datas) {
    return request.get(`${apiConfig.oa()}/document/selectDocumentById`, {
      params: datas
    });
  },

  /**@desc 获取文件统计数据**/
  selectDocumentAccessoryByBocId(datas) {
    return request.get(
      `${apiConfig.oa()}/document/selectDocumentAccessoryByBocId`,
      {
        params: datas
      }
    );
  },

  // 科室文档分类删除
  documentChannelDelete(datas) {
    return request.post(
      `${apiConfig.oa()}/documentChannel/deletedById/${datas.id}`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 科室文档分类删除
  documentChannelUpdate(datas) {
    return request.post(`${apiConfig.oa()}/documentChannel/update`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 科室文档分类分享
  shareChannelDocument(datas) {
    return request.post(
      `${apiConfig.oa()}/documentChannel/shareChannelDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 科室文档撤回我分享的
  cancelShareDocument(datas) {
    return request.post(`${apiConfig.oa()}/document/bacthCancle`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 科室文档删除分享给我的
  deleteDocumentShare(datas) {
    return request.post(`${apiConfig.oa()}/document/bacthDelete`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 个人文档-新
  // 个人文档 文档库
  getMyAttachment(datas) {
    return request.post(`${apiConfig.oa()}/attachment/getMyAttachment`, datas);
  },

  // 个人文档 分享库
  getShareAttachment(datas) {
    return request.post(
      `${apiConfig.oa()}/attachment/getShareAttachment`,
      datas
    );
  },

  // 个人文档 回收站
  getRecoveryAttachment(datas) {
    return request.post(
      `${apiConfig.oa()}/attachment/getRecoveryAttachment`,
      datas
    );
  },

  /**@desc 个人文档获取文件类型**/
  getFileExtension() {
    return request.get(
      `${apiConfig.oa()}/attachment/getFileExtension?moduleName=personal`
    );
  },

  // 个人文档视图
  getMyDocumentView(datas) {
    return request.post(
      `${apiConfig.oa()}/documentChannel/getMyDocumentView`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 个人文档 操作
  operationAttachment(datas) {
    return request.post(
      `${apiConfig.oa()}/attachment/operationAttachment`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 个人文档 分享
  bacthShareDocument(datas) {
    return request.post(
      `${apiConfig.oa()}/attachment/bacthShareDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 个人文档 上传
  attachmentBacthUpdate(datas) {
    return request.post(`${apiConfig.oa()}/attachment/bacthUpdate`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 重命名**/
  attachmentRename(datas) {
    return request.post(`${apiConfig.oa()}/attachment/update`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  // 分类详情
  documentChannelSelectById(datas) {
    return request.post(
      `${apiConfig.oa()}/documentChannel/selectById?id=${datas.id}`,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 个人文档批量移动
  bacthMoveDocument(datas) {
    return request.post(
      `${apiConfig.oa()}/attachment/bacthMoveDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  // 个人文档分类分享
  documentShareChannelDocument(datas) {
    return request.post(
      `${apiConfig.oa()}/document/shareChannelDocument`,
      datas,
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },

  // 个人分类分享
  attachmentShareChannel(datas) {
    return request.post(`${apiConfig.oa()}/attachment/shareChannel`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },

  /**@desc 是否为文档管理员**/
  documentIsDocAdmin(datas) {
    return request.get(`${apiConfig.oa()}/document/isDocAdmin`);
  },

  getMyDocumentNumbers(datas) {
    return request.get(`${apiConfig.oa()}/document/getMyDocumentNumbers`, {
      params: datas
    });
  },
  // 个人文档-我的分享撤回
  cancleMyDocumentShare(datas) {
    return request.post(`${apiConfig.oa()}/attachment/bacthCancle`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  },
  // 个人文档-分享给我的删除
  deleteShareToMe(datas) {
    return request.post(`${apiConfig.oa()}/attachment/bacthDelete`, datas, {
      header: {
        'Content-Type': 'application/json;charset=utf-8'
      }
    });
  }
};
