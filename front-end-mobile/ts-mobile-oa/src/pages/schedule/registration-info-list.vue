<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="预约挂号信息"></page-head>
    <view class="content-top">
      <view class="prompt-text">
        <text>挂号日期：{{ scheduleDate }}</text>
        <text>挂号医生：{{ doctorName }}</text>
      </view>
    </view>
    <view class="mescroll-content">
      <mescroll
        ref="mescroll"
        :isHasPage="false"
        :page="{ num: 0, size: 10000 }"
        @getDatas="getListData"
        @setDatas="setListData"
        @datasInit="datasInit"
      >
        <view class="contact_list">
          <view
            class="contact_item"
            v-for="(item, index) in dataList"
            :key="item.id"
            @tap="chooseItem(item.id, index)"
          >
            <view class="info-row">
              <view class="info-queue-no-text">
                排队号：
                <text class="queue-no">{{ item.queueNo }}</text>
              </view>
              <view class="info-name-text">
                <img
                  v-if="item.sexName == '女'"
                  class="info-sex-img"
                  :src="require(`@/static/img/woman.png`)"
                  alt=""
                />
                <img
                  v-else
                  class="info-sex-img"
                  :src="require(`@/static/img/man.png`)"
                  alt=""
                />
                <text class="info-text">{{ item.visitNo }}</text>
                <text class="info-text">{{ item.patientName }}</text>
                <text class="info-text">{{ item.age }}</text>
              </view>
            </view>
            <view class="info-row">
              <text>挂号来源：{{ item.regSource }}</text>
              <text>联系电话：{{ item.mobile }}</text>
            </view>
          </view>
        </view>
      </mescroll>
    </view>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      fromPage: '',
      scheduleDate: '',
      regSourceList: {
        0: '未知',
        1: '窗口',
        2: '自助设备',
        3: '移动终端',
        4: '医生站',
        12: '互联网医院',
        88: '体检',
        99: '转诊'
      },
      dataList: []
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage ? opt.fromPage : 'workBench';
    this.scheduleDate = opt.scheduleDate;
  },
  computed: {
    doctorName() {
      return this.$store.state.username;
    }
  },
  methods: {
    //获取数据
    getListData(page, successCallback, errorCallback) {
      this.ajax
        .getDoctorShedule({ scheduleDate: this.scheduleDate })
        .then(res => {
          let rows = res.object;
          successCallback(rows);
        })
        .catch(e => {
          errorCallback();
        });
    },
    setListData(rows) {
      rows.map(item => {
        item.regSourceName = this.regSourceList[item.regSource];
      });
      this.dataList = this.dataList.concat(rows);
    },
    datasInit() {
      this.dataList = [];
    },
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .content-top {
    background: #fff;

    margin-bottom: 20rpx;
    padding: 20rpx;
    .prompt-text {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 10rpx;
      line-height: 1;
      border-left: 4px solid $theme-color;
      font-size: 28rpx;
    }
  }
  .mescroll-content {
    flex: 1;
    position: relative;
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .info-sex-img {
      width: 60rpx;
    }
    .info-queue-no-text {
      width: 25%;
      font-size: 28rpx;
      .queue-no {
        display: inline-block;
        font-weight: bold;
      }
    }

    .info-name-text {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 28rpx;

      .info-text {
        line-height: 1;
        padding: 0 20rpx;
        border-right: 1px solid #ccc;
        &:last-child {
          border-right: none;
        }
      }
    }
    .info-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 28rpx;
    }
  }
}
</style>
