<template>
  <view class="ts-content">
    <page-head
      title="邮件详情"
      @clickLeft="returnBack"
      right-text="写邮件"
      @clickRight="sendEmail"
    ></page-head>
    <view class="email_head_info" v-if="isShow">
      <view class="subject">{{ subject }}</view>
      <view>发件人：{{ senderName }}</view>
      <view>收件人：{{ toName }}</view>
      <view v-show="ccName">抄送人：{{ ccName }}</view>
      <view>时间：{{ postTime }}</view>
    </view>
    <view class="content" @click="isForward && jumpToInfo()">
      <!-- <rich-text
        class="rich-text"
        :nodes="content"
        :preview="false"
        @click="richTextclick"
      ></rich-text> -->
      <!-- <u-parse
        :content="content"
        :imageProp="imageProp"
        @preview="preview"
        @navigate="navigate"
      ></u-parse> -->
      <mp-html :content="content" />
    </view>
    <view class="opt-row">
      <view v-if="isShow">
        <view class="mask" @tap="isShowFileList" v-if="isShowFile"></view>
        <view class="attachment">
          <view class="file-icon" @tap="isShowFileList">
            <text class="oa-icon oa-icon-fujian file_icon"></text>
            {{ fileCount }}个附件
          </view>
          <transition name="slide-fade">
            <view class="attachment_list" v-if="isShowFile">
              <view
                class="attachment_item"
                v-for="(item, index) in attachmentList"
                :key="index"
                @tap="previewFile(item.id, item.fileName)"
              >
                <text
                  class="oa-icon"
                  :class="'oa-icon-' + $oaModule.formatFileType(item.extension)"
                ></text>
                <view class="attachment_item_info">
                  <text class="original_name">{{ item.originalName }}</text>
                  <text class="file_size">
                    {{ item.fileSize | fileSizeFilter }}
                  </text>
                </view>
                <view
                  class="oa-icon oa-icon-xiazai down_load"
                  @tap.stop="downloadFile(item.id, item.fileName)"
                >
                </view>
              </view>
            </view>
          </transition>
        </view>
      </view>
      <view class="eamil_edit_foot">
        <view v-if="deleteBtn" class="foot_item deleteBtn" @tap="deleteEmail">
          <text class="oa-icon oa-icon-shanchu foot_item_icon"></text>
          <text class="foot_item_text">删除</text>
        </view>
        <view v-if="forwardBtn" class="foot_item noReadBtn" @tap="forwardEmail">
          <text class="oa-icon oa-icon-zhuanfa foot_item_icon"></text>
          <text class="foot_item_text">转发</text>
        </view>
        <view v-if="replyBtn" class="foot_item readBtn" @tap="replyEmail">
          <text class="oa-icon oa-icon-huifu foot_item_icon"></text>
          <text class="foot_item_text">回复</text>
        </view>
        <view v-if="dropBtn" class="foot_item dropBtn" @tap="dropEmail">
          <text class="oa-icon oa-icon-shanchu foot_item_icon"></text>
          <text class="foot_item_text">彻底删除</text>
        </view>
        <view
          v-if="recoveryBtn"
          class="foot_item recoveryBtn"
          @tap="recoveryEmail"
        >
          <text class="oa-icon oa-icon-huifu foot_item_icon"></text>
          <text class="foot_item_text">恢复邮件</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
// import loginModule from '@/common/js/loginModule.js';
// import uParse from '@/components/u-parse/u-parse';
import mpHtml from '@/components/mp-html/components/mp-html/mp-html.vue';
export default {
  components: {
    // uParse,
    mpHtml
  },
  data() {
    return {
      imageProp: {
        domain: this.$config.BASE_HOST,
        padding: 0
      },
      fromPage: '',
      datas: null,
      attachmentList: [],
      fileCount: 0,
      isShow: false,
      isShowFile: false,
      statusId: null,
      folderId: null,
      deleteBtn: true,
      forwardBtn: true,
      replyBtn: true,
      dropBtn: true,
      recoveryBtn: true,
      subject: null,
      senderName: null,
      toName: null,
      postTime: null,
      content: '',
      ccName: null,
      forwardType: null
    };
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo;
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    this.folderId = opt.folderId;
    this.fromPage = opt.fromPage;
    switch (this.folderId) {
      case '1':
        this.dropBtn = false;
        this.recoveryBtn = false;
        break;
      case '2':
        this.replyBtn = false;
        this.dropBtn = false;
        this.recoveryBtn = false;
        break;
      case '3':
        this.deleteBtn = false;
        this.forwardBtn = false;
        this.replyBtn = false;
        this.dropBtn = false;
        this.recoveryBtn = false;
        break;
      case '4':
        this.deleteBtn = false;
        this.forwardBtn = false;
        this.replyBtn = false;
        break;
    }
    await this.getEmailDatas(opt.statusId);
  },
  methods: {
    ...mapMutations(['changeState']),
    //获取流程信息数据
    async getEmailDatas(statusId) {
      await this.ajax
        .getEmailDetails({
          statusId: statusId
        })
        .then(res => {
          let data = res.object;
          this.datas = data;
          this.subject = data.subject;
          this.senderName = data.senderName;
          this.toName = data.toName || data.outEmailAddress;
          this.ccName = data.ccName;
          this.postTime = data.postTime;
          this.content = data.content;
          // this.content = data.content.replace(
          //   /\<img/gi,
          //   '<img style="width: 100%"'
          // );
          this.forwardType = data.forwardType;
          this.isForward =
            this.content.indexOf('informationDetail') == -1 ? false : true;
          if (null != data.attachmentList && data.attachmentList.length > 0) {
            this.isShow = true;
            this.attachmentList = data.attachmentList;
            this.fileCount = this.attachmentList.length;
          }
          this.statusId = data.statusId;
        })
        .catch(() => {});
    },
    isShowFileList() {
      this.isShowFile = !this.isShowFile;
    },
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    downloadFile(id, fileName) {
      let filePath = `${this.$config.BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      this.$downloadFile.downloadFile(filePath);
    },
    deleteEmail() {
      this.operateMail('您确定要删除邮件？', 'delete');
    },
    dropEmail() {
      this.operateMail('您确定要删除邮件？', 'clean');
    },
    recoveryEmail() {
      this.operateMail('您确定要恢复邮件？', 'recovery');
    },
    replyEmail() {
      uni.setStorageSync('email_detail', this.datas);
      this.$nextTick(() => {
        uni.navigateTo({
          url: `/pages/email/email-reply?from=reply&folderId=${this.folderId}&fromPage=${this.fromPage}`
        });
      });
    },
    forwardEmail() {
      uni.setStorageSync('email_detail', this.datas);
      this.$nextTick(() => {
        uni.navigateTo({
          url: `/pages/email/email-reply?from=forward&folderId=${this.folderId}&fromPage=${this.fromPage}`
        });
      });
    },
    operateMail(content, method) {
      uni.showModal({
        title: '提示',
        content: content,
        confirmText: '取消',
        cancelText: '确定',
        confirmColor: '#005BAC',
        success: res => {
          if (res.cancel) {
            this.ajax
              .confirmEmail({
                ids: this.statusId,
                method: method
              })
              .then(res => {
                if (res.success) {
                  uni.showToast({
                    title: res.object,
                    duration: 2000
                  });
                  uni.navigateTo({
                    url: `/pages/email/email-box-list?folderId=${this.folderId}&fromPage=${this.fromPage}`
                  });
                }
              });
          }
        }
      });
    },
    richTextclick(e) {
      console.loge;
    },
    sendEmail() {
      uni.navigateTo({
        url: `/pages/email/email-send?index=${this.folderId}&fromPage=${this.fromPage}`
      });
    },
    jumpToInfo() {
      uni.navigateTo({
        url: `/pages/information/information-details?informationId=${
          this.forwardType.split('-')[1]
        }&fromPage=email`
      });
    },
    returnBack() {
      if (this.fromPage === 'index') {
        uni.redirectTo({
          url: `/pages/index/email-unread-list?fromPage=${this.fromPage}`
        });
      } else {
        uni.navigateTo({
          url: `/pages/email/email-box-list?folderId=${this.folderId}&fromPage=${this.fromPage}`
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background: #fff;
  overflow: hidden;
  zoom: 100%;
  .content {
    margin-bottom: 170rpx;
    padding: 20rpx 20rpx;
    ::v-deep table {
      border: 1px solid #000;
      tr {
        border: 1px solid #000;
        td {
          border: 1px solid #000;
        }
      }
    }
    .content-img {
      width: 100% !important;
    }
  }
  .email_head_info {
    color: #999999;
    margin-bottom: 10rpx;
    font-size: 24rpx;
    padding: 20rpx 30rpx;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      left: 30rpx;
      right: 30rpx;
      bottom: 0;
      height: 1px;
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    view {
      font-size: 28rpx;
    }
    .subject {
      font-size: 32rpx;
      font-weight: bold;
      color: #333333;
    }
  }
  // .slide-fade-enter-active {//动画渐入
  //   transition: all .3s linear;
  // }
  // .slide-fade-leave-active {//动画渐出
  //   transition: all .3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  // }
  // .slide-fade-enter, .slide-fade-leave-to
  // /* .slide-fade-leave-active for below version 2.1.8 */ {//动画初始化
  //   transform: translateY(10px);
  //   opacity: 0;
  // }
  .opt-row {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 -1px 6px #ddd;
    .mask {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: #333333;
      opacity: 0.2;
      z-index: 9;
    }
    .attachment {
      z-index: 10;
      background-color: #ffffff;
      .file-icon {
        color: #10b47f;
        font-size: 28rpx;
        text-align: center;
        height: 80rpx;
        line-height: 80rpx;
        z-index: 10;
        background-color: #ffffff;
        position: relative;
        .file_icon {
          margin-right: 3px;
          font-size: 36rpx;
        }
      }
      .attachment_list {
        background-color: #ffffff;
        z-index: 10;
        position: relative;
        padding: 10rpx 20rpx 20rpx;
        box-sizing: border-box;
        max-height: 700rpx;
        overflow: auto;
        .attachment_item {
          text-decoration: none;
          display: block;
          font-size: 28rpx;
          color: #333333;
          padding: 6rpx 20rpx;
          border: 1px solid #dddddd;
          border-radius: 5px;
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          .oa-icon {
            font-size: 40rpx;
            margin-right: 20rpx;
            color: $theme-color;
          }
          .attachment_item_info {
            flex: 1;
            .original_name {
              font-size: 28rpx;
              color: #333333;
              margin-right: 20rpx;
            }
            .file_size {
              color: #999999;
              font-size: 24rpx;
            }
          }
          .down_load {
            flex-shrink: 0;
            margin-right: 0;
            margin-left: 8px;
          }
        }
      }
    }
    .eamil_edit_foot {
      z-index: 10;
      position: relative;
      background-color: #ffffff;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      &::before {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      .deleteBtn {
        color: #333333;
      }
      .noReadBtn {
        color: #f59a23;
      }
      .readBtn {
        color: #005bac;
      }
      .dropBtn {
        color: #333333;
      }
      .recoveryBtn {
        color: #005bac;
      }
      .foot_item {
        flex: 1;
        line-height: 90rpx;
        height: 90rpx;
        text-align: center;
        .foot_item_icon {
          margin-right: 3px;
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>
