<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="会议室申请"></page-head>
    <view class="meeting_content">
      <form @submit="formSubmit" style="height: 100%;overflow: auto;">
        <view class="basic_user_info">
          <view class="row_group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">申请人</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.applyEmpname
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">申请科室</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  applicationInfo.applyOrgname
                }}</text>
              </view>
            </view>
            <!-- <view class="row dis_flex">
							<view class="row_lable">
								<text class="required_red oa-icon oa-icon-asterisks"></text>
								<text class="row_lable_text">申请时间</text>
							</view>
							<view class="row_value">
								<text class="row_value_text">{{applicationInfo.destineDate}}</text>
							</view>
						</view> -->
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">联系人</text>
              </view>
              <view class="row_value row_value_input">
                <input
                  class="row_value_input_text"
                  type="text"
                  placeholder="请输入联系人"
                  name="linktelePerson"
                  v-model="applicationInfo.linktelePerson"
                />
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">联系方式</text>
              </view>
              <view class="row_value row_value_input">
                <input
                  class="row_value_input_text"
                  type="number"
                  placeholder="请输入联系方式"
                  name="linktelePhone"
                  v-model="applicationInfo.linktelePhone"
                />
              </view>
            </view>
          </view>
          <view class="row_group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">会议类型</text>
              </view>
              <view
                class="row_value row_value_input"
                data-popup-type="bottom"
                data-popup-name="popupdetail"
                data-popup-datas-name="appType"
                @tap="showPopup"
              >
                <input
                  class="row_value_input_text"
                  disabled="true"
                  placeholder="请选择会议类型"
                  name="apptypeName"
                  v-model="applicationInfo.apptypeName"
                />
                <uni-icons
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="arrowright"
                />
              </view>
            </view>
            <view class="row">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">会议主题</text>
              </view>
              <view class="row_value row_value_textarea">
                <textarea
                  class="row_value_textarea_text"
                  placeholder="请输入会议主题"
                  auto-height
                  name="motif"
                  v-model="applicationInfo.motif"
                />
              </view>
            </view>
            <view class="row">
              <view class="row_lable" style="width: 100%;">
                <text class="row_lable_text">参与人</text>
                <text
                  class="add_icon oa-icon oa-icon-tianjiachaosong"
                  @click="chooseParticipant"
                ></text>
              </view>
              <view class="row_value row_value_personal">
                <text class="personNameStr">{{ attendeeNameStr }}</text>
                <uni-icons
                  v-if="attendeeNameStr"
                  :size="40"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="closeempty"
                  @tap="emptyAttendee"
                />
              </view>
            </view>
            <view class="row">
              <view class="row_lable" style="width: 100%;">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">主持人</text>
                <text
                  class="add_icon oa-icon oa-icon-tianjiachaosong"
                  @click="chooseModerator"
                ></text>
                <input
                  v-show="false"
                  class="row_value_input_text"
                  placeholder="请选择会议主持人"
                  name="moderator"
                  v-model="emceeNameStr"
                />
              </view>
              <view class="row_value row_value_personal">
                <text class="personNameStr">{{ emceeNameStr }}</text>
                <uni-icons
                  v-if="emceeNameStr"
                  :size="30"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="closeempty"
                  @tap="emptEmcee"
                />
              </view>
            </view>
            <view class="row">
              <view class="row_lable">
                <text class="required_red oa-icon oa-icon-asterisks"></text>
                <text class="row_lable_text">会议内容</text>
              </view>
              <view class="row_value row_value_textarea">
                <textarea
                  class="row_value_textarea_text"
                  placeholder="请输入会议内容"
                  auto-height
                  name="content"
                  v-model="applicationInfo.content"
                />
              </view>
            </view>
          </view>
          <view class="meeting_room_list">
            <view class="meeting_room_situation" @click="checkMeetingUsage">
              <uni-icons
                :size="40"
                class="uni-icon-wrapper"
                color="#005BAC"
                type="search"
              />
              <text>会议室情况</text>
            </view>
            <view
              class="row_group"
              v-for="(item, index) in meetingList"
              :key="item.indexId"
            >
              <view class="meeting_room_item_title">
                <text>会议明细({{ index + 1 }})</text>
                <text
                  class="delet_meeting_room_btn"
                  v-if="index != 0"
                  @tap="deletMeetingRoom(index)"
                  >删除</text
                >
              </view>
              <view class="row dis_flex">
                <view class="row_lable">
                  <text class="required_red oa-icon oa-icon-asterisks"></text>
                  <text class="row_lable_text">开始时间</text>
                </view>
                <view
                  class="row_value row_value_input"
                  data-date-ref="date"
                  data-date-type="startTime"
                  :data-date-index="index"
                  :data-date-value="item.startTime"
                  @tap="showPicker"
                >
                  <input
                    class="row_value_input_text"
                    disabled="true"
                    placeholder="请选择会议开始时间"
                    :value="item.startTime"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
              </view>
              <view class="row dis_flex">
                <view class="row_lable">
                  <text class="required_red oa-icon oa-icon-asterisks"></text>
                  <text class="row_lable_text">结束时间</text>
                </view>
                <view
                  class="row_value row_value_input"
                  data-date-ref="date"
                  data-date-type="endTime"
                  :data-date-index="index"
                  :data-date-value="item.endTime"
                  @tap="showPicker"
                >
                  <input
                    class="row_value_input_text"
                    disabled="true"
                    placeholder="请选择会议结束时间"
                    :value="item.endTime"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
              </view>
              <view class="row dis_flex">
                <view class="row_lable">
                  <text class="required_red oa-icon oa-icon-asterisks"></text>
                  <text class="row_lable_text">会议地点</text>
                </view>
                <view
                  class="row_value row_value_input"
                  data-popup-type="bottom"
                  data-popup-name="popupdetail"
                  data-popup-datas-name="appAddress"
                  :data-popup-datas-index="index"
                  @tap="showPopup"
                >
                  <input
                    class="row_value_input_text"
                    disabled="true"
                    placeholder="请选择会议地点"
                    :value="item.meetingRoom"
                  />
                  <uni-icons
                    :size="30"
                    class="uni-icon-wrapper"
                    color="#bbb"
                    type="arrowright"
                  />
                </view>
              </view>
            </view>
            <view class="tip">
              如需添加多个会议室，请点击下方“增加会议室”按钮
            </view>
            <view class="meeting_room_add_btn dis_flex" @tap="addMeeting">
              <uni-icons
                :size="40"
                class="uni-icon-wrapper"
                color="#005BAC"
                type="plusempty"
              />
              <text>增加会议室</text>
            </view>
          </view>
          <view class="meeting_room_file">
            <view class="file_title">
              <text>附件</text>
              <view class="oa-icon oa-icon-tupiantianjia" @tap="addFile"></view>
            </view>
            <view class="file_list">
              <view class="file_item" v-for="t in fileList" :key="t.fileId">
                <view
                  class="file_item_info"
                  @click="downloadFile(t.fileId, t.fileRealName)"
                >
                  <view
                    class="oa-icon"
                    :class="'oa-icon-' + $oaModule.formatFileType(t.fileType)"
                  ></view>
                  <view class="file_item_name">
                    <text class="file_name">{{ t.fileName }}</text>
                    <text class="file_size">{{
                      t.fileSize | fileSizeFilter
                    }}</text>
                  </view>
                </view>
                <text
                  class="oa-icon oa-icon-guanbi delete_file"
                  @click.stop="deletFile(t.fileId)"
                ></text>
              </view>
            </view>
          </view>
        </view>
        <view class="bottom_btn">
          <button class="btn_item uni-bg-blue" form-type="submit">提交</button>
        </view>
      </form>
    </view>
    <uni-popup :type="popupType" ref="showpopupdetail">
      <view class="contact-list scroll_list">
        <view v-if="popupDatasName == 'appType'">
          <view
            class="contact-item"
            v-for="item in appTypeList"
            :key="item.id"
            :data-column-id="item.id"
            :data-column-name="item.type"
            data-popup-datas-name="appType"
            data-popup-name="popupdetail"
            @tap="selectColumn"
          >
            <text class="contact-item-text">{{ item.type }}</text>
            <view class="contact-item-icon">
              <uni-icons
                v-if="applicationInfo.apptypeid == item.id"
                type="checkmarkempty"
                color="#005BAC"
                size="44"
              />
            </view>
          </view>
        </view>
        <view v-if="popupDatasName == 'appAddress'">
          <view
            class="contact-item"
            v-for="item in appAddressList"
            :key="item.id"
            :data-column-id="item.id"
            :data-column-name="item.location + '-' + item.name"
            :data-column-processid="item.defaultProcessid"
            data-popup-datas-name="appAddress"
            data-popup-name="popupdetail"
            @tap="selectColumn"
          >
            <view class="contact-item-info">
              <view class="contact-item-text">{{ item.name }}</view>
              <view class="equipment">
                <text class="equipment_item" v-if="item.projector">投影</text>
                <text class="equipment_item" v-if="item.computer">电脑</text>
                <text class="equipment_item" v-if="item.whiteboard">白板</text>
                <text class="equipment_item" v-if="item.voicetube">话筒</text>
                <text class="equipment_item" v-if="item.airconditioner"
                  >空调</text
                >
                <text class="equipment_item" v-if="item.heating">暖气</text>
              </view>
              <view class="describe">
                <view class="describe_item">
                  <uni-icons type="person" color="#005BAC" size="32" />
                  <text>{{ item.capacitance }}</text>
                </view>
                <view class="describe_item">
                  <uni-icons type="location" color="#005BAC" size="32" />
                  <text>{{ item.location }}</text>
                </view>
              </view>
            </view>
            <uni-icons
              v-if="
                meetingList[selectAppAddressIndex]['meetingRoomId'] == item.id
              "
              type="checkmarkempty"
              color="#005BAC"
              size="48"
            />
          </view>
        </view>
      </view>
    </uni-popup>
    <date-picker
      mode="date"
      endDate="2100-12-31 23:59"
      :value="selectTimeValue"
      @confirm="onConfirm"
      ref="date"
      fields="minute"
    ></date-picker>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import uniPopup from '@/components/uni-popup/uni-popup.vue';
import datePicker from '@/components/picker/date-picker.vue';
import { chooseImage, chooseFile } from '@/common/js/uploadImg.js';
import graceChecker from '@/common/js/graceChecker.js';
import Base64 from '@/common/js/base64.min.js';
export default {
  components: {
    uniPopup,
    datePicker
  },
  data() {
    return {
      fromPage: '',
      applicationInfo: {
        applyEmpname: '', //预定人姓名
        applyEmp: '', //预定人ID
        applyOrgname: '', //预定部门名称
        applyOrg: '', //预定部门ID
        //destineDate: '',//预定时间
        linktelePerson: '', //联系人
        linktelePhone: '', //联系电话
        apptypeName: '', //会议类型名称
        apptypeid: '', //会议类型ID
        motif: '', //会议主题
        content: '', //会议内容
        attendeeName: '', //参会人name
        attendeeId: '', //参会人id
        emcee: '', //主持人id
        emceeName: '', //主持人name
        meetingTerm: '2', //会议期效
        startTimeStr: '', //会议开始时间，'#'连接
        endTimeStr: '', //会议结束时间，'#'连接
        boardroomIdStr: '', //会议室ID，'#'连接
        roomNames: '', //会议室地点（会议室地点+会议室名称），'#'连接
        defaultProcessidStr: '', //会议室流程ID，'#'连接
        accessoryName: '', //附件name
        accessoryId: '' //附件id
      },
      meetingList: [
        {
          startTime: '',
          endTime: '',
          meetingRoom: '',
          meetingRoomId: '',
          defaultProcessid: '',
          indexId: 'meetingindex-0'
        }
      ],
      attendeeNameStr: '',
      emceeNameStr: '',
      personlist: [],
      personCclist: [],
      nextTodoId: 0,
      fileList: [],
      fileNameList: [],
      filePathList: [],
      fileIdList: [],
      appTypeList: [],
      appAddressList: [],
      popupType: '',
      popupDatasName: '',
      selectAppAddressIndex: 0,
      selectTimeIndex: 0,
      selectTimeType: '',
      type: '',
      selectTimeValue: '',
      applicationInfos: ''
    };
  },
  computed: {
    ...mapState(['username', 'usercode', 'deptname', 'deptid'])
  },
  onLoad(opt) {
    let _self = this;
    _self.fromPage = opt.fromPage ? opt.fromPage : '';
    _self.getAppType();
    _self.getAppAddress();
  },
  created() {
    let _self = this;
    _self.applicationInfo.applyEmpname = _self.username;
    _self.applicationInfo.applyEmp = _self.usercode;
    _self.applicationInfo.applyOrgname = _self.deptname;
    _self.applicationInfo.applyOrg = _self.deptid;
  },
  methods: {
    //获取会议类型
    getAppType() {
      let _self = this;
      _self.ajax.getMeetingType().then(res => {
        _self.appTypeList = res.object;
      });
    },
    //获取会议室（地点）
    getAppAddress() {
      let _self = this;
      _self.ajax
        .getMeetingAddress({
          boardroom_status: 4
        })
        .then(res => {
          _self.appAddressList = res.rows;
        });
    },
    //选择参会人
    chooseParticipant() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personlist = data;
        let participantArr = [];
        _self.applicationInfo.attendeeId = '';
        _self.applicationInfo.attendeeName = '';
        data.forEach((item, index) => {
          _self.applicationInfo.attendeeId += `${item.id},`;
          _self.applicationInfo.attendeeName += `${item.name},`;
          if (index < 4) {
            participantArr.push(item.name);
          }
        });
        _self.attendeeNameStr = participantArr.join('、');
        if (data.length > 3) {
          _self.attendeeNameStr += `等${data.length}人`;
        }
        _self.applicationInfo.attendeeId = _self.applicationInfo.attendeeId.substring(
          0,
          _self.applicationInfo.attendeeId.length - 1
        );
        _self.applicationInfo.attendeeName = _self.applicationInfo.attendeeName.substring(
          0,
          _self.applicationInfo.attendeeName.length - 1
        );
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personlist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空参会人
    emptyAttendee() {
      let _self = this;
      _self.personlist = [];
      _self.applicationInfo.attendeeId = '';
      _self.applicationInfo.attendeeName = '';
      _self.attendeeNameStr = '';
    },
    //选择主持人
    chooseModerator() {
      let _self = this;
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', data => {
        _self.personCclist = data;
        let moderatorArr = [];
        _self.applicationInfo.emcee = '';
        _self.applicationInfo.emceeName = '';
        data.forEach((item, index) => {
          _self.applicationInfo.emcee += `${item.id},`;
          _self.applicationInfo.emceeName += `${item.name},`;
          if (index < 3) {
            moderatorArr.push(item.name);
          }
        });
        _self.emceeNameStr = moderatorArr.join('、');
        if (data.length > 3) {
          _self.emceeNameStr += `等${data.length}人`;
        }
        _self.applicationInfo.emcee = _self.applicationInfo.emcee.substring(
          0,
          _self.applicationInfo.emcee.length - 1
        );
        _self.applicationInfo.emceeName = _self.applicationInfo.emceeName.substring(
          0,
          _self.applicationInfo.emceeName.length - 1
        );
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      uni.setStorageSync('person_list', JSON.stringify(_self.personCclist));
      uni.navigateTo({
        url: '/pages/selectPerson/select-person?checkType=checkBox'
      });
    },
    //清空主持人
    emptEmcee() {
      let _self = this;
      _self.personCclist = [];
      _self.applicationInfo.emcee = '';
      _self.applicationInfo.emceeName = '';
      _self.emceeNameStr = '';
    },
    //增加会议室
    addMeeting() {
      let _self = this;
      _self.nextTodoId++;
      _self.meetingList.push({
        startTime: '',
        endTime: '',
        meetingRoom: '',
        meetingRoomId: '',
        defaultProcessid: '',
        indexId: `meetingindex-${_self.nextTodoId}`
      });
    },
    //删除会议室
    deletMeetingRoom(index) {
      let _self = this;
      _self.selectAppAddressIndex = 0;
      _self.meetingList.splice(index, 1);
    },
    //显示时间弹出层
    showPicker(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.selectTimeIndex = data.dateIndex;
      _self.selectTimeType = data.dateType;
      _self.selectTimeValue = data.dateValue;
      _self.$refs[data.dateRef].show();
    },
    //时间选择确认
    onConfirm(res) {
      let _self = this;
      if (_self.selectTimeType == 'endTime') {
        let compareTime = _self.meetingList[_self.selectTimeIndex]['startTime'];
        if (compareTime && compareTime > res.result) {
          _self.meetingList[_self.selectTimeIndex]['startTime'] = '';
          _self.meetingList[_self.selectTimeIndex]['endTime'] = '';
          return false;
        } else if (
          compareTime &&
          compareTime.substring(0, 10) != res.result.substring(0, 10)
        ) {
          _self.meetingList[_self.selectTimeIndex]['startTime'] = '';
          _self.meetingList[_self.selectTimeIndex]['endTime'] = '';
          uni.showToast({ title: '会议室不可跨天预约', icon: 'none' });
          return false;
        }
      } else if (_self.selectTimeType == 'startTime') {
        let compareTime = _self.meetingList[_self.selectTimeIndex]['endTime'];
        if (compareTime && compareTime < res.result) {
          _self.meetingList[_self.selectTimeIndex]['startTime'] = '';
          _self.meetingList[_self.selectTimeIndex]['endTime'] = '';
          return false;
        } else if (
          compareTime &&
          compareTime.substring(0, 10) != res.result.substring(0, 10)
        ) {
          _self.meetingList[_self.selectTimeIndex]['startTime'] = '';
          _self.meetingList[_self.selectTimeIndex]['endTime'] = '';
          uni.showToast({ title: '会议室不可跨天预约', icon: 'none' });
          return false;
        }
      }
      _self.meetingList[_self.selectTimeIndex][_self.selectTimeType] =
        res.result;
    },
    //显示弹出层
    showPopup(e) {
      let _self = this;
      let data = e.currentTarget.dataset;
      _self.popupType = data.popupType;
      _self.popupDatasName = data.popupDatasName;
      if (data.popupDatasName == 'appAddress') {
        _self.selectAppAddressIndex = data.popupDatasIndex;
      }
      _self.$nextTick(() => {
        _self.$refs[`show${data.popupName}`].open();
      });
    },
    //选择会议类型、会议地点
    selectColumn(e) {
      let data = e.currentTarget.dataset;
      if (data.popupDatasName == 'appAddress') {
        this.meetingList[this.selectAppAddressIndex]['meetingRoom'] =
          data.columnName;
        this.meetingList[this.selectAppAddressIndex]['meetingRoomId'] =
          data.columnId;
        this.meetingList[this.selectAppAddressIndex]['defaultProcessid'] =
          data.columnProcessid;
      } else if (data.popupDatasName == 'appType') {
        this.applicationInfo.apptypeName = data.columnName;
        this.applicationInfo.apptypeid = data.columnId;
      }
      this.$nextTick(() => {
        this.$refs[`show${data.popupName}`].close();
      });
    },
    //上传文件
    addFile() {
      let _self = this;
      chooseFile({
        limitNum: 9, //数量
        uploadFileUrl: `${_self.$config.BASE_HOST}/ts-document/attachment/fileUpload?module=hrm`, //服务器地址
        fileKeyName: 'file', //参数
        showLoading: true,
        hideLoading: true,
        loadingTitle: '上传中...',
        success: res => {
          let resVal = JSON.parse(res),
            nameList = resVal.object[0].fileName.split('.');
          resVal.object[0].fileType =
            nameList.length > 1 ? nameList[nameList.length - 1] : '';
          _self.fileList = _self.fileList.concat(resVal.object);
          _self.fileIdList.push(resVal.object[0].fileId);
          _self.fileNameList.push(resVal.object[0].fileName);
          _self.filePathList.push(resVal.object[0].filePath);
        }
      });
    },
    //删除文件
    deletFile(id) {
      let index = this.fileList.findIndex(item => item.id === id);
      this.fileList.splice(index, 1);
      this.fileNameList.splice(index, 1);
      this.fileIdList.splice(index, 1);
      this.filePathList.splice(index, 1);
    },
    //下载文件
    downloadFile(id, fileName) {
      let _self = this,
        filePath = `${
          _self.$config.ENABLE_FILE_PREVIEW
            ? _self.$config.DOCUMENT_BASE_HOST
            : _self.$config.BASE_HOST
        }/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (_self.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            _self.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      } else {
        _self.$downloadFile.downloadFile(filePath);
      }
    },
    //提交检查表单
    formSubmit(e) {
      let _self = this;
      //定义表单规则
      let rule = [
        {
          filedKey: 'linktelePerson',
          required: true,
          checkType: 'notnull',
          errorMsg: '请输入联系人'
        },
        {
          filedKey: 'linktelePhone',
          required: true,
          checkType: 'phone',
          errorMsg: '请输入联系方式',
          otherErrorMsg: '联系方式输入有误'
        },
        {
          filedKey: 'apptypeName',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择会议类型'
        },
        {
          filedKey: 'motif',
          required: true,
          checkType: 'notnull',
          errorMsg: '请输入会议主题'
        },
        {
          filedKey: 'moderator',
          required: true,
          checkType: 'notnull',
          errorMsg: '请选择会议主持人'
        },
        {
          filedKey: 'content',
          required: true,
          checkType: 'notnull',
          errorMsg: '请输入会议内容'
        }
      ];
      //进行表单检查
      let checkRes = graceChecker.check(e.detail.value, rule);
      if (checkRes) {
        let checkMeeingRes = _self.meetingListChecker();
        if (!_self.isRepeat(_self.meetingList)) {
          uni.showToast({ title: '会议重复申请', icon: 'none' });
          return false;
        }
        if (checkMeeingRes) {
          _self.applicationInfo.accessoryName = _self.fileNameList.join('#,#');
          _self.applicationInfo.accessoryId = _self.fileIdList.join('#,#'); //附件id
          _self.applicationInfo.accessoryUrl = _self.filePathList.join('#,#'); //附件id
          _self.submitApply();
        }
      } else {
        uni.showToast({ title: graceChecker.error, icon: 'none' });
      }
    },
    //检查会议室列表
    meetingListChecker() {
      let _self = this,
        list = _self.meetingList,
        startStr = '',
        endStr = '',
        addressStr = '',
        addressIdStr = '',
        processStr = '';
      for (var j = 0; j < list.length; j++) {
        let obj = list[j];
        for (var k in obj) {
          if (obj[k] == null || obj[k].length < 1) {
            if (k == 'startTime') {
              uni.showToast({ title: '请选择开始时间', icon: 'none' });
            } else if (k == 'endTime') {
              uni.showToast({ title: '请选择结束时间', icon: 'none' });
            } else if (k == 'meetingRoom') {
              uni.showToast({ title: '请选择会议地点', icon: 'none' });
            } else if (k == 'meetingRoomId') {
              uni.showToast({ title: '请选择会议地点', icon: 'none' });
            }
            return false;
          } else {
            let value = `${obj[k]}#`;
            if (k == 'startTime') {
              startStr += value;
            } else if (k == 'endTime') {
              endStr += value;
            } else if (k == 'meetingRoom') {
              addressStr += value;
            } else if (k == 'meetingRoomId') {
              addressIdStr += value;
            } else if (k == 'defaultProcessid') {
              processStr += value;
            }
          }
        }
      }
      _self.applicationInfo.startTimeStr = startStr.substring(
        0,
        startStr.length - 1
      );
      _self.applicationInfo.endTimeStr = endStr.substring(0, endStr.length - 1);
      _self.applicationInfo.boardroomIdStr = addressIdStr.substring(
        0,
        addressIdStr.length - 1
      );
      _self.applicationInfo.roomNames = addressStr.substring(
        0,
        addressStr.length - 1
      );
      _self.applicationInfo.defaultProcessidStr = processStr.substring(
        0,
        processStr.length - 1
      );
      return true;
    },
    //判断重复申请
    isRepeat(arr) {
      for (let i = 0; i < arr.length - 1; i++) {
        for (let j = i + 1; j < arr.length; j++) {
          if (
            (arr[i].meetingRoomId === arr[j].meetingRoomId &&
              arr[i].startTime >= arr[j].startTime &&
              arr[i].endTime <= arr[j].endTime) ||
            (arr[i].meetingRoomId === arr[j].meetingRoomId &&
              arr[i].endTime >= arr[j].startTime &&
              arr[i].endTime <= arr[j].endTime) ||
            (arr[i].meetingRoomId === arr[j].meetingRoomId &&
              arr[i].startTime <= arr[j].startTime &&
              arr[i].endTime >= arr[j].endTime)
          )
            return false;
        }
      }
      return true;
    },
    //提交申请到服务器
    submitApply() {
      let _self = this;
      _self.ajax.saveMeetingApply(_self.applicationInfo).then(res => {
        uni.showToast({
          title: '提交成功!',
          icon: 'none',
          complete: () => {
            setTimeout(() => {
              uni.redirectTo({
                url: '/pages/workflow/my-workflow-list'
              });
            }, 1500);
          }
        });
      });
    },
    checkMeetingUsage() {
      uni.navigateTo({
        url: '/pages/meeting/meeting-usage?fromPage=initMeetingWorkflow'
      });
    },
    //返回上一层
    returnBack() {
      let _self = this;
      if (_self.fromPage === 'workflowList') {
        uni.redirectTo({
          url: '/pages/workflow/workflow-list'
        });
      } else {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  .meeting_content {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .basic_user_info {
      margin-bottom: 140rpx;
    }
  }
  .dis_flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .row_group {
    margin-top: 30rpx;
    .row {
      width: 100%;
      background-color: #ffffff;
      position: relative;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child::after {
        height: 0;
      }
      .row_lable {
        width: 240rpx;
        font-size: 32rpx;
        color: #333;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        .row_lable_text {
          padding-right: 20rpx;
          box-sizing: border-box;
        }
        .required_red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add_icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
      }
      .row_lable ~ .row_value {
        flex: 1;
        font-size: 32rpx;
        color: #666;
        padding: 22rpx 30rpx;
        padding-left: 0;
        box-sizing: border-box;
        text-align: right;
      }
      .row_lable ~ .row_value_input {
        display: flex;
        justify-content: center;
        align-items: center;
        .row_value_input_text {
          text-align: right;
          flex: 1;
          font-size: 32rpx;
        }
      }
      .row_lable ~ .row_value_textarea {
        width: 100%;
        padding-left: 30rpx;
        padding-top: 0;
        text-align: left;
        .row_value_textarea_text {
          width: 100%;
          min-height: 180rpx;
          font-size: 32rpx;
        }
        .textarea-placeholder {
          color: #bbb;
        }
      }
      .row_lable ~ .row_value_personal {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
        padding: 0 30rpx 22rpx;
        .personNameStr {
          flex: 1;
        }
      }
    }
  }
  .meeting_room_list {
    position: relative;
    margin-top: 15px;
    .meeting_room_situation {
      position: absolute;
      right: 30rpx;
      font-size: 28rpx;
      color: #005bac;
    }
    .meeting_room_item_title {
      color: #999;
      font-size: 24rpx;
      padding: 0 30rpx 16rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .delet_meeting_room_btn {
        color: #005bac;
        font-size: 26rpx;
      }
    }
    .tip {
      color: #999;
      font-size: 24rpx;
      padding: 10rpx 30rpx 16rpx;
    }
    .meeting_room_add_btn {
      background-color: #fff;
      color: #005bac;
      font-size: 32rpx;
      text-align: center;
      padding: 22rpx 30rpx;
    }
  }
  .meeting_room_file {
    background-color: #fff;
    margin-top: 15px;
    position: relative;
    overflow: hidden;
    .file_title {
      padding: 22rpx 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #333;
      .oa-icon {
        font-size: 56rpx;
        position: absolute;
        right: 30rpx;
        color: #bbb;
      }
    }
    .file_list {
      .file_item {
        text-decoration: none;
        font-size: 32rpx;
        color: #333333;
        margin: 10rpx 20rpx 20rpx;
        padding: 6rpx 20rpx;
        border: 1px solid #eeeeee;
        border-radius: 5px;
        margin-bottom: 20rpx;
        display: flex;
        align-items: center;
        .file_item_info {
          text-decoration: none;
          flex: 1;
          text-align: left;
          display: flex;
          align-items: center;
          .file_item_name {
            flex: 1;
            margin: 0 20rpx;
          }
        }
        .oa-icon {
          font-size: 40rpx;
          color: $theme-color;
        }
        .file_name {
          font-size: 28rpx;
          color: #333333;
        }
        .file_size {
          color: #999999;
          font-size: 24rpx;
          margin-left: 20rpx;
        }
      }
    }
  }
  .bottom_btn {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #ffffff;
    box-shadow: 0 1px 6px #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    z-index: 10;
    .btn_item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
  .scroll_list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact-list {
    background-color: #ffffff;
    .contact-item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item-text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact-item-icon {
        line-height: 1;
      }
      .contact-item-info {
        flex: 1;
      }
      .equipment {
        .equipment_item {
          padding: 0 10rpx;
          color: #666;
          font-size: 24rpx;
          &:first-child {
            padding-left: 0;
          }
        }
      }
      .describe {
        display: flex;
        align-items: center;
        .describe_item {
          font-size: 24rpx;
          padding: 0 10rpx;
          color: #666;
          &:first-child {
            padding-left: 0;
          }
        }
      }
    }
  }
}
</style>
