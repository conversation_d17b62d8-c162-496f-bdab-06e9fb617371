<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="我的申请"></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      cancelButton="none"
      @confirm="search"
      placeholder="请输入流程名称或发起人"
    ></uni-search-bar>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.title }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0 && index != 2"
          >
            {{ tab.total >= 100 ? '99+' : tab.total }}
          </text>
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.workflowNumber"
              @tap="chooseItem(row, item['workflowType'], index)"
            >
              <view class="contact_item_row">
                <text
                  v-if="item['handleStatus'] != 2 && row.isPress === 1"
                  class="contact_item_speed"
                >
                  [催办]
                </text>
                <text
                  v-if="row.urgencyLevel && row.urgencyLevel != 1"
                  class="contact_item_urge"
                  >[{{ $oaModule.getUrgencyLevel(row.urgencyLevel) }}]</text
                >
                <view class="contact_item_title">
                  <text class="title">{{ row.workflowTitle }}</text>
                </view>
              </view>
              <view class="contact_item_title font-nomal">
                <text class="contact_item_time">
                  发起人:{{ row.createUserName }}
                </text>
                <text class="contact_item_time">
                  {{
                    (index != 2 ? row.createDate : row.wfFinishedDate)
                      | formatTime
                  }}
                </text>
              </view>
              <view class="contact_item_row">
                <text class="contact_item_node">
                  {{
                    row.status === 2 || row.status === 0
                      ? ''
                      : '当前节点：' + row.currentStepName
                  }}
                </text>
                <text
                  class="contact_item_status"
                  :style="[row.style]"
                  v-if="row.statusName"
                >
                  {{ row.statusName }}
                </text>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      keywords: '',
      tabIndex: 0, //当前选中的tab索引值，从0计数
      tabBars: [
        {
          title: '在办',
          workflowType: 'myDoing',
          handleStatus: 1,
          status: '',
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        },
        {
          title: '退回',
          workflowType: 'myReturn',
          handleStatus: 3, //办理状态
          status: '',
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          list: [],
          total: 0
        },
        {
          title: '办结',
          workflowType: 'myDone',
          handleStatus: 2,
          status: '',
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        },
        {
          title: '草稿',
          workflowType: 'myDraft',
          handleStatus: '',
          status: 0,
          downOption: false,
          isInit: false,
          list: [],
          total: 0
        }
      ],
      workflowNo: ''
    };
  },
  onLoad(opt) {
    if (opt.index) this.tabIndex = Number(opt.index);
    if (opt.workflowNo) this.workflowNo = opt.workflowNo;
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.getInfoNum();
  },
  methods: {
    getInfoNum() {
      this.ajax
        .getMyLaunchWorkflowCount({
          condition: this.keywords,
          workflowNo: this.workflowNo
        })
        .then(res => {
          this.$set(this.tabBars[0], 'total', res.object.inProcessCount);
          this.$set(this.tabBars[1], 'total', res.object.sendBackCount);
        });
    },
    //搜索
    search(res) {
      this.keywords = res.value;
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.$nextTick(() => {
        this.tabBars[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //tab点解切换
    async ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    //根据类型获取数据
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getMyLaunchWorkflowList({
          isMobile: true,
          handleStatus: this.tabBars[index]['handleStatus'],
          status: this.tabBars[index]['status'],
          pageSize: page.size,
          pageNo: page.num,
          workflowNo: this.workflowNo,
          workflowTitle: this.keywords,
          sidx: `inst.${index != '2' ? 'CREATE_DATE' : 'wf_finished_date'}`,
          sord: 'desc'
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      rows.forEach(item => {
        item.style = this.initStyle(item.statusName);
        this.tabBars[index]['list'].push(item);
      });
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    initStyle(value) {
      let style = {};
      switch (value) {
        case '待我办理':
          style.color = '#005BAC';
          style.backgroundColor = '#e5edff';
          break;
        case '退回上一步':
          style.color = '#ff8100';
          style.backgroundColor = '#fff1da';
          break;
        case '退回重办':
          style.color = '#ff8100';
          style.backgroundColor = '#fff1da';
          break;
        case '我已办理':
          style.color = '#999';
          style.backgroundColor = '#eee';
          break;
      }
      return style;
    },
    chooseItem(row, workflowType, index) {
      let pagePath = '',
        pagePramas;
      if (row.status == 0) {
        pagePath = '/pages/workflow/init-custom-workflow';
        pagePramas = {
          wfDefinitionId: row.wfDefinitionId,
          workflowNo: row.workflowNo,
          formId: row.formId,
          businessId: row.businessId,
          btnType: 'draft'
        };
      } else {
        if (row.isNormal === 'N') {
          pagePath = '/pages/workflow/my-workflow-detail';
        } else {
          pagePath = row.mobileDetailPageUrl;
        }
        uni.setStorageSync('workflow_info', row);
        pagePramas = {
          name: workflowType,
          formListPage: 'myApplyList',
          formListTabIndex: index
        };
      }
      uni.navigateTo({
        url: `${pagePath}?${this.$common.convertObj(pagePramas)}`
      });
    },
    //返回上一层
    returnBack() {
      if (this.workflowNo) {
        this.$parentTypeFun({
          type: 'jumpPage',
          path:
            '/ts-mobile-hrms/pages/consultation-management/index?fromPage=workBench&index=2'
        });
      } else {
        this.$parentTypeFun({
          type: 'jumpPage',
          path: '/workbench'
        });
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow: hidden;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 25%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title {
        color: #333;
        font-size: 30rpx;
        height: 80rpx;
        line-height: 76rpx;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        height: 80rpx;
        line-height: 28rpx;
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .contact_list {
    height: 100%;
    overflow: scroll;
  }

  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_row {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
    .contact_item_title {
      font-size: 32rpx;
      color: #333333;
      font-weight: bold;
      overflow: hidden;
      display: flex;
      justify-content: space-between;
      align-items: center;
      flex: 1;
      &.font-nomal {
        font-weight: 400;
      }
      .title {
        width: 100%;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        word-break: break-all;
      }
    }
    .contact_item_time {
      font-size: 24rpx;
      color: #999;
      overflow: hidden;
      .contact_item_icon {
        color: #f59a23;
        padding-right: 10rpx;
        font-size: 28rpx;
      }
    }
    .contact_item_node {
      font-size: 28rpx;
      color: #666;
    }
    .contact_item_speed {
      color: #dd1f36;
    }
    .contact_item_urge {
      color: #f59a23;
    }
    .contact_item_speed,
    .contact_item_urge {
      font-size: 28rpx;
      font-weight: bold;
    }
    .contact_item_status {
      font-size: 24rpx;
      transform: scale(0.83);
      color: #999;
      background-color: #eee;
      padding: 2rpx 10rpx;
      border-radius: 8rpx;
    }
  }
}
</style>
