<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="详情"></page-head>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-view-id="tab.viewId"
        :data-current="index"
        @click="onTabTap"
      >
        <text
          class="uni-tab-item-title"
          :class="tabIndex == index ? 'uni-tab-item-title-active' : ''"
          >{{ tab.name }}</text
        >
      </view>
    </scroll-view>
    <view class="content_wrap" v-if="showContent">
      <scroll-view
        class="content"
        scroll-y="true"
        :scroll-into-view="scrollViewId"
        :scroll-with-animation="true"
        @scroll="scroll"
      >
        <view class="form_wrap scroll-view-item" id="form">
          <view class="node_info">
            <view class="node_info_row">
              <text class="node_info_title">{{
                workflowInfo.workflowTitle
              }}</text>
            </view>

            <view class="node_info_time">
              流程编号： {{ workflowInfo.workflowNumber }}
            </view>
            <view class="node_info_row">
              <text v-if="workflowInfo.isPress === 1" class="node_info_speed"
                >[催办]</text
              >
              <text
                v-if="
                  workflowInfo.urgencyLevel && workflowInfo.urgencyLevel != 1
                "
                class="node_info_urge"
                >[{{
                  $oaModule.getUrgencyLevel(workflowInfo.urgencyLevel)
                }}]</text
              >
              <text class="node_info_time">{{
                workflowInfo.updateDate | formatTime
              }}</text>
            </view>
            <view class="node_info_row">
              <text class="node_info_node">
                发起人 {{ applicationInfo.applyEmpName }}
              </text>
              <text class="node_info_node">{{
                workflowInfo.status === 2
                  ? ''
                  : '当前节点：' + workflowInfo.currentStepName
              }}</text>
              <text
                class="node_info_status"
                :class="{
                  node_info_status_blue: workflowInfo.statusName === '待我办理',
                  node_info_status_org:
                    workflowInfo.statusName === '退回上一步' ||
                    workflowInfo.statusName === '退回重办'
                }"
              >
                {{ workflowInfo.statusName }}
              </text>
            </view>
          </view>
          <collapse-card :dataSource="meetingRoomDetails" />
          <view class="row-group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">主题</text>
              </view>
              <view class="row_value">
                <text class="row_value_text"> {{ applicationInfo.motif }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">参会人员</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">
                  {{ applicationInfo.attendNames }}
                </text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">参会人数</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">
                  {{ applicationInfo.controlNumber }}
                </text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议类型</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">{{
                  applicationInfo.appTypeName
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">预约时段</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">
                  {{ applicationInfo.times }}
                </text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">重复</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">
                  {{ applicationInfo.repeatRateName }}
                </text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">重复期至</text>
              </view>
              <view class="row_value">
                <text class="personNameStr">
                  {{ applicationInfo.repeatEndTime || '-' }}
                </text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">所需设备</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">
                  {{ applicationInfo.device }}</text
                >
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">备注</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">
                  {{ applicationInfo.remark }}</text
                >
              </view>
            </view>
          </view>
          <!-- <view
            class="row-group"
            v-for="(item, index) in applicationInfo.list"
            :key="index"
          >
            <view class="meeting_title">会议室({{ index + 1 }})</view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">开始时间</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.startTime | formatTime
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">结束时间</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.endTime | formatTime
                }}</text>
              </view>
            </view>
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">会议地点</text>
              </view>
              <view class="row_value">
                <text class="row_value_text">{{
                  item.location + '-' + item.name
                }}</text>
              </view>
            </view>
          </view>
          <view class="row-group">
            <view class="row dis_flex">
              <view class="row_lable">
                <text class="row_lable_text">附件</text>
              </view>
              <view class="row_value">
                <view
                  class="fiel_list"
                  v-if="
                    JSON.stringify(applicationInfo) != '{}' &&
                      applicationInfo.fileList.length > 0
                  "
                >
                  <view
                    class="file_item"
                    v-for="(item, index) in applicationInfo.fileList"
                    :key="index"
                    @tap="downloadFile(item.id)"
                  >
                    <text
                      class="oa-icon"
                      :class="'oa-icon-' + $oaModule.formatFileType(item.type)"
                    ></text>
                    <text>{{ item.name }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view> -->
        </view>
        <view class="form_wrap scroll-view-item" id="opinion">
          <view class="meeting_title">审批意见</view>
          <view v-if="optionList.length > 0" class="option_list">
            <view
              class="option_item"
              v-for="(item, index) in optionList"
              :key="index"
            >
              <view class="option_item_icon">
                {{ item.actAssigneeName | nameFilter }}
              </view>
              <view class="option_item_info">
                <view class="option_item_approval_name"
                  >{{ item.approvalFiledName }}
                </view>
                <view class="option_item_top">
                  <view class="option_item_top_left">
                    <text class="option_item_name">{{
                      item.actAssigneeName
                    }}</text>
                    <text
                      class="option_item_response"
                      :class="
                        item.selectResponse === '【通过】'
                          ? 'option_item_assent'
                          : 'option_item_dissent'
                      "
                      >{{ item.selectResponse }}</text
                    >
                  </view>
                  <text class="option_item_time">{{
                    item.finishedDate | formatTime
                  }}</text>
                </view>
                <!-- <view class="option_item_type">
                  {{ item.wfStepName }}
                </view> -->
                <view class="option_item_content"
                  >审批内容：{{ item.remark }}</view
                >
              </view>
            </view>
          </view>
          <view class="nothing" v-else>
            <view class="img_content">
              <image
                class="nothing_img"
                src="../../static/img/nothing.png"
                mode="aspectFit"
              ></image>
            </view>
          </view>
        </view>
        <view class="form_wrap scroll-view-item" id="record">
          <view class="meeting_title">流程信息</view>
          <view class="task_history_wrap">
            <data-process-history
              :processHistoryList="taskHistoryList"
            ></data-process-history>
          </view>
        </view>
      </scroll-view>
      <view class="btn_wrap" v-if="nameType === 'approvalToDo'">
        <button class="btn_item" @tap="formReturn">
          <text class="oa-icon oa-icon-liuchengtuihui"></text>
          不通过
        </button>
        <button class="btn_item theme-color" @tap="formSubmit">
          <text class="oa-icon oa-icon-tijiaobanli"></text>
          通过
        </button>
      </view>
    </view>
  </view>
</template>

<script>
import { mapState, mapMutations } from 'vuex';
import Base64 from '@/common/js/base64.min.js';
import loginModule from '@/common/js/loginModule.js';
import CollapseCard from './components/collapse-card.vue';
export default {
  components: {
    CollapseCard
  },
  data() {
    return {
      showContent: false,
      nameType: '',
      parentFrom: '',
      tabIndex: 0,
      tabBars: [
        {
          name: '申请详情',
          viewId: 'form'
        },
        {
          name: '审批意见',
          viewId: 'opinion'
        },
        {
          name: '流程信息',
          viewId: 'record'
        }
      ],
      formListPage: '',
      formListTabIndex: null,
      scrollStatus: true, //点击状态，是否能点击
      scrollViewId: '',
      applicationInfo: {},
      meetingRoomDetails: {},
      meetingRoomTypes: [],
      optionList: [],
      taskHistoryList: []
    };
  },
  computed: {
    ...mapState(['empcode'])
  },
  async onLoad(opt) {
    if (opt && opt.token && !this.$config.ENABLE_ACCOUNT_LOGIN) {
      let userInfo = await loginModule.loginInfo(opt);
      if (JSON.stringify(userInfo) != '{}') this.changeState(userInfo);
    }
    try {
      const meetingRoomTypes = await this.ajax.getDictItemByTypeCode({
        typeCode: 'MEETING_TYPE'
      });
      this.meetingRoomTypes = meetingRoomTypes.object || [];
    } catch (error) {}

    if (opt.isMobile) {
      this.getWorkflowData(opt.wfInstId, opt.status);
    } else {
      this.nameType = opt.name;
      this.parentFrom = opt.parentFrom ? opt.parentFrom : '';
      this.formListPage = opt.formListPage ? opt.formListPage : '';
      this.formListTabIndex = opt.formListTabIndex ? opt.formListTabIndex : '';
      this.workflowInfo = uni.getStorageSync('workflow_info');
      this.getData();
    }
  },
  methods: {
    ...mapMutations(['changeState']),
    //获取流程信息数据
    async getWorkflowData(wfInstanceId, status) {
      let _self = this;
      await _self.ajax
        .getWorkflowData({
          wfInstId: wfInstanceId,
          status: status //待办-1，办结-2
        })
        .then(async res => {
          _self.workflowInfo = res.object;
          uni.setStorageSync('workflow_info', _self.workflowInfo);
          let assigneeNo = res.object.assigneeNo
              ? res.object.assigneeNo.split(',')
              : '',
            permission = assigneeNo.includes(_self.empcode);
          if (res.object.status === 1 && permission != -1) {
            _self.nameType = 'approvalToDo';
          }
          await _self.getData();
        });
    },
    //获取数据
    async getData() {
      let _self = this;
      const applyRes = await _self.ajax.boardRoomApplyGet({
        id: _self.workflowInfo.businessId
      });
      if (applyRes.success && applyRes.statusCode === 200) {
        let repeatList = [
          { value: 0, label: '不重复' },
          { value: 1, label: '每天重复' },
          { value: 2, label: '每周重复' },
          { value: 3, label: '每月重复' }
        ];

        applyRes.object.applyEmpName =
          applyRes.object.applyEmployee.employeeName;
        applyRes.object.applyOrgname = applyRes.object.applyEmployee.orgName;

        applyRes.object.attendNames = applyRes.object.attendEmployeeList
          .map(e => {
            return e.username;
          })
          .join(',');

        if (applyRes.object.appTypeId) {
          applyRes.object.appTypeName = this.meetingRoomTypes.filter(
            item => item.itemNameValue == applyRes.object.appTypeId
          )[0].itemName;
        }

        applyRes.object.times = [
          applyRes.object.startTime,
          applyRes.object.endTime
        ].join(' ~ ');

        let [repeatFilter = {}] = repeatList.filter(
          item => item.value == applyRes.object.repeatRate
        );

        applyRes.object.repeatRateName = repeatFilter?.label || '-';

        _self.applicationInfo = applyRes.object;
        _self.applicationInfo.fileList = [];

        // 会议室详情
        const roomRes = await _self.ajax.boardRoomGet({
          id: applyRes.object.boardroomId
        });
        if (roomRes.success && roomRes.statusCode === 200) {
          _self.meetingRoomDetails = roomRes.object;
        }

        // 审批详情
        const taskRes = await _self.ajax.getTaskHisList({
          wfInstId: _self.applicationInfo.wfInstanceId,
          sidx: 'finished_date',
          sord: 'desc',
          pageNo: 1,
          pageSize: 100
        });
        _self.taskHistoryList = _self.$oaModule.taskHisList(taskRes.rows || []);

        // 处理附件
        // if (applyRes.object.accessoryId) {
        //   let fileRes = await this.ajax.getFileAttachmentByBusinessId({
        //     businessId: applyRes.object.accessoryId
        //   });
        //   if (fileRes.success && fileRes.statusCode === 200) {
        //     _self.applicationInfo.fileList = fileRes.object || [];
        //   }
        // }

        _self.showContent = true;
      }
      return;

      // await _self.ajax
      //   .getMeetingDatas(_self.workflowInfo.businessId)
      //   .then(async res => {
      //     let data = res.object,
      //       meeting = {};
      //     if (data.length > 0) {
      //       let fileName = data[0].accessoryName
      //           ? data[0].accessoryName.split('#,#')
      //           : [],
      //         fileId = data[0].accessoryId
      //           ? data[0].accessoryId.split('#,#')
      //           : [];

      //       meeting.fileList = fileId.map((item, index) => {
      //         let nameList =
      //           (fileName[index] && fileName[index].split('.')) || [];
      //         let icon =
      //           nameList.length > 1 ? nameList[nameList.length - 1] : '';
      //         return {
      //           id: item,
      //           name: fileName[index],
      //           type: icon
      //         };
      //       });
      //       meeting.list = data.map(item => {
      //         return {
      //           startTime: item.startTime,
      //           endTime: item.endTime,
      //           location: item.location,
      //           name: item.name
      //         };
      //       });
      //       _self.applicationInfo = { ...data[0], ...meeting };
      //     }
      //     await _self.getApprovalOpinion();
      //   });
    },
    //获取审批意见
    async getApprovalOpinion() {
      let _self = this;
      await _self.ajax
        .getApprovalOpinion(_self.workflowInfo.wfInstanceId)
        .then(async res => {
          if (res.object.length > 0) {
            res.object.forEach(item => {
              _self.commentList.forEach(one => {
                if (item.approvalFiled === one.wordNameKey) {
                  _self.optionList.push({
                    ...item,
                    ...{ approvalFiledName: one.wordName }
                  });
                }
              });
            });
          }
          await this.getTaskHisList();
        });
    },
    //获取流程信息
    async getTaskHisList() {
      let _self = this;
      await _self.ajax
        .getTaskHisList({
          wfInstId: _self.workflowInfo.wfInstanceId,
          pageNo: 1,
          pageSize: 100,
          sidx: 'finished_date',
          sord: 'desc'
        })
        .then(async res => {
          _self.taskHistoryList = _self.$oaModule.taskHisList(res.rows);
          await _self.getCurrentNodeTips();
        });
    },
    //获取流程节点提示信息
    async getCurrentNodeTips() {
      let _self = this;
      await _self.ajax
        .getCurrentNodeTips(_self.workflowInfo.wfInstanceId)
        .then(res => {
          _self.showContent = true;
          if (res.object.backToStepStr) {
            uni.showModal({
              content: `${res.object.backToStepStr}\n退回说明：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              showCancel: false,
              confirmColor: '#005BAC'
            });
          } else if (res.object.handleMarkedWords) {
            uni.showModal({
              content: `办理提示：${res.object.handleMarkedWords}`,
              confirmText: '知道了',
              confirmColor: '#005BAC',
              showCancel: false
            });
          }
        });
    },
    //tab点击事件
    onTabTap(e) {
      // 200毫秒才能执行下次点击
      if (this.scrollStatus) {
        this.scrollStatus = false;
        let data = e.currentTarget.dataset;
        this.scrollViewId = data.viewId;
        this.tabIndex = data.current;
        setTimeout(() => {
          // 200毫秒才能执行下次点击
          this.scrollStatus = true;
        }, 200);
      }
    },
    scroll(e) {},
    //查看附件详情
    downloadFile(id) {
      let _self = this;
      _self.ajax.getFiles({ idsStr: id }).then(res => {
        let filePath = `${
          _self.$config.ENABLE_FILE_PREVIEW
            ? _self.$config.DOCUMENT_BASE_HOST
            : _self.$config.BASE_HOST
        }/ts-document/attachment/downloadFile/${id}?fullfilename=${
          res.object[0].fileName
        }&source=mobile`;
        if (_self.$config.ENABLE_FILE_PREVIEW) {
          uni.navigateTo({
            url: `/pages/webview/webview?url=${
              _self.$config.BASE_HOST
            }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
          });
        } else {
          _self.$downloadFile.downloadFile(filePath);
        }
      });
    },
    //不通过
    formReturn() {
      uni.setStorageSync('comment_field', [
        {
          fieldType: 'comment',
          fileName: 'meetingComment',
          showName: '审核意见',
          isMust: '',
          defaultVal: '',
          placeholderContent: ''
        }
      ]);
      uni.setStorageSync('form_info', {
        taskId: this.workflowInfo.taskId,
        status: -1,
        businessId: this.workflowInfo.businessId,
        applyId: this.applicationInfo.id
      });
      this.$nextTick(() => {
        let pagePramas = {
          name: this.nameType,
          fromPage: 'approvalMeetingReturn'
        };
        if (this.parentFrom) pagePramas.parentFrom = this.parentFrom;
        uni.navigateTo({
          url: `/pages/workflow/operation/approval-pass?${this.$common.convertObj(
            pagePramas
          )}`
        });
      });
    },
    //通过
    formSubmit() {
      uni.setStorageSync('comment_field', [
        {
          fieldType: 'comment',
          fieldName: 'meetingComment',
          keyId: 'meetingComment',
          showName: '审核意见',
          isMust: '',
          defaultVal: '',
          placeholderContent: ''
        }
      ]);
      uni.setStorageSync('form_info', {
        taskId: this.workflowInfo.taskId,
        status: 1,
        businessId: this.workflowInfo.businessId,
        applyId: this.applicationInfo.id
      });
      this.$nextTick(() => {
        let pagePramas = {
          name: this.nameType,
          fromPage: 'approvalMeetingPass'
        };
        if (this.parentFrom) pagePramas.parentFrom = this.parentFrom;
        uni.navigateTo({
          url: `/pages/workflow/operation/approval-pass?${this.$common.convertObj(
            pagePramas
          )}`
        });
      });
    },
    //返回上一层
    returnBack() {
      let pagePath = '';
      uni.removeStorageSync('workflow_info');
      if (this.parentFrom) {
        pagePath = '/pages/index/work-unread-list';
      } else {
        pagePath = '/pages/workflow/workflow-approval-list';
      }
      if (this.formListPage) {
        if (this.formListPage == 'copyList') {
          pagePath = `/pages/workflow/workflow-copy-list?fromPage=workBench&index=0`;
        } else {
          pagePath = `/pages/workflow/my-workflow-list?fromPage=workBench&index=${this.formListTabIndex}`;
        }
      }
      this.$nextTick(() => {
        uni.redirectTo({
          url: pagePath
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  .dis_flex {
    display: flex;
    justify-content: center;
  }
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 33.3333333%;
      padding-left: 34rpx;
      padding-right: 34rpx;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title {
        color: #555;
        font-size: 30rpx;
        height: 80rpx;
        line-height: 76rpx;
        flex-wrap: nowrap;
        display: block;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .meeting_title {
    margin: 20rpx 30rpx 10rpx;
    font-size: 28rpx;
    color: #999;
  }
  .content_wrap {
    flex: 1;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: hidden;
      .node_info {
        background-color: #fff;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        border-bottom: 1px solid #eee;
        .node_info_row {
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_title {
          font-size: 32rpx;
          color: #333333;
          font-weight: bold;
          overflow: hidden;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .node_info_time {
          font-size: 24rpx;
          color: #999;
          overflow: hidden;
          .node_info_icon {
            color: #f59a23;
            padding-right: 10rpx;
            font-size: 28rpx;
          }
        }
        .node_info_node {
          font-size: 28rpx;
          color: #666;
        }
        .node_info_speed {
          color: #dd1f36;
        }
        .node_info_urge {
          color: #f59a23;
        }
        .node_info_speed,
        .node_info_urge {
          font-size: 28rpx;
          font-weight: bold;
        }
        .node_info_status {
          font-size: 24rpx;
          color: #999;
          font-weight: bold;
        }
        .node_info_status_blue {
          color: #005bac;
        }
        .node_info_status_org {
          color: #f59a23;
        }
      }
      .row-group {
        margin-top: 20rpx;
        .row {
          width: 100%;
          background-color: #ffffff;
          position: relative;
          &::after {
            position: absolute;
            content: '';
            bottom: 0;
            left: 30rpx;
            right: 0;
            transform: scaleY(-0.5);
            height: 1px;
            background-color: #eee;
          }
          &:last-child::after {
            height: 0;
          }
          .row_lable {
            width: 200rpx;
            font-size: 30rpx;
            color: #999;
            padding: 22rpx 30rpx;
            box-sizing: border-box;
            position: relative;
            text-align: right;
            .row_lable_text {
              padding-right: 20rpx;
              box-sizing: border-box;
            }
          }
          .row_lable ~ .row_value {
            flex: 1;
            font-size: 30rpx;
            color: #333;
            padding: 22rpx 30rpx;
            padding-left: 0;
            box-sizing: border-box;
            text-align: left;
          }
        }
      }
      .fiel_list {
        .file_item {
          text-decoration: none;
          color: #005bac;
          background-color: #fff;
          display: flex;
          align-items: center;
          font-size: 30rpx;
          .oa-icon {
            font-size: 40rpx;
            margin-right: 10rpx;
          }
        }
      }
      .nothing {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
        .img_content {
          width: 300rpx;
          height: 300rpx;
          .nothing_img {
            width: 100%;
            height: 100%;
          }
        }
        .tips_text {
          color: #666666;
        }
        .addBtn {
          padding: 10rpx 20rpx;
          margin-top: 30rpx;
          width: 200rpx;
        }
      }
      .option_list {
        background-color: #fff;
        .option_item {
          box-sizing: border-box;
          position: relative;
          width: 100%;
          padding: 22rpx 30rpx 22rpx 10rpx;
          display: flex;
          align-items: flex-start;
          &::after {
            position: absolute;
            z-index: 10;
            right: 0;
            left: 0;
            height: 1px;
            bottom: 0;
            content: '';
            -webkit-transform: scaleY(0.5);
            transform: scaleY(0.5);
            background-color: #eeeeee;
          }
          .option_item_icon {
            width: 80rpx;
            height: 80rpx;
            margin: 0 20rpx;
            border-radius: 100%;
            color: #ffffff;
            text-align: center;
            line-height: 2.8;
            background-color: #005bac;
            font-size: 28rpx;
          }
          .option_item_info {
            flex: 1;
            .option_item_approval_name {
              font-size: 32rpx;
              font-weight: bold;
              color: #333;
            }
            .option_item_top {
              display: flex;
              align-items: flex-end;
              .option_item_top_left {
                flex: 1;
                .option_item_name {
                  font-size: 28rpx;
                  color: #666;
                }
                .option_item_assent {
                  color: #3aad73;
                }
                .option_item_dissent {
                  color: #f59a23;
                }
                .option_item_response {
                  font-size: 28rpx;
                }
              }
              .option_item_time {
                font-size: 24rpx;
                color: #999;
              }
            }
            .option_item_type {
              font-size: 28rpx;
              color: #666;
            }
            .option_item_content {
              font-size: 28rpx;
              color: #333;
            }
          }
        }
      }
      .task_history_wrap {
        padding: 20rpx 30rpx 20rpx 40rpx;
        background-color: #fff;
      }
    }
    .theme-color {
      color: $theme-color !important;
    }
    .btn_wrap {
      background-color: #ffffff;
      box-shadow: 0 1px 6px #ddd;
      display: flex;
      align-items: center;
      justify-content: center;
      box-sizing: border-box;
      z-index: 10;
      .btn_item {
        height: 90rpx;
        flex: 1;
        box-sizing: border-box;
        text-align: center;
        font-size: 32rpx;
        position: relative;
        background-color: transparent;
        color: #666;
        display: flex;
        align-items: center;
        justify-content: center;
        &::after {
          border: 0;
          top: 20rpx;
          bottom: 20rpx;
          right: 0;
          left: unset;
          transform: scaleX(-0.5);
          width: 1px;
          height: unset;
          background-color: #ccc;
        }
        &:last-child::after {
          width: 0;
        }
        .oa-icon {
          margin: 0 6rpx;
          font-size: 36rpx;
        }
      }
    }
  }
}
</style>
