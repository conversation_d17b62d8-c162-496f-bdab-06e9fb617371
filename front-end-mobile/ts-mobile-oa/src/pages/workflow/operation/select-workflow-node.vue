<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="确认提交"></page-head>
    <view class="approval-info">
      <view class="single-next-node-item" v-if="nodetype == 'radio'">
        <view class="approval-row ts-flex">
          <view class="row-lable">
            <text class="required-red oa-icon oa-icon-asterisks"></text>
            <text class="row-lable_text">下步节点</text>
          </view>
          <view class="row-value row-value_input">
            <radio-group @change="nextStepRadioChange">
              <label
                class="next-step-radio"
                v-for="item in nextNodeList"
                :key="item.wfStepNo"
              >
                <radio
                  :value="item.wfStepNo"
                  :checked="item.wfStepNo === singleSelectedNode.wfStepNo"
                />
                {{ item.wfStepName }}
              </label>
            </radio-group>
          </view>
        </view>
        <view class="approval-row ts-flex" v-if="!isEnd">
          <view class="row-lable">
            <text class="row-lable_text">办理方式</text>
          </view>
          <view class="row-value row-value_input">
            <view
              style="font-size: 28rpx; color: #333;"
              v-html="singleSelectedNode.explainText"
            ></view>
          </view>
        </view>
        <view class="approval-row" v-if="!isEnd">
          <view class="row-lable" style="width: 100%;">
            <text class="required-red oa-icon oa-icon-asterisks"></text>
            <text class="row-lable_text">办理人</text>
            <text
              v-if="singleSelectedNode.lockApprover != '1'"
              class="add-icon oa-icon oa-icon-tianjiachaosong"
              @tap="chooseSingleNodeUsers"
            ></text>
          </view>
          <view class="row-value row-value_personal">
            <text class="personNameStr" @tap="chooseSingleNodeUsers">
              {{ singleSelectedNode.selectedUserList | personFilter }}
            </text>
            <uni-icons
              v-if="
                singleSelectedNode.selectedUserList.length > 0 &&
                  singleSelectedNode.lockApprover != '1'
              "
              :size="40"
              class="uni-icon-wrapper"
              color="#bbb"
              type="closeempty"
              @tap="emptySingleNodeUsers"
            />
          </view>
        </view>
      </view>
      <view class="multi-next-node-box" v-if="nodetype == 'checkbox'">
        <checkbox-group>
          <view
            class="multi-next-node-item"
            v-for="(nodeItem, index) in nextNodeList"
            :key="index"
          >
            <view class="approval-row ts-flex">
              <view class="row-lable">
                <text class="required-red oa-icon oa-icon-asterisks"></text>
                <text class="row-lable_text">下步节点</text>
              </view>
              <view class="row-value row-value_input">
                <checkbox
                  :value="nodeItem.wfStepNo"
                  :checked="true"
                  :disabled="true"
                />
                {{ nodeItem.wfStepName }}
              </view>
            </view>
            <view
              class="approval-row ts-flex"
              v-if="nodeItem['text'] != '结束'"
            >
              <view class="row-lable">
                <text class="row-lable_text">办理方式</text>
              </view>
              <view class="row-value row-value_input">
                <view
                  style="font-size: 28rpx; color: #333;"
                  v-html="
                    handleMultiInstanceExplainText(nodeItem.multiInstanceType)
                  "
                ></view>
              </view>
            </view>
            <view class="approval-row" v-if="nodeItem['text'] != '结束'">
              <view class="row-lable" style="width: 100%;">
                <!-- <text class="required-red oa-icon oa-icon-asterisks"></text> -->
                <text class="row-lable_text">办理人</text>
                <text
                  class="add-icon oa-icon oa-icon-tianjiachaosong"
                  @tap="chooseMultiNodeUsers(index)"
                ></text>
              </view>
              <view class="row-value row-value_personal">
                <text
                  class="personNameStr"
                  @tap="chooseMultiNodeUsers(index)"
                  >{{ nodeItem.selectedUserList | personFilter }}</text
                >
                <uni-icons
                  v-if="nodeItem.selectedUserList.length > 0"
                  :size="40"
                  class="uni-icon-wrapper"
                  color="#bbb"
                  type="closeempty"
                  @tap="emptyMultiNodeUsers(index)"
                />
              </view>
            </view>
          </view>
        </checkbox-group>
      </view>
      <view class="approval-row ts-flex" v-if="!isEnd">
        <view class="row-lable">
          <text class="row-lable_text">办理期限</text>
        </view>
        <view class="row-value row-value_input" @tap="showPicker">
          <input
            class="row-value_input_text"
            :disabled="true"
            type="text"
            :value="handleAllottedTime"
            placeholder="请选择办理期限"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="approval-row ts-flex">
        <view class="row-lable">
          <text class="row-lable_text">紧急程度</text>
        </view>
        <view class="row-value row-value_input" @tap="showPopup">
          <input
            class="row-value_input_text"
            :disabled="true"
            placeholder="请选择紧急程度"
            :value="urgency['text']"
          />
          <uni-icons
            :size="30"
            class="uni-icon-wrapper"
            color="#bbb"
            type="arrowright"
          />
        </view>
      </view>
      <view class="approval-row">
        <view class="row-lable">
          <text class="row-lable_text">办理提示</text>
        </view>
        <view class="row-value row-value_textarea">
          <textarea
            class="row-value_textarea_text"
            v-model="handleMarkedWords"
            placeholder="请输入提示内容"
          />
        </view>
      </view>
      <view class="approval-row">
        <view class="row-lable" style="width: 100%;">
          <text class="row-lable_text">抄送人</text>
          <text
            class="add-icon oa-icon oa-icon-tianjiachaosong"
            @tap="chooseCopyUsers"
          ></text>
        </view>
        <view class="row-value row-value_personal">
          <text class="personNameStr" @tap="chooseCopyUsers">{{
            copyUserList | personFilter
          }}</text>
          <uni-icons
            v-if="copyUserList.length > 0"
            :size="40"
            class="uni-icon-wrapper"
            color="#bbb"
            type="closeempty"
            @tap="emptyCopyUser"
          />
        </view>
      </view>
    </view>
    <view class="bottom-btn">
      <button class="btn-item uni-bg-blue" @tap="approvalSubmit">
        提交办理
      </button>
    </view>
    <date-picker
      ref="datePicker"
      endDate="2100-12-31 23:59"
      mode="date"
      fields="day"
      :value="handleAllottedTime"
      @confirm="onConfirmPicker"
    ></date-picker>
    <data-popup-select
      ref="popupSelect"
      :value="urgency.vulue"
      :selectList="urgencyList"
      fontSize="14"
      @change="popupSelectOnChange"
    ></data-popup-select>
  </view>
</template>

<script>
import { urgencyList, multiInstanceType } from '../mixins/dictionary.js';
import { handleMultiInstanceExplainText } from '../mixins/workflowFliter.js';

import datePicker from '@/components/picker/date-picker.vue';
export default {
  components: {
    datePicker
  },
  data() {
    return {
      workflowLabel: '',
      nextNodeList: [],
      formInfo: {},
      handleMarkedWords: '',
      handleAllottedTime: '',

      urgencyList,
      multiInstanceType,
      handleMultiInstanceExplainText,

      nodetype: 'radio',
      picker: {},
      singleSelectedNode: {
        explainText: '',
        wfStepNo: '',
        wfStepName: '',
        isAllUser: '',
        lockApprover: '0',
        userList: [],
        selectedUserList: []
      },
      urgency: {
        value: '',
        text: ''
      },
      copyUserList: [],
      isSubmit: false
    };
  },
  computed: {
    isEnd() {
      return this.singleSelectedNode['wfStepName'] == '结束';
    }
  },
  onLoad(opt) {
    if (opt.workflowLabel) this.workflowLabel = opt.workflowLabel;
    let nodeList = uni.getStorageSync('node_list');
    this.formInfo = uni.getStorageSync('form_info');
    if (nodeList.length) {
      let firstNode = nodeList[0];
      this.nodetype = this.multiInstanceType[firstNode.multiInstanceType][
        'inputType'
      ];
      if (this.nodetype == 'radio') {
        this.singleSelectedNode = this.handleNodeInfo(firstNode);
        this.nextNodeList = nodeList;
      } else {
        this.nextNodeList = nodeList.map(item => {
          item.userList = this.handleUserList(item.userList);
          if (item.isAllUser == 'Y') {
            item.selectedUserList = [];
          } else if (item.userList.length == 1 || item.approverFill == 1) {
            item.selectedUserList = item.userList;
          } else {
            item.selectedUserList = [];
          }
          return item;
        });
      }
    }
  },
  methods: {
    handleNodeInfo(node) {
      let userList = this.handleUserList(node.userList);
      let selectedUserList = [];
      if (userList.length === 1 || node.approverFill == 1) {
        selectedUserList = userList;
      }
      let nodeInfo = {
        explainText: this.handleMultiInstanceExplainText(
          node.multiInstanceType
        ),
        wfStepNo: node.wfStepNo,
        wfStepName: node.wfStepName,
        isAllUser: node.isAllUser,
        lockApprover: node.lockApprover || '0',
        userList,
        selectedUserList
      };
      return nodeInfo;
    },
    handleUserList(rows) {
      let list = [];
      rows.forEach(item => {
        list.push({
          id: item.usercode,
          name: item.username,
          empHeadImg: item.empHeadImg,
          empDeptName: item.deptname,
          empDutyName: item.dutyCode,
          sex: item.sex
        });
      });
      return list;
    },
    //显示时间弹出层
    showPicker() {
      this.$refs['datePicker'].show();
    },
    //时间选择确认
    onConfirmPicker(res) {
      this.handleAllottedTime = res.value;
    },
    //显示弹出层
    showPopup() {
      this.$refs['popupSelect'].open();
    },
    popupSelectOnChange(e) {
      this.urgency = e.detail.data;
    },
    //单节点切换
    nextStepRadioChange(e) {
      let node = this.nextNodeList.find(i => i.wfStepNo == e.detail.value);
      this.singleSelectedNode = this.handleNodeInfo(node);
    },
    //单节点选择办理人
    chooseSingleNodeUsers() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      if (this.singleSelectedNode.lockApprover == '1') return;
      uni.$on('personlist', res => {
        this.$set(this.singleSelectedNode, 'selectedUserList', res);
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('approval_person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(
        this.singleSelectedNode.selectedUserList,
        this.singleSelectedNode.userList
      );
    },
    //清空单节点办理人
    emptySingleNodeUsers() {
      this.singleSelectedNode.selectedUserList = [];
    },
    //多节点选择办理人
    chooseMultiNodeUsers(index) {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', res => {
        this.$set(this.nextNodeList[index], 'selectedUserList', res);
        uni.removeStorageSync('person_list');
        uni.removeStorageSync('approval_person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(
        this.nextNodeList[index].selectedUserList,
        this.nextNodeList[index].userList
      );
    },
    //清空多节点办理人
    emptyMultiNodeUsers(index) {
      this.$set(this.nextNodeList[index], 'selectedUserList', []);
    },
    //选择抄送人
    chooseCopyUsers() {
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('personlist', res => {
        this.copyUserList = res;
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('personlist');
      });
      this.jumpPageToSelectPerson(this.copyUserList);
    },
    jumpPageToSelectPerson(selectedUserList, userList) {
      uni.setStorageSync('person_list', JSON.stringify(selectedUserList));
      if (userList?.length) {
        uni.setStorageSync('approval_person_list', JSON.stringify(userList));
      }
      uni.navigateTo({
        url: `/pages/selectPerson/select-person?checkType=checkBox${
          userList?.length ? '&approval=true' : ''
        }`
      });
    },
    //清空抄送
    emptyCopyUser() {
      this.copyUserList = [];
    },
    //点击提交确定按钮
    approvalSubmit() {
      if (this.isSubmit) {
        return false;
      }
      this.isSubmit = true;
      let nextStepParam = {};
      if (this.nodetype == 'radio') {
        //单节点处理下个节点和下个办理人
        if (
          !this.isEnd &&
          this.singleSelectedNode.selectedUserList.length == 0
        ) {
          uni.showToast({ title: '请选择办理人', icon: 'none' });
          this.isSubmit = false;
          return false;
        } else {
          let nextStepUser = this.getUserInfo(
            this.singleSelectedNode.selectedUserList
          );
          nextStepParam = {
            users: nextStepUser.codeList.join(','),
            names: nextStepUser.nameList.join(','),
            wfStepId: this.singleSelectedNode.wfStepNo
          };
        }
      } else if (this.nodetype == 'checkbox') {
        let selectedNextStepList = [];
        this.nextNodeList.forEach(item => {
          if (item.selectedUserList.length) {
            let nextStepUser = this.getUserInfo(item.selectedUserList);
            selectedNextStepList.push({
              users: nextStepUser.codeList.join(','),
              names: nextStepUser.nameList.join(','),
              wfStepId: item.wfStepNo,
              wfDefinitionId: this.formInfo.wfDefinitionId,
              workflowNo: this.formInfo.workflowNo,
              taskId: this.formInfo.taskId
            });
          }
        });
        if (selectedNextStepList.length == 0) {
          uni.showToast({ title: '请选择办理人', icon: 'none' });
          this.isSubmit = false;
          return false;
        } else {
          nextStepParam = {
            nextTaskManyStepList: selectedNextStepList
          };
        }
      }
      nextStepParam = {
        ...nextStepParam,
        handleAllottedTime: this.handleAllottedTime,
        urgencyLevel: this.urgency.value,
        handleMarkedWords: this.handleMarkedWords
      };
      this.submitApply(nextStepParam);
    },
    //提交申请到服务器
    async submitApply(nextStepParam) {
      let copyUser = this.getUserInfo(this.copyUserList),
        data = {
          ...nextStepParam,
          copyToUsers: copyUser.codeList.join(','),
          copyToUserNames: copyUser.nameList.join(',')
        };
      this.formInfo = { ...this.formInfo, ...data };
      if (this.formInfo.dataMap.fieldSerialNumberRoleId != '') {
        let res = await this.ajax.saveSerialNo({
          fieldSerialNumberRoleId: this.formInfo.dataMap.fieldSerialNumberRoleId
        });
        if (res.success && res.object) {
          this.formInfo.dataMap[this.formInfo.dataMap.fieldSerialNumberProp] =
            res.object;
        }
      }
      delete this.formInfo.dataMap.fieldSerialNumberProp;
      delete this.formInfo.dataMap.fieldSerialNumberRoleId;
      this.ajax
        .startProcessInstance(this.formInfo)
        .then(res => {
          uni.showToast({ title: '提交成功!', icon: 'none' });
          uni.removeStorageSync('node_list');
          uni.removeStorageSync('form_info');
          this.$nextTick(() => {
            if (this.workflowLabel == 'yw') {
              uni.setStorageSync('tabIndex', 1);
              this.$parentTypeFun({
                type: 'jumpPage',
                path: `/ts-mobile-hrms/pages/qualification-authorization/index`
              });
            } else {
              uni.redirectTo({
                url: '/pages/workflow/my-workflow-list'
              });
            }
          });
        })
        .catch(e => {
          this.isSubmit = false;
        });
    },
    getUserInfo(row) {
      let user = {
        codeList: [],
        nameList: []
      };
      row.forEach(item => {
        user.codeList.push(item.id);
        user.nameList.push(item.name);
      });
      return user;
    },
    //返回上一层
    returnBack() {
      const pages = getCurrentPages(); //获取页面栈
      if (pages.length === 1) {
        //如果只有一个调用原生js
        history.back();
      } else {
        uni.navigateBack();
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 100%;
  .approval-info {
    flex: 1;
    .ts-flex {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
    }
    .multi-next-node-item {
      margin: 20rpx 0;
      /deep/ uni-checkbox .uni-checkbox-input.uni-checkbox-input-disabled {
        background-color: unset;
        &:before {
          color: unset;
        }
      }
    }
    .single-next-node-item {
      margin: 20rpx 0;
      /deep/ uni-radio .uni-radio-input {
        width: 18px;
        height: 18px;
      }
      .next-step-radio {
        display: inline-block;
        margin: 10rpx 0;
      }
    }
    .approval-row {
      position: relative;
      background-color: #ffffff;
      &::after {
        position: absolute;
        content: '';
        bottom: 0;
        left: 30rpx;
        right: 0;
        transform: scaleY(-0.5);
        height: 1px;
        background-color: #eee;
      }
      &:last-child {
        &::after {
          height: 0;
        }
      }
      .row-title {
        color: #333;
        margin: 30rpx 30rpx 20rpx;
      }
      .row-lable {
        width: 240rpx;
        padding: 22rpx 30rpx;
        box-sizing: border-box;
        position: relative;
        font-size: 28rpx;
        .required-red {
          color: #f00;
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          left: 4rpx;
          font-size: 24rpx;
        }
        .add-icon {
          font-size: 40rpx;
          padding: 0 30rpx;
          line-height: 1;
          color: #005bac;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
        }
        .file-add-icon {
          padding: 0 30rpx;
          font-size: 56rpx;
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          color: #bbb;
        }
        & ~ .row-value {
          flex: 1;
          font-size: 28rpx;
          color: #666;
          padding: 22rpx 30rpx;
          padding-left: 0;
          box-sizing: border-box;
          text-align: right;
        }
        & ~ .row-value_input {
          display: flex;
          justify-content: center;
          align-items: center;
          .row-value_input_text {
            text-align: right;
            flex: 1;
            font-size: 28rpx;
          }
        }
        & ~ .row-value_textarea {
          width: 100%;
          padding-left: 30rpx;
          padding-top: 0;
          text-align: left;
          .row-value_textarea_text {
            width: 100%;
            min-height: 160rpx;
            font-size: 28rpx;
          }
          .textarea-placeholder {
            color: #bbb;
          }
        }
        & ~ .row-value_personal {
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-sizing: border-box;
          text-align: left;
          padding: 0 30rpx 22rpx;
          .personNameStr {
            flex: 1;
          }
        }
      }
    }
  }
  .bottom-btn {
    background-color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 22rpx 30rpx;
    box-sizing: border-box;
    .btn-item {
      flex: 1;
      box-sizing: border-box;
      text-align: center;
      font-size: 32rpx;
      position: relative;
      border-radius: 10rpx;
    }
  }
  .scroll-list {
    max-height: 800rpx;
    overflow: auto;
  }
  .contact-list {
    background-color: #ffffff;
    .contact-item {
      padding: 22rpx 30rpx;
      font-size: 28rpx;
      color: #333333;
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        z-index: 10;
        right: 0;
        left: 30rpx;
        bottom: 0;
        height: 1px;
        content: '';
        -webkit-transform: scaleY(0.5);
        transform: scaleY(0.5);
        background-color: #eeeeee;
      }
      &:last-child::after {
        height: 0;
      }
      .contact-item-text {
        flex: 1;
        font-size: 28rpx;
        color: #333333;
      }
      .contact-item-icon {
        line-height: 1;
      }
    }
  }
}
</style>
