<template>
  <view
    class="data-list-item-info"
    @click.stop="() => clickLowerLevelFunc(data)"
  >
    <view class="left">
      <view class="organization-img-box">
        <img
          class="data-head-image"
          :src="require('@/static/img/organization-icon.png')"
        />
      </view>
    </view>
    <view class="right">
      <text class="data-name">{{ data.orgName }}</text>
      <text
        :class="{ 'data-operate': true }"
        @click.stop="() => clickLowerLevelFunc(data)"
      >
        下级
      </text>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    // 人员信息
    data: {
      type: Object,
      default: () => ({})
    },
    clickLowerLevelFunc: {
      type: Function,
      default: () => {}
    }
  }
};
</script>

<style lang="scss" scoped>
.data-list-item-info {
  display: flex;
  align-items: center;
  padding: 16rpx 32rpx;
  .left {
    .organization-img-box {
      width: $uni-img-size-lg;
      height: $uni-img-size-lg;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: $u-bg-color;
      .data-head-image {
        width: 48rpx;
        height: 48rpx;
        text-align: center;
        line-height: $uni-img-size-lg;
        font-size: $uni-font-size-base;
        color: $uni-text-color-inverse;
        &.sex-man {
          background-color: $sexman-color;
        }
        &.sex-woman {
          background-color: $sexwoman-color;
        }
      }
    }
  }
  .right {
    flex: 1;
    display: flex;
    justify-content: space-between;
    .data-name {
      font-size: $uni-font-size-base;
      color: $u-main-color;
    }
    .data-operate {
      font-size: $uni-font-size-base;
      color: $u-type-primary;
      &.disable {
        color: #e4e4e4;
      }
    }
  }
}
</style>
