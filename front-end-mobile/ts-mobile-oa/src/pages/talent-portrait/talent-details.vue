<template>
  <view class="talent-details">
    <page-head @clickLeft="returnBack" title="人才画像"></page-head>
    <view class="content-container">
      <view class="user-img-container">
        <view class="img-box">
          <image
            v-if="userInfo.avatar"
            :src="userInfo.avatar ? $config.BASE_HOST + userInfo.avatar : ''"
            mode="aspectFit"
            class="avata_img"
            @click="handleReviewAvatar(userInfo)"
          />
          <view
            v-else
            class="avata_img"
            :class="userInfo.genderText == '男' ? 'man' : 'woman'"
          ></view>
        </view>
        <view class="info-box">
          <text class="name-container">
            <text class="name">{{ userInfo.employeeName }}</text>
            <text class="name-status">{{ userInfo.employeeStatus }}</text>
          </text>
          <text>
            <text class="label">科室：</text>
            <text class="value">{{ userInfo.orgName || '' }}</text>
          </text>
          <text>
            <text class="label">岗位：</text>
            <text class="value">{{ userInfo.personalIdentityName || '' }}</text>
          </text>
        </view>
      </view>

      <u-tabs
        itemWidth="33%"
        :list="list"
        activeColor="#5260FF"
        :current="tabsAcitve"
        :fontSize="34"
        @change="handleChangeTabs"
      />

      <view v-show="tabsAcitve === 0">
        <view class="left-info-container">
          <view class="module-item basic-info">
            <text class="title">
              基础信息
            </text>

            <text class="info-item">
              <text class="label">性别：</text>
              <text class="value">{{ userInfo.genderText || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">年龄：</text>
              <text class="value">{{ userInfo.empAge || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">政治面貌：</text>
              <text class="value">
                {{ userInfo.politicalStatusText || '' }}
              </text>
            </text>
            <text class="info-item">
              <text class="label">最高学历：</text>
              <text class="value">{{ userInfo.zuigaoxueli || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">入院日期：</text>
              <text class="value">{{ entryDate || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">专业：</text>
              <text class="value">{{ userInfo.zhuanye || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">职务：</text>
              <text class="value">{{ userInfo.positionName || '' }}</text>
            </text>
            <text class="info-item">
              <text class="label">联系电话：</text>
              <text class="value">{{ userInfo.phoneNumber || '' }}</text>
            </text>
          </view>
          <view class="module-item annual-evaluation">
            <text class="title">
              年度考评
            </text>
            <view class="summary-table">
              <view
                class="item-row"
                v-for="(value, key, index) in ndkp"
                :key="index"
              >
                <view class="label">{{ key }}</view>
                <view class="value">{{ value || '-' }}</view>
              </view>
            </view>
          </view>
          <view class="module-item research-situation">
            <text class="title">
              科研论文
            </text>
            <view class="summary-table">
              <view
                class="item-row"
                v-for="(value, key, index) in kylw"
                :key="index"
              >
                <view class="label">{{ key }}</view>
                <view
                  class="value"
                  :class="{ details: value.size }"
                  @tap="chooseItem(value, 'kylw')"
                >
                  {{ value.size ? value.size + '篇' : '-' }}
                </view>
              </view>
            </view>
          </view>
          <view class="module-item rewards-punishments">
            <text class="title">
              奖惩情况
            </text>
            <view class="summary-table">
              <view
                class="item-row"
                v-for="(value, key, index) in jcqk"
                :key="index"
              >
                <view class="label">{{ key }}</view>
                <view
                  class="value"
                  :class="{ details: value.size }"
                  @tap="chooseItem(value, 'jcqk')"
                >
                  {{ value.size ? value.size + '篇' : '-' }}
                </view>
              </view>
            </view>
          </view>
          <view class="module-item train-continu-education">
            <text class="title">
              培训/进修情况
            </text>
            <view class="summary-table last">
              <view
                class="item-row"
                v-for="(value, key, index) in jxgp"
                :key="index"
              >
                <view class="label">{{ key }}</view>
                <view
                  class="value"
                  :class="{ details: value.size }"
                  @tap="chooseItem(value, key)"
                >
                  {{ value.size ? value.size + '项' : '-' }}
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view v-show="tabsAcitve === 1">
        <view
          class="resume-container"
          v-if="progressData && progressData.length > 0"
        >
          <view class="resume-item" v-for="(v, i) in progressData" :key="i">
            <view class="time">{{ v.firstTimeFormat }}</view>
            <view class="info">
              <view
                class="resume-title"
                v-for="(item, j) in v.fieldValues"
                :key="j"
              >
                {{ item }}
              </view>
              <view class="tips-round"></view>
            </view>
          </view>
          <view class="hospitalized">
            <view class="entry-date">{{ entryDate }}</view>
            <text class="icon">入院</text>
          </view>
        </view>

        <view v-else>
          <text>暂无信息</text>
        </view>
      </view>

      <view v-show="tabsAcitve === 2">
        <uni-collapse>
          <uni-collapse-item
            :title="item.groupName"
            v-for="item in showColumnGroupData"
            :key="item.id"
          >
            <view class="collapse-container">
              <template v-if="item.isDetailed == 1">
                <view
                  v-for="(fieldsItem, dataIndex) in item.dataList"
                  class="group-data-item"
                  :key="dataIndex"
                >
                  <view
                    v-if="item.dataList && item.dataList.length > 1"
                    class="group-item-title"
                    >第{{ dataIndex + 1 }}条
                  </view>

                  <fields-view :fields="fieldsItem" />
                </view>
              </template>

              <template v-else>
                <fields-view :fields="item.fields" />
              </template>
            </view>
          </uni-collapse-item>
        </uni-collapse>
      </view>
    </view>

    <uni-popup ref="popup" type="bottom" zIndex="9999">
      <view class="more-data-container">
        <view class="content">
          <view
            class="more-table-info-item"
            v-for="(item, index) in tableData"
            :key="index"
          >
            <view v-for="(k, v, i) in labels" :key="i">
              <text class="label">{{ k }}：</text>
              <text class="value" v-if="typeof item[v] === 'object'">
                <text v-for="file in item[v]" :key="file.id">
                  <view
                    class="file-item"
                    @tap="previewFile(file.id, file.originalName)"
                  >
                    {{ file.originalName }}
                  </view>
                </text>
              </text>
              <text class="value" v-else>{{ item[v] }}</text>
            </view>
          </view>
        </view>
        <view class="action-content">
          <view class="primary" @click="closePicker">关闭</view>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
import common from '@/common/js/common.js';
import Base64 from '@/common/js/base64.min.js';
import FieldsView from './components/fields-view';
import { cityDatas } from '@/common/js/cityData.js';
import { ImagePreview } from 'vant';

export default {
  components: {
    FieldsView
  },
  data() {
    return {
      userInfo: {},

      labels: {},

      formData: {},
      progressData: {},

      lineDeptData: [],
      tableData: [],
      groupData: [],
      showColumnGroupData: [],
      entryDate: '',
      ndkp: {},
      kylw: {},
      jcqk: {},
      jxgp: {},

      list: [{ name: '概况' }, { name: '院内履职' }, { name: '档案详情' }],
      tabsAcitve: 0
    };
  },
  watch: {
    tabsAcitve: {
      handler(val) {
        switch (val) {
          case 0:
            break;
          case 1:
            break;
        }
      },
      immediate: true
    }
  },
  async onLoad(data) {
    let { employeeId = '' } = data;
    if (employeeId) {
      // 获取自定义表单配置
      let _self = this;
      _self.handleSetGroupInfoAndDataInfo(employeeId);

      // 获取基础信息
      _self.ajax.customEmployeeBaseGrxxcjb(employeeId).then(res => {
        if (res.success) {
          let { entryDate = '' } = res.object;
          if (entryDate) {
            _self.entryDate = this.$dayjs(entryDate).format('YYYY-MM-DD');
          }

          _self.userInfo = res.object;
        }
      });

      // 获取年度统计
      _self.ajax.customEmployeeBaseRchx(employeeId).then(res => {
        if (res.success) {
          let { ndkp, kylw, jcqk, jxgp } = res.object;
          _self.ndkp = ndkp;
          _self.kylw = kylw;
          _self.jcqk = jcqk;
          _self.jxgp = jxgp;
        }
      });

      /**@desc 获取科室树数据 */
      this.ajax.getOneLineDeptListData().then(res => {
        if (res.object) {
          this.lineDeptData = res.object;
        }
      });
    }
  },
  methods: {
    handleReviewAvatar(row) {
      if (!row.avatar) return;
      ImagePreview({
        images: [row.avatar],
        closeable: true
      });
    },

    handleChangeTabs(val) {
      this.tabsAcitve = val;
    },
    async chooseItem(value, key) {
      let _self = this;
      if (typeof value === 'string') {
        return;
      }

      _self.tableData = common.deepClone(value.data);
      _self.labels = {};
      let dic = {
          kylw: '8',
          jcqk: 'jc',
          jx: 'jx',
          gp: 'gp',
          zyysgfhpx: '9'
        },
        groupItem = common.deepClone(_self.groupData).find(f => {
          let pengdingKey = key;
          if (key === '规范化培训') pengdingKey = 'zyysgfhpx';
          if (key === '进修') pengdingKey = 'jx';
          if (key === '规培') pengdingKey = 'gp';
          return f.id == dic[pengdingKey];
        }),
        groupFields = groupItem.fields,
        files = groupFields
          .filter(f => {
            _self.labels[f.fieldName] = f.showName;
            return f.fieldType === 'file';
          })
          .map(m => m.fieldName);

      // 是否存在 files Key
      for (let i = 0; i < _self.tableData.length; i++) {
        const fO = _self.tableData[i];

        for (const key in fO) {
          if (files.includes(key) && fO[key]) {
            let res = await _self.ajax.getFileAttachmentByBusinessId(fO[key]);
            if (res.success) fO[key] = res.object || [];
          }
        }
      }

      _self.$refs.popup.open();
    },
    async handleSetGroupInfoAndDataInfo(employeeId) {
      let _self = this;

      await _self.ajax
        .customEmployeeBaseFindDetailsById(employeeId, {
          groupId: 'all'
        })
        .then(res => {
          if (res.success) {
            _self.formData = res.object || {};
          }
        });

      await _self.ajax
        .customEmployeeGroupGetDetailList({
          groupId: 'all',
          archivesType: 'yw'
        })
        .then(async res => {
          if (res.success) {
            // 找到基础信息里的 头像 隐藏
            res.object
              .filter(f => f.id == '1')[0]
              .fields.forEach(e => {
                if (e.fieldName == 'avatar' && e.fieldType == 'img')
                  e.isHide = 1;
              });

            // 处理画像节点
            await _self.handleCustomData(res.object);

            _self.showColumnGroupData = common.deepClone(res.object) || [];
            _self.groupData = common.deepClone(res.object) || [];

            let groupIds = _self.showColumnGroupData.map(m => m.id);
            // 处理技术档案渲染
            if (groupIds.includes('jsda')) {
              await _self.ajax.getJsdaList(employeeId).then(async res => {
                if (res.success) {
                  if (res.object && res.object.length > 0) {
                    let workFlowArrs = res.object.map(item => {
                      return {
                        name: item.WORKFLOW_NAME,
                        date: item.WF_FINISHED_DATE,
                        wfInstanceId: item.WF_INSTANCE_ID,
                        wfDefinitionId: item.WF_DEFINITION_ID,
                        businessId: item.BUSINESS_ID,
                        workflowNo: item.WORKFLOW_NO
                      };
                    });

                    let jsdaItem =
                      _self.showColumnGroupData.find(
                        item => item.id === 'jsda'
                      ) || false;

                    if (jsdaItem) {
                      for (let i = 0; i < workFlowArrs.length; i++) {
                        const item = workFlowArrs[i];
                        let arr = [
                          {
                            showName: '类型',
                            value: item.name,
                            isHide: 0,
                            isAllowDeleted: 0
                          },
                          {
                            showName: '申请表内容',
                            value: '查看',
                            isHide: 0,
                            isAllowDeleted: 0,
                            config: {
                              style: {
                                color: '#06529F'
                              },
                              on: {
                                click: () => {
                                  let pagePramas = {
                                    isMobile: true,
                                    wfInstId: item.wfInstanceId,
                                    name: 'checkDetail'
                                  };
                                  uni.navigateTo({
                                    url: `/pages/workflow/my-workflow-detail?${this.$common.convertObj(
                                      pagePramas
                                    )}`
                                  });
                                }
                              }
                            }
                          },
                          {
                            showName: '审批办结日期',
                            value: item.date,
                            isHide: 0,
                            isAllowDeleted: 0
                          }
                        ];
                        jsdaItem.dataList.push(arr);
                      }
                    }
                  }
                }
              });
            }
          }
        });
    },
    // 处理自定义数据 移动端只展示 不修改 故码表类型 直接将值转换成 label;
    // 院内履职流程图 将需要展示的自定义分组、字段 分类出来 进行展示
    async handleCustomData(groupData) {
      let _self = this;

      for (var i = 0; i < groupData.length; i++) {
        var g = groupData[i];
        if (g.isDetailed == '1') {
          var d = _self.formData[g.id] || [];
          g.dataList = [];
          for (var x = 0; x < d.length; x++) {
            var f = common.deepClone(g.fields);
            for (var z = 0; z < f.length; z++) {
              f[z].value = d[x][f[z].fieldName];
            }
            g.dataList.push(f);
          }
        } else {
          var d = (_self.formData[g.id] && _self.formData[g.id][0]) || {};
          for (var x = 0; x < g.fields.length; x++) {
            g.fields[x].value = d[g.fields[x].fieldName];
          }
        }
      }
      for (let i = 0; i < groupData.length; i++) {
        const item = groupData[i];
        if (
          item.isDetailed == '1' &&
          item.dataList &&
          item.dataList.length > 0
        ) {
          for (let j = 0; j < item.dataList.length; j++) {
            const childItem = item.dataList[j];
            await _self.handleCustomSelectDicFile(childItem);
          }
        } else if (
          item.isDetailed == '0' &&
          item.fields &&
          item.fields.length > 0
        ) {
          await _self.handleCustomSelectDicFile(item.fields);
        }
      }

      let deepGroupData = common.deepClone(groupData);
      let progressData = deepGroupData
        .filter(
          f =>
            f.isDetailed == 1 &&
            f.showPassBy == 1 &&
            f.dataList &&
            f.dataList.length > 0
        )
        .flatMap(m => m.dataList)
        .map(p => p.filter(c => c.showPassByVal == 1 || c.showPassByDate == 1))
        .filter(f => {
          return f && f.length > 0;
        });

      let arr = [];
      for (let i = 0; i < progressData.length; i++) {
        let item = progressData[i];
        // 只找第一个展示时间
        let { value: firstTime = '' } =
            item.find(fd => fd.showPassByDate == 1) || {},
          firstTimeFormat = this.$dayjs(firstTime).format('YYYY-MM-DD');

        // 处理分组名称
        let groupId = item[0].groupId;
        let fitem = groupData.find(f => f.id === groupId) || {};
        let groupName = fitem.groupName;

        if (groupId.includes(',') && groupId == 'jx,gp,hy') {
          let { value: fValue = '' } =
            item.find(({ fieldName }) => fieldName == 'out_type') || {};
          groupName = fValue;
        }

        // 处理码表类型字段
        let fieldItem = item.filter(fl => fl.showPassByVal == 1);
        fieldItem.unshift({ progressLabel: groupName });

        arr.push({
          firstTimeFormat,
          fieldValues: fieldItem.map(cm => cm.progressLabel)
        });
      }

      // 过滤 展示时间字段 格式不正确的
      arr = arr.filter(f => f.firstTimeFormat != 'Invalid Date');

      // 排序
      arr = sortData(arr, 'firstTimeFormat');

      arr.reverse();
      _self.progressData = arr;

      function sortData(data, key) {
        data.map(item => {
          item._tm = item[key].replace(/-/g, '') * 1;
        });
        return data.sort((a, b) => a._tm - b._tm);
      }
    },
    async handleCustomSelectDicFile(arr) {
      for (let i = 0; i < arr.length; i++) {
        const f = arr[i];

        let _self = this;
        // 处理正常码表
        if (f.fieldType == 'select' && f.dataSource == 4 && f.dictSource) {
          let res = await _self.ajax.getDicApi(f.dictSource);
          if (res.success) {
            let { itemName = '' } =
              res.object.find(
                ({ itemNameValue }) => itemNameValue == f.value
              ) || {};
            f.value = itemName;
          }
        } else if (
          ['jobtitle_name', 'jobtitle_level', 'jobtitle_category'].includes(
            f.fieldName
          )
        ) {
          // 处理特殊 tree
          let res = await _self.ajax.jobtitleBasicGet({
            jobtitleBasicId: f.value
          });
          if (res.success) {
            f.value = res.object.jobtitleBasicName || '-';
          }
        } else if (f.fieldType == 'file' && f.value) {
          let res = await _self.ajax.getFileAttachmentByBusinessId(f.value);
          if (res.success) {
            f.value = res.object || [];
          }
        } else if (f.fieldType == 'address' && f.value) {
          let valueList = f.value.split('-'),
            data = cityDatas;
          f.value = valueList
            .map(key => {
              let cityData = data.find(item => item.value == key) || {};
              if (cityData.children) {
                data = cityData.children;
              }
              return cityData.label || '';
            })
            .filter(item => item)
            .join('-');
        } else if (f.fieldType == 'deptChose' && f.value) {
          /**@desc 科室数据匹配 */
          let find =
            this.lineDeptData.find(dept => dept.organizationId == f.value) ||
            {};
          f.value = find.name || '';
        } else if (
          ['concurrent_position', 'position_id'].includes(f.fieldName) &&
          f.value
        ) {
          this.ajax
            .getUserInfoEditPartTimePositionData({
              pageNo: 1,
              pageSize: 9999
            })
            .then(res => {
              if (res.success == false) {
                return;
              }
              let find = res.rows.find(i => i.positionId == f.value) || {};
              f.value = find.positionName || '';
            });
        }

        f.progressLabel = f.showName + '：' + f.value;
      }
    },
    // 预览
    previewFile(id, fileName) {
      let filePath = `${this.$config.DOCUMENT_BASE_HOST}/ts-document/attachment/downloadFile/${id}?fullfilename=${fileName}&source=mobile`;
      if (this.$config.ENABLE_FILE_PREVIEW) {
        uni.navigateTo({
          url: `/pages/webview/webview?url=${
            this.$config.BASE_HOST
          }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
        });
      }
    },
    closePicker() {
      this.$refs.popup.close();
    },
    //返回上一层
    returnBack() {
      uni.navigateBack();
    }
  }
};
</script>

<style lang="scss" scoped>
.talent-details {
  background: #fff;
  height: 100vh;
  overflow: auto;
  .label {
  }
  .value {
    color: rgba(0, 0, 0, 0.75);
    // font-weight: 600;
  }
  .file-item {
    color: $theme-color;
  }

  ::v-deep {
    .uni-collapse-cell__title-text {
      font-size: 16px;
      font-weight: 800;
    }
    .uni-collapse-cell__title {
      padding: 0 !important;
      height: 32px;
      &:active {
        background: #fff !important;
      }
    }
    .uni-collapse-cell--hide {
      height: 34px;
    }
    .uni-collapse-cell--open {
      background: #fff;
    }

    .collapse-container {
      display: flex;
      flex-direction: column;

      .group-data-item {
        padding: 8px 0;
        .group-item-title {
          font-size: 12px;
          margin-bottom: 4px;
          color: $theme-web-color;
        }
        &:last-child {
          .fields-item {
            &:last-child {
              border-bottom: 1px solid transparent;
            }
          }
        }
      }
    }
  }

  .more-data-container {
    height: 80vh;
    width: 100vw;
    background-color: #fff;
    padding: 16px;
    overflow: auto;
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      overflow: auto;
      .more-table-info-item {
        border: 1px solid #eee;
        padding: $uni-spacing-col-base;
        border-radius: $uni-border-radius-lg;
        margin-bottom: $uni-spacing-col-base;
      }
    }
    .action-content {
      display: flex;
      justify-content: center;
      height: 38px;
      view {
        width: 180px;
        border: 1px solid #eee;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 38px;
        &.primary {
          color: #fff;
          background-color: $theme-color;
          border-color: $theme-color;
        }
      }
    }
  }
  .content-container {
    padding: 0 16px;

    .user-img-container {
      display: flex;
      padding: $uni-spacing-col-base 0;
      .img-box {
        margin-right: $uni-spacing-col-base;
        .avata_img {
          width: 180rpx;
          height: 180rpx;
          background: url(../../static/img/avata.png) center no-repeat;
          background-size: cover;
          border-radius: 50%;
          margin-right: 8px;
          &.man {
            background-color: #5d9ce5;
          }
          &.woman {
            background-color: #ed74aa;
          }
        }
      }

      .info-box {
        display: flex;
        flex-direction: column;
        .name-container {
          margin-bottom: $uni-spacing-col-sm;
          .name {
            font-weight: 800;
            font-size: 20px;
          }
          .name-status {
            margin-left: $uni-spacing-col-base;
            color: #fff;
            background-color: $theme-web-color;
            padding: 8rpx 16rpx;
            border-radius: $uni-border-radius-lg;
            font-weight: 400;
            font-size: 24rpx;
          }
        }
      }
    }

    .left-info-container {
      display: flex;
      flex-direction: column;
      .module-item {
        display: flex;
        flex-direction: column;
        .title {
          font-size: 18px;
          color: rgba(0, 0, 0, 0.75);
          margin-bottom: $uni-spacing-col-base;
          font-weight: 800;
          padding-left: 12px;
          position: relative;
          &::after {
            position: absolute;
            content: '';
            width: 5px;
            height: 20px;
            left: 0;
            top: 4px;
            border-radius: 4px;
            background: $theme-web-color;
          }
        }
        .info-item {
          margin-bottom: $uni-spacing-col-base;
          .label,
          .value {
            font-size: 16px;
          }
        }
        .summary-table {
          width: 100%;
          display: flex;
          flex-wrap: wrap;
          border: 1px solid #eee;
          margin-bottom: $uni-spacing-col-base;
          box-sizing: border-box;
          &.last {
            .item-row {
              flex: 1;
            }
          }
          .item-row {
            width: 33.33%;
          }
          .label {
            background-color: #eee;
            line-height: 34px;
            text-align: center;
          }
          .value {
            background-color: #fff;
            line-height: 34px;
            text-align: center;
            &.details {
              color: $theme-web-color;
              cursor: pointer;
            }
          }
        }
      }
    }

    .resume-container {
      padding-bottom: 8px;
      .hospitalized {
        display: flex;
        flex-direction: column;
        padding-left: 200rpx;
        .entry-date {
          margin-bottom: 4px;
          font-size: 12px;
        }
        .icon {
          border-radius: 25px;
          width: 50px;
          padding: 2px 8px;
          border: 1px solid #d8d8d8;
          background-color: #fff;
          color: rgba(0, 0, 0, 0.65);
          text-align: center;
          font-size: 12px;
        }
      }
      .resume-item {
        display: flex;
        .time {
          width: 200rpx;
        }

        .info {
          flex: 1;
          display: flex;
          flex-direction: column;
          border-left: 2px solid #2670fa;
          position: relative;
          padding-left: 16px;
          .children-item {
            font-size: 14px;
            line-height: 22px;
            margin-bottom: 4px;
          }
          .tips-round {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #2670fa;
            position: absolute;
            left: -5px;
            top: 8px;
          }
        }

        .resume-title {
          font-size: 16px;
          color: #333;
          margin-bottom: 8px;
          &:first-child {
            font-weight: 700;
          }
        }
      }
    }
  }
}
</style>
