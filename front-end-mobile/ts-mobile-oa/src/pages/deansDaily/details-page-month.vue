<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" leftText="月报" />

    <view class="content-container">
      <component-head
        pageSubjectTitle="领导月报"
        :dailyBeginDate="dailyBeginDate"
      />

      <template
        v-if="personnelDirectionList.length || abnormalPersonList.length"
      >
        <view class="module-title">人员动态</view>
        <component-person-total-module
          :index="2"
          :paramsDate="paramsDate"
          :onTheJobTotal="onTheJobTotal"
          :onThePostTotal="onThePostTotal"
          :personnelDirectionTotal="personnelDirectionTotal"
          :personnelDirectionList="personnelDirectionList"
          :abnormalPersonTotal="abnormalPersonTotal"
          :abnormalPersonList="abnormalPersonList"
        />
      </template>

      <view class="module-title">收入情况</view>
      <view class="income-situation">
        <view class="table-container">
          <view class="table-head">
            <view class="head-item">指标</view>
            <view class="head-item">金额(元)</view>
            <view class="head-item">同比</view>
          </view>
          <view class="table-body">
            <view
              class="body-row"
              v-for="(item, i) in incomesituationData"
              :key="`income${i}`"
            >
              <view class="body-item center">{{ item.label }}</view>
              <view class="body-item right num-color">{{ item.value }}</view>
              <view class="body-item right">
                <text class="font-zf-icon" :style="`color: ${item.color};`">
                  {{ item.zfFont }}
                </text>
                <text>{{ item.ZF }}</text>
              </view>
            </view>
          </view>
        </view>

        <view class="echarts-label">
          近七周收入趋势(万元)
        </view>
        <view class="income-charts-box">
          <qiun-data-charts
            type="column"
            :chartData="weekIncomeChartsData"
            :loadingType="1"
            :disableScroll="true"
            canvasId="income-charts"
            :canvas2d="true"
            background="none"
            :animation="false"
            :ontouch="true"
            :echartsH5="true"
            :echartsApp="false"
            :eopts="weekIncomeChartsOpts"
            :opts="weekIncomeChartsOpts"
          />
        </view>

        <view class="echarts-label">
          门诊和住院收入占比
        </view>
        <view class="proportion-outpatient-inpatient-income-box">
          <view class="echarts-container">
            <qiun-data-charts
              type="ring"
              :chartData="proportionOutpatientInpatientIncomeMzData"
              :loadingType="1"
              :disableScroll="true"
              canvasId="proportion-outpatient-inpatient-income-mz-charts"
              :canvas2d="true"
              background="none"
              :animation="true"
              :ontouch="true"
              :opts="proportionOutpatientInpatientIncomeMzOpts"
              :eopts="proportionOutpatientInpatientIncomeMzOpts"
            />
          </view>
          <view class="list-container">
            <view
              class="list-item"
              v-for="item in proportionOutpatientInpatientIncomeMzList"
              :key="item.key"
            >
              <text :class="{ title: true, [item.className]: true }">
                {{ item.label }}
              </text>
              <text class="value">{{ item.value }}</text>
              <text class="percentage">{{ item.ZF }}</text>
            </view>
          </view>
        </view>
        <view class="proportion-outpatient-inpatient-income-box">
          <view class="echarts-container">
            <qiun-data-charts
              type="ring"
              :chartData="proportionOutpatientInpatientIncomeZyData"
              :loadingType="1"
              :disableScroll="true"
              canvasId="proportion-outpatient-inpatient-income-zy-charts"
              :canvas2d="true"
              background="none"
              :animation="true"
              :ontouch="true"
              :opts="proportionOutpatientInpatientIncomeZyOpts"
              :eopts="proportionOutpatientInpatientIncomeZyOpts"
            />
          </view>
          <view class="list-container">
            <view
              class="list-item"
              v-for="item in proportionOutpatientInpatientIncomeZyList"
              :key="item.key"
            >
              <text :class="{ title: true, [item.className]: true }">
                {{ item.label }}
              </text>
              <text class="value">{{ item.value }}</text>
              <text class="percentage">{{ item.ZF }}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="module-title">门诊和住院科室收入前五</view>
      <view class="mz-zy-container-top5">
        <view class="table-container">
          <view class="table-head">
            <view class="head-item">门诊科室</view>
            <view class="head-item">金额(元)</view>
            <view class="head-item">同比</view>
          </view>
          <view class="table-body">
            <view
              class="body-row"
              v-for="(item, i) in mzTopFive"
              :key="`mz${i}`"
            >
              <view class="body-item center">{{ item.label }}</view>
              <view class="body-item right num-color">{{ item.value }}</view>
              <view class="body-item right">
                <text class="font-zf-icon" :style="`color: ${item.color};`">
                  {{ item.zfFont }}
                </text>
                <text>{{ item.ZF }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="mz-zy-container-top5">
        <view class="table-container">
          <view class="table-head">
            <view class="head-item">住院科室</view>
            <view class="head-item">金额(元)</view>
            <view class="head-item">同比</view>
          </view>
          <view class="table-body">
            <view
              class="body-row"
              v-for="(item, i) in zyTopFive"
              :key="`zy${i}`"
            >
              <view class="body-item center">{{ item.label }}</view>
              <view class="body-item right num-color">{{ item.value }}</view>
              <view class="body-item right">
                <text class="font-zf-icon" :style="`color: ${item.color};`">
                  {{ item.zfFont }}
                </text>
                <text>{{ item.ZF }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="module-title">门诊与住院情况</view>
      <view class="mz-zy-container-top5">
        <view class="table-container">
          <view class="table-head">
            <view class="head-item">指标</view>
            <view class="head-item">人次</view>
            <view class="head-item">同比</view>
          </view>
          <view class="table-body">
            <view
              class="body-row"
              v-for="(item, i) in mzSituationList"
              :key="`mzSituation${i}`"
            >
              <view class="body-item center">{{ item.label }}</view>
              <view class="body-item center num-color">{{ item.value }}</view>
              <view class="body-item right">
                <text class="font-zf-icon" :style="`color: ${item.color};`">
                  {{ item.zfFont }}
                </text>
                <text>{{ item.ZF }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <view class="mz-zy-container-top5">
        <view class="table-container">
          <view class="table-head">
            <view class="head-item">指标</view>
            <view class="head-item">人次</view>
            <view class="head-item">同比</view>
          </view>
          <view class="table-body">
            <view
              class="body-row"
              v-for="(item, i) in zySituationList"
              :key="`zySituation${i}`"
            >
              <view class="body-item center">{{ item.label }}</view>
              <view
                class="body-item center num-color"
                :style="`color: ${item.valColor};`"
              >
                {{ item.value }}
              </view>
              <view class="body-item right">
                <text class="font-zf-icon" :style="`color: ${item.color};`">
                  {{ item.zfFont }}
                </text>
                <text>{{ item.ZF }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="module-title">住院科室业务数据</view>
      <view class="inpatient-department-business-data">
        <fixed-table-dept
          :data="deptListData"
          :header="columns"
          :fixed="true"
          :fixedFirstAndSecond="true"
          :border="true"
          :stripe="true"
          :showActions="true"
        />
      </view>
    </view>
  </view>
</template>

<script>
import moment from 'moment';

import common from '@/common/js/common.js';
import detailsPageWeekMonthMixin from './mixin/details-page-week-month-mixin.js';
import ComponentHead from './components/component-head.vue';
import ComponentPersonTotalModule from './components/component-person-total-module.vue';
import QiunDataCharts from '@/uni_modules/qiun-data-charts/components/qiun-data-charts/qiun-data-charts.vue';
import FixedTableDept from './components/fixed-table-dept.vue';

export default {
  components: {
    ComponentHead,
    ComponentPersonTotalModule,
    FixedTableDept,
    QiunDataCharts
  },
  mixins: [detailsPageWeekMonthMixin],
  data() {
    return {
      tabIndex: undefined,
      dailyBeginDate: undefined,

      id: undefined,
      onTheJobTotal: 0,
      onThePostTotal: 0,
      personnelDirectionTotal: 0,
      personnelDirectionList: [],
      abnormalPersonTotal: 0,
      abnormalPersonList: [],

      mzTopFive: [],
      zyTopFive: [],
      incomesituationData: [],
      mzSituationList: [],
      zySituationList: [],

      paramsDate: {},
      detailsObject: {},

      columns: [
        {
          prop: 'index',
          align: 'center',
          title: '序'
        },
        {
          prop: 'dept',
          title: '科室',
          align: 'left',
          overflow: true
        },
        {
          prop: 'zy_ryrc',
          title: '入院',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_cyrc',
          title: '出院',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_zyrc',
          title: '日均在院',
          align: 'center',
          width: 150
        },
        {
          prop: 'zy_bzrc',
          title: '日均病重',
          align: 'center',
          width: 150
        },
        {
          prop: 'zy_bwrc',
          title: '日均病危',
          align: 'center',
          width: 150
        },
        {
          prop: 'kcs',
          title: '余床数',
          align: 'center',
          width: 120,
          formatter: (val, row) => {
            let formatterVal = (row.bzcwsyl * 100).toFixed(2);
            const numericVal = parseFloat(formatterVal);
            if (numericVal >= 100) {
              return `<text style="color: red">${val}</text>`;
            }
            if (numericVal == 0) {
              return val;
            }
            if (numericVal < 50) {
              return `<text style="color: #53CBB5">${val}</text>`;
            }
            return val;
          }
        },
        {
          prop: 'bzcwsyl',
          title: '床位使用率',
          align: 'center',
          width: 200,
          formatter: (val, row) => {
            let formatterVal = (row.bzcwsyl * 100).toFixed(2);
            const numericVal = parseFloat(formatterVal);

            if (numericVal >= 100) {
              return `<text style="color: red">${formatterVal}%</text>`;
            }
            if (numericVal == 0) {
              return `${formatterVal}%`;
            }
            if (numericVal < 50) {
              return `<text style="color: #53CBB5">${formatterVal}%</text>`;
            }
            return `${formatterVal}%`;
          }
        },
        {
          prop: 'cws',
          align: 'center',
          title: '床位数',
          width: 120
        },
        {
          prop: 'zy_ssrc',
          title: '手术人次',
          align: 'center',
          width: 150
        },
        {
          prop: 'zy_yjssrc',
          title: '一级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_ejssrc',
          title: '二级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_sanjssrc',
          title: '三级',
          align: 'center',
          width: 100
        },
        {
          prop: 'zy_sijssrc',
          title: '四级',
          align: 'center',
          width: 100
        }
      ],
      deptListData: []
    };
  },

  async onLoad(opt) {
    let _self = this;
    _self.tabIndex = opt.index;
    _self.id = opt.id;
    let details = await _self.handleGetLeaderDailyDetails(_self.id);
    if (!details) {
      return;
    }
    _self.ajax.LeaderDailyReadClickSaveUpdate({ dailyId: opt.id });

    let { dailyBeginDate, dailyEndDate } = details;
    let endF = moment(dailyEndDate).format('MM-DD');
    endF = endF.replace('-', '月');
    endF = endF + '日';
    _self.dailyBeginDate = `${common.formatDateText(dailyBeginDate)}-${endF}`;

    _self.paramsDate = {
      start_date: dailyBeginDate,
      end_date: dailyEndDate
    };

    await _self.handleGetHrindexGetDayDyna();
    _self.handleGetHrindexGetPsnTrns();
    _self.handleGetHrindexGetPsnDstn();

    _self.handleGetLeaderDailyGetIncomeTrendByDate();
    _self.handleGetLeaderDailyGetDeptZbPageTyMz();
    _self.handleGetLeaderDailyGetDeptZbPageTyZy();
    _self.handleGetLeaderDailyGetLeaderweeklyReport();
  },

  filters: {
    formatNumber(value) {
      if (!value) return '0';
      const number = parseFloat(value);
      if (isNaN(number)) return value;
      return number.toLocaleString('en-US');
    }
  },

  methods: {
    async handleGetLeaderDailyDetails(id) {
      let _self = this;
      const res = await _self.ajax.LeaderDailyDetails(id);
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取详情失败,请联系管理员'
        });
        return false;
      }
      return res.object;
    },

    async handleGetHrindexGetDayDyna() {
      let _self = this;
      const res = await _self.ajax.hrindexGetDayDyna({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }

      _self.onTheJobTotal = Number(res.object?.total_cnt || 0);
    },

    async handleGetHrindexGetPsnTrns() {
      let _self = this;
      const res = await _self.ajax.hrindexGetPsnTrns({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }
      let list = res.object.dataList || [];
      let data = list.map(m => {
        return {
          name: m.cause,
          value: Number(m.cnt)
        };
      });
      data.sort((a, b) => b.value - a.value);
      // 人员去向
      _self.abnormalPersonTotal = data.reduce(
        (sum, item) => sum + item.value,
        0
      );
      data = data.filter(f => f.value > 0);
      _self.abnormalPersonList = data;
    },

    async handleGetHrindexGetPsnDstn() {
      let _self = this;
      const res = await _self.ajax.hrindexGetPsnDstn({
        type: 5,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }

      let list = res.object.dataList || [];
      let data = list.map(m => {
        return {
          name: m.leave_type,
          type: m.type,
          value: Number(m.cnt)
        };
      });
      data.sort((a, b) => b.value - a.value);
      // 人员去向
      _self.personnelDirectionTotal = data.reduce(
        (sum, item) => sum + item.value,
        0
      );
      // 在岗人员
      _self.onThePostTotal =
        _self.onTheJobTotal - _self.personnelDirectionTotal;
      data = data.filter(f => f.value > 0);
      _self.personnelDirectionList = data;
    },

    async handleGetLeaderDailyGetIncomeTrendByDate() {
      let _self = this;
      const res = await _self.ajax.LeaderDailyGetIncomeTrendByDate({
        lx: 2,
        ..._self.paramsDate
      });
      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }
      const data = res.object || [];
      const categories = data.map(m => {
        const beg = m.begdate.slice(8);
        const end = m.enddate.slice(8);
        return `${beg}-${end}`;
      });

      _self.weekIncomeChartsData.categories = categories;
      _self.weekIncomeChartsData.series[0]['data'] = data.map(m => m.sr_tq);
      _self.weekIncomeChartsData.series[1]['data'] = data.map(m => m.sr_bq);
    },

    async handleGetLeaderDailyGetDeptZbPageTyMz() {
      let _self = this;
      let result = await _self.handleGetLeaderDailyGetDeptZbPageTy('0105');
      if (Array.isArray(result)) {
        _self.mzTopFive = _self.handleFormatterTableData(
          result,
          'DEPT',
          'BQ_PVALUE',
          'ZF'
        );
      }
    },

    async handleGetLeaderDailyGetDeptZbPageTyZy() {
      let _self = this;
      let result = await _self.handleGetLeaderDailyGetDeptZbPageTy('0205');
      if (Array.isArray(result)) {
        _self.zyTopFive = _self.handleFormatterTableData(
          result,
          'DEPT',
          'BQ_PVALUE',
          'ZF'
        );
      }
    },

    async handleGetLeaderDailyGetDeptZbPageTy(zbcode) {
      let _self = this;
      const res = await _self.ajax.LeaderDailyGetDeptZbPageTy({
        ..._self.paramsDate,
        pageNo: 1,
        pageSize: 5,
        zbcode
      });
      return res.rows;
    },

    handleFormatterTableData(list, labelKey, valueKey, zfKey) {
      let _self = this;
      const zfMapping = [
        { font: '↑', color: 'lightgreen' }, // zfKey > 0
        { font: '→', color: 'dodgerblue' }, // zfKey == 0
        { font: '↓', color: 'red' } // zfKey < 0
      ];

      return list.map(item => {
        const zfIndex = item[zfKey] > 0 ? 0 : item[zfKey] == 0 ? 1 : 2;
        const { font, color } = zfMapping[zfIndex];

        return {
          ...item,
          label: item[labelKey],
          value: _self.$options.filters.formatNumber(item[valueKey]),
          zfFont: font,
          color: color,
          ZF: `${((item[zfKey] || 0) * 100).toFixed(2)}%`
        };
      });
    },

    async handleGetLeaderDailyGetLeaderweeklyReport() {
      let _self = this;
      const res = await _self.ajax.LeaderDailyGetLeaderweeklyReport({
        ..._self.paramsDate
      });

      if (!res.success) {
        uni.showToast({
          icon: 'none',
          title: res.message || '获取数据失败,请联系管理员'
        });
        return false;
      }
      let allSingleZbDataTqdb = res.object?.allSingleZbDataTqdb || {};
      let incomesituationData = [
        {
          label: '总收入',
          value: allSingleZbDataTqdb?.hj_zsr || 0,
          tq: allSingleZbDataTqdb?.hj_zsr_tqzf || 0
        },
        {
          label: '门急诊收入',
          value: allSingleZbDataTqdb?.mz_zsr || 0,
          tq: allSingleZbDataTqdb?.mz_zsr_tqzf || 0
        },
        {
          label: '住院收入',
          value: allSingleZbDataTqdb?.zy_zsr || 0,
          tq: allSingleZbDataTqdb?.zy_zsr_tqzf || 0
        },
        {
          label: '体检收入',
          value: allSingleZbDataTqdb?.hj_tjsr || 0,
          tq: allSingleZbDataTqdb?.hj_tjsr_tqzf || 0
        },
        {
          label: '门诊均次费用',
          value: allSingleZbDataTqdb?.mz_jcfy || 0,
          tq: allSingleZbDataTqdb?.mz_jcfy_tqzf || 0
        },
        {
          label: '住院均次费用',
          value: allSingleZbDataTqdb?.zy_jcfy || 0,
          tq: allSingleZbDataTqdb?.zy_jcfy_tqzf || 0
        }
      ];
      _self.incomesituationData = _self.handleFormatterTableData(
        incomesituationData,
        'label',
        'value',
        'tq'
      );
      let mzSituationList = [
        {
          label: '门急诊人次',
          value: allSingleZbDataTqdb?.mz_mjzrc || 0,
          tq: allSingleZbDataTqdb?.mz_mjzrc_tqzf || 0
        },
        {
          label: '门诊人次',
          value: allSingleZbDataTqdb?.mz_mzrc || 0,
          tq: allSingleZbDataTqdb?.mz_mzrc_tqzf || 0
        },
        {
          label: '急诊人次',
          value: allSingleZbDataTqdb?.mz_jzrc || 0,
          tq: allSingleZbDataTqdb?.mz_jzrc_tqzf || 0
        },
        {
          label: '预约就诊人次',
          value: allSingleZbDataTqdb?.mz_yyrc || 0,
          tq: allSingleZbDataTqdb?.mz_yyrc_tqzf || 0
        },
        {
          label: '预约就诊率',
          value: allSingleZbDataTqdb?.mz_yyjzl || 0,
          tq: allSingleZbDataTqdb?.mz_yyjzl_tqzf || 0
        }
      ];
      _self.mzSituationList = _self.handleFormatterTableData(
        mzSituationList,
        'label',
        'value',
        'tq'
      );
      // 预约就诊率 特殊处理
      let find = _self.mzSituationList.find(f => f.label == '预约就诊率');
      find.value =
        ((allSingleZbDataTqdb?.mz_yyjzl || 0) * 100).toFixed(2) + '%';

      let zySituationList = [
        {
          label: '日均在院人次',
          value: allSingleZbDataTqdb?.zy_zyrc || 0,
          tq: allSingleZbDataTqdb?.zy_zyrc_tqzf || 0
        },
        {
          label: '入院人次',
          value: allSingleZbDataTqdb?.zy_ryrc || 0,
          tq: allSingleZbDataTqdb?.zy_ryrc_tqzf || 0
        },
        {
          label: '出院人次',
          value: allSingleZbDataTqdb?.zy_cyrc || 0,
          tq: allSingleZbDataTqdb?.zy_cyrc_tqzf || 0
        },
        {
          label: '手术人次',
          value: allSingleZbDataTqdb?.zy_ssrc || 0,
          tq: allSingleZbDataTqdb?.zy_ssrc_tqzf || 0
        },
        {
          label: '留观人次',
          value: allSingleZbDataTqdb?.zy_lgrc || 0,
          tq: allSingleZbDataTqdb?.zy_lgrc_tqzf || 0
        },
        {
          label: '日均病危人次',
          value: allSingleZbDataTqdb?.zy_bwrc || 0,
          tq: allSingleZbDataTqdb?.zy_bwrc_tqzf || 0
        },
        {
          label: '日均病重人次',
          value: allSingleZbDataTqdb?.zy_bzrc || 0,
          tq: allSingleZbDataTqdb?.zy_bzrc_tqzf || 0
        },
        {
          label: '死亡人次',
          value: allSingleZbDataTqdb?.zy_swrc || 0,
          tq: allSingleZbDataTqdb?.zy_swrc_tqzf || 0
        },
        {
          label: '分娩人次',
          value: allSingleZbDataTqdb?.zy_fmrc || 0,
          tq: allSingleZbDataTqdb?.zy_fmrc_tqzf || 0
        },
        {
          label: '平均住院日',
          value: allSingleZbDataTqdb?.zy_pjzyr || 0,
          tq: allSingleZbDataTqdb?.zy_pjzyr_tqzf || 0
        }
      ];
      zySituationList.forEach(f => {
        if (['日均病危人次', '日均病重人次', '死亡人次'].includes(f.label)) {
          f.valColor = f.value > 0 ? 'red' : '';
        }
      });
      _self.zySituationList = _self.handleFormatterTableData(
        zySituationList,
        'label',
        'value',
        'tq'
      );

      let incomeType = res.object?.incomeType || {};
      let proportionOutpatientInpatientIncomeMzList = [
        {
          name: '药品',
          value: incomeType?.mz_ypsr || 0,
          tq: incomeType?.mz_ypsr_zb || 0,
          className: 'c1'
        },
        {
          name: '服务',
          value: incomeType?.mz_fwsr || 0,
          tq: incomeType?.mz_fwsr_zb || 0,
          className: 'c2'
        },
        {
          name: '检查',
          value: incomeType?.mz_jcsr || 0,
          tq: incomeType?.mz_jcsr_zb || 0,
          className: 'c3'
        },
        {
          name: '化验',
          value: incomeType?.mz_jysr || 0,
          tq: incomeType?.mz_jysr_zb || 0,
          className: 'c4'
        },
        {
          name: '材料',
          value: incomeType?.mz_clsr || 0,
          tq: incomeType?.mz_clsr_zb || 0,
          className: 'c5'
        }
      ];
      _self.proportionOutpatientInpatientIncomeMzData.series[0][
        'data'
      ] = proportionOutpatientInpatientIncomeMzList;
      _self.proportionOutpatientInpatientIncomeMzList = _self.handleFormatterTableData(
        proportionOutpatientInpatientIncomeMzList,
        'name',
        'value',
        'tq'
      );

      let proportionOutpatientInpatientIncomeZyList = [
        {
          name: '药品',
          value: incomeType?.zy_ypsr || 0,
          tq: incomeType?.zy_ypsr_zb || 0,
          className: 'c1'
        },
        {
          name: '服务',
          value: incomeType?.zy_fwsr || 0,
          tq: incomeType?.zy_fwsr_zb || 0,
          className: 'c2'
        },
        {
          name: '检查',
          value: incomeType?.zy_jcsr || 0,
          tq: incomeType?.zy_jcsr_zb || 0,
          className: 'c3'
        },
        {
          name: '化验',
          value: incomeType?.zy_jysr || 0,
          tq: incomeType?.zy_jysr_zb || 0,
          className: 'c4'
        },
        {
          name: '材料',
          value: incomeType?.zy_clsr || 0,
          tq: incomeType?.zy_clsr_zb || 0,
          className: 'c5'
        }
      ];
      _self.proportionOutpatientInpatientIncomeZyData.series[0][
        'data'
      ] = proportionOutpatientInpatientIncomeZyList;
      _self.proportionOutpatientInpatientIncomeZyList = _self.handleFormatterTableData(
        proportionOutpatientInpatientIncomeZyList,
        'name',
        'value',
        'tq'
      );

      let hospDeptZbDataList = res.object.hospDeptZbDataList || [];
      _self.deptListData = hospDeptZbDataList.map((m, index) => {
        return {
          ...m,
          index: index + 1
        };
      });
      _self.$forceUpdate();
    },

    returnBack() {
      uni.redirectTo({
        url: '/pages/deansDaily/index?index=2'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  background: #fff;

  ::v-deep {
    .blue {
      color: #4775c0 !important;
    }

    .org {
      color: #e99d42 !important;
    }

    .green {
      color: #79cfb9 !important;
    }

    .red {
      color: red !important;
    }

    .abnormal {
      color: #52cbb4 !important;
    }

    .destination {
      color: #e97582 !important;
    }

    .s09 {
      transform: scale(0.95) !important;
    }
  }

  .content-container {
    padding: 0 16rpx;

    .module-title {
      font-weight: bold;
      font-size: 36rpx;
      margin-bottom: 16rpx;
    }

    .table-container {
      display: flex;
      flex-direction: column;

      .table-head {
        display: flex;
        background: #eff4fc;
        padding: 16rpx;

        .head-item {
          flex: 1;
          text-align: center;
          font-weight: bold;
          font-size: 32rpx;
          &:first-child {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }
          &:nth-child(2) {
            transform: translateX(-4rpx);
          }
          &:last-child {
            flex: none !important;
            width: 200rpx !important;
          }
        }
      }

      .table-body {
        padding: 0 16rpx;

        .body-row {
          display: flex;

          .body-item {
            flex: 1;
            font-size: 32rpx;
            padding: 16rpx;
            border-right: 2rpx dashed #e7e7e7;
            border-bottom: 2rpx dashed #e7e7e7;
            .font-zf-icon {
              font-size: 32rpx;
              margin-right: 8rpx;
            }

            &:first-child {
              padding-left: 0rpx;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
            }

            &:last-child {
              padding-right: 0rpx;
              flex: none !important;
              width: 200rpx !important;
            }

            &.num-color {
              color: #4775c0;
            }

            &:last-child {
              border-right: none;
            }

            &.center {
              text-align: center;
            }

            &.left {
              text-align: left;
            }

            &.right {
              text-align: right;
            }
          }
        }
      }
    }

    //收入情况
    .income-situation {
      width: 100%;
      border: 2rpx solid #e7e7e7;
      border-radius: 16rpx;
      margin-bottom: 16rpx;

      .echarts-label {
        font-size: 36rpx;
        margin-top: 16rpx;
        font-weight: bold;
        padding-left: 16rpx;
      }

      .income-charts-box {
        background-color: #ffffff;
        height: 400rpx;
        width: 100%;
      }

      .proportion-outpatient-inpatient-income-box {
        background-color: #ffffff;
        height: 350rpx;
        width: 100%;
        display: flex;

        .echarts-container {
          width: 320rpx;
        }

        .list-container {
          flex: 1;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;

          .list-item {
            width: 100%;
            color: #5a5a5a;
            font-size: 24rpx;
            display: flex;
            padding-right: 16rpx;

            .title {
              width: 90rpx;

              &::before {
                content: '';
                display: inline-block;
                width: 16rpx;
                height: 16rpx;
                border-radius: 50%;
                margin-right: 16rpx;
              }

              &.c1 {
                &::before {
                  background-color: #1987fa;
                }
              }

              &.c2 {
                &::before {
                  background-color: #86c46f;
                }
              }

              &.c3 {
                &::before {
                  background-color: #f9c057;
                }
              }

              &.c4 {
                &::before {
                  background-color: #eb5a5b;
                }
              }

              &.c5 {
                &::before {
                  background-color: #68b9d8;
                }
              }
            }

            .value {
              flex: 1;
              direction: rtl;
              text-align: right;
              margin-right: 16rpx;
            }

            .percentage {
              width: 115rpx;
              direction: rtl;
              text-align: right;
              color: #929292;
            }
          }
        }
      }
    }

    //门诊和住院科室收入前五
    .mz-zy-container-top5 {
      width: 100%;
      border: 1px solid #e7e7e7;
      border-radius: 16rpx;
      margin-bottom: 16rpx;

      .table-container {
        .body-row {
          &:last-child {
            .body-item {
              border-bottom: none;
            }
          }
        }
      }
    }

    //住院科室业务数据
    .inpatient-department-business-data {
      width: 100%;
      border: 1px solid #e7e7e7;
      border-radius: 16rpx;
      padding: 16rpx;
    }
  }

  ::v-deep .uni-navbar {
    .uni-navbar__header-btns {
      > .uni-navbar-btn-text {
        text {
          font-size: 32rpx !important;
          transform: translateY(-2rpx);
        }
      }
    }

    .uni-nav-bar-right-text {
      span {
        color: #333 !important;
      }
    }
  }
}
</style>
