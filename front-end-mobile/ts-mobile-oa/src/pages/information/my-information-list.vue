<template>
  <view class="ts-content">
    <page-head @clickLeft="returnBack" title="我的发布"></page-head>
    <uni-search-bar
      radius="100"
      bgColor="#FFFFFF"
      searchBgColor="#eeeeee"
      borderColor="transparent"
      cancelButton="none"
      @confirm="search"
      placeholder="搜索"
    ></uni-search-bar>
    <scroll-view class="swiper_head" :scroll-x="true" :show-scrollbar="false">
      <view
        v-for="(tab, index) in tabBars"
        :key="index"
        class="uni-tab-item"
        :data-current="index"
        @click="ontabtap"
      >
        <view :class="tabIndex == index ? 'uni-tab-item-title-active' : ''">
          <text class="uni-tab-item-title">{{ tab.name }}</text>
          <text
            class="uni-tab-item-num"
            v-if="tab.total != null && tab.total != 0"
            >{{ tab.total >= 100 ? '99+' : tab.total }}</text
          >
        </view>
      </view>
    </scroll-view>
    <swiper
      :current="tabIndex"
      class="swiper_box"
      :duration="300"
      @change="ontabchange"
    >
      <swiper-item
        class="swiper_item"
        v-for="(item, index) in tabBars"
        :key="index"
      >
        <mescroll
          :ref="'mescroll' + index"
          :mescrollIndex="index"
          :down="item.downOption"
          @getDatas="getListData"
          @setDatas="setListData"
          @datasInit="datasInit"
        >
          <view class="contact_list">
            <view
              class="contact_item"
              v-for="row in item['list']"
              :key="row.id"
              @tap="chooseItem(row)"
            >
              <view
                class="contact_item_img"
                :data-c="row.initColor"
                :style="{ 'background-color': row.initColor }"
                >{{ row.channelName.substring(row.channelName.length - 2) }}
              </view>
              <view class="contact_item_info">
                <view class="contact_item_row">
                  <text class="contact_item_top" v-if="row.showSign === '1'"
                    >[顶]</text
                  >
                  <text
                    class="contact_item_quintessence"
                    v-if="row.isMarrow === '1'"
                    >[精]</text
                  >
                  <text
                    class="contact_item_title"
                    :class="[
                      row.bid ? 'unread' : '',
                      row.titleColor === '1' ? 'titleRed' : ''
                    ]"
                  >
                    {{
                      row.informationTitle
                        ? row.informationTitle.replace(
                            / &lt; \/?[^>]* &gt; /g,
                            ''
                          )
                        : '无主题'
                    }}
                  </text>
                  <text
                    class="contact_item_status"
                    v-if="row.informationStatus === '0'"
                    >待审核</text
                  >
                  <text
                    class="contact_item_read"
                    v-else-if="row.informationStatus != '2'"
                  >
                    浏览 {{ row.informationKits }}
                  </text>
                </view>
                <!-- <view class="contact_item_row">
                  <rich-text
                    class="contact_item_content"
                    :nodes="
                      row.informationContent
                        ? row.informationContent.replace(/<\/?[^>]*>/g, '')
                        : '此信息没有文字内容'
                    "
                  ></rich-text>
                </view> -->
                <view class="contact_item_row">
                  <text class="contact_item_user">{{
                    row.createDeptName + ' ' + row.createUserName
                  }}</text>
                  <text class="contact_item_time">{{
                    row.releaseDate
                      ? row.releaseDate.substring(0, 16)
                      : row.createDate.substring(0, 16)
                  }}</text>
                </view>
              </view>
            </view>
          </view>
        </mescroll>
      </swiper-item>
    </swiper>
  </view>
</template>

<script>
import { mapState } from 'vuex';
import mescroll from '@/components/mescroll-swiper/mescroll.vue';
export default {
  components: {
    mescroll
  },
  data() {
    return {
      keywords: '',
      tabBars: [
        {
          name: '待审批',
          handleStatus: 0, //办理状态
          downOption: false, //初始化时是否下拉加载
          isInit: false, //是否已初始化
          total: null,
          list: []
        },
        {
          name: '发布中',
          handleStatus: 1,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '取消发布',
          handleStatus: 3,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        },
        {
          name: '已驳回',
          handleStatus: 2,
          downOption: false,
          isInit: false,
          total: null,
          list: []
        }
      ],
      tabIndex: 0, //当前选中的tab索引值，从0计数
      infoTypeList: [
        {
          channelName: '全部类型',
          channelId: ''
        }
      ],
      itemColor: [
        '#005BAC',
        '#8080ff',
        '#1ab785',
        '#ff7f65',
        '#da70d6',
        '#639ef6',
        '#2cb8c4'
      ]
    };
  },
  onLoad(opt) {
    this.tabIndex = JSON.stringify(opt) != '{}' ? Number(opt.index) : 0;
    this.tabBars.map((item, index) => {
      if (this.tabIndex == index) {
        item.downOption = true;
        item.isInit = true;
      } else {
        item.downOption = false;
        item.isInit = false;
      }
    });
    this.getInfoCountChannel();
    this.getInfoNum();
  },
  methods: {
    getInfoNum() {
      this.ajax
        .getInfomationNumber({
          title: this.keywords
        })
        .then(res => {
          this.$set(this.tabBars[0], 'total', res.object.pendingapproval);
          this.$set(this.tabBars[1], 'total', res.object.approval);
          this.$set(this.tabBars[2], 'total', res.object.cancelpush);
          this.$set(this.tabBars[3], 'total', res.object.rejected);
        });
    },
    //搜索
    search(res) {
      this.keywords = res.value;
      this.tabBars.forEach(item => {
        item.list = [];
        item.isInit = false;
      });
      this.$nextTick(() => {
        this.tabBars[this.tabIndex]['isInit'] = true;
        this.$refs[`mescroll${this.tabIndex}`][0].downCallback();
      });
    },
    //获取信息类型
    getInfoCountChannel() {
      this.ajax
        .getInfoCountByChannel({
          index: 1
        })
        .then(res => {
          this.infoTypeList = this.infoTypeList.concat(res.object);
        });
    },
    async getListData(page, successCallback, errorCallback, keywords, index) {
      await this.ajax
        .getInformationList({
          index: 1,
          sidx: 'releaseDate',
          sord: 'desc',
          informationStatus: this.tabBars[index]['handleStatus'],
          informationTitle: this.keywords,
          pageSize: page.size,
          pageNo: page.num
        })
        .then(async res => {
          let rows = res.rows;
          await successCallback(rows, res.totalCount);
        })
        .catch(() => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      rows.forEach(item => {
        this.infoTypeList.forEach((one, index) => {
          if (one.channelName == item.channelName) {
            item.initColor = this.itemColor[index - 1];
          }
        });
      });
      this.tabBars[index]['total'] = totalCount;
      let list = rows.filter(item => item.status != 0);
      this.tabBars[index]['list'] = this.tabBars[index]['list'].concat(list);
    },
    datasInit(keywords, index) {
      this.tabBars[index]['list'] = [];
    },
    //tab点解切换
    async ontabtap(e) {
      let index = e.target.dataset.current || e.currentTarget.dataset.current;
      await this.switchTab(Number(index));
    },
    //tab滑动切换
    async ontabchange(e) {
      let index = e.target.current || e.detail.current;
      await this.switchTab(Number(index));
    },
    async switchTab(index) {
      if (this.tabIndex === index) {
        return;
      } else if (!this.tabBars[index]['isInit']) {
        this.tabBars[index]['isInit'] = true;
        await this.$refs[`mescroll${index}`][0].downCallback();
      }
      this.tabIndex = index;
    },
    chooseItem(item) {
      let pagePramas = {},
        pagePath = '';
      if (item.informationStatus === '0') {
        uni.setStorageSync('workflow_info', {
          businessId: item.id,
          wfInstanceId: item.workflowId
        });
        pagePramas = {
          fromPage: 'myInformation',
          formListTabIndex: this.tabIndex
        };
        pagePath = '/pages/workflow/my-information-detail';
      } else {
        pagePramas = {
          informationId: item.id,
          fromPage: 'myInformation',
          formListTabIndex: this.tabIndex
        };
        pagePath = '/pages/information/information-details';
      }

      // 判断信息是否过期
      let isOverdue = false;
      if (item.validendTime) {
        const validendTime = new Date(item.validendTime.replace(/-/g, '/'));
        if (new Date() > validendTime) {
          isOverdue = true;
        }
      }

      // 如果当前是发布中，并且信息未过期，则显示提醒
      if (this.tabIndex === 1 && !isOverdue) {
        pagePramas.showRemind = true;
      }

      uni.navigateTo({
        url: `${pagePath}?${this.$common.convertObj(pagePramas)}`
      });
    },
    returnBack() {
      this.$parentTypeFun({
        type: 'jumpPage',
        path: '/workbench'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .swiper_head {
    position: relative;
    width: 100%;
    height: 80rpx;
    background-color: #ffffff;
    flex-direction: row;
    box-sizing: border-box;
    /* #ifndef APP-PLUS */
    white-space: nowrap;
    /* #endif */
    /* flex-wrap: nowrap; */
    /* border-color: #cccccc;
			border-bottom-style: solid;
			border-bottom-width: 1px; */
    &::before,
    &::after {
      position: absolute;
      z-index: 10;
      right: 0;
      left: 0;
      height: 1px;
      content: '';
      -webkit-transform: scaleY(0.5);
      transform: scaleY(0.5);
      background-color: #eeeeee;
    }
    &::before {
      top: 0;
    }
    &::after {
      bottom: 0;
    }
    .uni-tab-item {
      /* #ifndef APP-PLUS */
      display: inline-block;
      /* #endif */
      flex-wrap: nowrap;
      width: 25%;
      box-sizing: border-box;
      text-align: center;
      .uni-tab-item-title,
      .uni-tab-item-num {
        color: #666;
        font-size: 30rpx;
        height: 100%;
        line-height: 2.5;
        flex-wrap: nowrap;
        box-sizing: border-box;
        /* #ifndef APP-PLUS */
        white-space: nowrap;
        /* #endif */
      }
      .uni-tab-item-num {
        font-size: 28rpx;
        color: #fff;
        background-color: #f59a23;
        border-radius: 40rpx;
        padding: 0 10rpx;
        margin: 0 10rpx;
      }
      .uni-tab-item-title-active {
        color: $theme-color;
        border-bottom: 2px solid $theme-color;
      }
    }
  }
  .swiper_box {
    flex: 1;
    .swiper_item {
      flex: 1;
      flex-direction: row;
    }
  }
  .uni_collapse_cell {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -webkit-flex-direction: column;
    flex-direction: column;
    border-color: #e5e5e5;
    border-bottom-width: 1px;
    border-bottom-style: solid;
    height: 48px;
    .uni_collapse_cell_title {
      padding: 12px 12px;
      position: relative;
      display: flex;
      width: 100%;
      box-sizing: border-box;
      height: 48px;
      line-height: 24px;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      .uni_collapse_cell_title_text {
        flex: 1;
        font-size: 14px;
        white-space: nowrap;
        color: inherit;
        overflow: hidden;
        text-overflow: ellipsis;
        color: rgb(51, 51, 51);
        font-weight: bold;
      }
    }
  }
  .contact_item {
    padding: 22rpx 30rpx;
    background-color: #ffffff;
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      content: '';
      bottom: 0;
      height: 1px;
      background-color: #eee;
      left: 30rpx;
      right: 0;
    }
    &:last-child::after {
      height: 0;
    }
    .contact_item_img {
      width: 80rpx;
      height: 80rpx;
      margin-right: 20rpx;
      border-radius: 100%;
      text-align: center;
      line-height: 80rpx;
      font-size: 28rpx;
      background-color: #005bac;
      color: #fff;
    }
    .contact_item_info {
      flex: 1;
      .contact_item_row {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        .contact_item_title {
          flex: 1;
          font-size: 32rpx;
          font-weight: bold;
          color: #333;
          padding-right: 20rpx;
          box-sizing: border-box;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
        .contact_item_read {
          font-size: 24rpx;
          color: #999;
        }
        .contact_item_top {
          color: #f59a23;
          font-size: 28rpx;
        }
        .contact_item_quintessence {
          color: #3aad73;
          font-size: 28rpx;
        }
        .titleRed {
          color: #dd1f36 !important;
        }
        .contact_item_status {
          font-size: 24rpx;
          color: #3aad73;
        }
        .contact_item_content {
          font-size: 28rpx;
          color: #666;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .contact_item_user {
          flex: 1;
        }
        .contact_item_user,
        .contact_item_time {
          font-size: 24rpx;
          color: #999;
        }
      }
    }
  }
}
</style>
