<template>
  <view class="ts-content edit-personal-information-container">
    <page-head title="编辑个人信息" right-text="" @clickLeft="returnBack">
    </page-head>

    <view class="edit-status-container" v-if="editStatus || hasStaging">
      <view class="edit-status-font">
        <view v-if="hasStaging">
          <view class="font staging">
            已默认自动填充暂存信息，黄色字段为暂存项
          </view>
        </view>

        <template v-if="editStatus">
          <view v-if="editStatus.status == 1">
            <view class="font">
              个人信息正在审批中,审批完成才能再次编辑
            </view>
            <view class="font">
              (注：红色字段为修改审批项）
            </view>
            <view class="process-info font">{{ processInfoText }}</view>
          </view>

          <view v-if="editStatus.status == 2">
            <view class="font">
              个人信息审批结束
              <span v-if="editStatus.data.length != 0">
                （<span class="red">红色字段</span>为不合格项）
              </span>
              <span v-else>
                (提示：全部通过)
              </span>
            </view>
            <view v-if="editStatus.data.length != 0" class="font">
              请您按照不合格提示项进行修改提交
            </view>
          </view>

          <view v-if="editStatus.status == 3">
            <view class="font">
              个人修改信息被驳回，请核对后重新提交！
            </view>
            <view class="font">
              (注：蓝色字段为审批合格项,红色字段为不合格项）
            </view>
          </view>
        </template>
      </view>
      <!-- <view class="font" @click.stop="handleSeeInfoHistory">查看 ></view> -->
    </view>

    <view class="bottom-operate" v-if="showOperateBtn">
      <view class="operate-container">
        <text class="pending-text" v-if="storageDate">
          上次暂存：{{ storageDate }}
        </text>
        <text class="submit-button" @click="handleStaging">暂存</text>
        <text class="submit-button submit" @click="handleSubmit">提交</text>
      </view>
    </view>

    <head-swiper
      v-model="activeTab"
      :tabs="headTabs"
      @change="handleActiveTabChange"
    />

    <view ref="editContent" class="edit-container">
      <view
        v-for="item of personalEditSetting"
        :key="item.id"
        :ref="item.id"
        class="edit-section-container"
      >
        <view class="section-title">
          <view
            :class="{
              'section-title-text': true,
              'pass-text': childrenPassGroup.includes(item.id),
              'out-text': childrenOutGroup.includes(item.id)
            }"
          >
            <text>
              {{ item.groupName }}
            </text>
            <text v-if="item.remark" class="group-remark-tips">
              {{ item.remark }}
            </text>
          </view>
          <text
            v-if="item.isDetailed == '1' && item.isEdit != 0 && showOperateBtn"
            @click="handleAddNewRow(item)"
          >
            <i class="oa-icon oa-icon-gengduo1 add-item-icon"></i>
          </text>
        </view>
        <template v-if="item.isDetailed == '0'">
          <template v-for="field of item.fields">
            <form-item
              :key="field.id"
              v-model="userInfo[item.id][0][field.fieldName]"
              :setting="field"
              :form="form[item.id]"
              :formData="userInfo[item.id][0]"
              :jobData="jobData"
              :lineDeptData="lineDeptData"
              @wake-up-popup="handleWakeUpChooseBox"
              @choose-img="handleChooseImg"
              :passFields="passFields"
              :outFields="outFields"
              :changeField="changeField[item.id] ? changeField[item.id][0] : {}"
            ></form-item>
            <view
              v-if="field.remark"
              class="form-item-remark-tips"
              :key="field.id + 'remark'"
            >
              {{ field.remark }}
            </view>
          </template>
        </template>
        <template v-if="item.isDetailed == '1'">
          <template v-if="userInfo[item.id] && userInfo[item.id].length">
            <view
              v-for="(data, dIndex) of userInfo[item.id]"
              :key="dIndex"
              class="child-form-item-container"
            >
              <view class="child-form-action-content">
                <view
                  class="oa-icon oa-icon-fanhui-copy"
                  @click.native="handleFoldItem($event)"
                ></view>
                <view
                  v-if="
                    !(dIndex == 0 && item.showDelete == '1') && showOperateBtn
                  "
                  class="oa-icon oa-icon-gengduo1"
                  @click="
                    handleDeleteChildFormItem(
                      userInfo[item.id],
                      form[item.id],
                      dIndex
                    )
                  "
                ></view>
              </view>
              <view class="child-form-content">
                <template v-for="field of item.fields">
                  <form-item
                    :key="field.id"
                    v-model="data[field.fieldName]"
                    :setting="field"
                    :form="form[item.id][dIndex]"
                    :formData="data"
                    :jobData="jobData"
                    :lineDeptData="lineDeptData"
                    @wake-up-popup="handleWakeUpChooseBox"
                    @choose-img="handleChooseImg"
                    :changeField="
                      changeField[item.id]
                        ? changeField[item.id][dIndex] || {}
                        : {}
                    "
                  ></form-item>
                </template>
              </view>
            </view>
          </template>
        </template>
      </view>
    </view>
    <uni-popup
      ref="popup"
      :type="picker.popupType"
      :popup-choice="picker.popupChoice"
      @maskclick="maskConfirm"
    >
      <view class="popup-content">
        <template v-if="['checkbox', 'radio'].includes(picker.popupChoice)">
          <view
            v-for="item of picker.optionsList"
            :key="item.value"
            class="option-item checkbox-item"
            @click="handleOptionSelect(item)"
          >
            {{ item.label }}

            <uni-icons
              v-if="picker.value == item.value"
              type="checkmarkempty"
              color="#005BAC"
              size="44"
            />
          </view>
        </template>
      </view>
    </uni-popup>

    <date-picker
      ref="datePicker"
      :mode="picker.mode"
      :fields="picker.fields"
      :value="picker.value"
      @confirm="handleDatePickerConfirm"
    ></date-picker>

    <!-- 地区选择 -->
    <u-select
      v-model="regionSelectShow"
      :list="cityDatas"
      :default-value="picker.regionDefault"
      mode="mutil-column-auto"
      @confirm="regionSelectConfirm"
    ></u-select>

    <input-prompt
      v-if="cancelShow"
      :value="inputVal"
      :type="inputType"
      :pattern="inputPattern"
      :title="titleText"
      :name="fieldName"
      :placeholder="placeholderText"
      @confirm="confirm"
      @cancel="cancel"
    ></input-prompt>
  </view>
</template>

<script>
import common from '@/common/js/common.js';

import inputPrompt from '@/components/input-prompt/input-prompt.vue';
import FormItem from './form-item.vue';
import DatePicker from '@/components/picker/date-picker.vue';
import UniPopup from '../../../components/uni-popup/uni-popup.vue';
//导入vuex的mapState值和mapMutations方法
import { mapState, mapMutations } from 'vuex';
import { cityDatas } from '@/common/js/cityData.js';
import formJs from './form.js';
//#region
/**
 * @typedef userInfoSettingGroupItem
 * @property {string} id 唯一标识
 * @property {string} groupName 分组名称
 * @property {1 | 0 | null} isDetailed 是否是多组数据
 * @property {1 | 0 | null} isEdit 该分组是否可以编辑，该属性将会影响此分组下所有的参数是否可以编辑
 * @property {1 | 0 | null} isAllowDeleted 是否可停用
 * @property {field[]} fields 该分组下参数列表
 */
/**
 * @namespace userInfoEdit
 * @typedef {object} field 详情可以参考接口返回 /ts-basics-bottom/employeeField/getList
 * @property {string} id 唯一标识
 * @property {string} fieldName 字段值名称，即value
 * @property {string} showName 字段名称，即label
 * @property {string | 'input' | 'textarea' | 'number' | 'address' | 'date' | 'radio' | 'checkbox' | 'select' | 'file' | 'img' | 'deptChose' | 'jobType_1' | 'jobType_2' | 'jobType3'} fieldType 字段内容类型
 * @property {1 | 0 | null} isMust 是否必填
 * @property {1 | 0 | null} isOnly 是否只读
 * @property {1 | 0 | null} isHide 是否隐藏
 * @property {1 | 0 | null} isAllowDeleted 是否可停用
 * @property {1 | 0 | null} isDisabled 是否禁用
 * @property {1 | 0 | null} isEdit 是否可编辑
 * @property {string} promptText 提示文字
 * @property {string | number} fieldLength 字段长度
 * @property {string | 'idCard' | 'isPhone' | 'email'} decimalDigit 校验规则
 * @property {string} dataFormat 时间格式
 */
//#endregion
export default {
  mixins: [formJs],
  components: {
    inputPrompt,
    FormItem,
    UniPopup,
    DatePicker
  },
  data() {
    return {
      processInfoText: '',

      editStatus: null,
      passFields: [],
      outFields: [],
      outFieldsShowTips: [],

      childrenPassGroup: [],
      childrenOutGroup: [],
      childrenOutGroupShowTips: [],

      fromPage: '',
      submiting: false,
      activeTab: '',
      scrollLock: null, // 滚动计时
      /**@type userInfoSettingGroupItem*/
      personalEditSetting: [], // 人员信息编辑设置

      changeField: {}, // 暂存变更字段
      storageDate: '',

      form: {}, // 表单对象
      userInfo: {}, // 用户信息
      databaseData: {}, // 最初用户信息
      rejectFormData: null, //流程驳回 驳回字段回显的 formData值
      passFormData: null, //流程通过 不合格字段回显的 formData值
      jobData: [], // 工作选项数据
      lineDeptData: [], // 平铺的科室数据

      regionSelectShow: false, // 城市选择弹窗是否显示
      cityDatas, // 城市选择数据

      cancelShow: false,
      hasStaging: false // 暂存记录
    };
  },
  computed: {
    employeeId() {
      let userInfo = this.$store.state.userInfo || {};
      return userInfo.employeeId;
    },
    employeeNo() {
      let userInfo = this.$store.state.userInfo || {};
      return userInfo.employeeNo;
    },
    showOperateBtn() {
      return this.editStatus?.status != '1';
    },
    headTabs() {
      let tabs = this.personalEditSetting.map(item => ({
        label: item.groupName,
        value: item.id
      }));
      if (
        this.editStatus &&
        this.editStatus?.status &&
        this.editStatus.data &&
        this.editStatus.data.length > 0
      ) {
        // 区分字表
        let childrenGroup = this.editStatus.data.filter(i => i.updateType == 2);
        switch (this.editStatus?.status) {
          case '1':
            // 审批中 tabs全展示红色
            let activeIds = childrenGroup.map(m => m.groupId);
            tabs.forEach(e => {
              if (activeIds.includes(e.value)) e.className = 'out-text';
            });
            break;
          case '3':
            // 驳回 tabs区分颜色
            let pass = childrenGroup
              .filter(m => m.auditStatus == 1)
              .map(m => m.groupId);
            let out = childrenGroup
              .filter(m => m.auditStatus == 2)
              .map(m => m.groupId);

            tabs.forEach(e => {
              if (pass.includes(e.value)) e.className = 'pass-text';
              if (out.includes(e.value)) e.className = 'out-text';
            });
            break;
        }
      }
      return tabs;
    }
  },
  async onLoad(opt) {
    this.fromPage = opt?.fromPage || '';

    let res = await this.ajax.customEmployeeBaseFindDetailsById(
      this.employeeId,
      {
        groupId: 'all'
      }
    );

    let resObject = common.deepClone(res.object);

    this.userInfo = common.deepClone(resObject);
    this.databaseData = common.deepClone(resObject);
    await this.handleGetProcessStatus();

    // 获取暂存信息
    if (this.editStatus?.status != '1') {
      await this.handleEchoStaging('echo');
    }

    this.getInitEditFormData();
    this.getJobData();
  },
  onReady() {
    this.$nextTick(() => {
      this.$refs.editContent.$el.removeEventListener(
        'scrollend',
        this.handleScrollEnd
      );
      this.$refs.editContent.$el.addEventListener(
        'scrollend',
        this.handleScrollEnd
      );
      this.$refs.editContent.$el.removeEventListener(
        'scroll',
        this.handleEditContent
      );
      this.$refs.editContent.$el.addEventListener(
        'scroll',
        this.handleEditContent
      );
    });
  },
  methods: {
    ...mapMutations(['changeState']),
    handleSeeInfoHistory() {
      uni.navigateTo({
        url: `/pages/personalCenter/history/history?fromPage=/pages/personalCenter/edit/edit`
      });
    },
    // 获取审批流程结果
    async handleGetProcessStatus() {
      let res = await this.ajax.customEmployeeBaseInApproval();
      if (res.success) {
        this.editStatus = res.object;
        if (
          this.editStatus &&
          this.editStatus?.status &&
          this.editStatus.data &&
          this.editStatus.data.length > 0
        ) {
          if (this.editStatus.status == '2') {
            this.editStatus.data = this.editStatus.data.filter(
              f => f.auditStatus == 2
            );
          }

          let parent = this.editStatus.data.filter(i => i.updateType == 1);
          let children = this.editStatus.data.filter(i => i.updateType == 2);
          switch (this.editStatus.status) {
            case '1':
              // 审批中 禁止提交
              // 字段展示红色
              // 字段为审批中修改的值
              this.outFields = parent.map(p => p.fieldName);
              this.childrenOutGroup = children.map(i => i.groupId);

              this.handleSetFormValue();
              let result = await this.cusotmEmployeeGetEmployeeTask();
              if (result && result?.isAdmin == 'false') {
                if (result.stepName && result.assigneeNames) {
                  this.processInfoText = `${result.stepName} -【${result.assigneeNames}】`;
                }
              }
              break;
            case '2':
              // 审批成功
              this.handleSetFormValue();
              this.outFields = parent
                .filter(f => f.auditStatus == 2)
                .map(p => p.fieldName);

              this.outFieldsShowTips = parent.filter(
                f => f.auditStatus == 2 && f.remark
              );

              this.childrenOutGroup = children
                .filter(f => f.auditStatus == 2)
                .map(i => i.groupId);

              this.childrenOutGroupShowTips = children.filter(
                f => f.auditStatus == 2 && f.remark
              );
              break;
            case '3':
              // 驳回
              // 区分颜色
              // 字段为驳回中修改的值
              this.passFields = parent
                .filter(f => f.auditStatus == 1)
                .map(m => m.fieldName);

              this.outFields = parent
                .filter(f => f.auditStatus == 2)
                .map(p => p.fieldName);

              this.outFieldsShowTips = parent.filter(
                f => f.auditStatus == 2 && f.remark
              );

              this.childrenPassGroup = children
                .filter(f => f.auditStatus == 1)
                .map(i => i.groupId);

              this.childrenOutGroup = children
                .filter(f => f.auditStatus == 2)
                .map(i => i.groupId);

              this.childrenOutGroupShowTips = children.filter(
                f => f.auditStatus == 2 && f.remark
              );

              this.handleSetFormValue();
              break;
          }
        }
      }
    },
    // 获取暂存信息
    async handleEchoStaging(type) {
      this.changeField = {};
      this.storageDate = '';

      let res = await this.ajax.customEmployeeBaseGetStorage({
        groupId: 'all'
      });
      if (res.success && res.object) {
        const data = res.object?.[0];
        if (!data || !Object.keys(data).length) return;

        const { content, storageDate } = data;
        const stagingContent = JSON.parse(content);
        const { stagingData, changeField } = stagingContent;
        if (!stagingData || JSON.stringify(stagingData) === '{}') return;

        this.storageDate = storageDate;
        if (type === 'echo') {
          this.userInfo = common.deepClone(stagingData);
          if (changeField) {
            for (const groupId in changeField) {
              if (changeField[groupId].every(e => e === false)) {
                delete changeField[groupId];
                continue;
              }

              changeField[groupId].forEach((row, index) => {
                if (typeof row === 'boolean' && !row) {
                  changeField[groupId][index] = {};
                }
              });
            }
            this.changeField = changeField;
          }
          this.hasStaging = true;
        }
      }
    },

    async handleStaging() {
      let before = common.deepClone(this.databaseData);
      if (
        this.editStatus &&
        this.editStatus.status == '2' &&
        this.editStatus.data.length &&
        this.passFormData
      ) {
        before = common.deepClone(this.passFormData);
      }
      // 暂存时有驳回情况，使用驳回数据更新后 进行对比；
      if (
        this.editStatus &&
        this.editStatus.status == '3' &&
        this.editStatus.data.length &&
        this.rejectFormData
      ) {
        before = common.deepClone(this.rejectFormData);
      }

      let stagingData = common.deepClone(this.userInfo); // 当前页面数据
      let changeField = this.handleDiff(before, stagingData);

      let data = {
        employeeId: this.employeeId,
        groupId: 'all',
        content: JSON.stringify({
          stagingData,
          changeField
        })
      };
      let res = await this.ajax.customEmployeeBaseStorage(data);

      if (res.success) {
        this.handleEchoStaging();
        uni.showToast({
          icon: 'none',
          title: '暂存数据成功!'
        });
      }
    },
    handleSetFormValue() {
      this.editStatus.data.forEach(i => {
        if (i.updateType == 2 && i.afterData)
          i.afterData = JSON.parse(i.afterData);
        if (i.updateType == 1) {
          this.$set(this.userInfo[i.groupId][0], i.fieldName, i.afterData);
        } else {
          this.$set(this.userInfo, [i.groupId], i.afterData);
        }
      });
      if (
        this.editStatus &&
        this.editStatus.status == '3' &&
        this.editStatus.data.length
      ) {
        this.rejectFormData = common.deepClone(this.userInfo);
      }
      if (
        this.editStatus &&
        this.editStatus.status == '2' &&
        this.editStatus.data.length
      ) {
        this.passFormData = common.deepClone(this.userInfo);
      }
    },
    /**@desc 获取渲染表格数据 */
    getInitEditFormData() {
      this.ajax
        .customEmployeeGroupGetDetailList({
          groupId: 'all',
          archivesType: 'yw'
        })
        .then(res => {
          if (!res.success) {
            return;
          }
          this.form = {};
          this.personalEditSetting = common.deepClone(res.object);

          // 无子表 字段底部添加 驳回备注
          if (Array.isArray(this.outFieldsShowTips)) {
            let itemField = this.outFieldsShowTips.map(m => {
              return {
                fieldName: m.fieldName,
                groupId: m.groupId,
                remark: m.remark
              };
            });

            itemField.forEach(e => {
              this.personalEditSetting
                .find(f => f.id == e.groupId)
                .fields.find(f => f.fieldName == e.fieldName).remark = e.remark;
            });
          }

          // 有子表 字段 右侧添加 驳回备注
          if (Array.isArray(this.childrenOutGroupShowTips)) {
            let itemField = this.childrenOutGroupShowTips.map(m => {
              return {
                fieldName: m.fieldName,
                groupId: m.groupId,
                remark: m.remark
              };
            });

            itemField.forEach(e => {
              this.personalEditSetting.find(f => f.id == e.groupId).remark =
                e.remark;
            });
          }

          this.computedSetting();

          // 本人修改 且没有暂存记录 与 流程修改记录不为审批中
          // 查看是否有默认展开项
          if (this.editStatus?.status != '1') {
            this.personalEditSetting.forEach(setting => {
              if (
                setting.isDetailed == '1' &&
                setting.showOpenBy == '1' &&
                Array.isArray(this.form[setting.id]) &&
                Array.isArray(this.userInfo[setting.id]) &&
                !this.form[setting.id].length &&
                !this.userInfo[setting.id].length
              ) {
                this.handleAddNewRow(setting);
              }
            });
          }

          // 判断是否获取科室数据
          res.object &&
            res.object.some(
              col =>
                col.fields &&
                col.fields.some(filed => filed.fieldType == 'deptChose')
            ) &&
            this.getDeptTreeData();
        });
    },
    /**@desc 获取工作选项数据 */
    getJobData() {
      this.ajax.getUserInfoEditJobData().then(res => {
        if (res.object) {
          let reSetOptions = function(list) {
            return list.map(item => {
              if (item.children && item.children.length) {
                item.children = reSetOptions(item.children);
              }
              let newItem = {
                ...item,
                label: item.name,
                value: item.id
              };
              delete newItem.name;
              delete newItem.id;
              return newItem;
            });
          };
          this.jobData = reSetOptions(res.object);
        }
      });
    },
    /**@desc 获取科室树数据 */
    getDeptTreeData() {
      this.ajax.getOneLineDeptListData().then(res => {
        if (res.object) {
          this.lineDeptData = res.object;
        }
      });
    },
    /**@desc 处理 tab 点击事件，用于滚动至指定位置 */
    handleActiveTabChange({ value }, index) {
      let pageDom = this.$refs[value][0];
      if (pageDom && pageDom.$el) {
        this.scrollLock = true;
        this.$refs.editContent.$el.scrollTo({
          top: pageDom.$el.offsetTop,
          behavior: 'smooth'
        });
      }
    },
    handleEditContent() {
      if (this.scrollLock) {
        return;
      }
      let scrollTop = this.$refs.editContent.$el.scrollTop,
        childNodeList = Array.from(this.$refs.editContent.$el.childNodes),
        index = childNodeList.findIndex(dom => dom.offsetTop > scrollTop + 50),
        activeTab = this.headTabs[index - 1].value;
      this.activeTab != activeTab && (this.activeTab = activeTab);
    },
    handleScrollEnd() {
      this.scrollLock = false;
    },
    handleFoldItem(e) {
      e.target.parentNode.parentNode.classList.toggle('is-fold');
    },
    handleDeleteChildFormItem(dataList, formList, index) {
      uni.showModal({
        title: '提示',
        content: '确定要删除该条数据吗?',
        success: res => {
          if (res.cancel) {
            return;
          }
          dataList.splice(index, 1);
          formList.splice(index, 1);
        }
      });
    },
    /**@desc 重新处理填写数据规则 */
    computedSetting() {
      this.personalEditSetting.forEach(setting => {
        if (setting.isDetailed != 1 && setting.fields) {
          /**@desc select 为政治面貌 且 默认值为 中共党员 时，入党时间必填 */
          //#region
          let index = setting.fields.findIndex(
            field =>
              field.fieldType == 'select' &&
              field.dataSource == 4 &&
              field.fieldName == 'political_status' &&
              field.value == 1
          );
          if (index >= 0) {
            let partyDateIndex = setting.fields.findIndex(field => {
              field.fieldName == 'party_date';
            });
            partyDateIndex >= 0 && (setting.fields[partyDateIndex].isMust = 1);
          }
          //#endregion
        }

        if (setting.isDetailed == '0') {
          if (!this.userInfo[setting.id] || !this.userInfo[setting.id].length) {
            this.userInfo[setting.id] = [{}];
          }
          this.form[setting.id] = {};
        } else {
          setting.fields &&
            setting.fields.forEach(field => {
              field.isEdit = setting.isEdit;
            });
          this.form[setting.id] = new Array(
            this.userInfo[setting.id]?.length || 0
          ).fill({});
        }
      });
    },
    /**@desc 添加新的数据 */
    handleAddNewRow(setting) {
      this.userInfo[setting.id].push({});
      this.form[setting.id].push({});
    },
    async handleSubmit() {
      if (this.editStatus && this.editStatus.status == 1) return;
      let validate = Object.keys(this.form).every(key => {
        let form = this.form[key];
        return this.handleValidateForm(form);
      });
      if (!validate || this.submiting) {
        return;
      }
      this.submiting = true;

      const validateHighestLevel = (field, label) => {
        const items = this.userInfo[field];
        if (Array.isArray(items) && items.length) {
          const highest = items.filter(f => f.highest_level === '1');
          if (highest.length === 0) {
            uni.showToast({
              icon: 'none',
              title: `请设置最高${label}`
            });
            this.handleActiveTabChange({ value: field });
            return false;
          }
          if (highest.length > 1) {
            uni.showToast({
              icon: 'none',
              title: `${label}信息只能有一个最高${label}`
            });
            this.handleActiveTabChange({ value: field });
            return false;
          }
        }
        return true;
      };

      if (
        !validateHighestLevel('5', '学历') ||
        !validateHighestLevel('4', '职称')
      ) {
        this.submiting = false;
        return false;
      }

      let dataList = this.personalEditSetting.map(setting => {
        let dataItem = {
          groupId: setting.id,
          isDetailed: setting.isDetailed,
          fields: [],
          detailFields: [],
          employeeId: this.employeeId
        };
        if (setting.isDetailed == '0') {
          let formData = (this.userInfo[setting.id] || [{}])[0];
          setting.fields.map(field => {
            dataItem.fields.push({
              id: field.id,
              fieldClass: field.fieldClass,
              fieldName: field.fieldName,
              showName: field.showName,
              isRemoveDuplicate: field.isRemoveDuplicate,
              value: formData[field.fieldName] || ''
            });
          });
        }
        if (setting.isDetailed == '1') {
          let list = [];
          let formDataList = this.userInfo[setting.id] || [];
          formDataList.map(formItem => {
            let childList = [];
            setting.fields.map(field => {
              childList.push({
                id: field.id,
                fieldClass: field.fieldClass,
                fieldName: field.fieldName,
                showName: field.showName,
                isRemoveDuplicate: field.isRemoveDuplicate,
                value: formItem[field.fieldName] || ''
              });
            });
            list.push(childList);
          });

          dataItem.detailFields = list;
        }
        return dataItem;
      });
      let submitData = {
        customFileds: dataList,
        groupId: 'all',
        employeeId: this.employeeId
      };
      let res = await this.ajax.customEmployeeBaseSave(submitData);
      if (res.success) {
        uni.showToast({
          icon: 'none',
          title: '编辑成功'
        });

        setTimeout(() => {
          this.$parentTypeFun({
            event: 'refreshUserInfo'
          });
          this.returnBack();
          this.submiting = false;
        }, 500);
        return;
      }

      uni.showToast({
        icon: 'none',
        title: res.message || '编辑失败'
      });
      this.submiting = false;
    },
    /**@desc 校验表单 */
    handleValidateForm(form) {
      if (form instanceof Array) {
        return form.every(childForm => this.handleValidateForm(childForm));
      } else {
        return Object.keys(form).every(key => {
          let res = form[key].validate();
          if (!res) {
            let col = form[key] || { $el: {} },
              top = col.$el.offsetTop || 0;
            this.scrollLock = true;

            col.$el.classList.add('validate');
            setTimeout(() => {
              col.$el.classList.remove('validate');
            }, 2400);

            this.$refs.editContent.$el.scrollTo({
              top,
              behavior: 'smooth'
            });
          }
          return res;
        });
      }
    },
    async cusotmEmployeeGetEmployeeTask() {
      try {
        let res = await this.ajax.customEmployeeBaseGetEmployeeTask(
          this.employeeNo
        );
        if (!res.success) return false;
        return res.object;
      } catch (error) {
        return false;
      }
    },

    handleDiff(before, after) {
      const modifiedKeys = {};
      const beforeGroupIds = Object.keys(before);
      const afterGroupIds = Object.keys(after);

      afterGroupIds.forEach(id => {
        if (beforeGroupIds.includes(id)) {
          // 对比相同分组的内容
          const diffResult = this.handleCompareObjects(before[id], after[id]);
          modifiedKeys[id] = diffResult;
        } else {
          // 如果之前没有这个分组，表示新增，标记为 'addOrEditGroup'
          modifiedKeys[id] = 'addOrEditGroup';
        }
      });

      return modifiedKeys;
    },

    handleCompareObjects(before, after) {
      const arr = [];
      const maxLen = Math.max(before.length, after.length);

      for (let i = 0; i < maxLen; i++) {
        const beforeItem = before[i] || {};
        const afterItem = after[i] || {};
        arr.push(this.handleObjectDiff(beforeItem, afterItem));
      }

      return arr.length ? arr : [false];
    },

    handleObjectDiff(obj1, obj2) {
      const diffData = {};
      const keys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);

      keys.forEach(key => {
        const val1 = obj1[key];
        const val2 = obj2[key];
        if (val1 === val2 || (this.isNil(val1) && this.isNil(val2))) return;

        diffData[key] = {
          before: val1 !== undefined ? val1 : undefined,
          after: val2 !== undefined ? val2 : undefined
        };
      });
      return Object.keys(diffData).length ? diffData : false;
    },

    isNil(value) {
      return value === undefined || value === null || value === '';
    },

    returnBack() {
      let path = 'my';
      if (this.fromPage) path = this.fromPage;
      this.$parentTypeFun({
        type: 'jumpPage',
        path: `/${this.fromPage}`
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.edit-personal-information-container {
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .form-item-remark-tips {
    padding: 0 16px;
    color: #fb4554 !important;
    opacity: 0.7;
    text-align: right;
    margin: 2px 0;
    font-size: 12px !important;
    border-bottom: 1px solid #eee;
  }
  .group-remark-tips {
    color: #fb4554 !important;
    opacity: 0.7;
    margin-left: 8px;
    font-size: 12px !important;
  }
  .bottom-operate {
    width: 100%;
    height: 42px;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #fff;
    z-index: 99;
    box-shadow: 0px -1px 6px #cccccc;
    padding: 0 8px;

    .operate-container {
      display: flex;
      align-items: center;
      .pending-text {
        font-size: 12px;
        margin-right: 8px;
      }
      .submit-button {
        padding: 4px 12px;
        border-radius: 5px;
        border: 1px solid #ccc;
        font-size: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        &.submit {
          margin-left: 8px;
          color: #fff;
          border: 1px solid #0c4bf5;
          background-color: #0c4bf5;
        }
      }
    }
  }

  .more-icon {
    width: 20px;
    height: 20px;
    margin-left: 16px;
  }

  .popup-pending-content {
    padding-top: 45px;
    z-index: 9999;
    background-color: #fff;
    .operate-item {
      border-bottom: 1px solid #eee;
      padding: 8px 0;
      display: flex;
      align-items: center;
      justify-content: center;

      .pending-echo {
        margin-left: 8px;
        font-size: 12px;
        color: #2782f7;
      }
    }
  }
  ::v-deep {
    .uni-tab-item {
      .out-text {
        color: #fa0213 !important;
      }

      .pass-text {
        color: #2782f7 !important;
      }
    }
  }
  .out-text {
    color: #fa0213 !important;
  }

  .pass-text {
    color: #2782f7 !important;
  }
  .edit-status-container {
    z-index: 100;
    width: 100%;
    background: rgb(22, 132, 252);
    padding: 4px 9px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgb(251, 251, 252);
    position: relative;
    .font {
      font-size: 10px !important;
      &.staging {
        color: rgb(222, 222, 5) !important;
      }
    }
    .red {
      color: #b61a27 !important;
    }
    .process-info {
      position: absolute;
      right: 0;
      top: 50%;
      width: 180px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      color: rgba(252, 202, 0, 1);
      font-size: 12px !important;
    }
  }
}
.edit-container {
  flex: 1;
  overflow-x: hidden;
  overflow-y: auto;
  margin-top: 8px;
  background-color: #fff;
  position: relative;
  padding-bottom: 42px;
}

.edit-section-container {
  padding: 10px 0;
  border-bottom: 1px dashed #eee;
}

.section-title {
  font-weight: bold;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  .section-title-text {
    display: inline-flex;
    align-items: center;
  }

  uni-text:first-child {
    display: inline-flex;
    align-items: center;
    &::before {
      content: ' ';
      height: 16px;
      width: 4px;
      border-radius: 4px;
      background-color: $theme-color;
      margin-right: 4px;
    }
  }
  .add-item-icon {
    font-size: 20px;
    font-weight: normal;
  }
}

.child-form-item-container {
  display: flex;
  overflow: hidden;
  max-height: 900vh;
  transition: all 0.3s;
  &:not(:last-child) {
    border-bottom: 1px solid #eee;
  }
  &.is-fold {
    max-height: 36px;
    .child-form-action-content .oa-icon-fanhui-copy {
      transform: rotate(180deg);
    }
  }
  .child-form-action-content {
    width: 32px;
    flex-shrink: 0;
    .oa-icon {
      text-align: center;
      line-height: 36px;
      transition: 0.5s transform;
    }
    .oa-icon-gengduo1 {
      transform: rotate(45deg);
      color: $u-type-error;
    }
  }
  .child-form-content {
    flex: 1;
  }
  .form-item {
    margin-left: 0;
  }
}

.popup-content {
  background: #fff;
  max-height: 80vh;
  overflow-x: hidden;
  overflow-y: auto;
  .option-item {
    padding: 8px 0;
    margin: 0 16px;
    &:not(:last-child) {
      border-bottom: 1px solid #eee;
    }
    &.checkbox-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      .uni-icons {
        height: 44rpx;
      }
    }
  }
}
</style>
