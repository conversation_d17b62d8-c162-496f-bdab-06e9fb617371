/**
数据验证（表单验证）
*/
const graceChecker = {
  error: '',
  check: function(data, rule) {
    for (let i = 0; i < rule.length; i++) {
      var key = rule[i].filedKey;
      var checkVal = data[key];
      var required = rule[i].required || false;
      var checkType = rule[i].checkType;
      var checkRule = rule[i].checkRule;
      var errorMsg = rule[i].errorMsg;
      var otherErrorMsg = rule[i].otherErrorMsg;
      if (!checkType || !key || !errorMsg) {
        return true;
      }
      if (required && this.isNull(checkType, checkVal)) {
        this.error = rule[i].errorMsg;
        return false;
      }
      switch (checkType) {
        case 'string':
          //字符串长度检查
          //checkRule规则
          // 1,3 代表 1到3个字符
          // 2 或 2, 代表只检查最短2个字符
          // 2,2 代表等于2个字符
          if (this.isNullData(checkVal)) {
            continue;
          }
          // if (!this.isType('String', checkVal)) {
          //   this.error = otherErrorMsg;
          //   return false;
          // }
          var reg = new RegExp('^.{' + checkRule + '}$');
          if (checkRule && !reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'number':
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (!this.isNumber(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'int':
          // 整数及长度检查
          //checkRule规则
          // 1,3 代表最小长度（不包含）,最大长度
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (checkRule) {
            var reg = new RegExp('^0|(-[1-9]|[1-9])[0-9]{' + checkRule + '}$');
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          } else {
            var reg = /(^$)|^(0|(-[1-9]|[1-9])[0-9]*)$/;
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          }
          break;
        case 'positiveInt':
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (!this.isNumber(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          if (checkRule) {
            var reg = new RegExp('^[1-9][0-9]{' + checkRule + '}$');
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          } else {
            var reg = /^[1-9][0-9]*$/;
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          }
          break;
        case 'nonnegativeInt':
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (!this.isNumber(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          if (checkRule) {
            var reg = new RegExp('^0|[1-9][0-9]{' + checkRule + '}$');
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          } else {
            var reg = /^0|[1-9][0-9]*$/;
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          }
          break;
        case 'nonnegativeNumber':
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (!this.isNumber(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          if (checkRule) {
            var reg = new RegExp('^(?:[1-9]d*|0)(?:.d{' + checkRule + '})?$');
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          } else {
            var reg = /^(?:[1-9]\d*|0)(?:\.\d+)?$/;
            if (!reg.test(checkVal)) {
              this.error = otherErrorMsg;
              return false;
            }
          }
          break;
        case 'between':
          // 数值区间检查 ( 小数或整数 )
          //checkRule规则
          // 1,3 或 2.5,1000 代表最小值,最大值
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (!this.isNumber(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          var minMax = checkRule.split(',');
          minMax[0] = Number(minMax[0]);
          minMax[1] = Number(minMax[1]);
          if (checkVal > minMax[1] || checkVal < minMax[0]) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'betweenD':
          // 数值区间检查 ( 整数 )
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = /^0|-?[1-9][0-9]?.?[0-9]*$/;
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          var minMax = checkRule.split(',');
          minMax[0] = Number(minMax[0]);
          minMax[1] = Number(minMax[1]);
          if (checkVal > minMax[1] || checkVal < minMax[0]) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'betweenF':
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = /^-?(0.?|[1-9]*.)+[0-9]+$/;
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          var minMax = checkRule.split(',');
          minMax[0] = Number(minMax[0]);
          minMax[1] = Number(minMax[1]);
          if (checkVal > minMax[1] || checkVal < minMax[0]) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'same':
          var type = {}.toString.call(checkVal);
          var isNull = false;
          if (type == '[object Array]') isNull = this.isNullArray(checkVal);
          else if (type == '[object Object]')
            isNull = this.isNullObject(checkVal);
          else isNull = this.isNullData(checkVal);
          if (isNull) {
            this.error = errorMsg;
            return false;
          }
          if (checkVal != checkRule) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'notsame':
          var type = {}.toString.call(checkVal);
          var isNull = false;
          if (type == '[object Array]') isNull = this.isNullArray(checkVal);
          else if (type == '[object Object]')
            isNull = this.isNullObject(checkVal);
          else isNull = this.isNullData(checkVal);
          if (isNull) {
            this.error = errorMsg;
            return false;
          }
          if (checkVal == checkRule) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'email':
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = /^\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'phone':
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = /^1[0-9]{10,10}$/;
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'idcard':
          if (this.isNullData(checkVal)) {
            continue;
          }
          //1.传入15位或者18位身份证号码，18位号码末位可以为数字或X
          var idCard = checkVal.toUpperCase();
          //2.去掉身份证的左右空格
          idCard = this.trim(idCard);
          //3.判断输入的身份证长度
          if (!/(^\d{15}$)|(^\d{17}([0-9]|X)$)/.test(idCard)) {
            this.error = otherErrorMsg;
            return false;
          }
          //4.验证前两位城市编码是否正确
          var aCity = {
            11: '北京',
            12: '天津',
            13: '河北',
            14: '山西',
            15: '内蒙古',
            21: '辽宁',
            22: '吉林',
            23: '黑龙江 ',
            31: '上海',
            32: '江苏',
            33: '浙江',
            34: '安徽',
            35: '福建',
            36: '江西',
            37: '山东',
            41: '河南',
            42: '湖北',
            43: '湖南',
            44: '广东',
            45: '广西',
            46: '海南',
            50: '重庆',
            51: '四川',
            52: '贵州',
            53: '云南',
            54: '西藏',
            61: '陕西',
            62: '甘肃',
            63: '青海',
            64: '宁夏',
            65: '新疆',
            71: '台湾',
            81: '香港',
            82: '澳门',
            91: '国外'
          };
          if (aCity[parseInt(idCard.substr(0, 2))] == null) {
            this.error = otherErrorMsg;
            return false;
          }
          //5.验证出生日期和校验位
          if (!this.validId15(idCard) && !this.validId18(idCard)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'zipcode':
          //邮编检查
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = /^[0-9]{6}$/;
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'reg':
          if (this.isNullData(checkVal)) {
            continue;
          }
          var reg = new RegExp(checkRule);
          if (!reg.test(checkVal)) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'in':
          if (this.isNullData(checkVal)) {
            continue;
          }
          if (checkRule.indexOf(checkVal) == -1) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'Array':
          var isArray =
            Array.isArray(checkVal) || this.isType('Array', checkVal);
          if (checkVal != null && checkVal != undefined && !isArray) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
        case 'Object':
          var isObject = this.isType('Object', checkVal);
          if (checkVal != null && checkVal != undefined && !isObject) {
            this.error = otherErrorMsg;
            return false;
          }
          break;
      }
    }
    return true;
  },
  //
  isNull: function(checkType, checkVal) {
    var stringType = [
      'string',
      'number',
      'int',
      'between',
      'betweenD',
      'betweenF',
      'email',
      'phone',
      'idcard',
      'zipcode',
      'reg',
      'in'
    ];
    var arrayType = 'Array';
    var objectType = 'Object';
    var anyType = ['same', 'notSame'];
    if (stringType.indexOf(checkType) != -1) {
      return this.isNullData(checkVal);
    } else if (arrayType == checkType) {
      return this.isNullArray(checkVal);
    } else if (objectType == checkType) {
      return this.isNullObject(checkType, checkVal);
    } else if (anyType.indexOf(checkType) != -1) {
      var type = {}.toString.call(checkVal);
      if (type == '[object Array]') return this.isNullArray(checkVal);
      else if (type == '[object Object]') return this.isNullObject(checkVal);
      else return this.isNullData(checkVal);
    }
  },
  isNullData: function(checkVal) {
    var reg = /[\S]+/;
    if (checkVal == null || checkVal == undefined || !reg.test(checkVal)) {
      return true;
    }
    return false;
  },
  isNullArray: function(checkVal) {
    var isArray = Array.isArray(checkVal) || this.isType('Array', checkVal);
    if (!isArray || checkVal.length < 1) {
      return true;
    }
    return false;
  },
  isNullObject: function(checkType, checkVal) {
    var isObject = this.isType('Object', checkVal);
    if (!isObject || JSON.stringify(checkType) == '{}') {
      return true;
    }
    return false;
  },
  //判断类型
  isType: function(checkType, checkVal) {
    return {}.toString.call(checkVal) == '[object ' + checkType + ']';
  },
  //判断是否为数字
  isNumber: function(checkVal) {
    var reg = /^\-?([1-9][0-9]*|0)(\.[0-9]+)?$/;
    return reg.test(checkVal);
  },
  //去掉字符串两端左右空格
  trim: function(strs) {
    return strs.replace(/(^\s*)|(\s*$)/g, ''); //使用js正则表达式方法
  },
  //出生日期的年月日验证
  yearMonthDayValidate: function(year, month, day) {
    year = parseInt(year); //年
    month = parseInt(month); //月
    day = parseInt(day); //日
    //判断年，月,日是否为空
    if (isNaN(year) || isNaN(month) || isNaN(day)) return false;
    //判断月是否是在1-12月之间
    if (month < 1 || month > 12) return false;
    //返回当月的最后一天
    let date = new Date(year, month, 0);
    //判断是否超过天数范围
    if (day < 1 || day > date.getDate()) return false;
    return true;
  },
  //身份证18位号码验证
  validId18: function(str) {
    if (str.length != 18) return false; //长度验证
    //1. 出生日期验证
    let reg = new RegExp(/^(\d{6})(\d{4})(\d{2})(\d{2})(\d{3})([0-9]|X)$/);
    let arrSplit = str.match(reg); //检查生日日期是否正确
    if (arrSplit == null) {
      return false;
    } else {
      if (!this.yearMonthDayValidate(arrSplit[2], arrSplit[3], arrSplit[4])) {
        return false;
      }
    }
    //2. 校验位验证
    let iW = new Array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1); //加权因子
    let iSum = 0;
    for (let i = 0; i < 17; i++) {
      let iC = str.charAt(i);
      let iVal = parseInt(iC);
      iSum += iVal * iW[i];
    }
    let iJYM = iSum % 11; //取模
    let sJYM = '';
    //获取的模查找对应的校验码字符值
    if (iJYM == 0) sJYM = '1';
    else if (iJYM == 1) sJYM = '0';
    else if (iJYM == 2) sJYM = 'x';
    else if (iJYM == 3) sJYM = '9';
    else if (iJYM == 4) sJYM = '8';
    else if (iJYM == 5) sJYM = '7';
    else if (iJYM == 6) sJYM = '6';
    else if (iJYM == 7) sJYM = '5';
    else if (iJYM == 8) sJYM = '4';
    else if (iJYM == 9) sJYM = '3';
    else if (iJYM == 10) sJYM = '2';
    let cCheck = str.charAt(17).toLowerCase();
    if (cCheck != sJYM) {
      return false;
    }
    return true;
  },
  //身份证15位(1984-2004)身份验证
  validId15: function(str) {
    if (str.length != 15) return false;
    //1. 出生日期验证
    let reg = new RegExp(/^(\d{6})(\d{2})(\d{2})(\d{2})(\d{3})$/);
    let arrSplit = str.match(reg); //检查生日日期是否正确
    if (arrSplit == null) {
      return false;
    } else {
      if (parseInt(arrSplit[2].substr(1)) > 0) {
        arrSplit[2] = '19' + arrSplit[2];
      } else {
        arrSplit[2] = '20' + arrSplit[2];
      }
      if (!this.yearMonthDayValidate(arrSplit[2], arrSplit[3], arrSplit[4])) {
        return false;
      }
    }
    return true;
  }
};
export default graceChecker;
