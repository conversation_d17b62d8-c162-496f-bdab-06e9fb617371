import request from './request.js';
import config from './setting.js';
export default {
  loginInfo: async function(opt) {
    let info = await this.getPersonalInfo(opt),
      data = info.object;
    if (
      config.ENABLE_IMPROVE_PERSON_INFO &&
      (!data.empIdcard || !data.empPhone)
    ) {
      uni.navigateTo({
        url: `/pages/personalCenter/settingPersonal/improve-information`
      });
    } else {
      let userInfo = {
        empid: data.id,
        username: data.empName,
        empsex: data.empSex,
        password: '',
        usercode: data.userAccounts,
        deptname: data.empDeptName,
        deptid: data.empDeptId,
        dutyname: data.empDutyName,
        headimg: data.empHeadImg,
        hasbind: true
      };
      uni.setStorageSync('_oa_user_key', JSON.stringify(userInfo));
      return userInfo;
    }
  },
  //获取个人信息,查询用户是否已完善信息
  getPersonalInfo: function(opt) {
    return request.asynRequest({
      api: `/ts-oa/employee/personalInformationSettings`,
      method: 'GET',
      data: {
        userCode: opt.userCode
      },
      showLoading: true,
      loadingTitle: '正在登录',
      hideLoading: true
    });
  }
};
