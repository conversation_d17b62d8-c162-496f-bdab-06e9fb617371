import common from './common.js';
const chooseImage = function(param_c) {
  uni.chooseImage({
    count: param_c.limitNum || 9,
    sizeType: param_c.sizeType || ['original', 'compressed'],
    sourceType: param_c.sourceType || ['album', 'camera'],
    success: res => {
      if (param_c.showLoading || param_c.showLoading == undefined) {
        common.loadingToast(param_c.loadingTitle || '上传中...');
      }
      uploadImg(res.tempFiles, param_c);
    },
    fail: err => {
      if (param_c.hideLoading) {
        uni.hideLoading();
      }
    }
  });
};

const uploadImg = function(filePath, param_u) {
  for (let i = 0; i < filePath.length; i++) {
    let path = filePath[i].path;
    let pathName = filePath[i].name;
    uni.uploadFile({
      url: param_u.uploadFileUrl, //开发者服务器地址
      name: param_u.fileKeyName, //开发者在服务器端通过这个 key 可以获取到文件二进制内容
      filePath: path,
      formData: param_u.formData || {},
      success: res => {
        if (param_u.hideLoading) {
          uni.hideLoading();
        }
        if (res.statusCode == 200) {
          //上传成功将原信息,直接删除,
          typeof param_u.success == 'function' && param_u.success(res.data);
        }
      },
      fail: err => {
        if (param_u.hideLoading) {
          uni.hideLoading();
        }
        common.toast(err.errMsg);
      }
    });
  }
};
const chooseFile = function(param_c) {
  uni.chooseFile({
    count: param_c.limitNum || 9, //在 H5 平台的表现，基于浏览器本身的规范。目前测试的结果来看，只能限制单选/多选，并不能限制数量。并且，在实际的手机浏览器很少有能够支持多选的
    type: param_c.type || 'all',
    extension: param_c.extension || [''], //暂只支持文件后缀名，例如['.zip','.exe','.js']，不支持application/msword等类似值
    sourceType: param_c.sourceType || ['album', 'camera'], //在H5端对应input的capture属性，设置为['album']无效，依然可以使用相机,仅在type为image或video时可用
    success: res => {
      if (param_c.showLoading || param_c.showLoading == undefined) {
        common.loadingToast(param_c.loadingTitle || '上传中...');
      }
      uploadImg(res.tempFiles, param_c);
    },
    fail: err => {
      if (param_c.hideLoading) {
        uni.hideLoading();
      }
    }
  });
};

export { chooseImage, chooseFile };
