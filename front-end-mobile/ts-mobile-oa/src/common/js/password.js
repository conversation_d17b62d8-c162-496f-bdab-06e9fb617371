export function pwdCheckStrong(val, length) {
  var length = length || 6;
  var checkType = {
    1: false,
    2: false,
    3: false,
    4: false
  };
  function CharMode(iN) {
    if (iN >= 48 && iN <= 57) {
      //数字
      checkType[3] = true;
      return 1;
    }
    if (iN >= 65 && iN <= 90) {
      //大写
      checkType[1] = true;
      return 2;
    }
    if (iN >= 97 && iN <= 122) {
      //小写
      checkType[2] = true;
      return 4;
    } else {
      checkType[4] = true;
      return 8;
    }
  }

  //计算密码模式
  function bitTotal(num) {
    var modes = 0;
    for (let i = 0; i < 4; i++) {
      if (num & 1) modes++;
      num >>>= 1;
    }
    return modes;
  }

  //返回强度级别
  function checkStrong(sPW) {
    if (sPW.length < length) return -1; //密码太短，不检测级别
    var Modes = 0;
    for (let i = 0; i < sPW.length; i++) {
      //密码模式
      Modes |= CharMode(sPW.charCodeAt(i));
    }
    return bitTotal(Modes);
  }
  var level = 0;
  if (!val) {
    level = 0;
  } else {
    level = checkStrong(val);
  }
  return {
    level: level,
    checkType: checkType
  };
}
