{"easycom": {"^u-(.*)": "@trasen-oa/trasen-uview-ui/components/u-$1/u-$1.vue"}, "pages": [{"path": "pages/index/index", "style": {"navigationBarTitleText": "消息"}}, {"path": "pages/deansDaily/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/deansDaily/details-page", "style": {"navigationStyle": "custom"}}, {"path": "pages/deansDaily/details-page-week", "style": {"navigationStyle": "custom"}}, {"path": "pages/deansDaily/details-page-month", "style": {"navigationStyle": "custom"}}, {"path": "pages/workBench/work-bench", "style": {"navigationBarTitleText": "工作台"}}, {"path": "pages/addressBook/address-book", "style": {"navigationBarTitleText": "通讯录"}}, {"path": "pages/personalCenter/personal-center", "style": {"navigationBarTitleText": "我的"}}, {"path": "pages/addressBook/groups", "style": {"navigationStyle": "custom"}}, {"path": "pages/addressBook/personal-info", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/edit/edit", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/history/history", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/improve-information", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingApproval/approval", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/setting/signature", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingApproval/process-agent", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingApproval/common-words", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/custom-groups", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/custom-groups-add", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/personal", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/setting/setting", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/privacy", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/setting/password", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/version", "style": {"navigationStyle": "custom"}}, {"path": "pages/personalCenter/settingPersonal/version-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/email-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/email-box-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/email-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/webview/webview", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/email-reply", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/email-send", "style": {"navigationStyle": "custom"}}, {"path": "pages/email/out-email-send", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/verification-by-pwd", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/payslip", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/file", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/department-file", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/new-department-file", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/add-document", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/add-file", "style": {"navigationStyle": "custom"}}, {"path": "pages/choose-person/choose-person", "style": {"navigationStyle": "custom"}}, {"path": "pages/choose-dept/choose-dept", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/personal-file", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/new-personal-file", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/class-rename", "style": {"navigationStyle": "custom"}}, {"path": "pages/file/dept-class-rename", "style": {"navigationStyle": "custom"}}, {"path": "pages/schedule/schedule-calendar", "style": {"navigationStyle": "custom"}}, {"path": "pages/schedule/add-schedule", "style": {"navigationStyle": "custom"}}, {"path": "pages/schedule/schedule-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/schedule/registration-info-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/information-access", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/unread-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/information-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/information-management", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/my-information-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/information/notice-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/officialDocument/document-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/init-information-workflow", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/workflow-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/init-custom-workflow", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-define", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/select-workflow-node", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/workflow-approval-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/approval-custom-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/approval-meeting-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/approval-information-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-add-nodes", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-sign-back", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-transfer", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-copy", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/approval-pass", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/approval-succeeded", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/approval-return", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/my-workflow-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/my-workflow-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/my-meeting-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/my-information-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/reinit-custom-workflow", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-handled-revoke", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-rescind", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-urge", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/operation/workflow-end", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/workflow-copy-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/workflow-access-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/workflow/init-meeting-workflow", "style": {"navigationStyle": "custom"}}, {"path": "pages/meeting/meeting-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/meeting/sign-in-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/meeting/sign-in-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/meeting/meeting-reference", "style": {"navigationStyle": "custom"}}, {"path": "pages/meeting/meeting-usage", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/salary-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/salary-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/salary-payslip-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/salary-payslip-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/payslip/salary-month-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/selectPerson/select-person", "style": {"navigationStyle": "custom"}}, {"path": "pages/selectDept/select-dept", "style": {"navigationStyle": "custom"}}, {"path": "pages/index/info-unread-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/index/email-unread-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/index/boardroom-signin-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/index/work-unread-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/index/govfile-unread-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/select-inspection-route", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/select-device", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/init-safety-supervision", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-check-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-check-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-check-review", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-rectification-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-rectification-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/safetySupervision/safety-rectification-review", "style": {"navigationStyle": "custom"}}, {"path": "pages/criticalValues/critical-values-list", "style": {"navigationStyle": "custom"}}, {"path": "pages/criticalValues/critical-values-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/trainManagement/my-train", "style": {"navigationStyle": "custom"}}, {"path": "pages/trainManagement/train-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/trainManagement/train-sign-page", "style": {"navigationStyle": "custom"}}, {"path": "pages/trainManagement/train-score", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/vehicle-reservation/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-reservation/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-approval/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-approval/components/pross-approval", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-delivery/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-delivery/components/add-delivery", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-vehicle-usage/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-vehicle-usage/components/departure-form", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/my-vehicle-usage/components/return-form", "style": {"navigationStyle": "custom"}}, {"path": "pages/vehicle/vehicle-detail/vehicle-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/talent-portrait/talent-portrait", "style": {"navigationStyle": "custom"}}, {"path": "pages/talent-portrait/dept-person-view", "style": {"navigationStyle": "custom"}}, {"path": "pages/talent-portrait/talent-details", "style": {"navigationStyle": "custom"}}, {"path": "pages/leaveStatistic/leave-statistic", "style": {"navigationStyle": "custom"}}, {"path": "pages/duty/duty-view/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/performance/index", "style": {"navigationStyle": "custom"}}, {"path": "pages/performance/question", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-registration", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-supervision", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-handling", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-copy", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/add-task", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-detail", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/task-all", "style": {"navigationStyle": "custom"}}, {"path": "pages/task/add-task-sup", "style": {"navigationStyle": "custom"}}, {"path": "pages/hospital-bed/index", "style": {"navigationStyle": "custom"}}], "globalStyle": {"navigationStyle": "custom", "navigationBarTextStyle": "black", "navigationBarBackgroundColor": "#FFFFFF", "backgroundColor": "#F8F8F8"}, "tabBar": {"color": "#666666", "selectedColor": "#005BAC", "backgroundColor": "#FFFFFF", "list": [{"pagePath": "pages/index/index", "iconPath": "static/img/msg.png", "selectedIconPath": "static/img/msg_active.png", "text": "消息"}, {"pagePath": "pages/workBench/work-bench", "iconPath": "static/img/workbench.png", "selectedIconPath": "static/img/workbench_active.png", "text": "工作台"}, {"pagePath": "pages/addressBook/address-book", "iconPath": "static/img/addressbook.png", "selectedIconPath": "static/img/addressbook_active.png", "text": "通讯录"}, {"pagePath": "pages/personalCenter/personal-center", "iconPath": "static/img/my.png", "selectedIconPath": "static/img/my_active.png", "text": "我的"}]}}