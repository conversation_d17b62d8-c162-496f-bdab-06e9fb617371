import request from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  /**@desc  获取科室下人员列表**/
  getDeptPersonList(data) {
    return request.post(
      `${apiConfig.hrms()}/schedulinggrouping/getPageAllList`,
      data
    );
  },
  /**@desc  获取科室排班列表**/
  getScheduleList(data) {
    return request.post(
      `${apiConfig.hrms()}/scheduleinfo/getSchedule`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  },
  /**@desc 获取科室列表 */
  getDeptSimpleList(data) {
    return request.post(
      `${apiConfig.basics()}/organization/pageSimple?pageNo=1&pageSize=999`,
      JSON.stringify(data),
      {
        header: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    );
  }
};
