const modulesFiles = require.context('./filters', true, /\.js$/);
const modules = modulesFiles.keys().reduce((modules, modulePath) => {
  const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1');
  modules[moduleName] = modulesFiles(modulePath).default;
  return modules;
}, {});

export default {
  install(Vue) {
    Object.keys(modules).forEach(key => {
      Object.keys(modules[key]).forEach(_key => {
        Vue.filter(_key, modules[key][_key]);
      });
    });
  }
};
