<template>
  <view class="base-filter-popup">
    <u-popup :mode="mode" v-model="vaule">
      <slot></slot>
    </u-popup>
  </view>
</template>
<script>
export default {
  name: 'base-filter-popup',
  props: {
    /**
     * 弹出方向，left|right|top|bottom|center
     */
    mode: {
      type: String,
      default: 'right'
    },
    vaule: {
      type: Boolean,
      default: false
    },
    /**
     * 是否显示遮罩
     */
    mask: {
      type: Boolean,
      default: true
    }
  }
};
</script>
