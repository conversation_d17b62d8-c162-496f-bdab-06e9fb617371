<template>
  <view class="base-checkbox">
    <u-popup
      :maskCloseAble="maskCloseAble"
      mode="bottom"
      :popup="false"
      v-model="value"
      length="auto"
      :safeAreaInsetBottom="safeAreaInsetBottom"
      @close="close"
      :z-index="uZIndex"
    >
      <view class="base-checkbox">
        <view class="u-select__header" @touchmove.stop.prevent="">
          <view
            class="u-select__header__confirm u-select__header__btn"
            :style="{ color: cancelColor }"
            hover-class="u-hover-class"
            :hover-stay-time="150"
            @tap="getResult('cancel')"
          >
            {{ cancelText }}
          </view>
          <view class="u-select__header__title">{{ title }}</view>
          <view
            class="u-select__header__confirm u-select__header__btn"
            :style="{ color: confirmColor }"
            hover-class="u-hover-class"
            :hover-stay-time="150"
            @touchmove.stop=""
            @tap.stop="getResult('confirm')"
          >
            {{ confirmText }}
          </view>
        </view>
        <view class="base-checkbox__body">
          <u-checkbox-group :wrap="true" @change="checkboxGroupChange">
            <u-checkbox
              @change="checkboxChange"
              v-model="item.checked"
              v-for="(item, index) in list"
              :key="index"
              :name="item[valueName]"
              :icon-position="checkboxIconPosition"
              :icon="checkboxIcon"
              :showActiveIconBackgroundColor="false"
              :iconSize="36"
            >
              {{ item[labelName] }}
              <!-- <template
                v-if="$slots.default || $slots.$default"
                slot-scope="list"
              >
                <slot :row="scope.row" :list="list" :index="index"></slot>
              </template> -->
            </u-checkbox>
          </u-checkbox-group>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
/**
 * base-checkbox 列选择器
 * @description 此组件应用于多选场景。
 * @property {Array} list 列数据，数组形式
 * @property {Boolean} v-model 布尔值变量，用于控制选择器的弹出与收起
 * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配(默认false)
 * @property {String} cancel-color 取消按钮的颜色（默认#606266）
 * @property {String} confirm-color 确认按钮的颜色(默认#005bac)
 * @property {String} confirm-text 确认按钮的文字
 * @property {String} cancel-text 取消按钮的文字
 * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker(默认true)
 * @property {String Number} z-index 弹出时的z-index值(默认10075)
 * @property {String} value-name 自定义list数据的value属性名 1.3.6
 * @property {String} label-name 自定义list数据的label属性名 1.3.6
 * @event {Function} confirm 点击确定按钮，返回当前选择的值
 * @example <u-select v-model="show" :list="list"></u-select>
 */
export default {
  name: 'base-checkbox',
  props: {
    // 列数据
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    // 通过双向绑定控制组件的弹出与收起
    value: {
      type: Boolean,
      default: false
    },
    // "取消"按钮的颜色
    cancelColor: {
      type: String,
      default: '#606266'
    },
    // "确定"按钮的颜色
    confirmColor: {
      type: String,
      default: '#005bac'
    },
    // 弹出的z-index值
    zIndex: {
      type: [String, Number],
      default: 0
    },
    safeAreaInsetBottom: {
      type: Boolean,
      default: false
    },
    // 是否允许通过点击遮罩关闭Picker
    maskCloseAble: {
      type: Boolean,
      default: true
    },
    // 自定义value属性名
    valueName: {
      type: String,
      default: 'value'
    },
    // 自定义label属性名
    labelName: {
      type: String,
      default: 'label'
    },
    // 顶部标题
    title: {
      type: String,
      default: ''
    },
    // 取消按钮的文字
    cancelText: {
      type: String,
      default: '取消'
    },
    // 确认按钮的文字
    confirmText: {
      type: String,
      default: '确认'
    },
    //复选框图标名称
    checkboxIcon: {
      type: String,
      default: 'checkbox-mark'
    },
    //复选框图标位置
    checkboxIconPosition: {
      type: String,
      default: 'right'
    },
    //复选框激活时的颜色
    checkboxActiveColor: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      //checkbox状态发生变化后，保存选中的值
      selectValue: [],
      selectLabel: []
    };
  },
  computed: {
    uZIndex() {
      // 如果用户有传递z-index值，优先使用
      return this.zIndex ? this.zIndex : this.$u.zIndex.popup;
    }
  },
  methods: {
    checkboxChange(e) {},
    checkboxGroupChange(e) {
      this.selectValue = e;
      this.selectLabel = [];
      this.list.map(item => {
        if (e.some(one => one == item[this.valueName])) {
          this.selectLabel.push(item[this.labelName]);
        }
      });
    },
    close() {
      this.$emit('input', false);
    },
    // 点击确定或者取消
    getResult(event = null) {
      // #ifdef MP-WEIXIN
      if (this.moving) return;
      // #endif
      if (event) this.$emit(event, this.selectValue, this.selectLabel);
      this.close();
    }
  }
};
</script>

<style lang="scss" scoped>
.base-checkbox {
  .u-select__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    height: 80rpx;
    padding: 0 30rpx;
  }
  .base-checkbox__body {
    width: 100%;
    max-height: 716rpx;
    height: auto;
    overflow: auto;
    background-color: #fff;
    /deep/ .u-checkbox-group {
      width: 100%;
    }
    /deep/ .u-checkbox {
      padding: $uni-spacing-row-base $uni-spacing-row-lg;
      position: relative;
      & ::after {
        position: absolute;
        content: '';
        bottom: 0;
        right: 0;
        left: 15px;
        height: 1px;
        background-color: $uni-border-color;
      }
    }
  }
}
</style>
