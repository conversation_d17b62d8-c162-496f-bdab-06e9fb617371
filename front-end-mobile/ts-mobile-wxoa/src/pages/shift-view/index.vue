<template>
  <view class="ts-container">
    <u-navbar title="排班" title-bold></u-navbar>
    <trasen-calendar ref="calender" @change="refresh"></trasen-calendar>
    <view class="dept-cell" @click="handleShowDeptCheck">
      <view>
        {{ dept.name }}
      </view>
      <u-icon name="arrow-right"></u-icon>
    </view>

    <view v-for="(item, index) of personList" :key="index" class="person-item">
      <view class="person-info">
        <view>{{ item.employeeName }}</view>
        <view>工号：{{ item.employeeNo }}</view>
      </view>
      <view
        v-for="(shift, shiftIndex) of item.shiftList"
        :key="shiftIndex"
        class="shift-item"
      >
        <view>{{ shift.frequencyName }}</view>
        <view>{{ shift.frequencyTime }}</view>
      </view>
      <view v-if="!item.shiftList || !item.shiftList.length" class="shift-item">
        无排班
      </view>
    </view>

    <u-popup
      v-model="showDeptChoose"
      :closeable="false"
      mode="bottom"
      :safe-area-inset-bottom="true"
      height="50%"
    >
      <view class="action-content">
        <view class="quit-btn" @click="showDeptChoose = false">取消</view>
        <view class="input-content">
          <u-search
            shape="square"
            v-model="deptSearchName"
            :show-action="false"
            placeholder="输入关键字搜索"
            @search="handleSearchDept"
            @clear="handleSearchDept"
          ></u-search>
        </view>

        <view class="search-btn" @click="handleSearchDept">确定</view>
      </view>
      <view class="dept-list">
        <view
          v-for="(item, index) of deptList"
          :key="index"
          class="dept-item"
          @click="handleSelectDept(item)"
        >
          <view>
            {{ item.name }}
          </view>
          <u-icon v-if="dept.id == item.id" name="checkmark"></u-icon>
        </view>
      </view>
    </u-popup>
  </view>
</template>

<script>
import index from './index.js';
import trasenCalendar from './component/trasen-calendar.vue';

export default {
  name: 'shift-view',
  mixins: [index],
  components: {
    trasenCalendar
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  height: 100%;
  overflow: hidden auto;
}
.dept-cell {
  margin: 16rpx 0;
  padding: 0 16px;
  background-color: #fff;
  line-height: 88rpx;
  display: flex;
  justify-content: space-between;
  color: $u-main-color;
  font-size: 32rpx;
  font-weight: 600;
  .u-icon {
    color: #a0a8b4;
  }
}
.input-content,
.action-content {
  display: flex;
  align-items: center;
}
.action-content {
  position: fixed;
  top: 0;
  z-index: 9;
  background-color: #fff;
  width: 100vw;
  box-shadow: 0px 1px 0px 0px #dddddd;
}
.quit-btn {
  color: $u-tips-color;
  font-size: 32rpx;
  line-height: 44px;
  margin: 0 16px;
}
.search-btn {
  color: $u-type-primary;
  font-size: 32rpx;
  line-height: 44px;
  margin: 0 16px;
}
.input-content {
  flex: 1;
  background-color: #f8f8f8;
  height: 60rpx;
  border-radius: 6px;
  .u-icon {
    color: $u-tips-color;
    margin: 0 8px;
  }
}
.dept-list {
  padding-top: 44px;
}
.dept-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 0 16px;
  font-size: 32rpx;
  color: $u-main-color;
  line-height: 44rpx;
  height: 44px;
  border-bottom: 1px solid #e6e6e6;
  .u-icon {
    color: $u-type-primary;
  }
}
.person-item {
  background-color: #fff;
  margin: 8px 0;
  padding: 0 16px;
}
.person-info {
  display: flex;
  justify-content: space-between;
  font-size: 28rpx;
  align-items: center;
  height: 44px;
}
.shift-item {
  border-top: 1px solid $u-bg-color;
  color: $u-tips-color;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 44px;
}
</style>
