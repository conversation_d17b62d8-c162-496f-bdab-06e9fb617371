export default {
  data() {
    return {
      fromPage: '',
      tabList: [
        {
          name: '待组织',
          status: 1,
          count: 0,
          path: '/pages/political/political-reporting/index',
          list: []
        },
        {
          name: '已完结',
          status: 2,
          count: 0,
          path: '/pages/political/political-detail/index',
          list: []
        },
        {
          name: '草稿',
          status: 0,
          count: 0,
          path: '/pages/political/political-detail/index',
          list: []
        }
      ],
      currentTab: 0,
      isShowScreen: false,
      screenFormList: [],
      screenForm: {
        politicalName: '',
        startDate: '',
        endDate: ''
      }
    };
  },
  onLoad(opt) {
    if (opt && opt.fromPage) {
      this.fromPage = opt.fromPage;
    }
    if (opt && opt.status) {
      this.currentTab = opt.status == '0' ? 2 : opt.status == '2' ? 1 : 0;
    }
  },
  methods: {
    search(val) {
      this.keywords = val;
      this.$nextTick(() => {
        this.tabList.map((item, index) => {
          this.datasInit(index);
          this.$refs[`mescroll${index}`][0].downCallback();
        });
      });
    },
    clear() {},
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    getListData(page, successCallback, errorCallback, index) {
      this.ajax
        .getPoliticalManagementList({
          pageNo: page.num,
          pageSize: page.size,
          politicalName: this.screenForm.politicalName,
          startDate: this.screenForm.startDate,
          endDate: this.screenForm.endDate,
          status: this.tabList[index].status,
          sord: 'desc',
          sidx: 'create_date'
        })
        .then(res => {
          successCallback(res.rows, res.totalCount, index);
        })
        .catch(err => {
          errorCallback();
        });
    },
    setListData(rows, totalCount, index) {
      this.tabList[index]['list'] = this.tabList[index]['list'].concat(rows);
      this.tabList[index]['count'] = totalCount;
    },
    datasInit(index) {
      this.tabList[index]['list'] = [];
    },
    jumpToDetail(id, path) {
      uni.navigateTo({
        url: `${path}?fromPage=political-management-list&formId=${id}`
      });
    },
    goBack() {
      if (this.fromPage == 'workBench' || this.fromPage == '') {
        this.jumpToWorkBench();
      }
    },
    jumpToWorkBench() {
      this.$parentTypeFun({
        type: 'redirectTo',
        path: '/workbench'
      });
    },
    toggleScreenInfo() {
      this.isShowScreen = true;
    }
  }
};
