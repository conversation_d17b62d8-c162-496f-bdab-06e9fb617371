<template>
  <view class="resource-file file-list">
    <view
      class="file-item"
      v-for="(item, index) in list"
      :key="index"
      @click="onClick(item)"
    >
      <view class="item-left">
        <u-icon
          :name="icon"
          custom-prefix="work-icon"
          :size="iconSize"
          :color="iconColor"
        ></u-icon>
        <slot name="left"></slot>
      </view>
      <view class="item-content">
        <view
          class="item-content-title"
          :style="{ 'font-size': `${titleSize}rpx`, color: titleColor }"
        >
          {{ item.title || item.fkFileName }}
        </view>
        <view class="item-content-note">
          <slot></slot>
        </view>
      </view>
      <view class="item-right">
        <slot name="right" :item="item"></slot>
      </view>
    </view>
  </view>
</template>

<script>
/**
 * resourceFile 图片列表
 * @description 列表组件
 */
export default {
  name: 'resource-file',
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    titleStyle: {
      type: String,
      default: ''
    },
    titleColor: {
      type: String,
      default: '#333333'
    },
    titleSize: {
      type: [String, Number],
      default: '28'
    },
    icon: {
      type: String,
      default: 'fujian'
    },
    iconSize: {
      type: [String, Number],
      default: '28'
    },
    iconColor: {
      type: String,
      default: '#666666'
    }
  },
  methods: {
    onClick(e) {
      this.$emit('click', e);
    }
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
@import '@/assets/css/ellipsis.scss';
.file-item {
  @include vue-flex;
  align-items: center;
}
.item-content {
  margin: 0 4px;
  flex: 1;
  min-width: 0;
}
.item-content-title {
  @include ellipsis;
}
</style>
