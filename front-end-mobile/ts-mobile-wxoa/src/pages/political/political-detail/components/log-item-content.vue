<template>
  <view class="log-item-content">
    <view class="item-content">
      <text class="item-log-node">{{ `【${content.operationContent}】` }}</text>
      <text class="item-log-operation">{{
        `操作人：${content.operationUserName} ${content.createDate}`
      }}</text>
    </view>
    <view>
      <resource-image
        v-if="content.imgList.length > 0"
        :list="content.imgList"
      ></resource-image>

      <resource-file
        v-if="content.fileList.length > 0"
        :list="content.fileList"
        :titleSize="28"
      >
        <template #right="{item}">
          <view class="file-operation-button">
            <text
              class="file-operation-button-item"
              @click="downloadFile(item)"
            >
              下载
            </text>
            <text class="file-operation-button-item" @click="previewFile(item)">
              预览
            </text>
          </view>
        </template>
      </resource-file>
    </view>
  </view>
</template>

<script>
import resourceImage from './resource-image.vue';
import resourceFile from './resource-file.vue';
import Base64 from '@/assets/js/base64.min.js';

export default {
  name: 'log-item-content',
  components: {
    resourceImage,
    resourceFile
  },
  props: {
    content: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  methods: {
    downloadFile(e) {
      // #ifdef H5
      let el = document.createElement('a');
      el.href = e.fileUrl;
      el.target = '_blank';
      el.download = e.fkFileName;
      document.body.appendChild(el);
      el.click();
      el.remove();
      // #endif
      // #ifndef H5
      uni.downloadFile({
        url: e.src,
        success: res => {
          if (res.statusCode === 200) {
            uni.saveFile({
              tempFilePath: res.tempFilePaths,
              success: function(data) {
                this.$u.toast('下载完成');
              }
            });
          }
        }
      });
      // #endif
    },
    //文件预览
    previewFile(e) {
      let filePath = `${this.$documentPreviewHost}${e.fileUrl}?fullfilename=${e.fkFileId}.${e.fileSuffix}`;
      uni.navigateTo({
        url: `/pages/webview/index?url=${
          this.$store.state.common.baseHost
        }/ts-preview/onlinePreview?url=${Base64.encode(filePath)}`
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.item-log-node {
  font-size: $uni-font-size-base;
  font-weight: bold;
  color: $uni-text-content-color;
}
.item-log-operation,
.item-log-remark {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
.item-log-remark {
  margin-bottom: $uni-spacing-col-lg;
}
.file-operation-button {
  font-size: $uni-font-size-base;
  color: $uni-color-primary;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
</style>
