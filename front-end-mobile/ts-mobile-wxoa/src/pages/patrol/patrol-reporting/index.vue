<template>
  <view class="ts-container">
    <u-navbar :title="title" title-bold></u-navbar>
    <view class="init-patrol-content-box">
      <view class="content-top">
        <view class="top-title">{{ form.patrolName }}</view>
        <view class="top-info">
          <u-icon
            custom-prefix="wxoa-icon"
            name="dingwei"
            color="#005bac"
            size="28"
          ></u-icon>
          <text class="top-info-text">{{ `巡查地点：${patrolLocation}` }}</text>
        </view>
        <view class="top-info">
          <u-icon
            custom-prefix="wxoa-icon"
            name="duoren"
            color="#005bac"
            size="28"
          ></u-icon>
          <text class="top-info-text">
            {{
              `巡查人(记录人)：${$store.state.common.userInfo.orgName}-${$store.state.common.userInfo.employeeName}`
            }}
          </text>
        </view>
      </view>
      <view class="content-box">
        <base-form
          ref="baseForm"
          :form-list="formList"
          :form-data.sync="form"
          :rules="rules"
          :show-submit-button="false"
          @perviewFile="handlePreview"
          @deletFile="handleDeletFile"
        ></base-form>
      </view>
      <view class="content-box form-info-box">
        <view
          class="content-item"
          v-for="(item, index) in patrolFormList"
          :key="item.patrolItemId"
        >
          <view class="item-title">
            {{ `${index + 1}、${item.patrolItemName}` }}
          </view>
          <base-form
            :ref="`baseForm_${item.patrolItemId}`"
            :form-list="item.formList"
            :form-data.sync="item.formData"
            :rules="item.formRule"
            :show-submit-button="false"
            @perviewFile="handlePreview"
            @deletFile="handleDeletFile"
          ></base-form>
        </view>
      </view>
    </view>
    <view class="init-patrol-operation-box">
      <view
        class="operation-button-item"
        v-for="(item, index) in showButtons"
        :key="index"
        :class="item.colorClass"
        @click="onClick(item)"
      >
        {{ item.name }}
      </view>
    </view>
  </view>
</template>
<script>
import index from './index.js';
import baseForm from '@/components/base-form/base-form.vue';
export default {
  name: 'init-patrol',
  mixins: [index],
  components: {
    baseForm
  }
};
</script>

<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
@import '@/assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.init-patrol-content-box {
  flex: 1;
  overflow: auto;
}
.content-top {
  padding: 0 $uni-spacing-row-lg;
  background-color: $uni-bg-color;
  margin-bottom: $uni-spacing-col-base;
}
.top-title {
  font-size: $uni-font-size-base;
  color: $uni-text-color;
  line-height: 1;
  padding: $uni-spacing-col-base 0;
}
.top-info {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
  line-height: 1;
  padding: 0;
  padding-bottom: $uni-spacing-col-base;
  .top-info-text {
    margin-left: $uni-spacing-row-xsm;
  }
}
.init-patrol-operation-box {
  @include vue-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ccc;
  width: 100%;
}
.operation-button-item {
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  height: 44px;
  line-height: 44px;
  flex: 1;
}
.normal-color {
  color: $uni-color-primary;
}
.content-box {
  margin-top: $uni-spacing-col-base;
}
.content-item .item-title {
  font-size: $uni-font-size-base;
  background-color: $uni-bg-color;
  margin: 0;
  padding: $uni-spacing-col-base $uni-spacing-row-lg 0;
}
.form-info-box .form /deep/ .form-container {
  margin: 0;
}
.form-info-box .form /deep/ .u-form-item--left__content__label {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
</style>
