<template>
  <view class="ts-container">
    <u-navbar title="巡查详情" title-bold title-width="500"></u-navbar>
    <view class="patrol-content-box">
      <base-tabs-swiper
        ref="tabSwiper"
        class="tab-swiper-box"
        :list="list"
        :current="currentTab"
        :is-scroll="false"
        @change="changeTab"
      ></base-tabs-swiper>
      <swiper
        class="swiper-box"
        :current="currentTab"
        :duration="300"
        @change="onTabChange"
      >
        <swiper-item class="swiper-item">
          <view class="patrol-form-box">
            <view class="patrol-info-top">
              <view class="top-title">{{ baseInfo.patrolName }}</view>
              <view class="top-info">
                <u-icon
                  custom-prefix="wxoa-icon"
                  name="dingwei"
                  color="#005bac"
                  size="28"
                ></u-icon>
                <text class="top-info-text">
                  {{ `地点：${baseInfo.location}` }}
                </text>
              </view>
              <view class="top-info">
                <u-icon
                  custom-prefix="wxoa-icon"
                  name="rili"
                  color="#005bac"
                  size="28"
                ></u-icon>
                <text class="top-info-text">
                  {{ `巡查日期：${baseInfo.checkDate}` }}
                </text>
              </view>
              <view class="top-info" v-if="baseInfo.checkUserName">
                <u-icon
                  custom-prefix="wxoa-icon"
                  name="duoren"
                  color="#005bac"
                  size="28"
                ></u-icon>
                <text class="top-info-text">
                  {{ `巡查人（记录人）：${baseInfo.checkUserName}` }}
                </text>
              </view>
            </view>
            <view class="content-box form-info-box">
              <view
                class="content-item"
                v-for="(item, index) in patrolFormList"
                :key="item.patrolItemId"
              >
                <view class="item-title">
                  {{ `${index + 1}、${item.patrolItemName}` }}
                </view>
                <base-form
                  :ref="`baseForm_${item.patrolItemId}`"
                  :form-list="item.formList"
                  :form-data.sync="item.formData"
                  :rules="item.formRule"
                  :disabled="true"
                  :show-submit-button="false"
                  @perviewFile="handlePreview"
                  @deletFile="handleDeletFile"
                ></base-form>
              </view>
            </view>
          </view>
        </swiper-item>
        <swiper-item class="swiper-item">
          <view class="log-box">
            <base-log-list>
              <base-log-list-item v-for="(item, index) in logList" :key="index">
                <log-item-content :content="item"></log-item-content>
              </base-log-list-item>
            </base-log-list>
          </view>
        </swiper-item>
      </swiper>
    </view>
    <view class="patrol-operation-box">
      <view
        class="operation-button-item"
        v-for="(item, index) in showButtons"
        :key="index"
        :class="item.colorClass"
        @click="clickButton(item)"
      >
        {{ item.name }}
      </view>
    </view>
  </view>
</template>

<script>
import index from './index.js';
import logItemContent from './components/log-item-content.vue';
export default {
  name: 'patrol-detail',
  mixins: [index],
  components: {
    logItemContent
  }
};
</script>
<style lang="scss" scoped>
@import '@/assets/css/flex.scss';
@import '@/assets/css/ellipsis.scss';
.ts-container {
  height: 100%;
  @include vue-flex(column);
}
.swiper-box {
  flex: 1;
  margin: $uni-spacing-col-base 0;
}
.patrol-content-box {
  flex: 1;
  @include vue-flex(column);
}
.patrol-form-box,
.log-box {
  overflow: auto;
  height: 100%;
}
.patrol-info-top {
  padding: 0 $uni-spacing-row-lg;
  background-color: $uni-bg-color;
  margin-bottom: $uni-spacing-col-base;
}
.top-title {
  font-size: $uni-font-size-lg;
  color: $uni-text-color;
  line-height: 1;
  padding: $uni-spacing-col-base 0;
}
.top-info {
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  line-height: 1;
  padding: 0;
  padding-bottom: $uni-spacing-col-base;
  .top-info-text {
    margin-left: $uni-spacing-row-xsm;
  }
}
.content-box {
  margin-top: $uni-spacing-col-base;
}
.content-item .item-title {
  font-size: $uni-font-size-base;
  background-color: $uni-bg-color;
  margin: 0;
  padding: $uni-spacing-col-base $uni-spacing-row-lg 0;
}
.form-info-box .form /deep/ .form-container {
  margin: 0;
}
.form-info-box .form /deep/ .u-form-item--left__content__label {
  font-size: $uni-font-size-sm;
  color: $uni-text-content-color;
}
.log-box {
  background-color: #ffffff;
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.patrol-operation-box {
  @include vue-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ccc;
  width: 100%;
}
.patrol-operation-box {
  @include vue-flex;
  align-items: center;
  justify-content: space-around;
  background-color: #ffffff;
  box-shadow: 0 1px 6px #ccc;
  width: 100%;
}
.operation-button-item {
  box-sizing: border-box;
  text-align: center;
  border-radius: 8px;
  font-size: $uni-font-size-base;
  color: $uni-text-content-color;
  height: 44px;
  line-height: 44px;
  flex: 1;
}
.normal-color {
  color: $uni-color-primary;
}
</style>
