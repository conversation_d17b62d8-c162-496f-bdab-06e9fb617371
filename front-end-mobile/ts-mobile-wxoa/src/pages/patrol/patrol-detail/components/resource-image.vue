<template>
  <view class="resource-image image-list">
    <view
      class="image-item"
      :style="imgStyle"
      v-for="(item, index) in list"
      :key="`image${index}`"
    >
      <image
        :src="item.url"
        mode="aspectFill"
        @click.stop="doPreviewImage(item.url, index)"
      ></image>
      <slot></slot>
    </view>
  </view>
</template>

<script>
/**
 * resourceImage 图片列表
 * @description 列表组件
 */
export default {
  name: 'resource-image',
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    imgStyle: {
      type: String,
      default: ''
    },
    // 是否在点击预览图后展示全屏图片预览
    previewFullImage: {
      type: <PERSON>olean,
      default: true
    }
  },
  methods: {
    doPreviewImage(url, index) {
      if (!this.previewFullImage) return;
      const images = this.list.map(item => item.src || item.url);
      uni.previewImage({
        urls: images,
        current: url,
        success: () => {
          this.$emit('on-preview', url, this.list, this.index);
        },
        fail: () => {
          uni.showToast({
            title: '预览图片失败',
            icon: 'none'
          });
        }
      });
      this.$emit('click');
    }
  }
};
</script>

<style lang="scss" scoped>
.image-list {
  width: 100%;
  background-color: #ffffff;
  // overflow-x: scroll;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
}
.image-item {
  width: 176rpx;
  height: 176rpx;
  margin: 4px;
  border: 1px solid #eee;
  border-radius: 2px;
  overflow: hidden;
  position: relative;
  display: inline-block;
}
.image-item image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  image-rendering: -webkit-optimize-contrast;
}
</style>
