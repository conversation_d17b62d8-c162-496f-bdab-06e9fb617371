export default {
  data() {
    return {
      fromPage: '',
      currentTab: 0,
      list: [
        {
          name: '基本信息'
        },
        {
          name: '操作日志'
        }
      ],
      baseInfo: {
        patrolName: '',
        location: '',
        checkDate: '',
        inspectedDepartmentName: '',
        checkUserName: ''
      },
      patrolFormList: [],
      logList: [],
      operationButtons: {
        delete: {
          name: '删除',
          type: 'patrolDelete',
          isShow: false,
          colorClass: 'normal-color'
        },
        edit: {
          name: '编辑',
          page: '/pages/patrol/patrol-reporting/index',
          isShow: false,
          colorClass: 'normal-color'
        }
      },
      showButtons: []
    };
  },
  onLoad(opt) {
    this.fromPage = opt.fromPage;
    this.getPatrolDetail(opt.formId);
    this.getPatrolLogList(opt.formId);
  },
  methods: {
    changeTab(index) {
      this.currentTab = index;
    },
    onTabChange(e) {
      this.currentTab = e.target.current || e.detail.current;
    },
    async getPatrolDetail(id) {
      await this.ajax.getPatrolDetail(id).then(async res => {
        this.baseInfo = { ...this.baseInfo, ...res.object };
        this.setOperationButtons(res.object);
        this.patrolFormList = await Promise.all(
          res.object.itemList.map(async item => {
            let itemObj = {};
            itemObj.patrolItemName = item.patrolItemName;
            itemObj.patrolItemId = item.id;
            itemObj.rectificationFollowUp = item.rectificationFollowUp;
            Object.assign(itemObj, await this.formatForm(item.itemChildList));
            return itemObj;
          })
        );
      });
    },
    setOperationButtons(info) {
      if (info.status == 0 || info.status == 2) {
        this.$set(this.operationButtons['edit'], 'isShow', true);
        this.$set(this.operationButtons['delete'], 'isShow', true);
      }
      this.showButtons = [];
      Object.keys(this.operationButtons).forEach(item => {
        if (this.operationButtons[item]['isShow']) {
          this.showButtons.push(this.operationButtons[item]);
        }
      });
    },
    //格式化数据
    async formatForm(list) {
      let formData = {},
        formRule = {},
        formList = await Promise.all(
          list.map(async item => {
            let obj = await this.formatField(item),
              fieldObj = {
                title: item.itemChildDescribe,
                id: item.id,
                required: item.itemChildRequired == 'Y'
              };
            Object.assign(fieldObj, obj.fieldAttr);
            Object.assign(formData, obj.fieldData);
            Object.assign(formRule, obj.fieldRule);
            return fieldObj;
          })
        );
      return {
        formList,
        formData,
        formRule
      };
    },
    async formatField(item) {
      let _self = this,
        fieldAttr = {},
        fieldData = {},
        fieldRule = {};
      fieldAttr.labelPosition = 'top';
      switch (item.itemChildType) {
        case 'DX':
          fieldAttr.type = 'select';
          fieldAttr.mode = 'select';
          fieldAttr.placeholder = '';
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = `${item.id}`;
          fieldAttr.disabled = true;
          fieldAttr.optionList = [];
          let selectOption = item.itemChildContent.split(',');
          fieldAttr.optionList = selectOption.map(one => {
            return {
              label: one,
              value: one
            };
          });

          fieldData[`${item.id}_nameVal`] = item.checkValue;
          fieldData[`${item.id}`] = item.checkValue;

          //设置字段校验规则
          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'FX':
          fieldAttr.type = 'select';
          fieldAttr.mode = 'checkbox';
          fieldAttr.placeholder = '';
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = `${item.id}`;
          fieldAttr.disabled = true;
          fieldAttr.optionList = [];
          let checkboxOption = item.itemChildContent.split(',');
          fieldAttr.optionList = checkboxOption.map(one => {
            return {
              label: one,
              value: one,
              checked: false,
              disabled: false
            };
          });

          fieldData[`${item.id}_nameVal`] = item.checkValue;
          fieldData[`${item.id}`] = item.checkValue;

          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'SRK':
          fieldAttr.type = 'text';
          fieldAttr.placeholder = '';
          fieldAttr.propVal = item.id;
          fieldAttr.disabled = true;

          fieldData[`${item.id}`] = item.checkValue;

          fieldRule[`${item.id}`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}`].push({
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
        case 'FJ':
          fieldAttr.baseType = 'base'; //附件上传模式，“base”-businessId关联文件模式，“fileList”-上传文件列表模式
          fieldAttr.type = 'file';
          fieldAttr.mode = 'image'; //文件类型，“all”-全部文件类型，“image”-仅图片类型
          fieldAttr.placeholder = '';
          fieldAttr.prop = `${item.id}_nameVal`;
          fieldAttr.propVal = item.id;
          fieldAttr.imgVal = `${item.id}_imgVal`;
          fieldAttr.fileVal = `${item.id}_fileVal`;
          fieldAttr.disabledUpload = true;
          fieldAttr.deletable = false;
          fieldAttr.name = 'file';
          fieldAttr.header = {
            token: _self.$store.state.common.token
          };
          fieldAttr.action = '/ts-basics-bottom/fileAttachment/upload?moduleName=oa';
          fieldAttr.deletAction =
            '/ts-basics-bottom/fileAttachment/deleteFileId';
          let businessId = item.checkValue || _self.$tools.guid();
          fieldAttr.formData = {
            businessId
          };
          fieldData[`${item.id}`] = businessId;
          let list = await _self.getFileList(businessId);
          fieldData[`${item.id}_imgVal`] = list.imgList;
          fieldData[`${item.id}_fileVal`] = list.fileList;
          fieldData[`${item.id}_nameVal`] = [...list.fileList, ...list.imgList];

          fieldRule[`${item.id}_nameVal`] = [];
          if (item.itemChildRequired == 'Y') {
            fieldRule[`${item.id}_nameVal`].push({
              // type: 'array',
              required: true,
              message: fieldAttr.placeholder,
              trigger: ''
            });
          }
          break;
      }
      return {
        fieldAttr,
        fieldData,
        fieldRule
      };
    },
    async getPatrolLogList(id) {
      await this.ajax.getPatrolLogList(id).then(async res => {
        this.logList = await Promise.all(
          res.object.map(async item => {
            let fileObj = { fileList: [], imgList: [] };
            if (item.businessId) {
              fileObj = await this.getFileList(item.businessId);
            }
            item = { ...fileObj, ...item };
            return item;
          })
        );
      });
    },
    //获取文件列表
    async getFileList(businessId) {
      let listObj = { fileList: [], imgList: [] };
      await this.ajax
        .getFileAttachmentByBusinessId({
          businessId: businessId
        })
        .then(async res => {
          await Promise.all(
            res.object.map(i => {
              let item = {
                url: `${this.$store.state.common.baseHost}${i.realPath}`,
                fileUrl: i.realPath,
                fkFileId: i.id,
                fkFileName: i.originalName
              };
              if (/gif|jpg|jpeg|png|bmp/i.test(i.fileExtension.toLowerCase())) {
                listObj.imgList.push(item);
              } else listObj.fileList.push(item);
            })
          );
        });
      return listObj;
    },
    //按钮点击
    async clickButton(item) {
      if (item.page) {
        this.jumpPage(item.page);
      } else {
        await this[item.type]();
      }
    },
    jumpPage(page) {
      uni.navigateTo({
        url: `${page}?formId=${this.baseInfo.id}`
      });
    },
    async patrolDelete() {
      this.ajax.deletePatrol(this.baseInfo.id).then(res => {
        uni.reLaunch({
          url: `/pages/patrol/patrol-management-list/index?tabIndex=${this.currentTab}`
        });
      });
    }
  }
};
