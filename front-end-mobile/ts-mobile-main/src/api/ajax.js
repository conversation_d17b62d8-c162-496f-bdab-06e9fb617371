import axios from 'axios';
import store from '@/store';
import qs from 'qs';
import { Toast } from 'vant';

let options = {
  axios,
  store,
  qs,
  Toast
};
let timeout = options.timeout || 120 * 1000;
let maxtimer = 3000;
let headers = Object.assign(
  {
    'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
    'x-requested-with': 'XMLHttpRequest'
  },
  options.headers
);
let downLoadTimer = true;
const request = options.axios.create({ timeout, headers });
const goToLogin = function() {
  const getCurrentPagePath = location.href;
  if (location.pathname.indexOf('/login') != -1) {
    return;
  }
  let hrefPath = '',
    toastIcon = '',
    toastTitle = '';
  const isMustAccountLogin =
    options.store.state.common.globalSetting.accountLogin;
  if (isMustAccountLogin) {
    hrefPath = `./login?returnURL=${getCurrentPagePath}`;
    toastIcon = 'text';
    toastTitle = '登录失效，请重新登录';
  } else {
    hrefPath = `/ts-information/cp/weixin/wxOAuth2Url?url=${getCurrentPagePath}`;
    toastIcon = 'loading';
    toastTitle = '登录失效，正在重新登录';
  }
  Toast({
    type: toastIcon,
    message: toastTitle,
    forbidClick: true,
    loadingType: 'spinner',
    onClose: function() {
      setTimeout(function() {
        location.href = hrefPath;
      });
    }
  });
};
/**@desc 在请求前做的操作**/
request.interceptors.request.use(
  function(config) {
    let noCache = new Date().getTime();
    let data = config.data || {};
    config.headers['startTimer'] = `${noCache}`; //请求前记录时间
    config.headers['token'] = options.store.state.common.token;
    if (config.method === 'get' || config.method === 'GET') {
      let pms = '';
      Object.keys(data).forEach(v => {
        if (pms === '') {
          pms = v + '=' + data[v];
        } else {
          pms += '&' + v + '=' + data[v];
        }
      });
      if (config.url.indexOf('?') === -1 && pms !== '') {
        config.url = config.url + '?' + pms;
      } else if (pms !== '') {
        config.url = config.url + '&' + pms;
      }
    } else if (
      config.headers['Content-Type'].indexOf(
        'application/x-www-form-urlencoded'
      ) > -1
    ) {
      config.data = options.qs.stringify(data);
    }
    return config;
  },
  function(error) {
    return Promise.reject(error);
  }
);
/**@desc 在请求后做的操作**/
request.interceptors.response.use(
  /**@desc 对响应数据做点什么**/
  response => {
    let endTimer = Date.parse(new Date());
    let endTotalTimer = endTimer - response.config.headers.startTimer;
    const data = response.data;
    if (Object.prototype.toString.call(data) === '[object Object]') {
      data.endTotalTimer = endTotalTimer;
    }
    if (data.statusCode === 21000) {
      goToLogin();
    } else if (
      data.success === false ||
      (data.statusCode === 0 && data.message)
    ) {
      if (response.config.headers.messageErrorAlert != 1) {
        //如果请求头里面messageErrorAlert为1说明自己处理错误消息
        if (downLoadTimer) {
          downLoadTimer = false;
          options.Toast({
            message: data.message,
            forbidClick: true
          });
          setTimeout(() => {
            downLoadTimer = true;
          }, maxtimer);
        }
      }
      return Promise.reject(response.data);
    }
    return data;
  },
  /**@desc 对请求错误做些什么**/
  error => {
    if (error.response) {
      if (error.response.status === 401) {
        goToLogin();
      } else {
        try {
          if (downLoadTimer) {
            downLoadTimer = false;
            options.Toast({
              message: error.response.data,
              forbidClick: true
            });
            setTimeout(() => {
              downLoadTimer = true;
            }, maxtimer);
          }
        } catch (error) {}
      }
      return Promise.reject(error.response.data);
    } else {
      return error;
    }
  }
);
/**@desc 把请求对象**/
export const $api = request;
/**@desc get请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $get = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'get'];
  headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
  if (show === false) headers.messageErrorAlert = 1;
  let transformRequest = [data => options.qs.stringify(data)];
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers, transformRequest })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
/**@desc post  表单提交请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $post = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'post'];
  if (show === false) headers.messageErrorAlert = 1;
  /**@desc 何锴 2021/12/13 修改由于两次stringify方法导致数据传输错误 Start */
  // let transformRequest = [data => qs.stringify(data)];
  return new Promise((resolve, reject) => {
    // request({ url, method, data, headers, transformRequest })
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
  /**@desc 何锴 2021/12/13 修改由于两次stringify方法导致数据传输错误 End */
};
/**@desc post json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $postJson = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'post'];
  if (show === false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/json';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
/**@desc put json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $put = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'put'];
  if (show === false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/json';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

/**@desc delete json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $delete = function(url, data = {}, show = false) {
  let [headers, method] = [{}, 'delete'];
  if (show === false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=UTF-8';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};

/**@desc post json请求快捷方式
 * @param url {String} 请求url地址
 * @param data {Object} 请求参数
 * @param show {Boolean} 该请求是否使用默认弹框**/
export const $upload = function(
  url,
  data = {},
  onUploadProgress,
  show = false
) {
  let [headers, method] = [{}, 'post'];
  if (show === false) headers.messageErrorAlert = 1;
  headers['Content-Type'] = 'multipart/form-data';
  return new Promise((resolve, reject) => {
    request({ url, method, data, headers, onUploadProgress })
      .then(res => resolve(res))
      .catch(error => reject(error));
  });
};
