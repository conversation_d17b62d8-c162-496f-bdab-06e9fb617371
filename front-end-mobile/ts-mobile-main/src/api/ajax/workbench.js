import { $get, $post, $postJson } from '../ajax.js';
import { apiConfig } from '../config.js';
export default {
  getMenus(code = 'ts-platform-phone') {
    return $get(`${apiConfig.system()}/menu/getfmenus`, {
      syscode: code
    });
  },
  getWxJsapiSignature(params) {
    return $get(
      `${apiConfig.information()}/notice/getWxJsapiSignature`,
      params
    );
  },
  getShowMenus() {
    return $get(`${apiConfig.basics()}/moduleMenu/getMenu?module=gzttb`);
  },
  getMyProcessData() {
    return $post(
      `${apiConfig.workflow()}/workflow/wfInst/mobile/myWfStatistical`
    );
  },
  getMyWorkOrder() {
    return $get(
      `${apiConfig.worksheet()}/workSheet/mobileMyWorkOrderStatistics`
    );
  },
  changeUserDept(params) {
    return $postJson(
      `${apiConfig.basics()}/api/organizationParttime/chooseOrganizationParttime`,
      params
    );
  },
  //短信登录
  suggestionBoxSave(params) {
    return $postJson(
      `/ts-hrms/api/suggestionBox/save`,
      params
    );
  },
  /**@desc 拨号**/
  callPhone(datas) {
    return $post(
      `${apiConfig.external()}/yy3yy/callPhone`,
      datas
    );
  },
};
