import { $get, $postJson } from '../ajax.js';
import { apiConfig } from '../config.js';
export default {
  loginOut(userCode) {
    return $get(`${apiConfig.oa()}/employee/wxSignOut?userCode=${userCode}`);
  },
  signInVerify(id) {
    return $get(`/ts-hrms/api/consultApply/signInVerify/${id}`, {}, true);
  },
  consultApplySignIn(data) {
    return $postJson(`/ts-hrms/api/consultApply/signIn`, data);
  },
};
