import { $post, $postJson, $get } from '../ajax.js';
import { apiConfig } from '../config.js';

export default {
  //获取内部联系人列表
  getInnerContractList(data) {
    return $post(
      `${apiConfig.basics()}/employee/linkman/innerLinkManlist`,
      data
    );
  },
  // 获取系统群组列表
  getOrgGroupList(data) {
    return $post(
      `${apiConfig.basics()}/employee/orgGroup/getOrgGroupListPage`,
      data
    );
  },
  //获取个人群组
  getPersonealGroupList(data) {
    return $post(
      `${apiConfig.basics()}/employee/orgGroup/getOrgGroupListPage`,
      data
    );
  },
  //获取外部联系人
  getOutterContractList(data) {
    return $post(`${apiConfig.basics()}/employee/linkman/list`, data);
  },
  //科室通讯录
  getAllOrgList(data) {
    // return $post(`${apiConfig.basics()}/organization/getAllOrgList`, data);
    return $get(`${apiConfig.basics()}/organizationContacts/list`, data);
  },
  //科室排班通讯录
  dutyAddressBook(params) {
    return $get(`${apiConfig.oa()}/api/dutyAddressBook/list`, params);
  },
  //更新个人群组
  updatePersonalGroup(data) {
    return $postJson(
      `${apiConfig.basics()}/employee/orgGroup/update`,
      JSON.stringify(data)
    );
  },
  //移除个人群组
  removePersonalGroup(id) {
    return $postJson(
      `${apiConfig.basics()}/employee/orgGroup/deletedById`,
      JSON.stringify({
        groupId: id
      })
    );
  },
  //移除外部联系人
  removeOutterContact(id) {
    return $postJson(`${apiConfig.basics()}/employee/linkman/deletedById`, {
      id: id
    });
  },
  editOutterContact(data) {
    return $postJson(`${apiConfig.basics()}/employee/linkman/update`, data);
  }
};
