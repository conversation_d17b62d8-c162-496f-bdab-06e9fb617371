import { $get, $postJson } from '../ajax.js';

import { apiConfig } from '../config.js';
export default {
  //获取日程信息
  getSchedule(data) {
    return $postJson(
      `${apiConfig.oa()}/schedule/selectScheduleDetailsList`,
      data
    );
  },
  selectVersionRecord() {
    return $get(`${apiConfig.basics()}/api/versionRecord/selectVersionRecord`);
  },
  versionRecordSave(data) {
    return $postJson(`${apiConfig.basics()}/api/versionRecord/save`, data);
  },
  getIndexMessageNumber() {
    return $get(
      `${apiConfig.information()}/information/getNoreadMessageNumbers`
    );
  },
  //埋点
  modesUsesSave(data) {
    return $postJson(
      `${apiConfig.basics()}/api/modesUses/save`,
      data
    );
  },
};
