import { $get, $post, $postJson } from '../ajax.js';
import { apiConfig } from '../config.js';
export default {
  /**@desc  获取应用基本设置**/
  getGlobalSetting() {
    return $get(`${apiConfig.basics()}/globalSetting/getSafeGlobalSetting`, {
      source: 'mobile'
    });
  },
  /**@desc  登陆后 获取应用基本设置**/
  getAllGlobalSetting() {
    return $get(`${apiConfig.basics()}/globalSetting/getAllGlobalSetting`, {
      source: 'mobile'
    });
  },
  /**@desc  获取机构**/
  getMultipleOrg(usercode) {
    return $post(
      `${apiConfig.basics()}/user/login/getMultipleOrg?usercode=${usercode}`
    );
  },
  //获取数据字典
  getDictionary(typeCode) {
    return $get(`${apiConfig.basics()}/dictItem/getDictItemByTypeCode`, {
      typeCode
    });
  },
  //设置用户信息
  changeUserInfo(data) {
    return $postJson(`${apiConfig.basics()}/employee/updateMyUser`, data);
  }
};
