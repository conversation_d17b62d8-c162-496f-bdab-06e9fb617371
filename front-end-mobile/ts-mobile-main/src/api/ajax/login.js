import { $get, $api, $postJson } from '../ajax.js';
import { apiConfig } from '../config.js';
export default {
  //账号登录
  accountLogin(data) {
    return $postJson(`${apiConfig.information()}/cp/weixin/login`, data);
  },
  //获取短信登录验证码
  getSmsLoginVerifyCode(data) {
    return $api({
      url: `${apiConfig.information()}/messageLogin/sendVerifyCode`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //获取忘记密码短信验证码
  getForgetLoginVerifyCode(data) {
    return $api({
      url: `${apiConfig.information()}/messageInternal/sendVerifyCode`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //验证账号是否存在
  valdiateUser(data) {
    return $api({
      url: `${apiConfig.oa()}/employee/valdiateUser`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //验证验证码是否正确
  checkverifyCoder(data) {
    return $api({
      url: `${apiConfig.information()}/messageInternal/verifyCode`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //验证验证码是否正确
  resetPassword(data) {
    return $api({
      url: `${apiConfig.oa()}/employee/resetPassword`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //短信登录
  smsLogin(data) {
    return $api({
      url: `${apiConfig.information()}/messageLogin/SMSlogin`,
      method: 'post',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8'
      },
      data
    });
  },
  //获取用户信息
  getUserInfo() {
    return $get(`${apiConfig.basics()}/employee/getMyEmployeeDetail`);
  },
  //获取用户个人信息状态
  employeeReviewStatus(employeeId) {
    return $get(`${apiConfig.basics()}/employee/reviewStatus/${employeeId}`);
  },
  //获取用户个人信息状态 档案改版 新
  customEmployeeBaseInApproval() {
    return $get(`${apiConfig.basics()}/api/customEmployeeBase/inApproval`);
  },
  //获取暂存信息
  cusotmEmployeeGetstorage(employeeId) {
    return $get(
      `${apiConfig.basics()}/cusotmEmployee/getstorage/${employeeId}`
    );
  },
  //获取暂存信息 档案改版 新
  customEmployeeBaseGetStorage(params) {
    return $api({
      url: `${apiConfig.basics()}/api/customEmployeeBase/getStorage`,
      method: 'get',
      params
    });
  },
  //流程记录人
  cusotmEmployeeGetEmployeeTask(employeeId) {
    return $get(
      `${apiConfig.basics()}/cusotmEmployee/getEmployeeTask/${employeeId}`
    );
  },
  //流程记录人 档案改版 新
  customEmployeeBaseGetEmployeeTask(employeeNo) {
    return $get(
      `${apiConfig.basics()}/api/customEmployeeBase/getEmployeeTask/${employeeNo}`
    );
  }
};
