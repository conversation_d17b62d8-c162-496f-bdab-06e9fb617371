.ts-container {

  input[type=search]::-webkit-search-cancel-button {
    -webkit-appearance: none;
  }
  .ts-navbar-placeholder {
    height: 44px;
  }
  .ts-navbar-content {
    height: 44px;
    line-height: 44px;
    text-align: center;
    font-size: 16px;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    color: #333;

    .ts-back-wrap {
      height: 100%;
      position: absolute;
      left: 0;
      padding: 7px 7px 7px 12px;
      box-sizing: border-box;
      color: #666;
      display: flex;
      align-items: center;
      i {
        font-size: 24px;
      } 
    }
    .ts-navbar-content-title{
      font-weight: bold;
    }
  }
}
.bottom-line::after {
  content: ' ';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #e4e4e4;
  transform: scaleY(0.5);
}

.top-line::before {
  content: ' ';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background-color: #e4e4e4;
  transform: scaleY(0.5);
}