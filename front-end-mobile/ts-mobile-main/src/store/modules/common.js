import Vue from 'vue';
export default {
  namespaced: true,
  state: () => ({
    baseHost: '',
    basePath: '',
    token: '', //用户token
    account: '',
    password: '',
    empCode: '',
    globalSetting: {
      passwordLength: 6,
      passwordPreset: '123456',
      passwordRule: '1,2,3',
      loginPageBackground: '',
      loginTopLogo: '',
      recordLinkUrl: '',
      recordNumber: '',
      originalContent: '',
      originalUrl: 'http://www.trasen.cn',
      mobilePlatform: '',
      salaryType: '',
    },
    whiteRouteList: [
      '/workSheet/dingTalkRouterChange/index',
      '/mobile-container/workbench',
    ],
    userInfo: {
      token: '',
      empHeadImg: '',
    }, //用户基本消息
    systemCustomCode: {}, //客户定制化参数（系统字典）
    personalSortData: {}, //人员排序参数
  }),
  mutations: {
    /**@desc 更新数据
     * @param {String} label state里面的key值
     * @param {any} value state的数据值**/
    setData(state, { label, value }) {
      let typeStr = Object.prototype.toString.call(value);
      if (typeStr == '[object Object]' || typeStr == '[object Array]') {
        Vue.set(state, label, JSON.parse(JSON.stringify(value)));
      } else {
        Vue.set(state, label, value);
      }
      // 主动广播用户信息改变了
      if (label == 'userInfo') {
        Vue.prototype.$mainMessageStore &&
          Vue.prototype.$mainMessageStore.setGlobalState({
            event: 'baseInformChange',
            data: {
              userInfo: value,
            },
          });
      }
    },
    clerarUserInfo(state) {
      state.token = ''; //用户token
      state.account = '';
      state.password = '';
      state.empCode = '';
      state.userInfo = {
        token: '',
        empHeadImg: '',
      };
    },
  },
};
