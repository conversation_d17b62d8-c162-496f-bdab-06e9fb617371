// #ifdef H5
import { Toast } from 'vant';

var jweixin = require('../lib/jweixinModule.js');
export default {
  //初始化
  initwxJdk: function(data) {
    jweixin.config({
      beta: true, // 必须这么写，否则wx.invoke调用形式的jsapi会有问题
      debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
      appId: data.appId, // 必填，企业微信的corpID
      timestamp: data.timestamp, // 必填，生成签名的时间戳
      nonceStr: data.nonceStr, // 必填，生成签名的随机串
      signature: data.signature, // 必填，签名，见附录1
      jsApiList: ['scanQRCode'] // 必填，需要使用的JS接口列表，所有JS接口列表见附录2
    });
    jweixin.ready(function() {
      jweixin.checkJsApi({
        jsApiList: ['scanQRCode'], // 需要检测的JS接口列表，所有JS接口列表见附录2,
        success: function(res) {
          // 以键值对的形式返回，可用的api值true，不可用为false
          // 如：{"checkResult":{"chooseImage":true},"errMsg":"checkJsApi:ok"}
        }
      });
    });
  },

  //扫码
  scanQRCode: function(callback) {
    jweixin.scanQRCode({
      desc: 'scanQRCode desc',
      needResult: 1, // 默认为0，扫描结果由微信处理，1则直接返回扫描结果，
      scanType: ['qrCode', 'barCode'], // 可以指定扫二维码还是一维码，默认二者都有
      success: function(res) {
        callback(res);
      },
      error: function(res) {
        if (res.errMsg.indexOf('function_not_exist') > 0) {
          Toast({
            message: '版本过低请升级',
            forbidClick: true
          });
        }
      }
    });
  }
};
//#endif
