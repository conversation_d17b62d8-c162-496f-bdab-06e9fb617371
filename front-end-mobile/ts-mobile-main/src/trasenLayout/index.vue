<template>
  <div class="home">
    <router-view class="router-view"></router-view>
    <tab-bar :tabBarList="tabBarList" v-show="showTabBar"></tab-bar>
    <div class="qiankun-container" ref="container"></div>
    <version-tips
      v-if="dialogVersionTips"
      v-model="dialogVersionTips"
      :versionInfo="versionInfo"
    />
  </div>
</template>

<script>
import index from './index.js';
import App from './App.js';
import tabBar from '../components/tab-bar/index.vue';

import VersionTips from './components/version-tips.vue';

export default {
  name: 'layout',
  mixins: [index, App],
  components: {
    tabBar,
    VersionTips
  }
};
</script>
<style scoped lang="scss">
.home {
  height: 100%;
  width: 100%;
}
.router-view {
  background-color: $bg-color-grey;
  height: calc(100vh - 50px);
  width: 100vw;
}
.qiankun-container {
  width: 100%;
  position: relative;
  height: 100%;
  background-color: $bg-color-grey;
}
/deep/ {
  .qiankun-container > div {
    height: 100%;
    width: 100%;
  }
}
</style>
