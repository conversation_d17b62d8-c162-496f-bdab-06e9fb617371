<template>
  <div>
    <div v-if="item.menus && item.menus.length != 0">
      <el-submenu :index="`${item.parentIndex_}`">
        <template slot="title">
          <span
            class="oaicon nav-item-icon "
            :class="item.icon"
            v-if="item.icon"
          ></span>
          <span slot="title" class="item-title">
            {{ item.menuname }}
          </span>
        </template>
        <sidebar-item
          :item="_item"
          :index="`${_item.parentIndex_}`"
          :key="_index"
          v-for="(_item, _index) in item.menus"
        />
      </el-submenu>
    </div>
    <el-menu-item
      @click="menuClick(item)"
      v-else
      :index="`${item.parentIndex_}`"
      class="item-title-menu"
    >
      <i class="oaicon nav-item-icon " :class="item.icon" v-if="item.icon"></i>
      <span slot="title">{{ item.menuname }}</span>
    </el-menu-item>
  </div>
</template>

<script>
export default {
  name: 'sidebarItem',
  props: {
    item: {
      //数据对象
      type: Object,
      default: () => {}
    },
    index: {
      //数据索引
      type: [Number, String],
      default: 0
    }
  },
  methods: {
    menuClick(item) {
      this.$router.push(item.alink);
    }
  }
};
</script>

<style scoped lang="scss">
.iconfont {
  padding-right: 20px;
}
.nav-item-icon {
  font-size: 20px;
  margin-right: 8px;
}
</style>
