<template>
  <div
    class="iframe-container"
    :class="[
      show ? 'iframe-container-active' : '',
      leftbg == 2 ? 'fixed-screen' : ''
    ]"
  >
    <iframe ref="iframe" :src="src" class="iframe"></iframe>
  </div>
</template>

<script>
import websocket from '@/trasenLayout/components/websocket';
import oldProjectjs from './oldProjectjs.js';
export default {
  mixins: [websocket, oldProjectjs],
  name: 'oldProject'
};
</script>

<style scoped lang="scss">
.iframe {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
}
.iframe-container {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.iframe-container-active {
  position: fixed;
  left: 200vw;
  top: 200vh;
}
.fixed-screen {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1000;
}
</style>
