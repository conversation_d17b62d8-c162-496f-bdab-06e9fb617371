<template>
  <div class="version-tips">
    <div class="content">
      <!-- <img
        style="height: 95px;"
        src="@/assets/images/top-version-level.jpg"
        alt=""
        srcset=""
      /> -->
      <div class="padding-content">
        <div class="tips-title">发现新版本</div>
        <span class="item-label">
          最新版本号:
          <span class="value"
            >{{ versionInfo.version || '空' }} ({{
              versionInfo.versionDate || ''
            }})</span
          >
        </span>
        <div class="title-content-tips">更新内容:</div>
        <div class="update-content" v-html="versionInfo.content"></div>
        <div class="submit" @click="handleMobileView">
          确定
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  model: {
    event: 'change',
    prop: 'show'
  },
  props: {
    dialogVersionTips: {
      type: Boolean
    },
    versionInfo: {
      type: Object
    }
  },
  data: function() {
    return {};
  },
  methods: {
    async handleMobileView() {
      const res = await this.ajax.versionRecordSave({
        versionId: this.versionInfo.id
      });
      if (res.success == false) {
        return;
      }

      const dom = document.getElementsByClassName('version-tips')[0];
      dom.style.left = '100%';

      setTimeout(() => {
        this.$emit('change', false);
      }, 500);
    }
  }
};
</script>

<style lang="scss" scoped>
.version-tips {
  position: fixed;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 999;
  box-sizing: border-box;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.5s;
  .content {
    width: 291px;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
    border-radius: 12px;
    overflow: hidden;

    .padding-content {
      padding: 0px 20px;
      .tips-title {
        margin: 10px;
        font-size: 16px;
        text-align: center;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #333333;
      }
      .item-label {
        font-size: 14px;
        color: #999;
      }
      .title-content-tips {
        font-size: 14px;
        font-weight: 600;
        padding-top: 5px;
        margin-bottom: 5px;
      }
      .update-content {
        min-height: 50px;
        max-height: 221px;
        width: 100%;
        font-size: 14px;
        overflow-y: auto;
        &::-webkit-scrollbar {
          width: 6px;
        }
        // 滚动条的滑块
        &::-webkit-scrollbar-thumb {
          border-radius: 3px;
          background-color: #999;
        }
        /deep/ p {
          margin: 0;
          margin-block: 0;
        }
      }
      .submit {
        line-height: 40px;
        text-align: center;
        font-size: 16px;
        color: #fff;
        height: 40px;
        margin: 13px 0 10px 0;
        background: linear-gradient(270deg, #005BAC 0%, #005BAC 100%);
        box-shadow: 0px 3px 5px 0px rgba(82, 96, 255, 0.3);
        border-radius: 40px;
      }
    }
  }
}
</style>
