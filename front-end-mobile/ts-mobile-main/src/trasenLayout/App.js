import { loadMicroApp, registerMicroApps, start } from 'qiankun';

export default {
  data() {
    return {
      showTabBar: true,
      subapp: {},
      qiankunApp: [],

      versionInfo: {},
      dialogVersionTips: false,
      list: [
        {
          title: '消息',
          inactiveIcon: require('@/assets/images/msg.png'),
          activeIcon: require('@/assets/images/msg-active.png'),
          pagePath: '/index',
          badge: '',
        },
        {
          title: '工作台',
          inactiveIcon: require('@/assets/images/workbench.png'),
          activeIcon: require('@/assets/images/workbench-active.png'),
          pagePath: '/workbench',
        },
        {
          title: '通讯录',
          inactiveIcon: require('@/assets/images/addressbook.png'),
          activeIcon: require('@/assets/images/addressbook-active.png'),
          pagePath: '/addressbook',
        },
        {
          title: '我的',
          inactiveIcon: require('@/assets/images/my.png'),
          activeIcon: require('@/assets/images/my-active.png'),
          pagePath: '/my',
        },
      ],
      tabBarList: [],
    };
  },
  methods: {
    /**
     * @desc 子应用调用父应用的方法
     * @param {Object} obj
     * @param {string} obj.event 事件触发名称
     * @param {string} obj.data 事件触发传递参数
     * **/
    parentTypeFun: function(obj) {
      if (obj.type == 'token') {
        this.getCommonUserInfo(obj.token);
      } else if (obj.type == 'goToLogin') {
        this.goToLogin();
      } else if (obj.type == 'jumpPage') {
        this.$router.push(obj.path);
      } else if (obj.type == 'redirectTo') {
        this.$router.replace(obj.path);
      } else if (obj.type == 'goBack') {
        this.$router.go(-1);
      } else {
        this.$root.$emit('parentTypeFun', obj);
      }
    },
    childComponentOutLogin(url = '') {
      localStorage.clear();
      sessionStorage.clear();
      const cookieKeys = this.$cookies.keys();
      for (let i = 0; i < cookieKeys.length; i++) {
        this.$cookies.remove(cookieKeys[i]);
      }
      this.$store.commit('common/setData', {
        label: 'token',
        value: '',
      });
      this.$store.commit('common/setData', {
        label: 'empCode',
        value: '',
      });
      this.$store.commit('common/setData', {
        label: 'userInfo',
        value: '',
      });
      this.$store.commit('common/setData', {
        label: 'account',
        value: '',
      });
      this.$store.commit('common/setData', {
        label: 'password',
        value: '',
      });

      url ? (location.href = url) : this.$router.push('/login');
    },
    storeInfo() {
      return JSON.parse(JSON.stringify(this.$store.state));
    },
    /**@desc 初始化乾坤**/
    initQiankun1() {
      let to = this.$route;
      this.app.map((item) => {
        if (to.path.indexOf(item.alink) == 0) {
          if (
            this.subapp[item.name] == null ||
            this.subapp[item.name] == undefined
          ) {
            this.subapp[item.name] = loadMicroApp({
              container: this.$refs.container, //容器节点
              name: item.userData.packageName, //包名
              entry: item.entry || item.alink,
              activeRule: `${this.$store.state.common.basePath}${item.alink}`, //激活路由
              props: {
                data: {
                  token: this.$store.state.common.token,
                  activeRule: `${this.$store.state.common.basePath}${item.alink}`, //激活路由
                },
                fn: {
                  parentTypeFun: this.parentTypeFun,
                  storeInfo: this.storeInfo,
                  childComponentOutLogin: this.childComponentOutLogin,
                },
              },
            });
          }
        } else if (this.subapp[item.name]) {
          this.subapp[item.name].unmount();
          this.subapp[item.name] = null;
        }
      });
    },
    setTabBarStatus(path) {
      let index = 0;
      this.app.forEach((item) => {
        if (path.indexOf(item.userData.packageName) != -1) {
          index++;
        }
      });
      this.showTabBar = index != 0 || path == '/login' ? false : true;
    },
    async handleMobileGetVersionDialog() {
      const res = await this.ajax.selectVersionRecord();
      if (res.success == false) {
        this.dialogVersionTips = false;
        return;
      }
      if (res.object && res.object?.isPush == 1) {
        this.versionInfo = res.object;
        this.dialogVersionTips = true;
      } else {
        this.versionInfo = {};
        this.dialogVersionTips = false;
      }
    },
    async getBottomMenus() {
      await this.ajax
        .getMenus('ts-platform-bottom')
        .then((res) => {
          let menus = res.object.length ? res.object[0].menus : [];
          if (menus.length) {
            let newMenus = [];
            menus.forEach((item) => {
              let menu = this.list.find((i) => i.title == item.menuname);
              if (menu) {
                newMenus.push(menu);
              }
            });
            this.tabBarList = newMenus;
          } else {
            this.tabBarList = this.list;
          }
        })
        .catch((err) => {});
    },
    getIndexMessageNumber(index) {
      this.ajax.getIndexMessageNumber().then((res) => {
        this.tabBarList[index].badge = res.object;
      });
    },
  },
  async created() {
    await this.ajax.getGlobalSetting().then(async (res) => {
      document.title = `${res.object.webTitle || '综合协同办公平台'}`;
      this.$store.commit('common/setData', {
        label: 'globalSetting',
        value: res.object,
      });
      if (res.object.orgCode == 'cssdeshfly') {
        await this.getBottomMenus();
      } else {
        this.tabBarList = this.list;
      }
      if (this.$cookies.get('token')) {
        let messageMenuIndex = this.tabBarList.findIndex(
          (i) => i.title == '消息'
        );
        if (messageMenuIndex != -1) {
          this.getIndexMessageNumber(messageMenuIndex);
        }
      }
    });
  },
  async mounted() {
    this.setTabBarStatus(this.$route.path);
    this.$root.$on('initQiankun1', this.initQiankun1);

    this.handleMobileGetVersionDialog();
  },
  watch: {
    $route(to, from) {
      let arr = ['/index', '/workbench', '/addressbook', '/my'];
      this.initQiankun1();
      this.setTabBarStatus(to.path);
      let path = to.fullPath.split('?')[0];
      this.showTabBar = arr.includes(path.toLowerCase());
      if (this.$cookies.get('token')) {
        let messageMenuIndex = this.tabBarList.findIndex(
          (i) => i.title == '消息'
        );
        if (messageMenuIndex != -1) {
          this.getIndexMessageNumber(messageMenuIndex);
        }
      }
    },
    '$store.state.common.token': {
      handler(val) {
        if (val) {
          let messageMenuIndex = this.tabBarList.findIndex(
            (i) => i.title == '消息'
          );
          if (messageMenuIndex != -1) {
            this.getIndexMessageNumber(messageMenuIndex);
          }
        }
      },
    },
  },
};
