import 'whatwg-fetch';
import 'custom-event-polyfill';
import 'core-js/stable/promise';
import 'core-js/stable/symbol';
import 'core-js/stable/string/starts-with';
import 'core-js/web/url';
import './assets/iconfont/iconfont.css';
import Vue from 'vue';
import dayjs from 'dayjs';
import App from './App.vue';
import routes from './router';
import VueRouter from 'vue-router';
import api from './api/index';
import VueCookies from 'vue-cookies';
import VueSession from 'vue-session';
import {
  Form,
  Toast,
  Button,
  Icon,
  List,
  Cell,
  Lazyload,
  ActionSheet,
  Calendar,
  Field,
  CellGroup,
  Popover,
  Picker,
  Popup,
} from 'vant';
import config from '@/config/config.js';

window.onerror = function(message, source, lineno, colno, error) {
  console.log('捕获到异常：', {
    message,
    source,
    lineno,
    colno,
    error,
  });
  return true;
};

import { $api } from '@/api/ajax.js';
Vue.prototype.$api = $api;
import { initGlobalState } from 'qiankun';
Vue.prototype.$mainMessageStore = initGlobalState({});
import store from './store';

Vue.use(VueRouter);
Vue.use(VueCookies);
Vue.use(VueSession);
Vue.use(api);
Vue.use(Lazyload, {
  lazyComponent: true,
});
Vue.use(Toast);
Vue.component(Form.name, Form);
Vue.component(Button.name, Button);
Vue.component(Icon.name, Icon);
Vue.component(List.name, List);
Vue.component(Calendar.name, Calendar);
Vue.component(ActionSheet.name, ActionSheet);
Vue.component(Cell.name, Cell);
Vue.component(Field.name, Field);
Vue.component(CellGroup.name, CellGroup);
Vue.component(Popover.name, Popover);
Vue.component(Picker.name, Picker);
Vue.component(Popup.name, Popup);

Vue.config.productionTip = false;

Vue.prototype.$dayjs = dayjs;
Vue.prototype.$config = config;

const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch((err) => err);
};
let base = '';
base = document.querySelector('base').href || '/';
base = base.replace(/^https?:\/\/[^\/]+/, '');
let _base = JSON.parse(JSON.stringify(base));
_base = _base.slice(0, _base.lastIndexOf('/'));
store.commit('common/setData', { label: 'basePath', value: _base });
store.commit('common/setData', {
  label: 'baseHost',
  value: window.location.origin,
});
const router = new VueRouter({
  base,
  mode: 'history',
  routes,
});

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount(document.getElementById(process.env.VUE_APP_CONTAINER));
