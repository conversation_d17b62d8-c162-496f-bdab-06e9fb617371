const routes = [
  {
    path: '/login',
    name: 'login',
    component: () => import('./views/login/index.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/index',
    name: 'index',
    component: () => import('./views/index/index.vue'),
    meta: {
      title: '首页'
    }
  },
  {
    path: '/workbench',
    name: 'workbench',
    component: () => import('./views/workbench/index.vue'),
    meta: {
      title: '工作台'
    }
  },
  {
    path: '/my',
    name: 'my',
    component: () => import('./views/my/index.vue'),
    meta: {
      title: '我的'
    }
  },
  {
    path: '/personalSettings',
    name: 'personalSettings',
    component: () => import('./views/personalSettings/index.vue'),
    meta: {
      title: '个人设置'
    }
  },
  {
    path: '/addressbook',
    name: 'addressbook',
    component: () => import('./views/addressbook/index.vue'),
    meta: {
      title: '通讯录'
    }
  },
  {
    path: '/group-manage',
    name: 'groupManage',
    component: () => import('./views/addressbook/groupManage/index.vue'),
    meta: {
      title: '群组管理'
    }
  },
  {
    path: '/internal-contact',
    name: 'internalContact',
    component: () => import('./views/addressbook/internalContact/index.vue'),
    meta: {
      title: '内部联系恶人'
    }
  },
  {
    path: '/external-contact',
    name: 'externalContact',
    component: () => import('./views/addressbook/externalContact/index.vue'),
    meta: {
      title: '外部联系恶人'
    }
  },
  {
    path: '/workSheet/dingTalkRouterChange/index',
    component: () => import('./views/dingTalkRouterChange/index.vue'),
    meta: {
      title: '钉钉路由跳转'
    }
  }
];
export default routes;
