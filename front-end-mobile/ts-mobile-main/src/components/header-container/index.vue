<template>
  <div style="height: 40px;background: green" class="flex-start">
    <div
      v-for="(item, index) in menuList"
      :key="index"
      class=" header-list"
      @click="goto(item)"
    >
      {{ item.alink }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'index',
  data() {
    return {
      menuList: window.qiankuanappsLogin
    };
  },
  methods: {
    /**@desc 页面调转详情
     * @param {Object} item
     * @param {Number} index
     * **/
    goto(item) {
      this.$router.push(`${item.alink}`);
    }
  },
  computed: {}
};
</script>

<style scoped lang="scss">
.header-list {
  margin: 0 10px;
}
// flex设
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}
.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}
.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.flex-column {
  flex-direction: column;
}
</style>
