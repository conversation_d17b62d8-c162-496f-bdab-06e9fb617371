<template>
  <div class="base-error " v-loading="loading">
    <div class="flex-center base-error-container" v-if="show404">
      <div><img src="./static/404.png" alt="" class="empty-img" /></div>
      <div class="error-text">抱歉，页面不见了……</div>
      <div class="flex-center">
        <el-button class="go-to-index" @click="gotoPrev">返回上一页</el-button>
        <el-button type="empty" @click="upadteLoad">点击刷新</el-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Error',
  data() {
    return {
      loading: false,
      show404: true
    };
  },
  methods: {
    /**@desc 回到上一页**/
    gotoPrev() {
      window.history.go(-1);
    },
    /**@desc 刷新页面**/
    upadteLoad() {
      this.$root.$emit('updateLoad');
      this.loading = true;
      this.show404 = false;
      setTimeout(() => {
        this.show404 = true;
        this.loading = false;
      }, 1000);
    }
  },
  mounted() {
    this.upadteLoad();
  }
};
</script>

<style scoped lang="scss">
.base-error {
  position: absolute;
  width: 100%;
  height: 100%;
}
.base-error-container {
  width: 100%;
  height: 100%;
  flex-direction: column;
}
.go-to-index {
  margin-right: 30px;
}
.error-text {
  margin: 20px 0 50px 0;
}
.empty-img {
  width: 395px;
  height: 405px;
}
</style>
