<template>
  <div class="person-select">
    <slot></slot>
    <select-popup v-model="show" height="100%">
      <div class="popup-header bottom-line">
        <span class="">人员选择</span>
        <!-- <van-icon
          name="success"
          class="option-btn"
          @click="confirm"
          v-if="multi"
        /> -->
        <span class="option-btn" @click="confirm" v-if="multi">
          确定
        </span>
        <van-icon
          name="cross"
          class="option-btn close-btn"
          @click="cancel"
          v-else
        />
      </div>
      <div class="popup-search">
        <van-search
          v-model="searchVal"
          show-action
          placeholder="输入姓名、科室搜索"
          shape="round"
          @search="onSearch"
        >
          <template #action>
            <div @click="onSearch">搜索</div>
          </template>
        </van-search>
        <div style="height: 6px;background:#f7f8fa;"></div>
      </div>
      <div class="popup-content" ref="scrollBox">
        <div
          class="user-item"
          v-for="(item, index) in userList"
          :key="index"
          :class="
            item.empSex == 0
              ? 'user-item-man'
              : item.empSex == 1
              ? 'user-item-woman'
              : ''
          "
          @click="choose(item, index)"
        >
          <!-- <div class="item-name">{{ item.empName.slice(-2) }}</div> -->
          <van-image
            :src="item.avata"
            fit="cover"
            lazy-load
            class="user-avatar"
            error-icon="user-o"
            loading-icon="user-o"
          >
          </van-image>
          <div class="item-info">
            <div class="user-name">{{ item.empName }}</div>
            <div class="user-dept">
              <span style="margin-right:16px;">
                {{ item.empDeptName || '无' }}
              </span>
              <span>{{ item.empCode }}</span>
            </div>
          </div>
          <div class="check-icon" v-if="isExsit(item) !== false">
            <van-icon name="success" />
          </div>
        </div>
        <div class="is-loading" v-show="loading">加载中...</div>
        <div class="no-more" v-show="finished">没有更多了...</div>
      </div>
    </select-popup>
  </div>
</template>

<script>
import { PullRefresh, Search } from 'vant';
import selectPopup from './select-popup.vue';
import { Image as VanImage } from 'vant';
export default {
  name: 'person-select',
  components: {
    [PullRefresh.name]: PullRefresh,
    [Search.name]: Search,
    selectPopup,
    [VanImage.name]: VanImage
  },
  data() {
    return {
      searchVal: '',
      userList: [],
      pageNo: 1,
      loading: false,
      finished: false
    };
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    multi: {
      type: Boolean,
      default: false
    },
    selectedUsers: {
      type: Array,
      default: () => []
    }
  },
  created() {
    this.getData();
  },
  mounted() {
    this.$refs.scrollBox.addEventListener('scroll', this.scroll);
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.scroll);
  },
  methods: {
    onSearch() {
      this.userList = [];
      this.pageNo = 1;
      this.getData();
    },
    cancel() {
      this.$emit('update:show', false);
    },
    confirm() {
      this.$emit('update:show', false);
      this.$emit('select', this.selectedUsers);
    },
    choose(item, index) {
      if (this.multi) {
        let _index = this.isExsit(item);
        let _list = this.selectedUsers;
        if (_index !== false) {
          _list.splice(_index, 1);
        } else {
          _list.push(item);
        }
        this.$emit('update:selectedUsers', _list);
      } else {
        this.$emit('update:selectedUsers', [item]);
        this.$emit('update:show', false);
        this.$emit('select', item);
      }
    },
    isExsit(item) {
      for (let i in this.selectedUsers) {
        if (this.selectedUsers[i].id == item.id) {
          return i;
          break;
        }
      }
      return false;
    },
    getData() {
      this.loading = true;
      this.$api({
        url: '/ts-oa/employee/getEmployeeList',
        method: 'post',
        data: {
          searchKey: this.searchVal,
          false: true,
          pageSize: 30,
          pageNo: this.pageNo,
          sidx: 'a.EMP_DEPT_CODE',
          sord: 'asc'
        }
      })
        .then(res => {
          if (res.rows) {
            this.userList.push(...res.rows);
            this.loading = false;
            if (res.rows.length < 30) {
              this.finished = true;
            }
          }
        })
        .catch(err => {});
    },
    scroll(e) {
      if (
        this.$refs.scrollBox.scrollTop +
          this.$refs.scrollBox.offsetHeight +
          100 >=
        this.$refs.scrollBox.scrollHeight
      ) {
        if (!this.loading && !this.finished) {
          this.pageNo++;
          this.getData();
        }
      }
    }
  }
};
</script>

<style lang="scss">
.ts-popup {
  .popup-header,
  .popup-search {
    font-size: 16px;
    font-weight: bold;
    height: 44px;
    line-height: 44px;
    text-align: center;
    position: fixed;
    top: 0;
    width: 100%;
    .option-btn {
      position: absolute;
      right: 12px;
      font-size: 14px;
      color: $theme-color;
      font-weight: normal;
    }
    .close-btn {
      font-size: 20px;
      color: #666;
      top: 12px;
    }
  }
  .popup-search {
    top: 44.5px;
    height: 60px;
    font-weight: normal;
    .van-search__content {
      background-color: #f4f4f4;
    }
  }
  .popup-content {
    position: absolute;
    left: 0;
    top: 105px;
    bottom: 0;
    right: 0;
    overflow: hidden;
    overflow-y: auto;
    .user-item-woman .van-image__error {
      background-color: $sexwoman-color;
      .van-image__error-icon {
        color: #fff;
      }
    }
    .user-item-man .van-image__error {
      background-color: $sexman-color;
      .van-image__error-icon {
        color: #fff;
      }
    }
    .user-item {
      height: 40px;
      padding: 8px 16px;
      display: flex;
      &:active {
        background-color: #eeeeef;
      }
      .item-name {
        height: 40px;
        width: 40px;
        border-radius: 50%;
        color: #fff;
        line-height: 40px;
        text-align: center;
        background-color: #0097a7;
        margin: 5px 15px;
      }
      .van-image__error-icon {
        font-size: 24px;
      }
      .van-image__loading-icon {
        font-size: 24px;
      }
      .user-avatar {
        height: 40px;
        width: 40px;
        border-radius: 50%;
        overflow: hidden;
      }
      .item-info {
        flex: 1;
        margin-left: 8px;
        .user-name {
          font-size: 16px;
          color: #333;
        }
        .user-dept {
          margin-top: 4px;
          color: #666;
          font-size: 12px;
        }
      }
      .check-icon {
        color: $theme-color;
        line-height: 40px;
        font-size: 16px;
      }
    }
    .no-more,
    .is-loading {
      font-size: 12px;
      text-align: center;
      color: #999;
      height: 50px;
      line-height: 50px;
    }
  }
}
</style>
