<template>
  <div class="ts-popup-box">
    <transition name="fade">
      <div
        class="ts-overlay ts-fade-out"
        @click="$emit('change', !value)"
        v-show="value"
      ></div>
    </transition>
    <transition name="slide">
      <div class="ts-popup" v-show="value" :style="styleObject">
        <slot></slot>
      </div>
    </transition>
  </div>
</template>

<script>
export default {
  name: 'TsPopup',
  model: {
    event: 'change'
  },
  data() {
    return {
      styleObject: {
        height: this.height
      }
    };
  },
  mounted() {
    //组件元素挂载到body
    this.$nextTick(() => {
      const body = document.querySelector('body');
      if (body.append) {
        body.append(this.$el);
      } else {
        body.appendChild(this.$el);
      }
    });
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    height: {
      type: String,
      default: '30%'
    }
  }
};
</script>

<style lang="scss">
.ts-popup-box {
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.2s;
  }
  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-enter,
  .slide-leave-active {
    transform: translate3d(0, 100%, 0);
  }
  .ts-overlay {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
  }
  .ts-popup {
    position: fixed;
    z-index: 99999;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: #fff;
    transition: transform 0.3s;
    overflow: hidden;
  }
}
</style>
