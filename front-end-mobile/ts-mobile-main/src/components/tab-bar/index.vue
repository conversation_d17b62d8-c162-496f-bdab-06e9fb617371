<template>
  <van-tabbar v-model="activeIndex" placeholder route @change="onChange">
    <van-tabbar-item
      v-for="(item, index) in tabBarList"
      :key="index"
      :to="item.pagePath"
      :badge="item.badge"
      replace
    >
      <span>{{ item.title }}</span>
      <template #icon="props">
        <img :src="props.active ? item.activeIcon : item.inactiveIcon" alt="" />
      </template>
    </van-tabbar-item>
  </van-tabbar>
</template>

<script>
/**
 * TabBar 标签栏
 * @description
 * @event {Function} onChange  选中发生变化触发
 */
import { Tabbar, TabbarItem } from 'vant';
export default {
  name: 'tabBar',
  components: {
    [Tabbar.name]: Tabbar,
    [TabbarItem.name]: TabbarItem,
  },
  data() {
    return {
      activeIndex: 0,
    };
  },
  props: {
    tabBarList: {
      type: Array,
      default: () => [],
    },
  },
  created() {
    this.init();
  },
  methods: {
    onChange(index) {
      this.$store.commit('common/setData', {
        label: 'tabBarIndex',
        value: index,
      });
    },
    init() {
      let path = this.$route.path;
      this.activeIndex = this.tabBarList.findIndex(
        (item) => item.url === `/${path}`
      );
    },
  },
};
</script>

<style scoped></style>
