export default {
  data() {
    return {
      tabType: 1,
      formRef: 'accountLoginForm',
      showSwitchTab: false,
      showInstitutionSelect: false,
      institutionList: [],

      globalSetting: {},
      source: '',
      logoImgPath: '',
      loginPageBackgroundImgPath: '',

      isShowFooter: true,
      docHeight: window.innerHeight,
      showHeight: window.innerHeight
    };
  },
  watch: {
    showHeight: function(newVal) {
      if (this.docHeight > newVal) {
        this.isShowFooter = false;
      } else {
        this.isShowFooter = true;
      }
    }
  },
  mounted() {
    window.onresize = () => {
      return (() => {
        this.showHeight = window.innerHeight;
      })();
    };
  },
  methods: {
    onconfirm() {},
    showOrgSelect() {
      this.$refs.nextTreeRef._show();
    }
  }
};
