import { pwdCheckStrong } from '@/utils/password.js';
import { Encrypt, Decrypt } from '@/utils/encrypt.js';
import getSetData from './mixins/getSetData.js';
import * as dingtalk from 'dingtalk-jsapi'; //引入钉钉jsapi
import moment from 'moment';
const sourceId = 'dingTalk'; // 钉钉
const autologinId = 'true'; // 钉钉自动登录

export default {
  name: 'login',
  mixins: [getSetData],
  data() {
    return {
      formData: {},
      forget: false,
      code: '',
      corpId: '', // 企业id
      authCode: '',
      useSmsLogin: false,
      autologin: 'true',
    };
  },

  async created() {
    let res = await this.ajax.getGlobalSetting();
    this.showInstitutionSelect = res.object.platformLoginType == 2;
    this.$set(this, 'globalSetting', res.object);
    this.source = res.object.mobilePlatform == 2 ? 'dingTalk' : '';
    this.showSwitchTab = res.object.smscode == 1;
    this.logoImgPath = res.object.loginTopLogo
      ? `/ts-basics-bottom/fileAttachment/readFile/${res.object.loginTopLogo}`
      : '';
    this.loginPageBackgroundImgPath = res.object.loginPageBackground
      ? `url(/ts-basics-bottom/fileAttachment/readFile/${res.object.loginPageBackground})`
      : require('@/assets/images/login-background-img.png');
  },

  async mounted() {
    const vm = this;

    let searchs = location.href.split('?')[1] || '';
    searchs.split('&').forEach((item) => {
      if (item.indexOf('code') >= 0) {
        this.code = item.split('=')[1];
      }
      if (item.indexOf('corpId') >= 0) {
        this.corpId = item.split('=')[1];
      }
      if (item.indexOf('autologin') >= 0) {
        this.autologin = item.split('=')[1];
      }
    });

    this.$set(
      this.formData,
      'usercode',
      this.$store.state.common.account || localStorage.getItem('account') || ''
    );
    this.$set(
      this.formData,
      'password',
      this.$store.state.common.password ||
        (localStorage.getItem('password') &&
          Decrypt(localStorage.getItem('password'))) ||
        ''
    );

    // 不延迟 拿不到 showInstitutionSelect
    await new Promise((resolve, reject) => {
      setTimeout(() => {
        resolve();
      }, 400);
    });

    if (this.showInstitutionSelect) {
      let usercode = this.formData.usercode;
      let res = await this.ajax.getMultipleOrg(usercode);

      if (res.success == false) {
        this.$message.error(res.message || '系统错误');
        return;
      }

      if (res.object && res.object.length) {
        this.$set(this.formData, 'orgCode', res.object[0].orgCode);
        this.$set(this.formData, 'orgName', res.object[0].orgName);
      }
    }

    if (this.source === sourceId && dingtalk.env.platform != 'notInDingTalk') {
      // 钉钉登录
      dingtalk.ready(() => {
        dingtalk.runtime.permission.requestAuthCode({
          corpId: this.corpId, // 企业id
          onSuccess: (info) => {
            vm.authCode = info.code; // 通过该免登授权码可以获取用户身份
            !this.formData.usercode && (this.autologin = false);

            if (this.autologin === autologinId) {
              let accountFormData = Object.assign({}, this.formData, {
                authCode: info.code,
                source: this.source,
              });
              this.handleAccountFormSubmit(accountFormData);
            }
          },
        });
      });
    } else if (this.formData.usercode && this.formData.password) {
      if (localStorage.getItem('values')) {
        let values = JSON.parse(localStorage.getItem('values'));
        this.$set(this.formData, 'orgCode', values.orgCode);
        this.$set(this.formData, 'orgName', values.orgName);
      }
      this.handleAccountFormSubmit(this.formData);
    }
  },
  methods: {
    changeTab(tab, ref) {
      if (tab != '3') {
        this.tabType = tab;
        this.forget = false;
      } else {
        this.forget = true;
      }
      this.formRef = ref;
      if (tab != '4' && tab != '3') {
        this.formData = {};
      }
      this.$nextTick(() => {
        if (tab == 1) {
          this.$refs.accountLoginForm.$refs.usercode.focus();
        } else if (tab == 2) {
          this.$refs.smsLoginForm.$refs.empPhone.focus();
        }
        if (tab == 4) {
          this.$refs.resetPasswordForm.$refs.password.focus();
        } else {
          this.$refs.forgeLoginForm.$refs.usercode.focus();
        }
      });
    },
    getSmsLoginVerifyCode(successCallback) {
      this.ajax
        .getSmsLoginVerifyCode({
          empPhone: this.formData.empPhone,
        })
        .then((res) => {
          successCallback();
          this.formData.token = res.object;
        })
        .catch((error) => {
          this.$toast({
            message: error.message,
            forbidClick: true,
          });
        });
    },
    getForLoginVerifyCode(successCallback, usercode, token) {
      this.ajax
        .getForgetLoginVerifyCode({
          token: token,
          userAccount: usercode,
        })
        .then((res) => {
          this.$toast({
            message: res.message,
            forbidClick: true,
          });
          successCallback();
          this.formData.token = res.object;
        })
        .catch((error) => {
          this.$toast({
            message: error.message,
            forbidClick: true,
          });
        });
    },
    handleLogin() {
      this.$refs[this.formRef].submit();
    },
    //账号密码登录
    async handleAccountFormSubmit(values) {
      if (this.showInstitutionSelect) {
        if (!values.orgCode) {
          this.$toast({
            message: '请选择机构',
            forbidClick: true,
          });
          return;
        }
      }

      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '登录中...',
        forbidClick: true,
        duration: 0,
      });

      let url = '',
        searchList = location.search.split('?')[1] || '',
        hashUrlString = searchList
          .split('&')
          .filter((item) => item.indexOf('url') >= 0),
        hashUrl = hashUrlString[0] && hashUrlString[0].split('=')[1];

      if (hashUrl) {
        hashUrl = decodeURIComponent(hashUrl);
      }
      let requestData = {
        ...values,
        code: this.code,
        url: hashUrl,
        source: this.source,
      };
      if (
        this.source === sourceId &&
        dingtalk.env.platform != 'notInDingTalk'
      ) {
        let dingRes = await new Promise((resolve, reject) => {
          dingtalk.runtime.permission.requestAuthCode({
            corpId: this.corpId,
            onSuccess: function(result) {
              resolve(result.code);
            },
            onFail: function(err) {
              resolve('');
            },
          });
        }).catch((res) => res);
        this.authCode = dingRes;
        requestData.authCode = this.authCode;
      }
      // this.source === sourceId && (requestData.authCode = this.authCode);
      requestData.password = Encrypt(requestData.password.trim()).toString();
      await this.ajax
        .accountLogin(requestData)
        .then(async (data) => {
          localStorage.setItem('account', data.object.usercode);
          localStorage.setItem('password', requestData.password);
          localStorage.setItem(
            'values',
            JSON.stringify({ orgCode: values.orgCode, orgName: values.orgName })
          );
          url = data.object.url;

          this.$cookies.set('trasen_pwd', requestData.password);
          this.$cookies.set('trasen_user', data.object.usercode);
          this.$cookies.set('THPMSCookie', data.object.token);
          this.$cookies.set('token', data.object.token);
          this.$cookies.set('emp_code', data.object.usercode);

          this.$store.commit('common/setData', {
            label: 'token',
            value: data.object.token,
          });
          this.$store.commit('common/setData', {
            label: 'empCode',
            value: data.object.usercode,
          });
          let passwordCheckPass = this.checkLowerPassword();
          await this.setUserInfo(passwordCheckPass, url);
        })
        .catch((error) => {
          let errorMessage = error.message;
          if (error.message.includes('qyapi.weixin.qq.com')) {
            errorMessage = '企业微信外网不通，请联系管理员!';
          }
          this.$toast({
            message: errorMessage,
            forbidClick: true,
          });
        });
    },
    //判断是否为弱密码
    checkLowerPassword() {
      let checkPass = true;
      if (
        this.globalSetting.remindPassword &&
        !(this.source === sourceId && this.autologin === autologinId)
      ) {
        let level = pwdCheckStrong(
            this.formData.password,
            this.globalSetting.passwordLength
          ),
          rules = [];

        if (
          null != this.globalSetting &&
          null != this.globalSetting.passwordRule &&
          '' != this.globalSetting.passwordRule
        ) {
          rules = this.globalSetting.passwordRule.split(',');
        }
        if (level.level < rules.length || level.level == -1) {
          checkPass = false;
        }
        for (let i = 0; i < rules.length; i++) {
          if (!level.checkType[rules[i]]) {
            checkPass = false;
          }
        }
        if (checkPass) {
          localStorage.setItem('eassy_password', false);
        } else {
          localStorage.setItem('eassy_password', true);
          this.$toast({
            message: '当前密码强度较低，为保护账号安全，请尽快更新密码',
          });
        }
      }
      return checkPass;
    },
    checkMustUpdatePassword() {
      if (this.globalSetting.passwordExpire != 1) return true;
      if (this.useSmsLogin) return true;
      let checkData = this.$store.state.common.userInfo.passwordExpireDate
        ? this.$store.state.common.userInfo.passwordExpireDate.split(' ')[0]
        : '';
      if (checkData == '') return false;
      let days = moment(moment().format('YYYY-MM-DD')).diff(
        moment(checkData),
        'day'
      );
      if (days >= 0) {
        return false;
      }
      return true;
    },
    async loginPswIsEqualPasswordPreset() {
      const res = await this.ajax.getAllGlobalSetting();
      if (!res.success) {
        this.$toast({
          message: res.message || res.object,
        });
      }
      return this.formData.password !== res.object?.passwordPreset;
    },
    async setUserInfo(passwordCheckPass = true, url) {
      await this.ajax
        .getUserInfo()
        .then(async (userInfoRes) => {
          this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
          this.$store.commit('common/setData', {
            label: 'userInfo',
            value: userInfoRes.object,
          });
          this.$toast.clear();
          let mustUpdatePassword = this.checkMustUpdatePassword();

          let pswEqualResult = true;
          if (this.globalSetting.modifyDefaultPwd == 1) {
            pswEqualResult = await this.loginPswIsEqualPasswordPreset();
          }
          if (passwordCheckPass && mustUpdatePassword && pswEqualResult) {
            if (this.$route.query.returnURL != undefined) {
              let search = '';
              for (const key in this.$route.query) {
                if (key !== 'returnURL') {
                  if (search === '') {
                    search = '?';
                  }
                  search = `${search}${key}=${this.$route.query[key]}&`;
                }
              }
              window.location.href = this.$route.query.returnURL + search;
            } else {
              if (url) {
                url.indexOf('http') >= 0
                  ? (window.location.href = url)
                  : await this.$router.replace(url);
              } else {
                let defaultPath =
                  {
                    jssrmyy: '/my',
                  }[this.globalSetting.orgCode] || '/workbench';
                // await this.$router.replace(defaultPath);
                let url =
                  window.location.href.split('/login')[0] + `${defaultPath}`;
                window.location.href = url;
              }
            }
          } else {
            let path = '/ts-mobile-oa/pages/personalCenter/setting/password';
            if (!mustUpdatePassword) {
              this.$toast({
                message: '您的密码已超期，请您重置密码',
                forbidClick: true,
                duration: 3000,
                onClose: () => {
                  this.$router.replace(
                    '/ts-mobile-oa/pages/personalCenter/setting/password?fromPage=login'
                  );
                },
              });
            } else if (!pswEqualResult) {
              this.$toast({
                message: '登陆密码与系统初始密码相同，请您重置密码',
                forbidClick: true,
                duration: 3000,
                onClose: () => {
                  localStorage.setItem('eassy_password', true);
                  this.$router.replace(path);
                },
              });
            } else {
              this.$router.replace(path);
            }
          }

          this.$root.$emit('initQiankun1');
        })
        .catch((error) => {
          this.$toast({
            message: error.message,
            forbidClick: true,
          });
        });
    },
    async handleForFormSubmit(values) {
      let param = {
        userAccount: this.formData.usercode,
        verifyCode: this.formData.verifyCode,
        token: this.formData.token,
      };
      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '验证中...',
        forbidClick: true,
        duration: 0,
      });
      await this.ajax
        .checkverifyCoder(param)
        .then(async (res) => {
          this.$toast.clear();
          if (!res.success) {
            this.$toast({
              message: res.message,
              forbidClick: true,
            });
            return;
          }
          this.formData.token = res.object;
          this.changeTab(4, 'resetPasswordForm');
        })
        .catch((e) => {
          this.$toast({
            message: e.message,
            forbidClick: true,
          });
        });
    },
    async handlereSetFormSubmit(values) {
      let password = Encrypt(this.formData.password.trim()).toString();
      let param = {
        userAccount: this.formData.usercode,
        newPassword: password,
        token: this.formData.token,
      };
      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '修改中...',
        forbidClick: true,
        duration: 0,
      });
      await this.ajax
        .resetPassword(param)
        .then(async (res) => {
          this.$toast.clear();
          if (!res.success) {
            this.$toast({
              message: res.message || res.object,
            });
          }
          this.$toast({
            message: res.message || res.object,
          });
          this.changeTab(1, 'accountLoginForm');
        })
        .catch((e) => {
          this.$toast({
            message: e,
            forbidClick: true,
            duration: 0,
          });
        });
    },
    //短信登录
    async handleSMSFormSubmit(values) {
      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '登录中...',
        forbidClick: true,
        duration: 0,
      });
      await this.ajax
        .smsLogin(this.formData)
        .then(async (res) => {
          this.$cookies.set('THPMSCookie', res.object.token);
          this.$cookies.set('token', res.object.token);
          this.$cookies.set('emp_code', res.object.usercode);
          this.$store.commit('common/setData', {
            label: 'token',
            value: res.object.token,
          });
          this.$store.commit('common/setData', {
            label: 'empCode',
            value: res.object.usercode,
          });
          this.useSmsLogin = true;
          await this.setUserInfo();
        })
        .catch((e) => {});
    },
    showErrorMessage(errorInfo) {
      this.$toast({
        message: errorInfo.errors[0].message,
        forbidClick: true,
        onOpened: () => {
          let ref = errorInfo.errors[0].name;
          this.$refs[this.formRef].$refs[ref]?.focus();
        },
      });
    },
  },
};
