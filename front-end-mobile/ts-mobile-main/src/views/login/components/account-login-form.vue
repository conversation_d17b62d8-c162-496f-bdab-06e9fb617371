<template>
  <van-form
    ref="accountLoginForm"
    :show-error="false"
    :validate-first="true"
    :show-error-message="false"
    @submit="onAccountFormSubmit"
    @failed="showErrorMessage"
  >
    <van-field
      style="display: none;"
      v-if="showInstitutionSelect"
      v-model="accountForm.orgCode"
      name="orgCode"
    />
    <van-field
      v-model="accountForm.usercode"
      ref="usercode"
      key="usercode"
      name="usercode"
      type="text"
      placeholder="请输入账号"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[{ required: true, message: '请输入账号' }]"
      @blur="handleAccountBlur"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="wo"
          :size="22"
          color="#999999"
        />
      </template>
    </van-field>

    <van-field
      v-model="accountForm.password"
      ref="password"
      key="password"
      :type="showPassword ? '' : 'password'"
      name="password"
      placeholder="请输入密码"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[{ required: true, message: '请输入密码' }]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="suo"
          :size="22"
          color="#999999"
        />
      </template>
      <template #right-icon>
        <van-icon
          :name="!showPassword ? 'eye-o' : 'eye'"
          :size="22"
          color="#999999"
          @click="showPassword = !showPassword"
        />
      </template>
    </van-field>

    <van-field
      v-if="showInstitutionSelect"
      key="orgName"
      v-model="accountForm.orgName"
      name="orgName"
      placeholder="请选择机构"
      size="large"
      :readonly="true"
      @click-input="handleClickInput"
      :clearable="true"
      :rules="[{ required: true, message: '请选择机构' }]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="yiyuanjianzhu"
          :size="22"
          color="#999999"
        />
      </template>
    </van-field>

    <select-popup v-model="showOrg" height="400px">
      <van-picker
        :columns="orgColumns"
        show-toolbar
        @cancel="onCancelOrg"
        @confirm="onConfirmOrg"
      />
    </select-popup>
  </van-form>
</template>

<script>
import SelectPopup from '@/components/person-select/select-popup.vue';
export default {
  name: 'account-login-form',
  components: {
    SelectPopup
  },
  model: {
    event: 'change',
    prop: 'accountForm'
  },
  props: {
    showInstitutionSelect: {
      type: Boolean,
      default: false
    },
    accountForm: {
      type: Object,
      default: () => {}
    },
    showErrorMessage: {
      type: Function,
      defalut: () => undefined
    }
  },
  data() {
    return {
      showPassword: false,
      showOrg: false,

      institutionList: [],
      orgColumns: []
    };
  },
  methods: {
    submit() {
      this.$refs.accountLoginForm.submit();
    },
    handleAccountBlur() {
      let usercode = this.accountForm.usercode;
      if (!this.showInstitutionSelect) return;

      this.ajax.getMultipleOrg(usercode).then(res => {
        if (res.success == false) {
          this.$message.error(res.message || '系统错误');
          return;
        }

        if (res.object && res.object.length) {
          this.institutionList = res.object;
          this.orgColumns = res.object.map(item => item.orgName);

          this.$set(this.accountForm, 'orgCode', res.object[0].orgCode);
          this.$set(this.accountForm, 'orgName', res.object[0].orgName);
        }
      });
    },
    handleClickInput() {
      this.showOrg = true;
    },
    onCancelOrg() {
      this.showOrg = false;
    },
    onConfirmOrg(val, index) {
      let orgCode = this.institutionList[index].orgCode;
      let orgName = this.institutionList[index].orgName;
      this.$set(this.accountForm, 'orgCode', orgCode);
      this.$set(this.accountForm, 'orgName', orgName);
      this.showOrg = false;
    },
    onAccountFormSubmit(values) {
      this.$emit('submit', values);
    }
  }
};
</script>

<style scoped lang="scss">
.van-cell {
  padding: 10px 0;
}
.van-cell::after {
  right: 0;
  left: 0;
}
</style>
