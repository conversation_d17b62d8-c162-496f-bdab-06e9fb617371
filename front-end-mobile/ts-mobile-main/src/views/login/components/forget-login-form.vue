<template>
  <van-form
    ref="forgeLoginForm"
    :show-error="false"
    :validate-first="true"
    :show-error-message="false"
    @submit="onSMSFormSubmit"
    @failed="showErrorMessage"
  >
    <van-field
      v-model="forgetForm.usercode"
      ref="usercode"
      name="usercode"
      placeholder="请输入账号"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[
        { required: true, message: '请输入手机号码' },
      ]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="wo"
          :size="22"
          color="#999999"
        />
      </template>
    </van-field>
    <van-field
      v-model="forgetForm.verifyCode"
      ref="verifyCode"
      name="verifyCode"
      placeholder="请输入验证码"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[{ required: true, message: '请输入验证码' }]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="youxiang1"
          :size="22"
          color="#999999"
        />
      </template>
      <template #button>
        <van-button
          ref="verifyBtn"
          type="info"
          plain
          size="small"
          class="verify-btn"
          native-type="button"
          @click="valdiateUser"
        >
          获取短信验证码
        </van-button>
      </template>
    </van-field>
  </van-form>
</template>
<script>
export default {
  name: 'sms-login-form',
  model: {
    event: 'change',
    prop: 'forgetForm'
  },
  props: {
    forgetForm: {
      type: Object,
      default: () => {}
    },
    showErrorMessage: {
      type: Function,
      defalut: () => undefined
    }
  },
  data() {
    return {
      verifyCodeToekn: '',
      canSms: false
    };
  },
  methods: {
    async valdiateUser() {
      try {
        await this.$refs.forgeLoginForm.validate('usercode');
        this.ajax.valdiateUser({ userAccount: this.forgetForm.usercode}).then(res => {
          if(!res.success){
            this.canSms = false;
            this.$toast({
              message: '请输入有效的账号',
              forbidClick: true,
            });
            return;
          }
          this.$emit('getVerifyCode', () => {
            this.$refs.verifyBtn.disabled = true;
            this.countdown('verifyBtn', '获取短信验证码');
            this.$refs.verifyCode.focus();
          }, this.forgetForm.usercode, res.object);
          this.canSms = true;
        });
      } catch (e) {
        this.$toast({
          message: e.message,
          forbidClick: true,
          onOpened: () => {
            this.$refs.usercode.focus();
          }
        });
      }
    },
    //倒计时
    countdown(ref, text) {
      let time = 120;
      this.$refs[ref].innerHTML = time + '秒后重新获取';
      let timer = setInterval(() => {
        time--;
        this.$refs[ref].innerHTML = time + '秒后重新获取';
        if (timer && time == 0) {
          this.$refs[ref].innerHTML = text;
          this.$refs[ref].disabled = false;
          clearInterval(timer);
        }
      }, 1000);
      this.$once('hook:beforeDestroy', () => {
        if (timer) {
          clearInterval(timer);
          timer = null;
        }
      });
    },
    submit() {
      this.$refs.forgeLoginForm.submit();
    },
    onSMSFormSubmit(values) {
      this.$emit('submit', values);
    }
  }
};
</script>

<style scoped lang="scss">
.verify-btn {
  border: 0;
  background: none;
}
.van-cell {
  padding: 10px 0;
}
.van-cell::after {
  right: 0;
  left: 0;
}
</style>
