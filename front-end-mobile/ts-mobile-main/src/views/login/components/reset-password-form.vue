<template>
  <van-form
    ref="resetPasswordForm"
    :show-error="false"
    :validate-first="true"
    :show-error-message="false"
    @submit="onAccountFormSubmit"
    @failed="showErrorMessage"
  >
    <van-field
      v-model="passwordForm.password"
      ref="password"
      :type="showPassword ? '' : 'password'"
      name="password"
      placeholder="请输入新密码"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[{ required: true, message: '请输入新密码' }]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="suo"
          :size="22"
          color="#999999"
        />
      </template>
      <template #right-icon>
        <van-icon
          :name="!showPassword ? 'eye-o' : 'eye'"
          :size="22"
          color="#999999"
          @click="showPassword = !showPassword"
        />
      </template>
    </van-field>
    <van-field
      v-model="passwordForm.confirmPassword"
      ref="confirmPassword"
      :type="showPassword ? '' : 'password'"
      name="password"
      placeholder="请再次输入新密码"
      size="large"
      autocomplete="off"
      :clearable="true"
      :rules="[{ required: true, message: '请再次输入新密码' }]"
    >
      <template #left-icon>
        <van-icon
          class-prefix="main-icon"
          name="suo"
          :size="22"
          color="#999999"
        />
      </template>
      <template #right-icon>
        <van-icon
          :name="!showPassword ? 'eye-o' : 'eye'"
          :size="22"
          color="#999999"
          @click="showPassword = !showPassword"
        />
      </template>
    </van-field>
  </van-form>
</template>

<script>
export default {
  name: 'account-login-form',
  model: {
    event: 'change',
    prop: 'passwordForm'
  },
  props: {
    passwordForm: {
      type: Object,
      default: () => {}
    },
    showErrorMessage: {
      type: Function,
      defalut: () => undefined
    }
  },
  data() {
    return {
      showPassword: false,
    };
  },
  methods: {
    submit() {
      this.$refs.resetPasswordForm.submit();
    },
    handleClickInput() {
      this.showOrg = true;
    },

    onAccountFormSubmit(values) {
      if(this.passwordForm.password.trim().length < 6){
        this.$toast({
          message: '新密码不能少于6位',
        });
        return;
      }
      if(this.passwordForm.password.trim() != this.passwordForm.confirmPassword.trim()) {
        this.$toast({
          message: '两次密码不一致，请重新输入',
        });
        return;
      }
      this.$emit('submit', values);
    }
  }
};
</script>

<style scoped lang="scss">
.van-cell {
  padding: 10px 0;
}
.van-cell::after {
  right: 0;
  left: 0;
}
</style>
