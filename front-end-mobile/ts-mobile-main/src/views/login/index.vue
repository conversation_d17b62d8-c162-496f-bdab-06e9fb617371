<template>
  <div class="login-content">
    <div
      class="login-top"
      :style="{
        backgroundImage: loginPageBackgroundImgPath
      }"
    >
      <div v-if="logoImgPath" class="logo-banner">
        <img class="logo-img" :src="logoImgPath" />
      </div>
    </div>
    <div class="login-form">
      <p class="title">综合协同办公平台</p>
      <account-login-form
        v-if="tabType == 1 && !forget"
        ref="accountLoginForm"
        :accountForm="formData"
        :showErrorMessage="showErrorMessage"
        :showInstitutionSelect="showInstitutionSelect"
        @submit="handleAccountFormSubmit"
      ></account-login-form>
      <sms-login-form
        v-else-if="tabType == 2 && !forget"
        ref="smsLoginForm"
        :smsForm="formData"
        :showErrorMessage="showErrorMessage"
        @getVerifyCode="getSmsLoginVerifyCode"
        @submit="handleSMSFormSubmit"
      ></sms-login-form>
      <forget-login-form
        v-show="forget"
        ref="forgeLoginForm"
        :forgetForm="formData"
        :showErrorMessage="showErrorMessage"
        @getVerifyCode="getForLoginVerifyCode"
        @submit="handleForFormSubmit"
      ></forget-login-form>
      <reset-password-form
        v-show="tabType == 4"
        ref="resetPasswordForm"
        :passwordForm="formData"
        :showErrorMessage="showErrorMessage"
        @submit="handlereSetFormSubmit"
      ></reset-password-form>
      <div style="margin: 16px;">
        <van-button round block type="info" @click="handleLogin">
          {{ forget ? '下一步' : '提交' }}
        </van-button>
        <!-- <p class="option-box" v-if="showSwitchTab"> -->
          <div class="option-box-copy">
            <div :class="!globalSetting.forgetPwd ? 'noForget' : ''" v-if="showSwitchTab">
              <span v-if="tabType == 2 && !forget" @click="changeTab(1, 'accountLoginForm')">
                账号登录
              </span>
              <span v-if="tabType == 1 && !forget" @click="changeTab(2, 'smsLoginForm')">
                短信登录
              </span>
            </div>
            <div v-if="globalSetting.forgetPwd" :class="!showSwitchTab ? 'noForget' : ''">
              <span v-if="!forget && tabType !=4 " @click="changeTab(3, 'forgeLoginForm')">
                忘记密码
              </span>
              <span v-if="forget || tabType == 4" @click="changeTab(1, 'accountLoginForm')">
                返回登录
              </span>
            </div>
          </div>
          <!-- <span v-if="tabType == 1">忘记密码</span> -->
        <!-- </p> -->
      </div>
    </div>
    <div class="login-bottom" v-show="isShowFooter">
      <a
        class="record-link"
        :href="$store.state.common.globalSetting.recordLinkUrl"
      >
        {{ $store.state.common.globalSetting.recordNumber }}
      </a>
      <p class="original">
        {{
          `综合协同办公平台${$store.state.common.globalSetting.originalContent}`
        }}
      </p>
    </div>
  </div>
</template>

<script>
import accountLoginForm from './components/account-login-form.vue';
import smsLoginForm from './components/sms-login-form.vue';
import forgetLoginForm from './components/forget-login-form.vue';
import resetPasswordForm from './components/reset-password-form.vue';
import index from './index.js';
export default {
  components: { accountLoginForm, smsLoginForm, forgetLoginForm, resetPasswordForm },
  name: 'index',
  mixins: [index]
};
</script>

<style scoped lang="scss">
.login-content {
  height: 100%;
  background-color: #ffffff;
  overflow-x: hidden;
}
.login-top {
  height: 203px;
  position: relative;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.logo-banner {
  height: 70px;
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  bottom: 0;
  width: 100%;
  left: 0;
  box-sizing: border-box;
  text-align: center;
}
.logo-img {
  height: 100%;
  background-position: center center;
  background-size: cover;
  background-repeat: no-repeat;
}
.login-form {
  margin: 0 45px;
}
.title {
  font-size: 16px;
  color: #666666;
  margin: 35px 0 20px;
  font-weight: bold;
}
.main-icon {
  font-size: 22px;
  color: #999;
  margin-right: 15px;
}
.noForget {
  width: 100%;
  text-align: center;
}
.option-box {
  color: #666666;
  text-align: center;
  span {
    margin: 0 10px;
    position: relative;
    &::before {
      content: '';
      position: absolute;
      width: 1px;
      top: 0;
      bottom: 0;
      left: -10px;
      background-color: #666666;
    }
    &:first-child {
      &::before {
        width: 0;
      }
    }
  }
}
.option-box-copy {
  color: #666666;
  display: flex;
  margin-top: 10px;
  justify-content: space-between;
}
.login-bottom {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}
.record-link,
.original {
  display: block;
  color: #999999;
  text-align: center;
  font-size: 10px;
}
</style>
