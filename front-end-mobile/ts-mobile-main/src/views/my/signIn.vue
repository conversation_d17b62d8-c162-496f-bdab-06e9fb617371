<template>
  <div>
    <van-popup v-model="show" round :style="{ height: '20%',width: '80%',minHeight: '320px' }">
      <div class="title">签到</div>
      <div class="content">
        <div class="choose">
          <van-field
            v-model="opinion"
            ref="opinion"
            name="opinion"
            :placeholder="placeholder"
            type="textarea"
            :maxlength="800"
            rows="10"
          />
        </div>
      </div>
      <div class="footer">
        <div class="submit btn" @click="submit">签到</div>
      </div>
    </van-popup>
  </div>
</template>
<script>
export default {
  data() {
    return {
      show: false,
      opinion: '',
      placeholder: '请输入科室意见',
      applyId: ''
    };
  },
  methods: {
    close() {
      this.show = false;
      this.opinion = '';
      this.applyId = '';
    },
    open(applyId) {
      this.applyId = applyId;
      this.show = true;
    },
    submit() {
      let data = {
        applyId: this.applyId,
        signComment: this.opinion
      };
      this.ajax.consultApplySignIn(data).then(res => {
        this.$toast({
          message: '签到成功',
          forbidClick: true
        });
        this.close();
      }).catch(e => {
        this.$toast({
          message: '签到失败',
          forbidClick: true
        });
        this.close();
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.footer {
  display: flex;
  font-size: 14px;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40px;
  align-items: center;
  justify-content: center;
  div {
    width: 50%;
    height: 30px;
    text-align: center;
    border-radius: 10px;
  }
  .submit {
    line-height: 30px;
    color: #fff;
    background: #5260ff;
  }
}
.title {
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  margin-top: 5px;
}
.content {
  padding: 10px 10px;
  .choose {
    line-height: 50px;
    .right {
      color: #ccc;
    }
  }
}
</style>
