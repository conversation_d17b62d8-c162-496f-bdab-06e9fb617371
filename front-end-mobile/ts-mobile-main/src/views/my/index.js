import wxInit from '../../utils/wxInit';
export default {
  data() {
    return {
      myJumpItemArr: [
        {
          name: '文档管理',
          link: 'ts-mobile-oa/pages/file/file?fromPage=my&index=0',
          img: require('@/assets/images/document-operation.png')
        },
        {
          name: '电子邮箱',
          link: 'ts-mobile-oa/pages/email/email-list?fromPage=my&index=0',
          img: require('@/assets/images/email-operation.png')
        },
        {
          name: '工资条',
          link: 'ts-mobile-oa/pages/payslip/salary-details?fromPage=my&index=0',
          img: require('@/assets/images/wages-operation.png')
        }
      ],
      mianOperationArr: [
        {
          name: '个人档案',
          id: 999,
          moreSpan: '',
          style: '',
          link:
            '/ts-mobile-oa/pages/personalCenter/edit/edit?fromPage=my&index=0',
          img: require('@/assets/images/my-person-item.png')
        },
        {
          name: '常用语',
          link:
            '/ts-mobile-oa/pages/personalCenter/settingApproval/common-words',
          img: require('@/assets/images/my-common-words.png')
        },
        {
          name: '个人设置',
          link:
            '/ts-mobile-oa/pages/personalCenter/setting/setting?fromIndex=my',
          img: require('@/assets/images/my-set-up.png')
        },
        {
          name: '扫一扫',
          link: '#',
          img: require('@/assets/images/scan.png')
        },
        {
          name: '版本信息',
          moreSpan: '版本1.0.2',
          link:
            '/ts-mobile-oa/pages/personalCenter/settingPersonal/version?fromIndex=my',
          img: require('@/assets/images/my-edition.png')
        }
      ]
    };
  },
  async created() {
    let userInfoRes = await this.ajax.getUserInfo();
    this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
    this.$store.commit('common/setData', {
      label: 'userInfo',
      value: userInfoRes.object
    });
    this.handleGetProcessStatus();

    if (!this.showEmail) {
      let index = this.myJumpItemArr.findIndex(
        item => item.name === '电子邮箱'
      );
      if (index > -1) this.myJumpItemArr.splice(index, 1);
    }
  },
  async beforeMount() {
    this.$store.state.common.globalSetting.mobilePlatform != 2 &&
      (await this.getWxJsapiSignature());
  },
  computed: {
    userImg() {
      return (
        this.$store.state.common.userInfo.avatar ||
        require('@/assets/images/head-img.png')
      );
    },
    username() {
      return this.$store.state.common.userInfo.employeeName || '';
    },
    employeeId() {
      return this.$store.state.common.userInfo.employeeId || '';
    },
    employeeNo() {
      return this.$store.state.common.userInfo.employeeNo || '';
    },
    phoneNumber() {
      return this.$store.state.common.userInfo.phoneNumber || '';
    },
    orgName() {
      return this.$store.state.common.userInfo.orgName || '';
    },
    employDuty() {
      return this.$store.state.common.userInfo.employDuty || '';
    },
    showEmail() {
      return (
        this.$store.state.common.globalSetting.orgCode !== '浏阳市妇幼保健院'
      );
    }
  },
  methods: {
    //获取签名信息并初始化jdk
    async getWxJsapiSignature() {
      await this.ajax
        .getWxJsapiSignature({
          REFERER: this.$store.state.common.token,
        })
        .then((res) => {
          wxInit.initwxJdk(res.object);
        });
    },
    async goto(item) {
      if (item.link == '#') {
        wxInit.scanQRCode(this.scanQRCodeCallbackTo);
      } else {
        this.$router.push(item.link);
      }
    },
    async scanQRCodeCallbackTo(res) {
      let res1 = await this.ajax.signInVerify(res.resultStr);
      if (res1.success) {
        this.$refs.signIn.open(res.resultStr);
      } else {
        this.$toast({ message: res1.message });
      }
    },
    async handleGetProcessStatus() {
      let status = null,
        data = [],
        dir = {
          1: '审批中',
          2: '审批结束',
          3: '驳回'
        },
        find = this.mianOperationArr.find(item => item.id === 999);

      let res = await this.ajax.customEmployeeBaseInApproval();
      if (res.success && res.object) {
        status = res.object.status;
        data = (res.object.data || []).filter(f => f.auditStatus == 2);
      }
      find.moreSpan = dir[status];

      // 审批中 获取流程信息展示
      if (status == '1') {
        let result = await this.cusotmEmployeeGetEmployeeTask();

        if (result && result?.isAdmin == 'false') {
          let processText = '';
          if (result.stepName && result.assigneeNames) {
            processText = `${result.stepName} -【${result.assigneeNames}】`;
          }
          find.moreSpan = `${processText} ${find.moreSpan}`;
        }
        return false;
      }

      // 审批中 获取流程信息展示
      if (status == '2') {
        if (data.length == 0) {
          find.moreSpan = find.moreSpan + '（提示：全部通过）';
        } else {
          find.moreSpan = find.moreSpan + `（提示：有${data.length}项不合格）`;
        }
      }

      // 驳回优先级高 不展示赞存信息
      if (status != '3') {
        let result = await this.cusotmEmployeeGetstorage();
        if (!result || !Object.keys(result).length) return;
        find.moreSpan = `有暂存信息，请及时补充完整提交`;
        find.className = `red`;
      }
    },
    async cusotmEmployeeGetstorage() {
      try {
        let res = await this.ajax.customEmployeeBaseGetStorage({
          groupId: 'all'
        });
        if (!res.success) return false;
        return res.object?.[0];
      } catch (error) {
        return false;
      }
    },
    async cusotmEmployeeGetEmployeeTask() {
      try {
        let res = await this.ajax.customEmployeeBaseGetEmployeeTask(
          this.employeeNo
        );
        if (!res.success) return false;
        return res.object;
      } catch (error) {
        return false;
      }
    }
  }
};
