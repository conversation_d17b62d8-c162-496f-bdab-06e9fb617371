<template>
  <div class="ts-container" id="MainMyBox">
    <div class="mian-my-background">
      <div class="background-top">我的</div>

      <div class="background-info">
        <div class="info-left">
          <img class="info-user-img" :src="userImg" />
          <div class="info-content">
            <p>
              <span class="info-userName">{{ username }}</span>
              <span class="info-userTel">{{ phoneNumber }}</span>
            </p>
            <span>{{ orgName }}</span>
            <span>{{ employDuty }}</span>
          </div>
        </div>

        <img
          @click="
            $router.push(
              '/ts-mobile-oa/pages/personalCenter/edit/edit?fromPage=my&index=0'
            )
          "
          src="@/assets/images/mian-info-edit.png"
        />
      </div>

      <ul class="backgroun-operation">
        <li
          v-for="item in myJumpItemArr"
          :key="item.link"
          @click="$router.push(item.link)"
        >
          <img :src="item.img" alt="" />
          <span>{{ item.name }}</span>
        </li>
      </ul>
    </div>

    <ul class="my-item-ul">
      <li
        v-for="item in mianOperationArr"
        :key="item.link"
        @click="goto(item)"
      >
        <div class="li-type">
          <img :src="item.img" alt="" />
          <span>{{ item.name }}</span>
        </div>
        <div class="li-operation">
          <span :class="item.className">{{ item.moreSpan || '' }}</span>
          <img src="@/assets/images/my-jump-mor.png" alt="" />
        </div>
      </li>
    </ul>
    <signIn ref="signIn" />
  </div>
</template>

<script>
import index from './index.js';
import signIn from './signIn.vue';
export default {
  name: 'my',
  components: { signIn },
  mixins: [index]
};
</script>

<style lang="scss" scoped>
.ts-container {
  display: flex;
  flex-direction: column;
}

#MainMyBox {
  * {
    box-sizing: border-box;
  }

  .red {
    color: red !important;
  }

  overflow: scroll;
  height: 100%;

  .mian-my-background {
    width: 100%;
    height: 264px;
    position: relative;
    background: no-repeat center 80%
      url('../../assets/images/mian-my-background-img.png');

    .background-top {
      width: 100%;
      height: 40px;
      position: absolute;
      top: 0;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      font-weight: 600;
      color: #333333;
    }

    .background-info {
      width: 100%;
      height: 150px;
      padding: 0 16px;
      position: absolute;
      top: 40px;
      left: 0;
      display: flex;
      align-items: center;

      .info-left {
        display: flex;

        .info-user-img {
          width: 70px;
          height: 70px;
          margin-right: 8px;
          border-radius: 50%;
          object-fit: cover;
        }

        .info-content {
          display: flex;
          flex-direction: column;
          justify-content: space-around;
          font-size: 12px;
          font-weight: 400;
          color: #666666;

          p {
            margin: 0;

            .info-userName {
              font-size: 16px;
              font-weight: 400;
              color: #333333;
              line-height: 22px;
              margin-right: 8px;
            }

            .info-userTel {
              color: #333333;
            }
          }
        }
      }

      > img {
        position: absolute;
        right: 16px;
        top: 16px;
        width: 18px;
        height: 18px;
      }
    }

    .backgroun-operation {
      position: absolute;
      width: calc(100% - 32px);
      height: 114px;
      top: 190px;
      left: 16px;
      background: #ffffff;
      box-shadow: 0px 3px 10px 0px rgba(45, 118, 235, 0.08);
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: space-around;

      li {
        width: 33%;
        display: flex;
        flex-direction: column;
        align-items: center;

        > img {
          width: 40px;
          height: 40px;
          margin-bottom: 8px;
        }

        > span {
          font-size: 12px;
          font-weight: 400;
          color: #333333;
          line-height: 18px;
        }
      }
    }
  }

  .my-item-ul {
    margin-top: 60px;
    width: 100%;
    padding-left: 16px;
    background: #fff;

    li {
      height: 56px;
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #f4f4f4;

      > .li-type {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        img {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }

        span {
          font-size: 14px;
          font-weight: 600;
          color: #333333;
          line-height: 22px;
        }
      }

      > .li-operation {
        display: flex;
        align-items: center;
        padding: 0 20px;
        font-size: 10px;
        font-weight: 400;
        color: #666666;
        line-height: 18px;

        img {
          width: 16px;
          height: 16px;
          margin-left: 5px;
        }
      }
    }
  }
}
</style>
