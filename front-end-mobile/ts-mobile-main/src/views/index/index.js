export default {
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false
    };
  },
  computed: {
    headImg() {
      return this.$store.state.common.userInfo.avatar
        ? this.$store.state.common.userInfo.avatar
        : require('@/assets/images/head-img.png');
    },
    userText() {
      return `${this.$store.state.common.userInfo.orgName}-${this.$store.state.common.userInfo.employeeName}`;
    }
  },
  filters: {
    tagNum(num) {
      return num >= 100 ? '99+' : num;
    },
    formatTime(time) {
      if (time) {
        let current = new Date(),
          cY = current.getFullYear(),
          cM = current.getMonth() + 1,
          cD = current.getDate(),
          currentStr = `${cY}年${cM}月${cD}日`,
          getDate = new Date(time.replace(/-/g, '/')),
          gY = getDate.getFullYear(),
          gM = getDate.getMonth() + 1,
          gD = getDate.getDate(),
          ghh = getDate.getHours(),
          gmm =
            getDate.getMinutes() < 10
              ? `0${getDate.getMinutes()}`
              : getDate.getMinutes(),
          getDateStr = `${gY}年${gM}月${gD}日`;
        if (currentStr === getDateStr) {
          return `今天 ${ghh}:${gmm}`;
        } else if (gY === cY) {
          return `${gM}月${gD}日 ${ghh}:${gmm}`;
        } else {
          return `${gY}年${gM}月${gD}日 ${ghh}:${gmm}`;
        }
      }
    }
  },
  methods: {
    async onLoad() {
      await this.ajax.getMessageRemindList().then(res => {
        res.object.map(item => {
          switch (item.title) {
            case '信息发布':
              item.urlStr = '/ts-mobile-oa/pages/index/info-unread-list';
              break;
            case '审批助手':
              item.urlStr = '/ts-mobile-oa/pages/index/work-unread-list';
              break;
            case '邮箱助手':
              item.urlStr = '/ts-mobile-oa/pages/index/email-unread-list';
              break;
            case '会议助手':
              item.urlStr = '/ts-mobile-oa/pages/index/boardroom-signin-list';
              break;
            case '收文助手':
              item.urlStr = '/ts-mobile-oa/pages/index/govfile-unread-list';
              break;
            case '工单助手':
              item.urlStr =
                '/ts-mobile-work-order/pages/work-order-message-list/index';
              break;
          }
        });
        this.list = [];
        this.list = this.list.concat(res.object);
      });
      if (this.refreshing) {
        this.refreshing = false;
      }
      this.loading = false;
      this.finished = true;
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onClick(item) {
      this.$router.push(`${item.urlStr}?formPage=index`);
    }
  }
};
