<template>
  <div class="ts-container">
    <div class="user-container">
      <img class="user-head-img" :src="headImg" />
      <span>{{ userText }}</span>
      <div class="line"></div>
      <div
        class="flex-col-center work-status-box"
        @click="handleShowSetUserStatuModal"
      >
        <span>{{ workStatusLable }}...</span>
        <base-icon
          class="main-icon-fanhui user-statu-icon"
          size="12"
        ></base-icon>
      </div>
    </div>
    <!-- 今日日程 -->
    <div class="today-schedule-box">
      <div class="schedule-title-box">
        {{ today }}
        <div class="flex-col-center" @click="handleGoToFUllSchedule">
          查看全部日程
          <base-icon
            class="main-icon-fanhui user-statu-icon"
            size="12"
            color="#A0A8B4"
          ></base-icon>
        </div>
      </div>
      <div
        v-for="(item, index) of scheduleList"
        :key="index"
        class="today-schedule-item flex-col-center"
      >
        <span class="schedule-time">
          {{
            item.scheduleStartTime +
              (item.scheduleEndTime ? '-' + item.scheduleEndTime : '')
          }}
        </span>
        <span class="schedule-name">
          {{ item.scheduleSubject }}
        </span>
      </div>
      <div v-if="!scheduleList.length" class="no-schedule">今日暂无日程</div>
    </div>

    <div class="list-container">
      <van-pull-refresh
        style="height: 100%;"
        v-model="refreshing"
        @refresh="onRefresh"
      >
        <van-list
          :finished="finished"
          finished-text="没有更多了"
          v-model="loading"
          @load="onLoad"
        >
          <div
            class="van-cell van-cell--large"
            v-for="(item, index) in list"
            :key="index"
            @click="onClick(item)"
          >
            <span
              class="van-cell__left-icon"
              :style="{ 'background-color': item.backgroundcolor }"
            >
              <base-icon
                :class="item.iconfont"
                color="#ffffff"
                :size="24"
              ></base-icon>
            </span>
            <div class="van-cell__title">
              <div class="cell-item_content">
                <div>{{ item.title }}</div>
                <span class="cell-item_content-time">
                  {{ item.createDate | formatTime }}
                </span>
              </div>
              <div
                class="van-cell__label cell-item_content"
                v-if="item.subject"
              >
                <p class="cell-item_content-note">{{ item.subject }}</p>
                <template v-if="item.noread">
                  <van-tag round type="primary">
                    {{ item.noread | tagNum }}
                  </van-tag>
                </template>
              </div>
            </div>
          </div>
        </van-list>
      </van-pull-refresh>
    </div>
    <van-action-sheet
      v-model="showSetUserStatuModal"
      class="user-statu-set-modal"
    >
      <div class="title">
        工作状态设置
        <base-icon
          class="main-icon-shanchu1"
          size="16"
          color="#a0a8b4"
          @click="showSetUserStatuModal = false"
        ></base-icon>
      </div>
      <div
        v-for="(item, index) of userStatuList"
        :key="index"
        @click="handleChangeUserStatu(item)"
        class="user-statu-item"
      >
        <div class="flex-col-center">
          <img
            v-if="item.itemImg"
            :src="'/ts-document/attachment/' + item.itemImg"
          />
          {{ item.itemName }}
        </div>

        <div class="flex-col-center">
          <!-- <div v-if="item.itemNameValue == '2'" class="flex-col-center">
            可同步会议日程
            <van-switch
              size="15px"
              inactive-color="#C6CEDC"
              style="margin-left: 4px;"
            />
          </div> -->
          <div class="checked-box">
            <img
              v-if="userStatuCode == item.itemNameValue"
              :src="require('@/assets/images/icon-xuanzhong.png')"
            />
          </div>
        </div>
      </div>
    </van-action-sheet>
  </div>
</template>

<script>
import index from './index.js';
import { $get } from '@/api/ajax';
import { PullRefresh, Tag, ActionSheet, Toast } from 'vant';
import baseIcon from '../../components/base-icon/index.vue';

export default {
  name: 'index',
  mixins: [index],
  components: {
    [PullRefresh.name]: PullRefresh,
    [Tag.name]: Tag,
    baseIcon,
    VanActionSheet: ActionSheet
  },
  data() {
    return {
      list: [],
      loading: false,
      finished: false,
      refreshing: false,
      showSetUserStatuModal: false, //工作状态设置
      userStatuList: [], //用户状态选择列表
      today: this.$dayjs().format('YYYY年MM月DD日'),
      scheduleList: []
    };
  },
  computed: {
    headImg() {
      return this.$store.state.common.userInfo.avatar
        ? this.$store.state.common.userInfo.avatar
        : require('@/assets/images/head-img.png');
    },
    userText() {
      return `${this.$store.state.common.userInfo.orgName}-${this.$store.state.common.userInfo.employeeName}`;
    },
    workStatusLable() {
      return (
        this.$store.state.common.userInfo.workStatusLable || '请选择工作状态'
      );
    },
    userStatuCode() {
      return this.$store.state.common.userInfo.workStatus;
    },
    showEmail() {
      return (
        this.$store.state.common.globalSetting.orgCode !== '浏阳市妇幼保健院'
      );
    }
  },
  filters: {
    tagNum(num) {
      return num >= 100 ? '99+' : num;
    },
    formatTime(time) {
      if (time) {
        let current = new Date(),
          cY = current.getFullYear(),
          cM = current.getMonth() + 1,
          cD = current.getDate(),
          currentStr = `${cY}年${cM}月${cD}日`,
          getDate = new Date(time.replace(/-/g, '/')),
          gY = getDate.getFullYear(),
          gM = getDate.getMonth() + 1,
          gD = getDate.getDate(),
          ghh = getDate.getHours(),
          gmm =
            getDate.getMinutes() < 10
              ? `0${getDate.getMinutes()}`
              : getDate.getMinutes(),
          getDateStr = `${gY}年${gM}月${gD}日`;
        if (currentStr === getDateStr) {
          return `今天 ${ghh}:${gmm}`;
        } else if (gY === cY) {
          return `${gM}月${gD}日 ${ghh}:${gmm}`;
        } else {
          return `${gY}年${gM}月${gD}日 ${ghh}:${gmm}`;
        }
      }
    }
  },
  methods: {
    async onLoad() {
      let res = await $get('/ts-information/information/getMessageRemindList');
      if (this.refreshing) {
        this.list = [];
        this.refreshing = false;
      }
      if (res.success) {
        res.object.map(item => {
          switch (item.title) {
            case '信息发布':
              item.urlStr = '/ts-mobile-oa/pages/index/info-unread-list';
              break;
            case '审批助手':
              item.urlStr = '/ts-mobile-oa/pages/index/work-unread-list';
              break;
            case '邮箱助手':
              item.urlStr = '/ts-mobile-oa/pages/index/email-unread-list';
              break;
            case '会议助手':
              item.urlStr = '/ts-mobile-oa/pages/index/boardroom-signin-list';
              break;
            case '收文助手':
              item.urlStr = '/ts-mobile-oa/pages/index/govfile-unread-list';
              break;
            case '工单助手':
              item.urlStr =
                '/ts-mobile-work-order/pages/work-order-message-list/index';
              break;
          }
        });
        this.list = this.list.concat(res.object);

        if (!this.showEmail) {
          let index = this.list.findIndex(item => item.title === '邮箱助手');
          if (index > -1) this.list.splice(index, 1);
        }
      }
      this.loading = false;
      this.finished = true;

      this.refreshUserInfo();
      this.getSchedule();
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false;
      // 重新加载数据
      // 将 loading 设置为 true，表示处于加载状态
      this.loading = true;
      this.onLoad();
    },
    onClick(item) {
      this.$router.push(`${item.urlStr}?formPage=index`);
    },
    handleShowSetUserStatuModal() {
      this.userStatuList = [];
      Promise.all([
        this.ajax.getDictionary('WORK_STATUS'),
        this.refreshUserInfo()
      ]).then(resList => {
        if (resList[0].success == false) {
          return;
        }
        this.userStatuList = resList[0].object || [];
        this.showSetUserStatuModal = true;
      });
    },
    handleChangeUserStatu(item) {
      Toast.loading();
      const { itemNameValue, itemName } = item;
      this.ajax.changeUserInfo({ workStatus: itemNameValue }).then(res => {
        if (res.success == false) {
          return;
        }
        Toast.clear();
        let userInfo = this.$store.state.common.userInfo;
        userInfo.workStatus = itemNameValue;
        userInfo.workStatusLable = itemName;
        Toast.success('修改工作状态成功');
        this.showSetUserStatuModal = false;
      });
    },
    //刷新用户信息
    refreshUserInfo() {
      return this.ajax.getUserInfo().then(userInfoRes => {
        if (userInfoRes.success == false) {
          return Promise.reject();
        }
        this.$session.set('_oa_user_key', JSON.stringify(userInfoRes.object));
        this.$store.commit('common/setData', {
          label: 'userInfo',
          value: userInfoRes.object
        });

        return userInfoRes;
      });
    },
    //获取今日日程
    getSchedule() {
      this.ajax
        .getSchedule({
          scheduleDate: this.$dayjs().format('YYYY-MM-DD')
        })
        .then(res => {
          if (res.success == false) {
            return;
          }
          this.scheduleList = res.object.slice(0, 3);
        });
    },
    handleGoToFUllSchedule() {
      this.$router.push(
        '/ts-mobile-oa/pages/schedule/schedule-calendar?fromPage=index&index=0'
      );
    }
  }
};
</script>

<style lang="scss" scoped>
.ts-container {
  display: flex;
  flex-direction: column;
}
.user-container {
  padding: 10px 16px;
  background-color: #ffffff;
  display: flex;
  justify-content: left;
  align-items: center;
  > span {
    overflow: hidden;
    text-overflow: ellipsis;
  }
  * {
    white-space: nowrap;
  }
  .line {
    width: 1px;
    height: 16px;
    background-color: #e2e4e8;
    margin: 0 8px;
  }
}
.user-head-img {
  width: 24px;
  height: 24px;
  margin-right: 6px;
  border-radius: 100%;
  object-fit: cover;
}
.list-container {
  flex: 1;
}
.van-cell__left-icon {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  margin-right: 10px;
  color: #ffffff;
  text-align: center;
  line-height: 40px;
  font-size: 14px;
}
.cell-item_content {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.cell-item_content-note {
  margin: 0;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
.cell-item_content-time {
  font-size: $font-size-sm;
  color: $text-color-grey;
}
.user-statu-icon {
  display: inline-block;
  transform: rotate(180deg);
}
.flex-col-center {
  display: flex;
  align-items: center;
}
.work-status-box {
  flex: 1;
  min-width: 52px;
  overflow: hidden;
  span {
    flex-shrink: 1;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 25px;
  }
  * {
    font-size: 12px;
    color: #666666;
  }
  .user-statu-icon {
    margin-left: 15px;
  }
}
.user-statu-set-modal {
  border-radius: 0;
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #333333;
    line-height: 38px;
    position: relative;
    text-align: center;
    .main-icon {
      position: absolute;
      top: 0;
      right: 8px;
    }
  }
}
.user-statu-item {
  margin-left: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid #e2e4e8;

  img {
    height: 20px;
    width: 20px;
    margin-right: 8px;
  }
  .checked-box {
    padding: 0 16px;
    width: 16px;
    img {
      width: 16px;
      height: 16px;
    }
  }
}
.today-schedule-box {
  background: #ffffff;
  box-shadow: 0px 6px 24px 0px rgba(45, 118, 235, 0.08);
  border-radius: 4px;
  margin: 8px 16px;
  height: 80px;
  padding: 8px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  transition: all 0.3s;
  transform-origin: top;
  > div {
    margin-bottom: 4px;
  }
}
.no-schedule {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #a0a8b4;
}
.schedule-title-box {
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  color: #333333;
  > div {
    color: #a0a8b4;
    font-weight: 400;
    font-size: 12px;
    .main-icon {
      font-size: 12px;
      margin-left: 4px;
      transform: rotate(180deg) scale(0.85);
    }
  }
}
.schedule-name,
.schedule-time {
  color: #666666;
  font-size: 12px;
}
.schedule-time {
  margin-right: 8px;
}
.today-schedule-item::before {
  content: ' ';
  width: 4px;
  height: 4px;
  background-color: #cccccc;
  margin-right: 8px;
  border-radius: 50%;
}
</style>
