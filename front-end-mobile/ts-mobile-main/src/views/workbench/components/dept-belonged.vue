<template>
  <van-popover
    class="dept-belonged"
    v-model="showPopover"
    trigger="click"
    :actions="actions"
    placement="bottom-end"
    @select="onSelect"
  >
    <template #reference>
      <van-cell
        :title="value.orgName"
        icon-prefix="main-icon"
        icon="yiyuanjianzhu"
        is-link
        value="切换"
      >
      </van-cell>
    </template>
  </van-popover>
</template>

<script>
export default {
  name: 'dept-belonged',
  model: {
    event: 'change',
    prop: 'value'
  },
  props: {
    deptList: {
      type: Array,
      default: () => []
    },
    value: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      showPopover: false
    };
  },
  computed: {
    actions() {
      return this.deptList.map(item => {
        return {
          id: item.id,
          text: item.orgName,
          orgId: item.orgId,
          orgName: item.orgName,
          className:
            this.value.orgId == item.orgId ? 'menu-text active' : 'menu-text'
        };
      });
    }
  },
  methods: {
    onSelect(e) {
      this.$emit('change', {
        id: e.id,
        orgId: e.orgId,
        orgName: e.orgName
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.dept-belonged {
  display: block;
}
</style>
<style lang="scss">
.menu-text {
  padding: 0 8px;
  .van-popover__action-text {
    justify-content: left;
  }
  &.active {
    .van-popover__action-text {
      color: $theme-color;
    }
    &::after {
      position: absolute;
      font-family: 'vant-icon';
      content: '\e728';
      right: 8px;
      color: $theme-color;
    }
  }
}
</style>
