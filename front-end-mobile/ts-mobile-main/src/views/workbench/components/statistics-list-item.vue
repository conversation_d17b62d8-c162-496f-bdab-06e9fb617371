<template>
  <div class="statistics-list-item">
    <p class="item-count" :style="{ color: countColor }">{{ count }}</p>
    <p class="item-title">{{ title }}</p>
  </div>
</template>

<script>
export default {
  name: 'statistics-list-item',
  props: {
    count: {
      type: [Number, String],
      default: 0
    },
    countColor: {
      type: String,
      default: '#666'
    },
    title: {
      type: String,
      default: ''
    }
  }
};
</script>

<style lang="scss" scoped>
.statistics-list-item {
  text-align: center;
}
.item-count {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
}
.item-title {
  margin: 8px 0;
  font-size: 14px;
  color: $text-color;
}
</style>
