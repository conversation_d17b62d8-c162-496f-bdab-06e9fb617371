<template>
  <van-popup v-model="show" position="bottom" round :style="{ height: '50%'}">
    <div class="search">
      <van-search
        class="search-input"
        v-model="searchVal"
        shape="round"
        placeholder="输入姓名、科室、电话搜索"
        @search="search"
        @input="search"
      />
    </div>
    <div class="content">
      <van-pull-refresh
        style="height: 100%;"
        v-model="refreshing"
        @refresh="onRefresh()"
      >
        <div class="tab-list">
          <van-list
            v-model="loading"
            :finished="finished"
            finished-text="没有更多了"
            @load="onLoad()"
          >
            <div
              class="user-cell"
              v-for="(item, index) in contractArr"
              :key="index"
              style="background:#fff;"
              @click="viewInternalContact(item)"
            >
              <div class="user-info">
                <div class="user-name">{{ item.empName }}</div>
                <div class="user-phone">{{ item.empPhone || item.empPhoneSecond || '--' }}</div>
              </div>
            </div>
          </van-list>
        </div>
      </van-pull-refresh>      
    </div>
  </van-popup>
</template>
<script>
import { PullRefresh, Search } from 'vant';
export default {
  components: {
    [PullRefresh.name]: PullRefresh,
    [Search.name]: Search
  },
  data() {
    return {
      searchVal: '',
      pageNo: 1,
      finished: false,
      contractArr: [],
      loading: false,
      refreshing: false,
      show: false
    };
  },
  methods: {
    open() {
      this.search();
      this.show = true;
    },
    close() {
      this.contractArr = [];
      this.pageNo = 1;
      this.show = false;
    },
    search() {
      this.pageNo = 1;
      this.finished = false;
      this.contractArr = [];
      this.loading = true;
      this.getData();
    },
    onRefresh() {
      this.pageNo = 1;
      this.finished = false;
      this.searchVal = '';
      this.getData();
    },
    onLoad() {
      if (this.refreshing) {
        this.loading = false;
        return false;
      }
      this.getData();
    },
    viewInternalContact(item) {
      this.$emit('choose',item);
      this.close();
    },
    getData() {
      //内部联系人
      this.ajax
        .getInnerContractList({
          pageNo: this.pageNo,
          pageSize: 30,
          employeeName: this.searchVal
        })
        .then(res => {
          if (res) {
            setTimeout(() => {
              this.refreshing = false;
            }, 500);
            if (this.pageNo == 1) {
              this.contractArr = [];
            }
            this.contractArr.push(...res.rows);
            this.loading = false;
            this.pageNo++;
            if (this.contractArr.length >= res.totalCount) {
              this.finished = true;
            }
          }
        });
    }
  }
}
</script>
<style lang="scss" scoped>
.search {
  position: fixed;
  width: 100%;
  top: 50vh;
  z-index: 1000;
}
.content {
  position: absolute;
  top: 60px;
  width: 100%;
  z-index: 999;
  .user-cell {
    padding: 3px 5px;
    .user-info {
      display: flex;
      .user-name {
        font-size: 14px;
        line-height: 30px;
        margin-right: 10px;
      }
      .user-phone {
        font-size: 14px;
        line-height: 30px;
        color: #ccc;
      }
    }

  }
}
</style>
