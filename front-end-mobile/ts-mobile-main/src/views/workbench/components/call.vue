<template>
  <div>
    <van-popup
      v-model="show"
      round
      :style="{ height: '20%', width: '80%', minHeight: '180px' }"
    >
      <div class="content">
        <div class="choose">
          <div class="label">电话号码:</div>
          <van-field
            v-model="phone"
            ref="empPhone"
            name="empPhone"
            placeholder="请输入手机号码"
            size="large"
            autocomplete="off"
            :clearable="true"
            type="number"
          />
        </div>
        <div class="choose">
          <div class="label">选择联系人:</div>
          <div class="input value right" @click="personselect">
            {{ name == '' ? '请选择联系人' : name }}
          </div>
        </div>
      </div>
      <div class="footer">
        <div class="btn" @click="close">取消</div>
        <div class="submit btn" @click="submit">确定</div>
      </div>
    </van-popup>
    <person-select ref="personSelect" @choose="choose" />
  </div>
</template>

<script>
import personSelect from './personSelect.vue';
export default {
  components: { personSelect },
  data() {
    return {
      show: false,
      phone: '',
      name: '',
    };
  },
  methods: {
    close() {
      this.show = false;
      this.name = '';
      this.phome = '';
    },
    open() {
      this.show = true;
    },
    clearName() {
      this.name = '';
    },
    choose(item) {
      this.name = item.empName;
      this.phone = item.empPhone;
    },
    personselect() {
      this.$refs.personSelect.open();
    },
    submit() {
      let data = {
        mytel: this.$store.state.common.userInfo.phoneNumber,
        mycode: this.$store.state.common.userInfo.employeeNo,
        calltel: this.phone,
      };
      this.ajax
        .callPhone(data)
        .then((res) => {
          this.close();
        })
        .catch((e) => {
          this.close();
        });
    },
  },
};
</script>
<style lang="scss" scoped>
.footer {
  display: flex;
  font-size: 14px;
  justify-content: space-between;
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 40px;
  align-items: center;
  div {
    width: 50%;
    text-align: center;
  }
  .submit {
    color: #5260ff;
  }
}
.content {
  padding: 10px 10px;
  .choose {
    display: flex;
    line-height: 50px;
    .label {
      width: 30%;
      text-align: right;
      margin-right: 5px;
    }
    .input {
      border: none;
    }
    .value {
      width: 70%;
    }
    .right {
      color: #ccc;
    }
  }
}
/deep/ .van-cell {
  padding: 10px 0;
  width: 70%;
}
</style>
