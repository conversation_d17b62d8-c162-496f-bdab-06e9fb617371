<template>
  <div class="ts-container">
    <dept-belonged
      v-if="deptList.length > 1"
      v-model="currentDept"
      :deptList="deptList"
      @change="handleDeptChange"
    ></dept-belonged>
    <van-cell
        :title="orgName"
        icon-prefix="main-icon"
        icon="yiyuanjianzhu"
        is-link
        :value="orgColumns.length > 1 ? '切换' : ''"
        @click="changes"
        v-if="orgColumns.length > 1"
      >
      </van-cell>
    <work-card title="我的流程" v-if="processStatisticalList.length">
      <statistics-list>
        <statistics-list-item
          v-for="(item, index) in processStatisticalList"
          :key="index"
          :count="item.count"
          :title="item.name"
          :count-color="item.color"
          @click.native="handleChangeRouter(item.path)"
        ></statistics-list-item>
      </statistics-list>
    </work-card>
    <!-- <work-card title="我的工单" v-if="workOrderStatisticalList.length">
      <statistics-list>
        <statistics-list-item
          v-for="(item, index) in workOrderStatisticalList"
          :key="index"
          :count="item.count"
          :title="item.name"
          :count-color="item.color"
        ></statistics-list-item>
      </statistics-list>
    </work-card> -->
    <work-card v-for="item in menuList" :key="item.id" :title="item.menuname">
      <menu-list>
        <menu-list-item
          v-for="row in item.menus"
          :key="row.id"
          :icon="row.icon | iconFilter"
          :icon-color="row.icon | colorFilter"
          :title="row.menuname"
          @click="jumpPage(row.menuname, row.packageName, row.alink)"
        ></menu-list-item>
      </menu-list>
    </work-card>
    <call ref="call" />
    <feedBack ref="feedBack" />
    <select-popup v-model="showOrg" height="400px">
      <van-picker
        :columns="orgColumns"
        show-toolbar
        @cancel="onCancelOrg"
        @confirm="onConfirmOrg"
      />
    </select-popup>
  </div>
</template>

<script>
import deptBelonged from './components/dept-belonged.vue';
import workCard from './components/work-card.vue';
import statisticsList from './components/statistics-list.vue';
import statisticsListItem from './components/statistics-list-item.vue';
import menuList from './components/menu-list.vue';
import menuListItem from './components/menu-list-item.vue';
import index from './index.js';
import call from './components/call.vue'
import feedBack from './components/feedBack.vue';
import SelectPopup from '@/components/person-select/select-popup.vue';
export default {
  name: 'workbench',
  mixins: [index],
  components: {
    deptBelonged,
    workCard,
    statisticsList,
    statisticsListItem,
    menuList,
    menuListItem,
    call,
    feedBack,
    SelectPopup
  }
};
</script>

<style scoped>
.ts-container {
  overflow: scroll;
}
</style>
