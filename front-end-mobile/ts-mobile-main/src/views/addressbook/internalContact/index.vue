<template>
  <div class="ts-container">
    <div class="ts-navbar">
      <div class="ts-navbar-content bottom-line">
        <div class="ts-back-wrap" @click="$router.back()">
          <van-icon name="arrow-left" />
        </div>
        <span class="ts-navbar-content-title">个人信息</span>
      </div>
      <div class="ts-navbar-placeholder"></div>
    </div>
    <div class="page-content" v-if="form">
      <div class="user-info">
        <van-image
          :src="form.avatar"
          fit="cover"
          lazy-load
          class="user-avatar"
          error-icon="user-o"
          loading-icon="user-o"
          @click="handleReviewAvatar(form)"
        >
        </van-image>
        <div class="user-details">
          <div style="font-size:16px;margin-top:5px;">
            <span>{{ form.empName }}</span>
            <img
              src="@/assets/images/sex-man.png"
              class="sex-icon"
              v-if="form.empSex == 0"
            />
            <img
              src="@/assets/images/sex-woman.png"
              class="sex-icon"
              v-else-if="form.empSex == 1"
            />
          </div>
          <div style="color:#666;margin-top:10px;">{{ form.empDeptName }}</div>
          <!-- <img src="@/assets/images/send.png" alt="" class="send-icon" /> -->
        </div>
      </div>
      <div style="height:8px;background:#F4F4F4;"></div>
      <div class="contract-card bottom-line">
        <div class="cell-label">邮箱</div>
        <div class="cell-value">{{ form.empEmail || '暂无' }}</div>
      </div>
      <div class="contract-card bottom-line">
        <div class="cell-label">手机号码</div>
        <div
          class="cell-value"
          v-if="!form.empBusinessPhone && !form.empBusinessPhone"
        >
          暂无
        </div>
        <div class="cell-value" v-if="form.empPhone">
          {{ form.empPhone }}
          <span
            v-if="form.empPhone && form.empPhone.indexOf('*') != -1"
            style="float:right;font-size:12px;color:#999;margin-right:10px"
          >
            已开启隐私保护
          </span>
          <div v-else class="phone-icon" @click="call(form.empPhone)"></div>
        </div>
        <div class="cell-value" v-if="form.empBusinessPhone">
          {{ form.empBusinessPhone }}
          <div class="phone-icon" @click="call(form.empBusinessPhone)"></div>
        </div>
      </div>
      <div class="contract-card bottom-line">
        <div class="cell-label">手机短号</div>
        <div
          class="cell-value"
          v-if="
            !form.empBusinessPhone &&
              !form.empUnicomBusinessPhone &&
              !form.empTelecomBusinessPhone
          "
        >
          暂无
        </div>
        <div class="cell-value" v-if="form.empBusinessPhone">
          <img src="@/assets/images/yidong.png" />
          <span>{{ form.empBusinessPhone }}</span>
          <div class="phone-icon" @click="call(form.empBusinessPhone)"></div>
        </div>
        <div class="cell-value" v-if="form.empUnicomBusinessPhone">
          <img src="@/assets/images/liantong.png" />
          <span>{{ form.empUnicomBusinessPhone }}</span>
          <div
            class="phone-icon"
            @click="call(form.empUnicomBusinessPhone)"
          ></div>
        </div>
        <div class="cell-value" v-if="form.empTelecomBusinessPhone">
          <img src="@/assets/images/dianxin.png" />
          <span>{{ form.empTelecomBusinessPhone }}</span>
          <div
            class="phone-icon"
            @click="call(form.empTelecomBusinessPhone)"
          ></div>
        </div>
      </div>
      <div class="contract-card bottom-line">
        <div class="cell-label">座机号码</div>
        <div class="cell-value" v-if="!form.landlineNumber">暂无</div>
        <div class="cell-value" v-if="form.landlineNumber">
          <span>{{ form.landlineNumber }}</span>
          <div class="phone-icon" @click="call(form.landlineNumber)"></div>
        </div>
      </div>
    </div>
    <div class="page-bottom" v-if="form && showSwitch">
      <div class="switch bottom-line">
        <div
          class="prev"
          @click="
            pageNo--;
            getData();
          "
        >
          <van-icon name="arrow-left" />
        </div>
        <div
          class="next"
          @click="
            pageNo++;
            getData();
          "
        >
          <van-icon name="arrow" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import index from './index.js';
export default {
  name: 'internalContact',
  mixins: [index]
};
</script>

<style scoped lang="scss">
.ts-container {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .page-content {
    margin-bottom: 44px;
    .user-info {
      padding: 16px 15px;
      display: flex;
      position: relative;
      .sex-icon {
        width: 17px;
        height: 17px;
        vertical-align: text-top;
        margin-left: 16px;
      }
      .send-icon {
        width: 22px;
        height: 22px;
        position: absolute;
        top: 35px;
        right: 15px;
      }
      .user-avatar {
        height: 60px;
        width: 60px;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 16px;
        img {
          height: 60px;
          width: 60px;
        }
      }
      .user-details {
        flex: 1;
      }
    }
    .contract-card {
      position: relative;
    }
    .cell-label,
    .cell-value {
      font-size: 16px;
      padding: 12px 15px;
      position: relative;
      img {
        height: 22px;
        width: 22px;
        vertical-align: bottom;
        margin-right: 10px;
      }
    }
    .cell-value {
      color: #666;
    }
    .phone-icon {
      background: url('../../../assets/images/phone.png') no-repeat center;
      background-size: 22px 22px;
      position: absolute;
      width: 40px;
      height: 40px;
      right: 15px;
      top: 1px;
      border-radius: 50%;
      &:active {
        background-color: #efefef;
      }
    }
  }
  .page-bottom {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 999;
    background-color: #fff;
    box-shadow: 0px -2px 2px 0px #e4e4e4;
  }
  .switch {
    height: 44px;
    line-height: 44px;
    display: flex;
    .prev,
    .next {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        font-size: 18px;
        color: #666;
      }
      &:active {
        background-color: #efefef;
      }
    }
    .next {
      text-align: left;
    }
  }
}
</style>
