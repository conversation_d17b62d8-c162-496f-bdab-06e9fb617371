import { SwipeCell } from 'vant';
import personSelect from '@/components/person-select/index.vue';
export default {
  components: {
    [SwipeCell.name]: SwipeCell,
    personSelect
  },
  data() {
    return {
      groupName: '123',
      showSelect: false,
      selectedUser: [],
      form: {
        groupClassId: '',
        groupClassName: '',
        groupId: '',
        groupName: '',
        groupOrder: '',
        groupType: '',
        groupUserNames: '',
        groupUserString: '',
        rangeEmp: '',
        rangeEmpCode: '',
        rangeName: '',
        rangeOrg: ''
      }
    };
  },
  created() {
    let _d = this.$route.params.data;
    for (let key in this.form) {
      this.form[key] = _d[key];
    }
    this.selectedUser = _d.employeeList;
  },
  methods: {
    chooseUser(users) {
      this.$toast({
        loadingType: 'spinner',
        message: '更新中...',
        forbidClick: true,
        duration: 0
      });
      this.form.groupUserNames = users.reduce((prev, cur) => {
        return prev + cur.empName + ',';
      }, '');
      this.form.groupUserString = users.reduce((prev, cur) => {
        return prev + cur.empCode + ',';
      }, '');

      this.ajax.updatePersonalGroup(this.form).then(res => {
        if (res.success) {
          this.$toast.clear();
          this.$toast('更新成功!');
          this.selectedUser = users;
        }
      });
    },
    removeUser(user) {
      let _index = this.isExsit(user);
      this.selectedUser.splice(_index, 1);
      this.$toast({
        type: 'loading',
        loadingType: 'spinner',
        message: '更新中...',
        forbidClick: true,
        duration: 0
      });
      this.form.groupUserNames = this.selectedUser.reduce((prev, cur) => {
        return prev + cur.empName + ',';
      }, '');
      this.form.groupUserString = this.selectedUser.reduce((prev, cur) => {
        return prev + cur.empCode + ',';
      }, '');

      this.ajax.updatePersonalGroup(this.form).then(res => {
        if (res.success) {
          this.$toast.clear();
          this.$toast('更新成功!');
        }
      });
    },
    removeGroup() {
      this.$toast({
        loadingType: 'spinner',
        message: '删除中...',
        forbidClick: true,
        duration: 0
      });
      this.ajax.removePersonalGroup(this.form.groupId).then(res => {
        if (res.success) {
          this.$toast.clear();
          this.$toast('删除成功!');
        }
      });
    },
    isExsit(item) {
      for (let i in this.selectedUser) {
        if (this.selectedUser[i].id == item.id) {
          return i;
        }
      }
      return false;
    }
  }
};
