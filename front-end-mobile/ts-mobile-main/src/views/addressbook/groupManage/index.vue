<template>
  <div class="ts-container">
    <div class="ts-navbar">
      <div class="ts-navbar-content bottom-line">
        <div class="ts-back-wrap" @click="$router.back()">
          <van-icon name="arrow-left" />
        </div>
        <span class="ts-navbar-content-title">群组管理</span>
      </div>
      <div class="ts-navbar-placeholder"></div>
    </div>
    <div class="page-content">
      <van-cell-group>
        <van-field label="群组名称" v-model="form.groupName" readonly />
      </van-cell-group>

      <person-select
        :show.sync="showSelect"
        @select="chooseUser"
        multi
        :selectedUsers.sync="selectedUser"
      >
        <div class="cell" style="padding:13px 15px 16px 15px;">
          <div class="label">群组成员</div>
          <div
            class="value"
            style="text-align:right;font-size:22px;color:#979797;"
          >
            <van-icon name="add-o" @click="showSelect = true" />
          </div>
        </div>
      </person-select>
      <van-swipe-cell
        right-width="100"
        v-for="item in selectedUser"
        :key="item.id"
      >
        <div
          class="user-item bottom-line"
          :class="
            item.empSex == 0
              ? 'user-item-man'
              : item.empSex == 1
              ? 'user-item-woman'
              : ''
          "
        >
          <!-- <div class="user-avatar">
            {{ item.empName.slice(-2) }}
          </div> -->
          <van-image
            :src="item.avata"
            fit="cover"
            lazy-load
            class="user-avatar"
            error-icon="user-o"
            loading-icon="user-o"
          >
          </van-image>
          <div class="user-info">
            <div class="user-name">{{ item.empName }}</div>
            <div class="user-dept" v-if="item.empDeptName">
              {{ item.empDeptName }}
            </div>
            <div class="user-post" v-if="item.positionName">
              {{ item.positionName }}
            </div>
            <div class="user-code" v-if="item.empCode">{{ item.empCode }}</div>
          </div>
        </div>
        <template #right>
          <van-button
            square
            text="移除"
            type="danger"
            class="delete-button"
            @click="removeUser(item)"
          />
        </template>
      </van-swipe-cell>
    </div>
    <div class="page-bottom" @click="removeGroup">移除分组</div>
  </div>
</template>

<script>
import index from './index.js';
import { Image as VanImage } from 'vant';
export default {
  name: 'addressbook',
  mixins: [index],
  components: {
    [VanImage.name]: VanImage
  }
};
</script>

<style scoped lang="scss">
.delete-button {
  height: 100%;
  width: 104px;
}
.ts-container {
  background-color: #fff;
  .page-content {
    padding-bottom: 50px;
    .bottom-line::after {
      left: 15px;
    }
    .cell {
      display: flex;
      align-content: center;
      position: relative;
      .label {
        width: 56px;
        font-size: 14px;
        color: #666;
      }
      .value {
        flex: 1;
        height: 30px;
        margin-left: 13px;
        input {
          border: 0;
        }
      }
    }
  }
  .van-swipe-cell:last-child .user-item::after {
    display: none;
  }
  .user-item-woman /deep/.van-image__error {
    background-color: $sexwoman-color;
    .van-image__error-icon {
      color: #fff;
    }
  }
  .user-item-man /deep/.van-image__error {
    background-color: $sexman-color;
    .van-image__error-icon {
      color: #fff;
    }
  }
  .user-item {
    padding: 7px 0 9px 15px;
    display: flex;
    position: relative;
    /deep/.van-image__error-icon {
      font-size: 24px;
    }
    /deep/.van-image__loading-icon {
      font-size: 24px;
    }
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      color: #fff;
      overflow: hidden;
      // img {
      //   height: 40px;
      //   width: 40px;
      //   border-radius: 50%;
      // }
    }
    .user-info {
      flex: 1;
      margin-left: 12px;
      .user-name {
        font-size: 16px;
      }
      .user-dept,
      .user-post,
      .user-code {
        font-size: 12px;
        color: #666;
        margin-top: 2px;
        display: inline-block;
        margin-right: 12px;
      }
    }
  }
  .page-bottom {
    height: 44px;
    line-height: 44px;
    text-align: center;
    bottom: 0;
    width: 100%;
    position: fixed;
    color: #e24242;
    font-size: 18px;
    box-shadow: 0px -2px 2px 0px #e4e4e4;
    background-color: #fff;
  }
}
</style>
