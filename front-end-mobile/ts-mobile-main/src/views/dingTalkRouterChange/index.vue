<template>
  <div></div>
</template>

<script>
import * as dingtalk from 'dingtalk-jsapi'; //引入钉钉jsapi
import { $get } from '@/api/ajax.js';
import { Base64 } from 'js-base64';

export default {
  async mounted() {
    let corpId = '',
      searchs = location.href.split('?')[1] || '';

    searchs.split('&').forEach(item => {
      if (item.indexOf('corpId') >= 0) {
        corpId = item.split('=')[1];
      }
    });

    if (!corpId) {
      this.$store.dispatch('common/goToLogin');
      return;
    }

    let dingRes = await new Promise((resolve, reject) => {
      dingtalk.ready(() => {
        dingtalk.runtime.permission.requestAuthCode({
          corpId: corpId, // 企业id
          onSuccess: info => {
            // vm.authCode = info.code; // 通过该免登授权码可以获取用户身份
            resolve(info);
          },
          onFail: function(err) {
            reject(false);
          }
        });
      });
    });

    if (!dingRes) {
      this.$store.dispatch('common/goToLogin');
      return;
    }

    let aDom = document.createElement('a');
    aDom.href =
      `/ts-information/dingtalk/callback?code=${dingRes.code}&loginType=1&toUrl=` +
      Base64.encode(
        'ts-mobile-work-order/pages/work-order-reporting/index?fromPage=workBench&index=0'
      );
    aDom.click();
    // $get('/ts-information/dingtalk/callback', {
    //   code: dingRes.code,
    //   loginType: 1,
    //   toUrl: Base64.encode(
    //     'ts-mobile-work-order/pages/work-order-reporting/index?fromPage=workBench&index=0'
    //   )
    // });
  }
};
</script>

<style></style>
