### 事件触发方式
* 子应用触发主应用统一方法  $parentTypeFun
```
/**@desc 子应用 */
this.$parentTypeFun({
  event: 'eventName',
  data: {}
})

/**@desc 主应用 -查看 /trasenLayout/App.js 中的 parentTypeFun 方法*/
```
* 主应用广播事件
```
/**@desc 主应用触发 详情查看 /trasenLayout/App.js 中的 mounted方法 */
this.$mainMessageStore.setGlobalState(data);

/**@desc 子应用监听事件 */
// main.js
export async function mount(props) {
  //...
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  Vue.prototype.$setGlobalState = props.setGlobalState;
  render(props);
}

// APP.vue
<script>
  export default {
    //...
    mounted() {
      // 注册事件监听
      this.$onGlobalStateChange((state = {}, prev = {}) => {
        let { event, data: newVal } = state,
          oldVal = prev.data || {};
        this.$event.create('mainMessage').trigger(event, newVal, oldVal);
      });
    }
  }
</script>

```