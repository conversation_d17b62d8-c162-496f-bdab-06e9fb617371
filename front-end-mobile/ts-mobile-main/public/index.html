<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, viewport-fit=cover">
  <link rel="icon" href="<%= VUE_APP_BASE_URL %>favicon.ico">
  <base href="<%= VUE_APP_BASE_URL%>" />
  <title></title>
  <script>
    var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') || CSS.supports('top: constant(a)'));
		document.write('<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' + (coverSupport ? ', viewport-fit=cover' : '') + '" />');
    window.qiankuanappsLogin = [
      {
        name: 'tsMobileEquipment',
        userData: {
          packageName: 'ts-mobile-equipment'
        },
        alink: '/ts-mobile-equipment/',
      },
      {
        name: 'tsMobileWorkOrder',
        userData: {
          packageName: 'ts-mobile-work-order'
        },
        alink: '/ts-mobile-work-order/',
      },
      {
        name: 'tsMobileOa',
        userData: {
          packageName: 'ts-mobile-oa'
        },
        alink: '/ts-mobile-oa/',
      },
      {
        name: 'tsMobileMeeting',
        userData: {
          packageName: 'ts-mobile-meeting'
        },
        alink: '/ts-mobile-meeting/',
      },
      {
        name: 'tsMobileHrms',
        userData: {
          packageName: 'ts-mobile-hrms'
        },
        alink: '/ts-mobile-hrms/',
      },
      {
        name: 'tsMobileWxoa',
        userData: {
          packageName: 'ts-mobile-wxoa'
        },
        alink: '/ts-mobile-wxoa/',
      },
      {
        name: 'tsMobileContract',
        userData: {
          packageName: 'ts-mobile-contract'
        },
        alink: '/ts-mobile-contract/',
      },
      {
        userData: {
          packageName: 'vue3.1'
        },
        alink: '/vue3.1/',
      },
    ]
    window['<%= VUE_APP_BASE_URL%>'] = [{
      matchPath: '*',
      config: {
        sso: {
          base: '',
          url: '/user'
        },
        resource: {
          base: '',
          url: '/ts-resource'
        },
        form: {
          base: '',
          url: '/ts-form'
        },
        external: {
          base: '',
          url: '/ts-external'
        },
        basics: {
          base: '',
          url: '/ts-basics-bottom'
        },
        information: {
          base: '',
          url: '/ts-information'
        },
        oa: {
          base: '',
          url: '/ts-oa'
        },
        workflow: {
          base: '',
          url: '/ts-workflow'
        },
        worksheet: {
          base: '',
          url: '/ts-worksheet'
        },
        system: {
          base: '',
          url: '/ts-system'
        },
        document: {
          base: '',
          url: '/ts-document'
        },
        preview: {
          base: '',
          url: '/ts-preview'
        },
      }
    }];
  </script>
</head>

<body>
  <div id="<%= VUE_APP_CONTAINER %>"></div>
</body>

</html>